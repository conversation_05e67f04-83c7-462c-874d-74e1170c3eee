.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;
.super Lorg/xbet/ui_common/paging/BasePagingSource;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/paging/BasePagingSource<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\n\u0008\u0000\u0018\u0000  2\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0001!B\u0017\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ%\u0010\u000c\u001a\u0004\u0018\u00010\u00022\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\nH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rJ8\u0010\u0012\u001a\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00110\u00102\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000eH\u0096@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0016\u001a\u00020\u0015*\u0004\u0018\u00010\u00022\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0002H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J#\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00112\u0006\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001f\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;",
        "Lorg/xbet/ui_common/paging/BasePagingSource;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "Leu/i;",
        "getCurrentCountryIdUseCase",
        "Lw81/d;",
        "getTournamentGamesScenario",
        "<init>",
        "(Leu/i;Lw81/d;)V",
        "Landroidx/paging/M;",
        "state",
        "m",
        "(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
        "Landroidx/paging/PagingSource$a;",
        "params",
        "Lkotlin/Pair;",
        "Landroidx/paging/PagingSource$b;",
        "l",
        "(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "nextKey",
        "",
        "n",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;)Z",
        "",
        "throwable",
        "j",
        "(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;",
        "b",
        "Leu/i;",
        "c",
        "Lw81/d;",
        "d",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final b:Leu/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lw81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->d:Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$a;

    return-void
.end method

.method public constructor <init>(Leu/i;Lw81/d;)V
    .locals 0
    .param p1    # Leu/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lw81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/paging/BasePagingSource;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->b:Leu/i;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->c:Lw81/d;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public bridge synthetic e(Landroidx/paging/M;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->m(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public j(Ljava/lang/Throwable;)Landroidx/paging/PagingSource$b;
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            ")",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/PagingSource$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Landroidx/paging/PagingSource$b$a;-><init>(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public bridge synthetic k(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->n(Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public l(Landroidx/paging/PagingSource$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 17
    .param p1    # Landroidx/paging/PagingSource$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingSource$a<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
            "+",
            "Landroidx/paging/PagingSource$b<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->label:I

    .line 22
    .line 23
    :goto_0
    move-object v11, v2

    .line 24
    goto :goto_1

    .line 25
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;

    .line 26
    .line 27
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :goto_1
    iget-object v1, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    iget v3, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->label:I

    .line 38
    .line 39
    const-wide/16 v12, 0x0

    .line 40
    .line 41
    const/4 v4, 0x2

    .line 42
    const/4 v5, 0x1

    .line 43
    const/4 v14, 0x0

    .line 44
    if-eqz v3, :cond_3

    .line 45
    .line 46
    if-eq v3, v5, :cond_2

    .line 47
    .line 48
    if-ne v3, v4, :cond_1

    .line 49
    .line 50
    iget v2, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->I$0:I

    .line 51
    .line 52
    iget-object v3, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 53
    .line 54
    check-cast v3, Landroidx/paging/PagingSource$a;

    .line 55
    .line 56
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    goto/16 :goto_7

    .line 60
    .line 61
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 64
    .line 65
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 66
    .line 67
    .line 68
    throw v1

    .line 69
    :cond_2
    iget-wide v5, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->J$0:J

    .line 70
    .line 71
    iget v3, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->I$0:I

    .line 72
    .line 73
    iget-object v7, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 74
    .line 75
    check-cast v7, Ljava/lang/Long;

    .line 76
    .line 77
    iget-object v8, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 78
    .line 79
    check-cast v8, Lw81/d;

    .line 80
    .line 81
    iget-object v9, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 82
    .line 83
    check-cast v9, Landroidx/paging/PagingSource$a;

    .line 84
    .line 85
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 86
    .line 87
    .line 88
    move-wide v15, v5

    .line 89
    move-object v5, v1

    .line 90
    move-object v1, v9

    .line 91
    move v9, v3

    .line 92
    move-object v3, v7

    .line 93
    move-wide v6, v15

    .line 94
    goto :goto_5

    .line 95
    :cond_3
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    check-cast v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 103
    .line 104
    if-eqz v1, :cond_4

    .line 105
    .line 106
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->a()I

    .line 107
    .line 108
    .line 109
    move-result v1

    .line 110
    move v3, v1

    .line 111
    goto :goto_2

    .line 112
    :cond_4
    const/4 v1, 0x0

    .line 113
    const/4 v3, 0x0

    .line 114
    :goto_2
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->c:Lw81/d;

    .line 115
    .line 116
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    check-cast v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 121
    .line 122
    if-eqz v1, :cond_5

    .line 123
    .line 124
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->c()J

    .line 125
    .line 126
    .line 127
    move-result-wide v6

    .line 128
    goto :goto_3

    .line 129
    :cond_5
    move-wide v6, v12

    .line 130
    :goto_3
    invoke-virtual/range {p1 .. p1}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    check-cast v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 135
    .line 136
    if-eqz v1, :cond_6

    .line 137
    .line 138
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->b()Ljava/lang/Long;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    goto :goto_4

    .line 143
    :cond_6
    move-object v1, v14

    .line 144
    :goto_4
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource;->b:Leu/i;

    .line 145
    .line 146
    move-object/from16 v10, p1

    .line 147
    .line 148
    iput-object v10, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 149
    .line 150
    iput-object v8, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 151
    .line 152
    iput-object v1, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 153
    .line 154
    iput v3, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->I$0:I

    .line 155
    .line 156
    iput-wide v6, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->J$0:J

    .line 157
    .line 158
    iput v5, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->label:I

    .line 159
    .line 160
    invoke-interface {v9, v11}, Leu/i;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v5

    .line 164
    if-ne v5, v2, :cond_7

    .line 165
    .line 166
    goto :goto_6

    .line 167
    :cond_7
    move v9, v3

    .line 168
    move-object v3, v1

    .line 169
    move-object v1, v10

    .line 170
    :goto_5
    check-cast v5, Ljava/lang/Number;

    .line 171
    .line 172
    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    .line 173
    .line 174
    .line 175
    move-result v5

    .line 176
    iput-object v1, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$0:Ljava/lang/Object;

    .line 177
    .line 178
    iput-object v14, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$1:Ljava/lang/Object;

    .line 179
    .line 180
    iput-object v14, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->L$2:Ljava/lang/Object;

    .line 181
    .line 182
    iput v9, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->I$0:I

    .line 183
    .line 184
    iput v4, v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/TournamentsGamePagingSource$loadNextPage$1;->label:I

    .line 185
    .line 186
    move-wide v15, v6

    .line 187
    move v7, v5

    .line 188
    move-wide v4, v15

    .line 189
    move-object v6, v3

    .line 190
    move-object v3, v8

    .line 191
    const/16 v8, 0x10

    .line 192
    .line 193
    const/4 v10, 0x0

    .line 194
    invoke-interface/range {v3 .. v11}, Lw81/d;->a(JLjava/lang/Long;IIIZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 195
    .line 196
    .line 197
    move-result-object v3

    .line 198
    if-ne v3, v2, :cond_8

    .line 199
    .line 200
    :goto_6
    return-object v2

    .line 201
    :cond_8
    move-object v2, v3

    .line 202
    move-object v3, v1

    .line 203
    move-object v1, v2

    .line 204
    move v2, v9

    .line 205
    :goto_7
    check-cast v1, Ljava/util/List;

    .line 206
    .line 207
    if-eqz v1, :cond_d

    .line 208
    .line 209
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 210
    .line 211
    .line 212
    move-result v4

    .line 213
    if-eqz v4, :cond_9

    .line 214
    .line 215
    goto :goto_9

    .line 216
    :cond_9
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 217
    .line 218
    .line 219
    move-result v4

    .line 220
    const/16 v5, 0x10

    .line 221
    .line 222
    if-ge v4, v5, :cond_a

    .line 223
    .line 224
    goto :goto_9

    .line 225
    :cond_a
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 226
    .line 227
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 228
    .line 229
    .line 230
    move-result-object v5

    .line 231
    check-cast v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 232
    .line 233
    if-eqz v5, :cond_b

    .line 234
    .line 235
    invoke-virtual {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->c()J

    .line 236
    .line 237
    .line 238
    move-result-wide v12

    .line 239
    :cond_b
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 240
    .line 241
    .line 242
    move-result-object v5

    .line 243
    check-cast v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 244
    .line 245
    if-eqz v5, :cond_c

    .line 246
    .line 247
    invoke-virtual {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->b()Ljava/lang/Long;

    .line 248
    .line 249
    .line 250
    move-result-object v5

    .line 251
    goto :goto_8

    .line 252
    :cond_c
    move-object v5, v14

    .line 253
    :goto_8
    add-int/lit8 v6, v2, 0x10

    .line 254
    .line 255
    invoke-direct {v4, v12, v13, v5, v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;-><init>(JLjava/lang/Long;I)V

    .line 256
    .line 257
    .line 258
    goto :goto_a

    .line 259
    :cond_d
    :goto_9
    move-object v4, v14

    .line 260
    :goto_a
    new-instance v5, Landroidx/paging/PagingSource$b$c;

    .line 261
    .line 262
    if-nez v2, :cond_e

    .line 263
    .line 264
    goto :goto_b

    .line 265
    :cond_e
    invoke-virtual {v3}, Landroidx/paging/PagingSource$a;->a()Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object v2

    .line 269
    move-object v14, v2

    .line 270
    check-cast v14, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 271
    .line 272
    :goto_b
    invoke-direct {v5, v1, v14, v4}, Landroidx/paging/PagingSource$b$c;-><init>(Ljava/util/List;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 273
    .line 274
    .line 275
    invoke-static {v4, v5}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 276
    .line 277
    .line 278
    move-result-object v1

    .line 279
    return-object v1
.end method

.method public m(Landroidx/paging/M;)Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;
    .locals 6
    .param p1    # Landroidx/paging/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/M<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroidx/paging/M;->d()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_9

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    if-eqz v2, :cond_0

    .line 17
    .line 18
    invoke-virtual {v2}, Landroidx/paging/PagingSource$b$c;->i()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    move-object v2, v1

    .line 26
    :goto_0
    invoke-virtual {p1, v0}, Landroidx/paging/M;->c(I)Landroidx/paging/PagingSource$b$c;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {v0}, Landroidx/paging/PagingSource$b$c;->f()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move-object v0, v1

    .line 40
    :goto_1
    if-eqz v2, :cond_2

    .line 41
    .line 42
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->c()J

    .line 43
    .line 44
    .line 45
    move-result-wide v3

    .line 46
    goto :goto_2

    .line 47
    :cond_2
    if-eqz v0, :cond_3

    .line 48
    .line 49
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->c()J

    .line 50
    .line 51
    .line 52
    move-result-wide v3

    .line 53
    goto :goto_2

    .line 54
    :cond_3
    const-wide/16 v3, 0x0

    .line 55
    .line 56
    :goto_2
    if-eqz v2, :cond_4

    .line 57
    .line 58
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->a()I

    .line 59
    .line 60
    .line 61
    move-result v5

    .line 62
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 67
    .line 68
    add-int/2addr v5, p1

    .line 69
    goto :goto_3

    .line 70
    :cond_4
    if-eqz v0, :cond_5

    .line 71
    .line 72
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->a()I

    .line 73
    .line 74
    .line 75
    move-result v5

    .line 76
    invoke-virtual {p1}, Landroidx/paging/M;->e()Landroidx/paging/C;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iget p1, p1, Landroidx/paging/C;->a:I

    .line 81
    .line 82
    sub-int/2addr v5, p1

    .line 83
    goto :goto_3

    .line 84
    :cond_5
    const/4 v5, 0x0

    .line 85
    :goto_3
    if-eqz v2, :cond_7

    .line 86
    .line 87
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->b()Ljava/lang/Long;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-nez p1, :cond_6

    .line 92
    .line 93
    goto :goto_4

    .line 94
    :cond_6
    move-object v1, p1

    .line 95
    goto :goto_5

    .line 96
    :cond_7
    :goto_4
    if-eqz v0, :cond_8

    .line 97
    .line 98
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;->b()Ljava/lang/Long;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    :cond_8
    :goto_5
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;

    .line 103
    .line 104
    invoke-direct {p1, v3, v4, v1, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;-><init>(JLjava/lang/Long;I)V

    .line 105
    .line 106
    .line 107
    return-object p1

    .line 108
    :cond_9
    return-object v1
.end method

.method public n(Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;Lorg/xplatform/aggregator/impl/tournaments/presentation/paging/a;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
