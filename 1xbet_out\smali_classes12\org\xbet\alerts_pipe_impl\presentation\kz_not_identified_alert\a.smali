.class public final synthetic Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/a;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/a;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;

    invoke-static {v0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->N2(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
