.class public final synthetic Lorg/xbet/main_menu/impl/presentation/container/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/c;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/c;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->Q2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;

    move-result-object v0

    return-object v0
.end method
