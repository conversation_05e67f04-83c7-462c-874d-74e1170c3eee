.class public final synthetic LLW0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# instance fields
.field public final synthetic a:LLW0/b;


# direct methods
.method public synthetic constructor <init>(LLW0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LLW0/a;->a:LLW0/b;

    return-void
.end method


# virtual methods
.method public final onGlobalLayout()V
    .locals 1

    .line 1
    iget-object v0, p0, LLW0/a;->a:LLW0/b;

    invoke-static {v0}, LLW0/b;->a(LLW0/b;)V

    return-void
.end method
