.class final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.domain.usecase.GetStageTableUseCase$invoke$1$1"
    f = "GetStageTableUseCase.kt"
    l = {
        0x25
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "it",
        "",
        "LDy0/a;",
        "<anonymous>",
        "(J)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

.field final synthetic $eventId:I

.field final synthetic $initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

.field final synthetic $userRegistrationCountryId:Ljava/lang/Integer;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            "I",
            "Ljava/lang/Integer;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "Lkotlin/jvm/internal/Ref$BooleanRef;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iput p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$eventId:I

    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$userRegistrationCountryId:Ljava/lang/Integer;

    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    iget v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$eventId:I

    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$userRegistrationCountryId:Ljava/lang/Integer;

    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public final invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 2
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, v0, v1, p2}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-object p1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)Lo9/a;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-interface {p1}, Lo9/a;->j()Z

    .line 34
    .line 35
    .line 36
    move-result v7

    .line 37
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->this$0:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 38
    .line 39
    iget v4, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$eventId:I

    .line 40
    .line 41
    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$userRegistrationCountryId:Ljava/lang/Integer;

    .line 42
    .line 43
    iget-object v6, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    .line 44
    .line 45
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->$initRequestInternal:Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 46
    .line 47
    iget-boolean v8, p1, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 48
    .line 49
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$1$1;->label:I

    .line 50
    .line 51
    move-object v9, p0

    .line 52
    invoke-static/range {v3 .. v9}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->a(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    if-ne p1, v0, :cond_2

    .line 57
    .line 58
    return-object v0

    .line 59
    :cond_2
    return-object p1
.end method
