.class public final Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.WhoWinViewModel$getUiState$$inlined$map$1$2"
    f = "WhoWinViewModel.kt"
    l = {
        0x32
    }
    m = "emit"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;Lkotlin/coroutines/e;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;->result:Ljava/lang/Object;

    iget p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;->label:I

    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2$1;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;

    const/4 v0, 0x0

    invoke-virtual {p1, v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1$2;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
