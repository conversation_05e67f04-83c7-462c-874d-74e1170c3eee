.class public final LOA0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\u001a\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a!\u0010\t\u001a\u00020\u0008*\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\n\u001a!\u0010\u000b\u001a\u00020\u0008*\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\n\u00a8\u0006\u000c"
    }
    d2 = {
        "LYA0/a;",
        "LUA0/b;",
        "c",
        "(LYA0/a;)LUA0/b;",
        "",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;",
        "type",
        "",
        "a",
        "(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I",
        "b",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;",
            ">;",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;",
            ")I"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;

    .line 27
    .line 28
    invoke-virtual {v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;->a()Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    invoke-static {v0}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    :cond_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    if-eqz v0, :cond_2

    .line 49
    .line 50
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    move-object v1, v0

    .line 55
    check-cast v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;

    .line 56
    .line 57
    invoke-virtual {v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;->c()Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    if-ne v1, p1, :cond_1

    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_2
    const/4 v0, 0x0

    .line 65
    :goto_1
    check-cast v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;

    .line 66
    .line 67
    if-eqz v0, :cond_3

    .line 68
    .line 69
    invoke-virtual {v0}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;->a()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    if-eqz p0, :cond_3

    .line 74
    .line 75
    invoke-static {p0}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    if-eqz p0, :cond_3

    .line 80
    .line 81
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 82
    .line 83
    .line 84
    move-result p0

    .line 85
    return p0

    .line 86
    :cond_3
    const/4 p0, 0x0

    .line 87
    return p0
.end method

.method public static final b(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;",
            ">;",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;",
            ")I"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;

    .line 27
    .line 28
    invoke-virtual {v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel;->a()Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    invoke-static {v0}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    :cond_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    if-eqz v0, :cond_2

    .line 49
    .line 50
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    move-object v1, v0

    .line 55
    check-cast v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;

    .line 56
    .line 57
    invoke-virtual {v1}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;->c()Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    if-ne v1, p1, :cond_1

    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_2
    const/4 v0, 0x0

    .line 65
    :goto_1
    check-cast v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;

    .line 66
    .line 67
    if-eqz v0, :cond_3

    .line 68
    .line 69
    invoke-virtual {v0}, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic;->b()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    if-eqz p0, :cond_3

    .line 74
    .line 75
    invoke-static {p0}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    if-eqz p0, :cond_3

    .line 80
    .line 81
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 82
    .line 83
    .line 84
    move-result p0

    .line 85
    return p0

    .line 86
    :cond_3
    const/4 p0, 0x0

    .line 87
    return p0
.end method

.method public static final c(LYA0/a;)LUA0/b;
    .locals 15
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, LMA0/b;->c(LYA0/a;)Lkotlin/Pair;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v3, v1

    .line 10
    check-cast v3, Ljava/lang/String;

    .line 11
    .line 12
    invoke-virtual {v0}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v8, v0

    .line 17
    check-cast v8, Ljava/lang/String;

    .line 18
    .line 19
    invoke-virtual {p0}, LYA0/a;->v()J

    .line 20
    .line 21
    .line 22
    move-result-wide v0

    .line 23
    const-wide/16 v4, 0x1

    .line 24
    .line 25
    cmp-long v2, v0, v4

    .line 26
    .line 27
    if-nez v2, :cond_5

    .line 28
    .line 29
    invoke-virtual {p0}, LYA0/a;->q()Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_5

    .line 34
    .line 35
    new-instance v2, LUA0/b;

    .line 36
    .line 37
    sget-object v0, LDX0/e;->a:LDX0/e;

    .line 38
    .line 39
    invoke-virtual {p0}, LYA0/a;->D()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    check-cast v1, Ljava/lang/String;

    .line 48
    .line 49
    const-string v4, ""

    .line 50
    .line 51
    if-nez v1, :cond_0

    .line 52
    .line 53
    move-object v1, v4

    .line 54
    :cond_0
    invoke-virtual {p0}, LYA0/a;->F()Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object v5

    .line 58
    invoke-static {v5}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v5

    .line 62
    check-cast v5, Ljava/lang/Long;

    .line 63
    .line 64
    const-wide/16 v6, 0x0

    .line 65
    .line 66
    if-eqz v5, :cond_1

    .line 67
    .line 68
    invoke-virtual {v5}, Ljava/lang/Long;->longValue()J

    .line 69
    .line 70
    .line 71
    move-result-wide v9

    .line 72
    goto :goto_0

    .line 73
    :cond_1
    move-wide v9, v6

    .line 74
    :goto_0
    invoke-virtual {v0, v1, v9, v10}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 79
    .line 80
    .line 81
    move-result-object v5

    .line 82
    sget-object v9, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;->CORNERS:Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 83
    .line 84
    invoke-static {v5, v9}, LOA0/c;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 85
    .line 86
    .line 87
    move-result v5

    .line 88
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object v10

    .line 92
    sget-object v11, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;->YELLOW_CARDS:Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 93
    .line 94
    invoke-static {v10, v11}, LOA0/c;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 95
    .line 96
    .line 97
    move-result v10

    .line 98
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 99
    .line 100
    .line 101
    move-result-object v12

    .line 102
    sget-object v13, Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;->RED_CARDS:Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;

    .line 103
    .line 104
    invoke-static {v12, v13}, LOA0/c;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 105
    .line 106
    .line 107
    move-result v12

    .line 108
    invoke-virtual {p0}, LYA0/a;->G()Ljava/util/List;

    .line 109
    .line 110
    .line 111
    move-result-object v14

    .line 112
    invoke-static {v14}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v14

    .line 116
    check-cast v14, Ljava/lang/String;

    .line 117
    .line 118
    if-nez v14, :cond_2

    .line 119
    .line 120
    goto :goto_1

    .line 121
    :cond_2
    move-object v4, v14

    .line 122
    :goto_1
    invoke-virtual {p0}, LYA0/a;->I()Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object v14

    .line 126
    invoke-static {v14}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v14

    .line 130
    check-cast v14, Ljava/lang/Long;

    .line 131
    .line 132
    if-eqz v14, :cond_3

    .line 133
    .line 134
    invoke-virtual {v14}, Ljava/lang/Long;->longValue()J

    .line 135
    .line 136
    .line 137
    move-result-wide v6

    .line 138
    :cond_3
    invoke-virtual {v0, v4, v6, v7}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v0

    .line 142
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 143
    .line 144
    .line 145
    move-result-object v4

    .line 146
    invoke-static {v4, v9}, LOA0/c;->b(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 147
    .line 148
    .line 149
    move-result v4

    .line 150
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 151
    .line 152
    .line 153
    move-result-object v6

    .line 154
    invoke-static {v6, v11}, LOA0/c;->b(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 155
    .line 156
    .line 157
    move-result v11

    .line 158
    invoke-virtual {p0}, LYA0/a;->C()Ljava/util/List;

    .line 159
    .line 160
    .line 161
    move-result-object v6

    .line 162
    invoke-static {v6, v13}, LOA0/c;->b(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/TabloStatisticItemModel$Statistic$Type;)I

    .line 163
    .line 164
    .line 165
    move-result v6

    .line 166
    invoke-virtual {p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 167
    .line 168
    .line 169
    move-result-object p0

    .line 170
    sget-object v7, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->HOSTS_VS_GUESTS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 171
    .line 172
    if-ne p0, v7, :cond_4

    .line 173
    .line 174
    const/4 p0, 0x1

    .line 175
    const/4 v13, 0x1

    .line 176
    :goto_2
    move-object v9, v0

    .line 177
    move v7, v12

    .line 178
    move v12, v6

    .line 179
    move v6, v10

    .line 180
    move v10, v4

    .line 181
    move-object v4, v1

    .line 182
    goto :goto_3

    .line 183
    :cond_4
    const/4 p0, 0x0

    .line 184
    const/4 v13, 0x0

    .line 185
    goto :goto_2

    .line 186
    :goto_3
    invoke-direct/range {v2 .. v13}, LUA0/b;-><init>(Ljava/lang/String;Ljava/lang/String;IIILjava/lang/String;Ljava/lang/String;IIIZ)V

    .line 187
    .line 188
    .line 189
    return-object v2

    .line 190
    :cond_5
    const/4 p0, 0x0

    .line 191
    return-object p0
.end method
