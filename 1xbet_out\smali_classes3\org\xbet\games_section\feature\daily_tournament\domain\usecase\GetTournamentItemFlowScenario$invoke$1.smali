.class final Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.daily_tournament.domain.usecase.GetTournamentItemFlowScenario$invoke$1"
    f = "GetTournamentItemFlowScenario.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;->a()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lp40/a;",
        "Lp40/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Pair<",
        "+",
        "Lp40/b;",
        "+",
        "Lp40/c;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "Lp40/a;",
        "prizes",
        "Lp40/b;",
        "place",
        "Lkotlin/Pair;",
        "Lp40/c;",
        "<anonymous>",
        "(Lp40/a;Lp40/b;)Lkotlin/Pair;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x3

    invoke-direct {p0, v0, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lp40/a;

    check-cast p2, Lp40/b;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->invoke(Lp40/a;Lp40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lp40/a;Lp40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lp40/a;",
            "Lp40/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "Lp40/b;",
            "Lp40/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;

    invoke-direct {v0, p3}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;-><init>(Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lp40/a;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lp40/b;

    .line 18
    .line 19
    invoke-virtual {p1}, Lp40/a;->b()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {p1}, Lp40/a;->a()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    invoke-static {v1, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    check-cast p1, Lp40/c;

    .line 32
    .line 33
    if-nez p1, :cond_0

    .line 34
    .line 35
    new-instance p1, Lp40/c;

    .line 36
    .line 37
    const/4 v1, 0x3

    .line 38
    const/4 v2, 0x0

    .line 39
    invoke-direct {p1, v2, v2, v1, v2}, Lp40/c;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    invoke-static {v0, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    return-object p1

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1
.end method
