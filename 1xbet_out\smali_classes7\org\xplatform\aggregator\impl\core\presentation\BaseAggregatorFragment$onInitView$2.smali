.class final Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.BaseAggregatorFragment$onInitView$2"
    f = "BaseAggregatorFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
        "balanceData",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
            "TT;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->invoke(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$a;

    .line 16
    .line 17
    if-nez v0, :cond_2

    .line 18
    .line 19
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$b;

    .line 20
    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    .line 24
    .line 25
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    if-eqz p1, :cond_2

    .line 30
    .line 31
    const-string v0, ""

    .line 32
    .line 33
    invoke-virtual {p1, v0, v0}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setBalanceValue(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;

    .line 38
    .line 39
    if-eqz v0, :cond_1

    .line 40
    .line 41
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    .line 42
    .line 43
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->G2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 44
    .line 45
    .line 46
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    .line 47
    .line 48
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;

    .line 49
    .line 50
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->a()Lorg/xbet/balance/model/BalanceModel;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$a$c;->b()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-virtual {v0, v1, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->c3(Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    goto :goto_0

    .line 62
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 63
    .line 64
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 65
    .line 66
    .line 67
    throw p1

    .line 68
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 69
    .line 70
    return-object p1

    .line 71
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 72
    .line 73
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 74
    .line 75
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 76
    .line 77
    .line 78
    throw p1
.end method
