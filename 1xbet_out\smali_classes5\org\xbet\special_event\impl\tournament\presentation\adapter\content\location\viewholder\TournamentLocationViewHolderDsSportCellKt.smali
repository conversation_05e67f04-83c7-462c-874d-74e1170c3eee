.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lfx0/a;",
        "tournamentLocationClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lfx0/a;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lfx0/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt;->g(Lfx0/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/d1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/d1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lfx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt;->h(Lfx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lfx0/a;)LA4/c;
    .locals 4
    .param p0    # Lfx0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lfx0/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lkx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lkx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lkx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lkx0/b;-><init>(Lfx0/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt$tournamentLocationAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt$tournamentLocationAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt$tournamentLocationAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/viewholder/TournamentLocationViewHolderDsSportCellKt$tournamentLocationAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/d1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/d1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/d1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lfx0/a;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/d1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/d1;->f:Landroid/view/View;

    .line 8
    .line 9
    new-instance v1, Lkx0/c;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lkx0/c;-><init>(Lfx0/a;LB4/a;)V

    .line 12
    .line 13
    .line 14
    const/4 p0, 0x1

    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 17
    .line 18
    .line 19
    new-instance p0, Lkx0/d;

    .line 20
    .line 21
    invoke-direct {p0, p1}, Lkx0/d;-><init>(LB4/a;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 25
    .line 26
    .line 27
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 28
    .line 29
    return-object p0
.end method

.method public static final h(Lfx0/a;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Ljx0/a;

    .line 6
    .line 7
    invoke-virtual {p2}, Ljx0/a;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, Ljx0/a;

    .line 16
    .line 17
    invoke-virtual {p1}, Ljx0/a;->getTitle()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-interface {p0, v0, v1, p1}, Lfx0/a;->a(JLjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 25
    .line 26
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LGq0/d1;

    .line 6
    .line 7
    iget-object p1, p1, LGq0/d1;->c:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Ljx0/a;

    .line 14
    .line 15
    invoke-virtual {v0}, Ljx0/a;->getTitle()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LGq0/d1;

    .line 27
    .line 28
    iget-object p1, p1, LGq0/d1;->d:Lorg/xbet/uikit/components/passwordrequirement/BulletList;

    .line 29
    .line 30
    const-string v0, ""

    .line 31
    .line 32
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    check-cast p1, LGq0/d1;

    .line 40
    .line 41
    iget-object p1, p1, LGq0/d1;->d:Lorg/xbet/uikit/components/passwordrequirement/BulletList;

    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    check-cast v0, Ljx0/a;

    .line 48
    .line 49
    invoke-virtual {v0}, Ljx0/a;->d()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    if-nez v0, :cond_0

    .line 58
    .line 59
    const/4 v0, 0x0

    .line 60
    goto :goto_0

    .line 61
    :cond_0
    const/16 v0, 0x8

    .line 62
    .line 63
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    check-cast p1, LGq0/d1;

    .line 71
    .line 72
    iget-object p1, p1, LGq0/d1;->d:Lorg/xbet/uikit/components/passwordrequirement/BulletList;

    .line 73
    .line 74
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    check-cast p0, Ljx0/a;

    .line 79
    .line 80
    invoke-virtual {p0}, Ljx0/a;->d()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object p0

    .line 84
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/passwordrequirement/BulletList;->setLabels(Ljava/util/List;)V

    .line 85
    .line 86
    .line 87
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 88
    .line 89
    return-object p0
.end method
