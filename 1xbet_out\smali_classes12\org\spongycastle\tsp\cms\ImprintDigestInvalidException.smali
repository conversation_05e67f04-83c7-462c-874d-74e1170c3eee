.class public Lorg/spongycastle/tsp/cms/ImprintDigestInvalidException;
.super Ljava/lang/Exception;
.source "SourceFile"


# instance fields
.field private token:Lorg/spongycastle/tsp/a;


# direct methods
.method public constructor <init>(Ljava/lang/String;Lorg/spongycastle/tsp/a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public getTimeStampToken()Lorg/spongycastle/tsp/a;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method
