.class public final Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0006\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006R\u0014\u0010\u0008\u001a\u00020\u00078\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0008\u0010\tR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u000e\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0010\u001a\u00020\r8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000fR\u0014\u0010\u0011\u001a\u00020\n8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u000cR\u0014\u0010\u0012\u001a\u00020\n8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u000c\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;",
        "a",
        "()Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;",
        "",
        "DEFAULT_SCREEN_SIZE_THRESHOLD",
        "I",
        "",
        "VALUE_WIDTH_PERCENTAGE",
        "F",
        "",
        "CHANGE_FILTERS_KEY",
        "Ljava/lang/String;",
        "REQUEST_TOP_UP",
        "SMALL_DEVICE_EDITOR_BOTTOM_MARGIN",
        "DEFAULT_DEVICE_EDITOR_BOTTOM_MARGIN",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
