.class public final enum Lorg/xbet/analytics/domain/scope/history/HistoryParamType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/analytics/domain/scope/history/HistoryParamType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/analytics/domain/scope/history/HistoryParamType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u001c\n\u0002\u0010\u000e\n\u0000\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u001d\u001a\u00020\u001ej\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018j\u0002\u0008\u0019j\u0002\u0008\u001aj\u0002\u0008\u001bj\u0002\u0008\u001c\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xbet/analytics/domain/scope/history/HistoryParamType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "BET_STATUS",
        "BET_STATUS_NONE",
        "BET_STATUS_ACCEPTED",
        "BET_STATUS_LOST",
        "BET_STATUS_ALL",
        "BET_STATUS_WAITING",
        "BET_STATUS_DECLINED",
        "BET_STATUS_WIN",
        "BET_STATUS_PAID",
        "BET_STATUS_REMOVED",
        "BET_STATUS_EXPIRED",
        "BET_STATUS_BLOCKED",
        "BET_STATUS_PURCHASING",
        "BET_STATUS_AUTO_BET_WAITING",
        "BET_STATUS_AUTO_BET_DROPPED",
        "BET_FILTER",
        "BET_FILTER_ACCEPTED",
        "BET_FILTER_LOST",
        "BET_FILTER_WON",
        "BET_FILTER_PAID_PUT",
        "BET_FILTER_REMOVED",
        "BET_FILTER_BLOCKED",
        "BET_FILTER_SOLD",
        "BET_FILTER_ALL",
        "BET_STATUS_AUTO_BET_ACTIVATED",
        "getEventName",
        "",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_PAID_PUT:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_SOLD:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_FILTER_WON:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_AUTO_BET_ACTIVATED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_AUTO_BET_DROPPED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_AUTO_BET_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_DECLINED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_EXPIRED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_NONE:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_PAID:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_PURCHASING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

.field public static final enum BET_STATUS_WIN:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 2
    .line 3
    const-string v1, "BET_STATUS"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 12
    .line 13
    const-string v1, "BET_STATUS_NONE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_NONE:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 22
    .line 23
    const-string v1, "BET_STATUS_ACCEPTED"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 32
    .line 33
    const-string v1, "BET_STATUS_LOST"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 42
    .line 43
    const-string v1, "BET_STATUS_ALL"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 52
    .line 53
    const-string v1, "BET_STATUS_WAITING"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 62
    .line 63
    const-string v1, "BET_STATUS_DECLINED"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_DECLINED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 72
    .line 73
    const-string v1, "BET_STATUS_WIN"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_WIN:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 82
    .line 83
    const-string v1, "BET_STATUS_PAID"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_PAID:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 93
    .line 94
    const-string v1, "BET_STATUS_REMOVED"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 104
    .line 105
    const-string v1, "BET_STATUS_EXPIRED"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_EXPIRED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 113
    .line 114
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 115
    .line 116
    const-string v1, "BET_STATUS_BLOCKED"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 124
    .line 125
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 126
    .line 127
    const-string v1, "BET_STATUS_PURCHASING"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_PURCHASING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 135
    .line 136
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 137
    .line 138
    const-string v1, "BET_STATUS_AUTO_BET_WAITING"

    .line 139
    .line 140
    const/16 v2, 0xd

    .line 141
    .line 142
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 143
    .line 144
    .line 145
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 146
    .line 147
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 148
    .line 149
    const-string v1, "BET_STATUS_AUTO_BET_DROPPED"

    .line 150
    .line 151
    const/16 v2, 0xe

    .line 152
    .line 153
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 154
    .line 155
    .line 156
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_DROPPED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 157
    .line 158
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 159
    .line 160
    const-string v1, "BET_FILTER"

    .line 161
    .line 162
    const/16 v2, 0xf

    .line 163
    .line 164
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 165
    .line 166
    .line 167
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 168
    .line 169
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 170
    .line 171
    const-string v1, "BET_FILTER_ACCEPTED"

    .line 172
    .line 173
    const/16 v2, 0x10

    .line 174
    .line 175
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 176
    .line 177
    .line 178
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 179
    .line 180
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 181
    .line 182
    const-string v1, "BET_FILTER_LOST"

    .line 183
    .line 184
    const/16 v2, 0x11

    .line 185
    .line 186
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 187
    .line 188
    .line 189
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 190
    .line 191
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 192
    .line 193
    const-string v1, "BET_FILTER_WON"

    .line 194
    .line 195
    const/16 v2, 0x12

    .line 196
    .line 197
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 198
    .line 199
    .line 200
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_WON:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 201
    .line 202
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 203
    .line 204
    const-string v1, "BET_FILTER_PAID_PUT"

    .line 205
    .line 206
    const/16 v2, 0x13

    .line 207
    .line 208
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 209
    .line 210
    .line 211
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_PAID_PUT:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 212
    .line 213
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 214
    .line 215
    const-string v1, "BET_FILTER_REMOVED"

    .line 216
    .line 217
    const/16 v2, 0x14

    .line 218
    .line 219
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 220
    .line 221
    .line 222
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 223
    .line 224
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 225
    .line 226
    const-string v1, "BET_FILTER_BLOCKED"

    .line 227
    .line 228
    const/16 v2, 0x15

    .line 229
    .line 230
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 231
    .line 232
    .line 233
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 234
    .line 235
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 236
    .line 237
    const-string v1, "BET_FILTER_SOLD"

    .line 238
    .line 239
    const/16 v2, 0x16

    .line 240
    .line 241
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 242
    .line 243
    .line 244
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_SOLD:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 245
    .line 246
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 247
    .line 248
    const-string v1, "BET_FILTER_ALL"

    .line 249
    .line 250
    const/16 v2, 0x17

    .line 251
    .line 252
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 253
    .line 254
    .line 255
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 256
    .line 257
    new-instance v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 258
    .line 259
    const-string v1, "BET_STATUS_AUTO_BET_ACTIVATED"

    .line 260
    .line 261
    const/16 v2, 0x18

    .line 262
    .line 263
    invoke-direct {v0, v1, v2}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;-><init>(Ljava/lang/String;I)V

    .line 264
    .line 265
    .line 266
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_ACTIVATED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 267
    .line 268
    invoke-static {}, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->a()[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->$VALUES:[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 273
    .line 274
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 275
    .line 276
    .line 277
    move-result-object v0

    .line 278
    sput-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->$ENTRIES:Lkotlin/enums/a;

    .line 279
    .line 280
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;
    .locals 3

    .line 1
    const/16 v0, 0x19

    new-array v0, v0, [Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_NONE:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_DECLINED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_WIN:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_PAID:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_EXPIRED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_PURCHASING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_WAITING:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_DROPPED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_ACCEPTED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_LOST:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_WON:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_PAID_PUT:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x13

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_REMOVED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x14

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_BLOCKED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x15

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_SOLD:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x16

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_FILTER_ALL:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x17

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->BET_STATUS_AUTO_BET_ACTIVATED:Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    const/16 v2, 0x18

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/analytics/domain/scope/history/HistoryParamType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/analytics/domain/scope/history/HistoryParamType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType;->$VALUES:[Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/analytics/domain/scope/history/HistoryParamType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getEventName()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/history/HistoryParamType$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const-string v1, "accepted"

    .line 10
    .line 11
    const-string v2, "blocked"

    .line 12
    .line 13
    const-string v3, "lost"

    .line 14
    .line 15
    const-string v4, "removed"

    .line 16
    .line 17
    const-string v5, "all"

    .line 18
    .line 19
    packed-switch v0, :pswitch_data_0

    .line 20
    .line 21
    .line 22
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 23
    .line 24
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 25
    .line 26
    .line 27
    throw v0

    .line 28
    :pswitch_0
    return-object v5

    .line 29
    :pswitch_1
    const-string v0, "sold"

    .line 30
    .line 31
    return-object v0

    .line 32
    :pswitch_2
    return-object v4

    .line 33
    :pswitch_3
    const-string v0, "filter"

    .line 34
    .line 35
    return-object v0

    .line 36
    :pswitch_4
    const-string v0, "paid_put"

    .line 37
    .line 38
    return-object v0

    .line 39
    :pswitch_5
    const-string v0, "won"

    .line 40
    .line 41
    return-object v0

    .line 42
    :pswitch_6
    return-object v3

    .line 43
    :pswitch_7
    return-object v2

    .line 44
    :pswitch_8
    return-object v1

    .line 45
    :pswitch_9
    const-string v0, "claim_accepted"

    .line 46
    .line 47
    return-object v0

    .line 48
    :pswitch_a
    const-string v0, "claim_reset"

    .line 49
    .line 50
    return-object v0

    .line 51
    :pswitch_b
    const-string v0, "claim_waiting"

    .line 52
    .line 53
    return-object v0

    .line 54
    :pswitch_c
    const-string v0, "purchasing"

    .line 55
    .line 56
    return-object v0

    .line 57
    :pswitch_d
    return-object v2

    .line 58
    :pswitch_e
    const-string v0, "expired"

    .line 59
    .line 60
    return-object v0

    .line 61
    :pswitch_f
    return-object v4

    .line 62
    :pswitch_10
    const-string v0, "paid"

    .line 63
    .line 64
    return-object v0

    .line 65
    :pswitch_11
    const-string v0, "win"

    .line 66
    .line 67
    return-object v0

    .line 68
    :pswitch_12
    return-object v3

    .line 69
    :pswitch_13
    const-string v0, "declined"

    .line 70
    .line 71
    return-object v0

    .line 72
    :pswitch_14
    return-object v1

    .line 73
    :pswitch_15
    const-string v0, "waiting"

    .line 74
    .line 75
    return-object v0

    .line 76
    :pswitch_16
    return-object v5

    .line 77
    :pswitch_17
    const-string v0, "none"

    .line 78
    .line 79
    return-object v0

    .line 80
    :pswitch_18
    const-string v0, "status"

    .line 81
    .line 82
    return-object v0

    .line 83
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
