.class public final LrT0/b;
.super Ljava/lang/Object;


# static fields
.field public static blackout:I = 0x7f0a01f7

.field public static blastGameFieldBackground:I = 0x7f0a01f8

.field public static bonusDescriptionText:I = 0x7f0a020a

.field public static bonusSeparator:I = 0x7f0a020e

.field public static bonusText:I = 0x7f0a0211

.field public static changeBetButton:I = 0x7f0a0421

.field public static coeffFive:I = 0x7f0a0516

.field public static coeffFour:I = 0x7f0a0517

.field public static coeffOne:I = 0x7f0a051a

.field public static coeffSix:I = 0x7f0a051b

.field public static coeffThree:I = 0x7f0a051d

.field public static coeffTwo:I = 0x7f0a051e

.field public static descriptionLayout:I = 0x7f0a063f

.field public static gameContainer:I = 0x7f0a0917

.field public static gameEndedDescriptionText:I = 0x7f0a091b

.field public static gameEndedTitleText:I = 0x7f0a091c

.field public static gameField:I = 0x7f0a091d

.field public static ivCoeff:I = 0x7f0a0c04

.field public static ivCoeffValue:I = 0x7f0a0c07

.field public static ivProgress:I = 0x7f0a0cd6

.field public static odysseyGameFieldBackground:I = 0x7f0a0ffa

.field public static playAgainButton:I = 0x7f0a10a2

.field public static progressBar:I = 0x7f0a111f

.field public static tvCoeffCount:I = 0x7f0a19c6

.field public static tvCoeffValue:I = 0x7f0a19c8

.field public static tvCombination:I = 0x7f0a19d4

.field public static txtPlaceBet:I = 0x7f0a1dc9


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
