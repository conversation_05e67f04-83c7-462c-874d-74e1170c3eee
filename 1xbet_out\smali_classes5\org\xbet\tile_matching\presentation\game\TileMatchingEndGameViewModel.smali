.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;,
        Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00b8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008*\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u00002\u00020\u0001:\u0002noB\u009b\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010+\u001a\u00020*2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u0017\u0010.\u001a\u00020*2\u0006\u0010)\u001a\u00020-H\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u0017\u00100\u001a\u00020*2\u0006\u0010)\u001a\u00020-H\u0002\u00a2\u0006\u0004\u00080\u0010/J\u0017\u00102\u001a\u0002012\u0006\u0010)\u001a\u00020-H\u0002\u00a2\u0006\u0004\u00082\u00103J\u0017\u00106\u001a\u00020*2\u0006\u00105\u001a\u000204H\u0002\u00a2\u0006\u0004\u00086\u00107J\u0017\u00108\u001a\u00020*2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u00088\u0010,J\u0015\u0010;\u001a\u0008\u0012\u0004\u0012\u00020:09H\u0000\u00a2\u0006\u0004\u0008;\u0010<J\u0015\u0010>\u001a\u0008\u0012\u0004\u0012\u00020=09H\u0000\u00a2\u0006\u0004\u0008>\u0010<J\u000f\u0010?\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008?\u0010@J\u000f\u0010A\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008A\u0010@J\u000f\u0010B\u001a\u00020*H\u0000\u00a2\u0006\u0004\u0008B\u0010@J\u0017\u0010D\u001a\u00020*2\u0006\u0010C\u001a\u00020:H\u0002\u00a2\u0006\u0004\u0008D\u0010ER\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u001a\u0010k\u001a\u0008\u0012\u0004\u0012\u00020:0h8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u001a\u0010m\u001a\u0008\u0012\u0004\u0012\u00020=0h8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010j\u00a8\u0006p"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/core/domain/usecases/u;",
        "observeCommandUseCase",
        "LwX0/c;",
        "router",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lak/a;",
        "balanceFeature",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "setGameInProgressUseCase",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "getBetSumUseCase",
        "Lorg/xbet/core/domain/usecases/bet/h;",
        "getCurrentMinBetUseCase",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "onBetSetScenario",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "startGameIfPossibleScenario",
        "LWv/b;",
        "getConnectionStatusUseCase",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lorg/xbet/core/domain/usecases/game_state/a;",
        "checkHaveNoFinishGameUseCase",
        "Lorg/xbet/core/domain/usecases/balance/a;",
        "checkBalanceIsChangedUseCase",
        "LVv/d;",
        "getAutoSpinStateUseCase",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "getCurrencyUseCase",
        "Lorg/xbet/tile_matching/domain/usecases/c;",
        "getTileMatchingModelUseCase",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)V",
        "LTv/d;",
        "command",
        "",
        "J3",
        "(LTv/d;)V",
        "LTv/a$j;",
        "M3",
        "(LTv/a$j;)V",
        "R3",
        "",
        "L3",
        "(LTv/a$j;)Z",
        "",
        "throwable",
        "K3",
        "(Ljava/lang/Throwable;)V",
        "G3",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;",
        "H3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
        "I3",
        "N3",
        "()V",
        "O3",
        "P3",
        "event",
        "Q3",
        "(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V",
        "v1",
        "LwX0/c;",
        "x1",
        "LxX0/a;",
        "y1",
        "Lak/a;",
        "F1",
        "Lm8/a;",
        "H1",
        "Lorg/xbet/core/domain/usecases/game_state/l;",
        "I1",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "P1",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "S1",
        "Lorg/xbet/core/domain/usecases/bet/h;",
        "V1",
        "Lorg/xbet/core/domain/usecases/bet/o;",
        "b2",
        "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
        "v2",
        "LWv/b;",
        "x2",
        "Lorg/xbet/core/domain/usecases/d;",
        "y2",
        "Lorg/xbet/core/domain/usecases/game_state/a;",
        "F2",
        "Lorg/xbet/core/domain/usecases/balance/a;",
        "H2",
        "LVv/d;",
        "I2",
        "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
        "P2",
        "Lorg/xbet/tile_matching/domain/usecases/c;",
        "Lkotlinx/coroutines/flow/V;",
        "S2",
        "Lkotlinx/coroutines/flow/V;",
        "viewActions",
        "V2",
        "viewState",
        "b",
        "a",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lorg/xbet/core/domain/usecases/balance/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/core/domain/usecases/game_state/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:LVv/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/core/domain/usecases/bet/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xbet/tile_matching/domain/usecases/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/core/domain/usecases/bet/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/core/domain/usecases/bet/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LWv/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lorg/xbet/core/domain/usecases/game_state/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)V
    .locals 12
    .param p1    # Lorg/xbet/core/domain/usecases/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/core/domain/usecases/game_state/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/core/domain/usecases/bet/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/core/domain/usecases/bet/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/core/domain/usecases/bet/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LWv/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/core/domain/usecases/game_state/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/core/domain/usecases/balance/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LVv/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/tile_matching/domain/usecases/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v1:LwX0/c;

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->x1:LxX0/a;

    .line 7
    .line 8
    move-object/from16 p2, p4

    .line 9
    .line 10
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->y1:Lak/a;

    .line 11
    .line 12
    move-object/from16 p2, p5

    .line 13
    .line 14
    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 15
    .line 16
    move-object/from16 v0, p6

    .line 17
    .line 18
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->H1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 19
    .line 20
    move-object/from16 v0, p7

    .line 21
    .line 22
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->I1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 23
    .line 24
    move-object/from16 v0, p8

    .line 25
    .line 26
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P1:Lorg/xbet/core/domain/usecases/bet/d;

    .line 27
    .line 28
    move-object/from16 v0, p9

    .line 29
    .line 30
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->S1:Lorg/xbet/core/domain/usecases/bet/h;

    .line 31
    .line 32
    move-object/from16 v0, p10

    .line 33
    .line 34
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V1:Lorg/xbet/core/domain/usecases/bet/o;

    .line 35
    .line 36
    move-object/from16 v0, p11

    .line 37
    .line 38
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->b2:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 39
    .line 40
    move-object/from16 v0, p12

    .line 41
    .line 42
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v2:LWv/b;

    .line 43
    .line 44
    move-object/from16 v0, p13

    .line 45
    .line 46
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->x2:Lorg/xbet/core/domain/usecases/d;

    .line 47
    .line 48
    move-object/from16 v0, p14

    .line 49
    .line 50
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/a;

    .line 51
    .line 52
    move-object/from16 v0, p15

    .line 53
    .line 54
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F2:Lorg/xbet/core/domain/usecases/balance/a;

    .line 55
    .line 56
    move-object/from16 v0, p16

    .line 57
    .line 58
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->H2:LVv/d;

    .line 59
    .line 60
    move-object/from16 v0, p17

    .line 61
    .line 62
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->I2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 63
    .line 64
    move-object/from16 v0, p18

    .line 65
    .line 66
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P2:Lorg/xbet/tile_matching/domain/usecases/c;

    .line 67
    .line 68
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;

    .line 69
    .line 70
    const/4 v1, 0x0

    .line 71
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;-><init>(Z)V

    .line 72
    .line 73
    .line 74
    invoke-static {v0}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 79
    .line 80
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 81
    .line 82
    const/16 v1, 0x7f

    .line 83
    .line 84
    const/4 v2, 0x0

    .line 85
    const/4 v3, 0x0

    .line 86
    const/4 v4, 0x0

    .line 87
    const-wide/16 v5, 0x0

    .line 88
    .line 89
    const/4 v7, 0x0

    .line 90
    const/4 v8, 0x0

    .line 91
    const-wide/16 v9, 0x0

    .line 92
    .line 93
    const/4 v11, 0x0

    .line 94
    move-object/from16 p6, v0

    .line 95
    .line 96
    move-object/from16 p17, v2

    .line 97
    .line 98
    move-object/from16 p7, v3

    .line 99
    .line 100
    move-object/from16 p8, v4

    .line 101
    .line 102
    move-wide/from16 p9, v5

    .line 103
    .line 104
    move-object/from16 p11, v7

    .line 105
    .line 106
    move-wide/from16 p13, v9

    .line 107
    .line 108
    const/16 p12, 0x0

    .line 109
    .line 110
    const/16 p15, 0x0

    .line 111
    .line 112
    const/16 p16, 0x7f

    .line 113
    .line 114
    invoke-direct/range {p6 .. p17}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;-><init>(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 115
    .line 116
    .line 117
    invoke-static {v0}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 122
    .line 123
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/u;->a()Lkotlinx/coroutines/flow/e;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$1;

    .line 128
    .line 129
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$1;-><init>(Ljava/lang/Object;)V

    .line 130
    .line 131
    .line 132
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$2;

    .line 137
    .line 138
    const/4 v1, 0x0

    .line 139
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$2;-><init>(Lkotlin/coroutines/e;)V

    .line 140
    .line 141
    .line 142
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 147
    .line 148
    .line 149
    move-result-object v0

    .line 150
    invoke-interface {p2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 151
    .line 152
    .line 153
    move-result-object p2

    .line 154
    invoke-static {v0, p2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 155
    .line 156
    .line 157
    move-result-object p2

    .line 158
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 159
    .line 160
    .line 161
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->b2:Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->K3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/a$j;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->L3(LTv/a$j;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method private final G3(LTv/d;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$addCommand$1;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$addCommand$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$addCommand$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$addCommand$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/d;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final J3(LTv/d;)V
    .locals 1

    .line 1
    instance-of v0, p1, LTv/a$j;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LTv/a$j;

    .line 6
    .line 7
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->M3(LTv/a$j;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    instance-of v0, p1, LTv/b$t;

    .line 12
    .line 13
    if-nez v0, :cond_2

    .line 14
    .line 15
    instance-of v0, p1, LTv/b$o;

    .line 16
    .line 17
    if-nez v0, :cond_2

    .line 18
    .line 19
    instance-of v0, p1, LTv/b$u;

    .line 20
    .line 21
    if-nez v0, :cond_2

    .line 22
    .line 23
    instance-of p1, p1, LTv/b$s;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    return-void

    .line 29
    :cond_2
    :goto_0
    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;

    .line 30
    .line 31
    const/4 v0, 0x1

    .line 32
    invoke-direct {p1, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;-><init>(Z)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method private final K3(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$handleGameError$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final L3(LTv/a$j;)Z
    .locals 5

    .line 1
    invoke-virtual {p1}, LTv/a$j;->b()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/games_section/api/models/GameBonusType;->RETURN_HALF:Lorg/xbet/games_section/api/models/GameBonusType;

    .line 6
    .line 7
    if-ne v0, v1, :cond_1

    .line 8
    .line 9
    invoke-virtual {p1}, LTv/a$j;->g()D

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    const-wide/16 v2, 0x0

    .line 14
    .line 15
    cmpl-double v4, v0, v2

    .line 16
    .line 17
    if-lez v4, :cond_1

    .line 18
    .line 19
    invoke-virtual {p1}, LTv/a$j;->f()Lorg/xbet/core/domain/StatusBetEnum;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    sget-object v1, Lorg/xbet/core/domain/StatusBetEnum;->LOSE:Lorg/xbet/core/domain/StatusBetEnum;

    .line 24
    .line 25
    if-eq v0, v1, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P1:Lorg/xbet/core/domain/usecases/bet/d;

    .line 28
    .line 29
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 30
    .line 31
    .line 32
    move-result-wide v0

    .line 33
    invoke-virtual {p1}, LTv/a$j;->g()D

    .line 34
    .line 35
    .line 36
    move-result-wide v2

    .line 37
    cmpl-double p1, v0, v2

    .line 38
    .line 39
    if-lez p1, :cond_1

    .line 40
    .line 41
    :cond_0
    const/4 p1, 0x1

    .line 42
    return p1

    .line 43
    :cond_1
    const/4 p1, 0x0

    .line 44
    return p1
.end method

.method private final M3(LTv/a$j;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->H2:LVv/d;

    .line 4
    .line 5
    invoke-virtual {v1}, LVv/d;->a()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x1

    .line 10
    if-nez v1, :cond_3

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->y2:Lorg/xbet/core/domain/usecases/game_state/a;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/core/domain/usecases/game_state/a;->a()Z

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-eqz v1, :cond_1

    .line 19
    .line 20
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F2:Lorg/xbet/core/domain/usecases/balance/a;

    .line 21
    .line 22
    invoke-virtual {v1}, Lorg/xbet/core/domain/usecases/balance/a;->a()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    if-nez v1, :cond_0

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    const/4 v1, 0x0

    .line 30
    const/4 v12, 0x0

    .line 31
    goto :goto_1

    .line 32
    :cond_1
    :goto_0
    const/4 v12, 0x1

    .line 33
    :goto_1
    iget-object v1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    :cond_2
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v15

    .line 39
    move-object v3, v15

    .line 40
    check-cast v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 41
    .line 42
    const/16 v13, 0x3f

    .line 43
    .line 44
    const/4 v14, 0x0

    .line 45
    const/4 v4, 0x0

    .line 46
    const/4 v5, 0x0

    .line 47
    const-wide/16 v6, 0x0

    .line 48
    .line 49
    const/4 v8, 0x0

    .line 50
    const/4 v9, 0x0

    .line 51
    const-wide/16 v10, 0x0

    .line 52
    .line 53
    invoke-static/range {v3 .. v14}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;->b(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZDZILjava/lang/Object;)Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    invoke-interface {v1, v15, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v3

    .line 61
    if-eqz v3, :cond_2

    .line 62
    .line 63
    :cond_3
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;

    .line 64
    .line 65
    invoke-direct {v1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;-><init>(Z)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V

    .line 69
    .line 70
    .line 71
    invoke-direct/range {p0 .. p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->R3(LTv/a$j;)V

    .line 72
    .line 73
    .line 74
    return-void
.end method

.method private final R3(LTv/a$j;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$showRestartOptions$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/a$j;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final synthetic p3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->J3(LTv/d;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->p3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->I1:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lak/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->y1:Lak/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)LxX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->x1:LxX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->x2:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P1:Lorg/xbet/core/domain/usecases/bet/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->I2:Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->S1:Lorg/xbet/core/domain/usecases/bet/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P2:Lorg/xbet/tile_matching/domain/usecases/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lorg/xbet/core/domain/usecases/bet/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V1:Lorg/xbet/core/domain/usecases/bet/o;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final H3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final I3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final N3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v2:LWv/b;

    .line 2
    .line 3
    invoke-virtual {v0}, LWv/b;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;-><init>(Z)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->H1:Lorg/xbet/core/domain/usecases/game_state/l;

    .line 20
    .line 21
    const/4 v1, 0x1

    .line 22
    invoke-virtual {v0, v1}, Lorg/xbet/core/domain/usecases/game_state/l;->a(Z)V

    .line 23
    .line 24
    .line 25
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onPlayAgainClicked$1;

    .line 30
    .line 31
    invoke-direct {v3, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onPlayAgainClicked$1;-><init>(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    new-instance v7, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onPlayAgainClicked$2;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {v7, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onPlayAgainClicked$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    const/16 v8, 0xa

    .line 47
    .line 48
    const/4 v9, 0x0

    .line 49
    const/4 v4, 0x0

    .line 50
    const/4 v6, 0x0

    .line 51
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final O3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final P3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->v2:LWv/b;

    .line 2
    .line 3
    invoke-virtual {v0}, LWv/b;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;

    .line 11
    .line 12
    const/4 v1, 0x0

    .line 13
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a$a;-><init>(Z)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V

    .line 17
    .line 18
    .line 19
    sget-object v0, LTv/a$p;->a:LTv/a$p;

    .line 20
    .line 21
    invoke-direct {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->G3(LTv/d;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->F1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
