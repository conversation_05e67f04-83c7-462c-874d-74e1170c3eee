.class public final synthetic Lorg/xbet/themeswitch/impl/data/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/a;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/a;->a:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;

    invoke-static {v0}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->a(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lkotlinx/coroutines/N;

    move-result-object v0

    return-object v0
.end method
