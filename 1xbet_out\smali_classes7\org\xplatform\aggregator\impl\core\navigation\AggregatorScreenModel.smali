.class public final Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0010\u0000\n\u0002\u0008\u0014\u0008\u0087\u0008\u0018\u00002\u00020\u0001BW\u0012\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u0012\u0008\u0008\u0002\u0010\n\u001a\u00020\t\u0012\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\r\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\r\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001d\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\r\u0010\u001a\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ`\u0010\u001c\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00072\u0008\u0008\u0002\u0010\n\u001a\u00020\t2\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u00052\u0008\u0008\u0002\u0010\r\u001a\u00020\u0002H\u00c6\u0001\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0010\u0010\u001e\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0010\u0010 \u001a\u00020\u0015H\u00d6\u0001\u00a2\u0006\u0004\u0008 \u0010\u001bJ\u001a\u0010#\u001a\u00020\u00102\u0008\u0010\"\u001a\u0004\u0018\u00010!H\u00d6\u0003\u00a2\u0006\u0004\u0008#\u0010$R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010%\u001a\u0004\u0008&\u0010\u001fR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\'\u0010%\u001a\u0004\u0008(\u0010\u001fR\u0017\u0010\u0006\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u0008)\u0010*\u001a\u0004\u0008+\u0010,R\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008+\u0010-\u001a\u0004\u0008.\u0010/R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008.\u00100\u001a\u0004\u00081\u00102R\u0017\u0010\u000b\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u00081\u0010*\u001a\u0004\u00083\u0010,R\u0017\u0010\u000c\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u00084\u0010*\u001a\u0004\u0008)\u0010,R\u0017\u0010\r\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008&\u0010%\u001a\u0004\u00084\u0010\u001f\u00a8\u00065"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "Landroid/os/Parcelable;",
        "",
        "title",
        "subtitle",
        "",
        "partitionId",
        "Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;",
        "screenType",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "categoryId",
        "partType",
        "subStringValue",
        "<init>",
        "(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)V",
        "",
        "i",
        "()Z",
        "Landroid/os/Parcel;",
        "dest",
        "",
        "flags",
        "",
        "writeToParcel",
        "(Landroid/os/Parcel;I)V",
        "describeContents",
        "()I",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "Ljava/lang/String;",
        "h",
        "b",
        "getSubtitle",
        "c",
        "J",
        "d",
        "()J",
        "Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;",
        "e",
        "()Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "f",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "getCategoryId",
        "g",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:J

.field public final d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:J

.field public final g:J

.field public final h:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel$a;

    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel$a;-><init>()V

    sput-object v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->CREATOR:Landroid/os/Parcelable$Creator;

    return-void
.end method

.method public constructor <init>()V
    .locals 14

    .line 1
    const/16 v12, 0xff

    const/4 v13, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const-wide/16 v3, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-wide/16 v7, 0x0

    const-wide/16 v9, 0x0

    const/4 v11, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    .line 4
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    .line 5
    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    .line 6
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    .line 7
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 8
    iput-wide p7, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    .line 9
    iput-wide p9, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    .line 10
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p13, p12, 0x1

    .line 11
    const-string v0, ""

    if-eqz p13, :cond_0

    move-object p1, v0

    :cond_0
    and-int/lit8 p13, p12, 0x2

    if-eqz p13, :cond_1

    move-object p2, v0

    :cond_1
    and-int/lit8 p13, p12, 0x4

    if-eqz p13, :cond_2

    const-wide/16 p3, -0x1

    :cond_2
    and-int/lit8 p13, p12, 0x8

    if-eqz p13, :cond_3

    .line 12
    sget-object p5, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$None;->INSTANCE:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$None;

    :cond_3
    and-int/lit8 p13, p12, 0x10

    if-eqz p13, :cond_4

    .line 13
    sget-object p6, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    :cond_4
    and-int/lit8 p13, p12, 0x20

    if-eqz p13, :cond_5

    .line 14
    sget-object p7, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    invoke-virtual {p7}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    move-result-wide p7

    :cond_5
    and-int/lit8 p13, p12, 0x40

    if-eqz p13, :cond_6

    .line 15
    sget-object p9, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    invoke-virtual {p9}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    move-result-wide p9

    :cond_6
    and-int/lit16 p12, p12, 0x80

    if-eqz p12, :cond_7

    move-object p12, v0

    :goto_0
    move-wide p10, p9

    move-wide p8, p7

    move-object p7, p6

    move-object p6, p5

    move-wide p4, p3

    move-object p3, p2

    move-object p2, p1

    move-object p1, p0

    goto :goto_1

    :cond_7
    move-object p12, p11

    goto :goto_0

    .line 16
    :goto_1
    invoke-direct/range {p1 .. p12}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)V

    return-void
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILjava/lang/Object;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 0

    .line 1
    and-int/lit8 p13, p12, 0x1

    if-eqz p13, :cond_0

    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    :cond_0
    and-int/lit8 p13, p12, 0x2

    if-eqz p13, :cond_1

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    :cond_1
    and-int/lit8 p13, p12, 0x4

    if-eqz p13, :cond_2

    iget-wide p3, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    :cond_2
    and-int/lit8 p13, p12, 0x8

    if-eqz p13, :cond_3

    iget-object p5, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    :cond_3
    and-int/lit8 p13, p12, 0x10

    if-eqz p13, :cond_4

    iget-object p6, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    :cond_4
    and-int/lit8 p13, p12, 0x20

    if-eqz p13, :cond_5

    iget-wide p7, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    :cond_5
    and-int/lit8 p13, p12, 0x40

    if-eqz p13, :cond_6

    iget-wide p9, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    :cond_6
    and-int/lit16 p12, p12, 0x80

    if-eqz p12, :cond_7

    iget-object p11, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    :cond_7
    move-object p13, p11

    move-wide p11, p9

    move-wide p9, p7

    move-object p7, p5

    move-object p8, p6

    move-wide p5, p3

    move-object p3, p1

    move-object p4, p2

    move-object p2, p0

    invoke-virtual/range {p2 .. p13}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final a(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 12
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    move-object v1, p1

    move-object v2, p2

    move-wide v3, p3

    move-object/from16 v5, p5

    move-object/from16 v6, p6

    move-wide/from16 v7, p7

    move-wide/from16 v9, p9

    move-object/from16 v11, p11

    invoke-direct/range {v0 .. v11}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)V

    return-object v0
.end method

.method public final c()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final d()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final e()Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    if-eq v1, v3, :cond_6

    return v2

    :cond_6
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_7

    return v2

    :cond_7
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_8

    return v2

    :cond_8
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    iget-object p1, p1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_9

    return v2

    :cond_9
    return v0
.end method

.method public final f()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public final i()Z
    .locals 14

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 2
    .line 3
    const/16 v12, 0xff

    .line 4
    .line 5
    const/4 v13, 0x0

    .line 6
    const/4 v1, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    const-wide/16 v3, 0x0

    .line 9
    .line 10
    const/4 v5, 0x0

    .line 11
    const/4 v6, 0x0

    .line 12
    const-wide/16 v7, 0x0

    .line 13
    .line 14
    const-wide/16 v9, 0x0

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 13
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    iget-wide v6, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    iget-wide v8, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    iget-object v10, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    const-string v12, "AggregatorScreenModel(title="

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", subtitle="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", partitionId="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", screenType="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", searchScreenType="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", categoryId="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", partType="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v8, v9}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", subStringValue="

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 2
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->a:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->b:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->c:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d:Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeSerializable(Ljava/io/Serializable;)V

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->e:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    invoke-virtual {p2}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->f:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->g:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    return-void
.end method
