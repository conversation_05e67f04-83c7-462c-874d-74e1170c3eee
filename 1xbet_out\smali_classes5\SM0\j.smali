.class public final synthetic LSM0/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LSM0/j;->a:LB4/a;

    iput-object p2, p0, LSM0/j;->b:L<PERSON><PERSON>/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LSM0/j;->a:LB4/a;

    iget-object v1, p0, LSM0/j;->b:L<PERSON><PERSON>/jvm/functions/Function1;

    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/stage/impl/stagetable/presentation/common/viewholder/GroupSelectorViewHolderKt;->b(LB4/a;Lkotlin/jvm/functions/Function1;Landroid/view/View;)V

    return-void
.end method
