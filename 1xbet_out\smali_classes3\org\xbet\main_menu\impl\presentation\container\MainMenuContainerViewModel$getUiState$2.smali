.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$getUiState$2"
    f = "MainMenuContainerViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->E0()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
        ">;",
        "Ljava/lang/Throwable;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u0004*\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
        "",
        "it",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Ljava/lang/Throwable;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
            ">;",
            "Ljava/lang/Throwable;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;

    iget-object p2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {p1, p2, p3}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x1

    .line 18
    const/4 v1, 0x0

    .line 19
    if-eqz p1, :cond_0

    .line 20
    .line 21
    invoke-static {p1, v1, v0, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 25
    .line 26
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    if-eqz p1, :cond_1

    .line 31
    .line 32
    invoke-static {p1, v1, v0, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    :cond_1
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    if-eqz p1, :cond_2

    .line 42
    .line 43
    invoke-static {p1, v1, v0, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    :cond_2
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->R3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    if-eqz p1, :cond_3

    .line 53
    .line 54
    invoke-static {p1, v1, v0, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    :cond_3
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 58
    .line 59
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->M3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    if-eqz p1, :cond_4

    .line 64
    .line 65
    invoke-static {p1, v1, v0, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 69
    .line 70
    return-object p1

    .line 71
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 72
    .line 73
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 74
    .line 75
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 76
    .line 77
    .line 78
    throw p1
.end method
