.class public final synthetic LK1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static a(Ljava/util/concurrent/Executor;Lt1/l;)LK1/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Ljava/util/concurrent/Executor;",
            ">(TT;",
            "Lt1/l<",
            "TT;>;)",
            "LK1/b;"
        }
    .end annotation

    .line 1
    new-instance v0, LK1/a$a;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LK1/a$a;-><init>(Ljava/util/concurrent/Executor;Lt1/l;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
