.class public final LNA0/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u001a\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a%\u0010\n\u001a\u00020\t2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a%\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000c2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LYA0/a;",
        "LRA0/d;",
        "c",
        "(LYA0/a;)LRA0/d;",
        "",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
        "statisticItemModelList",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
        "key",
        "",
        "a",
        "(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;",
        "",
        "sportId",
        "",
        "b",
        "(JLjava/util/List;)Z",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
            ">;",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->a()Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    if-ne v2, p1, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_1
    move-object v0, v1

    .line 27
    :goto_0
    check-cast v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    invoke-virtual {v0}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->b()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    :cond_2
    const-string p0, ""

    .line 36
    .line 37
    if-eqz v1, :cond_4

    .line 38
    .line 39
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    if-nez p1, :cond_3

    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_3
    new-instance p1, Lorg/json/JSONObject;

    .line 47
    .line 48
    invoke-direct {p1, v1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    const-string v0, "Shtout"

    .line 52
    .line 53
    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->has(Ljava/lang/String;)Z

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    if-eqz v1, :cond_4

    .line 58
    .line 59
    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    new-instance v0, Lkotlin/text/Regex;

    .line 64
    .line 65
    const-string v1, "[^a-z]"

    .line 66
    .line 67
    invoke-direct {v0, v1}, Lkotlin/text/Regex;-><init>(Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {v0, p1, p0}, Lkotlin/text/Regex;->replace(Ljava/lang/CharSequence;Ljava/lang/String;)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    :cond_4
    :goto_1
    return-object p0
.end method

.method public static final b(JLjava/util/List;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
            ">;)Z"
        }
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->STAT_ONE:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 2
    .line 3
    invoke-static {p2, v0}, LNA0/k;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v1, 0x1

    .line 12
    const/4 v2, 0x0

    .line 13
    if-lez v0, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    sget-object v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->STAT_TWO:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 17
    .line 18
    invoke-static {p2, v0}, LNA0/k;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p2

    .line 22
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    if-lez p2, :cond_1

    .line 27
    .line 28
    :goto_0
    const/4 p2, 0x1

    .line 29
    goto :goto_1

    .line 30
    :cond_1
    const/4 p2, 0x0

    .line 31
    :goto_1
    const-wide/16 v3, 0x1

    .line 32
    .line 33
    cmp-long v0, p0, v3

    .line 34
    .line 35
    if-eqz v0, :cond_3

    .line 36
    .line 37
    const-wide/16 v3, 0x2

    .line 38
    .line 39
    cmp-long v0, p0, v3

    .line 40
    .line 41
    if-eqz v0, :cond_3

    .line 42
    .line 43
    const-wide/16 v3, 0x3

    .line 44
    .line 45
    cmp-long v0, p0, v3

    .line 46
    .line 47
    if-nez v0, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    const/4 p0, 0x0

    .line 51
    goto :goto_3

    .line 52
    :cond_3
    :goto_2
    const/4 p0, 0x1

    .line 53
    :goto_3
    if-eqz p2, :cond_4

    .line 54
    .line 55
    if-eqz p0, :cond_4

    .line 56
    .line 57
    return v1

    .line 58
    :cond_4
    return v2
.end method

.method public static final c(LYA0/a;)LRA0/d;
    .locals 4
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, LYA0/a;->v()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-static {v0, v1, v2}, LNA0/k;->b(JLjava/util/List;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_2

    .line 14
    .line 15
    invoke-virtual {p0}, LYA0/a;->D()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    check-cast v0, Ljava/lang/String;

    .line 24
    .line 25
    const-string v1, ""

    .line 26
    .line 27
    if-nez v0, :cond_0

    .line 28
    .line 29
    move-object v0, v1

    .line 30
    :cond_0
    invoke-virtual {p0}, LYA0/a;->G()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-static {v2}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    check-cast v2, Ljava/lang/String;

    .line 39
    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v1, v2

    .line 44
    :goto_0
    invoke-virtual {p0}, LYA0/a;->h()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    invoke-static {p0}, LNA0/p;->a(LYA0/a;)LRA0/g;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    new-instance v3, LRA0/d;

    .line 53
    .line 54
    invoke-direct {v3, p0, v0, v1, v2}, LRA0/d;-><init>(LRA0/g;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 55
    .line 56
    .line 57
    return-object v3

    .line 58
    :cond_2
    const/4 p0, 0x0

    .line 59
    return-object p0
.end method
