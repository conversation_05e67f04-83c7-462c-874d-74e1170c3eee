.class public final LmE0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u001a\u0019\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u001a\u0013\u0010\u0006\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a\u0013\u0010\u0008\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\u0007\u001a\u0013\u0010\t\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0007\u001a\u0013\u0010\n\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0007\u001a\u0013\u0010\u000b\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0007\u001a\u0013\u0010\u000c\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\u0007\u001a\u0013\u0010\r\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u0007\u001a\u0013\u0010\u000e\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0007\u001a\u0013\u0010\u000f\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0007\u001a\u000f\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001a\u0013\u0010\u0013\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0007\u001a\u0013\u0010\u0014\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0007\u001a\u0013\u0010\u0015\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0007\u001a\u0013\u0010\u0016\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0007\u001a\u000f\u0010\u0017\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0012\u001a\u0013\u0010\u0018\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0007\u001a\u0013\u0010\u0019\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0007\u001a\u0013\u0010\u001a\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0007\u001a\u0013\u0010\u001b\u001a\u00020\u0005*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0007\u00a8\u0006\u001c"
    }
    d2 = {
        "LiE0/a;",
        "",
        "LnE0/c;",
        "c",
        "(LiE0/a;)Ljava/util/List;",
        "LnE0/a;",
        "b",
        "(LiE0/a;)LnE0/a;",
        "o",
        "l",
        "n",
        "k",
        "f",
        "t",
        "a",
        "m",
        "LnE0/b;",
        "p",
        "()LnE0/b;",
        "h",
        "g",
        "j",
        "i",
        "e",
        "r",
        "q",
        "s",
        "d",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->arm_span:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->a()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->a()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final b(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->country_title:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->b()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->b()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final c(LiE0/a;)Ljava/util/List;
    .locals 21
    .param p0    # LiE0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LiE0/a;",
            ")",
            "Ljava/util/List<",
            "LnE0/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static/range {p0 .. p0}, LmE0/a;->b(LiE0/a;)LnE0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static/range {p0 .. p0}, LmE0/a;->o(LiE0/a;)LnE0/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static/range {p0 .. p0}, LmE0/a;->l(LiE0/a;)LnE0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static/range {p0 .. p0}, LmE0/a;->n(LiE0/a;)LnE0/a;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    invoke-static/range {p0 .. p0}, LmE0/a;->k(LiE0/a;)LnE0/a;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    invoke-static/range {p0 .. p0}, LmE0/a;->f(LiE0/a;)LnE0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v5

    .line 25
    invoke-static/range {p0 .. p0}, LmE0/a;->t(LiE0/a;)LnE0/a;

    .line 26
    .line 27
    .line 28
    move-result-object v6

    .line 29
    invoke-static/range {p0 .. p0}, LmE0/a;->a(LiE0/a;)LnE0/a;

    .line 30
    .line 31
    .line 32
    move-result-object v7

    .line 33
    invoke-static/range {p0 .. p0}, LmE0/a;->m(LiE0/a;)LnE0/a;

    .line 34
    .line 35
    .line 36
    move-result-object v8

    .line 37
    invoke-static {}, LmE0/a;->p()LnE0/b;

    .line 38
    .line 39
    .line 40
    move-result-object v9

    .line 41
    invoke-static/range {p0 .. p0}, LmE0/a;->h(LiE0/a;)LnE0/a;

    .line 42
    .line 43
    .line 44
    move-result-object v10

    .line 45
    invoke-static/range {p0 .. p0}, LmE0/a;->g(LiE0/a;)LnE0/a;

    .line 46
    .line 47
    .line 48
    move-result-object v11

    .line 49
    invoke-static/range {p0 .. p0}, LmE0/a;->j(LiE0/a;)LnE0/a;

    .line 50
    .line 51
    .line 52
    move-result-object v12

    .line 53
    invoke-static/range {p0 .. p0}, LmE0/a;->i(LiE0/a;)LnE0/a;

    .line 54
    .line 55
    .line 56
    move-result-object v13

    .line 57
    invoke-static {}, LmE0/a;->e()LnE0/b;

    .line 58
    .line 59
    .line 60
    move-result-object v14

    .line 61
    invoke-static/range {p0 .. p0}, LmE0/a;->r(LiE0/a;)LnE0/a;

    .line 62
    .line 63
    .line 64
    move-result-object v15

    .line 65
    invoke-static/range {p0 .. p0}, LmE0/a;->q(LiE0/a;)LnE0/a;

    .line 66
    .line 67
    .line 68
    move-result-object v16

    .line 69
    invoke-static/range {p0 .. p0}, LmE0/a;->s(LiE0/a;)LnE0/a;

    .line 70
    .line 71
    .line 72
    move-result-object v17

    .line 73
    invoke-static/range {p0 .. p0}, LmE0/a;->d(LiE0/a;)LnE0/a;

    .line 74
    .line 75
    .line 76
    move-result-object v18

    .line 77
    move-object/from16 v19, v0

    .line 78
    .line 79
    const/16 v0, 0x13

    .line 80
    .line 81
    new-array v0, v0, [LnE0/c;

    .line 82
    .line 83
    const/16 v20, 0x0

    .line 84
    .line 85
    aput-object v19, v0, v20

    .line 86
    .line 87
    const/16 v19, 0x1

    .line 88
    .line 89
    aput-object v1, v0, v19

    .line 90
    .line 91
    const/4 v1, 0x2

    .line 92
    aput-object v2, v0, v1

    .line 93
    .line 94
    const/4 v1, 0x3

    .line 95
    aput-object v3, v0, v1

    .line 96
    .line 97
    const/4 v1, 0x4

    .line 98
    aput-object v4, v0, v1

    .line 99
    .line 100
    const/4 v1, 0x5

    .line 101
    aput-object v5, v0, v1

    .line 102
    .line 103
    const/4 v1, 0x6

    .line 104
    aput-object v6, v0, v1

    .line 105
    .line 106
    const/4 v1, 0x7

    .line 107
    aput-object v7, v0, v1

    .line 108
    .line 109
    const/16 v1, 0x8

    .line 110
    .line 111
    aput-object v8, v0, v1

    .line 112
    .line 113
    const/16 v1, 0x9

    .line 114
    .line 115
    aput-object v9, v0, v1

    .line 116
    .line 117
    const/16 v1, 0xa

    .line 118
    .line 119
    aput-object v10, v0, v1

    .line 120
    .line 121
    const/16 v1, 0xb

    .line 122
    .line 123
    aput-object v11, v0, v1

    .line 124
    .line 125
    const/16 v1, 0xc

    .line 126
    .line 127
    aput-object v12, v0, v1

    .line 128
    .line 129
    const/16 v1, 0xd

    .line 130
    .line 131
    aput-object v13, v0, v1

    .line 132
    .line 133
    const/16 v1, 0xe

    .line 134
    .line 135
    aput-object v14, v0, v1

    .line 136
    .line 137
    const/16 v1, 0xf

    .line 138
    .line 139
    aput-object v15, v0, v1

    .line 140
    .line 141
    const/16 v1, 0x10

    .line 142
    .line 143
    aput-object v16, v0, v1

    .line 144
    .line 145
    const/16 v1, 0x11

    .line 146
    .line 147
    aput-object v17, v0, v1

    .line 148
    .line 149
    const/16 v1, 0x12

    .line 150
    .line 151
    aput-object v18, v0, v1

    .line 152
    .line 153
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    return-object v0
.end method

.method public static final d(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->free_defeat:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->c()LiE0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/c;->b()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->c()LiE0/c;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/c;->b()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final e()LnE0/b;
    .locals 4

    .line 1
    new-instance v0, LnE0/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 4
    .line 5
    sget v2, Lpb/k;->grappling:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    new-array v3, v3, [Ljava/lang/CharSequence;

    .line 9
    .line 10
    invoke-direct {v1, v2, v3}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {v0, v1}, LnE0/b;-><init>(Lorg/xbet/ui_common/resources/UiText;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public static final f(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->height:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->d()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->d()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final g(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->hits_accuracy:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->j()LiE0/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/d;->b()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->j()LiE0/d;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/d;->b()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final h(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->hits_per_minute:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->j()LiE0/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/d;->c()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->j()LiE0/d;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/d;->c()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final i(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->hits_protection:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->j()LiE0/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/d;->d()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->j()LiE0/d;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/d;->d()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final j(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->hits_received_per_minute:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->j()LiE0/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/d;->e()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->j()LiE0/d;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/d;->e()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final k(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->judgment:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->e()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->e()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final l(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->knockout:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->f()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->f()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final m(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->leg_span:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->g()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->g()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final n(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->pain_techniques:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->h()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->h()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final o(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->statistic_fight_record:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->i()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->i()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method

.method public static final p()LnE0/b;
    .locals 4

    .line 1
    new-instance v0, LnE0/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 4
    .line 5
    sget v2, Lpb/k;->significant_hits:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    new-array v3, v3, [Ljava/lang/CharSequence;

    .line 9
    .line 10
    invoke-direct {v1, v2, v3}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {v0, v1}, LnE0/b;-><init>(Lorg/xbet/ui_common/resources/UiText;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public static final q(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->takedown_accuracy:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->c()LiE0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/c;->c()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->c()LiE0/c;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/c;->c()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final r(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->takedown_averaged:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->c()LiE0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/c;->d()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->c()LiE0/c;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/c;->d()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final s(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->takedown_protection:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->c()LiE0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LiE0/c;->e()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const-string v3, "-"

    .line 28
    .line 29
    if-nez v2, :cond_0

    .line 30
    .line 31
    move-object v1, v3

    .line 32
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, LiE0/b;->c()LiE0/c;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p0}, LiE0/c;->e()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-nez v2, :cond_1

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    move-object v3, p0

    .line 52
    :goto_0
    new-instance p0, LnE0/a;

    .line 53
    .line 54
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    return-object p0
.end method

.method public static final t(LiE0/a;)LnE0/a;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/ui_common/resources/UiText$ByRes;

    .line 2
    .line 3
    sget v1, Lpb/k;->weight:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/CharSequence;

    .line 7
    .line 8
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/resources/UiText$ByRes;-><init>(I[Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, LiE0/a;->a()LiE0/b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LiE0/b;->k()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const-string v3, "-"

    .line 24
    .line 25
    if-nez v2, :cond_0

    .line 26
    .line 27
    move-object v1, v3

    .line 28
    :cond_0
    invoke-virtual {p0}, LiE0/a;->b()LiE0/b;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, LiE0/b;->k()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_1
    move-object v3, p0

    .line 44
    :goto_0
    new-instance p0, LnE0/a;

    .line 45
    .line 46
    invoke-direct {p0, v0, v1, v3}, LnE0/a;-><init>(Lorg/xbet/ui_common/resources/UiText;Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    return-object p0
.end method
