.class public interface abstract Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;,
        Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;,
        Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;,
        Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;,
        Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008w\u0018\u00002\u00020\u0001:\u0005\u0002\u0003\u0004\u0005\u0006\u0082\u0001\u0005\u0007\u0008\t\n\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "Landroid/os/Parcelable;",
        "UserAgreementDocumentsDialog",
        "ActivatePhoneKzAlert",
        "IdentificationAlertKzAlert",
        "KzFirstDepositBottom",
        "NotIdentifiedKzAlert",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$ActivatePhoneKzAlert;",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$IdentificationAlertKzAlert;",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$KzFirstDepositBottom;",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$NotIdentifiedKzAlert;",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType$UserAgreementDocumentsDialog;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
