.class final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.promo.presentation.OneXGamesPromoViewModel$handlePromoItems$2"
    f = "OneXGamesPromoViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->g4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $promoUiModelList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lq50/a;",
            ">;"
        }
    .end annotation
.end field

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
            "Ljava/util/List<",
            "Lq50/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->$promoUiModelList:Ljava/util/List;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->$promoUiModelList:Ljava/util/List;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Ljava/util/List;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$d;

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->$promoUiModelList:Ljava/util/List;

    .line 16
    .line 17
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b$d;-><init>(Ljava/util/List;)V

    .line 18
    .line 19
    .line 20
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->P3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$b;)V

    .line 21
    .line 22
    .line 23
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 24
    .line 25
    invoke-static {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->G3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    sget-object v0, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->UNKNOWN:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 30
    .line 31
    if-eq p1, v0, :cond_2

    .line 32
    .line 33
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->$promoUiModelList:Ljava/util/List;

    .line 36
    .line 37
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    if-eqz v1, :cond_1

    .line 46
    .line 47
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    check-cast v1, Lq50/a;

    .line 52
    .line 53
    invoke-virtual {v1}, Lq50/a;->b()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    invoke-static {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->G3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;)Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    if-ne v2, v3, :cond_0

    .line 62
    .line 63
    invoke-static {p1, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->O3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lq50/a;)V

    .line 64
    .line 65
    .line 66
    iget-object p1, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel$handlePromoItems$2;->this$0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 67
    .line 68
    sget-object v0, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->UNKNOWN:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 69
    .line 70
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->S3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    .line 71
    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_1
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 75
    .line 76
    const-string v0, "Collection contains no element matching the predicate."

    .line 77
    .line 78
    invoke-direct {p1, v0}, Ljava/util/NoSuchElementException;-><init>(Ljava/lang/String;)V

    .line 79
    .line 80
    .line 81
    throw p1

    .line 82
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 83
    .line 84
    return-object p1

    .line 85
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 86
    .line 87
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 88
    .line 89
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 90
    .line 91
    .line 92
    throw p1
.end method
