.class public final Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0006\n\u0002\u0008\u0007\n\u0002\u0010\u0007\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0010\t\n\u0002\u0008\r\u0018\u00002\u00020\u0001B9\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001e\u0010\u0014\u001a\u00020\u00132\u000c\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u0010H\u0080@\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001b\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00110\u00100\u0016H\u0000\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u0019\u001a\u00020\u0011H\u0000\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001d\u001a\u00020\u001a2\u0006\u0010\u0019\u001a\u00020\u0011H\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u001cJ\u000f\u0010\u001e\u001a\u00020\u001aH\u0000\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\u001aH\u0000\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u0017\u0010#\u001a\u00020\u001a2\u0006\u0010\"\u001a\u00020!H\u0000\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020!H\u0000\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\u001aH\u0000\u00a2\u0006\u0004\u0008\'\u0010\u001fJ\u000f\u0010)\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008)\u0010*J\u0017\u0010,\u001a\u00020\u001a2\u0006\u0010+\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008,\u0010-J\u001d\u00100\u001a\u00020/2\u000c\u0010.\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u0010H\u0000\u00a2\u0006\u0004\u00080\u00101J\u0017\u00103\u001a\u00020\u001a2\u0006\u00102\u001a\u00020\u0013H\u0000\u00a2\u0006\u0004\u00083\u00104J\u000f\u00105\u001a\u00020\u0013H\u0000\u00a2\u0006\u0004\u00085\u00106J\u0017\u00109\u001a\u00020\u001a2\u0006\u00108\u001a\u000207H\u0000\u00a2\u0006\u0004\u00089\u0010:J\u000f\u0010;\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008;\u0010<J\u000f\u0010=\u001a\u000207H\u0000\u00a2\u0006\u0004\u0008=\u0010<J\u0017\u0010?\u001a\u00020>2\u0006\u0010+\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008?\u0010@J\u0015\u0010A\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u0010H\u0000\u00a2\u0006\u0004\u0008A\u0010BJ\u000f\u0010D\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008D\u0010ER\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010NR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010O\u00a8\u0006P"
    }
    d2 = {
        "Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;",
        "",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "getActiveBalanceUseCase",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "getBonusUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "updateLastBetForMultiChoiceGameScenario",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;",
        "africanRouletteRepository",
        "Lm8/a;",
        "coroutineDispatchers",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/balance/c;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/game_info/H;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;Lm8/a;)V",
        "",
        "Lig/a;",
        "rouletteBets",
        "Lig/b;",
        "r",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/e;",
        "o",
        "()Lkotlinx/coroutines/flow/e;",
        "bet",
        "",
        "t",
        "(Lig/a;)V",
        "e",
        "s",
        "()V",
        "i",
        "Lorg/xbet/games_section/api/models/GameBonusType;",
        "bonus",
        "v",
        "(Lorg/xbet/games_section/api/models/GameBonusType;)V",
        "n",
        "()Lorg/xbet/games_section/api/models/GameBonusType;",
        "g",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "q",
        "()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "africanRouletteBetType",
        "x",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "betsList",
        "",
        "j",
        "(Ljava/util/List;)D",
        "africanRouletteGameModel",
        "u",
        "(Lig/b;)V",
        "l",
        "()Lig/b;",
        "",
        "offset",
        "w",
        "(F)V",
        "p",
        "()F",
        "f",
        "",
        "h",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z",
        "m",
        "()Ljava/util/List;",
        "",
        "k",
        "()J",
        "a",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "b",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "c",
        "Lorg/xbet/core/domain/usecases/game_info/H;",
        "d",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;",
        "Lm8/a;",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/domain/usecases/balance/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/core/domain/usecases/bonus/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/core/domain/usecases/game_info/H;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/balance/c;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/game_info/H;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;Lm8/a;)V
    .locals 0
    .param p1    # Lorg/xbet/core/domain/usecases/balance/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/bonus/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/game_info/H;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->a:Lorg/xbet/core/domain/usecases/balance/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->b:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->c:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->f:Lm8/a;

    .line 15
    .line 16
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)J
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->k()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    return-wide v0
.end method

.method public static final synthetic c(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)Lorg/xbet/core/domain/usecases/bonus/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->b:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final e(Lig/a;)V
    .locals 1
    .param p1    # Lig/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->b(Lig/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final f()F
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->d()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public final g()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->e()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final h(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z
    .locals 1
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->f(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public final i()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->g()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final j(Ljava/util/List;)D
    .locals 4
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lig/a;",
            ">;)D"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const-wide/16 v0, 0x0

    .line 6
    .line 7
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-eqz v2, :cond_0

    .line 12
    .line 13
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    check-cast v2, Lig/a;

    .line 18
    .line 19
    invoke-virtual {v2}, Lig/a;->c()D

    .line 20
    .line 21
    .line 22
    move-result-wide v2

    .line 23
    add-double/2addr v0, v2

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    return-wide v0
.end method

.method public final k()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->a:Lorg/xbet/core/domain/usecases/balance/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/core/domain/usecases/balance/c;->a()Lorg/xbet/balance/model/BalanceModel;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    return-wide v0

    .line 14
    :cond_0
    new-instance v0, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;

    .line 15
    .line 16
    const-wide/16 v1, -0x1

    .line 17
    .line 18
    invoke-direct {v0, v1, v2}, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;-><init>(J)V

    .line 19
    .line 20
    .line 21
    throw v0
.end method

.method public final l()Lig/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->h()Lig/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final m()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lig/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->i()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final n()Lorg/xbet/games_section/api/models/GameBonusType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->j()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final o()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lig/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->l()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final p()F
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->m()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public final q()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->n()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final r(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lig/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lig/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->f:Lm8/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, p1, v2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2;-><init>(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;Ljava/util/List;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1, p2}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public final s()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->p()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(Lig/a;)V
    .locals 2
    .param p1    # Lig/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->q(Lig/a;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->c:Lorg/xbet/core/domain/usecases/game_info/H;

    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->k()D

    .line 11
    .line 12
    .line 13
    move-result-wide v0

    .line 14
    invoke-virtual {p1, v0, v1}, Lorg/xbet/core/domain/usecases/game_info/H;->a(D)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final u(Lig/b;)V
    .locals 1
    .param p1    # Lig/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->r(Lig/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final v(Lorg/xbet/games_section/api/models/GameBonusType;)V
    .locals 1
    .param p1    # Lorg/xbet/games_section/api/models/GameBonusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->s(Lorg/xbet/games_section/api/models/GameBonusType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final w(F)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->t(F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final x(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 1
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->e:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->u(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
