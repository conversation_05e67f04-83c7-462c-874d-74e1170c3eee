.class public final synthetic LKX0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:Lkotlin/jvm/internal/Ref$IntRef;

.field public final synthetic c:I

.field public final synthetic d:J

.field public final synthetic e:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Ljava/util/List;Lkotlin/jvm/internal/Ref$IntRef;IJLjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKX0/e;->a:Ljava/util/List;

    iput-object p2, p0, LKX0/e;->b:Lkotlin/jvm/internal/Ref$IntRef;

    iput p3, p0, LKX0/e;->c:I

    iput-wide p4, p0, LKX0/e;->d:J

    iput-object p6, p0, LKX0/e;->e:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, LKX0/e;->a:Ljava/util/List;

    iget-object v1, p0, LKX0/e;->b:Lkotlin/jvm/internal/Ref$IntRef;

    iget v2, p0, LKX0/e;->c:I

    iget-wide v3, p0, LKX0/e;->d:J

    iget-object v5, p0, LKX0/e;->e:Ljava/lang/String;

    move-object v6, p1

    check-cast v6, Ljava/lang/Throwable;

    invoke-static/range {v0 .. v6}, LKX0/m;->c(Ljava/util/List;Lkotlin/jvm/internal/Ref$IntRef;IJLjava/lang/String;Ljava/lang/Throwable;)LRe/b;

    move-result-object p1

    return-object p1
.end method
