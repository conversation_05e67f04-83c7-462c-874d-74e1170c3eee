.class public final enum Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0016\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\u0008\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;",
        "",
        "value",
        "",
        "<init>",
        "(Ljava/lang/String;ILjava/lang/String;)V",
        "getValue",
        "()Ljava/lang/String;",
        "Main",
        "MenuOneXGames",
        "OneXAll",
        "OneXSearch",
        "OneXFavourite",
        "OneXDaily",
        "OneXCashback",
        "OneXFavouriteNew",
        "Game",
        "OneXBonuses",
        "UserIcon",
        "Viewed",
        "MainStrip",
        "PopularNewTop",
        "PopularNewXGames",
        "OneXGames",
        "MainScreen",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum Game:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum Main:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum MainScreen:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum MainStrip:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum MenuOneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXAll:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXBonuses:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXCashback:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXDaily:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXFavourite:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXFavouriteNew:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum OneXSearch:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum PopularNewTop:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum UserIcon:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

.field public static final enum Viewed:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;


# instance fields
.field private final value:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const-string v2, "main_screen"

    .line 5
    .line 6
    const-string v3, "Main"

    .line 7
    .line 8
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 9
    .line 10
    .line 11
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Main:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    const-string v2, "menu_games"

    .line 17
    .line 18
    const-string v3, "MenuOneXGames"

    .line 19
    .line 20
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 21
    .line 22
    .line 23
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MenuOneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 24
    .line 25
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 26
    .line 27
    const/4 v1, 0x2

    .line 28
    const-string v2, "games_all"

    .line 29
    .line 30
    const-string v3, "OneXAll"

    .line 31
    .line 32
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 33
    .line 34
    .line 35
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXAll:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 36
    .line 37
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 38
    .line 39
    const/4 v1, 0x3

    .line 40
    const-string v2, "search_games"

    .line 41
    .line 42
    const-string v3, "OneXSearch"

    .line 43
    .line 44
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 45
    .line 46
    .line 47
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXSearch:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 48
    .line 49
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 50
    .line 51
    const/4 v1, 0x4

    .line 52
    const-string v2, "games_favor"

    .line 53
    .line 54
    const-string v3, "OneXFavourite"

    .line 55
    .line 56
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXFavourite:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 62
    .line 63
    const/4 v1, 0x5

    .line 64
    const-string v2, "games_daily"

    .line 65
    .line 66
    const-string v3, "OneXDaily"

    .line 67
    .line 68
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 69
    .line 70
    .line 71
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXDaily:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 72
    .line 73
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 74
    .line 75
    const/4 v1, 0x6

    .line 76
    const-string v2, "cashback"

    .line 77
    .line 78
    const-string v3, "OneXCashback"

    .line 79
    .line 80
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 81
    .line 82
    .line 83
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXCashback:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 84
    .line 85
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 86
    .line 87
    const/4 v1, 0x7

    .line 88
    const-string v2, "favorite"

    .line 89
    .line 90
    const-string v3, "OneXFavouriteNew"

    .line 91
    .line 92
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 93
    .line 94
    .line 95
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXFavouriteNew:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 96
    .line 97
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 98
    .line 99
    const-string v1, "Game"

    .line 100
    .line 101
    const/16 v2, 0x8

    .line 102
    .line 103
    const-string v3, "games"

    .line 104
    .line 105
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 106
    .line 107
    .line 108
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Game:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 109
    .line 110
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 111
    .line 112
    const/16 v1, 0x9

    .line 113
    .line 114
    const-string v2, "bonuses"

    .line 115
    .line 116
    const-string v4, "OneXBonuses"

    .line 117
    .line 118
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 119
    .line 120
    .line 121
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXBonuses:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 122
    .line 123
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 124
    .line 125
    const/16 v1, 0xa

    .line 126
    .line 127
    const-string v2, "user_icon"

    .line 128
    .line 129
    const-string v4, "UserIcon"

    .line 130
    .line 131
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->UserIcon:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 135
    .line 136
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 137
    .line 138
    const/16 v1, 0xb

    .line 139
    .line 140
    const-string v2, "viewed"

    .line 141
    .line 142
    const-string v4, "Viewed"

    .line 143
    .line 144
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 145
    .line 146
    .line 147
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Viewed:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 148
    .line 149
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 150
    .line 151
    const/16 v1, 0xc

    .line 152
    .line 153
    const-string v2, "main_screen_strip"

    .line 154
    .line 155
    const-string v4, "MainStrip"

    .line 156
    .line 157
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 158
    .line 159
    .line 160
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MainStrip:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 161
    .line 162
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 163
    .line 164
    const/16 v1, 0xd

    .line 165
    .line 166
    const-string v2, "popular_new_top"

    .line 167
    .line 168
    const-string v4, "PopularNewTop"

    .line 169
    .line 170
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 171
    .line 172
    .line 173
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewTop:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 174
    .line 175
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 176
    .line 177
    const/16 v1, 0xe

    .line 178
    .line 179
    const-string v2, "popular_new_games"

    .line 180
    .line 181
    const-string v4, "PopularNewXGames"

    .line 182
    .line 183
    invoke-direct {v0, v4, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 184
    .line 185
    .line 186
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 187
    .line 188
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 189
    .line 190
    const-string v1, "OneXGames"

    .line 191
    .line 192
    const/16 v2, 0xf

    .line 193
    .line 194
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 195
    .line 196
    .line 197
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 198
    .line 199
    new-instance v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 200
    .line 201
    const/16 v1, 0x10

    .line 202
    .line 203
    const-string v2, "main"

    .line 204
    .line 205
    const-string v3, "MainScreen"

    .line 206
    .line 207
    invoke-direct {v0, v3, v1, v2}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 208
    .line 209
    .line 210
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MainScreen:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 211
    .line 212
    invoke-static {}, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->a()[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 213
    .line 214
    .line 215
    move-result-object v0

    .line 216
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->$VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 217
    .line 218
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 219
    .line 220
    .line 221
    move-result-object v0

    .line 222
    sput-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->$ENTRIES:Lkotlin/enums/a;

    .line 223
    .line 224
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;ILjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput-object p3, p0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->value:Ljava/lang/String;

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;
    .locals 3

    .line 1
    const/16 v0, 0x11

    new-array v0, v0, [Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Main:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MenuOneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXAll:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXSearch:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXFavourite:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXDaily:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXCashback:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXFavouriteNew:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Game:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXBonuses:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->UserIcon:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Viewed:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MainStrip:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewTop:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->OneXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->MainScreen:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->$VALUES:[Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getValue()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->value:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
