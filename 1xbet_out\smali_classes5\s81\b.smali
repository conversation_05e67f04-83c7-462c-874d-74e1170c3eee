.class public final Ls81/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls81/b$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a#\u0010\u0008\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u0001H\u0007\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;",
        "",
        "horizontal",
        "",
        "b",
        "(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I",
        "isPromo",
        "isNewGame",
        "a",
        "(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;ZZ)I",
        "api_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;ZZ)I
    .locals 3
    .param p0    # Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;->BACKGROUND_L:Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    if-nez p1, :cond_2

    .line 6
    .line 7
    :cond_0
    sget-object v1, Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;->GRADIENT:Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 8
    .line 9
    if-ne p0, v1, :cond_1

    .line 10
    .line 11
    if-nez p1, :cond_2

    .line 12
    .line 13
    :cond_1
    sget-object v2, Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;->TRANSPARENCY:Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 14
    .line 15
    if-ne p0, v2, :cond_3

    .line 16
    .line 17
    if-eqz p1, :cond_3

    .line 18
    .line 19
    :cond_2
    sget p0, LlZ0/n;->Widget_Tag_Rounded_Red:I

    .line 20
    .line 21
    return p0

    .line 22
    :cond_3
    if-ne p0, v0, :cond_4

    .line 23
    .line 24
    if-nez p2, :cond_6

    .line 25
    .line 26
    :cond_4
    if-ne p0, v1, :cond_5

    .line 27
    .line 28
    if-nez p2, :cond_6

    .line 29
    .line 30
    :cond_5
    if-ne p0, v2, :cond_7

    .line 31
    .line 32
    if-eqz p2, :cond_7

    .line 33
    .line 34
    :cond_6
    sget p0, LlZ0/n;->Widget_Tag_Rounded_Green:I

    .line 35
    .line 36
    return p0

    .line 37
    :cond_7
    sget-object v0, Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;->BACKGROUND_S:Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 38
    .line 39
    if-ne p0, v0, :cond_8

    .line 40
    .line 41
    if-nez p1, :cond_9

    .line 42
    .line 43
    :cond_8
    sget-object v1, Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;->HORIZONTAL_BACKGROUND:Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 44
    .line 45
    if-ne p0, v1, :cond_a

    .line 46
    .line 47
    if-eqz p1, :cond_a

    .line 48
    .line 49
    :cond_9
    sget p0, LlZ0/n;->Widget_Tag_RectangularS_Red:I

    .line 50
    .line 51
    return p0

    .line 52
    :cond_a
    if-ne p0, v0, :cond_b

    .line 53
    .line 54
    if-nez p2, :cond_c

    .line 55
    .line 56
    :cond_b
    if-ne p0, v1, :cond_d

    .line 57
    .line 58
    if-eqz p2, :cond_d

    .line 59
    .line 60
    :cond_c
    sget p0, LlZ0/n;->Widget_Tag_RectangularS_Green:I

    .line 61
    .line 62
    return p0

    .line 63
    :cond_d
    const/4 p0, 0x0

    .line 64
    return p0
.end method

.method public static final b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I
    .locals 1
    .param p0    # Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Ls81/b$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_8

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_6

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_4

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-eq p0, v0, :cond_2

    .line 20
    .line 21
    const/4 v0, 0x5

    .line 22
    if-ne p0, v0, :cond_1

    .line 23
    .line 24
    if-eqz p1, :cond_0

    .line 25
    .line 26
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_HorizontalBackground_Horizontal:I

    .line 27
    .line 28
    return p0

    .line 29
    :cond_0
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_HorizontalBackground_Vertical:I

    .line 30
    .line 31
    return p0

    .line 32
    :cond_1
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 33
    .line 34
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 35
    .line 36
    .line 37
    throw p0

    .line 38
    :cond_2
    if-eqz p1, :cond_3

    .line 39
    .line 40
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_Transparency_Horizontal:I

    .line 41
    .line 42
    return p0

    .line 43
    :cond_3
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_Transparency_Vertical:I

    .line 44
    .line 45
    return p0

    .line 46
    :cond_4
    if-eqz p1, :cond_5

    .line 47
    .line 48
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_Gradient_Horizontal:I

    .line 49
    .line 50
    return p0

    .line 51
    :cond_5
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_Gradient_Vertical:I

    .line 52
    .line 53
    return p0

    .line 54
    :cond_6
    if-eqz p1, :cond_7

    .line 55
    .line 56
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_BackgroundS_Horizontal:I

    .line 57
    .line 58
    return p0

    .line 59
    :cond_7
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_BackgroundS_Vertical:I

    .line 60
    .line 61
    return p0

    .line 62
    :cond_8
    if-eqz p1, :cond_9

    .line 63
    .line 64
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_BackgroundL_Horizontal:I

    .line 65
    .line 66
    return p0

    .line 67
    :cond_9
    sget p0, LS11/g;->Widget_AggregatorGameCardCollection_BackgroundL_Vertical:I

    .line 68
    .line 69
    return p0
.end method
