.class public final Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/core/view/K;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment;->A2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment;


# direct methods
.method public constructor <init>(ZLorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment;)V
    .locals 0

    iput-boolean p1, p0, Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment$a;->a:Z

    iput-object p2, p0, Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment$a;->b:Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onApplyWindowInsets(Landroid/view/View;Landroidx/core/view/F0;)Landroidx/core/view/F0;
    .locals 4

    .line 1
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->s(I)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    iget p1, p1, LI0/d;->d:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    invoke-static {}, Landroidx/core/view/F0$o;->g()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iget p1, p1, LI0/d;->d:I

    .line 31
    .line 32
    :goto_0
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment$a;->b:Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment;

    .line 33
    .line 34
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireView()Landroid/view/View;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getPaddingLeft()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    invoke-virtual {v0}, Landroid/view/View;->getPaddingRight()I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    invoke-virtual {v0, v1, v2, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 51
    .line 52
    .line 53
    iget-boolean p1, p0, Lorg/xbet/ui_common/dialogs/BaseBottomSheetNewDialogFragment$a;->a:Z

    .line 54
    .line 55
    if-eqz p1, :cond_1

    .line 56
    .line 57
    sget-object p1, Landroidx/core/view/F0;->b:Landroidx/core/view/F0;

    .line 58
    .line 59
    return-object p1

    .line 60
    :cond_1
    return-object p2
.end method
