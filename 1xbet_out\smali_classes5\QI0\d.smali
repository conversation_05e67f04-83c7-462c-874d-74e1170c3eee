.class public final synthetic LQI0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;

.field public final synthetic c:J


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;J)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LQI0/d;->a:LB4/a;

    iput-object p2, p0, LQI0/d;->b:Lkotlin/jvm/functions/Function1;

    iput-wide p3, p0, LQI0/d;->c:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LQI0/d;->a:LB4/a;

    iget-object v1, p0, LQI0/d;->b:L<PERSON><PERSON>/jvm/functions/Function1;

    iget-wide v2, p0, LQI0/d;->c:J

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/statistic/player/impl/player/player_lastgame/presentation/adapter/PlayerLastGameAdapterDelegateKt;->d(LB4/a;Lkotlin/jvm/functions/Function1;JLjava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
