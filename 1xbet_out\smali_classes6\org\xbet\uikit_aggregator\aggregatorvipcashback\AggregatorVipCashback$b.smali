.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0013\u0008\u0082\u0008\u0018\u00002\u00020\u0001B9\u0012\n\u0008\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\u000e\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004\u0012\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u0007\u0012\u0008\u0008\u0002\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJB\u0010\r\u001a\u00020\u00002\n\u0008\u0002\u0010\u0003\u001a\u0004\u0018\u00010\u00022\u000e\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u00072\u0008\u0008\u0002\u0010\n\u001a\u00020\tH\u00c6\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0010\u0010\u0010\u001a\u00020\u000fH\u00d6\u0001\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0010\u0010\u0013\u001a\u00020\u0012H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001a\u0010\u0016\u001a\u00020\t2\u0008\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0016\u0010\u0017R\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u0018\u001a\u0004\u0008\u0019\u0010\u001aR\u001d\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u001c\u001a\u0004\u0008\u001d\u0010\u001eR\u0019\u0010\u0008\u001a\u0004\u0018\u00010\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001f\u001a\u0004\u0008 \u0010!R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\"\u001a\u0004\u0008#\u0010$\u00a8\u0006%"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;",
        "",
        "Lg31/h;",
        "cashbackModel",
        "",
        "Lg31/b;",
        "levels",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "type",
        "",
        "showShimmer",
        "<init>",
        "(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)V",
        "a",
        "(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "Lg31/h;",
        "c",
        "()Lg31/h;",
        "b",
        "Ljava/util/List;",
        "d",
        "()Ljava/util/List;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "f",
        "()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "Z",
        "e",
        "()Z",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lg31/h;

.field public final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lg31/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

.field public final d:Z


# direct methods
.method public constructor <init>()V
    .locals 7

    .line 1
    const/16 v5, 0xf

    const/4 v6, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;-><init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)V
    .locals 0
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
            "Z)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    .line 4
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    .line 5
    iput-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 6
    iput-boolean p4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    return-void
.end method

.method public synthetic constructor <init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p6, p5, 0x1

    const/4 v0, 0x0

    if-eqz p6, :cond_0

    move-object p1, v0

    :cond_0
    and-int/lit8 p6, p5, 0x2

    if-eqz p6, :cond_1

    .line 7
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p2

    :cond_1
    and-int/lit8 p6, p5, 0x4

    if-eqz p6, :cond_2

    move-object p3, v0

    :cond_2
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_3

    const/4 p4, 0x0

    .line 8
    :cond_3
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;-><init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILjava/lang/Object;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;
    .locals 0

    .line 1
    and-int/lit8 p6, p5, 0x1

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    .line 6
    .line 7
    :cond_0
    and-int/lit8 p6, p5, 0x2

    .line 8
    .line 9
    if-eqz p6, :cond_1

    .line 10
    .line 11
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    .line 12
    .line 13
    :cond_1
    and-int/lit8 p6, p5, 0x4

    .line 14
    .line 15
    if-eqz p6, :cond_2

    .line 16
    .line 17
    iget-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 18
    .line 19
    :cond_2
    and-int/lit8 p5, p5, 0x8

    .line 20
    .line 21
    if-eqz p5, :cond_3

    .line 22
    .line 23
    iget-boolean p4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    .line 24
    .line 25
    :cond_3
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 26
    .line 27
    .line 28
    move-result-object p0

    .line 29
    return-object p0
.end method


# virtual methods
.method public final a(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;
    .locals 1
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
            "Z)",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3, p4}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;-><init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;Z)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final c()Lg31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    iget-object v3, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget-boolean v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    iget-boolean p1, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    if-eq v1, p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public final f()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 3

    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lg31/h;->hashCode()I

    move-result v0

    :goto_0
    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    invoke-virtual {v2}, Ljava/lang/Object;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    if-nez v2, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v2}, Ljava/lang/Object;->hashCode()I

    move-result v1

    :goto_1
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->a:Lg31/h;

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b:Ljava/util/List;

    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    iget-boolean v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d:Z

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "StateWrapper(cashbackModel="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", levels="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", type="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", showShimmer="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
