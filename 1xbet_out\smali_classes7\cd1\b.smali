.class public final synthetic Lcd1/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/content/DialogInterface$OnClickListener;


# instance fields
.field public final synthetic a:Lru/ok/android/sdk/AbstractWidgetActivity;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lru/ok/android/sdk/AbstractWidgetActivity;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcd1/b;->a:Lru/ok/android/sdk/AbstractWidgetActivity;

    iput-object p2, p0, Lcd1/b;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/content/DialogInterface;I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lcd1/b;->a:Lru/ok/android/sdk/AbstractWidgetActivity;

    iget-object v1, p0, Lcd1/b;->b:Ljava/lang/String;

    invoke-static {v0, v1, p1, p2}, Lru/ok/android/sdk/AbstractWidgetActivity;->b(Lru/ok/android/sdk/AbstractWidgetActivity;Ljava/lang/String;Landroid/content/DialogInterface;I)V

    return-void
.end method
