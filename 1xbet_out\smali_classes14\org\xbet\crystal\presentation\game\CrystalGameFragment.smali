.class public final Lorg/xbet/crystal/presentation/game/CrystalGameFragment;
.super LXW0/a;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/crystal/presentation/game/f;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/crystal/presentation/game/CrystalGameFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 =2\u00020\u00012\u00020\u0002:\u0001>B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\t\u0010\u0004J!\u0010\u000e\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0019\u0010\u0010\u001a\u00020\u00082\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0012\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0004J\u0017\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0004J\u001f\u0010\u001c\u001a\u00020\u00082\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u001f\u0010 \u001a\u00020\u00082\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u000f\u0010\"\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0004J\u000f\u0010#\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0004R\u001b\u0010)\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001d\u0010/\u001a\u0004\u0018\u00010*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\"\u00107\u001a\u0002008\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104\"\u0004\u00085\u00106R\u001b\u0010<\u001a\u0002088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00089\u0010,\u001a\u0004\u0008:\u0010;\u00a8\u0006?"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/game/CrystalGameFragment;",
        "LXW0/a;",
        "Lorg/xbet/crystal/presentation/game/f;",
        "<init>",
        "()V",
        "Lkotlinx/coroutines/x0;",
        "O2",
        "()Lkotlinx/coroutines/x0;",
        "",
        "u2",
        "Landroid/view/View;",
        "view",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "t2",
        "(Landroid/os/Bundle;)V",
        "O0",
        "LZx/c;",
        "round",
        "P1",
        "(LZx/c;)V",
        "onDestroyView",
        "LZx/b;",
        "gameModel",
        "",
        "gameInProcess",
        "N2",
        "(LZx/b;Z)V",
        "",
        "currencySymbol",
        "K2",
        "(LZx/b;Ljava/lang/String;)V",
        "M2",
        "L2",
        "LXx/a;",
        "i0",
        "LRc/c;",
        "G2",
        "()LXx/a;",
        "binding",
        "LYx/a;",
        "j0",
        "Lkotlin/j;",
        "H2",
        "()LYx/a;",
        "crystalComponent",
        "LYx/a$a;",
        "k0",
        "LYx/a$a;",
        "J2",
        "()LYx/a$a;",
        "setViewModelFactory",
        "(LYx/a$a;)V",
        "viewModelFactory",
        "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;",
        "l0",
        "I2",
        "()Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;",
        "viewModel",
        "m0",
        "a",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k0:LYx/a$a;

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/crystal/databinding/CrystalFragmentBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->m0:Lorg/xbet/crystal/presentation/game/CrystalGameFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LSx/c;->crystal_fragment:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$binding$2;->INSTANCE:Lorg/xbet/crystal/presentation/game/CrystalGameFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/crystal/presentation/game/a;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/crystal/presentation/game/a;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iput-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->j0:Lkotlin/j;

    .line 24
    .line 25
    new-instance v0, Lorg/xbet/crystal/presentation/game/b;

    .line 26
    .line 27
    invoke-direct {v0, p0}, Lorg/xbet/crystal/presentation/game/b;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V

    .line 28
    .line 29
    .line 30
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$1;

    .line 31
    .line 32
    invoke-direct {v1, p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 33
    .line 34
    .line 35
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 36
    .line 37
    new-instance v3, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$2;

    .line 38
    .line 39
    invoke-direct {v3, v1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 40
    .line 41
    .line 42
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    const-class v2, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 47
    .line 48
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    new-instance v3, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$3;

    .line 53
    .line 54
    invoke-direct {v3, v1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 55
    .line 56
    .line 57
    new-instance v4, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$4;

    .line 58
    .line 59
    const/4 v5, 0x0

    .line 60
    invoke-direct {v4, v5, v1}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 61
    .line 62
    .line 63
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->l0:Lkotlin/j;

    .line 68
    .line 69
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)LXx/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;LZx/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->K2(LZx/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->L2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->M2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;LZx/b;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->N2(LZx/b;Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final F2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)LYx/a;
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    instance-of v0, p0, Lorg/xbet/crystal/presentation/holder/CrystalFragment;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    check-cast p0, Lorg/xbet/crystal/presentation/holder/CrystalFragment;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object p0, v1

    .line 14
    :goto_0
    if-eqz p0, :cond_1

    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/holder/CrystalFragment;->i4()LYx/a;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    return-object p0

    .line 21
    :cond_1
    return-object v1
.end method

.method private final O2()Lkotlinx/coroutines/x0;
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->I2()Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->C3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$1;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment$subscribeOnVM$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    return-object v0
.end method

.method public static final P2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->J2()LYx/a$a;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)LYx/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->F2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)LYx/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->P2(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final G2()LXx/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LXx/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final H2()LYx/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LYx/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final I2()Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final J2()LYx/a$a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->k0:LYx/a$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final K2(LZx/b;Ljava/lang/String;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 16
    .line 17
    invoke-virtual {v0, p2}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->setCurrencySymbol(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    iget-object p2, p2, LXx/a;->c:Lorg/xbet/crystal/presentation/views/CrystalFieldView;

    .line 25
    .line 26
    invoke-virtual {p1}, LZx/b;->f()LZx/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {p1}, LZx/d;->a()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-virtual {p2, p1}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->r(Ljava/util/List;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final L2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->f:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final M2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->f:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 16
    .line 17
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final N2(LZx/b;Z)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->c:Lorg/xbet/crystal/presentation/views/CrystalFieldView;

    .line 6
    .line 7
    invoke-virtual {p1}, LZx/b;->f()LZx/d;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, LZx/d;->a()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1, p2}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->v(Ljava/util/List;Z)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1}, LZx/b;->f()LZx/d;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-virtual {p1}, LZx/d;->a()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result p2

    .line 34
    if-eqz p2, :cond_0

    .line 35
    .line 36
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object p2

    .line 40
    check-cast p2, LZx/c;

    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 47
    .line 48
    invoke-virtual {p2}, LZx/c;->c()Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object p2

    .line 52
    invoke-virtual {v0, p2}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c(Ljava/util/List;)V

    .line 53
    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_0
    return-void
.end method

.method public O0()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->I2()Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F3()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public P1(LZx/c;)V
    .locals 1
    .param p1    # LZx/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->d:Lorg/xbet/crystal/presentation/views/CrystalStatusView;

    .line 6
    .line 7
    invoke-virtual {p1}, LZx/c;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {v0, p1}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c(Ljava/util/List;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/a;->c:Lorg/xbet/crystal/presentation/views/CrystalFieldView;

    .line 6
    .line 7
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->j()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v0, v0, LXx/a;->c:Lorg/xbet/crystal/presentation/views/CrystalFieldView;

    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->i()V

    .line 17
    .line 18
    .line 19
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, LXW0/a;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->O2()Lkotlinx/coroutines/x0;

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->G2()LXx/a;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LXx/a;->c:Lorg/xbet/crystal/presentation/views/CrystalFieldView;

    .line 9
    .line 10
    invoke-virtual {p1, p0}, Lorg/xbet/crystal/presentation/views/CrystalFieldView;->setUpdateInterface(Lorg/xbet/crystal/presentation/game/f;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->I2()Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-virtual {p1}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->G3()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/crystal/presentation/game/CrystalGameFragment;->H2()LYx/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0, p0}, LYx/a;->b(Lorg/xbet/crystal/presentation/game/CrystalGameFragment;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method
