.class public interface abstract LhF0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LhF0/e$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u0000 \n2\u00020\u0001:\u0001\nJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\'\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u0007H\'\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\'\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0012\u001a\u00020\u0011H\'\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "LhF0/e;",
        "",
        "Lorg/xbet/statistic/heat_map/impl/data/repository/HeatMapStatisticsRepositoryImpl;",
        "heatMapStatisticsRepositoryImpl",
        "LjF0/a;",
        "d",
        "(Lorg/xbet/statistic/heat_map/impl/data/repository/HeatMapStatisticsRepositoryImpl;)LjF0/a;",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "factory",
        "Landroidx/lifecycle/e0$c;",
        "a",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)Landroidx/lifecycle/e0$c;",
        "Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;",
        "viewModel",
        "Landroidx/lifecycle/b0;",
        "c",
        "(Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;)Landroidx/lifecycle/b0;",
        "LbF0/a;",
        "heatMapScreenFactory",
        "LaF0/a;",
        "b",
        "(LbF0/a;)LaF0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LhF0/e$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LhF0/e$a;->a:LhF0/e$a;

    .line 2
    .line 3
    sput-object v0, LhF0/e;->a:LhF0/e$a;

    .line 4
    .line 5
    return-void
.end method


# virtual methods
.method public abstract a(Lorg/xbet/ui_common/viewmodel/core/l;)Landroidx/lifecycle/e0$c;
    .param p1    # Lorg/xbet/ui_common/viewmodel/core/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract b(LbF0/a;)LaF0/a;
    .param p1    # LbF0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract c(Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;)Landroidx/lifecycle/b0;
    .param p1    # Lorg/xbet/statistic/heat_map/impl/presentation/viewmodel/HeatMapStatisticViewModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract d(Lorg/xbet/statistic/heat_map/impl/data/repository/HeatMapStatisticsRepositoryImpl;)LjF0/a;
    .param p1    # Lorg/xbet/statistic/heat_map/impl/data/repository/HeatMapStatisticsRepositoryImpl;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
