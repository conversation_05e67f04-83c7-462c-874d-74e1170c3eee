.class public final Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0086B\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;",
        "",
        "LUv/a;",
        "gamesRepository",
        "<init>",
        "(LUv/a;)V",
        "Lf50/c;",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LUv/a;",
        "popular_classic_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LUv/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LUv/a;)V
    .locals 0
    .param p1    # LUv/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;->a:LUv/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lf50/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase;->a:LUv/a;

    .line 54
    .line 55
    iput v3, v0, Lorg/xbet/games_section/feature/popular_classic/domain/usecases/PopularOneXGamesLuckyWheelUseCase$invoke$1;->label:I

    .line 56
    .line 57
    invoke-interface {p1, v0}, LUv/a;->I(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    if-ne p1, v1, :cond_3

    .line 62
    .line 63
    return-object v1

    .line 64
    :cond_3
    :goto_1
    check-cast p1, Ljava/lang/Iterable;

    .line 65
    .line 66
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    :cond_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 71
    .line 72
    .line 73
    move-result v0

    .line 74
    if-eqz v0, :cond_5

    .line 75
    .line 76
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    move-object v1, v0

    .line 81
    check-cast v1, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;

    .line 82
    .line 83
    sget-object v2, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->Companion:Lorg/xbet/games_section/api/models/OneXGamesPromoType$a;

    .line 84
    .line 85
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getId()I

    .line 86
    .line 87
    .line 88
    move-result v1

    .line 89
    invoke-virtual {v2, v1}, Lorg/xbet/games_section/api/models/OneXGamesPromoType$a;->a(I)Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    sget-object v2, Lorg/xbet/games_section/api/models/OneXGamesPromoType;->LUCKY_WHEEL:Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 94
    .line 95
    if-ne v1, v2, :cond_4

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :cond_5
    const/4 v0, 0x0

    .line 99
    :goto_2
    check-cast v0, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;

    .line 100
    .line 101
    if-eqz v0, :cond_6

    .line 102
    .line 103
    new-instance p1, Lf50/c;

    .line 104
    .line 105
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getName()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getDesc()Ljava/lang/String;

    .line 110
    .line 111
    .line 112
    move-result-object v2

    .line 113
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/OneXGamesActionResult;->getUnderMaintenance()Z

    .line 114
    .line 115
    .line 116
    move-result v0

    .line 117
    invoke-direct {p1, v1, v2, v0}, Lf50/c;-><init>(Ljava/lang/String;Ljava/lang/String;Z)V

    .line 118
    .line 119
    .line 120
    return-object p1

    .line 121
    :cond_6
    new-instance p1, Ljava/lang/Throwable;

    .line 122
    .line 123
    const-string v0, "No LuckyWheel from popular OneXGames"

    .line 124
    .line 125
    invoke-direct {p1, v0}, Ljava/lang/Throwable;-><init>(Ljava/lang/String;)V

    .line 126
    .line 127
    .line 128
    throw p1
.end method
