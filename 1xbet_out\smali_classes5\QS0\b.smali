.class public final LQS0/b;
.super LVX0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LQS0/b;",
        "LVX0/a;",
        "",
        "isTablet",
        "<init>",
        "(Z)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Z)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-direct {p0, v0, v1, v0}, LVX0/a;-><init>(Landroidx/recyclerview/widget/i$f;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/SwipexCardTwoTeamViewHolderKt;->m(Z)LA4/c;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/SwipexCardMultiTeamViewHolderKt;->m(Z)LA4/c;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/SwipexCardSingleEventViewHolderKt;->i(Z)LA4/c;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-static {}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/mini/SwipexMiniCardTwoTeamViewHolderKt;->k()LA4/c;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p1, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-static {}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/mini/SwipexMiniCardMultiTeamViewHolderKt;->k()LA4/c;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {p1, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-static {}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/mini/SwipexMiniCardSingleEventViewHolderKt;->i()LA4/c;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    invoke-virtual {p1, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 53
    .line 54
    .line 55
    return-void
.end method
