.class interface abstract Lcom/google/crypto/tink/shaded/protobuf/ByteString$ByteArrayCopier;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "ByteArrayCopier"
.end annotation


# virtual methods
.method public abstract a([BII)[B
.end method
