.class public final Lorg/xplatform/aggregator/impl/core/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0008\u0014\u0008\u0000\u0018\u0000 02\u00020\u0001:\u0001\u000cB!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0013\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0081\u0001\u0010 \u001a\u00020\u001b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u000e2\u0006\u0010\u0014\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00102\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u001b0\u00192\u000e\u0008\u0002\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u001d2\u000e\u0008\u0002\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\u0004\u0008 \u0010!JW\u0010%\u001a\u00020\u001b2\u0006\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\"\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00102\u0006\u0010#\u001a\u00020\u00152\u000e\u0008\u0002\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u001d2\u0008\u0008\u0002\u0010$\u001a\u00020\u0010\u00a2\u0006\u0004\u0008%\u0010&JG\u0010\'\u001a\u00020\u001b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00102\u000e\u0008\u0002\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u001d2\u000e\u0008\u0002\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u001dH\u0002\u00a2\u0006\u0004\u0008\'\u0010(J1\u0010+\u001a\u00020\u001b2\u0006\u0010)\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010*\u001a\u00020\u00152\u0008\u0008\u0002\u0010$\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008+\u0010,R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010-R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010.R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010/\u00a8\u00061"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)V",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "a",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "partId",
        "",
        "title",
        "partType",
        "gameId",
        "productId",
        "",
        "needTransfer",
        "noLoyalty",
        "subtitle",
        "Lkotlin/Function1;",
        "",
        "",
        "errorHandler",
        "",
        "filterIds",
        "providersList",
        "c",
        "(JLjava/lang/String;JJJZZLjava/lang/String;Lkotlin/jvm/functions/Function1;Ljava/util/List;Ljava/util/List;)V",
        "categoryId",
        "fromSuccessfulSearch",
        "subStringValue",
        "b",
        "(JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;)V",
        "f",
        "(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V",
        "id",
        "fromSuccesfulSearch",
        "g",
        "(JLjava/lang/String;ZLjava/lang/String;)V",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "LP91/b;",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/impl/core/presentation/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/c$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->d:Lorg/xplatform/aggregator/impl/core/presentation/c$a;

    return-void
.end method

.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/c;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/c;->c:Lorg/xbet/analytics/domain/scope/g0;

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic d(Lorg/xplatform/aggregator/impl/core/presentation/c;JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;ILjava/lang/Object;)V
    .locals 14

    .line 1
    move/from16 v0, p12

    .line 2
    .line 3
    and-int/lit8 v1, v0, 0x40

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    move-object v12, v1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object/from16 v12, p10

    .line 14
    .line 15
    :goto_0
    and-int/lit16 v0, v0, 0x80

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    const-string v0, ""

    .line 20
    .line 21
    move-object v13, v0

    .line 22
    :goto_1
    move-object v2, p0

    .line 23
    move-wide v3, p1

    .line 24
    move-wide/from16 v5, p3

    .line 25
    .line 26
    move-wide/from16 v7, p5

    .line 27
    .line 28
    move-object/from16 v9, p7

    .line 29
    .line 30
    move-object/from16 v10, p8

    .line 31
    .line 32
    move/from16 v11, p9

    .line 33
    .line 34
    goto :goto_2

    .line 35
    :cond_1
    move-object/from16 v13, p11

    .line 36
    .line 37
    goto :goto_1

    .line 38
    :goto_2
    invoke-virtual/range {v2 .. v13}, Lorg/xplatform/aggregator/impl/core/presentation/c;->b(JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public static synthetic e(Lorg/xplatform/aggregator/impl/core/presentation/c;JLjava/lang/String;JJJZZLjava/lang/String;Lkotlin/jvm/functions/Function1;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)V
    .locals 18

    .line 1
    move/from16 v0, p16

    .line 2
    .line 3
    and-int/lit16 v1, v0, 0x200

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    move-object/from16 v16, v1

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    move-object/from16 v16, p14

    .line 15
    .line 16
    :goto_0
    and-int/lit16 v0, v0, 0x400

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    move-object/from16 v17, v0

    .line 25
    .line 26
    :goto_1
    move-object/from16 v2, p0

    .line 27
    .line 28
    move-wide/from16 v3, p1

    .line 29
    .line 30
    move-object/from16 v5, p3

    .line 31
    .line 32
    move-wide/from16 v6, p4

    .line 33
    .line 34
    move-wide/from16 v8, p6

    .line 35
    .line 36
    move-wide/from16 v10, p8

    .line 37
    .line 38
    move/from16 v12, p10

    .line 39
    .line 40
    move/from16 v13, p11

    .line 41
    .line 42
    move-object/from16 v14, p12

    .line 43
    .line 44
    move-object/from16 v15, p13

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_1
    move-object/from16 v17, p15

    .line 48
    .line 49
    goto :goto_1

    .line 50
    :goto_2
    invoke-virtual/range {v2 .. v17}, Lorg/xplatform/aggregator/impl/core/presentation/c;->c(JLjava/lang/String;JJJZZLjava/lang/String;Lkotlin/jvm/functions/Function1;Ljava/util/List;Ljava/util/List;)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public static synthetic h(Lorg/xplatform/aggregator/impl/core/presentation/c;JLjava/lang/String;ZLjava/lang/String;ILjava/lang/Object;)V
    .locals 6

    .line 1
    and-int/lit8 p6, p6, 0x8

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    const-string p5, ""

    .line 6
    .line 7
    :cond_0
    move-object v0, p0

    .line 8
    move-wide v1, p1

    .line 9
    move-object v3, p3

    .line 10
    move v4, p4

    .line 11
    move-object v5, p5

    .line 12
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/c;->g(JLjava/lang/String;ZLjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public final a()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/c;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final b(JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;)V
    .locals 20
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JJJ",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    const-wide/16 v3, 0x0

    .line 10
    .line 11
    const/4 v5, 0x0

    .line 12
    cmp-long v6, p1, v1

    .line 13
    .line 14
    if-nez v6, :cond_0

    .line 15
    .line 16
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 17
    .line 18
    new-instance v6, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 19
    .line 20
    new-instance v11, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;

    .line 21
    .line 22
    const/4 v2, 0x1

    .line 23
    invoke-direct {v11, v3, v4, v2, v5}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;-><init>(JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 24
    .line 25
    .line 26
    const/16 v18, 0xf7

    .line 27
    .line 28
    const/16 v19, 0x0

    .line 29
    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    const-wide/16 v9, 0x0

    .line 33
    .line 34
    const/4 v12, 0x0

    .line 35
    const-wide/16 v13, 0x0

    .line 36
    .line 37
    const-wide/16 v15, 0x0

    .line 38
    .line 39
    const/16 v17, 0x0

    .line 40
    .line 41
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 45
    .line 46
    .line 47
    return-void

    .line 48
    :cond_0
    const-wide/16 v1, 0x2

    .line 49
    .line 50
    cmp-long v6, p1, v1

    .line 51
    .line 52
    if-eqz v6, :cond_1

    .line 53
    .line 54
    cmp-long v1, p1, v3

    .line 55
    .line 56
    if-eqz v1, :cond_1

    .line 57
    .line 58
    const-wide/16 v1, 0x3

    .line 59
    .line 60
    cmp-long v3, p1, v1

    .line 61
    .line 62
    if-nez v3, :cond_2

    .line 63
    .line 64
    :cond_1
    move-wide/from16 v1, p3

    .line 65
    .line 66
    move-object/from16 v3, p7

    .line 67
    .line 68
    move/from16 v4, p9

    .line 69
    .line 70
    move-object/from16 v5, p11

    .line 71
    .line 72
    goto :goto_0

    .line 73
    :cond_2
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 74
    .line 75
    new-instance v6, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 76
    .line 77
    new-instance v11, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 78
    .line 79
    const/4 v2, 0x2

    .line 80
    move-object/from16 v3, p10

    .line 81
    .line 82
    invoke-direct {v11, v3, v5, v2, v5}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 83
    .line 84
    .line 85
    const/16 v18, 0x90

    .line 86
    .line 87
    const/16 v19, 0x0

    .line 88
    .line 89
    const/4 v12, 0x0

    .line 90
    const/16 v17, 0x0

    .line 91
    .line 92
    move-wide/from16 v15, p1

    .line 93
    .line 94
    move-wide/from16 v9, p3

    .line 95
    .line 96
    move-wide/from16 v13, p5

    .line 97
    .line 98
    move-object/from16 v7, p7

    .line 99
    .line 100
    move-object/from16 v8, p8

    .line 101
    .line 102
    invoke-direct/range {v6 .. v19}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 103
    .line 104
    .line 105
    invoke-virtual {v1, v6}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 106
    .line 107
    .line 108
    return-void

    .line 109
    :goto_0
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/c;->g(JLjava/lang/String;ZLjava/lang/String;)V

    .line 110
    .line 111
    .line 112
    return-void
.end method

.method public final c(JLjava/lang/String;JJJZZLjava/lang/String;Lkotlin/jvm/functions/Function1;Ljava/util/List;Ljava/util/List;)V
    .locals 21
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "JJJZZ",
            "Ljava/lang/String;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    const-wide/16 v0, 0x1

    .line 2
    .line 3
    cmp-long v2, p4, v0

    .line 4
    .line 5
    if-nez v2, :cond_0

    .line 6
    .line 7
    move-object/from16 v3, p0

    .line 8
    .line 9
    move-wide/from16 v4, p1

    .line 10
    .line 11
    move-object/from16 v6, p3

    .line 12
    .line 13
    move-object/from16 v7, p12

    .line 14
    .line 15
    move-object/from16 v8, p14

    .line 16
    .line 17
    move-object/from16 v9, p15

    .line 18
    .line 19
    invoke-virtual/range {v3 .. v9}, Lorg/xplatform/aggregator/impl/core/presentation/c;->f(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    const-wide/16 v0, 0x2

    .line 24
    .line 25
    cmp-long v2, p4, v0

    .line 26
    .line 27
    if-eqz v2, :cond_3

    .line 28
    .line 29
    const-wide/16 v0, 0x0

    .line 30
    .line 31
    cmp-long v2, p4, v0

    .line 32
    .line 33
    if-nez v2, :cond_1

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const-wide/16 v2, 0x3

    .line 37
    .line 38
    cmp-long v4, p4, v2

    .line 39
    .line 40
    if-nez v4, :cond_2

    .line 41
    .line 42
    cmp-long v2, p6, v0

    .line 43
    .line 44
    if-lez v2, :cond_2

    .line 45
    .line 46
    move-object/from16 v0, p0

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 49
    .line 50
    new-instance v19, Ljava/util/ArrayList;

    .line 51
    .line 52
    invoke-direct/range {v19 .. v19}, Ljava/util/ArrayList;-><init>()V

    .line 53
    .line 54
    .line 55
    new-instance v0, Lorg/xplatform/aggregator/api/model/Game;

    .line 56
    .line 57
    const/16 v17, 0x0

    .line 58
    .line 59
    const/16 v18, 0x0

    .line 60
    .line 61
    const-wide/16 v5, 0x0

    .line 62
    .line 63
    const-wide/16 v7, 0x0

    .line 64
    .line 65
    const-string v9, ""

    .line 66
    .line 67
    const-string v11, ""

    .line 68
    .line 69
    const/4 v12, 0x0

    .line 70
    const/4 v13, 0x0

    .line 71
    const/4 v14, 0x0

    .line 72
    move-object/from16 v10, p3

    .line 73
    .line 74
    move-wide/from16 v3, p8

    .line 75
    .line 76
    move/from16 v15, p10

    .line 77
    .line 78
    move/from16 v16, p11

    .line 79
    .line 80
    move-object/from16 v20, v1

    .line 81
    .line 82
    move-wide/from16 v1, p6

    .line 83
    .line 84
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/api/model/Game;-><init>(JJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZZLjava/util/List;)V

    .line 85
    .line 86
    .line 87
    const/16 v1, 0x1fb9

    .line 88
    .line 89
    move-object/from16 v2, p13

    .line 90
    .line 91
    move-object/from16 v3, v20

    .line 92
    .line 93
    invoke-virtual {v3, v0, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 94
    .line 95
    .line 96
    :cond_2
    return-void

    .line 97
    :cond_3
    :goto_0
    const/16 v0, 0x8

    .line 98
    .line 99
    const/4 v1, 0x0

    .line 100
    const/4 v2, 0x0

    .line 101
    const/4 v3, 0x0

    .line 102
    move-object/from16 p4, p0

    .line 103
    .line 104
    move-wide/from16 p5, p1

    .line 105
    .line 106
    move-object/from16 p7, p3

    .line 107
    .line 108
    move-object/from16 p11, v1

    .line 109
    .line 110
    move-object/from16 p9, v3

    .line 111
    .line 112
    const/16 p8, 0x0

    .line 113
    .line 114
    const/16 p10, 0x8

    .line 115
    .line 116
    invoke-static/range {p4 .. p11}, Lorg/xplatform/aggregator/impl/core/presentation/c;->h(Lorg/xplatform/aggregator/impl/core/presentation/c;JLjava/lang/String;ZLjava/lang/String;ILjava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    return-void
.end method

.method public final f(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 6
    .line 7
    .line 8
    move-result-wide v1

    .line 9
    cmp-long v3, p1, v1

    .line 10
    .line 11
    if-nez v3, :cond_0

    .line 12
    .line 13
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 14
    .line 15
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 16
    .line 17
    new-instance v7, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;

    .line 18
    .line 19
    const/4 v3, 0x1

    .line 20
    const/4 v4, 0x0

    .line 21
    const-wide/16 v5, 0x0

    .line 22
    .line 23
    invoke-direct {v7, v5, v6, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$RecommendedScreen;-><init>(JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 24
    .line 25
    .line 26
    const/16 v14, 0xf7

    .line 27
    .line 28
    const/4 v15, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v8, 0x0

    .line 31
    const-wide/16 v9, 0x0

    .line 32
    .line 33
    const-wide/16 v11, 0x0

    .line 34
    .line 35
    const/4 v13, 0x0

    .line 36
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1, v2}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_0
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 44
    .line 45
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 46
    .line 47
    new-instance v7, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 48
    .line 49
    move-object/from16 v3, p5

    .line 50
    .line 51
    move-object/from16 v4, p6

    .line 52
    .line 53
    invoke-direct {v7, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;-><init>(Ljava/util/List;Ljava/util/List;)V

    .line 54
    .line 55
    .line 56
    const/16 v14, 0xf0

    .line 57
    .line 58
    const/4 v15, 0x0

    .line 59
    const/4 v8, 0x0

    .line 60
    const-wide/16 v9, 0x0

    .line 61
    .line 62
    const-wide/16 v11, 0x0

    .line 63
    .line 64
    const/4 v13, 0x0

    .line 65
    move-wide/from16 v5, p1

    .line 66
    .line 67
    move-object/from16 v3, p3

    .line 68
    .line 69
    move-object/from16 v4, p4

    .line 70
    .line 71
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {v1, v2}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 75
    .line 76
    .line 77
    return-void
.end method

.method public final g(JLjava/lang/String;ZLjava/lang/String;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/core/presentation/c;->b:LP91/b;

    .line 4
    .line 5
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 6
    .line 7
    new-instance v7, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;

    .line 8
    .line 9
    move/from16 v3, p4

    .line 10
    .line 11
    invoke-direct {v7, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$NewGamesFolderScreen;-><init>(Z)V

    .line 12
    .line 13
    .line 14
    const/16 v14, 0x72

    .line 15
    .line 16
    const/4 v15, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    const/4 v8, 0x0

    .line 19
    const-wide/16 v9, 0x0

    .line 20
    .line 21
    const-wide/16 v11, 0x0

    .line 22
    .line 23
    move-wide/from16 v5, p1

    .line 24
    .line 25
    move-object/from16 v3, p3

    .line 26
    .line 27
    move-object/from16 v13, p5

    .line 28
    .line 29
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v1, v2}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method
