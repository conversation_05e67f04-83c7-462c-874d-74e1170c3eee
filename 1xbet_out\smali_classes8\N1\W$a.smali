.class public final LN1/W$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN1/W;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:[Ljava/lang/String;

.field public final c:I


# direct methods
.method public constructor <init>(Ljava/lang/String;[Ljava/lang/String;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LN1/W$a;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-object p2, p0, LN1/W$a;->b:[Ljava/lang/String;

    .line 7
    .line 8
    iput p3, p0, LN1/W$a;->c:I

    .line 9
    .line 10
    return-void
.end method
