.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->e(LHy0/b;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Landroid/graphics/drawable/Drawable;

.field public final synthetic c:LHy0/b;

.field public final synthetic d:LB4/a;

.field public final synthetic e:LHy0/b;


# direct methods
.method public constructor <init>(LB4/a;Landroid/graphics/drawable/Drawable;LHy0/b;LB4/a;LHy0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->b:Landroid/graphics/drawable/Drawable;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->c:LHy0/b;

    .line 6
    .line 7
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->d:LB4/a;

    .line 8
    .line 9
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->e:LHy0/b;

    .line 10
    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 12
    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 8
    .line 9
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 10
    .line 11
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, LGq0/u1;

    .line 16
    .line 17
    iget-object v2, p1, LGq0/u1;->c:Landroid/widget/ImageView;

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 20
    .line 21
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    check-cast p1, LIy0/f;

    .line 26
    .line 27
    invoke-virtual {p1}, LIy0/f;->f()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->b:Landroid/graphics/drawable/Drawable;

    .line 32
    .line 33
    const/4 p1, 0x0

    .line 34
    new-array v7, p1, [LYW0/d;

    .line 35
    .line 36
    const/16 v11, 0xec

    .line 37
    .line 38
    const/4 v12, 0x0

    .line 39
    const/4 v5, 0x0

    .line 40
    const/4 v6, 0x0

    .line 41
    const/4 v8, 0x0

    .line 42
    const/4 v9, 0x0

    .line 43
    const/4 v10, 0x0

    .line 44
    invoke-static/range {v1 .. v12}, LCX0/l;->H(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Z[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 48
    .line 49
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    check-cast v0, LGq0/u1;

    .line 54
    .line 55
    iget-object v0, v0, LGq0/u1;->d:Lorg/xbet/uikit/components/separator/Separator;

    .line 56
    .line 57
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 58
    .line 59
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    check-cast v1, LIy0/f;

    .line 64
    .line 65
    invoke-virtual {v1}, LIy0/f;->o()Z

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    if-eqz v1, :cond_0

    .line 70
    .line 71
    goto :goto_0

    .line 72
    :cond_0
    const/16 p1, 0x8

    .line 73
    .line 74
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 75
    .line 76
    .line 77
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a:LB4/a;

    .line 78
    .line 79
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->c:LHy0/b;

    .line 80
    .line 81
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->c(LB4/a;LHy0/b;)V

    .line 82
    .line 83
    .line 84
    return-void

    .line 85
    :cond_1
    new-instance v0, Ljava/util/ArrayList;

    .line 86
    .line 87
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 88
    .line 89
    .line 90
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 95
    .line 96
    .line 97
    move-result v1

    .line 98
    if-eqz v1, :cond_2

    .line 99
    .line 100
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    check-cast v1, Ljava/util/Collection;

    .line 105
    .line 106
    check-cast v1, Ljava/lang/Iterable;

    .line 107
    .line 108
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 109
    .line 110
    .line 111
    goto :goto_1

    .line 112
    :cond_2
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v0

    .line 120
    if-eqz v0, :cond_4

    .line 121
    .line 122
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    check-cast v0, LIy0/f$b;

    .line 127
    .line 128
    instance-of v0, v0, LIy0/f$b$a;

    .line 129
    .line 130
    if-eqz v0, :cond_3

    .line 131
    .line 132
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->d:LB4/a;

    .line 133
    .line 134
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->e:LHy0/b;

    .line 135
    .line 136
    invoke-static {v0, v1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt;->c(LB4/a;LHy0/b;)V

    .line 137
    .line 138
    .line 139
    goto :goto_2

    .line 140
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 141
    .line 142
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 143
    .line 144
    .line 145
    throw p1

    .line 146
    :cond_4
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/OpponentViewHolderKt$b;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
