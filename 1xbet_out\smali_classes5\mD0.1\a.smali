.class public final synthetic LmD0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lf8/g;


# direct methods
.method public synthetic constructor <init>(Lf8/g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LmD0/a;->a:Lf8/g;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LmD0/a;->a:Lf8/g;

    invoke-static {v0}, LmD0/b;->a(Lf8/g;)LlD0/a;

    move-result-object v0

    return-object v0
.end method
