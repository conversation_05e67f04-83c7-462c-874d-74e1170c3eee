.class public final Lorg/xbet/uikit_sport/compose/sport_game_events/r;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/compose/sport_game_events/r$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u001a)\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004H\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "",
        "time",
        "Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;",
        "eventPositionInSection",
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "b",
        "(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Lorg/xbet/uikit_sport/compose/sport_game_events/r;->c(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 38
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move/from16 v4, p4

    const v0, -0x246e81e1

    move-object/from16 v1, p3

    .line 1
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    move-result-object v1

    const/4 v2, 0x1

    and-int/lit8 v3, p5, 0x1

    const/4 v5, 0x2

    const/4 v6, 0x4

    if-eqz v3, :cond_0

    or-int/lit8 v3, v4, 0x6

    move v7, v3

    move-object/from16 v3, p0

    goto :goto_1

    :cond_0
    and-int/lit8 v3, v4, 0x6

    if-nez v3, :cond_2

    move-object/from16 v3, p0

    invoke-interface {v1, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_1

    const/4 v7, 0x4

    goto :goto_0

    :cond_1
    const/4 v7, 0x2

    :goto_0
    or-int/2addr v7, v4

    goto :goto_1

    :cond_2
    move-object/from16 v3, p0

    move v7, v4

    :goto_1
    and-int/lit8 v8, p5, 0x2

    if-eqz v8, :cond_3

    or-int/lit8 v7, v7, 0x30

    goto :goto_3

    :cond_3
    and-int/lit8 v8, v4, 0x30

    if-nez v8, :cond_5

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Enum;->ordinal()I

    move-result v8

    invoke-interface {v1, v8}, Landroidx/compose/runtime/j;->x(I)Z

    move-result v8

    if-eqz v8, :cond_4

    const/16 v8, 0x20

    goto :goto_2

    :cond_4
    const/16 v8, 0x10

    :goto_2
    or-int/2addr v7, v8

    :cond_5
    :goto_3
    and-int/lit8 v8, p5, 0x4

    if-eqz v8, :cond_7

    or-int/lit16 v7, v7, 0x180

    :cond_6
    move-object/from16 v9, p2

    goto :goto_5

    :cond_7
    and-int/lit16 v9, v4, 0x180

    if-nez v9, :cond_6

    move-object/from16 v9, p2

    invoke-interface {v1, v9}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_8

    const/16 v10, 0x100

    goto :goto_4

    :cond_8
    const/16 v10, 0x80

    :goto_4
    or-int/2addr v7, v10

    :goto_5
    and-int/lit16 v10, v7, 0x93

    const/16 v11, 0x92

    if-ne v10, v11, :cond_a

    invoke-interface {v1}, Landroidx/compose/runtime/j;->c()Z

    move-result v10

    if-nez v10, :cond_9

    goto :goto_6

    .line 2
    :cond_9
    invoke-interface {v1}, Landroidx/compose/runtime/j;->n()V

    move-object v5, v1

    move-object v3, v9

    goto/16 :goto_e

    :cond_a
    :goto_6
    if-eqz v8, :cond_b

    .line 3
    sget-object v8, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    goto :goto_7

    :cond_b
    move-object v8, v9

    :goto_7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    move-result v9

    if-eqz v9, :cond_c

    const/4 v9, -0x1

    const-string v10, "org.xbet.uikit_sport.compose.sport_game_events.TimeEventComponent (TimeEventComponent.kt:34)"

    invoke-static {v0, v7, v9, v10}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 4
    :cond_c
    sget-object v0, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    invoke-virtual {v0}, Landroidx/compose/foundation/layout/Arrangement;->b()Landroidx/compose/foundation/layout/Arrangement$f;

    move-result-object v0

    .line 5
    sget-object v9, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    invoke-virtual {v9}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    move-result-object v10

    const/16 v11, 0x36

    .line 6
    invoke-static {v0, v10, v1, v11}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    move-result-object v0

    const/4 v10, 0x0

    .line 7
    invoke-static {v1, v10}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    move-result v11

    .line 8
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    move-result-object v12

    .line 9
    invoke-static {v1, v8}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    move-result-object v13

    .line 10
    sget-object v14, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    move-result-object v15

    .line 11
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    move-result-object v16

    invoke-static/range {v16 .. v16}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    move-result v16

    if-nez v16, :cond_d

    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 12
    :cond_d
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 13
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    move-result v16

    if-eqz v16, :cond_e

    .line 14
    invoke-interface {v1, v15}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    goto :goto_8

    .line 15
    :cond_e
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 16
    :goto_8
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    move-result-object v15

    .line 17
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    move-result-object v10

    invoke-static {v15, v0, v10}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 18
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    move-result-object v0

    invoke-static {v15, v12, v0}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 19
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    move-result-object v0

    .line 20
    invoke-interface {v15}, Landroidx/compose/runtime/j;->B()Z

    move-result v10

    if-nez v10, :cond_f

    invoke-interface {v15}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    move-result-object v10

    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-static {v10, v12}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v10

    if-nez v10, :cond_10

    .line 21
    :cond_f
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    invoke-interface {v15, v10}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 22
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v10

    invoke-interface {v15, v10, v0}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 23
    :cond_10
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    move-result-object v0

    invoke-static {v15, v13, v0}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 24
    sget-object v16, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 25
    sget-object v17, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    const/16 v20, 0x2

    const/16 v21, 0x0

    const/high16 v18, 0x3f800000    # 1.0f

    const/16 v19, 0x0

    .line 26
    invoke-static/range {v16 .. v21}, Landroidx/compose/foundation/layout/l;->a(Landroidx/compose/foundation/layout/m;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    move-result-object v0

    move-object/from16 v30, v16

    move-object/from16 v10, v17

    .line 27
    sget-object v31, Lorg/xbet/uikit_sport/compose/sport_game_events/r$a;->a:[I

    invoke-virtual/range {p1 .. p1}, Ljava/lang/Enum;->ordinal()I

    move-result v11

    aget v11, v31, v11

    const/4 v12, 0x3

    if-eq v11, v2, :cond_13

    if-eq v11, v5, :cond_13

    if-eq v11, v12, :cond_12

    if-ne v11, v6, :cond_11

    goto :goto_9

    :cond_11
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v0

    .line 28
    :cond_12
    :goto_9
    sget-object v11, LA11/a;->a:LA11/a;

    invoke-virtual {v11}, LA11/a;->y()F

    move-result v11

    goto :goto_a

    .line 29
    :cond_13
    sget-object v11, LA11/a;->a:LA11/a;

    invoke-virtual {v11}, LA11/a;->z()F

    move-result v11

    .line 30
    :goto_a
    invoke-static {v0, v11}, Landroidx/compose/foundation/layout/SizeKt;->A(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    move-result-object v15

    .line 31
    sget-object v0, LB11/e;->a:LB11/e;

    sget v11, LB11/e;->b:I

    invoke-virtual {v0, v1, v11}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    move-result-object v13

    invoke-virtual {v13}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSeparator-0d7_KjU()J

    move-result-wide v16

    const/16 v19, 0x2

    const/16 v20, 0x0

    const/16 v18, 0x0

    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/BackgroundKt;->d(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;ILjava/lang/Object;)Landroidx/compose/ui/l;

    move-result-object v13

    const/4 v15, 0x0

    .line 32
    invoke-static {v13, v1, v15}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 33
    sget-object v32, LA11/a;->a:LA11/a;

    invoke-virtual/range {v32 .. v32}, LA11/a;->M0()F

    move-result v13

    invoke-virtual/range {v32 .. v32}, LA11/a;->o0()F

    move-result v15

    invoke-static {v10, v13, v15}, Landroidx/compose/foundation/layout/SizeKt;->x(Landroidx/compose/ui/l;FF)Landroidx/compose/ui/l;

    move-result-object v13

    .line 34
    invoke-virtual {v0, v1, v11}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    move-result-object v15

    invoke-virtual {v15}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    move-result-wide v5

    invoke-static {}, LR/i;->i()LR/h;

    move-result-object v15

    invoke-static {v13, v5, v6, v15}, Landroidx/compose/foundation/BackgroundKt;->c(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    move-result-object v5

    .line 35
    invoke-virtual/range {v32 .. v32}, LA11/a;->z()F

    move-result v6

    .line 36
    invoke-virtual {v0, v1, v11}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    move-result-object v13

    invoke-virtual {v13}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSeparator-0d7_KjU()J

    move-result-wide v12

    .line 37
    invoke-static {}, LR/i;->i()LR/h;

    move-result-object v15

    .line 38
    invoke-static {v5, v6, v12, v13, v15}, Landroidx/compose/foundation/BorderKt;->f(Landroidx/compose/ui/l;FJLandroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    move-result-object v5

    .line 39
    invoke-virtual {v9}, Landroidx/compose/ui/e$a;->e()Landroidx/compose/ui/e;

    move-result-object v6

    const/4 v15, 0x0

    .line 40
    invoke-static {v6, v15}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    move-result-object v6

    .line 41
    invoke-static {v1, v15}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    move-result v9

    .line 42
    invoke-interface {v1}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    move-result-object v12

    .line 43
    invoke-static {v1, v5}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    move-result-object v5

    .line 44
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    move-result-object v13

    .line 45
    invoke-interface {v1}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    move-result-object v15

    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    move-result v15

    if-nez v15, :cond_14

    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 46
    :cond_14
    invoke-interface {v1}, Landroidx/compose/runtime/j;->l()V

    .line 47
    invoke-interface {v1}, Landroidx/compose/runtime/j;->B()Z

    move-result v15

    if-eqz v15, :cond_15

    .line 48
    invoke-interface {v1, v13}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    goto :goto_b

    .line 49
    :cond_15
    invoke-interface {v1}, Landroidx/compose/runtime/j;->h()V

    .line 50
    :goto_b
    invoke-static {v1}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    move-result-object v13

    .line 51
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    move-result-object v15

    invoke-static {v13, v6, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 52
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    move-result-object v6

    invoke-static {v13, v12, v6}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 53
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    move-result-object v6

    .line 54
    invoke-interface {v13}, Landroidx/compose/runtime/j;->B()Z

    move-result v12

    if-nez v12, :cond_16

    invoke-interface {v13}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    move-result-object v12

    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v15

    invoke-static {v12, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v12

    if-nez v12, :cond_17

    .line 55
    :cond_16
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v12

    invoke-interface {v13, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 56
    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-interface {v13, v9, v6}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 57
    :cond_17
    invoke-virtual {v14}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    move-result-object v6

    invoke-static {v13, v5, v6}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 58
    sget-object v5, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 59
    sget-object v5, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    invoke-virtual {v5}, Landroidx/compose/ui/text/style/i$a;->a()I

    move-result v5

    invoke-static {v5}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    move-result-object v5

    .line 60
    sget-object v6, LC11/a;->a:LC11/a;

    invoke-virtual {v6}, LC11/a;->e()Landroidx/compose/ui/text/a0;

    move-result-object v6

    const/4 v15, 0x0

    invoke-static {v6, v1, v15}, LB11/a;->d(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    move-result-object v25

    and-int/lit8 v27, v7, 0xe

    const/16 v28, 0x0

    const v29, 0xfdfe

    const/4 v6, 0x0

    move-object v9, v8

    const-wide/16 v7, 0x0

    move-object v12, v9

    move-object v13, v10

    const-wide/16 v9, 0x0

    move v14, v11

    const/4 v11, 0x0

    move-object/from16 v18, v12

    const/4 v12, 0x0

    move-object/from16 v19, v13

    const/4 v13, 0x0

    move/from16 v21, v14

    const/16 v20, 0x0

    const-wide/16 v14, 0x0

    const/16 v22, 0x4

    const/16 v16, 0x0

    move-object/from16 v23, v18

    move-object/from16 v24, v19

    const-wide/16 v18, 0x0

    const/16 v26, 0x0

    const/16 v20, 0x0

    move/from16 v33, v21

    const/16 v21, 0x0

    const/16 v34, 0x4

    const/16 v22, 0x0

    move-object/from16 v35, v23

    const/16 v23, 0x0

    move-object/from16 v36, v24

    const/16 v24, 0x0

    move-object/from16 v26, v1

    move-object/from16 v17, v5

    move/from16 v37, v33

    const/4 v1, 0x2

    move-object v5, v3

    const/4 v3, 0x3

    .line 61
    invoke-static/range {v5 .. v29}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    move-object/from16 v5, v26

    .line 62
    invoke-interface {v5}, Landroidx/compose/runtime/j;->j()V

    const/16 v20, 0x2

    const/16 v21, 0x0

    const/high16 v18, 0x3f800000    # 1.0f

    const/16 v19, 0x0

    move-object/from16 v16, v30

    move-object/from16 v17, v36

    .line 63
    invoke-static/range {v16 .. v21}, Landroidx/compose/foundation/layout/l;->a(Landroidx/compose/foundation/layout/m;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    move-result-object v6

    .line 64
    invoke-virtual/range {p1 .. p1}, Ljava/lang/Enum;->ordinal()I

    move-result v7

    aget v7, v31, v7

    if-eq v7, v2, :cond_1a

    if-eq v7, v1, :cond_19

    if-eq v7, v3, :cond_19

    const/4 v1, 0x4

    if-ne v7, v1, :cond_18

    goto :goto_c

    :cond_18
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v0

    .line 65
    :cond_19
    invoke-virtual/range {v32 .. v32}, LA11/a;->z()F

    move-result v1

    goto :goto_d

    .line 66
    :cond_1a
    :goto_c
    invoke-virtual/range {v32 .. v32}, LA11/a;->y()F

    move-result v1

    .line 67
    :goto_d
    invoke-static {v6, v1}, Landroidx/compose/foundation/layout/SizeKt;->A(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    move-result-object v7

    move/from16 v14, v37

    .line 68
    invoke-virtual {v0, v5, v14}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    move-result-object v0

    invoke-virtual {v0}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSeparator-0d7_KjU()J

    move-result-wide v8

    const/4 v11, 0x2

    const/4 v12, 0x0

    const/4 v10, 0x0

    invoke-static/range {v7 .. v12}, Landroidx/compose/foundation/BackgroundKt;->d(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;ILjava/lang/Object;)Landroidx/compose/ui/l;

    move-result-object v0

    const/4 v15, 0x0

    .line 69
    invoke-static {v0, v5, v15}, Landroidx/compose/foundation/layout/o0;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 70
    invoke-interface {v5}, Landroidx/compose/runtime/j;->j()V

    .line 71
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    move-result v0

    if-eqz v0, :cond_1b

    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    :cond_1b
    move-object/from16 v3, v35

    :goto_e
    invoke-interface {v5}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    move-result-object v6

    if-eqz v6, :cond_1c

    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/q;

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move/from16 v5, p5

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/q;-><init>(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;II)V

    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    :cond_1c
    return-void
.end method

.method public static final c(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    move-result v4

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v5, p4

    move-object v3, p5

    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/r;->b(Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_game_events/model/DsEventPositionInSection;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method
