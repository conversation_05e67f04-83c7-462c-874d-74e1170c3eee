.class public final Lt61/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lt61/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lt61/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lt61/a$b$b;,
        Lt61/a$b$c;,
        Lt61/a$b$d;,
        Lt61/a$b$e;,
        Lt61/a$b$a;,
        Lt61/a$b$f;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/usecase/l;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/presentation/wallets/WalletsViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:Lak/b;

.field public final b:LTZ0/a;

.field public final c:LzX0/k;

.field public final d:Lt61/a$b;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lr80/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LlR/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lu61/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/usecase/g;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/n;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/usecase/c;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/b;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/u;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/wallet/impl/domain/wallets/usecase/j;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;LmS0/a;LiR/a;Lak/b;LTZ0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lr80/a;LwX0/a;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/a;Lorg/xbet/ui_common/utils/internet/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/ui_common/utils/M;Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;Lu61/a;Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/g;Lorg/xbet/wallet/impl/domain/wallets/usecase/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/j;LwX0/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/l;LzX0/k;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, Lt61/a$b;->d:Lt61/a$b;

    .line 4
    iput-object p5, p0, Lt61/a$b;->a:Lak/b;

    .line 5
    iput-object p6, p0, Lt61/a$b;->b:LTZ0/a;

    move-object/from16 v0, p25

    .line 6
    iput-object v0, p0, Lt61/a$b;->c:LzX0/k;

    .line 7
    invoke-virtual/range {p0 .. p25}, Lt61/a$b;->b(LQW0/c;Lak/a;LmS0/a;LiR/a;Lak/b;LTZ0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lr80/a;LwX0/a;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/a;Lorg/xbet/ui_common/utils/internet/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/ui_common/utils/M;Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;Lu61/a;Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/g;Lorg/xbet/wallet/impl/domain/wallets/usecase/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/j;LwX0/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/l;LzX0/k;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lak/a;LmS0/a;LiR/a;Lak/b;LTZ0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lr80/a;LwX0/a;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/a;Lorg/xbet/ui_common/utils/internet/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/ui_common/utils/M;Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;Lu61/a;Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/g;Lorg/xbet/wallet/impl/domain/wallets/usecase/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/j;LwX0/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/l;LzX0/k;Lt61/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p25}, Lt61/a$b;-><init>(LQW0/c;Lak/a;LmS0/a;LiR/a;Lak/b;LTZ0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lr80/a;LwX0/a;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/a;Lorg/xbet/ui_common/utils/internet/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/ui_common/utils/M;Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;Lu61/a;Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/g;Lorg/xbet/wallet/impl/domain/wallets/usecase/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/j;LwX0/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/l;LzX0/k;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lt61/a$b;->c(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;)Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lak/a;LmS0/a;LiR/a;Lak/b;LTZ0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lr80/a;LwX0/a;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/a;Lorg/xbet/ui_common/utils/internet/a;LSX0/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/ui_common/utils/M;Lorg/xbet/wallet/impl/domain/wallets/scenarios/LoadWalletsScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/GetCurrentCurrencyIdUseCase;Lu61/a;Lorg/xbet/wallet/impl/domain/wallets/scenarios/DeleteAccountScenario;Lorg/xbet/wallet/impl/domain/wallets/usecase/g;Lorg/xbet/wallet/impl/domain/wallets/usecase/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/j;LwX0/c;Lorg/xbet/wallet/impl/domain/wallets/usecase/l;LzX0/k;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, Lt61/a$b;->e:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, Lt61/a$b;->f:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iput-object v2, v0, Lt61/a$b;->g:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iput-object v2, v0, Lt61/a$b;->h:Ldagger/internal/h;

    .line 28
    .line 29
    new-instance v2, Lt61/a$b$b;

    .line 30
    .line 31
    move-object/from16 v3, p4

    .line 32
    .line 33
    invoke-direct {v2, v3}, Lt61/a$b$b;-><init>(LiR/a;)V

    .line 34
    .line 35
    .line 36
    iput-object v2, v0, Lt61/a$b;->i:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    iput-object v2, v0, Lt61/a$b;->j:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    iput-object v2, v0, Lt61/a$b;->k:Ldagger/internal/h;

    .line 49
    .line 50
    new-instance v2, Lt61/a$b$c;

    .line 51
    .line 52
    move-object/from16 v3, p1

    .line 53
    .line 54
    invoke-direct {v2, v3}, Lt61/a$b$c;-><init>(LQW0/c;)V

    .line 55
    .line 56
    .line 57
    iput-object v2, v0, Lt61/a$b;->l:Ldagger/internal/h;

    .line 58
    .line 59
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    iput-object v2, v0, Lt61/a$b;->m:Ldagger/internal/h;

    .line 64
    .line 65
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 66
    .line 67
    .line 68
    move-result-object v2

    .line 69
    iput-object v2, v0, Lt61/a$b;->n:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    iput-object v2, v0, Lt61/a$b;->o:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    iput-object v2, v0, Lt61/a$b;->p:Ldagger/internal/h;

    .line 82
    .line 83
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 84
    .line 85
    .line 86
    move-result-object v2

    .line 87
    iput-object v2, v0, Lt61/a$b;->q:Ldagger/internal/h;

    .line 88
    .line 89
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    iput-object v2, v0, Lt61/a$b;->r:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    iput-object v2, v0, Lt61/a$b;->s:Ldagger/internal/h;

    .line 100
    .line 101
    new-instance v2, Lt61/a$b$d;

    .line 102
    .line 103
    invoke-direct {v2, v1}, Lt61/a$b$d;-><init>(Lak/a;)V

    .line 104
    .line 105
    .line 106
    iput-object v2, v0, Lt61/a$b;->t:Ldagger/internal/h;

    .line 107
    .line 108
    new-instance v2, Lt61/a$b$e;

    .line 109
    .line 110
    invoke-direct {v2, v1}, Lt61/a$b$e;-><init>(Lak/a;)V

    .line 111
    .line 112
    .line 113
    iput-object v2, v0, Lt61/a$b;->u:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 116
    .line 117
    .line 118
    move-result-object v2

    .line 119
    iput-object v2, v0, Lt61/a$b;->v:Ldagger/internal/h;

    .line 120
    .line 121
    new-instance v2, Lt61/a$b$a;

    .line 122
    .line 123
    invoke-direct {v2, v1}, Lt61/a$b$a;-><init>(Lak/a;)V

    .line 124
    .line 125
    .line 126
    iput-object v2, v0, Lt61/a$b;->w:Ldagger/internal/h;

    .line 127
    .line 128
    new-instance v2, Lt61/a$b$f;

    .line 129
    .line 130
    invoke-direct {v2, v1}, Lt61/a$b$f;-><init>(Lak/a;)V

    .line 131
    .line 132
    .line 133
    iput-object v2, v0, Lt61/a$b;->x:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    iput-object v1, v0, Lt61/a$b;->y:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 142
    .line 143
    .line 144
    move-result-object v1

    .line 145
    iput-object v1, v0, Lt61/a$b;->z:Ldagger/internal/h;

    .line 146
    .line 147
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 148
    .line 149
    .line 150
    move-result-object v1

    .line 151
    iput-object v1, v0, Lt61/a$b;->A:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v2, v0, Lt61/a$b;->e:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object v3, v0, Lt61/a$b;->f:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object v4, v0, Lt61/a$b;->g:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v5, v0, Lt61/a$b;->h:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object v6, v0, Lt61/a$b;->i:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object v7, v0, Lt61/a$b;->j:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object v8, v0, Lt61/a$b;->k:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v9, v0, Lt61/a$b;->l:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v10, v0, Lt61/a$b;->m:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v11, v0, Lt61/a$b;->n:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v12, v0, Lt61/a$b;->o:Ldagger/internal/h;

    .line 174
    .line 175
    iget-object v13, v0, Lt61/a$b;->p:Ldagger/internal/h;

    .line 176
    .line 177
    iget-object v14, v0, Lt61/a$b;->q:Ldagger/internal/h;

    .line 178
    .line 179
    iget-object v15, v0, Lt61/a$b;->r:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 p23, v1

    .line 182
    .line 183
    iget-object v1, v0, Lt61/a$b;->s:Ldagger/internal/h;

    .line 184
    .line 185
    move-object/from16 p15, v1

    .line 186
    .line 187
    iget-object v1, v0, Lt61/a$b;->t:Ldagger/internal/h;

    .line 188
    .line 189
    move-object/from16 p16, v1

    .line 190
    .line 191
    iget-object v1, v0, Lt61/a$b;->u:Ldagger/internal/h;

    .line 192
    .line 193
    move-object/from16 p17, v1

    .line 194
    .line 195
    iget-object v1, v0, Lt61/a$b;->v:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p18, v1

    .line 198
    .line 199
    iget-object v1, v0, Lt61/a$b;->w:Ldagger/internal/h;

    .line 200
    .line 201
    move-object/from16 p19, v1

    .line 202
    .line 203
    iget-object v1, v0, Lt61/a$b;->x:Ldagger/internal/h;

    .line 204
    .line 205
    move-object/from16 p20, v1

    .line 206
    .line 207
    iget-object v1, v0, Lt61/a$b;->y:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 p21, v1

    .line 210
    .line 211
    iget-object v1, v0, Lt61/a$b;->z:Ldagger/internal/h;

    .line 212
    .line 213
    move-object/from16 p22, v1

    .line 214
    .line 215
    move-object/from16 p1, v2

    .line 216
    .line 217
    move-object/from16 p2, v3

    .line 218
    .line 219
    move-object/from16 p3, v4

    .line 220
    .line 221
    move-object/from16 p4, v5

    .line 222
    .line 223
    move-object/from16 p5, v6

    .line 224
    .line 225
    move-object/from16 p6, v7

    .line 226
    .line 227
    move-object/from16 p7, v8

    .line 228
    .line 229
    move-object/from16 p8, v9

    .line 230
    .line 231
    move-object/from16 p9, v10

    .line 232
    .line 233
    move-object/from16 p10, v11

    .line 234
    .line 235
    move-object/from16 p11, v12

    .line 236
    .line 237
    move-object/from16 p12, v13

    .line 238
    .line 239
    move-object/from16 p13, v14

    .line 240
    .line 241
    move-object/from16 p14, v15

    .line 242
    .line 243
    invoke-static/range {p1 .. p23}, Lorg/xbet/wallet/impl/presentation/wallets/s;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/wallet/impl/presentation/wallets/s;

    .line 244
    .line 245
    .line 246
    move-result-object v1

    .line 247
    iput-object v1, v0, Lt61/a$b;->B:Ldagger/internal/h;

    .line 248
    .line 249
    return-void
.end method

.method public final c(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;)Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lt61/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/wallet/impl/presentation/wallets/l;->d(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lt61/a$b;->a:Lak/b;

    .line 9
    .line 10
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, Lck/a;

    .line 19
    .line 20
    invoke-static {p1, v0}, Lorg/xbet/wallet/impl/presentation/wallets/l;->b(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;Lck/a;)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, Lt61/a$b;->b:LTZ0/a;

    .line 24
    .line 25
    invoke-static {p1, v0}, Lorg/xbet/wallet/impl/presentation/wallets/l;->a(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;LTZ0/a;)V

    .line 26
    .line 27
    .line 28
    iget-object v0, p0, Lt61/a$b;->c:LzX0/k;

    .line 29
    .line 30
    invoke-static {p1, v0}, Lorg/xbet/wallet/impl/presentation/wallets/l;->c(Lorg/xbet/wallet/impl/presentation/wallets/WalletsFragment;LzX0/k;)V

    .line 31
    .line 32
    .line 33
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/wallet/impl/presentation/wallets/WalletsViewModel;

    .line 2
    .line 3
    iget-object v1, p0, Lt61/a$b;->B:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, Lt61/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
