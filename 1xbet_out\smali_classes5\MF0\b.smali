.class public final LMF0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/lottie/LottieView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Lcom/facebook/shimmer/ShimmerFrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Landroidx/constraintlayout/widget/Group;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lcom/google/android/material/appbar/MaterialToolbar;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;Landroid/view/View;Lcom/facebook/shimmer/ShimmerFrameLayout;Landroidx/constraintlayout/widget/Group;Lcom/google/android/material/appbar/MaterialToolbar;)V
    .locals 0
    .param p1    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie/LottieView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Lcom/facebook/shimmer/ShimmerFrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Landroidx/constraintlayout/widget/Group;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lcom/google/android/material/appbar/MaterialToolbar;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LMF0/b;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LMF0/b;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 7
    .line 8
    iput-object p3, p0, LMF0/b;->c:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;

    .line 9
    .line 10
    iput-object p4, p0, LMF0/b;->d:Landroid/view/View;

    .line 11
    .line 12
    iput-object p5, p0, LMF0/b;->e:Lcom/facebook/shimmer/ShimmerFrameLayout;

    .line 13
    .line 14
    iput-object p6, p0, LMF0/b;->f:Landroidx/constraintlayout/widget/Group;

    .line 15
    .line 16
    iput-object p7, p0, LMF0/b;->g:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 17
    .line 18
    return-void
.end method

.method public static a(Landroid/view/View;)LMF0/b;
    .locals 10
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, LLF0/b;->lottieEmptyView:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v4, v1

    .line 8
    check-cast v4, Lorg/xbet/uikit/components/lottie/LottieView;

    .line 9
    .line 10
    if-eqz v4, :cond_0

    .line 11
    .line 12
    sget v0, LLF0/b;->panelView:I

    .line 13
    .line 14
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v5, v1

    .line 19
    check-cast v5, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;

    .line 20
    .line 21
    if-eqz v5, :cond_0

    .line 22
    .line 23
    sget v0, LLF0/b;->separator:I

    .line 24
    .line 25
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 26
    .line 27
    .line 28
    move-result-object v6

    .line 29
    if-eqz v6, :cond_0

    .line 30
    .line 31
    sget v0, LLF0/b;->shimmer:I

    .line 32
    .line 33
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    move-object v7, v1

    .line 38
    check-cast v7, Lcom/facebook/shimmer/ShimmerFrameLayout;

    .line 39
    .line 40
    if-eqz v7, :cond_0

    .line 41
    .line 42
    sget v0, LLF0/b;->shimmerGroup:I

    .line 43
    .line 44
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    move-object v8, v1

    .line 49
    check-cast v8, Landroidx/constraintlayout/widget/Group;

    .line 50
    .line 51
    if-eqz v8, :cond_0

    .line 52
    .line 53
    sget v0, LLF0/b;->toolbar:I

    .line 54
    .line 55
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    move-object v9, v1

    .line 60
    check-cast v9, Lcom/google/android/material/appbar/MaterialToolbar;

    .line 61
    .line 62
    if-eqz v9, :cond_0

    .line 63
    .line 64
    new-instance v2, LMF0/b;

    .line 65
    .line 66
    move-object v3, p0

    .line 67
    check-cast v3, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 68
    .line 69
    invoke-direct/range {v2 .. v9}, LMF0/b;-><init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/ScrollablePanel;Landroid/view/View;Lcom/facebook/shimmer/ShimmerFrameLayout;Landroidx/constraintlayout/widget/Group;Lcom/google/android/material/appbar/MaterialToolbar;)V

    .line 70
    .line 71
    .line 72
    return-object v2

    .line 73
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 74
    .line 75
    .line 76
    move-result-object p0

    .line 77
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 78
    .line 79
    .line 80
    move-result-object p0

    .line 81
    new-instance v0, Ljava/lang/NullPointerException;

    .line 82
    .line 83
    const-string v1, "Missing required view with ID: "

    .line 84
    .line 85
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object p0

    .line 89
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 90
    .line 91
    .line 92
    throw v0
.end method


# virtual methods
.method public b()Landroidx/constraintlayout/widget/ConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LMF0/b;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LMF0/b;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
