.class public Lh4/i;
.super Lh4/h;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lh4/h<",
        "Lcom/github/mikephil/charting/charts/RadarChart;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Lcom/github/mikephil/charting/charts/RadarChart;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lh4/h;-><init>(Lcom/github/mikephil/charting/charts/PieRadarChartBase;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public b(IFF)Lh4/d;
    .locals 5

    .line 1
    invoke-virtual {p0, p1}, Lh4/i;->c(I)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object v0, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 6
    .line 7
    check-cast v0, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 8
    .line 9
    invoke-virtual {v0, p2, p3}, Lcom/github/mikephil/charting/charts/PieRadarChartBase;->y(FF)F

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    iget-object p3, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 14
    .line 15
    check-cast p3, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 16
    .line 17
    invoke-virtual {p3}, Lcom/github/mikephil/charting/charts/RadarChart;->getFactor()F

    .line 18
    .line 19
    .line 20
    move-result p3

    .line 21
    div-float/2addr p2, p3

    .line 22
    const/4 p3, 0x0

    .line 23
    const v0, 0x7f7fffff    # Float.MAX_VALUE

    .line 24
    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    if-ge v1, v2, :cond_1

    .line 32
    .line 33
    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    check-cast v2, Lh4/d;

    .line 38
    .line 39
    invoke-virtual {v2}, Lh4/d;->j()F

    .line 40
    .line 41
    .line 42
    move-result v3

    .line 43
    sub-float/2addr v3, p2

    .line 44
    invoke-static {v3}, Ljava/lang/Math;->abs(F)F

    .line 45
    .line 46
    .line 47
    move-result v3

    .line 48
    cmpg-float v4, v3, v0

    .line 49
    .line 50
    if-gez v4, :cond_0

    .line 51
    .line 52
    move-object p3, v2

    .line 53
    move v0, v3

    .line 54
    :cond_0
    add-int/lit8 v1, v1, 0x1

    .line 55
    .line 56
    goto :goto_0

    .line 57
    :cond_1
    return-object p3
.end method

.method public c(I)Ljava/util/List;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lh4/d;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lh4/h;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->clear()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 7
    .line 8
    check-cast v0, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 9
    .line 10
    invoke-virtual {v0}, Lcom/github/mikephil/charting/charts/Chart;->getAnimator()Lc4/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Lc4/a;->a()F

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    iget-object v1, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 19
    .line 20
    check-cast v1, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 21
    .line 22
    invoke-virtual {v1}, Lcom/github/mikephil/charting/charts/Chart;->getAnimator()Lc4/a;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {v1}, Lc4/a;->b()F

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    iget-object v2, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 31
    .line 32
    check-cast v2, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 33
    .line 34
    invoke-virtual {v2}, Lcom/github/mikephil/charting/charts/RadarChart;->getSliceAngle()F

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    iget-object v3, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 39
    .line 40
    check-cast v3, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 41
    .line 42
    invoke-virtual {v3}, Lcom/github/mikephil/charting/charts/RadarChart;->getFactor()F

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    const/4 v4, 0x0

    .line 47
    invoke-static {v4, v4}, Lp4/e;->c(FF)Lp4/e;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    const/4 v5, 0x0

    .line 52
    const/4 v11, 0x0

    .line 53
    :goto_0
    iget-object v5, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 54
    .line 55
    check-cast v5, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 56
    .line 57
    invoke-virtual {v5}, Lcom/github/mikephil/charting/charts/Chart;->getData()Lf4/h;

    .line 58
    .line 59
    .line 60
    move-result-object v5

    .line 61
    check-cast v5, Lf4/n;

    .line 62
    .line 63
    invoke-virtual {v5}, Lf4/h;->i()I

    .line 64
    .line 65
    .line 66
    move-result v5

    .line 67
    if-ge v11, v5, :cond_0

    .line 68
    .line 69
    iget-object v5, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 70
    .line 71
    check-cast v5, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 72
    .line 73
    invoke-virtual {v5}, Lcom/github/mikephil/charting/charts/Chart;->getData()Lf4/h;

    .line 74
    .line 75
    .line 76
    move-result-object v5

    .line 77
    check-cast v5, Lf4/n;

    .line 78
    .line 79
    invoke-virtual {v5, v11}, Lf4/h;->h(I)Lj4/e;

    .line 80
    .line 81
    .line 82
    move-result-object v5

    .line 83
    invoke-interface {v5, p1}, Lj4/e;->i(I)Lcom/github/mikephil/charting/data/Entry;

    .line 84
    .line 85
    .line 86
    move-result-object v6

    .line 87
    invoke-virtual {v6}, Lf4/e;->c()F

    .line 88
    .line 89
    .line 90
    move-result v7

    .line 91
    iget-object v8, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 92
    .line 93
    check-cast v8, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 94
    .line 95
    invoke-virtual {v8}, Lcom/github/mikephil/charting/charts/RadarChart;->getYChartMin()F

    .line 96
    .line 97
    .line 98
    move-result v8

    .line 99
    sub-float/2addr v7, v8

    .line 100
    iget-object v8, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 101
    .line 102
    check-cast v8, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 103
    .line 104
    invoke-virtual {v8}, Lcom/github/mikephil/charting/charts/Chart;->getCenterOffsets()Lp4/e;

    .line 105
    .line 106
    .line 107
    move-result-object v8

    .line 108
    mul-float v7, v7, v3

    .line 109
    .line 110
    mul-float v7, v7, v1

    .line 111
    .line 112
    move v9, v7

    .line 113
    int-to-float v7, p1

    .line 114
    mul-float v10, v2, v7

    .line 115
    .line 116
    mul-float v10, v10, v0

    .line 117
    .line 118
    iget-object v12, p0, Lh4/h;->a:Lcom/github/mikephil/charting/charts/PieRadarChartBase;

    .line 119
    .line 120
    check-cast v12, Lcom/github/mikephil/charting/charts/RadarChart;

    .line 121
    .line 122
    invoke-virtual {v12}, Lcom/github/mikephil/charting/charts/PieRadarChartBase;->getRotationAngle()F

    .line 123
    .line 124
    .line 125
    move-result v12

    .line 126
    add-float/2addr v10, v12

    .line 127
    invoke-static {v8, v9, v10, v4}, Lp4/i;->r(Lp4/e;FFLp4/e;)V

    .line 128
    .line 129
    .line 130
    iget-object v13, p0, Lh4/h;->b:Ljava/util/List;

    .line 131
    .line 132
    move-object v8, v6

    .line 133
    new-instance v6, Lh4/d;

    .line 134
    .line 135
    invoke-virtual {v8}, Lf4/e;->c()F

    .line 136
    .line 137
    .line 138
    move-result v8

    .line 139
    iget v9, v4, Lp4/e;->c:F

    .line 140
    .line 141
    iget v10, v4, Lp4/e;->d:F

    .line 142
    .line 143
    invoke-interface {v5}, Lj4/e;->n0()Lcom/github/mikephil/charting/components/YAxis$AxisDependency;

    .line 144
    .line 145
    .line 146
    move-result-object v12

    .line 147
    invoke-direct/range {v6 .. v12}, Lh4/d;-><init>(FFFFILcom/github/mikephil/charting/components/YAxis$AxisDependency;)V

    .line 148
    .line 149
    .line 150
    invoke-interface {v13, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 151
    .line 152
    .line 153
    add-int/lit8 v11, v11, 0x1

    .line 154
    .line 155
    goto :goto_0

    .line 156
    :cond_0
    iget-object p1, p0, Lh4/h;->b:Ljava/util/List;

    .line 157
    .line 158
    return-object p1
.end method
