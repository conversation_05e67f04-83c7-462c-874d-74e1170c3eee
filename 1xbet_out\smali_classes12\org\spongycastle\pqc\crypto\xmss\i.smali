.class public final Lorg/spongycastle/pqc/crypto/xmss/i;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/p;

.field public final b:Lorg/spongycastle/crypto/e;

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I


# direct methods
.method public constructor <init>(Lorg/spongycastle/crypto/e;)V
    .locals 6

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    if-eqz p1, :cond_1

    .line 5
    .line 6
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->b:Lorg/spongycastle/crypto/e;

    .line 7
    .line 8
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->h(Lorg/spongycastle/crypto/e;)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->c:I

    .line 13
    .line 14
    const/16 v1, 0x10

    .line 15
    .line 16
    iput v1, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->d:I

    .line 17
    .line 18
    mul-int/lit8 v2, v0, 0x8

    .line 19
    .line 20
    int-to-double v2, v2

    .line 21
    invoke-static {v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->n(I)I

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    int-to-double v4, v4

    .line 26
    div-double/2addr v2, v4

    .line 27
    invoke-static {v2, v3}, Ljava/lang/Math;->ceil(D)D

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    double-to-int v2, v2

    .line 32
    iput v2, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->f:I

    .line 33
    .line 34
    const/4 v3, 0x1

    .line 35
    rsub-int/lit8 v4, v3, 0x10

    .line 36
    .line 37
    mul-int v4, v4, v2

    .line 38
    .line 39
    invoke-static {v4}, Lorg/spongycastle/pqc/crypto/xmss/t;->n(I)I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    invoke-static {v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->n(I)I

    .line 44
    .line 45
    .line 46
    move-result v5

    .line 47
    div-int/2addr v4, v5

    .line 48
    int-to-double v4, v4

    .line 49
    invoke-static {v4, v5}, Ljava/lang/Math;->floor(D)D

    .line 50
    .line 51
    .line 52
    move-result-wide v4

    .line 53
    double-to-int v4, v4

    .line 54
    add-int/2addr v4, v3

    .line 55
    iput v4, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->g:I

    .line 56
    .line 57
    add-int/2addr v2, v4

    .line 58
    iput v2, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->e:I

    .line 59
    .line 60
    invoke-interface {p1}, Lorg/spongycastle/crypto/e;->c()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    invoke-static {v3, v0, v1, v2}, Lorg/spongycastle/pqc/crypto/xmss/h;->b(Ljava/lang/String;III)Lorg/spongycastle/pqc/crypto/xmss/h;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->a:Lorg/spongycastle/pqc/crypto/xmss/p;

    .line 69
    .line 70
    if-eqz v0, :cond_0

    .line 71
    .line 72
    return-void

    .line 73
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 74
    .line 75
    new-instance v1, Ljava/lang/StringBuilder;

    .line 76
    .line 77
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 78
    .line 79
    .line 80
    const-string v2, "cannot find OID for digest algorithm: "

    .line 81
    .line 82
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    .line 84
    .line 85
    invoke-interface {p1}, Lorg/spongycastle/crypto/e;->c()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 97
    .line 98
    .line 99
    throw v0

    .line 100
    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    .line 101
    .line 102
    const-string v0, "digest == null"

    .line 103
    .line 104
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw p1
.end method


# virtual methods
.method public a()Lorg/spongycastle/crypto/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->b:Lorg/spongycastle/crypto/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public b()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public c()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public d()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/spongycastle/pqc/crypto/xmss/i;->d:I

    .line 2
    .line 3
    return v0
.end method
