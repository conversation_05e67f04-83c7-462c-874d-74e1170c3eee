.class public final LHc1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Landroid/annotation/SuppressLint;
    value = {
        "WrongConstant"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u0008\u00c7\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00072\n\u0010\u0006\u001a\u00060\u0004j\u0002`\u0005\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LHc1/a;",
        "",
        "<init>",
        "()V",
        "",
        "Lorg/xplatform/social/api/core/EnSocialType;",
        "type",
        "",
        "a",
        "(I)Ljava/lang/String;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LHc1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LHc1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LHc1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LHc1/a;->a:LHc1/a;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(I)Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    if-eq p1, v0, :cond_9

    .line 3
    .line 4
    const/4 v0, 0x5

    .line 5
    if-eq p1, v0, :cond_8

    .line 6
    .line 7
    const/4 v0, 0x7

    .line 8
    if-eq p1, v0, :cond_7

    .line 9
    .line 10
    const/16 v0, 0x9

    .line 11
    .line 12
    if-eq p1, v0, :cond_6

    .line 13
    .line 14
    const/16 v0, 0xb

    .line 15
    .line 16
    if-eq p1, v0, :cond_5

    .line 17
    .line 18
    const/16 v0, 0xd

    .line 19
    .line 20
    if-eq p1, v0, :cond_4

    .line 21
    .line 22
    const/16 v0, 0x11

    .line 23
    .line 24
    if-eq p1, v0, :cond_3

    .line 25
    .line 26
    const/16 v0, 0x13

    .line 27
    .line 28
    if-eq p1, v0, :cond_2

    .line 29
    .line 30
    const/16 v0, 0x1a

    .line 31
    .line 32
    if-eq p1, v0, :cond_1

    .line 33
    .line 34
    const/16 v0, 0x1f

    .line 35
    .line 36
    if-eq p1, v0, :cond_0

    .line 37
    .line 38
    const-string p1, "UNKNOWN"

    .line 39
    .line 40
    return-object p1

    .line 41
    :cond_0
    const-string p1, "DISCORD"

    .line 42
    .line 43
    return-object p1

    .line 44
    :cond_1
    const-string p1, "ITS_ME"

    .line 45
    .line 46
    return-object p1

    .line 47
    :cond_2
    const-string p1, "APPLE_ID"

    .line 48
    .line 49
    return-object p1

    .line 50
    :cond_3
    const-string p1, "TELEGRAM"

    .line 51
    .line 52
    return-object p1

    .line 53
    :cond_4
    const-string p1, "X.COM"

    .line 54
    .line 55
    return-object p1

    .line 56
    :cond_5
    const-string p1, "GOOGLE"

    .line 57
    .line 58
    return-object p1

    .line 59
    :cond_6
    const-string p1, "MAILRU"

    .line 60
    .line 61
    return-object p1

    .line 62
    :cond_7
    const-string p1, "YANDEX"

    .line 63
    .line 64
    return-object p1

    .line 65
    :cond_8
    const-string p1, "OK"

    .line 66
    .line 67
    return-object p1

    .line 68
    :cond_9
    const-string p1, "VK"

    .line 69
    .line 70
    return-object p1
.end method
