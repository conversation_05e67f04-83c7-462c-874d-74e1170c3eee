.class public final LH91/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a!\u0010\u0004\u001a\u00020\u0000*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
        "filterItemChangeList",
        "a",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;Ljava/util/List;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;Ljava/util/List;)Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
    .locals 4
    .param p0    # Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
            "Ljava/util/List<",
            "+",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    move-object v1, v0

    .line 16
    check-cast v1, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 17
    .line 18
    invoke-interface {v1}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v0, 0x0

    .line 34
    :goto_0
    check-cast v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;

    .line 35
    .line 36
    new-instance p1, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;

    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getName()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    if-eqz v0, :cond_3

    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    invoke-interface {v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;->getId()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-static {v3, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v0

    .line 60
    if-eqz v0, :cond_3

    .line 61
    .line 62
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->P()Z

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    if-nez v0, :cond_2

    .line 67
    .line 68
    const/4 v0, 0x1

    .line 69
    goto :goto_1

    .line 70
    :cond_2
    const/4 v0, 0x0

    .line 71
    goto :goto_1

    .line 72
    :cond_3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->P()Z

    .line 73
    .line 74
    .line 75
    move-result v0

    .line 76
    :goto_1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->e0()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    invoke-direct {p1, v1, v2, v0, p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;)V

    .line 81
    .line 82
    .line 83
    return-object p1
.end method
