.class public final Lorg/xbet/analytics/data/repositories/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lug/d;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lug/b;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lug/d;",
            ">;",
            "LBc/a<",
            "Lug/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/c;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/c;->b:LBc/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(LBc/a;LBc/a;)Lorg/xbet/analytics/data/repositories/c;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lug/d;",
            ">;",
            "LBc/a<",
            "Lug/b;",
            ">;)",
            "Lorg/xbet/analytics/data/repositories/c;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/c;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xbet/analytics/data/repositories/c;-><init>(LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lug/d;Lug/b;)Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;-><init>(Lug/d;Lug/b;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/c;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lug/d;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/c;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lug/b;

    .line 16
    .line 17
    invoke-static {v0, v1}, Lorg/xbet/analytics/data/repositories/c;->c(Lug/d;Lug/b;)Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/c;->b()Lorg/xbet/analytics/data/repositories/CustomBTagBTTRepositoryImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
