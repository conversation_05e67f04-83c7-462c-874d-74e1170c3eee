.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/B;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic c:I

.field public final synthetic d:J

.field public final synthetic e:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->b:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->c:I

    iput-wide p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->d:J

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->e:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->a:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->b:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->c:I

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->d:J

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/core/presentation/B;->e:Lkotlin/jvm/functions/Function1;

    invoke-static/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;->a(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
