.class public final LoN0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LpN0/f;",
        "LrN0/a;",
        "a",
        "(LpN0/f;)LrN0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LpN0/f;)LrN0/a;
    .locals 4
    .param p0    # LpN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LrN0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LpN0/f;->a()Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    const/4 v3, 0x1

    .line 15
    if-ne v2, v3, :cond_1

    .line 16
    .line 17
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->GREEN:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 18
    .line 19
    goto :goto_6

    .line 20
    :cond_1
    :goto_0
    if-nez v1, :cond_2

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_2
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const/4 v3, 0x2

    .line 28
    if-ne v2, v3, :cond_3

    .line 29
    .line 30
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->LIGHT_GREEN:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 31
    .line 32
    goto :goto_6

    .line 33
    :cond_3
    :goto_1
    if-nez v1, :cond_4

    .line 34
    .line 35
    goto :goto_2

    .line 36
    :cond_4
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    const/4 v3, 0x3

    .line 41
    if-ne v2, v3, :cond_5

    .line 42
    .line 43
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->YELLOW:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 44
    .line 45
    goto :goto_6

    .line 46
    :cond_5
    :goto_2
    if-nez v1, :cond_6

    .line 47
    .line 48
    goto :goto_3

    .line 49
    :cond_6
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 50
    .line 51
    .line 52
    move-result v2

    .line 53
    const/4 v3, 0x4

    .line 54
    if-ne v2, v3, :cond_7

    .line 55
    .line 56
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->LIGHT_YELLOW:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 57
    .line 58
    goto :goto_6

    .line 59
    :cond_7
    :goto_3
    if-nez v1, :cond_8

    .line 60
    .line 61
    goto :goto_4

    .line 62
    :cond_8
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    const/4 v3, 0x5

    .line 67
    if-ne v2, v3, :cond_9

    .line 68
    .line 69
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->LIGHT_RED:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 70
    .line 71
    goto :goto_6

    .line 72
    :cond_9
    :goto_4
    if-nez v1, :cond_a

    .line 73
    .line 74
    goto :goto_5

    .line 75
    :cond_a
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    const/4 v2, 0x6

    .line 80
    if-ne v1, v2, :cond_b

    .line 81
    .line 82
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->RED:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 83
    .line 84
    goto :goto_6

    .line 85
    :cond_b
    :goto_5
    sget-object v1, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;->NONE:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;

    .line 86
    .line 87
    :goto_6
    invoke-virtual {p0}, LpN0/f;->b()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object p0

    .line 91
    if-nez p0, :cond_c

    .line 92
    .line 93
    const-string p0, ""

    .line 94
    .line 95
    :cond_c
    invoke-direct {v0, v1, p0}, LrN0/a;-><init>(Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/RowColor;Ljava/lang/String;)V

    .line 96
    .line 97
    .line 98
    return-object v0
.end method
