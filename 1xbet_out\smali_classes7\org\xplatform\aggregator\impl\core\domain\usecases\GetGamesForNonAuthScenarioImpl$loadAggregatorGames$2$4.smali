.class final Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.domain.usecases.GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4"
    f = "GetGamesForNonAuthScenarioImpl.kt"
    l = {
        0x51
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Result<",
        "+",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;>;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlin/Result;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlin/Result;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $endPoint:Ljava/lang/String;

.field final synthetic $filterType:Ljava/lang/String;

.field final synthetic $hasBrands:Z

.field final synthetic $isForceUpdate:Z

.field final synthetic $test:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;",
            "ZZ",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$test:Z

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$hasBrands:Z

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$endPoint:Ljava/lang/String;

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$filterType:Ljava/lang/String;

    iput-boolean p6, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$isForceUpdate:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$test:Z

    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$hasBrands:Z

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$endPoint:Ljava/lang/String;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$filterType:Ljava/lang/String;

    iget-boolean v6, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$isForceUpdate:Z

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    check-cast p1, Lkotlin/Result;

    .line 16
    .line 17
    invoke-virtual {p1}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 23
    .line 24
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 25
    .line 26
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 34
    .line 35
    const/4 p1, 0x1

    .line 36
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$test:Z

    .line 37
    .line 38
    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$hasBrands:Z

    .line 39
    .line 40
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$endPoint:Ljava/lang/String;

    .line 41
    .line 42
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$filterType:Ljava/lang/String;

    .line 43
    .line 44
    iget-boolean v6, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->$isForceUpdate:Z

    .line 45
    .line 46
    iput p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2$4;->label:I

    .line 47
    .line 48
    move-object v7, p0

    .line 49
    invoke-static/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->i(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    if-ne p1, v0, :cond_2

    .line 54
    .line 55
    return-object v0

    .line 56
    :cond_2
    :goto_0
    invoke-static {p1}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    return-object p1
.end method
