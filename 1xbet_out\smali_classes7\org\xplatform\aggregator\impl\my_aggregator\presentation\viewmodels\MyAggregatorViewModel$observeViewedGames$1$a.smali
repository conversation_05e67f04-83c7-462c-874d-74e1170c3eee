.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;)V
    .locals 0

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1$a;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->L5(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    if-ne p1, p2, :cond_0

    .line 12
    .line 13
    return-object p1

    .line 14
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$observeViewedGames$1$a;->a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
