.class public final LHB0/a$b$o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/h;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LHB0/a$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "o"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/h<",
        "LBi0/d;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LAi0/a;


# direct methods
.method public constructor <init>(LAi0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LHB0/a$b$o;->a:LAi0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()LBi0/d;
    .locals 1

    .line 1
    iget-object v0, p0, LHB0/a$b$o;->a:LAi0/a;

    .line 2
    .line 3
    invoke-interface {v0}, LAi0/a;->d()LBi0/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LBi0/d;

    .line 12
    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LHB0/a$b$o;->a()LBi0/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
