.class public final Lqy0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lqy0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lqy0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lqy0/a$b$a;,
        Lqy0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:Lqy0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LBu0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/special_event/impl/statistic/data/StatisticStadiumRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIu0/d;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LIu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHg/d;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfS/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public r:Lorg/xbet/special_event/impl/venues/presentation/j;

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lqy0/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LiR/a;Ljava/lang/String;LfX/b;LwX0/c;Ljava/lang/Integer;Lc8/h;Lf8/g;LHX0/e;LSX0/a;LBu0/a;LHg/d;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, Lqy0/a$b;->a:Lqy0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p12}, Lqy0/a$b;->b(LQW0/c;LiR/a;Ljava/lang/String;LfX/b;LwX0/c;Ljava/lang/Integer;Lc8/h;Lf8/g;LHX0/e;LSX0/a;LBu0/a;LHg/d;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LiR/a;Ljava/lang/String;LfX/b;LwX0/c;Ljava/lang/Integer;Lc8/h;Lf8/g;LHX0/e;LSX0/a;LBu0/a;LHg/d;Lqy0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p12}, Lqy0/a$b;-><init>(LQW0/c;LiR/a;Ljava/lang/String;LfX/b;LwX0/c;Ljava/lang/Integer;Lc8/h;Lf8/g;LHX0/e;LSX0/a;LBu0/a;LHg/d;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lqy0/a$b;->c(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LiR/a;Ljava/lang/String;LfX/b;LwX0/c;Ljava/lang/Integer;Lc8/h;Lf8/g;LHX0/e;LSX0/a;LBu0/a;LHg/d;)V
    .locals 0

    .line 1
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    iput-object p4, p0, Lqy0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p4

    .line 11
    iput-object p4, p0, Lqy0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance p4, Lqy0/a$b$a;

    .line 14
    .line 15
    invoke-direct {p4, p1}, Lqy0/a$b$a;-><init>(LQW0/c;)V

    .line 16
    .line 17
    .line 18
    iput-object p4, p0, Lqy0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, Lqy0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p1}, LCu0/c;->a(LBc/a;)LCu0/c;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, Lqy0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, Lqy0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, Lqy0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object p4, p0, Lqy0/a$b;->d:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object p6, p0, Lqy0/a$b;->f:Ldagger/internal/h;

    .line 47
    .line 48
    iget-object p7, p0, Lqy0/a$b;->g:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static {p4, p6, p7, p1}, Lorg/xbet/special_event/impl/statistic/data/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/statistic/data/a;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, Lqy0/a$b;->i:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, Lqy0/a$b;->j:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {p1}, LIu0/c;->a(LBc/a;)LIu0/c;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, Lqy0/a$b;->k:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lqy0/a$b;->l:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static {}, LWo0/c;->a()LWo0/c;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, Lqy0/a$b;->m:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, Lqy0/a$b;->n:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, Lqy0/a$b;->o:Ldagger/internal/h;

    .line 95
    .line 96
    new-instance p1, Lqy0/a$b$b;

    .line 97
    .line 98
    invoke-direct {p1, p2}, Lqy0/a$b$b;-><init>(LiR/a;)V

    .line 99
    .line 100
    .line 101
    iput-object p1, p0, Lqy0/a$b;->p:Ldagger/internal/h;

    .line 102
    .line 103
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 104
    .line 105
    .line 106
    move-result-object p11

    .line 107
    iput-object p11, p0, Lqy0/a$b;->q:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object p2, p0, Lqy0/a$b;->b:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object p3, p0, Lqy0/a$b;->c:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object p4, p0, Lqy0/a$b;->k:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p5, p0, Lqy0/a$b;->d:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p6, p0, Lqy0/a$b;->l:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p7, p0, Lqy0/a$b;->m:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p8, p0, Lqy0/a$b;->n:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p9, p0, Lqy0/a$b;->o:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p10, p0, Lqy0/a$b;->p:Ldagger/internal/h;

    .line 126
    .line 127
    invoke-static/range {p2 .. p11}, Lorg/xbet/special_event/impl/venues/presentation/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/venues/presentation/j;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    iput-object p1, p0, Lqy0/a$b;->r:Lorg/xbet/special_event/impl/venues/presentation/j;

    .line 132
    .line 133
    invoke-static {p1}, Lqy0/g;->c(Lorg/xbet/special_event/impl/venues/presentation/j;)Ldagger/internal/h;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, Lqy0/a$b;->s:Ldagger/internal/h;

    .line 138
    .line 139
    return-void
.end method

.method public final c(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, Lqy0/a$b;->s:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lqy0/f;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/venues/presentation/f;->a(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;Lqy0/f;)V

    .line 10
    .line 11
    .line 12
    return-object p1
.end method
