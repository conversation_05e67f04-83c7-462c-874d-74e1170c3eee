.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

.field public final synthetic b:Lorg/xbet/uikit/components/accountselection/AccountSelection;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/r;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/r;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/r;->a:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/r;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->B2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
