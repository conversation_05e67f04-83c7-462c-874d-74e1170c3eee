.class final Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.utils.temporarystore.TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2"
    f = "TemporaryResultStore.kt"
    l = {
        0x45,
        0x46
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->k()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore<",
            "TT;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->this$0:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->this$0:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;

    invoke-direct {p1, v0, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;-><init>(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iput v3, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->label:I

    .line 35
    .line 36
    const-wide/32 v3, 0x1d4c0

    .line 37
    .line 38
    .line 39
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-ne p1, v0, :cond_3

    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_3
    :goto_0
    iget-object p1, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->this$0:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->c(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;)Lkotlinx/coroutines/flow/V;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    new-instance v1, Lkotlin/Pair;

    .line 53
    .line 54
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 55
    .line 56
    .line 57
    move-result-wide v3

    .line 58
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    new-instance v4, LKo0/a$a;

    .line 63
    .line 64
    new-instance v5, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$TemporaryResultExpiredException;

    .line 65
    .line 66
    invoke-direct {v5}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$TemporaryResultExpiredException;-><init>()V

    .line 67
    .line 68
    .line 69
    invoke-direct {v4, v5}, LKo0/a$a;-><init>(Ljava/lang/Throwable;)V

    .line 70
    .line 71
    .line 72
    invoke-direct {v1, v3, v4}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 73
    .line 74
    .line 75
    iput v2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;->label:I

    .line 76
    .line 77
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    if-ne p1, v0, :cond_4

    .line 82
    .line 83
    :goto_1
    return-object v0

    .line 84
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 85
    .line 86
    return-object p1
.end method
