.class public final synthetic LGO0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LGO0/b;


# direct methods
.method public synthetic constructor <init>(LGO0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LGO0/a;->a:LGO0/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LGO0/a;->a:LGO0/b;

    invoke-static {v0}, LGO0/b;->a(LGO0/b;)LFO0/a;

    move-result-object v0

    return-object v0
.end method
