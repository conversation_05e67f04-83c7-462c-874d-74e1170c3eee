.class public final Lc71/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "La71/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:Lc71/d;


# direct methods
.method public constructor <init>(Lc71/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lc71/f;->a:Lc71/d;

    .line 5
    .line 6
    return-void
.end method

.method public static a(Lc71/d;)Lc71/f;
    .locals 1

    .line 1
    new-instance v0, Lc71/f;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lc71/f;-><init>(Lc71/d;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lc71/d;)La71/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lc71/d;->b()La71/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-static {p0}, Ldagger/internal/g;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    check-cast p0, La71/a;

    .line 10
    .line 11
    return-object p0
.end method


# virtual methods
.method public b()La71/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lc71/f;->a:Lc71/d;

    .line 2
    .line 3
    invoke-static {v0}, Lc71/f;->c(Lc71/d;)La71/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lc71/f;->b()La71/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
