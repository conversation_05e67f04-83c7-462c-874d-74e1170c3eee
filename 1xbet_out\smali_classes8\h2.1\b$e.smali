.class public final Lh2/b$e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh2/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation


# instance fields
.field public final a:J

.field public final b:J

.field public final c:Ljava/lang/String;


# direct methods
.method public constructor <init>(JJLjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lh2/b$e;->a:J

    .line 5
    .line 6
    iput-wide p3, p0, Lh2/b$e;->b:J

    .line 7
    .line 8
    iput-object p5, p0, Lh2/b$e;->c:Ljava/lang/String;

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic a(Lh2/b$e;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lh2/b$e;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lh2/b$e;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lh2/b$e;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static synthetic c(Lh2/b$e;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lh2/b$e;->b:J

    .line 2
    .line 3
    return-wide v0
.end method
