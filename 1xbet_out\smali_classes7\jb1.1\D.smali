.class public final Ljb1/D;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a1\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0005H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\u001b\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007*\u0004\u0018\u00010\u000bH\u0000\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a!\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007*\u00020\u000e2\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u001a\u0017\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014\u001a/\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u001a7\u0010 \u001a\u00020\u001f2\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u00192\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008 \u0010!\u001a\u0017\u0010\"\u001a\u00020\u001f2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\"\u0010#\u001a\u0017\u0010%\u001a\u00020$2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008%\u0010&\u00a8\u0006\'"
    }
    d2 = {
        "Lk81/a;",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "",
        "currencySymbol",
        "LHX0/e;",
        "resourceManager",
        "",
        "Lkb1/x;",
        "h",
        "(Lk81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;",
        "Lo81/b;",
        "f",
        "(Lo81/b;)Ljava/util/List;",
        "Li81/a;",
        "g",
        "(Li81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;)Ljava/util/List;",
        "Ln81/d;",
        "stage",
        "e",
        "(Ln81/d;)Ljava/lang/String;",
        "Lk81/b;",
        "item",
        "d",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lk81/b;Ljava/lang/String;LHX0/e;)Ljava/lang/String;",
        "",
        "index",
        "currentStage",
        "Ljava/util/Date;",
        "startAt",
        "endAt",
        "Lmb1/a;",
        "c",
        "(IILorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/util/Date;Ljava/util/Date;)Lmb1/a;",
        "b",
        "(I)Lmb1/a;",
        "LL11/c;",
        "a",
        "(I)LL11/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(I)LL11/c;
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    if-eq p0, v0, :cond_2

    .line 3
    .line 4
    const/4 v0, 0x2

    .line 5
    if-eq p0, v0, :cond_1

    .line 6
    .line 7
    const/4 v0, 0x3

    .line 8
    if-eq p0, v0, :cond_0

    .line 9
    .line 10
    const-string p0, "Number_Other.webp"

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const-string p0, "Number_Bronze.webp"

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_1
    const-string p0, "Number_Silver.webp"

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_2
    const-string p0, "Number_Gold.webp"

    .line 20
    .line 21
    :goto_0
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 22
    .line 23
    new-instance v1, Ljava/lang/StringBuilder;

    .line 24
    .line 25
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 26
    .line 27
    .line 28
    const-string v2, "/static/img/android/casino/alt_design/aggregator_tournament_prize/icon_s_icon_l_number/"

    .line 29
    .line 30
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {v0, p0}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    return-object p0
.end method

.method public static final b(I)Lmb1/a;
    .locals 1

    .line 1
    new-instance v0, Lmb1/a$b;

    .line 2
    .line 3
    invoke-static {p0}, Ljb1/D;->a(I)LL11/c;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-direct {v0, p0}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public static final c(IILorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/util/Date;Ljava/util/Date;)Lmb1/a;
    .locals 9

    .line 1
    add-int/lit8 v0, p0, 0x1

    .line 2
    .line 3
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Ljava/util/Date;

    .line 8
    .line 9
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    .line 13
    .line 14
    .line 15
    move-result-wide v2

    .line 16
    sget-object v4, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 17
    .line 18
    invoke-virtual {p4}, Ljava/util/Date;->getTime()J

    .line 19
    .line 20
    .line 21
    move-result-wide v5

    .line 22
    invoke-virtual {p3}, Ljava/util/Date;->getTime()J

    .line 23
    .line 24
    .line 25
    move-result-wide v7

    .line 26
    sub-long/2addr v5, v7

    .line 27
    invoke-virtual {v4, v5, v6}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 28
    .line 29
    .line 30
    move-result-wide v5

    .line 31
    invoke-virtual {p3}, Ljava/util/Date;->getTime()J

    .line 32
    .line 33
    .line 34
    move-result-wide v7

    .line 35
    sub-long/2addr v2, v7

    .line 36
    invoke-virtual {v4, v2, v3}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 37
    .line 38
    .line 39
    move-result-wide v2

    .line 40
    long-to-double v2, v2

    .line 41
    long-to-double v4, v5

    .line 42
    div-double/2addr v2, v4

    .line 43
    const/16 v4, 0x64

    .line 44
    .line 45
    int-to-double v5, v4

    .line 46
    mul-double v2, v2, v5

    .line 47
    .line 48
    const-wide/16 v5, 0x0

    .line 49
    .line 50
    cmpl-double v7, v2, v5

    .line 51
    .line 52
    if-lez v7, :cond_0

    .line 53
    .line 54
    const-wide/high16 v5, 0x3ff0000000000000L    # 1.0

    .line 55
    .line 56
    cmpg-double v7, v2, v5

    .line 57
    .line 58
    if-gez v7, :cond_0

    .line 59
    .line 60
    const/4 v2, 0x1

    .line 61
    goto :goto_0

    .line 62
    :cond_0
    double-to-int v2, v2

    .line 63
    :goto_0
    sget-object v3, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 64
    .line 65
    if-ne p2, v3, :cond_3

    .line 66
    .line 67
    if-ge p0, p1, :cond_1

    .line 68
    .line 69
    new-instance p0, Lmb1/a$d;

    .line 70
    .line 71
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->COMPLETE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 72
    .line 73
    invoke-direct {p0, v0, p1}, Lmb1/a$d;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;)V

    .line 74
    .line 75
    .line 76
    return-object p0

    .line 77
    :cond_1
    if-ne p0, p1, :cond_2

    .line 78
    .line 79
    invoke-virtual {v1, p3}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 80
    .line 81
    .line 82
    move-result p0

    .line 83
    if-nez p0, :cond_2

    .line 84
    .line 85
    new-instance p0, Lmb1/a$c;

    .line 86
    .line 87
    invoke-direct {p0, v0, v2, v4}, Lmb1/a$c;-><init>(Ljava/lang/String;II)V

    .line 88
    .line 89
    .line 90
    return-object p0

    .line 91
    :cond_2
    new-instance p0, Lmb1/a$d;

    .line 92
    .line 93
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->PREPARE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 94
    .line 95
    invoke-direct {p0, v0, p1}, Lmb1/a$d;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;)V

    .line 96
    .line 97
    .line 98
    return-object p0

    .line 99
    :cond_3
    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    .line 100
    .line 101
    .line 102
    move-result-wide p0

    .line 103
    invoke-virtual {p3}, Ljava/util/Date;->getTime()J

    .line 104
    .line 105
    .line 106
    move-result-wide p2

    .line 107
    cmp-long v3, p0, p2

    .line 108
    .line 109
    if-ltz v3, :cond_4

    .line 110
    .line 111
    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    .line 112
    .line 113
    .line 114
    move-result-wide p0

    .line 115
    invoke-virtual {p4}, Ljava/util/Date;->getTime()J

    .line 116
    .line 117
    .line 118
    move-result-wide p2

    .line 119
    cmp-long v3, p0, p2

    .line 120
    .line 121
    if-gez v3, :cond_4

    .line 122
    .line 123
    new-instance p0, Lmb1/a$c;

    .line 124
    .line 125
    invoke-direct {p0, v0, v2, v4}, Lmb1/a$c;-><init>(Ljava/lang/String;II)V

    .line 126
    .line 127
    .line 128
    return-object p0

    .line 129
    :cond_4
    invoke-virtual {v1}, Ljava/util/Date;->getTime()J

    .line 130
    .line 131
    .line 132
    move-result-wide p0

    .line 133
    invoke-virtual {p4}, Ljava/util/Date;->getTime()J

    .line 134
    .line 135
    .line 136
    move-result-wide p2

    .line 137
    cmp-long p4, p0, p2

    .line 138
    .line 139
    if-ltz p4, :cond_5

    .line 140
    .line 141
    new-instance p0, Lmb1/a$d;

    .line 142
    .line 143
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->COMPLETE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 144
    .line 145
    invoke-direct {p0, v0, p1}, Lmb1/a$d;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;)V

    .line 146
    .line 147
    .line 148
    return-object p0

    .line 149
    :cond_5
    new-instance p0, Lmb1/a$d;

    .line 150
    .line 151
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->PREPARE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 152
    .line 153
    invoke-direct {p0, v0, p1}, Lmb1/a$d;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;)V

    .line 154
    .line 155
    .line 156
    return-object p0
.end method

.method public static final d(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lk81/b;Ljava/lang/String;LHX0/e;)Ljava/lang/String;
    .locals 4

    .line 1
    sget v0, Lpb/k;->fs:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    new-array v1, v1, [Ljava/lang/Object;

    .line 5
    .line 6
    invoke-interface {p3, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 7
    .line 8
    .line 9
    move-result-object p3

    .line 10
    invoke-virtual {p1}, Lk81/b;->b()I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-lez v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {p1}, Lk81/b;->b()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    new-instance v1, Ljava/lang/StringBuilder;

    .line 21
    .line 22
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    invoke-virtual {v1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p3

    .line 35
    goto :goto_0

    .line 36
    :cond_0
    const-string p3, ""

    .line 37
    .line 38
    :goto_0
    sget-object v0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 39
    .line 40
    const-string v1, " "

    .line 41
    .line 42
    if-eq p0, v0, :cond_1

    .line 43
    .line 44
    invoke-virtual {p1}, Lk81/b;->f()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    new-instance p1, Ljava/lang/StringBuilder;

    .line 49
    .line 50
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 54
    .line 55
    .line 56
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 60
    .line 61
    .line 62
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object p0

    .line 66
    return-object p0

    .line 67
    :cond_1
    sget-object p0, Ll8/j;->a:Ll8/j;

    .line 68
    .line 69
    invoke-virtual {p1}, Lk81/b;->a()D

    .line 70
    .line 71
    .line 72
    move-result-wide v2

    .line 73
    sget-object p1, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 74
    .line 75
    invoke-virtual {p0, v2, v3, p1}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    invoke-interface {p3}, Ljava/lang/CharSequence;->length()I

    .line 80
    .line 81
    .line 82
    move-result p1

    .line 83
    if-lez p1, :cond_2

    .line 84
    .line 85
    new-instance p1, Ljava/lang/StringBuilder;

    .line 86
    .line 87
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 97
    .line 98
    .line 99
    const-string p0, " \u00b7 "

    .line 100
    .line 101
    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 102
    .line 103
    .line 104
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 105
    .line 106
    .line 107
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 108
    .line 109
    .line 110
    move-result-object p0

    .line 111
    return-object p0

    .line 112
    :cond_2
    new-instance p1, Ljava/lang/StringBuilder;

    .line 113
    .line 114
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 115
    .line 116
    .line 117
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 118
    .line 119
    .line 120
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 121
    .line 122
    .line 123
    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 124
    .line 125
    .line 126
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 127
    .line 128
    .line 129
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 130
    .line 131
    .line 132
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object p0

    .line 136
    return-object p0
.end method

.method public static final e(Ln81/d;)Ljava/lang/String;
    .locals 7

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Ln81/d;->d()Ljava/util/Date;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x4

    .line 8
    const/4 v5, 0x0

    .line 9
    const-string v2, "d MMMM"

    .line 10
    .line 11
    const/4 v3, 0x0

    .line 12
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v6

    .line 16
    invoke-virtual {p0}, Ln81/d;->c()Ljava/util/Date;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    const-string v2, "d MMMM"

    .line 21
    .line 22
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    new-instance v0, Ljava/lang/StringBuilder;

    .line 27
    .line 28
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    const-string v1, " - "

    .line 35
    .line 36
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 37
    .line 38
    .line 39
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    return-object p0
.end method

.method public static final f(Lo81/b;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lo81/b;",
            ")",
            "Ljava/util/List<",
            "Lkb1/x;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lo81/b;->a()Lk81/a;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    if-eqz p0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lk81/a;->b()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 p0, 0x0

    .line 15
    :goto_0
    const-string v0, ""

    .line 16
    .line 17
    if-nez p0, :cond_1

    .line 18
    .line 19
    move-object p0, v0

    .line 20
    :cond_1
    invoke-interface {p0}, Ljava/lang/CharSequence;->length()I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-lez v1, :cond_2

    .line 25
    .line 26
    new-instance v1, Lkb1/x$c;

    .line 27
    .line 28
    new-instance v2, LZ21/a;

    .line 29
    .line 30
    sget-object v3, Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;->STATIC:Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;

    .line 31
    .line 32
    invoke-direct {v2, v3, v0, p0}, LZ21/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;Ljava/lang/String;Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-direct {v1, v2}, Lkb1/x$c;-><init>(LZ21/b;)V

    .line 36
    .line 37
    .line 38
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    return-object p0

    .line 43
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    return-object p0
.end method

.method public static final g(Li81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;)Ljava/util/List;
    .locals 16
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            ")",
            "Ljava/util/List<",
            "Lkb1/x;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual/range {p0 .. p0}, Li81/a;->h()Ln81/b;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Ln81/b;->c()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    new-instance v2, Ljb1/D$a;

    .line 15
    .line 16
    invoke-direct {v2}, Ljb1/D$a;-><init>()V

    .line 17
    .line 18
    .line 19
    invoke-static {v1, v2}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const/4 v2, 0x0

    .line 28
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-eqz v3, :cond_3

    .line 33
    .line 34
    add-int/lit8 v3, v2, 0x1

    .line 35
    .line 36
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v4

    .line 40
    check-cast v4, Ln81/d;

    .line 41
    .line 42
    invoke-virtual/range {p0 .. p0}, Li81/a;->i()Lo81/a;

    .line 43
    .line 44
    .line 45
    move-result-object v5

    .line 46
    invoke-virtual {v5}, Lo81/a;->a()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v5

    .line 50
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object v5

    .line 54
    :cond_0
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 55
    .line 56
    .line 57
    move-result v6

    .line 58
    if-eqz v6, :cond_1

    .line 59
    .line 60
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v6

    .line 64
    move-object v7, v6

    .line 65
    check-cast v7, Lo81/b;

    .line 66
    .line 67
    invoke-virtual {v7}, Lo81/b;->c()J

    .line 68
    .line 69
    .line 70
    move-result-wide v7

    .line 71
    invoke-virtual {v4}, Ln81/d;->b()J

    .line 72
    .line 73
    .line 74
    move-result-wide v9

    .line 75
    cmp-long v11, v7, v9

    .line 76
    .line 77
    if-nez v11, :cond_0

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_1
    const/4 v6, 0x0

    .line 81
    :goto_1
    check-cast v6, Lo81/b;

    .line 82
    .line 83
    if-nez v6, :cond_2

    .line 84
    .line 85
    move-object/from16 v15, p1

    .line 86
    .line 87
    goto :goto_2

    .line 88
    :cond_2
    new-instance v5, Lkb1/x$b;

    .line 89
    .line 90
    new-instance v7, Lmb1/b;

    .line 91
    .line 92
    invoke-virtual {v6}, Lo81/b;->c()J

    .line 93
    .line 94
    .line 95
    move-result-wide v8

    .line 96
    invoke-static {v4}, Ljb1/D;->e(Ln81/d;)Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v10

    .line 100
    invoke-virtual {v6}, Lo81/b;->a()Lk81/a;

    .line 101
    .line 102
    .line 103
    move-result-object v11

    .line 104
    invoke-virtual {v11}, Lk81/a;->e()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v11

    .line 108
    invoke-virtual {v6}, Lo81/b;->a()Lk81/a;

    .line 109
    .line 110
    .line 111
    move-result-object v6

    .line 112
    invoke-virtual {v6}, Lk81/a;->d()Ljava/util/List;

    .line 113
    .line 114
    .line 115
    move-result-object v6

    .line 116
    invoke-interface {v6}, Ljava/util/Collection;->isEmpty()Z

    .line 117
    .line 118
    .line 119
    move-result v6

    .line 120
    xor-int/lit8 v13, v6, 0x1

    .line 121
    .line 122
    invoke-virtual/range {p0 .. p0}, Li81/a;->l()J

    .line 123
    .line 124
    .line 125
    move-result-wide v14

    .line 126
    long-to-int v6, v14

    .line 127
    invoke-virtual {v4}, Ln81/d;->d()Ljava/util/Date;

    .line 128
    .line 129
    .line 130
    move-result-object v12

    .line 131
    invoke-virtual {v4}, Ln81/d;->c()Ljava/util/Date;

    .line 132
    .line 133
    .line 134
    move-result-object v4

    .line 135
    move-object/from16 v15, p1

    .line 136
    .line 137
    invoke-static {v2, v6, v15, v12, v4}, Ljb1/D;->c(IILorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/util/Date;Ljava/util/Date;)Lmb1/a;

    .line 138
    .line 139
    .line 140
    move-result-object v14

    .line 141
    const/4 v12, 0x0

    .line 142
    invoke-direct/range {v7 .. v14}, Lmb1/b;-><init>(JLjava/lang/String;Ljava/lang/String;ZZLmb1/a;)V

    .line 143
    .line 144
    .line 145
    const-string v2, ""

    .line 146
    .line 147
    invoke-direct {v5, v2, v7}, Lkb1/x$b;-><init>(Ljava/lang/String;Lmb1/c;)V

    .line 148
    .line 149
    .line 150
    invoke-interface {v0, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 151
    .line 152
    .line 153
    :goto_2
    move v2, v3

    .line 154
    goto :goto_0

    .line 155
    :cond_3
    return-object v0
.end method

.method public static final h(Lk81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;
    .locals 21
    .param p0    # Lk81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk81/a;",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Ljava/lang/String;",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "Lkb1/x;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    move-object/from16 v2, p3

    .line 6
    .line 7
    invoke-virtual/range {p0 .. p0}, Lk81/a;->a()Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v4, Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;->PLACES_COUNT:Lorg/xplatform/aggregator/api/model/tournaments/prize/PrizePlaceType;

    .line 12
    .line 13
    const-string v7, ""

    .line 14
    .line 15
    const/16 v8, 0xa

    .line 16
    .line 17
    const-string v9, ": "

    .line 18
    .line 19
    const/4 v10, 0x0

    .line 20
    if-ne v3, v4, :cond_4

    .line 21
    .line 22
    sget v3, Lpb/k;->player_info_position:I

    .line 23
    .line 24
    new-array v4, v10, [Ljava/lang/Object;

    .line 25
    .line 26
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    invoke-virtual/range {p0 .. p0}, Lk81/a;->d()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v4

    .line 34
    new-instance v11, Ljava/util/ArrayList;

    .line 35
    .line 36
    invoke-static {v4, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 37
    .line 38
    .line 39
    move-result v8

    .line 40
    invoke-direct {v11, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 41
    .line 42
    .line 43
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 44
    .line 45
    .line 46
    move-result-object v4

    .line 47
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 48
    .line 49
    .line 50
    move-result v8

    .line 51
    if-eqz v8, :cond_3

    .line 52
    .line 53
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v8

    .line 57
    add-int/lit8 v12, v10, 0x1

    .line 58
    .line 59
    if-gez v10, :cond_0

    .line 60
    .line 61
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 62
    .line 63
    .line 64
    :cond_0
    check-cast v8, Lk81/b;

    .line 65
    .line 66
    invoke-virtual {v8}, Lk81/b;->d()I

    .line 67
    .line 68
    .line 69
    move-result v10

    .line 70
    invoke-virtual {v8}, Lk81/b;->e()I

    .line 71
    .line 72
    .line 73
    move-result v13

    .line 74
    if-ne v10, v13, :cond_1

    .line 75
    .line 76
    new-instance v13, Ljava/lang/StringBuilder;

    .line 77
    .line 78
    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    .line 79
    .line 80
    .line 81
    invoke-virtual {v13, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 85
    .line 86
    .line 87
    invoke-virtual {v13, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 88
    .line 89
    .line 90
    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v10

    .line 94
    :goto_1
    move-object/from16 v16, v10

    .line 95
    .line 96
    goto :goto_2

    .line 97
    :cond_1
    new-instance v14, Ljava/lang/StringBuilder;

    .line 98
    .line 99
    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    .line 100
    .line 101
    .line 102
    invoke-virtual {v14, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {v14, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 106
    .line 107
    .line 108
    invoke-virtual {v14, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 109
    .line 110
    .line 111
    const-string v10, "-"

    .line 112
    .line 113
    invoke-virtual {v14, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    invoke-virtual {v14, v13}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 117
    .line 118
    .line 119
    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 120
    .line 121
    .line 122
    move-result-object v10

    .line 123
    goto :goto_1

    .line 124
    :goto_2
    new-instance v10, Lkb1/x$b;

    .line 125
    .line 126
    new-instance v13, Lmb1/b;

    .line 127
    .line 128
    invoke-virtual {v8}, Lk81/b;->c()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v14

    .line 132
    invoke-static {v14}, Lkotlin/text/StringsKt;->y(Ljava/lang/String;)Ljava/lang/Long;

    .line 133
    .line 134
    .line 135
    move-result-object v14

    .line 136
    if-eqz v14, :cond_2

    .line 137
    .line 138
    invoke-virtual {v14}, Ljava/lang/Long;->longValue()J

    .line 139
    .line 140
    .line 141
    move-result-wide v14

    .line 142
    goto :goto_3

    .line 143
    :cond_2
    const-wide/16 v14, 0x0

    .line 144
    .line 145
    :goto_3
    invoke-static {v0, v8, v1, v2}, Ljb1/D;->d(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lk81/b;Ljava/lang/String;LHX0/e;)Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object v17

    .line 149
    const/16 v19, 0x0

    .line 150
    .line 151
    invoke-static {v12}, Ljb1/D;->b(I)Lmb1/a;

    .line 152
    .line 153
    .line 154
    move-result-object v20

    .line 155
    const/16 v18, 0x0

    .line 156
    .line 157
    invoke-direct/range {v13 .. v20}, Lmb1/b;-><init>(JLjava/lang/String;Ljava/lang/String;ZZLmb1/a;)V

    .line 158
    .line 159
    .line 160
    invoke-direct {v10, v7, v13}, Lkb1/x$b;-><init>(Ljava/lang/String;Lmb1/c;)V

    .line 161
    .line 162
    .line 163
    invoke-interface {v11, v10}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 164
    .line 165
    .line 166
    move v10, v12

    .line 167
    goto :goto_0

    .line 168
    :cond_3
    return-object v11

    .line 169
    :cond_4
    sget v3, Lpb/k;->stocks_prizes:I

    .line 170
    .line 171
    new-array v4, v10, [Ljava/lang/Object;

    .line 172
    .line 173
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v3

    .line 177
    invoke-virtual/range {p0 .. p0}, Lk81/a;->d()Ljava/util/List;

    .line 178
    .line 179
    .line 180
    move-result-object v4

    .line 181
    new-instance v11, Ljava/util/ArrayList;

    .line 182
    .line 183
    invoke-static {v4, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 184
    .line 185
    .line 186
    move-result v8

    .line 187
    invoke-direct {v11, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 188
    .line 189
    .line 190
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 191
    .line 192
    .line 193
    move-result-object v4

    .line 194
    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 195
    .line 196
    .line 197
    move-result v8

    .line 198
    if-eqz v8, :cond_7

    .line 199
    .line 200
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object v8

    .line 204
    add-int/lit8 v12, v10, 0x1

    .line 205
    .line 206
    if-gez v10, :cond_5

    .line 207
    .line 208
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 209
    .line 210
    .line 211
    :cond_5
    check-cast v8, Lk81/b;

    .line 212
    .line 213
    new-instance v10, Lkb1/x$b;

    .line 214
    .line 215
    new-instance v13, Lmb1/b;

    .line 216
    .line 217
    invoke-virtual {v8}, Lk81/b;->c()Ljava/lang/String;

    .line 218
    .line 219
    .line 220
    move-result-object v14

    .line 221
    invoke-static {v14}, Lkotlin/text/StringsKt;->y(Ljava/lang/String;)Ljava/lang/Long;

    .line 222
    .line 223
    .line 224
    move-result-object v14

    .line 225
    if-eqz v14, :cond_6

    .line 226
    .line 227
    invoke-virtual {v14}, Ljava/lang/Long;->longValue()J

    .line 228
    .line 229
    .line 230
    move-result-wide v14

    .line 231
    goto :goto_5

    .line 232
    :cond_6
    const-wide/16 v14, 0x0

    .line 233
    .line 234
    :goto_5
    invoke-virtual {v8}, Lk81/b;->g()Ljava/lang/String;

    .line 235
    .line 236
    .line 237
    move-result-object v5

    .line 238
    new-instance v6, Ljava/lang/StringBuilder;

    .line 239
    .line 240
    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    .line 241
    .line 242
    .line 243
    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 244
    .line 245
    .line 246
    invoke-virtual {v6, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 247
    .line 248
    .line 249
    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 250
    .line 251
    .line 252
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 253
    .line 254
    .line 255
    move-result-object v16

    .line 256
    invoke-static {v0, v8, v1, v2}, Ljb1/D;->d(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lk81/b;Ljava/lang/String;LHX0/e;)Ljava/lang/String;

    .line 257
    .line 258
    .line 259
    move-result-object v17

    .line 260
    const/16 v19, 0x0

    .line 261
    .line 262
    invoke-static {v12}, Ljb1/D;->b(I)Lmb1/a;

    .line 263
    .line 264
    .line 265
    move-result-object v20

    .line 266
    const/16 v18, 0x0

    .line 267
    .line 268
    invoke-direct/range {v13 .. v20}, Lmb1/b;-><init>(JLjava/lang/String;Ljava/lang/String;ZZLmb1/a;)V

    .line 269
    .line 270
    .line 271
    invoke-direct {v10, v7, v13}, Lkb1/x$b;-><init>(Ljava/lang/String;Lmb1/c;)V

    .line 272
    .line 273
    .line 274
    invoke-interface {v11, v10}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 275
    .line 276
    .line 277
    move v10, v12

    .line 278
    goto :goto_4

    .line 279
    :cond_7
    return-object v11
.end method
