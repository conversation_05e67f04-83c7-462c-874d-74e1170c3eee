.class public final LHN0/d$a;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LHN0/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0008\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001d\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LHN0/d$a;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LDN0/x;",
        "binding",
        "<init>",
        "(LDN0/x;)V",
        "LaZ0/a;",
        "item",
        "",
        "width",
        "",
        "d",
        "(LaZ0/a;I)V",
        "e",
        "LDN0/x;",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LDN0/x;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDN0/x;)V
    .locals 1
    .param p1    # LDN0/x;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LDN0/x;->b()Landroid/widget/FrameLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LHN0/d$a;->e:LDN0/x;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final d(LaZ0/a;I)V
    .locals 8
    .param p1    # LaZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LaZ0/a$d;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 9
    .line 10
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 11
    .line 12
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 16
    .line 17
    iget-object v0, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 18
    .line 19
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 23
    .line 24
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 25
    .line 26
    check-cast p1, LaZ0/a$d;

    .line 27
    .line 28
    invoke-virtual {p1}, LaZ0/a$d;->a()I

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(I)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    instance-of v0, p1, LaZ0/a$c;

    .line 37
    .line 38
    if-eqz v0, :cond_1

    .line 39
    .line 40
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 41
    .line 42
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 48
    .line 49
    iget-object v0, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 50
    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 55
    .line 56
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 57
    .line 58
    check-cast p1, LaZ0/a$c;

    .line 59
    .line 60
    invoke-virtual {p1}, LaZ0/a$c;->a()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 65
    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    instance-of v0, p1, LaZ0/a$a;

    .line 69
    .line 70
    if-eqz v0, :cond_2

    .line 71
    .line 72
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 73
    .line 74
    iget-object v0, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 75
    .line 76
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 77
    .line 78
    .line 79
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 80
    .line 81
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 82
    .line 83
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 84
    .line 85
    .line 86
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 87
    .line 88
    iget-object v0, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 89
    .line 90
    check-cast p1, LaZ0/a$a;

    .line 91
    .line 92
    invoke-virtual {p1}, LaZ0/a$a;->a()I

    .line 93
    .line 94
    .line 95
    move-result p1

    .line 96
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 97
    .line 98
    .line 99
    goto :goto_0

    .line 100
    :cond_2
    instance-of v0, p1, LaZ0/a$b;

    .line 101
    .line 102
    if-eqz v0, :cond_5

    .line 103
    .line 104
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 105
    .line 106
    iget-object v0, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 107
    .line 108
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 109
    .line 110
    .line 111
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 112
    .line 113
    iget-object v0, v0, LDN0/x;->c:Landroid/widget/TextView;

    .line 114
    .line 115
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 116
    .line 117
    .line 118
    sget-object v2, LCX0/l;->a:LCX0/l;

    .line 119
    .line 120
    iget-object v0, p0, LHN0/d$a;->e:LDN0/x;

    .line 121
    .line 122
    iget-object v3, v0, LDN0/x;->b:Landroid/widget/ImageView;

    .line 123
    .line 124
    sget-object v4, Lorg/xbet/ui_common/utils/image/ImageCropType;->CIRCLE_IMAGE:Lorg/xbet/ui_common/utils/image/ImageCropType;

    .line 125
    .line 126
    check-cast p1, LaZ0/a$b;

    .line 127
    .line 128
    invoke-virtual {p1}, LaZ0/a$b;->a()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v6

    .line 132
    sget v7, Lpb/g;->ic_profile:I

    .line 133
    .line 134
    const/4 v5, 0x1

    .line 135
    invoke-virtual/range {v2 .. v7}, LCX0/l;->E(Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;I)V

    .line 136
    .line 137
    .line 138
    :goto_0
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 139
    .line 140
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    iget p1, p1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 145
    .line 146
    if-eq p1, p2, :cond_4

    .line 147
    .line 148
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 149
    .line 150
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 151
    .line 152
    .line 153
    move-result-object v0

    .line 154
    if-eqz v0, :cond_3

    .line 155
    .line 156
    iput p2, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 157
    .line 158
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 159
    .line 160
    .line 161
    return-void

    .line 162
    :cond_3
    new-instance p1, Ljava/lang/NullPointerException;

    .line 163
    .line 164
    const-string p2, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 165
    .line 166
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 167
    .line 168
    .line 169
    throw p1

    .line 170
    :cond_4
    return-void

    .line 171
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 172
    .line 173
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 174
    .line 175
    .line 176
    throw p1
.end method
