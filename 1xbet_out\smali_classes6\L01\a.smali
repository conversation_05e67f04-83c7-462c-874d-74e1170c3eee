.class public interface abstract LL01/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008%\u0008f\u0018\u00002\u00020\u0001J\u0019\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0019\u0010\t\u001a\u00020\u00042\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u0007H&\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u000f\u001a\u00020\u000eH&\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0012\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u0014\u0010\u0013J\u0019\u0010\u0017\u001a\u00020\u00042\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H&\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0019\u0010\u0019\u001a\u00020\u00042\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H&\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\u000f\u0010\u001a\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\u001a\u0010\u0013J\u0017\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\u001bH&\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010\u001f\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\u001bH&\u00a2\u0006\u0004\u0008\u001f\u0010\u001eJ#\u0010#\u001a\u00020\u00042\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00040 H&\u00a2\u0006\u0004\u0008#\u0010$J#\u0010%\u001a\u00020\u00042\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00040 H&\u00a2\u0006\u0004\u0008%\u0010$J\'\u0010*\u001a\u00020\u00042\u0016\u0010)\u001a\u0012\u0012\u0004\u0012\u00020\'0&j\u0008\u0012\u0004\u0012\u00020\'`(H&\u00a2\u0006\u0004\u0008*\u0010+J7\u0010.\u001a\u00020\u00042\u0016\u0010)\u001a\u0012\u0012\u0004\u0012\u00020\'0&j\u0008\u0012\u0004\u0012\u00020\'`(2\u000e\u0008\u0002\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u00040,H&\u00a2\u0006\u0004\u0008.\u0010/J\u0019\u00102\u001a\u00020\u00042\u0008\u00101\u001a\u0004\u0018\u000100H&\u00a2\u0006\u0004\u00082\u00103J\u0017\u00105\u001a\u00020\u00042\u0006\u00104\u001a\u00020\u000bH&\u00a2\u0006\u0004\u00085\u00106J\u000f\u00107\u001a\u00020\u0004H&\u00a2\u0006\u0004\u00087\u0010\u0013J\u000f\u00108\u001a\u00020\u0004H&\u00a2\u0006\u0004\u00088\u0010\u0013J\u0019\u0010;\u001a\u0004\u0018\u00010:2\u0006\u00109\u001a\u00020!H&\u00a2\u0006\u0004\u0008;\u0010<J\u0017\u0010=\u001a\u00020\u00042\u0006\u00109\u001a\u00020!H&\u00a2\u0006\u0004\u0008=\u0010>J\u0017\u0010?\u001a\u00020\u00042\u0006\u00109\u001a\u00020!H&\u00a2\u0006\u0004\u0008?\u0010>J\u0019\u0010A\u001a\u00020\u00042\u0008\u0010@\u001a\u0004\u0018\u00010\u0015H&\u00a2\u0006\u0004\u0008A\u0010\u0018J\u000f\u0010B\u001a\u00020\u0015H&\u00a2\u0006\u0004\u0008B\u0010CJ\u0017\u0010E\u001a\u00020\u00042\u0006\u0010D\u001a\u00020\u0015H&\u00a2\u0006\u0004\u0008E\u0010\u0018J\u000f\u0010F\u001a\u00020\u0015H&\u00a2\u0006\u0004\u0008F\u0010CJ\u0017\u0010H\u001a\u00020\u00042\u0006\u0010G\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008H\u00106J\u0017\u0010J\u001a\u00020\u00042\u0006\u0010I\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008J\u00106J\u0017\u0010L\u001a\u00020\u00042\u0006\u0010K\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008L\u00106J\u0019\u0010M\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008M\u0010\u0006J\u0019\u0010N\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008N\u0010\u0006J\u0019\u0010O\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008O\u0010\u0006J\u001d\u0010Q\u001a\u00020\u00042\u000c\u0010P\u001a\u0008\u0012\u0004\u0012\u00020\u00040,H&\u00a2\u0006\u0004\u0008Q\u0010RJ\u000f\u0010S\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008S\u0010\u0013J\u000f\u0010T\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008T\u0010\u0013J\'\u0010V\u001a\u00020\u00042\u0008\u0008\u0002\u0010U\u001a\u00020\u000b2\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u00040,H&\u00a2\u0006\u0004\u0008V\u0010WJ\u001d\u0010X\u001a\u00020\u00042\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u00040,H&\u00a2\u0006\u0004\u0008X\u0010RJ\u001d\u0010Y\u001a\u00020\u00042\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u00040,H&\u00a2\u0006\u0004\u0008Y\u0010RJ\u000f\u0010Z\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008Z\u0010\u0013J\u0017\u0010[\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u000200H&\u00a2\u0006\u0004\u0008[\u00103J\u000f\u0010\\\u001a\u00020\u0004H&\u00a2\u0006\u0004\u0008\\\u0010\u0013J\u0017\u0010^\u001a\u00020\u00042\u0006\u0010]\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008^\u00106\u00a8\u0006_"
    }
    d2 = {
        "LL01/a;",
        "",
        "",
        "color",
        "",
        "setNavigationBarBackground",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setNavigationBarDrawableBackground",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "c",
        "()Z",
        "Lorg/xbet/uikit/components/toolbar/base/DSSearchFieldState;",
        "searchFieldState",
        "e",
        "(Lorg/xbet/uikit/components/toolbar/base/DSSearchFieldState;)V",
        "l",
        "()V",
        "b",
        "",
        "hint",
        "setSearchFieldHint",
        "(Ljava/lang/CharSequence;)V",
        "setSearchFieldText",
        "j",
        "Landroid/text/TextWatcher;",
        "textWatcher",
        "r",
        "(Landroid/text/TextWatcher;)V",
        "q",
        "Lkotlin/Function1;",
        "",
        "listener",
        "h",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setOnSearchViewExpandedStateChangedListener",
        "Ljava/util/ArrayList;",
        "LM01/c;",
        "Lkotlin/collections/ArrayList;",
        "buttons",
        "setNavigationBarButtons",
        "(Ljava/util/ArrayList;)V",
        "Lkotlin/Function0;",
        "onSearchIconClickListener",
        "setNavigationBarButtonsWithExternalClick",
        "(Ljava/util/ArrayList;Lkotlin/jvm/functions/Function0;)V",
        "Landroid/content/res/ColorStateList;",
        "colorStateList",
        "setNavigationBarButtonsColorStateList",
        "(Landroid/content/res/ColorStateList;)V",
        "enable",
        "d",
        "(Z)V",
        "g",
        "p",
        "iconId",
        "Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;",
        "m",
        "(Ljava/lang/String;)Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;",
        "s",
        "(Ljava/lang/String;)V",
        "i",
        "title",
        "setTitle",
        "getTitle",
        "()Ljava/lang/CharSequence;",
        "subTitle",
        "setSubTitle",
        "getSubTitle",
        "showTitleIcon",
        "setTitleIconVisible",
        "showTitle",
        "setTitleVisible",
        "showSubtitle",
        "setSubtitleVisible",
        "setTitleColor",
        "setSubTitleColor",
        "setTitleChevronColor",
        "debounceClickListener",
        "setOnTitlesClickListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "f",
        "n",
        "forceExitOnSearchVisible",
        "setOnBackIconClickListener",
        "(ZLkotlin/jvm/functions/Function0;)V",
        "setOnHideSearchBarClickListener",
        "k",
        "setBackIconBackground",
        "setBackIconColor",
        "o",
        "show",
        "a",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Z)V
.end method

.method public abstract b()V
.end method

.method public abstract c()Z
.end method

.method public abstract d(Z)V
.end method

.method public abstract e(Lorg/xbet/uikit/components/toolbar/base/DSSearchFieldState;)V
    .param p1    # Lorg/xbet/uikit/components/toolbar/base/DSSearchFieldState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract f()V
.end method

.method public abstract g()V
.end method

.method public abstract getSubTitle()Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract getTitle()Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract h(Lkotlin/jvm/functions/Function1;)V
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract i(Ljava/lang/String;)V
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract j()V
.end method

.method public abstract k(Lkotlin/jvm/functions/Function0;)V
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract l()V
.end method

.method public abstract m(Ljava/lang/String;)Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract n()V
.end method

.method public abstract o()V
.end method

.method public abstract p()V
.end method

.method public abstract q(Landroid/text/TextWatcher;)V
    .param p1    # Landroid/text/TextWatcher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract r(Landroid/text/TextWatcher;)V
    .param p1    # Landroid/text/TextWatcher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract s(Ljava/lang/String;)V
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract setBackIconBackground()V
.end method

.method public abstract setBackIconColor(Landroid/content/res/ColorStateList;)V
    .param p1    # Landroid/content/res/ColorStateList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract setNavigationBarBackground(I)V
.end method

.method public abstract setNavigationBarButtons(Ljava/util/ArrayList;)V
    .param p1    # Ljava/util/ArrayList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "LM01/c;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setNavigationBarButtonsColorStateList(Landroid/content/res/ColorStateList;)V
.end method

.method public abstract setNavigationBarButtonsWithExternalClick(Ljava/util/ArrayList;Lkotlin/jvm/functions/Function0;)V
    .param p1    # Ljava/util/ArrayList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/ArrayList<",
            "LM01/c;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setNavigationBarDrawableBackground(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract setOnBackIconClickListener(ZLkotlin/jvm/functions/Function0;)V
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setOnHideSearchBarClickListener(Lkotlin/jvm/functions/Function0;)V
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setOnSearchViewExpandedStateChangedListener(Lkotlin/jvm/functions/Function1;)V
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setOnTitlesClickListener(Lkotlin/jvm/functions/Function0;)V
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract setSearchFieldHint(Ljava/lang/CharSequence;)V
.end method

.method public abstract setSearchFieldText(Ljava/lang/CharSequence;)V
.end method

.method public abstract setSubTitle(Ljava/lang/CharSequence;)V
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract setSubTitleColor(I)V
.end method

.method public abstract setSubtitleVisible(Z)V
.end method

.method public abstract setTitle(Ljava/lang/CharSequence;)V
.end method

.method public abstract setTitleChevronColor(I)V
.end method

.method public abstract setTitleColor(I)V
.end method

.method public abstract setTitleIconVisible(Z)V
.end method

.method public abstract setTitleVisible(Z)V
.end method
