.class public final LGA0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001f\u0010\u0004\u001a\u0004\u0018\u00010\u0003*\u0004\u0018\u00010\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LJA0/d;",
        "",
        "sportId",
        "LWA0/h;",
        "a",
        "(LJA0/d;J)LWA0/h;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LJA0/d;J)LWA0/h;
    .locals 14

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    const/4 v2, 0x0

    .line 4
    if-eqz p0, :cond_1a

    .line 5
    .line 6
    const-wide/16 v3, 0x42

    .line 7
    .line 8
    const/16 v5, 0xa

    .line 9
    .line 10
    const-string v6, "0"

    .line 11
    .line 12
    cmp-long v7, p1, v3

    .line 13
    .line 14
    if-nez v7, :cond_10

    .line 15
    .line 16
    invoke-virtual {p0}, LJA0/d;->b()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v8

    .line 20
    if-eqz v8, :cond_0

    .line 21
    .line 22
    new-array v9, v1, [C

    .line 23
    .line 24
    const/16 v3, 0x2d

    .line 25
    .line 26
    aput-char v3, v9, v0

    .line 27
    .line 28
    const/4 v12, 0x6

    .line 29
    const/4 v13, 0x0

    .line 30
    const/4 v10, 0x0

    .line 31
    const/4 v11, 0x0

    .line 32
    invoke-static/range {v8 .. v13}, Lkotlin/text/StringsKt;->k1(Ljava/lang/CharSequence;[CZIILjava/lang/Object;)Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    move-object v3, v2

    .line 38
    :goto_0
    if-nez v3, :cond_1

    .line 39
    .line 40
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    :cond_1
    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    check-cast v0, Ljava/lang/String;

    .line 49
    .line 50
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    check-cast v3, Ljava/lang/String;

    .line 55
    .line 56
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    if-eqz v4, :cond_2

    .line 61
    .line 62
    invoke-virtual {v4}, LIA0/g$a;->b()LIA0/g$a$b;

    .line 63
    .line 64
    .line 65
    move-result-object v4

    .line 66
    if-eqz v4, :cond_2

    .line 67
    .line 68
    invoke-virtual {v4}, LIA0/g$a$b;->a()LIA0/g$a$b$a;

    .line 69
    .line 70
    .line 71
    move-result-object v4

    .line 72
    if-eqz v4, :cond_2

    .line 73
    .line 74
    invoke-virtual {v4}, LIA0/g$a$b$a;->a()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v4

    .line 78
    goto :goto_1

    .line 79
    :cond_2
    move-object v4, v2

    .line 80
    :goto_1
    invoke-static {v4, v0}, Lorg/xbet/ui_common/utils/ExtensionsKt;->z(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 85
    .line 86
    .line 87
    move-result-object v4

    .line 88
    if-eqz v4, :cond_3

    .line 89
    .line 90
    invoke-virtual {v4}, LIA0/g$a;->b()LIA0/g$a$b;

    .line 91
    .line 92
    .line 93
    move-result-object v4

    .line 94
    if-eqz v4, :cond_3

    .line 95
    .line 96
    invoke-virtual {v4}, LIA0/g$a$b;->a()LIA0/g$a$b$a;

    .line 97
    .line 98
    .line 99
    move-result-object v4

    .line 100
    if-eqz v4, :cond_3

    .line 101
    .line 102
    invoke-virtual {v4}, LIA0/g$a$b$a;->b()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v4

    .line 106
    goto :goto_2

    .line 107
    :cond_3
    move-object v4, v2

    .line 108
    :goto_2
    invoke-static {v4, v3}, Lorg/xbet/ui_common/utils/ExtensionsKt;->z(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v3

    .line 112
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 113
    .line 114
    .line 115
    move-result-object v4

    .line 116
    if-eqz v4, :cond_5

    .line 117
    .line 118
    invoke-virtual {v4}, LIA0/g$a;->a()LIA0/g$a$a;

    .line 119
    .line 120
    .line 121
    move-result-object v4

    .line 122
    if-eqz v4, :cond_5

    .line 123
    .line 124
    invoke-virtual {v4}, LIA0/g$a$a;->b()Ljava/lang/Integer;

    .line 125
    .line 126
    .line 127
    move-result-object v4

    .line 128
    if-nez v4, :cond_4

    .line 129
    .line 130
    goto :goto_3

    .line 131
    :cond_4
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 132
    .line 133
    .line 134
    move-result v4

    .line 135
    if-ne v4, v1, :cond_5

    .line 136
    .line 137
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    invoke-virtual {v1}, LIA0/g$a;->a()LIA0/g$a$a;

    .line 142
    .line 143
    .line 144
    move-result-object v1

    .line 145
    invoke-virtual {v1}, LIA0/g$a$a;->a()Ljava/lang/Double;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    if-eqz v1, :cond_5

    .line 150
    .line 151
    invoke-virtual {v1}, Ljava/lang/Double;->toString()Ljava/lang/String;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    goto :goto_4

    .line 156
    :cond_5
    :goto_3
    move-object v1, v2

    .line 157
    :goto_4
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 158
    .line 159
    .line 160
    move-result-object v4

    .line 161
    if-eqz v4, :cond_7

    .line 162
    .line 163
    invoke-virtual {v4}, LIA0/g$a;->a()LIA0/g$a$a;

    .line 164
    .line 165
    .line 166
    move-result-object v4

    .line 167
    if-eqz v4, :cond_7

    .line 168
    .line 169
    invoke-virtual {v4}, LIA0/g$a$a;->b()Ljava/lang/Integer;

    .line 170
    .line 171
    .line 172
    move-result-object v4

    .line 173
    if-nez v4, :cond_6

    .line 174
    .line 175
    goto :goto_5

    .line 176
    :cond_6
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 177
    .line 178
    .line 179
    move-result v4

    .line 180
    const/4 v7, 0x2

    .line 181
    if-ne v4, v7, :cond_7

    .line 182
    .line 183
    invoke-virtual {p0}, LJA0/d;->a()LIA0/g$a;

    .line 184
    .line 185
    .line 186
    move-result-object v4

    .line 187
    invoke-virtual {v4}, LIA0/g$a;->a()LIA0/g$a$a;

    .line 188
    .line 189
    .line 190
    move-result-object v4

    .line 191
    invoke-virtual {v4}, LIA0/g$a$a;->a()Ljava/lang/Double;

    .line 192
    .line 193
    .line 194
    move-result-object v4

    .line 195
    if-eqz v4, :cond_7

    .line 196
    .line 197
    invoke-virtual {v4}, Ljava/lang/Double;->toString()Ljava/lang/String;

    .line 198
    .line 199
    .line 200
    move-result-object v4

    .line 201
    goto :goto_6

    .line 202
    :cond_7
    :goto_5
    move-object v4, v2

    .line 203
    :goto_6
    new-instance v7, Ljava/lang/StringBuilder;

    .line 204
    .line 205
    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    .line 206
    .line 207
    .line 208
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 209
    .line 210
    .line 211
    const-string v0, "-"

    .line 212
    .line 213
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 214
    .line 215
    .line 216
    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 217
    .line 218
    .line 219
    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 220
    .line 221
    .line 222
    move-result-object v0

    .line 223
    invoke-virtual {p0}, LJA0/d;->c()Ljava/util/List;

    .line 224
    .line 225
    .line 226
    move-result-object p0

    .line 227
    if-eqz p0, :cond_e

    .line 228
    .line 229
    new-instance v3, Ljava/util/ArrayList;

    .line 230
    .line 231
    invoke-static {p0, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 232
    .line 233
    .line 234
    move-result v5

    .line 235
    invoke-direct {v3, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 236
    .line 237
    .line 238
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 239
    .line 240
    .line 241
    move-result-object p0

    .line 242
    :goto_7
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 243
    .line 244
    .line 245
    move-result v5

    .line 246
    if-eqz v5, :cond_f

    .line 247
    .line 248
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 249
    .line 250
    .line 251
    move-result-object v5

    .line 252
    check-cast v5, LIA0/g$b;

    .line 253
    .line 254
    new-instance v7, LYA0/h;

    .line 255
    .line 256
    invoke-virtual {v5}, LIA0/g$b;->b()Ljava/lang/String;

    .line 257
    .line 258
    .line 259
    move-result-object v8

    .line 260
    if-nez v8, :cond_9

    .line 261
    .line 262
    invoke-virtual {v5}, LIA0/g$b;->a()Ljava/lang/Integer;

    .line 263
    .line 264
    .line 265
    move-result-object v8

    .line 266
    if-eqz v8, :cond_8

    .line 267
    .line 268
    invoke-virtual {v8}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 269
    .line 270
    .line 271
    move-result-object v8

    .line 272
    goto :goto_8

    .line 273
    :cond_8
    move-object v8, v2

    .line 274
    :goto_8
    if-nez v8, :cond_9

    .line 275
    .line 276
    move-object v8, v6

    .line 277
    :cond_9
    invoke-virtual {v5}, LIA0/g$b;->c()Ljava/lang/Integer;

    .line 278
    .line 279
    .line 280
    move-result-object v9

    .line 281
    if-eqz v9, :cond_a

    .line 282
    .line 283
    invoke-virtual {v9}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 284
    .line 285
    .line 286
    move-result-object v9

    .line 287
    if-nez v9, :cond_b

    .line 288
    .line 289
    :cond_a
    move-object v9, v6

    .line 290
    :cond_b
    invoke-virtual {v5}, LIA0/g$b;->d()Ljava/lang/Integer;

    .line 291
    .line 292
    .line 293
    move-result-object v5

    .line 294
    if-eqz v5, :cond_c

    .line 295
    .line 296
    invoke-virtual {v5}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 297
    .line 298
    .line 299
    move-result-object v5

    .line 300
    if-nez v5, :cond_d

    .line 301
    .line 302
    :cond_c
    move-object v5, v6

    .line 303
    :cond_d
    invoke-direct {v7, v8, v9, v5}, LYA0/h;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 304
    .line 305
    .line 306
    invoke-interface {v3, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 307
    .line 308
    .line 309
    goto :goto_7

    .line 310
    :cond_e
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 311
    .line 312
    .line 313
    move-result-object v3

    .line 314
    :cond_f
    new-instance p0, LWA0/h$a;

    .line 315
    .line 316
    invoke-direct {p0, v0, v3, v1, v4}, LWA0/h$a;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V

    .line 317
    .line 318
    .line 319
    return-object p0

    .line 320
    :cond_10
    invoke-virtual {p0}, LJA0/d;->b()Ljava/lang/String;

    .line 321
    .line 322
    .line 323
    move-result-object v0

    .line 324
    if-nez v0, :cond_11

    .line 325
    .line 326
    const-string v0, ""

    .line 327
    .line 328
    :cond_11
    invoke-virtual {p0}, LJA0/d;->c()Ljava/util/List;

    .line 329
    .line 330
    .line 331
    move-result-object p0

    .line 332
    if-eqz p0, :cond_18

    .line 333
    .line 334
    new-instance v1, Ljava/util/ArrayList;

    .line 335
    .line 336
    invoke-static {p0, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 337
    .line 338
    .line 339
    move-result v3

    .line 340
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 341
    .line 342
    .line 343
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 344
    .line 345
    .line 346
    move-result-object p0

    .line 347
    :goto_9
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 348
    .line 349
    .line 350
    move-result v3

    .line 351
    if-eqz v3, :cond_19

    .line 352
    .line 353
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 354
    .line 355
    .line 356
    move-result-object v3

    .line 357
    check-cast v3, LIA0/g$b;

    .line 358
    .line 359
    new-instance v4, LYA0/h;

    .line 360
    .line 361
    invoke-virtual {v3}, LIA0/g$b;->b()Ljava/lang/String;

    .line 362
    .line 363
    .line 364
    move-result-object v5

    .line 365
    if-nez v5, :cond_13

    .line 366
    .line 367
    invoke-virtual {v3}, LIA0/g$b;->a()Ljava/lang/Integer;

    .line 368
    .line 369
    .line 370
    move-result-object v5

    .line 371
    if-eqz v5, :cond_12

    .line 372
    .line 373
    invoke-virtual {v5}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 374
    .line 375
    .line 376
    move-result-object v5

    .line 377
    goto :goto_a

    .line 378
    :cond_12
    move-object v5, v2

    .line 379
    :goto_a
    if-nez v5, :cond_13

    .line 380
    .line 381
    move-object v5, v6

    .line 382
    :cond_13
    invoke-virtual {v3}, LIA0/g$b;->c()Ljava/lang/Integer;

    .line 383
    .line 384
    .line 385
    move-result-object v7

    .line 386
    if-eqz v7, :cond_14

    .line 387
    .line 388
    invoke-virtual {v7}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 389
    .line 390
    .line 391
    move-result-object v7

    .line 392
    if-nez v7, :cond_15

    .line 393
    .line 394
    :cond_14
    move-object v7, v6

    .line 395
    :cond_15
    invoke-virtual {v3}, LIA0/g$b;->d()Ljava/lang/Integer;

    .line 396
    .line 397
    .line 398
    move-result-object v3

    .line 399
    if-eqz v3, :cond_16

    .line 400
    .line 401
    invoke-virtual {v3}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 402
    .line 403
    .line 404
    move-result-object v3

    .line 405
    if-nez v3, :cond_17

    .line 406
    .line 407
    :cond_16
    move-object v3, v6

    .line 408
    :cond_17
    invoke-direct {v4, v5, v7, v3}, LYA0/h;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 409
    .line 410
    .line 411
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 412
    .line 413
    .line 414
    goto :goto_9

    .line 415
    :cond_18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 416
    .line 417
    .line 418
    move-result-object v1

    .line 419
    :cond_19
    new-instance p0, LWA0/h$b;

    .line 420
    .line 421
    invoke-direct {p0, v0, v1}, LWA0/h$b;-><init>(Ljava/lang/String;Ljava/util/List;)V

    .line 422
    .line 423
    .line 424
    return-object p0

    .line 425
    :cond_1a
    return-object v2
.end method
