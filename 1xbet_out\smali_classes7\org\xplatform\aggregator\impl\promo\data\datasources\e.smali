.class public final Lorg/xplatform/aggregator/impl/promo/data/datasources/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/promo/data/datasources/e$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0000\u0018\u0000 \u00162\u00020\u0001:\u0001*B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ0\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J0\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0014\u001a\u00020\nH\u0086@\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J0\u0010\u001b\u001a\u00020\u00152\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u001a\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ3\u0010#\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\"0!0 2\u0006\u0010\u0018\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u001d\u001a\u00020\u000e2\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008#\u0010$J=\u0010(\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\'0!0 2\u0006\u0010\u0018\u001a\u00020\u000e2\u0008\u0008\u0002\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u001d\u001a\u00020\u000e\u00a2\u0006\u0004\u0008(\u0010)R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u001b\u00103\u001a\u00020.8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102R \u00108\u001a\u000e\u0012\u0004\u0012\u000205\u0012\u0004\u0012\u00020\'048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R \u0010;\u001a\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020\"048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u00107\u00a8\u0006<"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/e;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;",
        "aggregatorPromoProductsPagingSource",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;",
        "aggregatorPromoGamesPagingSource",
        "<init>",
        "(Lf8/g;Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;)V",
        "",
        "token",
        "",
        "accountId",
        "",
        "country",
        "whence",
        "Lua1/f;",
        "g",
        "(Ljava/lang/String;JIILkotlin/coroutines/e;)Ljava/lang/Object;",
        "language",
        "Lua1/e;",
        "f",
        "(Ljava/lang/String;JILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "bonusId",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "statusBonus",
        "n",
        "(Ljava/lang/String;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "pageSize",
        "",
        "test",
        "Lkotlinx/coroutines/flow/e;",
        "Landroidx/paging/PagingData;",
        "Ly81/a;",
        "h",
        "(IIZ)Lkotlinx/coroutines/flow/e;",
        "searchQuery",
        "currentCountryId",
        "Lua1/b;",
        "j",
        "(ILjava/lang/String;II)Lkotlinx/coroutines/flow/e;",
        "a",
        "Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;",
        "b",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;",
        "Lva1/a;",
        "c",
        "Lkotlin/j;",
        "l",
        "()Lva1/a;",
        "service",
        "Landroidx/paging/o;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/paging/b;",
        "d",
        "Landroidx/paging/o;",
        "factoryPromoProducts",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
        "e",
        "factoryPromoGames",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:Lorg/xplatform/aggregator/impl/promo/data/datasources/e$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroidx/paging/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/paging/o<",
            "Lorg/xplatform/aggregator/impl/promo/presentation/paging/b;",
            "Lua1/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroidx/paging/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/paging/o<",
            "Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;",
            "Ly81/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->f:Lorg/xplatform/aggregator/impl/promo/data/datasources/e$a;

    return-void
.end method

.method public constructor <init>(Lf8/g;Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->a:Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->b:Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;

    .line 7
    .line 8
    new-instance p2, Lorg/xplatform/aggregator/impl/promo/data/datasources/b;

    .line 9
    .line 10
    invoke-direct {p2, p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/b;-><init>(Lf8/g;)V

    .line 11
    .line 12
    .line 13
    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->c:Lkotlin/j;

    .line 18
    .line 19
    new-instance p1, Landroidx/paging/o;

    .line 20
    .line 21
    new-instance p2, Lorg/xplatform/aggregator/impl/promo/data/datasources/c;

    .line 22
    .line 23
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/c;-><init>(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)V

    .line 24
    .line 25
    .line 26
    invoke-direct {p1, p2}, Landroidx/paging/o;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 27
    .line 28
    .line 29
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->d:Landroidx/paging/o;

    .line 30
    .line 31
    new-instance p1, Landroidx/paging/o;

    .line 32
    .line 33
    new-instance p2, Lorg/xplatform/aggregator/impl/promo/data/datasources/d;

    .line 34
    .line 35
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/d;-><init>(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)V

    .line 36
    .line 37
    .line 38
    invoke-direct {p1, p2}, Landroidx/paging/o;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 39
    .line 40
    .line 41
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->e:Landroidx/paging/o;

    .line 42
    .line 43
    return-void
.end method

.method public static synthetic a(Lf8/g;)Lva1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->m(Lf8/g;)Lva1/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->d(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->e(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->b:Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final e(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->a:Lorg/xplatform/aggregator/impl/promo/presentation/paging/AggregatorPromoProductsPagingSource;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;IIZILjava/lang/Object;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    and-int/lit8 p4, p4, 0x2

    .line 2
    .line 3
    if-eqz p4, :cond_0

    .line 4
    .line 5
    const/16 p2, 0x10

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->h(IIZ)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static synthetic k(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;ILjava/lang/String;IIILjava/lang/Object;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    and-int/lit8 p6, p5, 0x2

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    const-string p2, ""

    .line 6
    .line 7
    :cond_0
    and-int/lit8 p5, p5, 0x8

    .line 8
    .line 9
    if-eqz p5, :cond_1

    .line 10
    .line 11
    const/16 p4, 0x20

    .line 12
    .line 13
    :cond_1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->j(ILjava/lang/String;II)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    return-object p0
.end method

.method public static final m(Lf8/g;)Lva1/a;
    .locals 1

    .line 1
    const-class v0, Lva1/a;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, Lva1/a;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public final f(Ljava/lang/String;JILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lua1/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->l()Lva1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v8, 0x2

    .line 6
    const/4 v9, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    move-object v1, p1

    .line 9
    move-wide v3, p2

    .line 10
    move v6, p4

    .line 11
    move-object v5, p5

    .line 12
    move-object/from16 v7, p6

    .line 13
    .line 14
    invoke-static/range {v0 .. v9}, Lva1/a$a;->a(Lva1/a;Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;ILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    return-object p1
.end method

.method public final g(Ljava/lang/String;JIILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JII",
            "Lkotlin/coroutines/e<",
            "-",
            "Lua1/f;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->l()Lva1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v5

    .line 9
    const/16 v8, 0x10

    .line 10
    .line 11
    const/4 v9, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    move-object v1, p1

    .line 14
    move-wide v2, p2

    .line 15
    move v4, p5

    .line 16
    move-object/from16 v7, p6

    .line 17
    .line 18
    invoke-static/range {v0 .. v9}, Lva1/a$a;->b(Lva1/a;Ljava/lang/String;JILjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    return-object p1
.end method

.method public final h(IIZ)Lkotlinx/coroutines/flow/e;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IIZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Ly81/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/C;

    .line 2
    .line 3
    const/16 v7, 0x38

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    const/4 v5, 0x0

    .line 10
    const/4 v6, 0x0

    .line 11
    move v1, p2

    .line 12
    invoke-direct/range {v0 .. v8}, Landroidx/paging/C;-><init>(IIZIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->e:Landroidx/paging/o;

    .line 16
    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;

    .line 18
    .line 19
    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    const/4 v2, 0x0

    .line 24
    invoke-direct {v1, p1, v2, p3}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/b;-><init>(IILjava/lang/Boolean;)V

    .line 25
    .line 26
    .line 27
    new-instance p1, Landroidx/paging/Pager;

    .line 28
    .line 29
    invoke-direct {p1, v0, v1, p2}, Landroidx/paging/Pager;-><init>(Landroidx/paging/C;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p1}, Landroidx/paging/Pager;->a()Lkotlinx/coroutines/flow/e;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    return-object p1
.end method

.method public final j(ILjava/lang/String;II)Lkotlinx/coroutines/flow/e;
    .locals 9
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "II)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lua1/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/C;

    .line 2
    .line 3
    const/16 v7, 0x38

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    const/4 v5, 0x0

    .line 10
    const/4 v6, 0x0

    .line 11
    move v1, p4

    .line 12
    invoke-direct/range {v0 .. v8}, Landroidx/paging/C;-><init>(IIZIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->d:Landroidx/paging/o;

    .line 16
    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/promo/presentation/paging/b;

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    invoke-direct {v1, p1, p2, p3, v2}, Lorg/xplatform/aggregator/impl/promo/presentation/paging/b;-><init>(ILjava/lang/String;II)V

    .line 21
    .line 22
    .line 23
    new-instance p1, Landroidx/paging/Pager;

    .line 24
    .line 25
    invoke-direct {p1, v0, v1, p4}, Landroidx/paging/Pager;-><init>(Landroidx/paging/C;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p1}, Landroidx/paging/Pager;->a()Lkotlinx/coroutines/flow/e;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method

.method public final l()Lva1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lva1/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final n(Ljava/lang/String;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lua1/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->l()Lva1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v3, Lta1/a;

    .line 6
    .line 7
    invoke-virtual {p5}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->key()I

    .line 8
    .line 9
    .line 10
    move-result p5

    .line 11
    invoke-direct {v3, p2, p3, p4, p5}, Lta1/a;-><init>(JII)V

    .line 12
    .line 13
    .line 14
    const/4 v5, 0x2

    .line 15
    const/4 v6, 0x0

    .line 16
    const/4 v2, 0x0

    .line 17
    move-object v1, p1

    .line 18
    move-object v4, p6

    .line 19
    invoke-static/range {v0 .. v6}, Lva1/a$a;->c(Lva1/a;Ljava/lang/String;Ljava/lang/String;Lta1/a;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1
.end method
