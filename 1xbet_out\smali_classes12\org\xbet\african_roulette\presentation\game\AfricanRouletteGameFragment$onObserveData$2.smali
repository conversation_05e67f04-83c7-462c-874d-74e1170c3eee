.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteGameFragment$onObserveData$2"
    f = "AfricanRouletteGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->invoke(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v0, v0, Lgg/b;->d:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;

    .line 26
    .line 27
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;->a()Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-nez p1, :cond_0

    .line 34
    .line 35
    const/4 p1, 0x4

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    const/4 p1, 0x0

    .line 38
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    instance-of v0, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$c;

    .line 43
    .line 44
    if-eqz v0, :cond_2

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 47
    .line 48
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iget-object v0, v0, Lgg/b;->d:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;

    .line 53
    .line 54
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$c;

    .line 55
    .line 56
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$c;->a()Ljava/util/List;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-virtual {v0, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->v(Ljava/util/List;)V

    .line 61
    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_2
    instance-of v0, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;

    .line 65
    .line 66
    if-eqz v0, :cond_3

    .line 67
    .line 68
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 69
    .line 70
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;

    .line 71
    .line 72
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;->a()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-static {v0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->I2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 77
    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_3
    instance-of p1, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$a;

    .line 81
    .line 82
    if-eqz p1, :cond_4

    .line 83
    .line 84
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 85
    .line 86
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->H2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)V

    .line 87
    .line 88
    .line 89
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1

    .line 92
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 93
    .line 94
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 95
    .line 96
    .line 97
    throw p1

    .line 98
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 99
    .line 100
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 101
    .line 102
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 103
    .line 104
    .line 105
    throw p1
.end method
