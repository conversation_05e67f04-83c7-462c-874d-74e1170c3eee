.class public final LQH0/a;
.super Ljava/lang/Object;


# static fields
.field public static ballImageView:I = 0x7f0a017b

.field public static barrier:I = 0x7f0a0196

.field public static bottomBarrier:I = 0x7f0a024f

.field public static bottomSpace:I = 0x7f0a0268

.field public static btnResult:I = 0x7f0a02d9

.field public static chipsTab:I = 0x7f0a0456

.field public static clHeader:I = 0x7f0a04a7

.field public static clMedals:I = 0x7f0a04b2

.field public static clRefereeCard:I = 0x7f0a04c2

.field public static content:I = 0x7f0a057b

.field public static datePagerItem:I = 0x7f0a0602

.field public static divider:I = 0x7f0a0674

.field public static emptyView:I = 0x7f0a0710

.field public static firstIcon:I = 0x7f0a07fa

.field public static firstName:I = 0x7f0a0800

.field public static flContentContainer:I = 0x7f0a0878

.field public static flResultContainer:I = 0x7f0a0898

.field public static flStatusView:I = 0x7f0a08a2

.field public static flToolbarInfo:I = 0x7f0a08ac

.field public static flToolbarSelectors:I = 0x7f0a08ae

.field public static guideline:I = 0x7f0a0a05

.field public static guideline1:I = 0x7f0a0a06

.field public static guideline2:I = 0x7f0a0a07

.field public static guideline3:I = 0x7f0a0a08

.field public static guidelineCenter:I = 0x7f0a0a13

.field public static guidelineLastGame:I = 0x7f0a0a1b

.field public static item:I = 0x7f0a0bc7

.field public static ivBackground:I = 0x7f0a0be8

.field public static ivBall:I = 0x7f0a0beb

.field public static ivBronzeMedal:I = 0x7f0a0bf4

.field public static ivCountryIcon:I = 0x7f0a0c21

.field public static ivData:I = 0x7f0a0c2b

.field public static ivGameBackground:I = 0x7f0a0c74

.field public static ivGoals:I = 0x7f0a0c78

.field public static ivGoldMedal:I = 0x7f0a0c7a

.field public static ivInjury:I = 0x7f0a0c8a

.field public static ivPlayerOne:I = 0x7f0a0cca

.field public static ivPlayerTwo:I = 0x7f0a0ccd

.field public static ivRefereeAvatar:I = 0x7f0a0ce1

.field public static ivSilverMedal:I = 0x7f0a0d14

.field public static ivTeamOne:I = 0x7f0a0d2d

.field public static ivTeamTwo:I = 0x7f0a0d3a

.field public static ivToolbarInfo:I = 0x7f0a0d4e

.field public static ivToolbarSelectors:I = 0x7f0a0d50

.field public static llContent:I = 0x7f0a0e67

.field public static llGoals:I = 0x7f0a0e79

.field public static llMatchInfo:I = 0x7f0a0e7f

.field public static llRedCards:I = 0x7f0a0e8a

.field public static llShimmer:I = 0x7f0a0e93

.field public static llTeamOne:I = 0x7f0a0e99

.field public static llTeamTwo:I = 0x7f0a0e9c

.field public static llTime:I = 0x7f0a0ea3

.field public static llYellowCards:I = 0x7f0a0eb0

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static lottie_empty_view:I = 0x7f0a0ef5

.field public static navigationBar:I = 0x7f0a0faf

.field public static parent:I = 0x7f0a104c

.field public static penaltyTeamFirst:I = 0x7f0a1077

.field public static penaltyTeamSecond:I = 0x7f0a1078

.field public static recyclerView:I = 0x7f0a11ad

.field public static recycler_referee_last_game:I = 0x7f0a11b8

.field public static redCardTeamFirst:I = 0x7f0a11bf

.field public static redCardTeamSecond:I = 0x7f0a11c1

.field public static result:I = 0x7f0a11f6

.field public static rvAdditionalInfo:I = 0x7f0a1256

.field public static rvChips:I = 0x7f0a1265

.field public static rvContent:I = 0x7f0a126b

.field public static rvInfo:I = 0x7f0a127a

.field public static rvInnings:I = 0x7f0a127c

.field public static rvMenu:I = 0x7f0a128c

.field public static rvPairStatistic:I = 0x7f0a1290

.field public static rvReferees:I = 0x7f0a129d

.field public static rvResults:I = 0x7f0a129f

.field public static rvSelectors:I = 0x7f0a12a4

.field public static rvStatisticBlock:I = 0x7f0a12b4

.field public static score:I = 0x7f0a12f4

.field public static scoreDelimiter:I = 0x7f0a12f6

.field public static scrollView:I = 0x7f0a1304

.field public static scrollablePanel:I = 0x7f0a130b

.field public static scrollable_panel:I = 0x7f0a130d

.field public static secondIcon:I = 0x7f0a1332

.field public static secondName:I = 0x7f0a1339

.field public static segments:I = 0x7f0a13a9

.field public static separator:I = 0x7f0a13c7

.field public static shimmer:I = 0x7f0a1400

.field public static shimmer1:I = 0x7f0a1401

.field public static shimmer2:I = 0x7f0a1402

.field public static shimmer3:I = 0x7f0a1403

.field public static shimmers:I = 0x7f0a148b

.field public static staticNavigationBar:I = 0x7f0a15f5

.field public static teamCardView:I = 0x7f0a16d0

.field public static teamTabs:I = 0x7f0a16ef

.field public static titleMatch:I = 0x7f0a1815

.field public static toolbar:I = 0x7f0a183e

.field public static tvAbbreviation:I = 0x7f0a1919

.field public static tvAttemptsCountPlayerOne:I = 0x7f0a1941

.field public static tvAttemptsCountPlayerTwo:I = 0x7f0a1942

.field public static tvBronzeMedal:I = 0x7f0a198f

.field public static tvCompetition:I = 0x7f0a19d7

.field public static tvData:I = 0x7f0a1a0a

.field public static tvDate:I = 0x7f0a1a0b

.field public static tvDescription:I = 0x7f0a1a20

.field public static tvFullTitle:I = 0x7f0a1ab5

.field public static tvGoals:I = 0x7f0a1ac9

.field public static tvGoldMedal:I = 0x7f0a1acb

.field public static tvInjuryDate:I = 0x7f0a1ae5

.field public static tvInjuryName:I = 0x7f0a1ae6

.field public static tvName:I = 0x7f0a1b31

.field public static tvPlayerOneName:I = 0x7f0a1b87

.field public static tvPlayerOneScore:I = 0x7f0a1b88

.field public static tvPlayerTwoName:I = 0x7f0a1b8f

.field public static tvPlayerTwoScore:I = 0x7f0a1b90

.field public static tvPointCountPlayerOne:I = 0x7f0a1b94

.field public static tvPointCountPlayerTwo:I = 0x7f0a1b95

.field public static tvRedCards:I = 0x7f0a1bc4

.field public static tvRefereeAge:I = 0x7f0a1bc5

.field public static tvRefereeCountry:I = 0x7f0a1bc6

.field public static tvRefereeName:I = 0x7f0a1bc7

.field public static tvRefereeType:I = 0x7f0a1bc8

.field public static tvScoreOne:I = 0x7f0a1bf0

.field public static tvScoreTwo:I = 0x7f0a1bf4

.field public static tvShortTitle:I = 0x7f0a1c36

.field public static tvSilverMedal:I = 0x7f0a1c3b

.field public static tvTeamOne:I = 0x7f0a1c78

.field public static tvTeamTwo:I = 0x7f0a1c81

.field public static tvTime:I = 0x7f0a1c9e

.field public static tvTitle:I = 0x7f0a1cac

.field public static tvTitleAttempts:I = 0x7f0a1cad

.field public static tvTitleMatch:I = 0x7f0a1cb4

.field public static tvTitlePoints:I = 0x7f0a1cb6

.field public static tvTopTitle:I = 0x7f0a1cba

.field public static tvTranscription:I = 0x7f0a1cd0

.field public static tvTransferType:I = 0x7f0a1cd2

.field public static tvValue:I = 0x7f0a1ced

.field public static tvYellowCards:I = 0x7f0a1d38

.field public static viewArrow:I = 0x7f0a1f28

.field public static viewBackground:I = 0x7f0a1f2a

.field public static viewLineStatisticOne:I = 0x7f0a1f5c

.field public static viewLineStatisticTwo:I = 0x7f0a1f5d

.field public static viewRow1:I = 0x7f0a1f6c

.field public static viewRow2:I = 0x7f0a1f6d

.field public static viewRowTitle1:I = 0x7f0a1f70

.field public static viewRowTitle2:I = 0x7f0a1f72

.field public static viewTopRaiders:I = 0x7f0a1f88

.field public static viewTopTacklers:I = 0x7f0a1f89

.field public static view_shadow:I = 0x7f0a1fa9

.field public static yellowCardTeamFirst:I = 0x7f0a201f

.field public static yellowCardTeamSecond:I = 0x7f0a2020


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
