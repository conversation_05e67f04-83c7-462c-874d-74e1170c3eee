.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u000c\u0008\u0081\u0008\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\u0008\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001d\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\r\u0010\u0011\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0010\u0010\u0013\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0010\u0010\u0015\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0015\u0010\u0012J\u001a\u0010\u0019\u001a\u00020\u00182\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u001c\u001a\u0004\u0008\u001d\u0010\u0012R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008\u001e\u0010\u0014R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008 \u0010!\u001a\u0004\u0008 \u0010\"R\u0017\u0010\u0008\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008#\u0010\u001c\u001a\u0004\u0008\u001b\u0010\u0012\u00a8\u0006$"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
        "Landroid/os/Parcelable;",
        "",
        "eventId",
        "",
        "group",
        "",
        "sportId",
        "champId",
        "<init>",
        "(ILjava/lang/String;JI)V",
        "Landroid/os/Parcel;",
        "dest",
        "flags",
        "",
        "writeToParcel",
        "(Landroid/os/Parcel;I)V",
        "describeContents",
        "()I",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "I",
        "G",
        "b",
        "Ljava/lang/String;",
        "c",
        "J",
        "()J",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I

.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:J

.field public final d:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams$a;

    invoke-direct {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams$a;-><init>()V

    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->CREATOR:Landroid/os/Parcelable$Creator;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->$stable:I

    return-void
.end method

.method public constructor <init>(ILjava/lang/String;JI)V
    .locals 0
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    .line 7
    .line 8
    iput-wide p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    .line 9
    .line 10
    iput p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final G()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    iget v3, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-wide v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    iget-wide v5, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_4

    return v2

    :cond_4
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    iget p1, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    if-eq v1, p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    iget-wide v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    iget v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "OpponentsScreenParams(eventId="

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", group="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", sportId="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", champId="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 2
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iget p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->a:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    iget-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b:Ljava/lang/String;

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-wide v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->c:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->d:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    return-void
.end method
