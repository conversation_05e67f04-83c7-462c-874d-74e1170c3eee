.class public final LJa1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lua1/b;",
        "",
        "service",
        "",
        "nightMode",
        "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
        "a",
        "(Lua1/b;Ljava/lang/String;Z)Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lua1/b;Ljava/lang/String;Z)Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;
    .locals 1
    .param p0    # Lua1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0, p2}, Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;-><init>(Ljava/lang/String;Lua1/b;Z)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
