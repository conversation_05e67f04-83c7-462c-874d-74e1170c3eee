.class public final LIN0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LIN0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static b:LOc/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static c:LOc/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, LIN0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LIN0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LIN0/a;->a:LIN0/a;

    .line 7
    .line 8
    sget-object v0, LIN0/a$a;->a:LIN0/a$a;

    .line 9
    .line 10
    const v1, 0x49414fb6    # 791803.4f

    .line 11
    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static {v1, v2, v0}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    sput-object v0, LIN0/a;->b:LOc/o;

    .line 19
    .line 20
    const v0, 0x2351646a

    .line 21
    .line 22
    .line 23
    sget-object v1, LIN0/a$b;->a:LIN0/a$b;

    .line 24
    .line 25
    invoke-static {v0, v2, v1}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    sput-object v0, LIN0/a;->c:LOc/o;

    .line 30
    .line 31
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a()LOc/o;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LIN0/a;->b:LOc/o;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()LOc/o;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LOc/o<",
            "Landroidx/compose/foundation/lazy/c;",
            "Ljava/lang/Integer;",
            "Landroidx/compose/runtime/j;",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LIN0/a;->c:LOc/o;

    .line 2
    .line 3
    return-object v0
.end method
