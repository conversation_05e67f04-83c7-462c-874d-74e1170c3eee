.class public final LQQ0/w;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0018\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0086\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "LQQ0/w;",
        "",
        "LQQ0/y;",
        "updateCurrentWinLossInfoUseCase",
        "LQQ0/A;",
        "updateSelectedMatchTypeUseCase",
        "LQQ0/C;",
        "updateSelectedSeasonUseCase",
        "<init>",
        "(LQQ0/y;LQQ0/A;LQQ0/C;)V",
        "LPQ0/d;",
        "winLossModel",
        "",
        "a",
        "(LPQ0/d;)V",
        "LQQ0/y;",
        "b",
        "LQQ0/A;",
        "c",
        "LQQ0/C;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQQ0/y;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LQQ0/A;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LQQ0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQQ0/y;LQQ0/A;LQQ0/C;)V
    .locals 0
    .param p1    # LQQ0/y;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LQQ0/A;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LQQ0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQQ0/w;->a:LQQ0/y;

    .line 5
    .line 6
    iput-object p2, p0, LQQ0/w;->b:LQQ0/A;

    .line 7
    .line 8
    iput-object p3, p0, LQQ0/w;->c:LQQ0/C;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(LPQ0/d;)V
    .locals 2
    .param p1    # LPQ0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LQQ0/w;->a:LQQ0/y;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LQQ0/y;->a(LPQ0/d;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LQQ0/w;->b:LQQ0/A;

    .line 7
    .line 8
    invoke-virtual {p1}, LPQ0/d;->b()Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LQQ0/A;->a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, LQQ0/w;->c:LQQ0/C;

    .line 16
    .line 17
    invoke-virtual {p1}, LPQ0/d;->c()I

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    invoke-virtual {v0, p1}, LQQ0/C;->a(I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method
