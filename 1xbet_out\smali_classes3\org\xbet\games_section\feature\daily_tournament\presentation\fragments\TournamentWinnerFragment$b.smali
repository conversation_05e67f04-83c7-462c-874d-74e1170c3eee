.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$b;
.super Landroidx/recyclerview/widget/RecyclerView$s;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\'\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "org/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$b",
        "Landroidx/recyclerview/widget/RecyclerView$s;",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "recyclerView",
        "",
        "dx",
        "dy",
        "",
        "onScrolled",
        "(Landroidx/recyclerview/widget/RecyclerView;II)V",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$b;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$s;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onScrolled(Landroidx/recyclerview/widget/RecyclerView;II)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$b;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;)Lm40/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lm40/d;->e:Landroid/view/View;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment$b;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;

    .line 10
    .line 11
    invoke-static {v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;->C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentWinnerFragment;)Lm40/d;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iget-object v1, v1, Lm40/d;->d:Landroid/view/View;

    .line 16
    .line 17
    invoke-super {p0, p1, p2, p3}, Landroidx/recyclerview/widget/RecyclerView$s;->onScrolled(Landroidx/recyclerview/widget/RecyclerView;II)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->computeVerticalScrollOffset()I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    const/4 p2, 0x0

    .line 25
    if-eqz p1, :cond_0

    .line 26
    .line 27
    const/4 p1, 0x1

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    const/4 p1, 0x0

    .line 30
    :goto_0
    const/16 p3, 0x8

    .line 31
    .line 32
    if-eqz p1, :cond_1

    .line 33
    .line 34
    const/4 v2, 0x0

    .line 35
    goto :goto_1

    .line 36
    :cond_1
    const/16 v2, 0x8

    .line 37
    .line 38
    :goto_1
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    if-nez p1, :cond_2

    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_2
    const/16 p2, 0x8

    .line 45
    .line 46
    :goto_2
    invoke-virtual {v1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    return-void
.end method
