.class public final Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/sport_collection/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_sport/sport_collection/SportsCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001d\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "org/xbet/uikit_sport/sport_collection/SportsCollection$a",
        "Lorg/xbet/uikit_sport/sport_collection/a$a;",
        "Landroid/view/View;",
        "view",
        "",
        "position",
        "",
        "a",
        "(Landroid/view/View;I)V",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_collection/SportsCollection;


# direct methods
.method public constructor <init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollection;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a(Landroid/view/View;I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollection;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->f(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_2

    .line 17
    .line 18
    const/4 p1, 0x2

    .line 19
    if-eq v0, p1, :cond_1

    .line 20
    .line 21
    const/4 p1, 0x3

    .line 22
    if-ne v0, p1, :cond_0

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 26
    .line 27
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollection;

    .line 32
    .line 33
    invoke-static {p1, p2}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->g(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;I)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_2
    invoke-virtual {p1}, Landroid/view/View;->isSelected()Z

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    if-nez p1, :cond_3

    .line 42
    .line 43
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollection;

    .line 44
    .line 45
    invoke-static {p1, p2}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->h(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;I)V

    .line 46
    .line 47
    .line 48
    :cond_3
    :goto_0
    return-void
.end method
