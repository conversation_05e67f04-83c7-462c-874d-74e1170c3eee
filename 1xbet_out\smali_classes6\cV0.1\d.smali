.class public final synthetic LcV0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LcV0/d;->a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LcV0/d;->a:Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;

    check-cast p1, LgV0/d;

    invoke-static {v0, p1}, Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;->C(Lorg/xbet/toto_bet/outcomes/TotoBetAccurateFlexboxLayout;LgV0/d;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
