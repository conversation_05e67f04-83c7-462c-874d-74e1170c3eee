.class public final LMQ0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001R\"\u0010\u0004\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0004\u0010\u0005\u001a\u0004\u0008\u0006\u0010\u0007R\u001c\u0010\t\u001a\u0004\u0018\u00010\u00088\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\n\u001a\u0004\u0008\u000b\u0010\u000cR\u001c\u0010\u000e\u001a\u0004\u0018\u00010\r8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u000e\u0010\u000f\u001a\u0004\u0008\u0010\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "LMQ0/c;",
        "",
        "",
        "LCN0/i;",
        "players",
        "Ljava/util/List;",
        "getPlayers",
        "()Ljava/util/List;",
        "",
        "sportId",
        "Ljava/lang/Integer;",
        "getSportId",
        "()Ljava/lang/Integer;",
        "LMQ0/b;",
        "response",
        "LMQ0/b;",
        "a",
        "()LMQ0/b;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final players:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "players"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LCN0/i;",
            ">;"
        }
    .end annotation
.end field

.field private final response:LMQ0/b;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "response"
    .end annotation
.end field

.field private final sportId:Ljava/lang/Integer;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "sportId"
    .end annotation
.end field


# virtual methods
.method public final a()LMQ0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LMQ0/c;->response:LMQ0/b;

    .line 2
    .line 3
    return-object v0
.end method
