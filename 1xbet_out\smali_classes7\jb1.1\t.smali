.class public final Ljb1/t;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/t$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u001au\u0010\u0019\u001a\u00020\u0018*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0000\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a=\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u0017\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 \u001a5\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008!\u0010\"\u001a-\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008#\u0010$\u001a5\u0010&\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020%0\u001d2\u0006\u0010\u0004\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008&\u0010\'\u001a5\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\t\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0017\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008(\u0010)\u001a\u0017\u0010+\u001a\u00020*2\u0006\u0010\u001b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008+\u0010,\u001a%\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008-\u0010.\u001a-\u0010/\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008/\u00100\u001a%\u00101\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u00081\u00102\u001a\'\u00108\u001a\u0002072\u0006\u00103\u001a\u00020\u00012\u0006\u00104\u001a\u00020\u00012\u0006\u00106\u001a\u000205H\u0002\u00a2\u0006\u0004\u00088\u00109\u001a\'\u0010;\u001a\u00020:2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020%0\u001d2\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008;\u0010<\u001a\'\u0010>\u001a\u00020=2\u0006\u0010\u001b\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008>\u0010?\u001a\u001f\u0010A\u001a\u00020\u00012\u0006\u0010@\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008A\u0010B\u001a\u0017\u0010D\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008D\u0010E\u001a\u0017\u0010F\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008F\u0010E\u001a\u0017\u0010G\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008G\u0010E\u001a\u0017\u0010H\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008H\u0010E\u00a8\u0006I"
    }
    d2 = {
        "Li81/a;",
        "",
        "currencySymbol",
        "LHX0/e;",
        "resourceManager",
        "Lkb1/m;",
        "topGames",
        "",
        "aggregatorGameCardCollectionStyle",
        "dSTournamentStagesCellType",
        "LO21/b;",
        "aggregatorProviderCardCollectionAppearanceModel",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "aggregatorTournamentTimerType",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;",
        "aggregatorTournamentPrizeStyleConfigType",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "aggregatorTournamentPrizePoolStyle",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;",
        "aggregatorTournamentProgressDSStyleType",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;",
        "aggregatorTournamentRulesDSStyleType",
        "Ljava/util/Locale;",
        "locale",
        "Lkb1/n;",
        "r",
        "(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;ILjava/lang/String;LO21/b;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;Ljava/util/Locale;)Lkb1/n;",
        "tournament",
        "styleType",
        "",
        "LVX0/i;",
        "a",
        "(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ljava/util/List;",
        "b",
        "(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;)Ljava/util/List;",
        "c",
        "(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Ljava/util/List;",
        "LN21/k;",
        "k",
        "(Ljava/util/List;LHX0/e;I)Ljava/util/List;",
        "g",
        "(Li81/a;Ljava/lang/String;LHX0/e;Ljava/util/Locale;)Ljava/util/List;",
        "",
        "q",
        "(Li81/a;)Z",
        "f",
        "(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)Ljava/util/List;",
        "e",
        "(Li81/a;LHX0/e;LO21/b;)Ljava/util/List;",
        "h",
        "(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)Ljava/util/List;",
        "title",
        "btnAllText",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;",
        "type",
        "Llb1/p;",
        "i",
        "(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)Llb1/p;",
        "Llb1/r;",
        "j",
        "(Ljava/util/List;I)Llb1/r;",
        "Llb1/g;",
        "d",
        "(Li81/a;LHX0/e;LO21/b;)Llb1/g;",
        "gamesSize",
        "p",
        "(ILHX0/e;)Ljava/lang/String;",
        "timerType",
        "l",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I",
        "m",
        "n",
        "o",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "Ljava/lang/String;",
            "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
            "Ljava/util/Locale;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Llb1/b;

    .line 2
    .line 3
    invoke-static {p0, p1, p2, p3, p4}, Ljb1/h;->h(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ln21/a;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-direct {v0, p0}, Llb1/b;-><init>(Ln21/b;)V

    .line 8
    .line 9
    .line 10
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    return-object p0
.end method

.method public static final b(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "Ljava/lang/String;",
            "Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lk81/a;->c()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-nez v0, :cond_2

    .line 10
    .line 11
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lk81/a;->d()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-nez v0, :cond_2

    .line 24
    .line 25
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {v0}, Lk81/a;->e()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    sget v1, Lpb/k;->all:I

    .line 34
    .line 35
    const/4 v2, 0x0

    .line 36
    new-array v2, v2, [Ljava/lang/Object;

    .line 37
    .line 38
    invoke-interface {p1, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-virtual {v2}, Lk81/a;->d()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    const/4 v3, 0x5

    .line 55
    if-le v2, v3, :cond_0

    .line 56
    .line 57
    goto :goto_0

    .line 58
    :cond_0
    const/4 v1, 0x0

    .line 59
    :goto_0
    if-nez v1, :cond_1

    .line 60
    .line 61
    const-string v1, ""

    .line 62
    .line 63
    :cond_1
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    invoke-virtual {p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    invoke-static {v2, p3, p0, p2, p1}, Ljb1/g;->b(Lk81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    new-instance p1, LV21/c;

    .line 76
    .line 77
    invoke-direct {p1, v0, v1, p0}, LV21/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V

    .line 78
    .line 79
    .line 80
    new-instance p0, LV21/b;

    .line 81
    .line 82
    invoke-direct {p0, p3, p1}, LV21/b;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;LV21/c;)V

    .line 83
    .line 84
    .line 85
    new-instance p1, Llb1/c;

    .line 86
    .line 87
    invoke-direct {p1, p0}, Llb1/c;-><init>(LV21/d;)V

    .line 88
    .line 89
    .line 90
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object p0

    .line 94
    return-object p0

    .line 95
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 96
    .line 97
    .line 98
    move-result-object p0

    .line 99
    return-object p0
.end method

.method public static final c(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {p0, p1, p2}, Ljb1/i;->d(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lq21/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    if-eqz p0, :cond_0

    .line 6
    .line 7
    new-instance p1, Llb1/e;

    .line 8
    .line 9
    invoke-direct {p1, p0}, Llb1/e;-><init>(Lq21/b;)V

    .line 10
    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    if-eqz p0, :cond_0

    .line 17
    .line 18
    return-object p0

    .line 19
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p0

    .line 23
    return-object p0
.end method

.method public static final d(Li81/a;LHX0/e;LO21/b;)Llb1/g;
    .locals 16

    .line 1
    invoke-virtual/range {p0 .. p0}, Li81/a;->q()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v1, 0x8

    .line 6
    .line 7
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, Ljava/util/ArrayList;

    .line 12
    .line 13
    const/16 v2, 0xa

    .line 14
    .line 15
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 20
    .line 21
    .line 22
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    if-eqz v2, :cond_0

    .line 31
    .line 32
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    check-cast v2, Ll81/a;

    .line 37
    .line 38
    invoke-virtual {v2}, Ll81/a;->c()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v5

    .line 46
    invoke-virtual {v2}, Ll81/a;->a()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-static {v3}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v2}, Ll81/a;->b()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v8

    .line 58
    sget v2, Lpb/k;->casino_providers_games:I

    .line 59
    .line 60
    const/4 v4, 0x0

    .line 61
    new-array v4, v4, [Ljava/lang/Object;

    .line 62
    .line 63
    move-object/from16 v15, p1

    .line 64
    .line 65
    invoke-interface {v15, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v10

    .line 69
    new-instance v4, LP21/c;

    .line 70
    .line 71
    invoke-static {v3}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 72
    .line 73
    .line 74
    move-result-object v6

    .line 75
    const/16 v13, 0xc0

    .line 76
    .line 77
    const/4 v14, 0x0

    .line 78
    const/4 v7, 0x0

    .line 79
    const-string v9, "-"

    .line 80
    .line 81
    const/4 v11, 0x0

    .line 82
    const/4 v12, 0x0

    .line 83
    invoke-direct/range {v4 .. v14}, LP21/c;-><init>(Ljava/lang/String;LL11/c;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LL11/c;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 84
    .line 85
    .line 86
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 87
    .line 88
    .line 89
    goto :goto_0

    .line 90
    :cond_0
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$a;

    .line 91
    .line 92
    move-object/from16 v2, p2

    .line 93
    .line 94
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$a;-><init>(Ljava/util/List;LO21/b;)V

    .line 95
    .line 96
    .line 97
    new-instance v1, Llb1/g;

    .line 98
    .line 99
    invoke-direct {v1, v0}, Llb1/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 100
    .line 101
    .line 102
    return-object v1
.end method

.method public static final e(Li81/a;LHX0/e;LO21/b;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "LO21/b;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0}, Li81/a;->q()Ljava/util/List;

    .line 3
    .line 4
    .line 5
    move-result-object v1

    .line 6
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-nez v1, :cond_2

    .line 11
    .line 12
    sget v1, Lpb/k;->providers:I

    .line 13
    .line 14
    new-array v2, v0, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {p1, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    sget v2, Lpb/k;->all:I

    .line 21
    .line 22
    new-array v3, v0, [Ljava/lang/Object;

    .line 23
    .line 24
    invoke-interface {p1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    invoke-virtual {p0}, Li81/a;->q()Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    const/16 v4, 0x8

    .line 37
    .line 38
    if-le v3, v4, :cond_0

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_0
    const/4 v2, 0x0

    .line 42
    :goto_0
    if-nez v2, :cond_1

    .line 43
    .line 44
    const-string v2, ""

    .line 45
    .line 46
    :cond_1
    sget-object v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PROVIDER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 47
    .line 48
    invoke-static {v1, v2, v3}, Ljb1/t;->i(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)Llb1/p;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    invoke-static {p0, p1, p2}, Ljb1/t;->d(Li81/a;LHX0/e;LO21/b;)Llb1/g;

    .line 53
    .line 54
    .line 55
    move-result-object p0

    .line 56
    const/4 p1, 0x2

    .line 57
    new-array p1, p1, [LVX0/i;

    .line 58
    .line 59
    aput-object v1, p1, v0

    .line 60
    .line 61
    const/4 p2, 0x1

    .line 62
    aput-object p0, p1, p2

    .line 63
    .line 64
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 65
    .line 66
    .line 67
    move-result-object p0

    .line 68
    return-object p0

    .line 69
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    return-object p0
.end method

.method public static final f(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->g()Ln81/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Ln81/c;->a()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Llb1/i;

    .line 16
    .line 17
    invoke-static {p0, p1}, Ljb1/j;->a(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)LZ21/a;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    invoke-direct {v0, p0}, Llb1/i;-><init>(LZ21/b;)V

    .line 22
    .line 23
    .line 24
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    return-object p0

    .line 29
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    return-object p0
.end method

.method public static final g(Li81/a;Ljava/lang/String;LHX0/e;Ljava/util/Locale;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Ljava/lang/String;",
            "LHX0/e;",
            "Ljava/util/Locale;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-static {p0, v0, p2, p3, p1}, Ljb1/q;->g(Li81/a;ZLHX0/e;Ljava/util/Locale;Ljava/lang/String;)Ljava/util/List;

    .line 3
    .line 4
    .line 5
    move-result-object p3

    .line 6
    invoke-interface {p3}, Ljava/util/Collection;->isEmpty()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_2

    .line 11
    .line 12
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->Companion:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;

    .line 13
    .line 14
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;->b(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    sget v0, Lpb/k;->tournament_stages:I

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    new-array v2, v1, [Ljava/lang/Object;

    .line 22
    .line 23
    invoke-interface {p2, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    sget v2, Lpb/k;->all:I

    .line 28
    .line 29
    new-array v1, v1, [Ljava/lang/Object;

    .line 30
    .line 31
    invoke-interface {p2, v2, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    invoke-static {p0}, Ljb1/t;->q(Li81/a;)Z

    .line 36
    .line 37
    .line 38
    move-result p0

    .line 39
    if-eqz p0, :cond_0

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 p2, 0x0

    .line 43
    :goto_0
    if-nez p2, :cond_1

    .line 44
    .line 45
    const-string p2, ""

    .line 46
    .line 47
    :cond_1
    new-instance p0, Lb31/a;

    .line 48
    .line 49
    invoke-direct {p0, p1, v0, p2, p3}, Lb31/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V

    .line 50
    .line 51
    .line 52
    new-instance p1, Llb1/k;

    .line 53
    .line 54
    invoke-direct {p1, p0}, Llb1/k;-><init>(Lb31/b;)V

    .line 55
    .line 56
    .line 57
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    return-object p0

    .line 62
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object p0

    .line 66
    return-object p0
.end method

.method public static final h(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)Ljava/util/List;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lj81/a;->a()Lj81/b;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Lj81/b;->c()Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;->STOPPED:Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;

    .line 14
    .line 15
    if-eq v0, v1, :cond_0

    .line 16
    .line 17
    new-instance v0, Llb1/m;

    .line 18
    .line 19
    new-instance v1, Le31/a;

    .line 20
    .line 21
    new-instance v2, Le31/c;

    .line 22
    .line 23
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-virtual {v3}, Lj81/a;->a()Lj81/b;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    invoke-virtual {v3}, Lj81/b;->b()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    invoke-static {p1}, Ljb1/t;->l(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I

    .line 36
    .line 37
    .line 38
    move-result v4

    .line 39
    invoke-static {p1}, Ljb1/t;->m(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I

    .line 40
    .line 41
    .line 42
    move-result v5

    .line 43
    invoke-static {p1}, Ljb1/t;->n(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I

    .line 44
    .line 45
    .line 46
    move-result v6

    .line 47
    invoke-static {p1}, Ljb1/t;->o(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I

    .line 48
    .line 49
    .line 50
    move-result v7

    .line 51
    sget-object v8, Ll8/b;->a:Ll8/b;

    .line 52
    .line 53
    new-instance v9, Ljava/util/Date;

    .line 54
    .line 55
    invoke-direct {v9}, Ljava/util/Date;-><init>()V

    .line 56
    .line 57
    .line 58
    invoke-virtual {v9}, Ljava/util/Date;->getTime()J

    .line 59
    .line 60
    .line 61
    move-result-wide v9

    .line 62
    sget-object v11, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 63
    .line 64
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 65
    .line 66
    .line 67
    move-result-object p0

    .line 68
    invoke-virtual {p0}, Lj81/a;->a()Lj81/b;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    invoke-virtual {p0}, Lj81/b;->a()J

    .line 73
    .line 74
    .line 75
    move-result-wide v12

    .line 76
    invoke-virtual {v11, v12, v13}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 77
    .line 78
    .line 79
    move-result-wide v11

    .line 80
    add-long/2addr v9, v11

    .line 81
    const/4 p0, 0x0

    .line 82
    invoke-virtual {v8, v9, v10, p0}, Ll8/b;->o0(JZ)Ljava/util/Date;

    .line 83
    .line 84
    .line 85
    move-result-object v8

    .line 86
    sget-object v9, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;->COUNTDOWN_TO_DATE:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;

    .line 87
    .line 88
    invoke-direct/range {v2 .. v9}, Le31/c;-><init>(Ljava/lang/String;IIIILjava/util/Date;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerMode;)V

    .line 89
    .line 90
    .line 91
    const/4 p0, 0x1

    .line 92
    invoke-direct {v1, p1, v2, p0}, Le31/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Le31/c;Z)V

    .line 93
    .line 94
    .line 95
    invoke-direct {v0, v1}, Llb1/m;-><init>(Le31/b;)V

    .line 96
    .line 97
    .line 98
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 99
    .line 100
    .line 101
    move-result-object p0

    .line 102
    return-object p0

    .line 103
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 104
    .line 105
    .line 106
    move-result-object p0

    .line 107
    return-object p0
.end method

.method public static final i(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)Llb1/p;
    .locals 1

    .line 1
    new-instance v0, Llb1/p;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, Llb1/p;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final j(Ljava/util/List;I)Llb1/r;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LN21/k;",
            ">;I)",
            "Llb1/r;"
        }
    .end annotation

    .line 1
    new-instance v0, Llb1/r;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-direct {v0, p1, p0, v1}, Llb1/r;-><init>(ILjava/util/List;Z)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final k(Ljava/util/List;LHX0/e;I)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LN21/k;",
            ">;",
            "LHX0/e;",
            "I)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-static {v0, p1}, Ljb1/t;->p(ILHX0/e;)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    const-string v0, ""

    .line 16
    .line 17
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 18
    .line 19
    invoke-static {p1, v0, v1}, Ljb1/t;->i(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)Llb1/p;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {p0, p2}, Ljb1/t;->j(Ljava/util/List;I)Llb1/r;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    const/4 p2, 0x2

    .line 28
    new-array p2, p2, [LVX0/i;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    aput-object p1, p2, v0

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    aput-object p0, p2, p1

    .line 35
    .line 36
    invoke-static {p2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    return-object p0

    .line 41
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    return-object p0
.end method

.method public static final l(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I
    .locals 1

    .line 1
    sget-object v0, Ljb1/t$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    sget p0, Lpb/k;->day_short_2:I

    .line 22
    .line 23
    return p0

    .line 24
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p0

    .line 30
    :cond_1
    sget p0, Lpb/k;->days_in_case_caps:I

    .line 31
    .line 32
    return p0

    .line 33
    :cond_2
    sget p0, Lpb/k;->day_short:I

    .line 34
    .line 35
    return p0

    .line 36
    :cond_3
    sget p0, Lpb/k;->day_short:I

    .line 37
    .line 38
    return p0
.end method

.method public static final m(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->TRANSPARENT_VERTICAL:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->hours_in_case_caps:I

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    const/4 p0, 0x0

    .line 9
    return p0
.end method

.method public static final n(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->TRANSPARENT_VERTICAL:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->minutes_in_case_caps:I

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    const/4 p0, 0x0

    .line 9
    return p0
.end method

.method public static final o(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->TRANSPARENT_VERTICAL:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->seconds_in_case_caps:I

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    const/4 p0, 0x0

    .line 9
    return p0
.end method

.method public static final p(ILHX0/e;)Ljava/lang/String;
    .locals 2

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->tournament_top_game:I

    .line 6
    .line 7
    new-array v0, v1, [Ljava/lang/Object;

    .line 8
    .line 9
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    return-object p0

    .line 14
    :cond_0
    sget p0, Lpb/k;->tournament_top_games:I

    .line 15
    .line 16
    new-array v0, v1, [Ljava/lang/Object;

    .line 17
    .line 18
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    return-object p0
.end method

.method public static final q(Li81/a;)Z
    .locals 5

    .line 1
    invoke-virtual {p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->PROVIDER:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x1

    .line 9
    const/4 v4, 0x3

    .line 10
    if-ne v0, v1, :cond_1

    .line 11
    .line 12
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object p0

    .line 20
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 21
    .line 22
    .line 23
    move-result p0

    .line 24
    if-le p0, v4, :cond_0

    .line 25
    .line 26
    return v3

    .line 27
    :cond_0
    return v2

    .line 28
    :cond_1
    invoke-virtual {p0}, Li81/a;->i()Lo81/a;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, Lo81/a;->a()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 37
    .line 38
    .line 39
    move-result p0

    .line 40
    if-le p0, v4, :cond_2

    .line 41
    .line 42
    return v3

    .line 43
    :cond_2
    return v2
.end method

.method public static final r(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;ILjava/lang/String;LO21/b;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;Ljava/util/Locale;)Lkb1/n;
    .locals 1
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkb1/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LO21/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0, p2, p1, p9, p12}, Ljb1/t;->a(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Ljava/util/Locale;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p9

    .line 9
    invoke-interface {v0, p9}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 10
    .line 11
    .line 12
    invoke-static {p0, p7}, Ljb1/t;->h(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object p7

    .line 16
    invoke-interface {v0, p7}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 17
    .line 18
    .line 19
    invoke-static {p0, p2, p10}, Ljb1/t;->c(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p7

    .line 23
    invoke-interface {v0, p7}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    invoke-static {p0, p2, p1, p8}, Ljb1/t;->b(Li81/a;LHX0/e;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;)Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 31
    .line 32
    .line 33
    invoke-virtual {p3}, Lkb1/m;->b()Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-static {p1, p2, p4}, Ljb1/t;->k(Ljava/util/List;LHX0/e;I)Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 42
    .line 43
    .line 44
    invoke-static {p0, p5, p2, p12}, Ljb1/t;->g(Li81/a;Ljava/lang/String;LHX0/e;Ljava/util/Locale;)Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 49
    .line 50
    .line 51
    invoke-static {p0, p11}, Ljb1/t;->f(Li81/a;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;)Ljava/util/List;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 56
    .line 57
    .line 58
    invoke-static {p0, p2, p6}, Ljb1/t;->e(Li81/a;LHX0/e;LO21/b;)Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 63
    .line 64
    .line 65
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    invoke-virtual {p0}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 70
    .line 71
    .line 72
    move-result-object p2

    .line 73
    sget-object p3, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 74
    .line 75
    invoke-virtual {p0}, Li81/a;->j()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    invoke-static {p2, p3, p0}, Lh81/c;->a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Z

    .line 80
    .line 81
    .line 82
    move-result p0

    .line 83
    new-instance p2, Lkb1/n;

    .line 84
    .line 85
    invoke-direct {p2, p1, p0}, Lkb1/n;-><init>(Ljava/util/List;Z)V

    .line 86
    .line 87
    .line 88
    return-object p2
.end method
