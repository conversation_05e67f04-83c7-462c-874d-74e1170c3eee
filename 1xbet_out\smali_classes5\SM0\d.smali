.class public final synthetic LSM0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroid/view/LayoutInflater;

    check-cast p2, Landroid/view/ViewGroup;

    invoke-static {p1, p2}, Lorg/xbet/statistic/stage/impl/stagetable/presentation/common/viewholder/FooterViewHolderKt;->a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LpM0/t;

    move-result-object p1

    return-object p1
.end method
