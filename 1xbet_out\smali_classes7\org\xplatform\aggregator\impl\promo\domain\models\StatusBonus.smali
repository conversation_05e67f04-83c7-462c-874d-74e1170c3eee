.class public final enum Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\r\n\u0002\u0010\u0008\n\u0000\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0006\u0010\u000e\u001a\u00020\u000fj\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\r\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "ACTIVE",
        "READY",
        "ACCOUNT_EXPECTED",
        "DELETE",
        "DELETED_BY_OPERATOR",
        "INTERRUPT",
        "EXPIRED",
        "PAID",
        "AWAITING_BY_OPERATOR",
        "UNKNOWN",
        "key",
        "",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

.field public static final enum ACCOUNT_EXPECTED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "3"
    .end annotation
.end field

.field public static final enum ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "1"
    .end annotation
.end field

.field public static final enum AWAITING_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "9"
    .end annotation
.end field

.field public static final enum DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "4"
    .end annotation
.end field

.field public static final enum DELETED_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "5"
    .end annotation
.end field

.field public static final enum EXPIRED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "7"
    .end annotation
.end field

.field public static final enum INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "6"
    .end annotation
.end field

.field public static final enum PAID:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "8"
    .end annotation
.end field

.field public static final enum READY:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "2"
    .end annotation
.end field

.field public static final enum UNKNOWN:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "0"
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 2
    .line 3
    const-string v1, "ACTIVE"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 12
    .line 13
    const-string v1, "READY"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->READY:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 20
    .line 21
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 22
    .line 23
    const-string v1, "ACCOUNT_EXPECTED"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACCOUNT_EXPECTED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 32
    .line 33
    const-string v1, "DELETE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 40
    .line 41
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 42
    .line 43
    const-string v1, "DELETED_BY_OPERATOR"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETED_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 50
    .line 51
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 52
    .line 53
    const-string v1, "INTERRUPT"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 62
    .line 63
    const-string v1, "EXPIRED"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->EXPIRED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 70
    .line 71
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 72
    .line 73
    const-string v1, "PAID"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->PAID:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 82
    .line 83
    const-string v1, "AWAITING_BY_OPERATOR"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->AWAITING_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 91
    .line 92
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 93
    .line 94
    const-string v1, "UNKNOWN"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->UNKNOWN:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 102
    .line 103
    invoke-static {}, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->a()[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->$VALUES:[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 108
    .line 109
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    sput-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->$ENTRIES:Lkotlin/enums/a;

    .line 114
    .line 115
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .locals 3

    .line 1
    const/16 v0, 0xa

    new-array v0, v0, [Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->READY:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACCOUNT_EXPECTED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETED_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->EXPIRED:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->PAID:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->AWAITING_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->UNKNOWN:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->$VALUES:[Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final key()I
    .locals 2

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const/4 v0, 0x0

    .line 19
    return v0

    .line 20
    :pswitch_1
    const/16 v0, 0x9

    .line 21
    .line 22
    return v0

    .line 23
    :pswitch_2
    const/16 v0, 0x8

    .line 24
    .line 25
    return v0

    .line 26
    :pswitch_3
    const/4 v0, 0x7

    .line 27
    return v0

    .line 28
    :pswitch_4
    const/4 v0, 0x6

    .line 29
    return v0

    .line 30
    :pswitch_5
    const/4 v0, 0x5

    .line 31
    return v0

    .line 32
    :pswitch_6
    const/4 v0, 0x4

    .line 33
    return v0

    .line 34
    :pswitch_7
    const/4 v0, 0x3

    .line 35
    return v0

    .line 36
    :pswitch_8
    const/4 v0, 0x2

    .line 37
    return v0

    .line 38
    :pswitch_9
    const/4 v0, 0x1

    .line 39
    return v0

    .line 40
    nop

    .line 41
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
