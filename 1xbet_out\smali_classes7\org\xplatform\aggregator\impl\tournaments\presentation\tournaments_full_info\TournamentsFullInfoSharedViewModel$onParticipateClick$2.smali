.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info.TournamentsFullInfoSharedViewModel$onParticipateClick$2"
    f = "TournamentsFullInfoSharedViewModel.kt"
    l = {
        0x255
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->w4(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

.field final synthetic $screenName:Ljava/lang/String;

.field final synthetic $tournamentId:J

.field final synthetic $tournamentTitle:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
            "J",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentId:J

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->X3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentId:J

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->Q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lw81/g;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentId:J

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 36
    .line 37
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->label:I

    .line 38
    .line 39
    invoke-interface {p1, v3, v4, v1, p0}, Lw81/g;->a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-ne p1, v0, :cond_2

    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_2
    :goto_0
    check-cast p1, Lh81/b;

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 49
    .line 50
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->Z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lh81/b;)Z

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    if-eqz v0, :cond_3

    .line 55
    .line 56
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 57
    .line 58
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentId:J

    .line 59
    .line 60
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    .line 61
    .line 62
    invoke-static {v0, v1, v2, p1, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLh81/b;Ljava/lang/String;)V

    .line 63
    .line 64
    .line 65
    :cond_3
    instance-of v0, p1, Lh81/b$g;

    .line 66
    .line 67
    if-eqz v0, :cond_4

    .line 68
    .line 69
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 70
    .line 71
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->O3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LwX0/C;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    if-eqz p1, :cond_b

    .line 80
    .line 81
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 82
    .line 83
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentId:J

    .line 84
    .line 85
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 86
    .line 87
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    .line 88
    .line 89
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    .line 90
    .line 91
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/A;

    .line 92
    .line 93
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/A;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    invoke-virtual {p1, v0}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 97
    .line 98
    .line 99
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 100
    .line 101
    goto/16 :goto_1

    .line 102
    .line 103
    :cond_4
    instance-of v0, p1, Lh81/b$c;

    .line 104
    .line 105
    const/4 v1, 0x0

    .line 106
    if-eqz v0, :cond_5

    .line 107
    .line 108
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 109
    .line 110
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->P3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lkotlinx/coroutines/flow/V;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 119
    .line 120
    .line 121
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 122
    .line 123
    sget-object v0, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 124
    .line 125
    invoke-virtual {p1, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->E4(Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Z)V

    .line 126
    .line 127
    .line 128
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 129
    .line 130
    goto/16 :goto_1

    .line 131
    .line 132
    :cond_5
    instance-of v0, p1, Lh81/b$a;

    .line 133
    .line 134
    if-eqz v0, :cond_6

    .line 135
    .line 136
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 137
    .line 138
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->D4()V

    .line 139
    .line 140
    .line 141
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 142
    .line 143
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;

    .line 148
    .line 149
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 150
    .line 151
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 152
    .line 153
    .line 154
    move-result-object v2

    .line 155
    sget v3, Lpb/k;->app_win_congratulations:I

    .line 156
    .line 157
    new-array v4, v1, [Ljava/lang/Object;

    .line 158
    .line 159
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v2

    .line 163
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 164
    .line 165
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 166
    .line 167
    .line 168
    move-result-object v3

    .line 169
    sget v4, Lpb/k;->tournamnet_enrolled_success:I

    .line 170
    .line 171
    new-array v5, v1, [Ljava/lang/Object;

    .line 172
    .line 173
    invoke-interface {v3, v4, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v3

    .line 177
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 178
    .line 179
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 180
    .line 181
    .line 182
    move-result-object v4

    .line 183
    sget v5, Lpb/k;->ok_new:I

    .line 184
    .line 185
    new-array v1, v1, [Ljava/lang/Object;

    .line 186
    .line 187
    invoke-interface {v4, v5, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 188
    .line 189
    .line 190
    move-result-object v1

    .line 191
    sget-object v4, Lorg/xbet/uikit/components/dialog/AlertType;->SUCCESS:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 192
    .line 193
    invoke-direct {v0, v2, v3, v1, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 194
    .line 195
    .line 196
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 197
    .line 198
    .line 199
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 200
    .line 201
    goto/16 :goto_1

    .line 202
    .line 203
    :cond_6
    instance-of v0, p1, Lh81/b$b;

    .line 204
    .line 205
    if-eqz v0, :cond_8

    .line 206
    .line 207
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 208
    .line 209
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 210
    .line 211
    .line 212
    move-result-object v0

    .line 213
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 214
    .line 215
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 216
    .line 217
    .line 218
    move-result-object v2

    .line 219
    sget v3, Lpb/k;->tournamenet_dialor_title:I

    .line 220
    .line 221
    new-array v4, v1, [Ljava/lang/Object;

    .line 222
    .line 223
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 224
    .line 225
    .line 226
    move-result-object v2

    .line 227
    check-cast p1, Lh81/b$b;

    .line 228
    .line 229
    invoke-virtual {p1}, Lh81/b$b;->b()Ljava/lang/String;

    .line 230
    .line 231
    .line 232
    move-result-object p1

    .line 233
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 234
    .line 235
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 236
    .line 237
    .line 238
    move-result v4

    .line 239
    if-nez v4, :cond_7

    .line 240
    .line 241
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 242
    .line 243
    .line 244
    move-result-object p1

    .line 245
    sget v3, Lpb/k;->unknown_service_error:I

    .line 246
    .line 247
    new-array v4, v1, [Ljava/lang/Object;

    .line 248
    .line 249
    invoke-interface {p1, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 250
    .line 251
    .line 252
    move-result-object p1

    .line 253
    :cond_7
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 254
    .line 255
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 256
    .line 257
    .line 258
    move-result-object v3

    .line 259
    sget v4, Lpb/k;->ok_new:I

    .line 260
    .line 261
    new-array v1, v1, [Ljava/lang/Object;

    .line 262
    .line 263
    invoke-interface {v3, v4, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 264
    .line 265
    .line 266
    move-result-object v1

    .line 267
    sget-object v3, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 268
    .line 269
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;

    .line 270
    .line 271
    invoke-direct {v4, v2, p1, v1, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 272
    .line 273
    .line 274
    invoke-virtual {v0, v4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 275
    .line 276
    .line 277
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 278
    .line 279
    goto :goto_1

    .line 280
    :cond_8
    instance-of v0, p1, Lh81/b$f;

    .line 281
    .line 282
    if-eqz v0, :cond_9

    .line 283
    .line 284
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 285
    .line 286
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 287
    .line 288
    .line 289
    move-result-object p1

    .line 290
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;

    .line 291
    .line 292
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 293
    .line 294
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 295
    .line 296
    .line 297
    move-result-object v2

    .line 298
    sget v3, Lpb/k;->tournamenet_dialor_title:I

    .line 299
    .line 300
    new-array v4, v1, [Ljava/lang/Object;

    .line 301
    .line 302
    invoke-interface {v2, v3, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 303
    .line 304
    .line 305
    move-result-object v2

    .line 306
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 307
    .line 308
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 309
    .line 310
    .line 311
    move-result-object v3

    .line 312
    sget v4, Lpb/k;->unknown_service_error:I

    .line 313
    .line 314
    new-array v5, v1, [Ljava/lang/Object;

    .line 315
    .line 316
    invoke-interface {v3, v4, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 317
    .line 318
    .line 319
    move-result-object v3

    .line 320
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 321
    .line 322
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 323
    .line 324
    .line 325
    move-result-object v4

    .line 326
    sget v5, Lpb/k;->ok_new:I

    .line 327
    .line 328
    new-array v1, v1, [Ljava/lang/Object;

    .line 329
    .line 330
    invoke-interface {v4, v5, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 331
    .line 332
    .line 333
    move-result-object v1

    .line 334
    sget-object v4, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 335
    .line 336
    invoke-direct {v0, v2, v3, v1, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 337
    .line 338
    .line 339
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 340
    .line 341
    .line 342
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 343
    .line 344
    goto :goto_1

    .line 345
    :cond_9
    instance-of v0, p1, Lh81/b$e;

    .line 346
    .line 347
    if-nez v0, :cond_c

    .line 348
    .line 349
    instance-of p1, p1, Lh81/b$d;

    .line 350
    .line 351
    if-eqz p1, :cond_a

    .line 352
    .line 353
    new-instance p1, Lkb1/F$b;

    .line 354
    .line 355
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 356
    .line 357
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 358
    .line 359
    .line 360
    move-result-object v0

    .line 361
    invoke-direct {p1, v0}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 362
    .line 363
    .line 364
    goto :goto_1

    .line 365
    :cond_a
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 366
    .line 367
    :cond_b
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 368
    .line 369
    return-object p1

    .line 370
    :cond_c
    new-instance p1, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;

    .line 371
    .line 372
    invoke-direct {p1}, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;-><init>()V

    .line 373
    .line 374
    .line 375
    throw p1
.end method
