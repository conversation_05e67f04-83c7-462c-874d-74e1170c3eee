.class public final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$a;,
        Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0018\u0000 D2\u00020\u0001:\u0001EB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0015\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0003J\u000f\u0010\u0016\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0003J\u000f\u0010\u0017\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0003J\u0017\u0010\u0019\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0003J\u000f\u0010\u001c\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0003J\u0019\u0010\u001f\u001a\u00020\u00062\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0016\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0019\u0010!\u001a\u00020\u00062\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0014\u00a2\u0006\u0004\u0008!\u0010 J\u000f\u0010\"\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\"\u0010\u0003J\u000f\u0010#\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008#\u0010\u0003R\u001a\u0010)\u001a\u00020$8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001b\u00105\u001a\u0002008BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104R\u001b\u0010:\u001a\u0002068BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00102\u001a\u0004\u00088\u00109R+\u0010C\u001a\u00020;2\u0006\u0010<\u001a\u00020;8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008=\u0010>\u001a\u0004\u0008?\u0010@\"\u0004\u0008A\u0010B\u00a8\u0006F"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
        "state",
        "",
        "s3",
        "(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;)V",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;",
        "action",
        "r3",
        "(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;)V",
        "",
        "time",
        "q3",
        "(Ljava/lang/String;)V",
        "Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;",
        "menuType",
        "g3",
        "(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;)V",
        "N3",
        "O3",
        "v3",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;",
        "M3",
        "(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;)V",
        "w3",
        "t3",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "t2",
        "v2",
        "onResume",
        "",
        "i0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "Lv80/e;",
        "j0",
        "LRc/c;",
        "c3",
        "()Lv80/e;",
        "binding",
        "Ly80/c;",
        "k0",
        "Lkotlin/j;",
        "d3",
        "()Ly80/c;",
        "component",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
        "l0",
        "f3",
        "()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
        "viewModel",
        "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
        "<set-?>",
        "m0",
        "LeX0/j;",
        "e3",
        "()Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
        "L3",
        "(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)V",
        "menuSectionStart",
        "n0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final n0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic o0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:Z

.field public final j0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    .line 4
    .line 5
    const-string v2, "binding"

    .line 6
    .line 7
    const-string v3, "getBinding()Lorg/xbet/main_menu/impl/databinding/FragmentMainMenuContainerBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "menuSectionStart"

    .line 20
    .line 21
    const-string v5, "getMenuSectionStart()Lorg/xbet/main_menu/api/domain/models/MenuSectionType;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v3, 0x2

    .line 31
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v3, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v2, v3, v0

    .line 37
    .line 38
    sput-object v3, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->o0:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$a;

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    invoke-direct {v0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->n0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$a;

    .line 47
    .line 48
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    sput-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->b1:Ljava/lang/String;

    .line 53
    .line 54
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Ls80/b;->fragment_main_menu_container:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->i0:Z

    .line 8
    .line 9
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$binding$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$binding$2;

    .line 10
    .line 11
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->j0:LRc/c;

    .line 16
    .line 17
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/k;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/container/k;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 20
    .line 21
    .line 22
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 23
    .line 24
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->k0:Lkotlin/j;

    .line 29
    .line 30
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/m;

    .line 31
    .line 32
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/container/m;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 33
    .line 34
    .line 35
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$c;

    .line 36
    .line 37
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$c;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 38
    .line 39
    .line 40
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$d;

    .line 41
    .line 42
    invoke-direct {v3, v0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$d;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 43
    .line 44
    .line 45
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$3;

    .line 46
    .line 47
    invoke-direct {v0, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$3;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 48
    .line 49
    .line 50
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$4;

    .line 51
    .line 52
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    const-class v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 60
    .line 61
    invoke-static {v1}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$5;

    .line 66
    .line 67
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$5;-><init>(Lkotlin/j;)V

    .line 68
    .line 69
    .line 70
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$6;

    .line 71
    .line 72
    const/4 v5, 0x0

    .line 73
    invoke-direct {v4, v5, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$special$$inlined$savedStateViewModels$default$6;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 74
    .line 75
    .line 76
    invoke-static {p0, v1, v2, v4, v3}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->l0:Lkotlin/j;

    .line 81
    .line 82
    new-instance v0, LeX0/j;

    .line 83
    .line 84
    const-string v1, "MENU_SECTION_TAB_KEY"

    .line 85
    .line 86
    invoke-direct {v0, v1}, LeX0/j;-><init>(Ljava/lang/String;)V

    .line 87
    .line 88
    .line 89
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->m0:LeX0/j;

    .line 90
    .line 91
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->B3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final A3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic B2()Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->l3()Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static final B3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->M4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic C2()Landroidx/fragment/app/Fragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->p3()Landroidx/fragment/app/Fragment;

    move-result-object v0

    return-object v0
.end method

.method public static final C3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic D2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->x3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static final D3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic E2()Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->o3()Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static final E3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->E3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final F3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->G4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic G2()Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->k3()Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static final G3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->D4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic H2()Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->n3()Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic H3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->g3(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic I2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->P3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/ui_common/viewmodel/core/e;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->q3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic J2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->z3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->r3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic K2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->A3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->s3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic L2()Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->m3()Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic M2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->F3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic N2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->G3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final N3()V
    .locals 14

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->d3()Ly80/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ly80/c;->b()Lck/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->MAIN_MENU:Lorg/xbet/balance/model/BalanceScreenType;

    .line 10
    .line 11
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 12
    .line 13
    .line 14
    move-result-object v6

    .line 15
    const/16 v12, 0x2ae

    .line 16
    .line 17
    const/4 v13, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    const-string v10, "SELECT_BALANCE_REQUEST_KEY"

    .line 25
    .line 26
    const/4 v11, 0x0

    .line 27
    invoke-static/range {v1 .. v13}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static synthetic O2()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->j3()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic P2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Ly80/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->b3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Ly80/c;

    move-result-object p0

    return-object p0
.end method

.method public static final P3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/ui_common/viewmodel/core/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->d3()Ly80/c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Ly80/c;->a()Ly80/f;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static synthetic Q2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->i3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic R2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->u3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static synthetic S2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->D3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic T2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->y3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic U2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lv80/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic V2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->b1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic W2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->H3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic X2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Y2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Z2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->K3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic a3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->L3(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final b3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Ly80/c;
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Ly80/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Ly80/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Ly80/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 53
    .line 54
    .line 55
    move-result-object p0

    .line 56
    invoke-virtual {v2, p0}, Ly80/d;->a(LwX0/c;)Ly80/c;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    return-object p0

    .line 61
    :cond_3
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    new-instance v0, Ljava/lang/StringBuilder;

    .line 64
    .line 65
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 66
    .line 67
    .line 68
    const-string v2, "Cannot create dependency "

    .line 69
    .line 70
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 85
    .line 86
    .line 87
    throw p0
.end method

.method public static final h3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;->b1:Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->e3()Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {v0, p0}, Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment$a;->b(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static final i3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;->o0:Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->e3()Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {v0, p0}, Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment$a;->a(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static final j3()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final k3()Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final l3()Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final m3()Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final n3()Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final o3()Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;->n0:Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment$a;->a()Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public static final p3()Landroidx/fragment/app/Fragment;
    .locals 1

    .line 1
    new-instance v0, Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/fragment/app/Fragment;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final t3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/n;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/n;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 8
    .line 9
    .line 10
    const-string v2, "CHANGE_BALANCE_REQUEST_KEY"

    .line 11
    .line 12
    invoke-virtual {v0, v2, p0, v1}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final u3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 3

    .line 1
    const-string p1, "NEGATIVE_CLICK_REQUEST_KEY"

    .line 2
    .line 3
    invoke-virtual {p2, p1}, Landroid/os/BaseBundle;->containsKey(Ljava/lang/String;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->B4()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_0
    const-string p1, "CHANGE_BALANCE_REQUEST_KEY"

    .line 18
    .line 19
    invoke-virtual {p2, p1}, Landroid/os/Bundle;->getBoolean(Ljava/lang/String;)Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 24
    .line 25
    const/16 v1, 0x21

    .line 26
    .line 27
    const-string v2, "GET_BALANCE_REQUEST_KEY"

    .line 28
    .line 29
    if-lt v0, v1, :cond_1

    .line 30
    .line 31
    const-class v0, Lorg/xbet/balance/model/BalanceModel;

    .line 32
    .line 33
    invoke-static {p2, v2, v0}, Lcom/xbet/security/impl/presentation/phone/confirm/check/a;->a(Landroid/os/Bundle;Ljava/lang/String;Ljava/lang/Class;)Ljava/io/Serializable;

    .line 34
    .line 35
    .line 36
    move-result-object p2

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    invoke-virtual {p2, v2}, Landroid/os/Bundle;->getSerializable(Ljava/lang/String;)Ljava/io/Serializable;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    instance-of v0, p2, Lorg/xbet/balance/model/BalanceModel;

    .line 43
    .line 44
    if-nez v0, :cond_2

    .line 45
    .line 46
    const/4 p2, 0x0

    .line 47
    :cond_2
    check-cast p2, Lorg/xbet/balance/model/BalanceModel;

    .line 48
    .line 49
    :goto_0
    check-cast p2, Lorg/xbet/balance/model/BalanceModel;

    .line 50
    .line 51
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->h4(Lorg/xbet/balance/model/BalanceModel;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A4(Z)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method private final w3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/v;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/v;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 8
    .line 9
    .line 10
    const-string v2, "SELECT_BALANCE_REQUEST_KEY"

    .line 11
    .line 12
    invoke-virtual {v0, v2, p0, v1}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final x3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 2

    .line 1
    const-string v0, "SELECT_BALANCE_REQUEST_KEY"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    const-string p1, "RESULT_ON_ITEM_SELECTED_LISTENER_KEY"

    .line 11
    .line 12
    invoke-virtual {p2, p1}, Landroid/os/BaseBundle;->containsKey(Ljava/lang/String;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_3

    .line 17
    .line 18
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 19
    .line 20
    const/16 v1, 0x21

    .line 21
    .line 22
    if-lt v0, v1, :cond_1

    .line 23
    .line 24
    const-class v0, Lorg/xbet/balance/model/BalanceModel;

    .line 25
    .line 26
    invoke-static {p2, p1, v0}, Lcom/xbet/security/impl/presentation/phone/confirm/check/a;->a(Landroid/os/Bundle;Ljava/lang/String;Ljava/lang/Class;)Ljava/io/Serializable;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    goto :goto_0

    .line 31
    :cond_1
    invoke-virtual {p2, p1}, Landroid/os/Bundle;->getSerializable(Ljava/lang/String;)Ljava/io/Serializable;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    instance-of p2, p1, Lorg/xbet/balance/model/BalanceModel;

    .line 36
    .line 37
    if-nez p2, :cond_2

    .line 38
    .line 39
    const/4 p1, 0x0

    .line 40
    :cond_2
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 41
    .line 42
    :goto_0
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 43
    .line 44
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z4(Lorg/xbet/balance/model/BalanceModel;)V

    .line 49
    .line 50
    .line 51
    :cond_3
    :goto_1
    return-void
.end method

.method public static synthetic y2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->h3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;

    move-result-object p0

    return-object p0
.end method

.method public static final y3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->C3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Landroid/view/View;)V

    return-void
.end method

.method public static final z3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->L4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final L3(Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->m0:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/j;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/io/Serializable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final M3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;)V
    .locals 10

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->d3()Ly80/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ly80/c;->b()Lck/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget v0, Lpb/k;->attention:I

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;->b()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    sget v0, Lpb/k;->ok_new:I

    .line 24
    .line 25
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    sget v0, Lpb/k;->cancel:I

    .line 30
    .line 31
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v6

    .line 35
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;->a()Lorg/xbet/balance/model/BalanceModel;

    .line 36
    .line 37
    .line 38
    move-result-object v8

    .line 39
    const-string v7, "CHANGE_BALANCE_REQUEST_KEY"

    .line 40
    .line 41
    const-string v9, "GET_BALANCE_REQUEST_KEY"

    .line 42
    .line 43
    invoke-interface/range {v1 .. v9}, Lck/a;->b(Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public final O3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->d3()Ly80/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v1}, Ly80/c;->e()LTZ0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 12
    .line 13
    sget v3, Lpb/k;->alert_call_pass_identification_title:I

    .line 14
    .line 15
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    sget v4, Lpb/k;->alert_call_pass_identification_message:I

    .line 20
    .line 21
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    sget v5, Lpb/k;->pass_identification:I

    .line 26
    .line 27
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v5

    .line 31
    sget v6, Lpb/k;->later:I

    .line 32
    .line 33
    invoke-virtual {v0, v6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 38
    .line 39
    const/16 v15, 0x3d0

    .line 40
    .line 41
    const/16 v16, 0x0

    .line 42
    .line 43
    const/4 v7, 0x0

    .line 44
    const-string v8, "NEED_IDENTIFICATION_TJ_DIALOG_REQUEST_KEY"

    .line 45
    .line 46
    const/4 v9, 0x0

    .line 47
    const/4 v10, 0x0

    .line 48
    const/4 v11, 0x0

    .line 49
    const/4 v12, 0x0

    .line 50
    const/4 v14, 0x0

    .line 51
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final c3()Lv80/e;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->j0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lv80/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final d3()Ly80/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ly80/c;

    .line 8
    .line 9
    return-object v0
.end method

.method public final e3()Lorg/xbet/main_menu/api/domain/models/MenuSectionType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->m0:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/j;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/io/Serializable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 13
    .line 14
    return-object v0
.end method

.method public final f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final g3(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;)V
    .locals 6

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    packed-switch p1, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/j;

    .line 13
    .line 14
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/j;-><init>()V

    .line 15
    .line 16
    .line 17
    const-string v0, ""

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :pswitch_0
    const-class p1, Lorg/xbet/main_menu/impl/presentation/list/line/ListLineItemsFragment;

    .line 21
    .line 22
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/i;

    .line 27
    .line 28
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/i;-><init>()V

    .line 29
    .line 30
    .line 31
    goto :goto_0

    .line 32
    :pswitch_1
    const-class p1, Lorg/xbet/main_menu/impl/presentation/title_grid_menu/TitledGridCardItemsFragment;

    .line 33
    .line 34
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/h;

    .line 39
    .line 40
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/h;-><init>()V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :pswitch_2
    const-class p1, Lorg/xbet/main_menu/impl/presentation/accordion_divided_menu/AccordionDividedLineItemsFragment;

    .line 45
    .line 46
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/g;

    .line 51
    .line 52
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/g;-><init>()V

    .line 53
    .line 54
    .line 55
    goto :goto_0

    .line 56
    :pswitch_3
    const-class p1, Lorg/xbet/main_menu/impl/presentation/accordion_grid_compact_menu/AccordeonGridCompactItemsFragment;

    .line 57
    .line 58
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/f;

    .line 63
    .line 64
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/f;-><init>()V

    .line 65
    .line 66
    .line 67
    goto :goto_0

    .line 68
    :pswitch_4
    const-class p1, Lorg/xbet/main_menu/impl/presentation/accordion_grid_menu/AccordionGridItemsFragment;

    .line 69
    .line 70
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/e;

    .line 75
    .line 76
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/e;-><init>()V

    .line 77
    .line 78
    .line 79
    goto :goto_0

    .line 80
    :pswitch_5
    const-class p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 81
    .line 82
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/d;

    .line 87
    .line 88
    invoke-direct {p1}, Lorg/xbet/main_menu/impl/presentation/container/d;-><init>()V

    .line 89
    .line 90
    .line 91
    goto :goto_0

    .line 92
    :pswitch_6
    const-class p1, Lorg/xbet/main_menu/impl/presentation/tabbed_grid_menu/TabbedGridCardItemsFragment;

    .line 93
    .line 94
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/c;

    .line 99
    .line 100
    invoke-direct {p1, p0}, Lorg/xbet/main_menu/impl/presentation/container/c;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 101
    .line 102
    .line 103
    goto :goto_0

    .line 104
    :pswitch_7
    const-class p1, Lorg/xbet/main_menu/impl/presentation/tabbed_menu/TabbedLineItemsFragment;

    .line 105
    .line 106
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object v0

    .line 110
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/b;

    .line 111
    .line 112
    invoke-direct {p1, p0}, Lorg/xbet/main_menu/impl/presentation/container/b;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 113
    .line 114
    .line 115
    :goto_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    sget v2, Ls80/a;->menuFragmentContainer:I

    .line 120
    .line 121
    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->A0()I

    .line 122
    .line 123
    .line 124
    move-result v3

    .line 125
    const/4 v4, 0x0

    .line 126
    invoke-static {v4, v3}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 127
    .line 128
    .line 129
    move-result-object v3

    .line 130
    new-instance v4, Ljava/util/ArrayList;

    .line 131
    .line 132
    const/16 v5, 0xa

    .line 133
    .line 134
    invoke-static {v3, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 135
    .line 136
    .line 137
    move-result v5

    .line 138
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 139
    .line 140
    .line 141
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 142
    .line 143
    .line 144
    move-result-object v3

    .line 145
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 146
    .line 147
    .line 148
    move-result v5

    .line 149
    if-eqz v5, :cond_0

    .line 150
    .line 151
    move-object v5, v3

    .line 152
    check-cast v5, Lkotlin/collections/L;

    .line 153
    .line 154
    invoke-virtual {v5}, Lkotlin/collections/L;->b()I

    .line 155
    .line 156
    .line 157
    move-result v5

    .line 158
    invoke-virtual {v1, v5}, Landroidx/fragment/app/FragmentManager;->z0(I)Landroidx/fragment/app/FragmentManager$k;

    .line 159
    .line 160
    .line 161
    move-result-object v5

    .line 162
    invoke-interface {v5}, Landroidx/fragment/app/FragmentManager$k;->getName()Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object v5

    .line 166
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 167
    .line 168
    .line 169
    goto :goto_1

    .line 170
    :cond_0
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 171
    .line 172
    .line 173
    move-result-object v3

    .line 174
    :cond_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 175
    .line 176
    .line 177
    move-result v4

    .line 178
    if-eqz v4, :cond_2

    .line 179
    .line 180
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v4

    .line 184
    move-object v5, v4

    .line 185
    check-cast v5, Ljava/lang/String;

    .line 186
    .line 187
    invoke-static {v5, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move-result v5

    .line 191
    if-eqz v5, :cond_1

    .line 192
    .line 193
    goto :goto_2

    .line 194
    :cond_2
    const/4 v4, 0x0

    .line 195
    :goto_2
    check-cast v4, Ljava/lang/String;

    .line 196
    .line 197
    invoke-virtual {v1}, Landroidx/fragment/app/FragmentManager;->r()Landroidx/fragment/app/N;

    .line 198
    .line 199
    .line 200
    move-result-object v3

    .line 201
    const/4 v5, 0x1

    .line 202
    invoke-static {v3, v5}, LXW0/g;->a(Landroidx/fragment/app/N;Z)V

    .line 203
    .line 204
    .line 205
    if-nez v4, :cond_3

    .line 206
    .line 207
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 208
    .line 209
    .line 210
    move-result-object p1

    .line 211
    check-cast p1, Landroidx/fragment/app/Fragment;

    .line 212
    .line 213
    invoke-virtual {v3, v2, p1, v0}, Landroidx/fragment/app/N;->t(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 214
    .line 215
    .line 216
    invoke-virtual {v3, v0}, Landroidx/fragment/app/N;->g(Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 217
    .line 218
    .line 219
    goto :goto_3

    .line 220
    :cond_3
    invoke-virtual {v1, v0}, Landroidx/fragment/app/FragmentManager;->r0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    .line 221
    .line 222
    .line 223
    move-result-object p1

    .line 224
    if-eqz p1, :cond_4

    .line 225
    .line 226
    invoke-virtual {v3, v2, p1, v0}, Landroidx/fragment/app/N;->t(ILandroidx/fragment/app/Fragment;Ljava/lang/String;)Landroidx/fragment/app/N;

    .line 227
    .line 228
    .line 229
    :cond_4
    :goto_3
    invoke-virtual {v3}, Landroidx/fragment/app/N;->j()I

    .line 230
    .line 231
    .line 232
    return-void

    .line 233
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->w3()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->t3()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->v3()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final q3(Ljava/lang/String;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lv80/e;->h:LPW0/K;

    .line 6
    .line 7
    iget-object v0, v0, LPW0/K;->b:Landroid/widget/TextView;

    .line 8
    .line 9
    sget v1, Lpb/k;->session_timer_title:I

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    new-array v2, v2, [Ljava/lang/Object;

    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    aput-object p1, v2, v3

    .line 16
    .line 17
    invoke-virtual {p0, v1, v2}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->i0:Z

    .line 2
    .line 3
    return v0
.end method

.method public final r3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;)V
    .locals 13

    .line 1
    const/4 v0, 0x0

    .line 2
    instance-of v1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;

    .line 3
    .line 4
    if-eqz v1, :cond_0

    .line 5
    .line 6
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;

    .line 7
    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->M3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$c;)V

    .line 9
    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    instance-of v1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$a;

    .line 13
    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 21
    .line 22
    const-string v2, "UPDATED_BALANCE_KEY"

    .line 23
    .line 24
    invoke-static {v2, v1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    const/4 v3, 0x1

    .line 29
    new-array v3, v3, [Lkotlin/Pair;

    .line 30
    .line 31
    aput-object v1, v3, v0

    .line 32
    .line 33
    invoke-static {v3}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p1, v2, v0}, Landroidx/fragment/app/FragmentManager;->K1(Ljava/lang/String;Landroid/os/Bundle;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_1
    instance-of v1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$d;

    .line 42
    .line 43
    if-eqz v1, :cond_2

    .line 44
    .line 45
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->N3()V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_2
    instance-of v1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;

    .line 50
    .line 51
    if-eqz v1, :cond_4

    .line 52
    .line 53
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    iget-object v1, v1, Lv80/e;->g:Landroid/widget/FrameLayout;

    .line 58
    .line 59
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;

    .line 60
    .line 61
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;->a()Z

    .line 62
    .line 63
    .line 64
    move-result p1

    .line 65
    if-eqz p1, :cond_3

    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_3
    const/16 v0, 0x8

    .line 69
    .line 70
    :goto_0
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 71
    .line 72
    .line 73
    return-void

    .line 74
    :cond_4
    instance-of v0, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$e;

    .line 75
    .line 76
    if-eqz v0, :cond_5

    .line 77
    .line 78
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->d3()Ly80/c;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    invoke-interface {v0}, Ly80/c;->d()LzX0/k;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    new-instance v2, Ly01/g;

    .line 87
    .line 88
    sget-object v3, Ly01/i$c;->a:Ly01/i$c;

    .line 89
    .line 90
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$e;

    .line 91
    .line 92
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$e;->a()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v4

    .line 96
    const/16 v9, 0x3c

    .line 97
    .line 98
    const/4 v10, 0x0

    .line 99
    const/4 v5, 0x0

    .line 100
    const/4 v6, 0x0

    .line 101
    const/4 v7, 0x0

    .line 102
    const/4 v8, 0x0

    .line 103
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 104
    .line 105
    .line 106
    const/16 v11, 0x1fc

    .line 107
    .line 108
    const/4 v12, 0x0

    .line 109
    const/4 v4, 0x0

    .line 110
    const/4 v6, 0x0

    .line 111
    const/4 v7, 0x0

    .line 112
    const/4 v9, 0x0

    .line 113
    move-object v3, p0

    .line 114
    invoke-static/range {v1 .. v12}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 115
    .line 116
    .line 117
    return-void

    .line 118
    :cond_5
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$f;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$f;

    .line 119
    .line 120
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 121
    .line 122
    .line 123
    move-result p1

    .line 124
    if-eqz p1, :cond_6

    .line 125
    .line 126
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->O3()V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 131
    .line 132
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 133
    .line 134
    .line 135
    throw p1
.end method

.method public final s3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lv80/e;->h:LPW0/K;

    .line 6
    .line 7
    invoke-virtual {v0}, LPW0/K;->b()Landroid/widget/FrameLayout;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->k()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    const/16 v2, 0x8

    .line 16
    .line 17
    const/4 v3, 0x0

    .line 18
    if-eqz v1, :cond_0

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/16 v1, 0x8

    .line 23
    .line 24
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget-object v0, v0, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 32
    .line 33
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->c()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_1

    .line 38
    .line 39
    const/4 v1, 0x0

    .line 40
    goto :goto_1

    .line 41
    :cond_1
    const/16 v1, 0x8

    .line 42
    .line 43
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->c()Z

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    if-eqz v0, :cond_2

    .line 51
    .line 52
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iget-object v0, v0, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 57
    .line 58
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->e()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountTitle(Ljava/lang/CharSequence;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v0, v0, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 70
    .line 71
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->i()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->f()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    invoke-virtual {v0, v1, v4}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setBalanceValue(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 80
    .line 81
    .line 82
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    iget-object v0, v0, Lv80/e;->c:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 87
    .line 88
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->d()Z

    .line 89
    .line 90
    .line 91
    move-result v1

    .line 92
    if-eqz v1, :cond_3

    .line 93
    .line 94
    const/4 v1, 0x0

    .line 95
    goto :goto_2

    .line 96
    :cond_3
    const/16 v1, 0x8

    .line 97
    .line 98
    :goto_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 99
    .line 100
    .line 101
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    iget-object v0, v0, Lv80/e;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 106
    .line 107
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->d()Z

    .line 108
    .line 109
    .line 110
    move-result v1

    .line 111
    if-eqz v1, :cond_4

    .line 112
    .line 113
    const/4 v1, 0x0

    .line 114
    goto :goto_3

    .line 115
    :cond_4
    const/16 v1, 0x8

    .line 116
    .line 117
    :goto_3
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 118
    .line 119
    .line 120
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 125
    .line 126
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->j()Z

    .line 127
    .line 128
    .line 129
    move-result v1

    .line 130
    if-eqz v1, :cond_5

    .line 131
    .line 132
    const/4 v1, 0x0

    .line 133
    goto :goto_4

    .line 134
    :cond_5
    const/16 v1, 0x8

    .line 135
    .line 136
    :goto_4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 137
    .line 138
    .line 139
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->j()Z

    .line 140
    .line 141
    .line 142
    move-result v0

    .line 143
    if-eqz v0, :cond_b

    .line 144
    .line 145
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 150
    .line 151
    const-string v1, "message"

    .line 152
    .line 153
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    if-eqz v0, :cond_7

    .line 158
    .line 159
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->g()Z

    .line 160
    .line 161
    .line 162
    move-result v1

    .line 163
    if-eqz v1, :cond_6

    .line 164
    .line 165
    const/4 v2, 0x0

    .line 166
    :cond_6
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 167
    .line 168
    .line 169
    :cond_7
    if-eqz v0, :cond_8

    .line 170
    .line 171
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->h()Ljava/lang/Integer;

    .line 172
    .line 173
    .line 174
    move-result-object v1

    .line 175
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;->setCount(Ljava/lang/Integer;)V

    .line 176
    .line 177
    .line 178
    :cond_8
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 179
    .line 180
    .line 181
    move-result-object v0

    .line 182
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 183
    .line 184
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->o()Ljava/lang/String;

    .line 185
    .line 186
    .line 187
    move-result-object v1

    .line 188
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->setTitle(Ljava/lang/CharSequence;)V

    .line 189
    .line 190
    .line 191
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 196
    .line 197
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->n()Ljava/lang/String;

    .line 198
    .line 199
    .line 200
    move-result-object v1

    .line 201
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 202
    .line 203
    .line 204
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->m()Z

    .line 205
    .line 206
    .line 207
    move-result v0

    .line 208
    if-eqz v0, :cond_9

    .line 209
    .line 210
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 211
    .line 212
    .line 213
    move-result-object v0

    .line 214
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 215
    .line 216
    sget v1, Lpb/g;->ic_profile_new_year:I

    .line 217
    .line 218
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->setProfileIcon(I)V

    .line 219
    .line 220
    .line 221
    :cond_9
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 222
    .line 223
    .line 224
    move-result-object v0

    .line 225
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 226
    .line 227
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->j()Z

    .line 228
    .line 229
    .line 230
    move-result v1

    .line 231
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->j(Z)V

    .line 232
    .line 233
    .line 234
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 235
    .line 236
    .line 237
    move-result-object v0

    .line 238
    iget-object v0, v0, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 239
    .line 240
    const-string v1, "settings"

    .line 241
    .line 242
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;

    .line 243
    .line 244
    .line 245
    move-result-object v0

    .line 246
    if-eqz v0, :cond_a

    .line 247
    .line 248
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->l()Lorg/xbet/uikit/models/StateStatus;

    .line 249
    .line 250
    .line 251
    move-result-object p1

    .line 252
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/toolbar/base/components/DSNavigationBarButton;->c(Lorg/xbet/uikit/models/StateStatus;)V

    .line 253
    .line 254
    .line 255
    :cond_a
    return-void

    .line 256
    :cond_b
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 257
    .line 258
    .line 259
    move-result-object v0

    .line 260
    iget-object v0, v0, Lv80/e;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 261
    .line 262
    invoke-virtual {p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->o()Ljava/lang/String;

    .line 263
    .line 264
    .line 265
    move-result-object p1

    .line 266
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setTitle(Ljava/lang/CharSequence;)V

    .line 267
    .line 268
    .line 269
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v1, v1, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 8
    .line 9
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/a;

    .line 10
    .line 11
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/a;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 12
    .line 13
    .line 14
    const/4 v3, 0x0

    .line 15
    const/4 v4, 0x1

    .line 16
    invoke-static {v1, v3, v2, v4, v3}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    iget-object v1, v1, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 24
    .line 25
    sget-object v2, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_2000:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 26
    .line 27
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/l;

    .line 28
    .line 29
    invoke-direct {v5, v0}, Lorg/xbet/main_menu/impl/presentation/container/l;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v1, v2, v5}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setTopUpAccountClickListener(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    iget-object v1, v1, Lv80/e;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 40
    .line 41
    sget-object v2, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_1000:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 42
    .line 43
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/o;

    .line 44
    .line 45
    invoke-direct {v5, v0}, Lorg/xbet/main_menu/impl/presentation/container/o;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {v1, v2, v5}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setUpdateClickListener(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;)V

    .line 49
    .line 50
    .line 51
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iget-object v1, v1, Lv80/e;->c:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 56
    .line 57
    invoke-virtual {v1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getAuthorizationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/p;

    .line 62
    .line 63
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/p;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iget-object v1, v1, Lv80/e;->c:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 74
    .line 75
    invoke-virtual {v1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getRegistrationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/q;

    .line 80
    .line 81
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/q;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 82
    .line 83
    .line 84
    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 85
    .line 86
    .line 87
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    iget-object v1, v1, Lv80/e;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 92
    .line 93
    invoke-virtual {v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->k()V

    .line 94
    .line 95
    .line 96
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    iget-object v1, v1, Lv80/e;->i:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 101
    .line 102
    new-instance v5, LM01/c;

    .line 103
    .line 104
    sget-object v8, Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;->INACTIVE:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 105
    .line 106
    move-object v7, v8

    .line 107
    sget v8, LlZ0/h;->ic_glyph_settings_inactive:I

    .line 108
    .line 109
    new-instance v9, Lorg/xbet/main_menu/impl/presentation/container/r;

    .line 110
    .line 111
    invoke-direct {v9, v0}, Lorg/xbet/main_menu/impl/presentation/container/r;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 112
    .line 113
    .line 114
    const/16 v17, 0x7f0

    .line 115
    .line 116
    const/16 v18, 0x0

    .line 117
    .line 118
    const-string v6, "settings"

    .line 119
    .line 120
    const/4 v10, 0x0

    .line 121
    const/4 v11, 0x0

    .line 122
    const/4 v12, 0x0

    .line 123
    const/4 v13, 0x0

    .line 124
    const/4 v14, 0x0

    .line 125
    const/4 v15, 0x0

    .line 126
    const/16 v16, 0x0

    .line 127
    .line 128
    invoke-direct/range {v5 .. v18}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 129
    .line 130
    .line 131
    new-array v2, v4, [LM01/c;

    .line 132
    .line 133
    const/16 v20, 0x0

    .line 134
    .line 135
    aput-object v5, v2, v20

    .line 136
    .line 137
    invoke-static {v2}, Lkotlin/collections/v;->h([Ljava/lang/Object;)Ljava/util/ArrayList;

    .line 138
    .line 139
    .line 140
    move-result-object v2

    .line 141
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtons(Ljava/util/ArrayList;)V

    .line 142
    .line 143
    .line 144
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    iget-object v1, v1, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 149
    .line 150
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/s;

    .line 151
    .line 152
    invoke-direct {v2, v0}, Lorg/xbet/main_menu/impl/presentation/container/s;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 153
    .line 154
    .line 155
    invoke-static {v1, v3, v2, v4, v3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 156
    .line 157
    .line 158
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->c3()Lv80/e;

    .line 159
    .line 160
    .line 161
    move-result-object v1

    .line 162
    iget-object v1, v1, Lv80/e;->f:Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;

    .line 163
    .line 164
    new-instance v6, LM01/c;

    .line 165
    .line 166
    sget v9, LlZ0/h;->ic_glyph_message:I

    .line 167
    .line 168
    new-instance v10, Lorg/xbet/main_menu/impl/presentation/container/t;

    .line 169
    .line 170
    invoke-direct {v10, v0}, Lorg/xbet/main_menu/impl/presentation/container/t;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 171
    .line 172
    .line 173
    sget-object v14, Lorg/xbet/uikit/components/counter/CounterType;->RED:Lorg/xbet/uikit/components/counter/CounterType;

    .line 174
    .line 175
    const/16 v18, 0x770

    .line 176
    .line 177
    const/16 v19, 0x0

    .line 178
    .line 179
    move-object v8, v7

    .line 180
    const-string v7, "message"

    .line 181
    .line 182
    const/4 v12, 0x0

    .line 183
    const/16 v16, 0x0

    .line 184
    .line 185
    const/16 v17, 0x0

    .line 186
    .line 187
    invoke-direct/range {v6 .. v19}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 188
    .line 189
    .line 190
    move-object v2, v6

    .line 191
    move-object v7, v8

    .line 192
    new-instance v6, LM01/c;

    .line 193
    .line 194
    sget v9, LlZ0/h;->ic_glyph_settings_inactive:I

    .line 195
    .line 196
    new-instance v10, Lorg/xbet/main_menu/impl/presentation/container/u;

    .line 197
    .line 198
    invoke-direct {v10, v0}, Lorg/xbet/main_menu/impl/presentation/container/u;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V

    .line 199
    .line 200
    .line 201
    const/16 v18, 0x7f0

    .line 202
    .line 203
    const-string v7, "settings"

    .line 204
    .line 205
    const/4 v14, 0x0

    .line 206
    invoke-direct/range {v6 .. v19}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 207
    .line 208
    .line 209
    const/4 v3, 0x2

    .line 210
    new-array v3, v3, [LM01/c;

    .line 211
    .line 212
    aput-object v2, v3, v20

    .line 213
    .line 214
    aput-object v6, v3, v4

    .line 215
    .line 216
    invoke-static {v3}, Lkotlin/collections/v;->h([Ljava/lang/Object;)Ljava/util/ArrayList;

    .line 217
    .line 218
    .line 219
    move-result-object v2

    .line 220
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/toolbar/profile/DSNavigationBarProfile;->setNavigationBarButtons(Ljava/util/ArrayList;)V

    .line 221
    .line 222
    .line 223
    return-void
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->g4()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v11

    .line 27
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    move-object v5, v10

    .line 31
    invoke-direct/range {v2 .. v7}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v15, 0x3

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v12, 0x0

    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v14, v2

    .line 40
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    new-instance v11, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$2;

    .line 52
    .line 53
    invoke-direct {v11, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$2;-><init>(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 65
    .line 66
    move-object v7, v4

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/4 v5, 0x3

    .line 71
    const/4 v6, 0x0

    .line 72
    const/4 v2, 0x0

    .line 73
    const/4 v3, 0x0

    .line 74
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->m4()Lkotlinx/coroutines/flow/e;

    .line 82
    .line 83
    .line 84
    move-result-object v8

    .line 85
    new-instance v11, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$3;

    .line 86
    .line 87
    invoke-direct {v11, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$3;-><init>(Ljava/lang/Object;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 91
    .line 92
    .line 93
    move-result-object v9

    .line 94
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 99
    .line 100
    move-object v7, v4

    .line 101
    invoke-direct/range {v7 .. v12}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 102
    .line 103
    .line 104
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 105
    .line 106
    .line 107
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->l4()Lkotlinx/coroutines/flow/e;

    .line 112
    .line 113
    .line 114
    move-result-object v8

    .line 115
    new-instance v11, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$4;

    .line 116
    .line 117
    invoke-direct {v11, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$4;-><init>(Ljava/lang/Object;)V

    .line 118
    .line 119
    .line 120
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 121
    .line 122
    .line 123
    move-result-object v9

    .line 124
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 125
    .line 126
    .line 127
    move-result-object v1

    .line 128
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 129
    .line 130
    move-object v7, v4

    .line 131
    invoke-direct/range {v7 .. v12}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 132
    .line 133
    .line 134
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 135
    .line 136
    .line 137
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    invoke-virtual {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->k4()Lkotlinx/coroutines/flow/e;

    .line 142
    .line 143
    .line 144
    move-result-object v3

    .line 145
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    .line 146
    .line 147
    new-instance v6, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$5;

    .line 148
    .line 149
    invoke-direct {v6, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$5;-><init>(Ljava/lang/Object;)V

    .line 150
    .line 151
    .line 152
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 153
    .line 154
    .line 155
    move-result-object v4

    .line 156
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 157
    .line 158
    .line 159
    move-result-object v1

    .line 160
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 161
    .line 162
    const/4 v7, 0x0

    .line 163
    invoke-direct/range {v2 .. v7}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 164
    .line 165
    .line 166
    const/4 v11, 0x3

    .line 167
    const/4 v8, 0x0

    .line 168
    const/4 v9, 0x0

    .line 169
    move-object v7, v1

    .line 170
    move-object v10, v2

    .line 171
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 172
    .line 173
    .line 174
    return-void
.end method

.method public final v3()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$initNeedIdentificationTjDialogListener$1;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->f3()Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$initNeedIdentificationTjDialogListener$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    const-string v1, "NEED_IDENTIFICATION_TJ_DIALOG_REQUEST_KEY"

    .line 11
    .line 12
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method
