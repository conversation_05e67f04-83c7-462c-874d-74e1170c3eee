.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$showGiftsWithChips$3"
    f = "AggregatorGiftsViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6(IIILjava/util/List;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $allGiftsCount:I

.field final synthetic $bonusesCount:I

.field final synthetic $freeSpinsCount:I

.field final synthetic $giftsByType:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation
.end field

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;IIILjava/util/List;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "III",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$allGiftsCount:I

    iput p3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$bonusesCount:I

    iput p4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$freeSpinsCount:I

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$giftsByType:Ljava/util/List;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$allGiftsCount:I

    iget v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$bonusesCount:I

    iget v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$freeSpinsCount:I

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$giftsByType:Ljava/util/List;

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;IIILjava/util/List;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 12
    .line 13
    new-instance v0, Lha1/c;

    .line 14
    .line 15
    new-instance v1, Lga1/a;

    .line 16
    .line 17
    sget-object v2, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->ALL:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 18
    .line 19
    iget v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$allGiftsCount:I

    .line 20
    .line 21
    invoke-direct {v1, v2, v3}, Lga1/a;-><init>(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;I)V

    .line 22
    .line 23
    .line 24
    new-instance v2, Lga1/a;

    .line 25
    .line 26
    sget-object v3, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->BONUSES:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 27
    .line 28
    iget v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$bonusesCount:I

    .line 29
    .line 30
    invoke-direct {v2, v3, v4}, Lga1/a;-><init>(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;I)V

    .line 31
    .line 32
    .line 33
    new-instance v3, Lga1/a;

    .line 34
    .line 35
    sget-object v4, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->FREE_SPINS:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 36
    .line 37
    iget v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$freeSpinsCount:I

    .line 38
    .line 39
    invoke-direct {v3, v4, v5}, Lga1/a;-><init>(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;I)V

    .line 40
    .line 41
    .line 42
    const/4 v4, 0x3

    .line 43
    new-array v4, v4, [Lga1/a;

    .line 44
    .line 45
    const/4 v5, 0x0

    .line 46
    aput-object v1, v4, v5

    .line 47
    .line 48
    const/4 v1, 0x1

    .line 49
    aput-object v2, v4, v1

    .line 50
    .line 51
    const/4 v1, 0x2

    .line 52
    aput-object v3, v4, v1

    .line 53
    .line 54
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-direct {v0, v1}, Lha1/c;-><init>(Ljava/util/List;)V

    .line 59
    .line 60
    .line 61
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lha1/c;)V

    .line 62
    .line 63
    .line 64
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 65
    .line 66
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->P4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 71
    .line 72
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lha1/c;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-static {v0}, Lkotlin/collections/v;->r(Ljava/lang/Object;)Ljava/util/List;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;->$giftsByType:Ljava/util/List;

    .line 81
    .line 82
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1

    .line 92
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 93
    .line 94
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 95
    .line 96
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 97
    .line 98
    .line 99
    throw p1
.end method
