.class public final synthetic Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/b;->a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/b;->a:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;

    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->y2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)LGy0/d;

    move-result-object v0

    return-object v0
.end method
