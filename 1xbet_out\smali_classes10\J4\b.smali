.class public interface abstract LJ4/b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<TResult:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract onComplete(LJ4/f;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJ4/f<",
            "TTResult;>;)V"
        }
    .end annotation
.end method
