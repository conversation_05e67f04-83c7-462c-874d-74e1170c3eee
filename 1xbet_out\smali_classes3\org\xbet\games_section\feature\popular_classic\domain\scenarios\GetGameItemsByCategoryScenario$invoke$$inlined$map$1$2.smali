.class public final Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlinx/coroutines/flow/f;

.field public final synthetic b:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/flow/f;Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;->b:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto/16 :goto_4

    .line 45
    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$1:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast p1, Lg9/a;

    .line 57
    .line 58
    iget-object v2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$0:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast v2, Lkotlinx/coroutines/flow/f;

    .line 61
    .line 62
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    goto :goto_1

    .line 66
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    .line 70
    .line 71
    check-cast p1, Lg9/a;

    .line 72
    .line 73
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;->b:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    .line 74
    .line 75
    invoke-virtual {p1}, Lg9/a;->a()I

    .line 76
    .line 77
    .line 78
    move-result v5

    .line 79
    iput-object v2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$0:Ljava/lang/Object;

    .line 80
    .line 81
    iput-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$1:Ljava/lang/Object;

    .line 82
    .line 83
    iput v4, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->label:I

    .line 84
    .line 85
    invoke-static {p2, v5, v0}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->a(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;ILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object p2

    .line 89
    if-ne p2, v1, :cond_4

    .line 90
    .line 91
    goto :goto_3

    .line 92
    :cond_4
    :goto_1
    check-cast p2, Ljava/lang/Iterable;

    .line 93
    .line 94
    new-instance v4, Ljava/util/ArrayList;

    .line 95
    .line 96
    const/16 v5, 0xa

    .line 97
    .line 98
    invoke-static {p2, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 99
    .line 100
    .line 101
    move-result v5

    .line 102
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 103
    .line 104
    .line 105
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 106
    .line 107
    .line 108
    move-result-object p2

    .line 109
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 110
    .line 111
    .line 112
    move-result v5

    .line 113
    if-eqz v5, :cond_5

    .line 114
    .line 115
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object v5

    .line 119
    check-cast v5, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 120
    .line 121
    new-instance v6, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 122
    .line 123
    iget-object v7, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2;->b:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    .line 124
    .line 125
    invoke-static {v7}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->b(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;)Li8/j;

    .line 126
    .line 127
    .line 128
    move-result-object v7

    .line 129
    invoke-interface {v7}, Li8/j;->invoke()Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v7

    .line 133
    invoke-direct {v6, v5, v7}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Ljava/lang/String;)V

    .line 134
    .line 135
    .line 136
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    goto :goto_2

    .line 140
    :cond_5
    new-instance p2, Lf50/a;

    .line 141
    .line 142
    invoke-virtual {p1}, Lg9/a;->a()I

    .line 143
    .line 144
    .line 145
    move-result v5

    .line 146
    invoke-static {v5}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 147
    .line 148
    .line 149
    move-result-object v5

    .line 150
    invoke-virtual {p1}, Lg9/a;->b()Ljava/lang/String;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    invoke-direct {p2, v4, v5, p1}, Lf50/a;-><init>(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V

    .line 155
    .line 156
    .line 157
    const/4 p1, 0x0

    .line 158
    iput-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$0:Ljava/lang/Object;

    .line 159
    .line 160
    iput-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->L$1:Ljava/lang/Object;

    .line 161
    .line 162
    iput v3, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1$2$1;->label:I

    .line 163
    .line 164
    invoke-interface {v2, p2, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 165
    .line 166
    .line 167
    move-result-object p1

    .line 168
    if-ne p1, v1, :cond_6

    .line 169
    .line 170
    :goto_3
    return-object v1

    .line 171
    :cond_6
    :goto_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 172
    .line 173
    return-object p1
.end method
