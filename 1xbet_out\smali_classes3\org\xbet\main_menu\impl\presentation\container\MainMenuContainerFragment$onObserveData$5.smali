.class final synthetic Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$5;
.super Lkotlin/jvm/internal/AdaptedFunctionReference;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/AdaptedFunctionReference;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "handleMenuStyleTypeState(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;)V"

    const/4 v6, 0x4

    const/4 v1, 0x2

    const-class v3, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    const-string v4, "handleMenuStyleTypeState"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/AdaptedFunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment$onObserveData$5;->invoke(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lkotlin/jvm/internal/AdaptedFunctionReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    invoke-static {v0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->W2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
