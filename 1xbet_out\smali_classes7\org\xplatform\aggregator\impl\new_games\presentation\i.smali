.class public final synthetic Lorg/xplatform/aggregator/impl/new_games/presentation/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/i;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/i;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    check-cast p1, LN21/k;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->m3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
