.class public final LOD0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LOD0/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u00084\u0008\u0086\u0008\u0018\u0000 M2\u00020\u0001:\u0001+B\u00ab\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\n\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\u0008\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0010\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0008\u0012\u0006\u0010\u0012\u001a\u00020\u0008\u0012\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u0013\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001a\u001a\u00020\u000c\u0012\u0006\u0010\u001b\u001a\u00020\u0016\u0012\u000c\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001c0\u0013\u0012\u0006\u0010\u001e\u001a\u00020\u0002\u0012\u0006\u0010\u001f\u001a\u00020\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u00da\u0001\u0010\"\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\t\u001a\u00020\u00082\u0008\u0008\u0002\u0010\n\u001a\u00020\u00082\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u00082\u0008\u0008\u0002\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u0010\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u0011\u001a\u00020\u00082\u0008\u0008\u0002\u0010\u0012\u001a\u00020\u00082\u000e\u0008\u0002\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00132\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u00162\u0008\u0008\u0002\u0010\u0019\u001a\u00020\u00182\u0008\u0008\u0002\u0010\u001a\u001a\u00020\u000c2\u0008\u0008\u0002\u0010\u001b\u001a\u00020\u00162\u000e\u0008\u0002\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001c0\u00132\u0008\u0008\u0002\u0010\u001e\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u001f\u001a\u00020\u0002H\u00c6\u0001\u00a2\u0006\u0004\u0008\"\u0010#J\u0010\u0010$\u001a\u00020\u000cH\u00d6\u0001\u00a2\u0006\u0004\u0008$\u0010%J\u0010\u0010&\u001a\u00020\u0008H\u00d6\u0001\u00a2\u0006\u0004\u0008&\u0010\'J\u001a\u0010)\u001a\u00020\u00162\u0008\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008)\u0010*R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\"\u0010/\u001a\u0004\u00080\u00101R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105R\u0017\u0010\t\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u00080\u00106\u001a\u0004\u00087\u0010\'R\u0017\u0010\n\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u00084\u00106\u001a\u0004\u00088\u0010\'R\u0017\u0010\u000b\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u00087\u00106\u001a\u0004\u00089\u0010\'R\u0017\u0010\r\u001a\u00020\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u0008:\u0010;\u001a\u0004\u0008<\u0010%R\u0017\u0010\u000f\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008=\u0010>\u001a\u0004\u0008?\u0010@R\u0017\u0010\u0010\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008A\u0010>\u001a\u0004\u0008B\u0010@R\u0017\u0010\u0011\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008C\u00106\u001a\u0004\u0008A\u0010\'R\u0017\u0010\u0012\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008D\u00106\u001a\u0004\u0008C\u0010\'R\u001d\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00138\u0006\u00a2\u0006\u000c\n\u0004\u0008<\u0010E\u001a\u0004\u0008D\u0010FR\u0017\u0010\u0017\u001a\u00020\u00168\u0006\u00a2\u0006\u000c\n\u0004\u00088\u0010G\u001a\u0004\u0008H\u0010IR\u0017\u0010\u0019\u001a\u00020\u00188\u0006\u00a2\u0006\u000c\n\u0004\u00089\u0010J\u001a\u0004\u0008K\u0010LR\u0017\u0010\u001a\u001a\u00020\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u0008H\u0010;\u001a\u0004\u0008M\u0010%R\u0017\u0010\u001b\u001a\u00020\u00168\u0006\u00a2\u0006\u000c\n\u0004\u0008K\u0010G\u001a\u0004\u0008:\u0010IR\u001d\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001c0\u00138\u0006\u00a2\u0006\u000c\n\u0004\u0008-\u0010E\u001a\u0004\u0008N\u0010FR\u0017\u0010\u001e\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008?\u0010,\u001a\u0004\u0008O\u0010.R\u0017\u0010\u001f\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008B\u0010,\u001a\u0004\u0008=\u0010.\u00a8\u0006P"
    }
    d2 = {
        "LOD0/a;",
        "",
        "",
        "sportId",
        "Ll8/b$a;",
        "eventDate",
        "Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;",
        "eventStatusType",
        "",
        "eventSubStatusTypeId",
        "scoreTeamOne",
        "scoreTeamTwo",
        "",
        "score",
        "LND0/k;",
        "teamOne",
        "teamTwo",
        "redCardTeamOne",
        "redCardTeamTwo",
        "",
        "LND0/h;",
        "referees",
        "",
        "showScore",
        "Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;",
        "source",
        "tournamentTitle",
        "finished",
        "LND0/f;",
        "periods",
        "champId",
        "globalChampId",
        "<init>",
        "(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)V",
        "b",
        "(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)LOD0/a;",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "J",
        "q",
        "()J",
        "Ll8/b$a;",
        "d",
        "()Ll8/b$a;",
        "c",
        "Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;",
        "e",
        "()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;",
        "I",
        "f",
        "m",
        "n",
        "g",
        "Ljava/lang/String;",
        "l",
        "h",
        "LND0/k;",
        "r",
        "()LND0/k;",
        "i",
        "s",
        "j",
        "k",
        "Ljava/util/List;",
        "()Ljava/util/List;",
        "Z",
        "o",
        "()Z",
        "Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;",
        "p",
        "()Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;",
        "t",
        "getPeriods",
        "getChampId",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final t:LOD0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final u:LOD0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:J

.field public final b:Ll8/b$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LND0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LND0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:I

.field public final k:I

.field public final l:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LND0/h;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Z

.field public final n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Z

.field public final q:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LND0/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:J

.field public final s:J


# direct methods
.method static constructor <clinit>()V
    .locals 25

    .line 1
    new-instance v0, LOD0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LOD0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LOD0/a;->t:LOD0/a$a;

    .line 8
    .line 9
    new-instance v2, LOD0/a;

    .line 10
    .line 11
    const-wide/16 v0, 0x0

    .line 12
    .line 13
    invoke-static {v0, v1}, Ll8/b$a$c;->f(J)J

    .line 14
    .line 15
    .line 16
    move-result-wide v0

    .line 17
    invoke-static {v0, v1}, Ll8/b$a$c;->d(J)Ll8/b$a$c;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    sget-object v6, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->UNKNOWN:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 22
    .line 23
    sget-object v0, LND0/k;->f:LND0/k$a;

    .line 24
    .line 25
    invoke-virtual {v0}, LND0/k$a;->a()LND0/k;

    .line 26
    .line 27
    .line 28
    move-result-object v11

    .line 29
    invoke-virtual {v0}, LND0/k$a;->a()LND0/k;

    .line 30
    .line 31
    .line 32
    move-result-object v12

    .line 33
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object v15

    .line 37
    sget-object v17, Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;->EMPTY:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 38
    .line 39
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v20

    .line 43
    const-wide/16 v21, 0x0

    .line 44
    .line 45
    const-wide/16 v23, 0x0

    .line 46
    .line 47
    const-wide/16 v3, -0x1

    .line 48
    .line 49
    const/4 v7, -0x1

    .line 50
    const/4 v8, 0x0

    .line 51
    const/4 v9, 0x0

    .line 52
    const-string v10, ""

    .line 53
    .line 54
    const/4 v13, 0x0

    .line 55
    const/4 v14, 0x0

    .line 56
    const/16 v16, 0x0

    .line 57
    .line 58
    const-string v18, ""

    .line 59
    .line 60
    const/16 v19, 0x0

    .line 61
    .line 62
    invoke-direct/range {v2 .. v24}, LOD0/a;-><init>(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)V

    .line 63
    .line 64
    .line 65
    sput-object v2, LOD0/a;->u:LOD0/a;

    .line 66
    .line 67
    return-void
.end method

.method public constructor <init>(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)V
    .locals 0
    .param p3    # Ll8/b$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LND0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LND0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ll8/b$a;",
            "Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;",
            "III",
            "Ljava/lang/String;",
            "LND0/k;",
            "LND0/k;",
            "II",
            "Ljava/util/List<",
            "LND0/h;",
            ">;Z",
            "Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;",
            "Ljava/lang/String;",
            "Z",
            "Ljava/util/List<",
            "LND0/f;",
            ">;JJ)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, LOD0/a;->a:J

    .line 5
    .line 6
    iput-object p3, p0, LOD0/a;->b:Ll8/b$a;

    .line 7
    .line 8
    iput-object p4, p0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 9
    .line 10
    iput p5, p0, LOD0/a;->d:I

    .line 11
    .line 12
    iput p6, p0, LOD0/a;->e:I

    .line 13
    .line 14
    iput p7, p0, LOD0/a;->f:I

    .line 15
    .line 16
    iput-object p8, p0, LOD0/a;->g:Ljava/lang/String;

    .line 17
    .line 18
    iput-object p9, p0, LOD0/a;->h:LND0/k;

    .line 19
    .line 20
    iput-object p10, p0, LOD0/a;->i:LND0/k;

    .line 21
    .line 22
    iput p11, p0, LOD0/a;->j:I

    .line 23
    .line 24
    iput p12, p0, LOD0/a;->k:I

    .line 25
    .line 26
    iput-object p13, p0, LOD0/a;->l:Ljava/util/List;

    .line 27
    .line 28
    iput-boolean p14, p0, LOD0/a;->m:Z

    .line 29
    .line 30
    iput-object p15, p0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 31
    .line 32
    move-object/from16 p1, p16

    .line 33
    .line 34
    iput-object p1, p0, LOD0/a;->o:Ljava/lang/String;

    .line 35
    .line 36
    move/from16 p1, p17

    .line 37
    .line 38
    iput-boolean p1, p0, LOD0/a;->p:Z

    .line 39
    .line 40
    move-object/from16 p1, p18

    .line 41
    .line 42
    iput-object p1, p0, LOD0/a;->q:Ljava/util/List;

    .line 43
    .line 44
    move-wide/from16 p1, p19

    .line 45
    .line 46
    iput-wide p1, p0, LOD0/a;->r:J

    .line 47
    .line 48
    move-wide/from16 p1, p21

    .line 49
    .line 50
    iput-wide p1, p0, LOD0/a;->s:J

    .line 51
    .line 52
    return-void
.end method

.method public static final synthetic a()LOD0/a;
    .locals 1

    .line 1
    sget-object v0, LOD0/a;->u:LOD0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public static synthetic c(LOD0/a;JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJILjava/lang/Object;)LOD0/a;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move/from16 v1, p23

    .line 4
    .line 5
    and-int/lit8 v2, v1, 0x1

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    iget-wide v2, v0, LOD0/a;->a:J

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    move-wide/from16 v2, p1

    .line 13
    .line 14
    :goto_0
    and-int/lit8 v4, v1, 0x2

    .line 15
    .line 16
    if-eqz v4, :cond_1

    .line 17
    .line 18
    iget-object v4, v0, LOD0/a;->b:Ll8/b$a;

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_1
    move-object/from16 v4, p3

    .line 22
    .line 23
    :goto_1
    and-int/lit8 v5, v1, 0x4

    .line 24
    .line 25
    if-eqz v5, :cond_2

    .line 26
    .line 27
    iget-object v5, v0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 28
    .line 29
    goto :goto_2

    .line 30
    :cond_2
    move-object/from16 v5, p4

    .line 31
    .line 32
    :goto_2
    and-int/lit8 v6, v1, 0x8

    .line 33
    .line 34
    if-eqz v6, :cond_3

    .line 35
    .line 36
    iget v6, v0, LOD0/a;->d:I

    .line 37
    .line 38
    goto :goto_3

    .line 39
    :cond_3
    move/from16 v6, p5

    .line 40
    .line 41
    :goto_3
    and-int/lit8 v7, v1, 0x10

    .line 42
    .line 43
    if-eqz v7, :cond_4

    .line 44
    .line 45
    iget v7, v0, LOD0/a;->e:I

    .line 46
    .line 47
    goto :goto_4

    .line 48
    :cond_4
    move/from16 v7, p6

    .line 49
    .line 50
    :goto_4
    and-int/lit8 v8, v1, 0x20

    .line 51
    .line 52
    if-eqz v8, :cond_5

    .line 53
    .line 54
    iget v8, v0, LOD0/a;->f:I

    .line 55
    .line 56
    goto :goto_5

    .line 57
    :cond_5
    move/from16 v8, p7

    .line 58
    .line 59
    :goto_5
    and-int/lit8 v9, v1, 0x40

    .line 60
    .line 61
    if-eqz v9, :cond_6

    .line 62
    .line 63
    iget-object v9, v0, LOD0/a;->g:Ljava/lang/String;

    .line 64
    .line 65
    goto :goto_6

    .line 66
    :cond_6
    move-object/from16 v9, p8

    .line 67
    .line 68
    :goto_6
    and-int/lit16 v10, v1, 0x80

    .line 69
    .line 70
    if-eqz v10, :cond_7

    .line 71
    .line 72
    iget-object v10, v0, LOD0/a;->h:LND0/k;

    .line 73
    .line 74
    goto :goto_7

    .line 75
    :cond_7
    move-object/from16 v10, p9

    .line 76
    .line 77
    :goto_7
    and-int/lit16 v11, v1, 0x100

    .line 78
    .line 79
    if-eqz v11, :cond_8

    .line 80
    .line 81
    iget-object v11, v0, LOD0/a;->i:LND0/k;

    .line 82
    .line 83
    goto :goto_8

    .line 84
    :cond_8
    move-object/from16 v11, p10

    .line 85
    .line 86
    :goto_8
    and-int/lit16 v12, v1, 0x200

    .line 87
    .line 88
    if-eqz v12, :cond_9

    .line 89
    .line 90
    iget v12, v0, LOD0/a;->j:I

    .line 91
    .line 92
    goto :goto_9

    .line 93
    :cond_9
    move/from16 v12, p11

    .line 94
    .line 95
    :goto_9
    and-int/lit16 v13, v1, 0x400

    .line 96
    .line 97
    if-eqz v13, :cond_a

    .line 98
    .line 99
    iget v13, v0, LOD0/a;->k:I

    .line 100
    .line 101
    goto :goto_a

    .line 102
    :cond_a
    move/from16 v13, p12

    .line 103
    .line 104
    :goto_a
    and-int/lit16 v14, v1, 0x800

    .line 105
    .line 106
    if-eqz v14, :cond_b

    .line 107
    .line 108
    iget-object v14, v0, LOD0/a;->l:Ljava/util/List;

    .line 109
    .line 110
    goto :goto_b

    .line 111
    :cond_b
    move-object/from16 v14, p13

    .line 112
    .line 113
    :goto_b
    and-int/lit16 v15, v1, 0x1000

    .line 114
    .line 115
    if-eqz v15, :cond_c

    .line 116
    .line 117
    iget-boolean v15, v0, LOD0/a;->m:Z

    .line 118
    .line 119
    goto :goto_c

    .line 120
    :cond_c
    move/from16 v15, p14

    .line 121
    .line 122
    :goto_c
    move-wide/from16 v16, v2

    .line 123
    .line 124
    and-int/lit16 v2, v1, 0x2000

    .line 125
    .line 126
    if-eqz v2, :cond_d

    .line 127
    .line 128
    iget-object v2, v0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 129
    .line 130
    goto :goto_d

    .line 131
    :cond_d
    move-object/from16 v2, p15

    .line 132
    .line 133
    :goto_d
    and-int/lit16 v3, v1, 0x4000

    .line 134
    .line 135
    if-eqz v3, :cond_e

    .line 136
    .line 137
    iget-object v3, v0, LOD0/a;->o:Ljava/lang/String;

    .line 138
    .line 139
    goto :goto_e

    .line 140
    :cond_e
    move-object/from16 v3, p16

    .line 141
    .line 142
    :goto_e
    const v18, 0x8000

    .line 143
    .line 144
    .line 145
    and-int v18, v1, v18

    .line 146
    .line 147
    if-eqz v18, :cond_f

    .line 148
    .line 149
    iget-boolean v1, v0, LOD0/a;->p:Z

    .line 150
    .line 151
    goto :goto_f

    .line 152
    :cond_f
    move/from16 v1, p17

    .line 153
    .line 154
    :goto_f
    const/high16 v18, 0x10000

    .line 155
    .line 156
    and-int v18, p23, v18

    .line 157
    .line 158
    move/from16 p1, v1

    .line 159
    .line 160
    if-eqz v18, :cond_10

    .line 161
    .line 162
    iget-object v1, v0, LOD0/a;->q:Ljava/util/List;

    .line 163
    .line 164
    goto :goto_10

    .line 165
    :cond_10
    move-object/from16 v1, p18

    .line 166
    .line 167
    :goto_10
    const/high16 v18, 0x20000

    .line 168
    .line 169
    and-int v18, p23, v18

    .line 170
    .line 171
    move-object/from16 p3, v1

    .line 172
    .line 173
    move-object/from16 p2, v2

    .line 174
    .line 175
    if-eqz v18, :cond_11

    .line 176
    .line 177
    iget-wide v1, v0, LOD0/a;->r:J

    .line 178
    .line 179
    goto :goto_11

    .line 180
    :cond_11
    move-wide/from16 v1, p19

    .line 181
    .line 182
    :goto_11
    const/high16 v18, 0x40000

    .line 183
    .line 184
    and-int v18, p23, v18

    .line 185
    .line 186
    if-eqz v18, :cond_12

    .line 187
    .line 188
    move-wide/from16 p4, v1

    .line 189
    .line 190
    iget-wide v1, v0, LOD0/a;->s:J

    .line 191
    .line 192
    move-wide/from16 p20, p4

    .line 193
    .line 194
    move-wide/from16 p22, v1

    .line 195
    .line 196
    :goto_12
    move/from16 p18, p1

    .line 197
    .line 198
    move-object/from16 p16, p2

    .line 199
    .line 200
    move-object/from16 p19, p3

    .line 201
    .line 202
    move-object/from16 p1, v0

    .line 203
    .line 204
    move-object/from16 p17, v3

    .line 205
    .line 206
    move-object/from16 p4, v4

    .line 207
    .line 208
    move-object/from16 p5, v5

    .line 209
    .line 210
    move/from16 p6, v6

    .line 211
    .line 212
    move/from16 p7, v7

    .line 213
    .line 214
    move/from16 p8, v8

    .line 215
    .line 216
    move-object/from16 p9, v9

    .line 217
    .line 218
    move-object/from16 p10, v10

    .line 219
    .line 220
    move-object/from16 p11, v11

    .line 221
    .line 222
    move/from16 p12, v12

    .line 223
    .line 224
    move/from16 p13, v13

    .line 225
    .line 226
    move-object/from16 p14, v14

    .line 227
    .line 228
    move/from16 p15, v15

    .line 229
    .line 230
    move-wide/from16 p2, v16

    .line 231
    .line 232
    goto :goto_13

    .line 233
    :cond_12
    move-wide/from16 p22, p21

    .line 234
    .line 235
    move-wide/from16 p20, v1

    .line 236
    .line 237
    goto :goto_12

    .line 238
    :goto_13
    invoke-virtual/range {p1 .. p23}, LOD0/a;->b(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)LOD0/a;

    .line 239
    .line 240
    .line 241
    move-result-object v0

    .line 242
    return-object v0
.end method


# virtual methods
.method public final b(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)LOD0/a;
    .locals 23
    .param p3    # Ll8/b$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LND0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LND0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ll8/b$a;",
            "Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;",
            "III",
            "Ljava/lang/String;",
            "LND0/k;",
            "LND0/k;",
            "II",
            "Ljava/util/List<",
            "LND0/h;",
            ">;Z",
            "Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;",
            "Ljava/lang/String;",
            "Z",
            "Ljava/util/List<",
            "LND0/f;",
            ">;JJ)",
            "LOD0/a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LOD0/a;

    .line 2
    .line 3
    move-wide/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v3, p3

    .line 6
    .line 7
    move-object/from16 v4, p4

    .line 8
    .line 9
    move/from16 v5, p5

    .line 10
    .line 11
    move/from16 v6, p6

    .line 12
    .line 13
    move/from16 v7, p7

    .line 14
    .line 15
    move-object/from16 v8, p8

    .line 16
    .line 17
    move-object/from16 v9, p9

    .line 18
    .line 19
    move-object/from16 v10, p10

    .line 20
    .line 21
    move/from16 v11, p11

    .line 22
    .line 23
    move/from16 v12, p12

    .line 24
    .line 25
    move-object/from16 v13, p13

    .line 26
    .line 27
    move/from16 v14, p14

    .line 28
    .line 29
    move-object/from16 v15, p15

    .line 30
    .line 31
    move-object/from16 v16, p16

    .line 32
    .line 33
    move/from16 v17, p17

    .line 34
    .line 35
    move-object/from16 v18, p18

    .line 36
    .line 37
    move-wide/from16 v19, p19

    .line 38
    .line 39
    move-wide/from16 v21, p21

    .line 40
    .line 41
    invoke-direct/range {v0 .. v22}, LOD0/a;-><init>(JLl8/b$a;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;IIILjava/lang/String;LND0/k;LND0/k;IILjava/util/List;ZLorg/xbet/statistic/domain/model/shortgame/ShortGameSource;Ljava/lang/String;ZLjava/util/List;JJ)V

    .line 42
    .line 43
    .line 44
    return-object v0
.end method

.method public final d()Ll8/b$a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->b:Ll8/b$a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, LOD0/a;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, LOD0/a;

    .line 12
    .line 13
    iget-wide v3, p0, LOD0/a;->a:J

    .line 14
    .line 15
    iget-wide v5, p1, LOD0/a;->a:J

    .line 16
    .line 17
    cmp-long v1, v3, v5

    .line 18
    .line 19
    if-eqz v1, :cond_2

    .line 20
    .line 21
    return v2

    .line 22
    :cond_2
    iget-object v1, p0, LOD0/a;->b:Ll8/b$a;

    .line 23
    .line 24
    iget-object v3, p1, LOD0/a;->b:Ll8/b$a;

    .line 25
    .line 26
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-nez v1, :cond_3

    .line 31
    .line 32
    return v2

    .line 33
    :cond_3
    iget-object v1, p0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 34
    .line 35
    iget-object v3, p1, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 36
    .line 37
    if-eq v1, v3, :cond_4

    .line 38
    .line 39
    return v2

    .line 40
    :cond_4
    iget v1, p0, LOD0/a;->d:I

    .line 41
    .line 42
    iget v3, p1, LOD0/a;->d:I

    .line 43
    .line 44
    if-eq v1, v3, :cond_5

    .line 45
    .line 46
    return v2

    .line 47
    :cond_5
    iget v1, p0, LOD0/a;->e:I

    .line 48
    .line 49
    iget v3, p1, LOD0/a;->e:I

    .line 50
    .line 51
    if-eq v1, v3, :cond_6

    .line 52
    .line 53
    return v2

    .line 54
    :cond_6
    iget v1, p0, LOD0/a;->f:I

    .line 55
    .line 56
    iget v3, p1, LOD0/a;->f:I

    .line 57
    .line 58
    if-eq v1, v3, :cond_7

    .line 59
    .line 60
    return v2

    .line 61
    :cond_7
    iget-object v1, p0, LOD0/a;->g:Ljava/lang/String;

    .line 62
    .line 63
    iget-object v3, p1, LOD0/a;->g:Ljava/lang/String;

    .line 64
    .line 65
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    if-nez v1, :cond_8

    .line 70
    .line 71
    return v2

    .line 72
    :cond_8
    iget-object v1, p0, LOD0/a;->h:LND0/k;

    .line 73
    .line 74
    iget-object v3, p1, LOD0/a;->h:LND0/k;

    .line 75
    .line 76
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    if-nez v1, :cond_9

    .line 81
    .line 82
    return v2

    .line 83
    :cond_9
    iget-object v1, p0, LOD0/a;->i:LND0/k;

    .line 84
    .line 85
    iget-object v3, p1, LOD0/a;->i:LND0/k;

    .line 86
    .line 87
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    if-nez v1, :cond_a

    .line 92
    .line 93
    return v2

    .line 94
    :cond_a
    iget v1, p0, LOD0/a;->j:I

    .line 95
    .line 96
    iget v3, p1, LOD0/a;->j:I

    .line 97
    .line 98
    if-eq v1, v3, :cond_b

    .line 99
    .line 100
    return v2

    .line 101
    :cond_b
    iget v1, p0, LOD0/a;->k:I

    .line 102
    .line 103
    iget v3, p1, LOD0/a;->k:I

    .line 104
    .line 105
    if-eq v1, v3, :cond_c

    .line 106
    .line 107
    return v2

    .line 108
    :cond_c
    iget-object v1, p0, LOD0/a;->l:Ljava/util/List;

    .line 109
    .line 110
    iget-object v3, p1, LOD0/a;->l:Ljava/util/List;

    .line 111
    .line 112
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 113
    .line 114
    .line 115
    move-result v1

    .line 116
    if-nez v1, :cond_d

    .line 117
    .line 118
    return v2

    .line 119
    :cond_d
    iget-boolean v1, p0, LOD0/a;->m:Z

    .line 120
    .line 121
    iget-boolean v3, p1, LOD0/a;->m:Z

    .line 122
    .line 123
    if-eq v1, v3, :cond_e

    .line 124
    .line 125
    return v2

    .line 126
    :cond_e
    iget-object v1, p0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 127
    .line 128
    iget-object v3, p1, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 129
    .line 130
    if-eq v1, v3, :cond_f

    .line 131
    .line 132
    return v2

    .line 133
    :cond_f
    iget-object v1, p0, LOD0/a;->o:Ljava/lang/String;

    .line 134
    .line 135
    iget-object v3, p1, LOD0/a;->o:Ljava/lang/String;

    .line 136
    .line 137
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 138
    .line 139
    .line 140
    move-result v1

    .line 141
    if-nez v1, :cond_10

    .line 142
    .line 143
    return v2

    .line 144
    :cond_10
    iget-boolean v1, p0, LOD0/a;->p:Z

    .line 145
    .line 146
    iget-boolean v3, p1, LOD0/a;->p:Z

    .line 147
    .line 148
    if-eq v1, v3, :cond_11

    .line 149
    .line 150
    return v2

    .line 151
    :cond_11
    iget-object v1, p0, LOD0/a;->q:Ljava/util/List;

    .line 152
    .line 153
    iget-object v3, p1, LOD0/a;->q:Ljava/util/List;

    .line 154
    .line 155
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 156
    .line 157
    .line 158
    move-result v1

    .line 159
    if-nez v1, :cond_12

    .line 160
    .line 161
    return v2

    .line 162
    :cond_12
    iget-wide v3, p0, LOD0/a;->r:J

    .line 163
    .line 164
    iget-wide v5, p1, LOD0/a;->r:J

    .line 165
    .line 166
    cmp-long v1, v3, v5

    .line 167
    .line 168
    if-eqz v1, :cond_13

    .line 169
    .line 170
    return v2

    .line 171
    :cond_13
    iget-wide v3, p0, LOD0/a;->s:J

    .line 172
    .line 173
    iget-wide v5, p1, LOD0/a;->s:J

    .line 174
    .line 175
    cmp-long p1, v3, v5

    .line 176
    .line 177
    if-eqz p1, :cond_14

    .line 178
    .line 179
    return v2

    .line 180
    :cond_14
    return v0
.end method

.method public final f()I
    .locals 1

    .line 1
    iget v0, p0, LOD0/a;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final g()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LOD0/a;->p:Z

    .line 2
    .line 3
    return v0
.end method

.method public final h()J
    .locals 2

    .line 1
    iget-wide v0, p0, LOD0/a;->s:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public hashCode()I
    .locals 3

    .line 1
    iget-wide v0, p0, LOD0/a;->a:J

    .line 2
    .line 3
    invoke-static {v0, v1}, Lu/l;->a(J)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget-object v1, p0, LOD0/a;->b:Ll8/b$a;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    mul-int/lit8 v0, v0, 0x1f

    .line 17
    .line 18
    iget-object v1, p0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 19
    .line 20
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    add-int/2addr v0, v1

    .line 25
    mul-int/lit8 v0, v0, 0x1f

    .line 26
    .line 27
    iget v1, p0, LOD0/a;->d:I

    .line 28
    .line 29
    add-int/2addr v0, v1

    .line 30
    mul-int/lit8 v0, v0, 0x1f

    .line 31
    .line 32
    iget v1, p0, LOD0/a;->e:I

    .line 33
    .line 34
    add-int/2addr v0, v1

    .line 35
    mul-int/lit8 v0, v0, 0x1f

    .line 36
    .line 37
    iget v1, p0, LOD0/a;->f:I

    .line 38
    .line 39
    add-int/2addr v0, v1

    .line 40
    mul-int/lit8 v0, v0, 0x1f

    .line 41
    .line 42
    iget-object v1, p0, LOD0/a;->g:Ljava/lang/String;

    .line 43
    .line 44
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    add-int/2addr v0, v1

    .line 49
    mul-int/lit8 v0, v0, 0x1f

    .line 50
    .line 51
    iget-object v1, p0, LOD0/a;->h:LND0/k;

    .line 52
    .line 53
    invoke-virtual {v1}, LND0/k;->hashCode()I

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    add-int/2addr v0, v1

    .line 58
    mul-int/lit8 v0, v0, 0x1f

    .line 59
    .line 60
    iget-object v1, p0, LOD0/a;->i:LND0/k;

    .line 61
    .line 62
    invoke-virtual {v1}, LND0/k;->hashCode()I

    .line 63
    .line 64
    .line 65
    move-result v1

    .line 66
    add-int/2addr v0, v1

    .line 67
    mul-int/lit8 v0, v0, 0x1f

    .line 68
    .line 69
    iget v1, p0, LOD0/a;->j:I

    .line 70
    .line 71
    add-int/2addr v0, v1

    .line 72
    mul-int/lit8 v0, v0, 0x1f

    .line 73
    .line 74
    iget v1, p0, LOD0/a;->k:I

    .line 75
    .line 76
    add-int/2addr v0, v1

    .line 77
    mul-int/lit8 v0, v0, 0x1f

    .line 78
    .line 79
    iget-object v1, p0, LOD0/a;->l:Ljava/util/List;

    .line 80
    .line 81
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 82
    .line 83
    .line 84
    move-result v1

    .line 85
    add-int/2addr v0, v1

    .line 86
    mul-int/lit8 v0, v0, 0x1f

    .line 87
    .line 88
    iget-boolean v1, p0, LOD0/a;->m:Z

    .line 89
    .line 90
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    add-int/2addr v0, v1

    .line 95
    mul-int/lit8 v0, v0, 0x1f

    .line 96
    .line 97
    iget-object v1, p0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 98
    .line 99
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 100
    .line 101
    .line 102
    move-result v1

    .line 103
    add-int/2addr v0, v1

    .line 104
    mul-int/lit8 v0, v0, 0x1f

    .line 105
    .line 106
    iget-object v1, p0, LOD0/a;->o:Ljava/lang/String;

    .line 107
    .line 108
    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    .line 109
    .line 110
    .line 111
    move-result v1

    .line 112
    add-int/2addr v0, v1

    .line 113
    mul-int/lit8 v0, v0, 0x1f

    .line 114
    .line 115
    iget-boolean v1, p0, LOD0/a;->p:Z

    .line 116
    .line 117
    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    .line 118
    .line 119
    .line 120
    move-result v1

    .line 121
    add-int/2addr v0, v1

    .line 122
    mul-int/lit8 v0, v0, 0x1f

    .line 123
    .line 124
    iget-object v1, p0, LOD0/a;->q:Ljava/util/List;

    .line 125
    .line 126
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 127
    .line 128
    .line 129
    move-result v1

    .line 130
    add-int/2addr v0, v1

    .line 131
    mul-int/lit8 v0, v0, 0x1f

    .line 132
    .line 133
    iget-wide v1, p0, LOD0/a;->r:J

    .line 134
    .line 135
    invoke-static {v1, v2}, Lu/l;->a(J)I

    .line 136
    .line 137
    .line 138
    move-result v1

    .line 139
    add-int/2addr v0, v1

    .line 140
    mul-int/lit8 v0, v0, 0x1f

    .line 141
    .line 142
    iget-wide v1, p0, LOD0/a;->s:J

    .line 143
    .line 144
    invoke-static {v1, v2}, Lu/l;->a(J)I

    .line 145
    .line 146
    .line 147
    move-result v1

    .line 148
    add-int/2addr v0, v1

    .line 149
    return v0
.end method

.method public final i()I
    .locals 1

    .line 1
    iget v0, p0, LOD0/a;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public final j()I
    .locals 1

    .line 1
    iget v0, p0, LOD0/a;->k:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LND0/h;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->l:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->g:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()I
    .locals 1

    .line 1
    iget v0, p0, LOD0/a;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public final n()I
    .locals 1

    .line 1
    iget v0, p0, LOD0/a;->f:I

    .line 2
    .line 3
    return v0
.end method

.method public final o()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, LOD0/a;->m:Z

    .line 2
    .line 3
    return v0
.end method

.method public final p()Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q()J
    .locals 2

    .line 1
    iget-wide v0, p0, LOD0/a;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final r()LND0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->h:LND0/k;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s()LND0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->i:LND0/k;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LOD0/a;->o:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 25
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-wide v1, v0, LOD0/a;->a:J

    .line 4
    .line 5
    iget-object v3, v0, LOD0/a;->b:Ll8/b$a;

    .line 6
    .line 7
    iget-object v4, v0, LOD0/a;->c:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 8
    .line 9
    iget v5, v0, LOD0/a;->d:I

    .line 10
    .line 11
    iget v6, v0, LOD0/a;->e:I

    .line 12
    .line 13
    iget v7, v0, LOD0/a;->f:I

    .line 14
    .line 15
    iget-object v8, v0, LOD0/a;->g:Ljava/lang/String;

    .line 16
    .line 17
    iget-object v9, v0, LOD0/a;->h:LND0/k;

    .line 18
    .line 19
    iget-object v10, v0, LOD0/a;->i:LND0/k;

    .line 20
    .line 21
    iget v11, v0, LOD0/a;->j:I

    .line 22
    .line 23
    iget v12, v0, LOD0/a;->k:I

    .line 24
    .line 25
    iget-object v13, v0, LOD0/a;->l:Ljava/util/List;

    .line 26
    .line 27
    iget-boolean v14, v0, LOD0/a;->m:Z

    .line 28
    .line 29
    iget-object v15, v0, LOD0/a;->n:Lorg/xbet/statistic/domain/model/shortgame/ShortGameSource;

    .line 30
    .line 31
    move-object/from16 v16, v15

    .line 32
    .line 33
    iget-object v15, v0, LOD0/a;->o:Ljava/lang/String;

    .line 34
    .line 35
    move-object/from16 v17, v15

    .line 36
    .line 37
    iget-boolean v15, v0, LOD0/a;->p:Z

    .line 38
    .line 39
    move/from16 v18, v15

    .line 40
    .line 41
    iget-object v15, v0, LOD0/a;->q:Ljava/util/List;

    .line 42
    .line 43
    move/from16 v19, v14

    .line 44
    .line 45
    move-object/from16 v20, v15

    .line 46
    .line 47
    iget-wide v14, v0, LOD0/a;->r:J

    .line 48
    .line 49
    move-wide/from16 v21, v14

    .line 50
    .line 51
    iget-wide v14, v0, LOD0/a;->s:J

    .line 52
    .line 53
    new-instance v0, Ljava/lang/StringBuilder;

    .line 54
    .line 55
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 56
    .line 57
    .line 58
    move-wide/from16 v23, v14

    .line 59
    .line 60
    const-string v14, "ShortGameModel(sportId="

    .line 61
    .line 62
    invoke-virtual {v0, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    const-string v1, ", eventDate="

    .line 69
    .line 70
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    const-string v1, ", eventStatusType="

    .line 77
    .line 78
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 79
    .line 80
    .line 81
    invoke-virtual {v0, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    const-string v1, ", eventSubStatusTypeId="

    .line 85
    .line 86
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 87
    .line 88
    .line 89
    invoke-virtual {v0, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    const-string v1, ", scoreTeamOne="

    .line 93
    .line 94
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 95
    .line 96
    .line 97
    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 98
    .line 99
    .line 100
    const-string v1, ", scoreTeamTwo="

    .line 101
    .line 102
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 106
    .line 107
    .line 108
    const-string v1, ", score="

    .line 109
    .line 110
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 111
    .line 112
    .line 113
    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    const-string v1, ", teamOne="

    .line 117
    .line 118
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 119
    .line 120
    .line 121
    invoke-virtual {v0, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 122
    .line 123
    .line 124
    const-string v1, ", teamTwo="

    .line 125
    .line 126
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 127
    .line 128
    .line 129
    invoke-virtual {v0, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 130
    .line 131
    .line 132
    const-string v1, ", redCardTeamOne="

    .line 133
    .line 134
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 135
    .line 136
    .line 137
    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 138
    .line 139
    .line 140
    const-string v1, ", redCardTeamTwo="

    .line 141
    .line 142
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 143
    .line 144
    .line 145
    invoke-virtual {v0, v12}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 146
    .line 147
    .line 148
    const-string v1, ", referees="

    .line 149
    .line 150
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 151
    .line 152
    .line 153
    invoke-virtual {v0, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 154
    .line 155
    .line 156
    const-string v1, ", showScore="

    .line 157
    .line 158
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 159
    .line 160
    .line 161
    move/from16 v1, v19

    .line 162
    .line 163
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 164
    .line 165
    .line 166
    const-string v1, ", source="

    .line 167
    .line 168
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 169
    .line 170
    .line 171
    move-object/from16 v1, v16

    .line 172
    .line 173
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 174
    .line 175
    .line 176
    const-string v1, ", tournamentTitle="

    .line 177
    .line 178
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 179
    .line 180
    .line 181
    move-object/from16 v1, v17

    .line 182
    .line 183
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 184
    .line 185
    .line 186
    const-string v1, ", finished="

    .line 187
    .line 188
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 189
    .line 190
    .line 191
    move/from16 v1, v18

    .line 192
    .line 193
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    .line 194
    .line 195
    .line 196
    const-string v1, ", periods="

    .line 197
    .line 198
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 199
    .line 200
    .line 201
    move-object/from16 v1, v20

    .line 202
    .line 203
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 204
    .line 205
    .line 206
    const-string v1, ", champId="

    .line 207
    .line 208
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 209
    .line 210
    .line 211
    move-wide/from16 v1, v21

    .line 212
    .line 213
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 214
    .line 215
    .line 216
    const-string v1, ", globalChampId="

    .line 217
    .line 218
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 219
    .line 220
    .line 221
    move-wide/from16 v1, v23

    .line 222
    .line 223
    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 224
    .line 225
    .line 226
    const-string v1, ")"

    .line 227
    .line 228
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 229
    .line 230
    .line 231
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object v0

    .line 235
    return-object v0
.end method
