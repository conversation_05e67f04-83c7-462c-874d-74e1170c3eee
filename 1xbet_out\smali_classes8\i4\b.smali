.class public interface abstract Li4/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Li4/e;


# virtual methods
.method public abstract d(Lcom/github/mikephil/charting/components/YAxis$AxisDependency;)Lp4/g;
.end method

.method public abstract e(Lcom/github/mikephil/charting/components/YAxis$AxisDependency;)Z
.end method

.method public abstract getData()Lf4/b;
.end method

.method public abstract getHighestVisibleX()F
.end method

.method public abstract getLowestVisibleX()F
.end method
