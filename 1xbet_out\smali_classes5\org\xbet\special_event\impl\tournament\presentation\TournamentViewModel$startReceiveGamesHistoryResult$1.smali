.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$startReceiveGamesHistoryResult$1"
    f = "TournamentViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "LKo0/a<",
        "Ljava/util/List<",
        "+",
        "Lorg/xbet/domain/betting/api/models/result/HistoryGameItem;",
        ">;>;",
        "Ljava/util/Set<",
        "+",
        "Ljava/lang/Long;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0008\u001a\u00020\u00072\u0012\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004H\n\u00a2\u0006\u0004\u0008\u0008\u0010\t"
    }
    d2 = {
        "LKo0/a;",
        "",
        "Lorg/xbet/domain/betting/api/models/result/HistoryGameItem;",
        "result",
        "",
        "",
        "expandIds",
        "",
        "<anonymous>",
        "(LKo0/a;Ljava/util/Set;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invoke(LKo0/a;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "Ljava/util/List<",
            "Lorg/xbet/domain/betting/api/models/result/HistoryGameItem;",
            ">;>;",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LKo0/a;

    check-cast p2, Ljava/util/Set;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->invoke(LKo0/a;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LKo0/a;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Ljava/util/Set;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 20
    .line 21
    instance-of v2, p1, LKo0/a$a;

    .line 22
    .line 23
    if-eqz v2, :cond_0

    .line 24
    .line 25
    check-cast p1, LKo0/a$a;

    .line 26
    .line 27
    invoke-virtual {p1}, LKo0/a$a;->a()Ljava/lang/Throwable;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-static {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->G3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/ui_common/utils/M;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1$a;

    .line 36
    .line 37
    invoke-direct {v2, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$startReceiveGamesHistoryResult$1$a;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)V

    .line 38
    .line 39
    .line 40
    invoke-interface {v0, p1, v2}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    instance-of v2, p1, LKo0/a$b;

    .line 45
    .line 46
    if-eqz v2, :cond_2

    .line 47
    .line 48
    check-cast p1, LKo0/a$b;

    .line 49
    .line 50
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    check-cast p1, Ljava/util/List;

    .line 55
    .line 56
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 57
    .line 58
    .line 59
    move-result v2

    .line 60
    if-eqz v2, :cond_1

    .line 61
    .line 62
    new-instance p1, LZx0/b;

    .line 63
    .line 64
    sget-object v0, LZx0/e;->a:LZx0/e;

    .line 65
    .line 66
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-static {}, Lkotlin/collections/Z;->e()Ljava/util/Set;

    .line 71
    .line 72
    .line 73
    move-result-object v3

    .line 74
    invoke-direct {p1, v0, v2, v3}, LZx0/b;-><init>(LZx0/g;Ljava/util/List;Ljava/util/Set;)V

    .line 75
    .line 76
    .line 77
    invoke-static {v1, p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 78
    .line 79
    .line 80
    goto :goto_0

    .line 81
    :cond_1
    new-instance v2, LZx0/b;

    .line 82
    .line 83
    sget-object v3, LZx0/g$a$b;->a:LZx0/g$a$b;

    .line 84
    .line 85
    invoke-direct {v2, v3, p1, v0}, LZx0/b;-><init>(LZx0/g;Ljava/util/List;Ljava/util/Set;)V

    .line 86
    .line 87
    .line 88
    invoke-static {v1, v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 89
    .line 90
    .line 91
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 92
    .line 93
    return-object p1

    .line 94
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 95
    .line 96
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 97
    .line 98
    .line 99
    throw p1

    .line 100
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 101
    .line 102
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 103
    .line 104
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw p1
.end method
