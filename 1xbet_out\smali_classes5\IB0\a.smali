.class public final LIB0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIB0/a$a;,
        LIB0/a$b;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()LIB0/c$a;
    .locals 2

    .line 1
    new-instance v0, LIB0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LIB0/a$a;-><init>(LIB0/b;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method
