.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0010\u0007\n\u0002\u0008\u0016\u0008\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\"\u0010\r\u001a\u00020\u00068\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0007\u0010\u0008\u001a\u0004\u0008\t\u0010\n\"\u0004\u0008\u000b\u0010\u000cR\u0017\u0010\u0013\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012R\u0017\u0010\u0016\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0010\u001a\u0004\u0008\u0015\u0010\u0012R\u0017\u0010\u001c\u001a\u00020\u00178\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u0017\u0010\u001d\u001a\u00020\u00178\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u0019\u001a\u0004\u0008\u0014\u0010\u001bR\u0017\u0010\u001f\u001a\u00020\u00178\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u0019\u001a\u0004\u0008\u0018\u0010\u001bR\u0017\u0010!\u001a\u00020\u00178\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u0019\u001a\u0004\u0008 \u0010\u001bR\u0017\u0010$\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\"\u0010\u0010\u001a\u0004\u0008#\u0010\u0012R\u0017\u0010%\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008#\u0010\u0010\u001a\u0004\u0008\"\u0010\u0012R\u0017\u0010&\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0011\u0010\u0010\u001a\u0004\u0008\u001e\u0010\u0012R\u0017\u0010\'\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\u0010\u001a\u0004\u0008\u000f\u0010\u0012R\"\u0010*\u001a\u00020\u000e8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008 \u0010\u0010\u001a\u0004\u0008\u0007\u0010\u0012\"\u0004\u0008(\u0010)R\u0017\u0010,\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008+\u0010\u0010\u001a\u0004\u0008+\u0010\u0012\u00a8\u0006-"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;",
        "",
        "Landroid/content/Context;",
        "context",
        "<init>",
        "(Landroid/content/Context;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;",
        "k",
        "()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;",
        "o",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;)V",
        "timerSizeType",
        "",
        "b",
        "I",
        "j",
        "()I",
        "timeSeparatorSpace",
        "c",
        "g",
        "spaceBetweenTimeAndTimeUnit",
        "",
        "d",
        "F",
        "e",
        "()F",
        "minUnitWidth",
        "maxUnitWidth",
        "f",
        "minUnitTextSize",
        "l",
        "unitTextShrinkStep",
        "h",
        "i",
        "timeCellBackgroundWidth",
        "timeCellBackgroundHeight",
        "shortTimerWidth",
        "longTimerWidth",
        "n",
        "(I)V",
        "currentTimerWidth",
        "m",
        "viewHeight",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:F

.field public final e:F

.field public final f:F

.field public final g:F

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public l:I

.field public final m:I


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 5
    .line 6
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 7
    .line 8
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    sget v1, LlZ0/g;->space_12:I

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->b:I

    .line 19
    .line 20
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget v1, LlZ0/g;->space_2:I

    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->c:I

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sget v1, LlZ0/g;->size_14:I

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->d:F

    .line 43
    .line 44
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    sget v1, LlZ0/g;->size_28:I

    .line 49
    .line 50
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->e:F

    .line 55
    .line 56
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    sget v1, LlZ0/g;->text_8:I

    .line 61
    .line 62
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->f:F

    .line 67
    .line 68
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    sget v1, LlZ0/g;->text_1:I

    .line 73
    .line 74
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 75
    .line 76
    .line 77
    move-result v0

    .line 78
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->g:F

    .line 79
    .line 80
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    sget v1, LlZ0/g;->size_28:I

    .line 85
    .line 86
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 87
    .line 88
    .line 89
    move-result v0

    .line 90
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->h:I

    .line 91
    .line 92
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    sget v1, LlZ0/g;->size_40:I

    .line 97
    .line 98
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 99
    .line 100
    .line 101
    move-result v0

    .line 102
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->i:I

    .line 103
    .line 104
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    sget v1, LlZ0/g;->size_250:I

    .line 109
    .line 110
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 111
    .line 112
    .line 113
    move-result v0

    .line 114
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->j:I

    .line 115
    .line 116
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    sget v2, LlZ0/g;->size_274:I

    .line 121
    .line 122
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 123
    .line 124
    .line 125
    move-result v1

    .line 126
    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->k:I

    .line 127
    .line 128
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->l:I

    .line 129
    .line 130
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    sget v0, LlZ0/g;->size_40:I

    .line 135
    .line 136
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 137
    .line 138
    .line 139
    move-result p1

    .line 140
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->m:I

    .line 141
    .line 142
    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->l:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->k:I

    .line 2
    .line 3
    return v0
.end method

.method public final c()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->e:F

    .line 2
    .line 3
    return v0
.end method

.method public final d()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->f:F

    .line 2
    .line 3
    return v0
.end method

.method public final e()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->d:F

    .line 2
    .line 3
    return v0
.end method

.method public final f()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public final g()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final h()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->i:I

    .line 2
    .line 3
    return v0
.end method

.method public final i()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->h:I

    .line 2
    .line 3
    return v0
.end method

.method public final j()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->g:F

    .line 2
    .line 3
    return v0
.end method

.method public final m()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->m:I

    .line 2
    .line 3
    return v0
.end method

.method public final n(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->l:I

    .line 2
    .line 3
    return-void
.end method

.method public final o(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView$TimeSize;

    .line 2
    .line 3
    return-void
.end method
