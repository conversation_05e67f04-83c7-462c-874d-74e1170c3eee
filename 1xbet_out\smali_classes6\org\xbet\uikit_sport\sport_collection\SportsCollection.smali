.class public final Lorg/xbet/uikit_sport/sport_collection/SportsCollection;
.super Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001b\u0010\u0013\u001a\u00020\u00122\u000c\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\n\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\r\u0010\u0015\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J!\u0010\u001a\u001a\u00020\u00122\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00120\u0017\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u000f\u0010 \u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008 \u0010\u000fJ\u000f\u0010!\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008!\u0010\u000fJ!\u0010%\u001a\u0004\u0018\u00010$2\u0006\u0010\"\u001a\u00020\u00062\u0006\u0010#\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008%\u0010&J\'\u0010*\u001a\u00020\u00122\u0006\u0010\'\u001a\u00020\u00062\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008*\u0010+J\u0017\u0010,\u001a\u00020\u00122\u0006\u0010)\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008,\u0010-J\u0017\u0010.\u001a\u00020\u00122\u0006\u0010)\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008.\u0010-R\u0014\u00102\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u00104R#\u0010;\u001a\n 6*\u0004\u0018\u000105058BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:R\u0014\u0010?\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/SportsCollection;",
        "Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "LP31/h;",
        "getShimmerItems",
        "()Ljava/util/List;",
        "getAdapterItemsSize",
        "()I",
        "LP31/i;",
        "items",
        "",
        "setItems",
        "(Ljava/util/List;)V",
        "setShimmers",
        "()V",
        "Lkotlin/Function1;",
        "LP31/g;",
        "listener",
        "setOnItemClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;",
        "selectionType",
        "setSelectionType",
        "(Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;)V",
        "j",
        "i",
        "fromIndex",
        "toIndex",
        "Landroid/view/View;",
        "k",
        "(II)Landroid/view/View;",
        "startVisibleItemIndex",
        "endVisibleItemIndex",
        "selectedIndex",
        "m",
        "(III)V",
        "o",
        "(I)V",
        "n",
        "",
        "c",
        "Z",
        "isRtl",
        "d",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;",
        "Landroidx/recyclerview/widget/w;",
        "kotlin.jvm.PlatformType",
        "e",
        "Lkotlin/j;",
        "getOrientationHelper",
        "()Landroidx/recyclerview/widget/w;",
        "orientationHelper",
        "LO31/b;",
        "f",
        "LO31/b;",
        "sportCollectionAdapter",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/e;
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final c:Z

.field public d:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LO31/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 9
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    move-result v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-ne v0, v2, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->c:Z

    .line 6
    sget-object v3, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;->NO_SPECIFIC_SCROLL:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    iput-object v3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->d:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 7
    new-instance v3, LN31/d;

    invoke-direct {v3, p0}, LN31/d;-><init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)V

    invoke-static {v3}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v3

    iput-object v3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->e:Lkotlin/j;

    .line 8
    new-instance v3, LO31/b;

    invoke-direct {v3}, LO31/b;-><init>()V

    iput-object v3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->f:LO31/b;

    .line 9
    sget-object v4, Lm31/g;->SportsCollection:[I

    .line 10
    invoke-virtual {p1, p2, v4, p3, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 11
    sget-object p3, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;->Companion:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType$a;

    .line 12
    sget v4, Lm31/g;->SportsCollection_sportCollectionSpecificScrollType:I

    .line 13
    invoke-virtual {p2, v4, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result v4

    .line 14
    invoke-virtual {p3, v4}, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType$a;->a(I)Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->d:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 15
    invoke-virtual {p0, v3}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 16
    new-instance p3, Landroidx/recyclerview/widget/LinearLayoutManager;

    invoke-direct {p3, p1, v1, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    if-eqz v0, :cond_1

    .line 17
    invoke-virtual {p3, v2}, Landroidx/recyclerview/widget/LinearLayoutManager;->setReverseLayout(Z)V

    .line 18
    :cond_1
    invoke-virtual {p0, p3}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 19
    new-instance p3, Lorg/xbet/uikit_sport/sport_collection/a;

    .line 20
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;

    invoke-direct {v0, p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection$a;-><init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)V

    .line 21
    invoke-direct {p3, p1, v0}, Lorg/xbet/uikit_sport/sport_collection/a;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_collection/a$a;)V

    .line 22
    invoke-virtual {p0, p3}, Landroidx/recyclerview/widget/RecyclerView;->addOnItemTouchListener(Landroidx/recyclerview/widget/RecyclerView$r;)V

    .line 23
    new-instance v1, LR11/c;

    .line 24
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p3, LlZ0/g;->space_4:I

    invoke-virtual {p1, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    .line 25
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p3, LlZ0/g;->medium_horizontal_margin_dynamic:I

    invoke-virtual {p1, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    const/16 v7, 0x14

    const/4 v8, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    .line 26
    invoke-direct/range {v1 .. v8}, LR11/c;-><init>(IIIIZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    invoke-virtual {p0, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 28
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)Landroidx/recyclerview/widget/w;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->l(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)Landroidx/recyclerview/widget/w;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->d:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->n(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final getAdapterItemsSize()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, LO31/b;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, LO31/b;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {v0}, LA4/e;->getItems()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    return v0

    .line 26
    :cond_1
    const/4 v0, 0x0

    .line 27
    return v0
.end method

.method private final getOrientationHelper()Landroidx/recyclerview/widget/w;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/w;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getShimmerItems()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LP31/h;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    :goto_0
    const/16 v2, 0x10

    .line 8
    .line 9
    if-ge v1, v2, :cond_0

    .line 10
    .line 11
    sget-object v2, LP31/h;->a:LP31/h;

    .line 12
    .line 13
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    add-int/lit8 v1, v1, 0x1

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    return-object v0
.end method

.method public static final synthetic h(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->o(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final l(Lorg/xbet/uikit_sport/sport_collection/SportsCollection;)Landroidx/recyclerview/widget/w;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-static {p0}, Landroidx/recyclerview/widget/w;->a(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)Landroidx/recyclerview/widget/w;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method


# virtual methods
.method public final i()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildCount()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    add-int/lit8 v0, v0, -0x1

    .line 10
    .line 11
    const/4 v1, -0x1

    .line 12
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->k(II)Landroid/view/View;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-nez v0, :cond_0

    .line 17
    .line 18
    return v1

    .line 19
    :cond_0
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    return v0
.end method

.method public final j()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildCount()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v0, 0x0

    .line 14
    :goto_0
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->k(II)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    if-nez v0, :cond_1

    .line 19
    .line 20
    const/4 v0, -0x1

    .line 21
    return v0

    .line 22
    :cond_1
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    return v0
.end method

.method public final k(II)Landroid/view/View;
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getOrientationHelper()Landroidx/recyclerview/widget/w;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/recyclerview/widget/w;->m()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getOrientationHelper()Landroidx/recyclerview/widget/w;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroidx/recyclerview/widget/w;->i()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-le p2, p1, :cond_0

    .line 18
    .line 19
    const/4 v2, 0x1

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v2, -0x1

    .line 22
    :goto_0
    const/4 v3, 0x0

    .line 23
    if-eq p1, p2, :cond_3

    .line 24
    .line 25
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    if-eqz v4, :cond_1

    .line 30
    .line 31
    invoke-virtual {v4, p1}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildAt(I)Landroid/view/View;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    :cond_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getOrientationHelper()Landroidx/recyclerview/widget/w;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    invoke-virtual {v4, v3}, Landroidx/recyclerview/widget/w;->g(Landroid/view/View;)I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getOrientationHelper()Landroidx/recyclerview/widget/w;

    .line 44
    .line 45
    .line 46
    move-result-object v5

    .line 47
    invoke-virtual {v5, v3}, Landroidx/recyclerview/widget/w;->d(Landroid/view/View;)I

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    if-ge v4, v1, :cond_2

    .line 52
    .line 53
    if-le v5, v0, :cond_2

    .line 54
    .line 55
    return-object v3

    .line 56
    :cond_2
    add-int/2addr p1, v2

    .line 57
    goto :goto_0

    .line 58
    :cond_3
    return-object v3
.end method

.method public final m(III)V
    .locals 2

    .line 1
    sub-int v0, p2, p1

    .line 2
    .line 3
    div-int/lit8 v0, v0, 0x2

    .line 4
    .line 5
    sub-int v0, p2, v0

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-le v0, p3, :cond_2

    .line 9
    .line 10
    sub-int/2addr v0, p3

    .line 11
    sub-int/2addr p1, v0

    .line 12
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    const/4 p3, -0x1

    .line 21
    if-le p2, p3, :cond_0

    .line 22
    .line 23
    move-object v1, p1

    .line 24
    :cond_0
    if-eqz v1, :cond_1

    .line 25
    .line 26
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    goto :goto_0

    .line 31
    :cond_1
    const/4 p1, 0x0

    .line 32
    :goto_0
    invoke-virtual {p0, p1}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 33
    .line 34
    .line 35
    return-void

    .line 36
    :cond_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getAdapterItemsSize()I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    sub-int/2addr p3, v0

    .line 41
    add-int/2addr p2, p3

    .line 42
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 47
    .line 48
    .line 49
    move-result p3

    .line 50
    if-ge p3, p1, :cond_3

    .line 51
    .line 52
    move-object v1, p2

    .line 53
    :cond_3
    if-eqz v1, :cond_4

    .line 54
    .line 55
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    goto :goto_1

    .line 60
    :cond_4
    add-int/lit8 p1, p1, -0x1

    .line 61
    .line 62
    :goto_1
    invoke-virtual {p0, p1}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public final n(I)V
    .locals 1

    .line 1
    const/4 v0, -0x1

    .line 2
    if-gt p1, v0, :cond_0

    .line 3
    .line 4
    goto :goto_0

    .line 5
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->j()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-ne p1, v0, :cond_1

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->i()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-ne p1, v0, :cond_2

    .line 20
    .line 21
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 22
    .line 23
    .line 24
    :cond_2
    :goto_0
    return-void
.end method

.method public final o(I)V
    .locals 5

    .line 1
    const/4 v0, -0x1

    .line 2
    if-gt p1, v0, :cond_0

    .line 3
    .line 4
    return-void

    .line 5
    :cond_0
    if-eqz p1, :cond_6

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getAdapterItemsSize()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    add-int/lit8 v1, v1, -0x1

    .line 12
    .line 13
    if-ne v1, p1, :cond_1

    .line 14
    .line 15
    goto :goto_3

    .line 16
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->j()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    const/4 v3, 0x0

    .line 29
    if-eq v2, v0, :cond_2

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_2
    move-object v1, v3

    .line 33
    :goto_0
    if-eqz v1, :cond_3

    .line 34
    .line 35
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    goto :goto_1

    .line 40
    :cond_3
    const/4 v1, 0x0

    .line 41
    :goto_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->i()I

    .line 42
    .line 43
    .line 44
    move-result v2

    .line 45
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 50
    .line 51
    .line 52
    move-result v4

    .line 53
    if-eq v4, v0, :cond_4

    .line 54
    .line 55
    move-object v3, v2

    .line 56
    :cond_4
    if-eqz v3, :cond_5

    .line 57
    .line 58
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    goto :goto_2

    .line 63
    :cond_5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getAdapterItemsSize()I

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    add-int/lit8 v0, v0, -0x1

    .line 68
    .line 69
    :goto_2
    invoke-virtual {p0, v1, v0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->m(III)V

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :cond_6
    :goto_3
    invoke-virtual {p0, p1}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 74
    .line 75
    .line 76
    return-void
.end method

.method public final setItems(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LP31/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, LO31/b;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, LO31/b;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_2

    .line 14
    .line 15
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->c:Z

    .line 16
    .line 17
    if-eqz v1, :cond_1

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->d1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :cond_1
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 24
    .line 25
    .line 26
    :cond_2
    return-void
.end method

.method public final setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LP31/g;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->f:LO31/b;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LO31/b;->q(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSelectionType(Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->d:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 2
    .line 3
    return-void
.end method

.method public final setShimmers()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->getShimmerItems()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollection;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
