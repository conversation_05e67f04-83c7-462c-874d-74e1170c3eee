.class public final synthetic Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/l;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    return-void
.end method


# virtual methods
.method public final onOffsetChanged(Lcom/google/android/material/appbar/AppBarLayout;I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/l;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    invoke-static {v0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lcom/google/android/material/appbar/AppBarLayout;I)V

    return-void
.end method
