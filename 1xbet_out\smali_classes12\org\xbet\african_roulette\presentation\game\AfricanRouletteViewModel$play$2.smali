.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteViewModel$play$2"
    f = "AfricanRouletteViewModel.kt"
    l = {
        0x132,
        0x133,
        0x135
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->k4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;

    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x3

    .line 9
    const/4 v4, 0x1

    .line 10
    const/4 v5, 0x2

    .line 11
    if-eqz v1, :cond_3

    .line 12
    .line 13
    if-eq v1, v4, :cond_2

    .line 14
    .line 15
    if-eq v1, v5, :cond_1

    .line 16
    .line 17
    if-ne v1, v3, :cond_0

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto/16 :goto_3

    .line 23
    .line 24
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 25
    .line 26
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 27
    .line 28
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    throw p1

    .line 32
    :cond_1
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->L$0:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v1, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 35
    .line 36
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    goto :goto_1

    .line 40
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 48
    .line 49
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->I3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lkotlinx/coroutines/flow/V;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    :cond_4
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    move-object v6, v1

    .line 58
    check-cast v6, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 59
    .line 60
    const/4 v7, 0x0

    .line 61
    invoke-static {v6, v7, v7, v5, v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;->b(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;ZZILjava/lang/Object;)Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$e;

    .line 62
    .line 63
    .line 64
    move-result-object v6

    .line 65
    invoke-interface {p1, v1, v6}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    if-eqz v1, :cond_4

    .line 70
    .line 71
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 72
    .line 73
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$b;

    .line 74
    .line 75
    invoke-direct {v1, v7}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a$b;-><init>(Z)V

    .line 76
    .line 77
    .line 78
    invoke-static {p1, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->M3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$a;)V

    .line 79
    .line 80
    .line 81
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 82
    .line 83
    new-instance v1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;

    .line 84
    .line 85
    invoke-direct {v1, v7}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$d;-><init>(Z)V

    .line 86
    .line 87
    .line 88
    invoke-static {p1, v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->N3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;)V

    .line 89
    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 92
    .line 93
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-virtual {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->o()Lkotlinx/coroutines/flow/e;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    iput v4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->label:I

    .line 102
    .line 103
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    if-ne p1, v0, :cond_5

    .line 108
    .line 109
    goto :goto_2

    .line 110
    :cond_5
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 111
    .line 112
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 113
    .line 114
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 115
    .line 116
    .line 117
    move-result-object v1

    .line 118
    iget-object v4, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 119
    .line 120
    invoke-static {v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 121
    .line 122
    .line 123
    move-result-object v4

    .line 124
    iput-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->L$0:Ljava/lang/Object;

    .line 125
    .line 126
    iput v5, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->label:I

    .line 127
    .line 128
    invoke-virtual {v4, p1, p0}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->r(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    if-ne p1, v0, :cond_6

    .line 133
    .line 134
    goto :goto_2

    .line 135
    :cond_6
    :goto_1
    check-cast p1, Lig/b;

    .line 136
    .line 137
    invoke-virtual {v1, p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->u(Lig/b;)V

    .line 138
    .line 139
    .line 140
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 141
    .line 142
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 147
    .line 148
    invoke-static {v1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    invoke-virtual {v1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->f()F

    .line 153
    .line 154
    .line 155
    move-result v1

    .line 156
    invoke-virtual {p1, v1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->w(F)V

    .line 157
    .line 158
    .line 159
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 160
    .line 161
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    sget-object v1, LTv/a$k;->a:LTv/a$k;

    .line 166
    .line 167
    iput-object v2, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->L$0:Ljava/lang/Object;

    .line 168
    .line 169
    iput v3, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->label:I

    .line 170
    .line 171
    invoke-virtual {p1, v1, p0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    if-ne p1, v0, :cond_7

    .line 176
    .line 177
    :goto_2
    return-object v0

    .line 178
    :cond_7
    :goto_3
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$play$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 179
    .line 180
    invoke-static {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->P3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)V

    .line 181
    .line 182
    .line 183
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 184
    .line 185
    return-object p1
.end method
