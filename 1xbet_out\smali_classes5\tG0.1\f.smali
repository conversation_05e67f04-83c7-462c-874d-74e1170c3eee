.class public interface abstract LtG0/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtG0/f$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u0000 \u000f2\u00020\u0001:\u0001\u000fJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\'\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u0007H\'\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\'\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LtG0/f;",
        "",
        "Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;",
        "lastGameRepositoryImpl",
        "LwG0/a;",
        "b",
        "(Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;)LwG0/a;",
        "LxG0/f;",
        "getLastGameUseCase",
        "LxG0/e;",
        "c",
        "(LxG0/f;)LxG0/e;",
        "LxG0/i;",
        "loadLastGameDataUseCase",
        "LxG0/h;",
        "a",
        "(LxG0/i;)LxG0/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LtG0/f$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LtG0/f$a;->a:LtG0/f$a;

    .line 2
    .line 3
    sput-object v0, LtG0/f;->a:LtG0/f$a;

    .line 4
    .line 5
    return-void
.end method


# virtual methods
.method public abstract a(LxG0/i;)LxG0/h;
    .param p1    # LxG0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract b(Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;)LwG0/a;
    .param p1    # Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract c(LxG0/f;)LxG0/e;
    .param p1    # LxG0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
