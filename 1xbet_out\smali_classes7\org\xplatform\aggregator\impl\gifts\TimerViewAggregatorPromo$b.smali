.class public final Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function0<",
        "LS91/u1;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Landroid/view/ViewGroup;

.field public final synthetic b:Landroid/view/ViewGroup;

.field public final synthetic c:Z


# direct methods
.method public constructor <init>(Landroid/view/ViewGroup;Landroid/view/ViewGroup;Z)V
    .locals 0

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->a:Landroid/view/ViewGroup;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->b:Landroid/view/ViewGroup;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->c:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()LL2/a;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LS91/u1;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->a:Landroid/view/ViewGroup;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->b:Landroid/view/ViewGroup;

    .line 12
    .line 13
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->c:Z

    .line 14
    .line 15
    invoke-static {v0, v1, v2}, LS91/u1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/u1;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;->a()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
