.class public final Lorg/xbet/tile_matching/data/repositories/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/data/repositories/a;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/data/repositories/a;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/tile_matching/data/repositories/a;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/tile_matching/data/repositories/a;->d:LBc/a;

    .line 11
    .line 12
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/tile_matching/data/repositories/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/tile_matching/data/data_sources/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;)",
            "Lorg/xbet/tile_matching/data/repositories/a;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/data/repositories/a;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xbet/tile_matching/data/repositories/a;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lc8/h;Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lorg/xbet/tile_matching/data/data_sources/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;-><init>(Lc8/h;Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lorg/xbet/tile_matching/data/data_sources/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/a;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lc8/h;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/a;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/a;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, Lorg/xbet/tile_matching/data/data_sources/a;

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xbet/tile_matching/data/repositories/a;->d:LBc/a;

    .line 26
    .line 27
    invoke-interface {v3}, LBc/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    check-cast v3, Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 32
    .line 33
    invoke-static {v0, v1, v2, v3}, Lorg/xbet/tile_matching/data/repositories/a;->c(Lc8/h;Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lorg/xbet/tile_matching/data/data_sources/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/data/repositories/a;->b()Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
