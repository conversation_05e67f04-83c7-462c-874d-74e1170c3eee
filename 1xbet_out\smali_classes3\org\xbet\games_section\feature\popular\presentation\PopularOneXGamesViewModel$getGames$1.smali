.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$getGames$1"
    f = "PopularOneXGamesViewModel.kt"
    l = {
        0x212,
        0x213
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->L$0:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v1, Ljava/util/List;

    .line 22
    .line 23
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    goto :goto_2

    .line 27
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 28
    .line 29
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 30
    .line 31
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 43
    .line 44
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lv30/b;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->label:I

    .line 49
    .line 50
    invoke-interface {p1, p0}, Lv30/b;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    if-ne p1, v0, :cond_3

    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_3
    :goto_0
    move-object v1, p1

    .line 58
    check-cast v1, Ljava/util/List;

    .line 59
    .line 60
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 61
    .line 62
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lv30/b;

    .line 63
    .line 64
    .line 65
    move-result-object v3

    .line 66
    iput-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->L$0:Ljava/lang/Object;

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->L$1:Ljava/lang/Object;

    .line 69
    .line 70
    iput v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->label:I

    .line 71
    .line 72
    invoke-interface {v3, p0}, Lv30/b;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    if-ne v2, v0, :cond_4

    .line 77
    .line 78
    :goto_1
    return-object v0

    .line 79
    :cond_4
    move-object v0, p1

    .line 80
    move-object p1, v2

    .line 81
    :goto_2
    check-cast p1, Ljava/util/List;

    .line 82
    .line 83
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    new-instance v0, Ljava/util/ArrayList;

    .line 88
    .line 89
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 90
    .line 91
    .line 92
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    :cond_5
    :goto_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    if-eqz v2, :cond_6

    .line 101
    .line 102
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    move-object v3, v2

    .line 107
    check-cast v3, Ls30/b;

    .line 108
    .line 109
    invoke-virtual {v3}, Ls30/b;->e()Ljava/util/List;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    invoke-interface {v3}, Ljava/util/Collection;->isEmpty()Z

    .line 114
    .line 115
    .line 116
    move-result v3

    .line 117
    if-nez v3, :cond_5

    .line 118
    .line 119
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 120
    .line 121
    .line 122
    goto :goto_3

    .line 123
    :cond_6
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 124
    .line 125
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->L3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Z

    .line 126
    .line 127
    .line 128
    move-result p1

    .line 129
    if-nez p1, :cond_7

    .line 130
    .line 131
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->c4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lcom/xbet/onexuser/domain/user/c;

    .line 134
    .line 135
    .line 136
    move-result-object v2

    .line 137
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 138
    .line 139
    .line 140
    move-result v2

    .line 141
    invoke-static {p1, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->o4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V

    .line 142
    .line 143
    .line 144
    :cond_7
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 145
    .line 146
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Y3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 151
    .line 152
    .line 153
    move-result v2

    .line 154
    if-eqz v2, :cond_8

    .line 155
    .line 156
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 157
    .line 158
    goto :goto_4

    .line 159
    :cond_8
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 160
    .line 161
    new-instance v3, Lb50/i$a;

    .line 162
    .line 163
    invoke-direct {v3, v0}, Lb50/i$a;-><init>(Ljava/util/List;)V

    .line 164
    .line 165
    .line 166
    invoke-direct {v2, v3}, Lorg/xbet/games_section/feature/popular/presentation/a$d;-><init>(Ljava/lang/Object;)V

    .line 167
    .line 168
    .line 169
    move-object v0, v2

    .line 170
    :goto_4
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 171
    .line 172
    .line 173
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 174
    .line 175
    invoke-static {p1, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)V

    .line 176
    .line 177
    .line 178
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 179
    .line 180
    return-object p1
.end method
