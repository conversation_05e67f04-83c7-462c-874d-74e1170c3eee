.class public final synthetic LNB0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lkotlin/jvm/functions/Function1;

.field public final synthetic c:Landroidx/compose/runtime/r1;


# direct methods
.method public synthetic constructor <init>(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, LNB0/d;->a:Z

    iput-object p2, p0, LNB0/d;->b:Lkotlin/jvm/functions/Function1;

    iput-object p3, p0, LNB0/d;->c:Landroidx/compose/runtime/r1;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-boolean v0, p0, LNB0/d;->a:Z

    iget-object v1, p0, LNB0/d;->b:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    iget-object v2, p0, LNB0/d;->c:Landroidx/compose/runtime/r1;

    invoke-static {v0, v1, v2}, LNB0/g$a;->d(ZLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
