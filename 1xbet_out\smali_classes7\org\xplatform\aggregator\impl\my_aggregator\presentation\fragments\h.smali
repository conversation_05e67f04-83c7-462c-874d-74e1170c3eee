.class public final synthetic Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

.field public final synthetic b:Lorg/xplatform/banners/api/domain/models/BannerModel;

.field public final synthetic c:Ljava/util/List;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->b:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->c:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->b:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;->c:Ljava/util/List;

    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->m3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
