.class public final Lorg/xplatform/aggregator/impl/gifts/usecases/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J \u0010\n\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/usecases/f;",
        "",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;",
        "aggregatorInteractor",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;)V",
        "",
        "activeChipTypeId",
        "giftTypeId",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "a",
        "(II)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/f;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(II)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/f;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;->a()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/f;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;

    .line 8
    .line 9
    const/4 v2, -0x1

    .line 10
    if-ne v0, v2, :cond_0

    .line 11
    .line 12
    move p1, p2

    .line 13
    :cond_0
    invoke-virtual {v1, p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;->b(I)V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->Companion:Lorg/xplatform/aggregator/api/navigation/GiftsChipType$a;

    .line 17
    .line 18
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/f;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;

    .line 19
    .line 20
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;->a()I

    .line 21
    .line 22
    .line 23
    move-result p2

    .line 24
    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/api/navigation/GiftsChipType$a;->a(I)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    return-object p1
.end method
