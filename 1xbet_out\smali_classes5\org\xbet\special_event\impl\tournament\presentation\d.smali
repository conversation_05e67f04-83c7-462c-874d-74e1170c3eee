.class public final Lorg/xbet/special_event/impl/tournament/presentation/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LVo/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->j0:LVo/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LVo/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->k0:LVo/b;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lbl0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->m0:Lbl0/b;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lbl0/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->l0:Lbl0/d;

    .line 2
    .line 3
    return-void
.end method

.method public static e(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->o0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static f(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;LRw0/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->i0:LRw0/f;

    .line 2
    .line 3
    return-void
.end method

.method public static g(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->n0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 2
    .line 3
    return-void
.end method
