.class public final LuV0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "LvV0/f;",
        "Lorg/xbet/toto_bet/domain/TotoBetType;",
        "totoBetType",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d;",
        "a",
        "(LvV0/f;Lorg/xbet/toto_bet/domain/TotoBetType;LHX0/e;)Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LvV0/f;Lorg/xbet/toto_bet/domain/TotoBetType;LHX0/e;)Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d;
    .locals 12
    .param p0    # LvV0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/toto_bet/domain/TotoBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LvV0/f;->i()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    new-instance p1, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$c;

    .line 8
    .line 9
    invoke-virtual {p0}, LvV0/f;->d()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-virtual {p0}, LvV0/f;->c()J

    .line 14
    .line 15
    .line 16
    move-result-wide v0

    .line 17
    invoke-direct {p1, p2, v0, v1}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$c;-><init>(Ljava/lang/String;J)V

    .line 18
    .line 19
    .line 20
    return-object p1

    .line 21
    :cond_0
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {v0}, LnV0/c;->b()Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    if-eqz v0, :cond_1

    .line 40
    .line 41
    new-instance p1, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$a;

    .line 42
    .line 43
    invoke-virtual {p0}, LvV0/f;->f()Lorg/xbet/uikit/components/lottie/a;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    invoke-direct {p1, p0}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$a;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 48
    .line 49
    .line 50
    return-object p1

    .line 51
    :cond_1
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    if-eqz v0, :cond_9

    .line 56
    .line 57
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {v0}, LnV0/c;->b()Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    if-eqz v1, :cond_3

    .line 74
    .line 75
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    move-object v2, v1

    .line 80
    check-cast v2, LnV0/b;

    .line 81
    .line 82
    invoke-virtual {v2}, LnV0/b;->k()Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    sget-object v3, Lorg/xbet/toto_bet/tirage/domain/model/TirageState;->ACTIVE:Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 87
    .line 88
    if-ne v2, v3, :cond_2

    .line 89
    .line 90
    goto :goto_0

    .line 91
    :cond_3
    const/4 v1, 0x0

    .line 92
    :goto_0
    check-cast v1, LnV0/b;

    .line 93
    .line 94
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-virtual {v0}, LnV0/c;->b()Ljava/util/List;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    new-instance v2, Ljava/util/ArrayList;

    .line 103
    .line 104
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 105
    .line 106
    .line 107
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    :cond_4
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 112
    .line 113
    .line 114
    move-result v3

    .line 115
    if-eqz v3, :cond_5

    .line 116
    .line 117
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 118
    .line 119
    .line 120
    move-result-object v3

    .line 121
    move-object v4, v3

    .line 122
    check-cast v4, LnV0/b;

    .line 123
    .line 124
    invoke-virtual {v4}, LnV0/b;->k()Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 125
    .line 126
    .line 127
    move-result-object v4

    .line 128
    sget-object v5, Lorg/xbet/toto_bet/tirage/domain/model/TirageState;->ACTIVE:Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 129
    .line 130
    if-eq v4, v5, :cond_4

    .line 131
    .line 132
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 133
    .line 134
    .line 135
    goto :goto_1

    .line 136
    :cond_5
    new-instance v0, Ljava/util/ArrayList;

    .line 137
    .line 138
    const/16 v3, 0xa

    .line 139
    .line 140
    invoke-static {v2, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 141
    .line 142
    .line 143
    move-result v3

    .line 144
    invoke-direct {v0, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 145
    .line 146
    .line 147
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 148
    .line 149
    .line 150
    move-result-object v2

    .line 151
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 152
    .line 153
    .line 154
    move-result v3

    .line 155
    if-eqz v3, :cond_6

    .line 156
    .line 157
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 158
    .line 159
    .line 160
    move-result-object v3

    .line 161
    check-cast v3, LnV0/b;

    .line 162
    .line 163
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 164
    .line 165
    .line 166
    move-result-object v4

    .line 167
    invoke-virtual {v4}, LnV0/c;->a()Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v4

    .line 171
    invoke-static {v3, p1, p2, v4}, LuV0/a;->a(LnV0/b;Lorg/xbet/toto_bet/domain/TotoBetType;LHX0/e;Ljava/lang/String;)Ljava/util/List;

    .line 172
    .line 173
    .line 174
    move-result-object v3

    .line 175
    invoke-interface {v0, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 176
    .line 177
    .line 178
    goto :goto_2

    .line 179
    :cond_6
    invoke-static {v0}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 180
    .line 181
    .line 182
    move-result-object v5

    .line 183
    invoke-virtual {p0}, LvV0/f;->d()Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v6

    .line 187
    invoke-virtual {p0}, LvV0/f;->c()J

    .line 188
    .line 189
    .line 190
    move-result-wide v10

    .line 191
    sget-object p2, Lorg/xbet/toto_bet/domain/TotoBetType;->TOTO_1XTOTO:Lorg/xbet/toto_bet/domain/TotoBetType;

    .line 192
    .line 193
    if-ne p1, p2, :cond_7

    .line 194
    .line 195
    sget-object p1, Lorg/xbet/toto_bet/tirage/presentation/model/HeaderState;->OneXHeader:Lorg/xbet/toto_bet/tirage/presentation/model/HeaderState;

    .line 196
    .line 197
    :goto_3
    move-object v7, p1

    .line 198
    goto :goto_4

    .line 199
    :cond_7
    sget-object p1, Lorg/xbet/toto_bet/tirage/presentation/model/HeaderState;->MainHeader:Lorg/xbet/toto_bet/tirage/presentation/model/HeaderState;

    .line 200
    .line 201
    goto :goto_3

    .line 202
    :goto_4
    if-nez v1, :cond_8

    .line 203
    .line 204
    sget-object p1, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$b$a;->a:Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$b$a;

    .line 205
    .line 206
    :goto_5
    move-object v8, p1

    .line 207
    goto :goto_6

    .line 208
    :cond_8
    new-instance p1, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$b$b;

    .line 209
    .line 210
    invoke-direct {p1, v1}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$b$b;-><init>(LnV0/b;)V

    .line 211
    .line 212
    .line 213
    goto :goto_5

    .line 214
    :goto_6
    invoke-virtual {p0}, LvV0/f;->h()LnV0/c;

    .line 215
    .line 216
    .line 217
    move-result-object p0

    .line 218
    invoke-virtual {p0}, LnV0/c;->a()Ljava/lang/String;

    .line 219
    .line 220
    .line 221
    move-result-object v9

    .line 222
    new-instance v4, LvV0/e;

    .line 223
    .line 224
    invoke-direct/range {v4 .. v11}, LvV0/e;-><init>(Ljava/util/List;Ljava/lang/String;Lorg/xbet/toto_bet/tirage/presentation/model/HeaderState;Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$b;Ljava/lang/String;J)V

    .line 225
    .line 226
    .line 227
    new-instance p0, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$d;

    .line 228
    .line 229
    invoke-direct {p0, v4}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$d;-><init>(LvV0/e;)V

    .line 230
    .line 231
    .line 232
    return-object p0

    .line 233
    :cond_9
    new-instance p1, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$b;

    .line 234
    .line 235
    invoke-virtual {p0}, LvV0/f;->g()Lorg/xbet/uikit/components/lottie/a;

    .line 236
    .line 237
    .line 238
    move-result-object p0

    .line 239
    invoke-direct {p1, p0}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoBetTirageViewModel$d$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 240
    .line 241
    .line 242
    return-object p1
.end method
