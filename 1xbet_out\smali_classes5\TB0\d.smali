.class public final LTB0/d;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LTB0/c;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u0006\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u0081\u0001\u0008\u0007\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u001e\u0010\r\u001a\u001a\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000c\u0012\u0004\u0012\u00020\u00050\n\u0012\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "LTB0/d;",
        "LA4/e;",
        "LTB0/c;",
        "Lkotlin/Function1;",
        "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
        "",
        "betEventClickListener",
        "betEventLongClickListener",
        "LTB0/b;",
        "marketHeaderClickListener",
        "Lkotlin/Function3;",
        "",
        "",
        "selectBetButtonListener",
        "pineMarketListener",
        "",
        "isPlayersDuel",
        "<init>",
        "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/n;Lkotlin/jvm/functions/Function1;Z)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/n;Lkotlin/jvm/functions/Function1;Z)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LTB0/b;",
            "Lkotlin/Unit;",
            ">;",
            "LOc/n<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Double;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LTB0/b;",
            "Lkotlin/Unit;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    sget-object v0, LTB0/c;->a:LTB0/c$a;

    .line 2
    .line 3
    invoke-virtual {v0}, LTB0/c$a;->a()Landroidx/recyclerview/widget/i$f;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 11
    .line 12
    invoke-static {p3, p5, p6}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/MarketHeaderAdapterDelegateKt;->r(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Z)LA4/c;

    .line 13
    .line 14
    .line 15
    move-result-object p3

    .line 16
    invoke-virtual {v0, p3}, LA4/d;->c(LA4/c;)LA4/d;

    .line 17
    .line 18
    .line 19
    move-result-object p3

    .line 20
    invoke-static {p1, p2, p6}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/EventBetAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Z)LA4/c;

    .line 21
    .line 22
    .line 23
    move-result-object p5

    .line 24
    invoke-virtual {p3, p5}, LA4/d;->c(LA4/c;)LA4/d;

    .line 25
    .line 26
    .line 27
    move-result-object p3

    .line 28
    invoke-static {p1, p2, p4, p6}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/EventBetAccuracyAdapterDelegateKt;->t(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/n;Z)LA4/c;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-virtual {p3, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-static {}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/HiddenMarketsCounterAdapterDelegateKt;->d()LA4/c;

    .line 37
    .line 38
    .line 39
    move-result-object p2

    .line 40
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 41
    .line 42
    .line 43
    return-void
.end method
