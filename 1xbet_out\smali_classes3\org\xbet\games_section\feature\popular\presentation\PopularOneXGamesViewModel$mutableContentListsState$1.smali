.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/q;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$mutableContentListsState$1"
    f = "PopularOneXGamesViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;-><init>(LDg/c;Lorg/xbet/core/domain/usecases/d;Lv30/b;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lm8/a;LJT/c;Lhf0/a;LwX0/a;LHX0/e;LVg0/a;Lw30/o;LR40/c;Lgk/b;Lkc1/a;Lw30/i;Lp9/c;Lw30/b;LSX0/c;LSR/a;LpS/b;Lcom/xbet/onexuser/domain/user/c;Lorg/xbet/analytics/domain/scope/NewsAnalytics;Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lorg/xbet/remoteconfig/domain/usecases/i;Lv30/a;LwX0/c;Landroidx/lifecycle/Q;Lp30/a;Lorg/xbet/games_section/feature/popular/presentation/b;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/q<",
        "Lorg/xbet/games_section/feature/popular/presentation/a<",
        "+",
        "Lb50/c;",
        ">;",
        "Lorg/xbet/games_section/feature/popular/presentation/a<",
        "+",
        "Lb50/e;",
        ">;",
        "Lorg/xbet/games_section/feature/popular/presentation/a<",
        "+",
        "Lb50/i;",
        ">;",
        "Lorg/xbet/games_section/feature/popular/presentation/a<",
        "+",
        "Lb50/a;",
        ">;",
        "Lorg/xbet/games_section/feature/popular/presentation/a<",
        "+",
        "Lb50/d;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00002\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00002\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0000H\n\u00a2\u0006\u0004\u0008\r\u0010\u000e"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular/presentation/a;",
        "Lb50/c;",
        "jackpotState",
        "Lb50/e;",
        "bannersState",
        "Lb50/i;",
        "gamesState",
        "Lb50/a;",
        "centerOfAttentionState",
        "Lb50/d;",
        "luckyWheelState",
        "",
        "LVX0/i;",
        "<anonymous>",
        "(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field synthetic L$2:Ljava/lang/Object;

.field synthetic L$3:Ljava/lang/Object;

.field synthetic L$4:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    const/4 p1, 0x6

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/a;

    check-cast p2, Lorg/xbet/games_section/feature/popular/presentation/a;

    check-cast p3, Lorg/xbet/games_section/feature/popular/presentation/a;

    check-cast p4, Lorg/xbet/games_section/feature/popular/presentation/a;

    check-cast p5, Lorg/xbet/games_section/feature/popular/presentation/a;

    check-cast p6, Lkotlin/coroutines/e;

    invoke-virtual/range {p0 .. p6}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->invoke(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/c;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/e;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/i;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/a;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/d;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-direct {v0, v1, p6}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$1:Ljava/lang/Object;

    iput-object p3, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$2:Ljava/lang/Object;

    iput-object p4, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$3:Ljava/lang/Object;

    iput-object p5, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$4:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v1, p1

    .line 14
    check-cast v1, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$1:Ljava/lang/Object;

    .line 17
    .line 18
    move-object v2, p1

    .line 19
    check-cast v2, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$2:Ljava/lang/Object;

    .line 22
    .line 23
    move-object v3, p1

    .line 24
    check-cast v3, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$3:Ljava/lang/Object;

    .line 27
    .line 28
    move-object v4, p1

    .line 29
    check-cast v4, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->L$4:Ljava/lang/Object;

    .line 32
    .line 33
    move-object v5, p1

    .line 34
    check-cast v5, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 35
    .line 36
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 37
    .line 38
    invoke-static/range {v0 .. v5}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    return-object p1

    .line 43
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 44
    .line 45
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 46
    .line 47
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    throw p1
.end method
