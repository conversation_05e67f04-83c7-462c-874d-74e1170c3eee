.class public final LsA0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u000b\u0008\u0000\u0018\u00002\u00020\u0001B+\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0001\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0015\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001d\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0011\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0015\u0010\u0014\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u0014\u0010\u0010J\u001d\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u0019\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u001cR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u001dR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u001eR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u001f\u00a8\u0006 "
    }
    d2 = {
        "LsA0/a;",
        "",
        "LDg/a;",
        "gamesAnalytics",
        "Lorg/xbet/analytics/domain/scope/O;",
        "favouriteAnalytics",
        "LHR/a;",
        "gamesFatmanLogger",
        "",
        "screenName",
        "<init>",
        "(LDg/a;Lorg/xbet/analytics/domain/scope/O;LHR/a;Ljava/lang/String;)V",
        "",
        "sportId",
        "",
        "b",
        "(J)V",
        "option",
        "a",
        "(JLjava/lang/String;)V",
        "d",
        "",
        "isFavourite",
        "c",
        "(JZ)V",
        "point",
        "e",
        "(Ljava/lang/String;)V",
        "LDg/a;",
        "Lorg/xbet/analytics/domain/scope/O;",
        "LHR/a;",
        "Ljava/lang/String;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LDg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/analytics/domain/scope/O;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LHR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDg/a;Lorg/xbet/analytics/domain/scope/O;LHR/a;Ljava/lang/String;)V
    .locals 0
    .param p1    # LDg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/analytics/domain/scope/O;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LsA0/a;->a:LDg/a;

    .line 5
    .line 6
    iput-object p2, p0, LsA0/a;->b:Lorg/xbet/analytics/domain/scope/O;

    .line 7
    .line 8
    iput-object p3, p0, LsA0/a;->c:LHR/a;

    .line 9
    .line 10
    iput-object p4, p0, LsA0/a;->d:Ljava/lang/String;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(JLjava/lang/String;)V
    .locals 2
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LsA0/a;->a:LDg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2, p3}, LDg/a;->b(JLjava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LsA0/a;->c:LHR/a;

    .line 7
    .line 8
    iget-object v1, p0, LsA0/a;->d:Ljava/lang/String;

    .line 9
    .line 10
    invoke-interface {v0, v1, p1, p2, p3}, LHR/a;->g(Ljava/lang/String;JLjava/lang/String;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final b(J)V
    .locals 2

    .line 1
    iget-object v0, p0, LsA0/a;->a:LDg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, LDg/a;->d(J)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LsA0/a;->c:LHR/a;

    .line 7
    .line 8
    iget-object v1, p0, LsA0/a;->d:Ljava/lang/String;

    .line 9
    .line 10
    invoke-interface {v0, v1, p1, p2}, LHR/a;->l(Ljava/lang/String;J)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final c(JZ)V
    .locals 2

    .line 1
    iget-object v0, p0, LsA0/a;->b:Lorg/xbet/analytics/domain/scope/O;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2, p3}, Lorg/xbet/analytics/domain/scope/O;->B(JZ)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LsA0/a;->c:LHR/a;

    .line 7
    .line 8
    iget-object v1, p0, LsA0/a;->d:Ljava/lang/String;

    .line 9
    .line 10
    invoke-interface {v0, v1, p1, p2, p3}, LHR/a;->i(Ljava/lang/String;JZ)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final d(J)V
    .locals 2

    .line 1
    iget-object v0, p0, LsA0/a;->a:LDg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, LDg/a;->n(J)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LsA0/a;->c:LHR/a;

    .line 7
    .line 8
    iget-object v1, p0, LsA0/a;->d:Ljava/lang/String;

    .line 9
    .line 10
    invoke-interface {v0, v1, p1, p2}, LHR/a;->n(Ljava/lang/String;J)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final e(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LsA0/a;->a:LDg/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LDg/a;->o(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LsA0/a;->c:LHR/a;

    .line 7
    .line 8
    iget-object v1, p0, LsA0/a;->d:Ljava/lang/String;

    .line 9
    .line 10
    invoke-interface {v0, v1, p1}, LHR/a;->o(Ljava/lang/String;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method
