.class public final LIN0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIN0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/o<",
        "Landroidx/compose/foundation/lazy/c;",
        "Ljava/lang/Integer;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LIN0/a$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LIN0/a$a;

    .line 2
    .line 3
    invoke-direct {v0}, LIN0/a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LIN0/a$a;->a:LIN0/a$a;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V
    .locals 1

    .line 1
    and-int/lit16 p1, p4, 0x81

    .line 2
    .line 3
    const/16 p2, 0x80

    .line 4
    .line 5
    if-ne p1, p2, :cond_1

    .line 6
    .line 7
    invoke-interface {p3}, Landroidx/compose/runtime/j;->c()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-interface {p3}, Landroidx/compose/runtime/j;->n()V

    .line 15
    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    if-eqz p1, :cond_2

    .line 23
    .line 24
    const/4 p1, -0x1

    .line 25
    const-string p2, "org.xbet.statistic.statistic_core.presentation.composable.ComposableSingletons$ShimmerContentKt.lambda$1229017014.<anonymous> (ShimmerContent.kt:43)"

    .line 26
    .line 27
    const v0, 0x49414fb6    # 791803.4f

    .line 28
    .line 29
    .line 30
    invoke-static {v0, p4, p1, p2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    :cond_2
    sget-object p1, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 34
    .line 35
    sget-object p2, LA11/a;->a:LA11/a;

    .line 36
    .line 37
    invoke-virtual {p2}, LA11/a;->v0()F

    .line 38
    .line 39
    .line 40
    move-result p4

    .line 41
    invoke-virtual {p2}, LA11/a;->T0()F

    .line 42
    .line 43
    .line 44
    move-result v0

    .line 45
    invoke-static {p1, v0, p4}, Landroidx/compose/foundation/layout/SizeKt;->x(Landroidx/compose/ui/l;FF)Landroidx/compose/ui/l;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-virtual {p2}, LA11/a;->P()F

    .line 50
    .line 51
    .line 52
    move-result p2

    .line 53
    invoke-static {p2}, LR/i;->f(F)LR/h;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    invoke-static {p1, p2}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    const/4 p2, 0x0

    .line 62
    const/4 p4, 0x1

    .line 63
    const/4 v0, 0x0

    .line 64
    invoke-static {p2, p3, v0, p4}, Lp11/a;->b(Lorg/xbet/uikit/compose/components/skeleton/model/DsSkeletonContentBackgroundStyle;Landroidx/compose/runtime/j;II)Lp11/c;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    invoke-static {p1, p2}, Lo11/a;->a(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-static {p1, p3, v0}, Landroidx/compose/foundation/layout/BoxKt;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 73
    .line 74
    .line 75
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 76
    .line 77
    .line 78
    move-result p1

    .line 79
    if-eqz p1, :cond_3

    .line 80
    .line 81
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 82
    .line 83
    .line 84
    :cond_3
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/lazy/c;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    check-cast p3, Landroidx/compose/runtime/j;

    .line 10
    .line 11
    check-cast p4, Ljava/lang/Number;

    .line 12
    .line 13
    invoke-virtual {p4}, Ljava/lang/Number;->intValue()I

    .line 14
    .line 15
    .line 16
    move-result p4

    .line 17
    invoke-virtual {p0, p1, p2, p3, p4}, LIN0/a$a;->a(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V

    .line 18
    .line 19
    .line 20
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p1
.end method
