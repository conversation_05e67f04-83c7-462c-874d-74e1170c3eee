.class public final LqC0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0010\u0018\u00002\u00020\u0001BA\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\'\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0016H\u0000\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'\u00a8\u0006("
    }
    d2 = {
        "LqC0/d;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "LDg/a;",
        "gamesAnalytics",
        "LlB0/a;",
        "subGameFiltersDialogFactory",
        "LWb0/a;",
        "tipsDialogFeature",
        "LuB0/a;",
        "marketsFragmentFactory",
        "LiR/a;",
        "fatmanFeature",
        "LHX0/e;",
        "resourceManager",
        "<init>",
        "(LQW0/c;LDg/a;LlB0/a;LWb0/a;LuB0/a;LiR/a;LHX0/e;)V",
        "Lorg/xbet/sportgame/subgames/api/SubGamesParams;",
        "params",
        "",
        "screenName",
        "LKA0/c$a;",
        "sportGameCoreLibProvider",
        "LqC0/c;",
        "a",
        "(Lorg/xbet/sportgame/subgames/api/SubGamesParams;Ljava/lang/String;LKA0/c$a;)LqC0/c;",
        "LQW0/c;",
        "b",
        "LDg/a;",
        "c",
        "LlB0/a;",
        "d",
        "LWb0/a;",
        "e",
        "LuB0/a;",
        "f",
        "LiR/a;",
        "g",
        "LHX0/e;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LDg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LlB0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LWb0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LuB0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LiR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LDg/a;LlB0/a;LWb0/a;LuB0/a;LiR/a;LHX0/e;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LDg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LlB0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LWb0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LuB0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LiR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LqC0/d;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, LqC0/d;->b:LDg/a;

    .line 7
    .line 8
    iput-object p3, p0, LqC0/d;->c:LlB0/a;

    .line 9
    .line 10
    iput-object p4, p0, LqC0/d;->d:LWb0/a;

    .line 11
    .line 12
    iput-object p5, p0, LqC0/d;->e:LuB0/a;

    .line 13
    .line 14
    iput-object p6, p0, LqC0/d;->f:LiR/a;

    .line 15
    .line 16
    iput-object p7, p0, LqC0/d;->g:LHX0/e;

    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/sportgame/subgames/api/SubGamesParams;Ljava/lang/String;LKA0/c$a;)LqC0/c;
    .locals 11
    .param p1    # Lorg/xbet/sportgame/subgames/api/SubGamesParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LKA0/c$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LqC0/a;->a()LqC0/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LqC0/d;->a:LQW0/c;

    .line 6
    .line 7
    invoke-interface {p3}, LKA0/c$a;->z0()LKA0/c;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    iget-object v6, p0, LqC0/d;->b:LDg/a;

    .line 12
    .line 13
    iget-object v7, p0, LqC0/d;->c:LlB0/a;

    .line 14
    .line 15
    iget-object v3, p0, LqC0/d;->d:LWb0/a;

    .line 16
    .line 17
    iget-object v8, p0, LqC0/d;->e:LuB0/a;

    .line 18
    .line 19
    iget-object v4, p0, LqC0/d;->f:LiR/a;

    .line 20
    .line 21
    iget-object v10, p0, LqC0/d;->g:LHX0/e;

    .line 22
    .line 23
    move-object v5, p1

    .line 24
    move-object v9, p2

    .line 25
    invoke-interface/range {v0 .. v10}, LqC0/c$a;->a(LQW0/c;LKA0/c;LWb0/a;LiR/a;Lorg/xbet/sportgame/subgames/api/SubGamesParams;LDg/a;LlB0/a;LuB0/a;Ljava/lang/String;LHX0/e;)LqC0/c;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    return-object p1
.end method
