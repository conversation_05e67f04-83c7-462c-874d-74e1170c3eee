.class public final Lorg/xbet/games_section/feature/popular_classic/presentation/a;
.super Lorg/xbet/ui_common/viewmodel/core/k;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular_classic/presentation/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\u0018\u0000 &2\u00020\u0001:\u0001\'B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001d\u0010\n\u001a\u00020\t2\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ/\u0010\u0013\u001a\u00020\u0012*\u0008\u0012\u0004\u0012\u00020\r0\u000c2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u00062\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J5\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00062\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u001bH\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ9\u0010 \u001a\u00020\u0012*\u0008\u0012\u0004\u0012\u00020\r0\u000c2\u0006\u0010\u0016\u001a\u00020\u001f2\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008 \u0010!J%\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u00062\u0006\u0010\u0016\u001a\u00020\u001f2\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\"\u0010#R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%\u00a8\u0006("
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular_classic/presentation/a;",
        "Lorg/xbet/ui_common/viewmodel/core/k;",
        "LHX0/e;",
        "resourceManager",
        "<init>",
        "(LHX0/e;)V",
        "",
        "Lf50/a;",
        "oneXGamesWithCategoryList",
        "",
        "p",
        "(Ljava/util/List;)Z",
        "",
        "LVX0/i;",
        "Lk50/g;",
        "oneXGamesDataList",
        "Lorg/xbet/core/domain/GamesCategoryTypeEnum;",
        "categoryType",
        "",
        "k",
        "(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V",
        "Lk50/h;",
        "oneXGamesUiModel",
        "Lk50/c;",
        "jackpotInfoUiModel",
        "Lk50/d;",
        "luckyWheelUiModel",
        "Lk50/a;",
        "centerOfAttentionUiModel",
        "l",
        "(Lk50/h;Lk50/c;Lk50/d;Lk50/a;)Ljava/util/List;",
        "Lk50/h$a;",
        "o",
        "(Ljava/util/List;Lk50/h$a;Lk50/c;Lk50/d;Lk50/a;)V",
        "n",
        "(Lk50/h$a;Lk50/a;)Ljava/util/List;",
        "d",
        "LHX0/e;",
        "e",
        "a",
        "popular_classic_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final e:Lorg/xbet/games_section/feature/popular_classic/presentation/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final d:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/a$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->e:Lorg/xbet/games_section/feature/popular_classic/presentation/a$a;

    return-void
.end method

.method public constructor <init>(LHX0/e;)V
    .locals 0
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/k;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->d:LHX0/e;

    .line 5
    .line 6
    return-void
.end method

.method private final k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lk50/g;",
            ">;",
            "Lorg/xbet/core/domain/GamesCategoryTypeEnum;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    :cond_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    move-object v1, v0

    .line 16
    check-cast v1, Lk50/g;

    .line 17
    .line 18
    invoke-virtual {v1}, Lk50/g;->d()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {p3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v0, 0x0

    .line 34
    :goto_0
    check-cast v0, Lk50/g;

    .line 35
    .line 36
    if-eqz v0, :cond_2

    .line 37
    .line 38
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    :cond_2
    return-void
.end method

.method private final p(Ljava/util/List;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lf50/a;",
            ">;)Z"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v1, :cond_1

    .line 11
    .line 12
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    move-object v3, v1

    .line 17
    check-cast v3, Lf50/a;

    .line 18
    .line 19
    invoke-virtual {v3}, Lf50/a;->c()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 24
    .line 25
    invoke-virtual {v4}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v3

    .line 33
    if-eqz v3, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_1
    move-object v1, v2

    .line 37
    :goto_0
    check-cast v1, Lf50/a;

    .line 38
    .line 39
    if-eqz v1, :cond_2

    .line 40
    .line 41
    invoke-virtual {v1}, Lf50/a;->e()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    if-eqz v0, :cond_2

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    check-cast v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 52
    .line 53
    if-eqz v0, :cond_2

    .line 54
    .line 55
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    if-eqz v0, :cond_2

    .line 60
    .line 61
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 62
    .line 63
    .line 64
    move-result-wide v0

    .line 65
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    goto :goto_1

    .line 70
    :cond_2
    move-object v0, v2

    .line 71
    :goto_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    if-eqz v1, :cond_4

    .line 80
    .line 81
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    move-object v3, v1

    .line 86
    check-cast v3, Lf50/a;

    .line 87
    .line 88
    invoke-virtual {v3}, Lf50/a;->c()Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object v3

    .line 92
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 93
    .line 94
    invoke-virtual {v4}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v4

    .line 98
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    move-result v3

    .line 102
    if-eqz v3, :cond_3

    .line 103
    .line 104
    goto :goto_2

    .line 105
    :cond_4
    move-object v1, v2

    .line 106
    :goto_2
    check-cast v1, Lf50/a;

    .line 107
    .line 108
    const/4 p1, 0x0

    .line 109
    if-eqz v1, :cond_8

    .line 110
    .line 111
    invoke-virtual {v1}, Lf50/a;->e()Ljava/util/List;

    .line 112
    .line 113
    .line 114
    move-result-object v3

    .line 115
    invoke-static {v3}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object v3

    .line 119
    check-cast v3, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 120
    .line 121
    if-eqz v3, :cond_5

    .line 122
    .line 123
    invoke-virtual {v3}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 124
    .line 125
    .line 126
    move-result-object v3

    .line 127
    if-eqz v3, :cond_5

    .line 128
    .line 129
    invoke-static {v3}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 130
    .line 131
    .line 132
    move-result-wide v2

    .line 133
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 134
    .line 135
    .line 136
    move-result-object v2

    .line 137
    :cond_5
    invoke-virtual {v1}, Lf50/a;->e()Ljava/util/List;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 142
    .line 143
    .line 144
    move-result v1

    .line 145
    const/4 v3, 0x1

    .line 146
    if-ne v1, v3, :cond_7

    .line 147
    .line 148
    invoke-static {v0, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 149
    .line 150
    .line 151
    move-result v0

    .line 152
    if-nez v0, :cond_6

    .line 153
    .line 154
    goto :goto_3

    .line 155
    :cond_6
    return p1

    .line 156
    :cond_7
    :goto_3
    return v3

    .line 157
    :cond_8
    return p1
.end method


# virtual methods
.method public final l(Lk50/h;Lk50/c;Lk50/d;Lk50/a;)Ljava/util/List;
    .locals 6
    .param p1    # Lk50/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lk50/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lk50/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lk50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk50/h;",
            "Lk50/c;",
            "Lk50/d;",
            "Lk50/a;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    instance-of v0, p1, Lk50/h$a;

    .line 6
    .line 7
    if-eqz v0, :cond_1

    .line 8
    .line 9
    instance-of v0, p2, Lk50/c$b;

    .line 10
    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    move-object v2, p1

    .line 14
    check-cast v2, Lk50/h$a;

    .line 15
    .line 16
    move-object v0, p0

    .line 17
    move-object v3, p2

    .line 18
    move-object v4, p3

    .line 19
    move-object v5, p4

    .line 20
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->o(Ljava/util/List;Lk50/h$a;Lk50/c;Lk50/d;Lk50/a;)V

    .line 21
    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance p1, Lk50/b;

    .line 25
    .line 26
    sget-object p2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 27
    .line 28
    invoke-virtual {p2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object p2

    .line 32
    invoke-direct {p1, p2}, Lk50/b;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    instance-of p1, p1, Lk50/h$b;

    .line 40
    .line 41
    if-eqz p1, :cond_2

    .line 42
    .line 43
    new-instance p1, Lk50/b;

    .line 44
    .line 45
    sget-object p2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 46
    .line 47
    invoke-virtual {p2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    invoke-direct {p1, p2}, Lk50/b;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    invoke-interface {v1, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 55
    .line 56
    .line 57
    :goto_0
    invoke-static {v1}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    return-object p1

    .line 62
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 63
    .line 64
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 65
    .line 66
    .line 67
    throw p1
.end method

.method public final n(Lk50/h$a;Lk50/a;)Ljava/util/List;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk50/h$a;",
            "Lk50/a;",
            ")",
            "Ljava/util/List<",
            "Lk50/g;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, p2, Lk50/a$a;

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    if-eqz v1, :cond_d

    .line 10
    .line 11
    check-cast p2, Lk50/a$a;

    .line 12
    .line 13
    invoke-virtual {p2}, Lk50/a$a;->d()Ls30/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->d:LHX0/e;

    .line 18
    .line 19
    invoke-static {v1, v3}, Lj50/b;->a(Ls30/a;LHX0/e;)Lk50/g;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    invoke-virtual {p2}, Lk50/a$a;->d()Ls30/a;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {v1}, Ls30/a;->a()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 35
    .line 36
    invoke-virtual {v3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    move-result v3

    .line 44
    if-eqz v3, :cond_1

    .line 45
    .line 46
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    new-instance v1, Ljava/util/ArrayList;

    .line 51
    .line 52
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 53
    .line 54
    .line 55
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-eqz v3, :cond_c

    .line 64
    .line 65
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    move-object v4, v3

    .line 70
    check-cast v4, Lf50/a;

    .line 71
    .line 72
    invoke-virtual {v4}, Lf50/a;->c()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v4

    .line 76
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 77
    .line 78
    invoke-virtual {v5}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v5

    .line 82
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    if-nez v4, :cond_0

    .line 87
    .line 88
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    goto :goto_0

    .line 92
    :cond_1
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 93
    .line 94
    invoke-virtual {v3}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v3

    .line 98
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    if-eqz v1, :cond_b

    .line 103
    .line 104
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    :cond_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 113
    .line 114
    .line 115
    move-result v3

    .line 116
    const/4 v4, 0x0

    .line 117
    if-eqz v3, :cond_3

    .line 118
    .line 119
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object v3

    .line 123
    move-object v5, v3

    .line 124
    check-cast v5, Lf50/a;

    .line 125
    .line 126
    invoke-virtual {v5}, Lf50/a;->c()Ljava/lang/String;

    .line 127
    .line 128
    .line 129
    move-result-object v5

    .line 130
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 131
    .line 132
    invoke-virtual {v6}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object v6

    .line 136
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    move-result v5

    .line 140
    if-eqz v5, :cond_2

    .line 141
    .line 142
    goto :goto_1

    .line 143
    :cond_3
    move-object v3, v4

    .line 144
    :goto_1
    check-cast v3, Lf50/a;

    .line 145
    .line 146
    if-eqz v3, :cond_4

    .line 147
    .line 148
    invoke-virtual {v3}, Lf50/a;->e()Ljava/util/List;

    .line 149
    .line 150
    .line 151
    move-result-object v4

    .line 152
    :cond_4
    if-eqz v4, :cond_6

    .line 153
    .line 154
    invoke-interface {v4}, Ljava/util/Collection;->size()I

    .line 155
    .line 156
    .line 157
    move-result v1

    .line 158
    const/4 v3, 0x1

    .line 159
    if-ne v1, v3, :cond_6

    .line 160
    .line 161
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 162
    .line 163
    .line 164
    move-result-object p2

    .line 165
    new-instance v1, Ljava/util/ArrayList;

    .line 166
    .line 167
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 168
    .line 169
    .line 170
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 171
    .line 172
    .line 173
    move-result-object p2

    .line 174
    :cond_5
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 175
    .line 176
    .line 177
    move-result v3

    .line 178
    if-eqz v3, :cond_c

    .line 179
    .line 180
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object v3

    .line 184
    move-object v4, v3

    .line 185
    check-cast v4, Lf50/a;

    .line 186
    .line 187
    invoke-virtual {v4}, Lf50/a;->c()Ljava/lang/String;

    .line 188
    .line 189
    .line 190
    move-result-object v4

    .line 191
    sget-object v5, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 192
    .line 193
    invoke-virtual {v5}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 194
    .line 195
    .line 196
    move-result-object v5

    .line 197
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 198
    .line 199
    .line 200
    move-result v4

    .line 201
    if-nez v4, :cond_5

    .line 202
    .line 203
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 204
    .line 205
    .line 206
    goto :goto_2

    .line 207
    :cond_6
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 208
    .line 209
    .line 210
    move-result-object v1

    .line 211
    new-instance v3, Ljava/util/ArrayList;

    .line 212
    .line 213
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 214
    .line 215
    .line 216
    move-result v4

    .line 217
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 218
    .line 219
    .line 220
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 221
    .line 222
    .line 223
    move-result-object v1

    .line 224
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 225
    .line 226
    .line 227
    move-result v4

    .line 228
    if-eqz v4, :cond_a

    .line 229
    .line 230
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 231
    .line 232
    .line 233
    move-result-object v4

    .line 234
    move-object v5, v4

    .line 235
    check-cast v5, Lf50/a;

    .line 236
    .line 237
    invoke-virtual {v5}, Lf50/a;->c()Ljava/lang/String;

    .line 238
    .line 239
    .line 240
    move-result-object v4

    .line 241
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 242
    .line 243
    invoke-virtual {v6}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 244
    .line 245
    .line 246
    move-result-object v6

    .line 247
    invoke-static {v4, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 248
    .line 249
    .line 250
    move-result v4

    .line 251
    if-eqz v4, :cond_9

    .line 252
    .line 253
    invoke-virtual {v5}, Lf50/a;->e()Ljava/util/List;

    .line 254
    .line 255
    .line 256
    move-result-object v4

    .line 257
    new-instance v6, Ljava/util/ArrayList;

    .line 258
    .line 259
    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 260
    .line 261
    .line 262
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 263
    .line 264
    .line 265
    move-result-object v4

    .line 266
    :cond_7
    :goto_4
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 267
    .line 268
    .line 269
    move-result v7

    .line 270
    if-eqz v7, :cond_8

    .line 271
    .line 272
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v7

    .line 276
    move-object v8, v7

    .line 277
    check-cast v8, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 278
    .line 279
    invoke-virtual {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 280
    .line 281
    .line 282
    move-result-object v8

    .line 283
    invoke-static {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 284
    .line 285
    .line 286
    move-result-wide v8

    .line 287
    invoke-virtual {p2}, Lk50/a$a;->d()Ls30/a;

    .line 288
    .line 289
    .line 290
    move-result-object v10

    .line 291
    invoke-virtual {v10}, Ls30/a;->b()Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 292
    .line 293
    .line 294
    move-result-object v10

    .line 295
    invoke-virtual {v10}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 296
    .line 297
    .line 298
    move-result-object v10

    .line 299
    invoke-static {v10}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 300
    .line 301
    .line 302
    move-result-wide v10

    .line 303
    cmp-long v12, v8, v10

    .line 304
    .line 305
    if-eqz v12, :cond_7

    .line 306
    .line 307
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 308
    .line 309
    .line 310
    goto :goto_4

    .line 311
    :cond_8
    const/4 v9, 0x6

    .line 312
    const/4 v10, 0x0

    .line 313
    const/4 v7, 0x0

    .line 314
    const/4 v8, 0x0

    .line 315
    invoke-static/range {v5 .. v10}, Lf50/a;->b(Lf50/a;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lf50/a;

    .line 316
    .line 317
    .line 318
    move-result-object v5

    .line 319
    :cond_9
    invoke-interface {v3, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 320
    .line 321
    .line 322
    goto :goto_3

    .line 323
    :cond_a
    move-object v1, v3

    .line 324
    goto :goto_5

    .line 325
    :cond_b
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 326
    .line 327
    .line 328
    move-result-object v1

    .line 329
    :cond_c
    :goto_5
    invoke-virtual {p1, v1}, Lk50/h$a;->a(Ljava/util/List;)Lk50/h$a;

    .line 330
    .line 331
    .line 332
    move-result-object p1

    .line 333
    :cond_d
    invoke-virtual {p1}, Lk50/h$a;->b()Ljava/util/List;

    .line 334
    .line 335
    .line 336
    move-result-object p1

    .line 337
    new-instance p2, Ljava/util/ArrayList;

    .line 338
    .line 339
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 340
    .line 341
    .line 342
    move-result v1

    .line 343
    invoke-direct {p2, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 344
    .line 345
    .line 346
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 347
    .line 348
    .line 349
    move-result-object p1

    .line 350
    :goto_6
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 351
    .line 352
    .line 353
    move-result v1

    .line 354
    if-eqz v1, :cond_e

    .line 355
    .line 356
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 357
    .line 358
    .line 359
    move-result-object v1

    .line 360
    check-cast v1, Lf50/a;

    .line 361
    .line 362
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->d:LHX0/e;

    .line 363
    .line 364
    sget-object v3, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareL:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 365
    .line 366
    invoke-virtual {v3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getConfigType()Ljava/lang/String;

    .line 367
    .line 368
    .line 369
    move-result-object v3

    .line 370
    invoke-static {v1, v2, v3}, Lj50/c;->a(Lf50/a;LHX0/e;Ljava/lang/String;)Lk50/g;

    .line 371
    .line 372
    .line 373
    move-result-object v1

    .line 374
    invoke-interface {p2, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 375
    .line 376
    .line 377
    goto :goto_6

    .line 378
    :cond_e
    invoke-interface {v0, p2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 379
    .line 380
    .line 381
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 382
    .line 383
    .line 384
    move-result-object p1

    .line 385
    return-object p1
.end method

.method public final o(Ljava/util/List;Lk50/h$a;Lk50/c;Lk50/d;Lk50/a;)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "Lk50/h$a;",
            "Lk50/c;",
            "Lk50/d;",
            "Lk50/a;",
            ")V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v2, p3

    .line 6
    .line 7
    move-object/from16 v3, p4

    .line 8
    .line 9
    move-object/from16 v4, p2

    .line 10
    .line 11
    move-object/from16 v5, p5

    .line 12
    .line 13
    invoke-virtual {v0, v4, v5}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->n(Lk50/h$a;Lk50/a;)Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v5

    .line 17
    sget-object v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->NEW:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 18
    .line 19
    const/4 v7, 0x5

    .line 20
    new-array v7, v7, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 21
    .line 22
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 23
    .line 24
    const/4 v9, 0x0

    .line 25
    aput-object v8, v7, v9

    .line 26
    .line 27
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->FOR_YOU:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 28
    .line 29
    const/4 v10, 0x1

    .line 30
    aput-object v8, v7, v10

    .line 31
    .line 32
    sget-object v8, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->BEST:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 33
    .line 34
    const/4 v11, 0x2

    .line 35
    aput-object v8, v7, v11

    .line 36
    .line 37
    const/4 v8, 0x3

    .line 38
    aput-object v6, v7, v8

    .line 39
    .line 40
    sget-object v12, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->SLOTS:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 41
    .line 42
    const/4 v13, 0x4

    .line 43
    aput-object v12, v7, v13

    .line 44
    .line 45
    invoke-static {v7}, Lkotlin/collections/v;->t([Ljava/lang/Object;)Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v7

    .line 49
    invoke-virtual {v4}, Lk50/h$a;->b()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    invoke-direct {v0, v4}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->p(Ljava/util/List;)Z

    .line 54
    .line 55
    .line 56
    move-result v4

    .line 57
    if-nez v4, :cond_0

    .line 58
    .line 59
    invoke-interface {v7, v6}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 60
    .line 61
    .line 62
    move-result v4

    .line 63
    const/4 v6, -0x1

    .line 64
    if-eq v4, v6, :cond_0

    .line 65
    .line 66
    invoke-interface {v7, v4}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    :cond_0
    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 70
    .line 71
    .line 72
    move-result-object v4

    .line 73
    :goto_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 74
    .line 75
    .line 76
    move-result v6

    .line 77
    if-eqz v6, :cond_1

    .line 78
    .line 79
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v6

    .line 83
    check-cast v6, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 84
    .line 85
    invoke-direct {v0, v1, v5, v6}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_1
    instance-of v4, v3, Lk50/d$a;

    .line 90
    .line 91
    if-eqz v4, :cond_2

    .line 92
    .line 93
    check-cast v3, Lk50/d$a;

    .line 94
    .line 95
    invoke-virtual {v3}, Lk50/d$a;->a()Lk50/e;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 100
    .line 101
    .line 102
    :cond_2
    new-array v3, v11, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 103
    .line 104
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->STAIRS:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 105
    .line 106
    aput-object v4, v3, v9

    .line 107
    .line 108
    sget-object v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->DICES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 109
    .line 110
    aput-object v4, v3, v10

    .line 111
    .line 112
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 113
    .line 114
    .line 115
    move-result-object v3

    .line 116
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 117
    .line 118
    .line 119
    move-result-object v3

    .line 120
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 121
    .line 122
    .line 123
    move-result v4

    .line 124
    if-eqz v4, :cond_3

    .line 125
    .line 126
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v4

    .line 130
    check-cast v4, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 131
    .line 132
    invoke-direct {v0, v1, v5, v4}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 133
    .line 134
    .line 135
    goto :goto_1

    .line 136
    :cond_3
    instance-of v3, v2, Lk50/c$a;

    .line 137
    .line 138
    if-eqz v3, :cond_4

    .line 139
    .line 140
    new-instance v12, Lk50/e;

    .line 141
    .line 142
    sget-object v3, Ll8/j;->a:Ll8/j;

    .line 143
    .line 144
    check-cast v2, Lk50/c$a;

    .line 145
    .line 146
    invoke-virtual {v2}, Lk50/c$a;->d()LP40/b;

    .line 147
    .line 148
    .line 149
    move-result-object v4

    .line 150
    invoke-virtual {v4}, LP40/b;->b()LP40/c;

    .line 151
    .line 152
    .line 153
    move-result-object v4

    .line 154
    invoke-virtual {v4}, LP40/c;->c()Ljava/lang/String;

    .line 155
    .line 156
    .line 157
    move-result-object v4

    .line 158
    invoke-static {v4}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 159
    .line 160
    .line 161
    move-result-wide v6

    .line 162
    invoke-virtual {v2}, Lk50/c$a;->d()LP40/b;

    .line 163
    .line 164
    .line 165
    move-result-object v2

    .line 166
    invoke-virtual {v2}, LP40/b;->a()Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object v2

    .line 170
    sget-object v4, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 171
    .line 172
    invoke-virtual {v3, v6, v7, v2, v4}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v14

    .line 176
    iget-object v2, v0, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->d:LHX0/e;

    .line 177
    .line 178
    sget v3, Lpb/k;->promo_jackpot:I

    .line 179
    .line 180
    new-array v4, v9, [Ljava/lang/Object;

    .line 181
    .line 182
    invoke-interface {v2, v3, v4}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 183
    .line 184
    .line 185
    move-result-object v15

    .line 186
    sget v16, Lc50/a;->jackpot_popular_banner:I

    .line 187
    .line 188
    const/16 v17, 0x0

    .line 189
    .line 190
    const/4 v13, 0x2

    .line 191
    invoke-direct/range {v12 .. v17}, Lk50/e;-><init>(ILjava/lang/String;Ljava/lang/String;IZ)V

    .line 192
    .line 193
    .line 194
    invoke-interface {v1, v12}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 195
    .line 196
    .line 197
    goto :goto_2

    .line 198
    :cond_4
    instance-of v3, v2, Lk50/c$b;

    .line 199
    .line 200
    if-nez v3, :cond_6

    .line 201
    .line 202
    sget-object v3, Lk50/c$c;->a:Lk50/c$c;

    .line 203
    .line 204
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 205
    .line 206
    .line 207
    move-result v2

    .line 208
    if-eqz v2, :cond_5

    .line 209
    .line 210
    goto :goto_2

    .line 211
    :cond_5
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 212
    .line 213
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 214
    .line 215
    .line 216
    throw v1

    .line 217
    :cond_6
    :goto_2
    new-array v2, v8, [Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 218
    .line 219
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CARD_GAMES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 220
    .line 221
    aput-object v3, v2, v9

    .line 222
    .line 223
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->LOTTERIES:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 224
    .line 225
    aput-object v3, v2, v10

    .line 226
    .line 227
    sget-object v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->OTHER:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 228
    .line 229
    aput-object v3, v2, v11

    .line 230
    .line 231
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 232
    .line 233
    .line 234
    move-result-object v2

    .line 235
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 236
    .line 237
    .line 238
    move-result-object v2

    .line 239
    :goto_3
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 240
    .line 241
    .line 242
    move-result v3

    .line 243
    if-eqz v3, :cond_7

    .line 244
    .line 245
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 246
    .line 247
    .line 248
    move-result-object v3

    .line 249
    check-cast v3, Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 250
    .line 251
    invoke-direct {v0, v1, v5, v3}, Lorg/xbet/games_section/feature/popular_classic/presentation/a;->k(Ljava/util/List;Ljava/util/List;Lorg/xbet/core/domain/GamesCategoryTypeEnum;)V

    .line 252
    .line 253
    .line 254
    goto :goto_3

    .line 255
    :cond_7
    return-void
.end method
