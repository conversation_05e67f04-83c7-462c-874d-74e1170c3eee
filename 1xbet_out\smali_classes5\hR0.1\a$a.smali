.class public final LhR0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LhR0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LhR0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LhR0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LhR0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a()LhR0/c;
    .locals 2

    .line 1
    new-instance v0, LhR0/a$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LhR0/a$b;-><init>(LhR0/b;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method
