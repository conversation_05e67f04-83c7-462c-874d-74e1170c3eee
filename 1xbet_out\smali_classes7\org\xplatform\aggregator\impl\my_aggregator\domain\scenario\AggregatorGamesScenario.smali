.class public final Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ$\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\n2\u000c\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\nH\u0086B\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0010R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0015"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
        "",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;",
        "aggregatorGamesUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "remoteConfigUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)V",
        "",
        "",
        "filtersList",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;",
        "b",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "c",
        "Li8/j;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->c:Li8/j;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x0

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_4

    .line 37
    .line 38
    if-eq v2, v5, :cond_3

    .line 39
    .line 40
    if-ne v2, v3, :cond_2

    .line 41
    .line 42
    iget p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$1:I

    .line 43
    .line 44
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$0:I

    .line 45
    .line 46
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v6, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;

    .line 49
    .line 50
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v7, Ljava/util/List;

    .line 53
    .line 54
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    :cond_1
    move p2, p1

    .line 58
    move-object p1, v7

    .line 59
    goto :goto_1

    .line 60
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 61
    .line 62
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 63
    .line 64
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    throw p1

    .line 68
    :cond_3
    iget p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$1:I

    .line 69
    .line 70
    iget v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$0:I

    .line 71
    .line 72
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 73
    .line 74
    check-cast v6, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;

    .line 75
    .line 76
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 77
    .line 78
    check-cast v7, Ljava/util/List;

    .line 79
    .line 80
    :try_start_0
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 81
    .line 82
    .line 83
    goto :goto_2

    .line 84
    :catchall_0
    move-exception p2

    .line 85
    goto :goto_3

    .line 86
    :cond_4
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    const/4 p2, 0x0

    .line 90
    const/4 v2, 0x0

    .line 91
    move-object v6, p0

    .line 92
    :goto_1
    :try_start_1
    sget-object v7, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 93
    .line 94
    iget-object v7, v6, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->a:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;

    .line 95
    .line 96
    iget-object v8, v6, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 97
    .line 98
    invoke-interface {v8}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 99
    .line 100
    .line 101
    move-result-object v8

    .line 102
    invoke-virtual {v8}, Lek0/o;->o()Lek0/a;

    .line 103
    .line 104
    .line 105
    move-result-object v8

    .line 106
    invoke-virtual {v8}, Lek0/a;->c()Z

    .line 107
    .line 108
    .line 109
    move-result v8

    .line 110
    iget-object v9, v6, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;->c:Li8/j;

    .line 111
    .line 112
    invoke-interface {v9}, Li8/j;->invoke()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v9

    .line 116
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 117
    .line 118
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 119
    .line 120
    iput v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$0:I

    .line 121
    .line 122
    iput p2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$1:I

    .line 123
    .line 124
    iput v5, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->label:I

    .line 125
    .line 126
    invoke-virtual {v7, p1, v8, v9, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/a;->a(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v7
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 130
    if-ne v7, v1, :cond_5

    .line 131
    .line 132
    goto/16 :goto_7

    .line 133
    .line 134
    :cond_5
    move-object v10, v7

    .line 135
    move-object v7, p1

    .line 136
    move p1, p2

    .line 137
    move-object p2, v10

    .line 138
    :goto_2
    :try_start_2
    check-cast p2, Ljava/util/List;

    .line 139
    .line 140
    invoke-static {p2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 144
    goto/16 :goto_9

    .line 145
    .line 146
    :catchall_1
    move-exception v7

    .line 147
    move-object v10, v7

    .line 148
    move-object v7, p1

    .line 149
    move p1, p2

    .line 150
    move-object p2, v10

    .line 151
    :goto_3
    if-eqz v2, :cond_6

    .line 152
    .line 153
    instance-of v8, p2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 154
    .line 155
    if-eqz v8, :cond_6

    .line 156
    .line 157
    move-object v8, p2

    .line 158
    check-cast v8, Lcom/xbet/onexcore/data/model/ServerException;

    .line 159
    .line 160
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 161
    .line 162
    .line 163
    move-result v8

    .line 164
    if-eqz v8, :cond_6

    .line 165
    .line 166
    const/4 v8, 0x1

    .line 167
    goto :goto_4

    .line 168
    :cond_6
    const/4 v8, 0x0

    .line 169
    :goto_4
    instance-of v9, p2, Ljava/util/concurrent/CancellationException;

    .line 170
    .line 171
    if-nez v9, :cond_e

    .line 172
    .line 173
    instance-of v9, p2, Ljava/net/ConnectException;

    .line 174
    .line 175
    if-nez v9, :cond_e

    .line 176
    .line 177
    if-nez v8, :cond_e

    .line 178
    .line 179
    instance-of v8, p2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 180
    .line 181
    if-eqz v8, :cond_9

    .line 182
    .line 183
    move-object v8, p2

    .line 184
    check-cast v8, Lcom/xbet/onexcore/data/model/ServerException;

    .line 185
    .line 186
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 187
    .line 188
    .line 189
    move-result v9

    .line 190
    if-nez v9, :cond_8

    .line 191
    .line 192
    invoke-virtual {v8}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 193
    .line 194
    .line 195
    move-result v8

    .line 196
    if-eqz v8, :cond_7

    .line 197
    .line 198
    goto :goto_5

    .line 199
    :cond_7
    const/4 v8, 0x0

    .line 200
    goto :goto_6

    .line 201
    :cond_8
    :goto_5
    const/4 v8, 0x1

    .line 202
    goto :goto_6

    .line 203
    :cond_9
    invoke-static {p2}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 204
    .line 205
    .line 206
    move-result v8

    .line 207
    if-nez v8, :cond_7

    .line 208
    .line 209
    goto :goto_5

    .line 210
    :goto_6
    add-int/2addr p1, v5

    .line 211
    const/4 v9, 0x3

    .line 212
    if-gt p1, v9, :cond_b

    .line 213
    .line 214
    if-eqz v8, :cond_a

    .line 215
    .line 216
    goto :goto_8

    .line 217
    :cond_a
    new-instance v8, Ljava/lang/StringBuilder;

    .line 218
    .line 219
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 220
    .line 221
    .line 222
    const-string v9, "error ("

    .line 223
    .line 224
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 225
    .line 226
    .line 227
    invoke-virtual {v8, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 228
    .line 229
    .line 230
    const-string v9, "): "

    .line 231
    .line 232
    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 233
    .line 234
    .line 235
    invoke-virtual {v8, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 236
    .line 237
    .line 238
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 239
    .line 240
    .line 241
    move-result-object p2

    .line 242
    sget-object v8, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 243
    .line 244
    invoke-virtual {v8, p2}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 245
    .line 246
    .line 247
    iput-object v7, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 248
    .line 249
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 250
    .line 251
    iput v2, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$0:I

    .line 252
    .line 253
    iput p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->I$1:I

    .line 254
    .line 255
    iput v3, v0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario$invoke$1;->label:I

    .line 256
    .line 257
    const-wide/16 v8, 0xbb8

    .line 258
    .line 259
    invoke-static {v8, v9, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 260
    .line 261
    .line 262
    move-result-object p2

    .line 263
    if-ne p2, v1, :cond_1

    .line 264
    .line 265
    :goto_7
    return-object v1

    .line 266
    :cond_b
    :goto_8
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 267
    .line 268
    invoke-static {p2}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 269
    .line 270
    .line 271
    move-result-object p1

    .line 272
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object p1

    .line 276
    :goto_9
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 277
    .line 278
    .line 279
    move-result p2

    .line 280
    if-eqz p2, :cond_c

    .line 281
    .line 282
    const/4 p1, 0x0

    .line 283
    :cond_c
    check-cast p1, Ljava/util/List;

    .line 284
    .line 285
    if-nez p1, :cond_d

    .line 286
    .line 287
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 288
    .line 289
    .line 290
    move-result-object p1

    .line 291
    :cond_d
    return-object p1

    .line 292
    :cond_e
    throw p2
.end method
