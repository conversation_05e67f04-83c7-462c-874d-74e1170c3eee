.class public final LoN0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u0004*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LpN0/a;",
        "",
        "LND0/k;",
        "teams",
        "LrN0/d;",
        "a",
        "(LpN0/a;Ljava/util/List;)LrN0/d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LpN0/a;Ljava/util/List;)LrN0/d;
    .locals 14
    .param p0    # LpN0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LpN0/a;",
            "Ljava/util/List<",
            "LND0/k;",
            ">;)",
            "LrN0/d;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LpN0/a;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, ""

    .line 6
    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    move-object v3, v1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object v3, v0

    .line 12
    :goto_0
    invoke-virtual {p0}, LpN0/a;->b()Ljava/lang/Long;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    const-wide/16 v4, 0x0

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 21
    .line 22
    .line 23
    move-result-wide v6

    .line 24
    goto :goto_1

    .line 25
    :cond_1
    move-wide v6, v4

    .line 26
    :goto_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    const/4 v8, 0x0

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    move-object v9, v2

    .line 42
    check-cast v9, LND0/k;

    .line 43
    .line 44
    invoke-virtual {v9}, LND0/k;->c()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v9

    .line 48
    invoke-virtual {p0}, LpN0/a;->g()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v10

    .line 52
    invoke-static {v9, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v9

    .line 56
    if-eqz v9, :cond_2

    .line 57
    .line 58
    goto :goto_2

    .line 59
    :cond_3
    move-object v2, v8

    .line 60
    :goto_2
    check-cast v2, LND0/k;

    .line 61
    .line 62
    if-eqz v2, :cond_e

    .line 63
    .line 64
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    :cond_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 69
    .line 70
    .line 71
    move-result v0

    .line 72
    if-eqz v0, :cond_5

    .line 73
    .line 74
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    move-object v9, v0

    .line 79
    check-cast v9, LND0/k;

    .line 80
    .line 81
    invoke-virtual {v9}, LND0/k;->c()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v9

    .line 85
    invoke-virtual {p0}, LpN0/a;->h()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v10

    .line 89
    invoke-static {v9, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    move-result v9

    .line 93
    if-eqz v9, :cond_4

    .line 94
    .line 95
    goto :goto_3

    .line 96
    :cond_5
    move-object v0, v8

    .line 97
    :goto_3
    check-cast v0, LND0/k;

    .line 98
    .line 99
    if-eqz v0, :cond_d

    .line 100
    .line 101
    sget-object p1, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 102
    .line 103
    invoke-virtual {p0}, LpN0/a;->f()Ljava/lang/Integer;

    .line 104
    .line 105
    .line 106
    move-result-object v9

    .line 107
    const/4 v10, 0x0

    .line 108
    if-eqz v9, :cond_6

    .line 109
    .line 110
    invoke-virtual {v9}, Ljava/lang/Integer;->intValue()I

    .line 111
    .line 112
    .line 113
    move-result v9

    .line 114
    goto :goto_4

    .line 115
    :cond_6
    const/4 v9, 0x0

    .line 116
    :goto_4
    invoke-virtual {p1, v9}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    invoke-virtual {p0}, LpN0/a;->d()Ljava/lang/Integer;

    .line 121
    .line 122
    .line 123
    move-result-object v9

    .line 124
    if-eqz v9, :cond_7

    .line 125
    .line 126
    invoke-virtual {v9}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 127
    .line 128
    .line 129
    move-result-object v9

    .line 130
    goto :goto_5

    .line 131
    :cond_7
    move-object v9, v8

    .line 132
    :goto_5
    if-nez v9, :cond_8

    .line 133
    .line 134
    move-object v9, v1

    .line 135
    :cond_8
    invoke-virtual {p0}, LpN0/a;->e()Ljava/lang/Integer;

    .line 136
    .line 137
    .line 138
    move-result-object v11

    .line 139
    if-eqz v11, :cond_9

    .line 140
    .line 141
    invoke-virtual {v11}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v8

    .line 145
    :cond_9
    if-nez v8, :cond_a

    .line 146
    .line 147
    goto :goto_6

    .line 148
    :cond_a
    move-object v1, v8

    .line 149
    :goto_6
    sget-object v8, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/ResultsGridWinnerType;->Companion:Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/ResultsGridWinnerType$a;

    .line 150
    .line 151
    invoke-virtual {p0}, LpN0/a;->i()Ljava/lang/Integer;

    .line 152
    .line 153
    .line 154
    move-result-object v11

    .line 155
    if-eqz v11, :cond_b

    .line 156
    .line 157
    invoke-virtual {v11}, Ljava/lang/Integer;->intValue()I

    .line 158
    .line 159
    .line 160
    move-result v10

    .line 161
    :cond_b
    invoke-virtual {v8, v10}, Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/ResultsGridWinnerType$a;->a(I)Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/ResultsGridWinnerType;

    .line 162
    .line 163
    .line 164
    move-result-object v11

    .line 165
    invoke-virtual {p0}, LpN0/a;->a()Ljava/lang/Long;

    .line 166
    .line 167
    .line 168
    move-result-object p0

    .line 169
    if-eqz p0, :cond_c

    .line 170
    .line 171
    invoke-virtual {p0}, Ljava/lang/Long;->longValue()J

    .line 172
    .line 173
    .line 174
    move-result-wide v4

    .line 175
    :cond_c
    move-wide v12, v4

    .line 176
    move-object v4, v2

    .line 177
    new-instance v2, LrN0/d;

    .line 178
    .line 179
    move-object v8, p1

    .line 180
    move-object v5, v0

    .line 181
    move-object v10, v1

    .line 182
    invoke-direct/range {v2 .. v13}, LrN0/d;-><init>(Ljava/lang/String;LND0/k;LND0/k;JLorg/xbet/statistic/domain/model/shortgame/EventStatusType;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/statistic/stat_results/impl/results_grid/domain/model/ResultsGridWinnerType;J)V

    .line 183
    .line 184
    .line 185
    return-object v2

    .line 186
    :cond_d
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 187
    .line 188
    invoke-direct {p0, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;)V

    .line 189
    .line 190
    .line 191
    throw p0

    .line 192
    :cond_e
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 193
    .line 194
    invoke-direct {p0, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;)V

    .line 195
    .line 196
    .line 197
    throw p0
.end method
