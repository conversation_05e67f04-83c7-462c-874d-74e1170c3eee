.class public final LHN0/d$d;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LHN0/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\t\u0008\u0002\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001d\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\r\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0012R \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0015"
    }
    d2 = {
        "LHN0/d$d;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LDN0/D;",
        "binding",
        "Lkotlin/Function1;",
        "",
        "",
        "onGameClick",
        "<init>",
        "(LDN0/D;Lkotlin/jvm/functions/Function1;)V",
        "LGN0/f;",
        "item",
        "",
        "position",
        "e",
        "(LGN0/f;I)V",
        "g",
        "()V",
        "LDN0/D;",
        "f",
        "Lkotlin/jvm/functions/Function1;",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LDN0/D;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDN0/D;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # LDN0/D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LDN0/D;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LDN0/D;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LHN0/d$d;->e:LDN0/D;

    .line 9
    .line 10
    iput-object p2, p0, LHN0/d$d;->f:Lkotlin/jvm/functions/Function1;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic d(LHN0/d$d;LGN0/f;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LHN0/d$d;->f(LHN0/d$d;LGN0/f;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(LHN0/d$d;LGN0/f;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, LHN0/d$d;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    check-cast p1, LGN0/e;

    .line 4
    .line 5
    invoke-virtual {p1}, LGN0/e;->c()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method


# virtual methods
.method public final e(LGN0/f;I)V
    .locals 8
    .param p1    # LGN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LNN0/g;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    check-cast p1, LNN0/g;

    .line 6
    .line 7
    invoke-virtual {p1}, LNN0/g;->b()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-lez v0, :cond_0

    .line 16
    .line 17
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 18
    .line 19
    iget-object v0, p0, LHN0/d$d;->e:LDN0/D;

    .line 20
    .line 21
    iget-object v2, v0, LDN0/D;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 22
    .line 23
    sget-object v3, Lorg/xbet/ui_common/utils/image/ImageCropType;->CIRCLE_IMAGE:Lorg/xbet/ui_common/utils/image/ImageCropType;

    .line 24
    .line 25
    invoke-virtual {p1}, LNN0/g;->b()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    sget v6, Lpb/g;->ic_profile:I

    .line 30
    .line 31
    const/4 v4, 0x1

    .line 32
    invoke-virtual/range {v1 .. v6}, LCX0/l;->E(Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;I)V

    .line 33
    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, LHN0/d$d;->e:LDN0/D;

    .line 36
    .line 37
    iget-object v0, v0, LDN0/D;->e:Landroid/widget/TextView;

    .line 38
    .line 39
    invoke-virtual {p1}, LNN0/g;->c()Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, LHN0/d$d;->e:LDN0/D;

    .line 47
    .line 48
    iget-object p1, p1, LDN0/D;->d:Landroid/widget/TextView;

    .line 49
    .line 50
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 55
    .line 56
    .line 57
    return-void

    .line 58
    :cond_1
    instance-of v0, p1, LGN0/e;

    .line 59
    .line 60
    if-eqz v0, :cond_4

    .line 61
    .line 62
    move-object v0, p1

    .line 63
    check-cast v0, LGN0/e;

    .line 64
    .line 65
    invoke-virtual {v0}, LGN0/e;->b()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    if-lez v1, :cond_2

    .line 74
    .line 75
    sget-object v2, LCX0/l;->a:LCX0/l;

    .line 76
    .line 77
    iget-object v1, p0, LHN0/d$d;->e:LDN0/D;

    .line 78
    .line 79
    iget-object v3, v1, LDN0/D;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 80
    .line 81
    sget-object v4, Lorg/xbet/ui_common/utils/image/ImageCropType;->CIRCLE_IMAGE:Lorg/xbet/ui_common/utils/image/ImageCropType;

    .line 82
    .line 83
    invoke-virtual {v0}, LGN0/e;->b()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v6

    .line 87
    sget v7, Lpb/g;->ic_profile:I

    .line 88
    .line 89
    const/4 v5, 0x1

    .line 90
    invoke-virtual/range {v2 .. v7}, LCX0/l;->E(Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;I)V

    .line 91
    .line 92
    .line 93
    :cond_2
    iget-object v1, p0, LHN0/d$d;->e:LDN0/D;

    .line 94
    .line 95
    iget-object v1, v1, LDN0/D;->e:Landroid/widget/TextView;

    .line 96
    .line 97
    invoke-virtual {v0}, LGN0/e;->d()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 102
    .line 103
    .line 104
    iget-object v1, p0, LHN0/d$d;->e:LDN0/D;

    .line 105
    .line 106
    iget-object v1, v1, LDN0/D;->d:Landroid/widget/TextView;

    .line 107
    .line 108
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object p2

    .line 112
    invoke-virtual {v1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 113
    .line 114
    .line 115
    iget-object p2, p0, LHN0/d$d;->e:LDN0/D;

    .line 116
    .line 117
    iget-object p2, p2, LDN0/D;->b:Landroid/widget/ImageView;

    .line 118
    .line 119
    invoke-virtual {v0}, LGN0/e;->e()Z

    .line 120
    .line 121
    .line 122
    move-result v0

    .line 123
    if-eqz v0, :cond_3

    .line 124
    .line 125
    const/4 v0, 0x0

    .line 126
    goto :goto_0

    .line 127
    :cond_3
    const/16 v0, 0x8

    .line 128
    .line 129
    :goto_0
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 130
    .line 131
    .line 132
    iget-object p2, p0, LHN0/d$d;->e:LDN0/D;

    .line 133
    .line 134
    invoke-virtual {p2}, LDN0/D;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 135
    .line 136
    .line 137
    move-result-object p2

    .line 138
    new-instance v0, LHN0/e;

    .line 139
    .line 140
    invoke-direct {v0, p0, p1}, LHN0/e;-><init>(LHN0/d$d;LGN0/f;)V

    .line 141
    .line 142
    .line 143
    const/4 p1, 0x1

    .line 144
    const/4 v1, 0x0

    .line 145
    invoke-static {p2, v1, v0, p1, v1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 146
    .line 147
    .line 148
    :cond_4
    return-void
.end method

.method public final g()V
    .locals 2

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    iget-object v1, p0, LHN0/d$d;->e:LDN0/D;

    .line 4
    .line 5
    iget-object v1, v1, LDN0/D;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, LCX0/l;->j(Landroid/widget/ImageView;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
