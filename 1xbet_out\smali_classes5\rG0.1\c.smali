.class public final LrG0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LsG0/a;",
        "LvG0/c;",
        "a",
        "(LsG0/a;)LvG0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LsG0/a;)LvG0/c;
    .locals 12
    .param p0    # LsG0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LvG0/c;

    .line 2
    .line 3
    invoke-virtual {p0}, LsG0/a;->a()Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    :goto_0
    invoke-virtual {p0}, LsG0/a;->c()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    const-string v4, ""

    .line 21
    .line 22
    if-nez v3, :cond_1

    .line 23
    .line 24
    move-object v3, v4

    .line 25
    :cond_1
    invoke-virtual {p0}, LsG0/a;->d()Ljava/lang/Integer;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    if-eqz v5, :cond_2

    .line 30
    .line 31
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 32
    .line 33
    .line 34
    move-result v5

    .line 35
    goto :goto_1

    .line 36
    :cond_2
    const/4 v5, 0x0

    .line 37
    :goto_1
    invoke-virtual {p0}, LsG0/a;->e()Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object v6

    .line 41
    if-eqz v6, :cond_3

    .line 42
    .line 43
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 44
    .line 45
    .line 46
    move-result v6

    .line 47
    goto :goto_2

    .line 48
    :cond_3
    const/4 v6, 0x0

    .line 49
    :goto_2
    invoke-virtual {p0}, LsG0/a;->g()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v7

    .line 53
    if-nez v7, :cond_4

    .line 54
    .line 55
    move-object v7, v4

    .line 56
    :cond_4
    invoke-virtual {p0}, LsG0/a;->h()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v8

    .line 60
    if-nez v8, :cond_5

    .line 61
    .line 62
    goto :goto_3

    .line 63
    :cond_5
    move-object v4, v8

    .line 64
    :goto_3
    invoke-virtual {p0}, LsG0/a;->i()Ljava/lang/Integer;

    .line 65
    .line 66
    .line 67
    move-result-object v8

    .line 68
    if-eqz v8, :cond_6

    .line 69
    .line 70
    invoke-virtual {v8}, Ljava/lang/Integer;->intValue()I

    .line 71
    .line 72
    .line 73
    move-result v8

    .line 74
    goto :goto_4

    .line 75
    :cond_6
    const/4 v8, -0x1

    .line 76
    :goto_4
    invoke-virtual {p0}, LsG0/a;->b()Ljava/lang/Long;

    .line 77
    .line 78
    .line 79
    move-result-object v9

    .line 80
    if-eqz v9, :cond_7

    .line 81
    .line 82
    invoke-virtual {v9}, Ljava/lang/Long;->longValue()J

    .line 83
    .line 84
    .line 85
    move-result-wide v9

    .line 86
    goto :goto_5

    .line 87
    :cond_7
    const-wide/16 v9, 0x0

    .line 88
    .line 89
    :goto_5
    sget-object v11, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 90
    .line 91
    invoke-virtual {p0}, LsG0/a;->f()Ljava/lang/Integer;

    .line 92
    .line 93
    .line 94
    move-result-object p0

    .line 95
    if-eqz p0, :cond_8

    .line 96
    .line 97
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 98
    .line 99
    .line 100
    move-result v2

    .line 101
    :cond_8
    invoke-virtual {v11, v2}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 102
    .line 103
    .line 104
    move-result-object p0

    .line 105
    move v2, v6

    .line 106
    move-object v6, v4

    .line 107
    move v4, v2

    .line 108
    move-object v2, v3

    .line 109
    move v3, v5

    .line 110
    move-object v5, v7

    .line 111
    move v7, v8

    .line 112
    move-wide v8, v9

    .line 113
    move-object v10, p0

    .line 114
    invoke-direct/range {v0 .. v10}, LvG0/c;-><init>(ILjava/lang/String;IILjava/lang/String;Ljava/lang/String;IJLorg/xbet/statistic/domain/model/shortgame/EventStatusType;)V

    .line 115
    .line 116
    .line 117
    return-object v0
.end method
