.class public final synthetic Lorg/xplatform/aggregator/impl/new_games/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/h;->a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/h;->b:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/h;->a:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/h;->b:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    check-cast p1, Landroidx/paging/f;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->q3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
