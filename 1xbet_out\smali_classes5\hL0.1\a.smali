.class public final LhL0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LiL0/b;",
        "LlL0/a;",
        "a",
        "(LiL0/b;)LlL0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LiL0/b;)LlL0/a;
    .locals 13
    .param p0    # LiL0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LlL0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LiL0/b;->d()Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const-string v2, "-"

    .line 8
    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    if-nez v1, :cond_1

    .line 16
    .line 17
    :cond_0
    move-object v1, v2

    .line 18
    :cond_1
    invoke-virtual {p0}, LiL0/b;->g()Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object v3

    .line 22
    if-eqz v3, :cond_2

    .line 23
    .line 24
    invoke-virtual {v3}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    if-nez v3, :cond_3

    .line 29
    .line 30
    :cond_2
    move-object v3, v2

    .line 31
    :cond_3
    invoke-virtual {p0}, LiL0/b;->c()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    if-eqz v4, :cond_5

    .line 36
    .line 37
    invoke-interface {v4}, Ljava/lang/CharSequence;->length()I

    .line 38
    .line 39
    .line 40
    move-result v4

    .line 41
    if-nez v4, :cond_4

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_4
    invoke-virtual {p0}, LiL0/b;->c()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    goto :goto_1

    .line 49
    :cond_5
    :goto_0
    move-object v4, v2

    .line 50
    :goto_1
    invoke-virtual {p0}, LiL0/b;->b()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v5

    .line 54
    if-eqz v5, :cond_6

    .line 55
    .line 56
    invoke-virtual {v5}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v5

    .line 60
    if-nez v5, :cond_7

    .line 61
    .line 62
    :cond_6
    move-object v5, v2

    .line 63
    :cond_7
    invoke-virtual {p0}, LiL0/b;->f()Ljava/lang/Integer;

    .line 64
    .line 65
    .line 66
    move-result-object v6

    .line 67
    if-eqz v6, :cond_8

    .line 68
    .line 69
    invoke-virtual {v6}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v6

    .line 73
    if-nez v6, :cond_9

    .line 74
    .line 75
    :cond_8
    move-object v6, v2

    .line 76
    :cond_9
    invoke-virtual {p0}, LiL0/b;->a()Ljava/lang/Integer;

    .line 77
    .line 78
    .line 79
    move-result-object v7

    .line 80
    if-eqz v7, :cond_a

    .line 81
    .line 82
    invoke-virtual {v7}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v7

    .line 86
    if-nez v7, :cond_b

    .line 87
    .line 88
    :cond_a
    move-object v7, v2

    .line 89
    :cond_b
    invoke-virtual {p0}, LiL0/b;->e()Ljava/lang/Integer;

    .line 90
    .line 91
    .line 92
    move-result-object v8

    .line 93
    if-eqz v8, :cond_d

    .line 94
    .line 95
    invoke-virtual {v8}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v8

    .line 99
    if-nez v8, :cond_c

    .line 100
    .line 101
    goto :goto_2

    .line 102
    :cond_c
    move-object v2, v8

    .line 103
    :cond_d
    :goto_2
    invoke-virtual {p0}, LiL0/b;->k()Ljava/lang/Boolean;

    .line 104
    .line 105
    .line 106
    move-result-object v8

    .line 107
    const/4 v9, 0x0

    .line 108
    if-eqz v8, :cond_e

    .line 109
    .line 110
    invoke-virtual {v8}, Ljava/lang/Boolean;->booleanValue()Z

    .line 111
    .line 112
    .line 113
    move-result v8

    .line 114
    goto :goto_3

    .line 115
    :cond_e
    const/4 v8, 0x0

    .line 116
    :goto_3
    invoke-virtual {p0}, LiL0/b;->i()Ljava/lang/Boolean;

    .line 117
    .line 118
    .line 119
    move-result-object v10

    .line 120
    if-eqz v10, :cond_f

    .line 121
    .line 122
    invoke-virtual {v10}, Ljava/lang/Boolean;->booleanValue()Z

    .line 123
    .line 124
    .line 125
    move-result v10

    .line 126
    goto :goto_4

    .line 127
    :cond_f
    const/4 v10, 0x0

    .line 128
    :goto_4
    invoke-virtual {p0}, LiL0/b;->j()Ljava/lang/Boolean;

    .line 129
    .line 130
    .line 131
    move-result-object v11

    .line 132
    if-eqz v11, :cond_10

    .line 133
    .line 134
    invoke-virtual {v11}, Ljava/lang/Boolean;->booleanValue()Z

    .line 135
    .line 136
    .line 137
    move-result v11

    .line 138
    goto :goto_5

    .line 139
    :cond_10
    const/4 v11, 0x0

    .line 140
    :goto_5
    invoke-virtual {p0}, LiL0/b;->h()Ljava/lang/Boolean;

    .line 141
    .line 142
    .line 143
    move-result-object p0

    .line 144
    if-eqz p0, :cond_11

    .line 145
    .line 146
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 147
    .line 148
    .line 149
    move-result v9

    .line 150
    move-object v12, v7

    .line 151
    move-object v7, v2

    .line 152
    move-object v2, v3

    .line 153
    move-object v3, v4

    .line 154
    move-object v4, v5

    .line 155
    move-object v5, v6

    .line 156
    move-object v6, v12

    .line 157
    move v12, v11

    .line 158
    move v11, v9

    .line 159
    move v9, v10

    .line 160
    move v10, v12

    .line 161
    goto :goto_6

    .line 162
    :cond_11
    move-object v9, v7

    .line 163
    move-object v7, v2

    .line 164
    move-object v2, v3

    .line 165
    move-object v3, v4

    .line 166
    move-object v4, v5

    .line 167
    move-object v5, v6

    .line 168
    move-object v6, v9

    .line 169
    move v9, v10

    .line 170
    move v10, v11

    .line 171
    const/4 v11, 0x0

    .line 172
    :goto_6
    invoke-direct/range {v0 .. v11}, LlL0/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZ)V

    .line 173
    .line 174
    .line 175
    return-object v0
.end method
