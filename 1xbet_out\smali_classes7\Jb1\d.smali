.class public final LJb1/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lzb1/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0000\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LJb1/d;",
        "Lzb1/b;",
        "<init>",
        "()V",
        "",
        "isVirtual",
        "Landroidx/fragment/app/Fragment;",
        "a",
        "(Z)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Z)Landroidx/fragment/app/Fragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->x1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$a;->b(Z)Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
