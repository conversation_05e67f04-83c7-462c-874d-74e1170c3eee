.class public final Lt71/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lt71/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u000e\u0018\u0000 \u000e2\u00020\u0001:\u0001\u0011B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u000f\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\r\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u000f\u0010\u000e\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u0008J\u000f\u0010\u000f\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0008J\u000f\u0010\u0010\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\u0008J\u000f\u0010\u0011\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0008J\u0019\u0010\u0013\u001a\u00020\u00062\u0008\u0010\u0012\u001a\u0004\u0018\u00010\tH\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0015\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "Lt71/a;",
        "",
        "Lorg/xbet/analytics/domain/b;",
        "analytics",
        "<init>",
        "(Lorg/xbet/analytics/domain/b;)V",
        "",
        "e",
        "()V",
        "",
        "errCode",
        "d",
        "(I)V",
        "c",
        "b",
        "h",
        "f",
        "a",
        "errorCode",
        "g",
        "(Ljava/lang/Integer;)V",
        "i",
        "Lorg/xbet/analytics/domain/b;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:Lt71/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xbet/analytics/domain/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lt71/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lt71/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lt71/a;->b:Lt71/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Lorg/xbet/analytics/domain/b;)V
    .locals 0
    .param p1    # Lorg/xbet/analytics/domain/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()V
    .locals 2

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "widget_line_call"

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public b()V
    .locals 2

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "widget_live_call"

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public c(I)V
    .locals 3

    .line 1
    const-string v0, "widget_line_error"

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 6
    .line 7
    invoke-interface {p1, v0}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    iget-object v1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 12
    .line 13
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const-string v2, "error_code"

    .line 18
    .line 19
    invoke-static {v2, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {p1}, Lkotlin/collections/P;->f(Lkotlin/Pair;)Ljava/util/Map;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-interface {v1, v0, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public d(I)V
    .locals 3

    .line 1
    const-string v0, "widget_live_error"

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 6
    .line 7
    invoke-interface {p1, v0}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    iget-object v1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 12
    .line 13
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const-string v2, "error_code"

    .line 18
    .line 19
    invoke-static {v2, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {p1}, Lkotlin/collections/P;->f(Lkotlin/Pair;)Ljava/util/Map;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-interface {v1, v0, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public e()V
    .locals 3

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "screen"

    .line 4
    .line 5
    const-string v2, "selected_anomin"

    .line 6
    .line 7
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-static {v1}, Lkotlin/collections/P;->f(Lkotlin/Pair;)Ljava/util/Map;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    const-string v2, "login_page_call"

    .line 16
    .line 17
    invoke-interface {v0, v2, v1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public f()V
    .locals 2

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "widget_favor_empty"

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public g(Ljava/lang/Integer;)V
    .locals 3

    .line 1
    const-string v0, "widget_favor_error"

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 6
    .line 7
    const-string v2, "error_code"

    .line 8
    .line 9
    invoke-static {v2, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-static {p1}, Lkotlin/collections/P;->f(Lkotlin/Pair;)Ljava/util/Map;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-interface {v1, v0, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    iget-object p1, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 22
    .line 23
    invoke-interface {p1, v0}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public h()V
    .locals 2

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "widget_favor_call"

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public i()V
    .locals 2

    .line 1
    iget-object v0, p0, Lt71/a;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    const-string v1, "widget_favor_anonim"

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lorg/xbet/analytics/domain/b;->a(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
