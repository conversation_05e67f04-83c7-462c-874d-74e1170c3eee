.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000f\u0008\u0000\u0018\u0000 %2\u00020\u0001:\u0001\u001eB1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ,\u0010\u0015\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00140\u00130\u00122\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0086\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J4\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00132\u000c\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u00132\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ+\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00132\u000c\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010 R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010!R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\"R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;",
        "",
        "Lu81/b;",
        "repository",
        "LfX/b;",
        "testRepository",
        "LHT/b;",
        "lastActionRepository",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "remoteConfigUseCase",
        "<init>",
        "(Lu81/b;LfX/b;LHT/b;Lm8/a;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "",
        "forCarousel",
        "",
        "endPoint",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "d",
        "(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;",
        "Lg9/e;",
        "actions",
        "b",
        "(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "games",
        "c",
        "(Ljava/util/List;Z)Ljava/util/List;",
        "a",
        "Lu81/b;",
        "LfX/b;",
        "LHT/b;",
        "Lm8/a;",
        "e",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "f",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LHT/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->f:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$a;

    return-void
.end method

.method public constructor <init>(Lu81/b;LfX/b;LHT/b;Lm8/a;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 0
    .param p1    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHT/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->a:Lu81/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->b:LfX/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->c:LHT/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->d:Lm8/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->e:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 13
    .line 14
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->b(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final b(Ljava/util/List;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lg9/e;",
            ">;Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v6, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->label:I

    .line 34
    .line 35
    const/4 v7, 0x2

    .line 36
    const/4 v2, 0x1

    .line 37
    const/16 v8, 0xa

    .line 38
    .line 39
    if-eqz v1, :cond_3

    .line 40
    .line 41
    if-eq v1, v2, :cond_2

    .line 42
    .line 43
    if-ne v1, v7, :cond_1

    .line 44
    .line 45
    iget-boolean p1, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->Z$0:Z

    .line 46
    .line 47
    iget-object p2, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$1:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast p2, Ljava/util/Set;

    .line 50
    .line 51
    iget-object p3, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$0:Ljava/lang/Object;

    .line 52
    .line 53
    check-cast p3, Ljava/util/List;

    .line 54
    .line 55
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    goto/16 :goto_7

    .line 59
    .line 60
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 61
    .line 62
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 63
    .line 64
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    throw p1

    .line 68
    :cond_2
    iget-boolean p2, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->Z$0:Z

    .line 69
    .line 70
    iget-object p1, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$0:Ljava/lang/Object;

    .line 71
    .line 72
    check-cast p1, Ljava/util/List;

    .line 73
    .line 74
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 75
    .line 76
    .line 77
    goto :goto_3

    .line 78
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 82
    .line 83
    .line 84
    move-result p4

    .line 85
    if-eqz p4, :cond_4

    .line 86
    .line 87
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    return-object p1

    .line 92
    :cond_4
    new-instance p4, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$b;

    .line 93
    .line 94
    invoke-direct {p4}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$b;-><init>()V

    .line 95
    .line 96
    .line 97
    invoke-static {p1, p4}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->a:Lu81/b;

    .line 102
    .line 103
    new-instance p4, Ljava/util/ArrayList;

    .line 104
    .line 105
    invoke-static {p1, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 106
    .line 107
    .line 108
    move-result v3

    .line 109
    invoke-direct {p4, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 110
    .line 111
    .line 112
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object v3

    .line 116
    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v4

    .line 120
    if-eqz v4, :cond_5

    .line 121
    .line 122
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v4

    .line 126
    check-cast v4, Lg9/e;

    .line 127
    .line 128
    invoke-virtual {v4}, Lg9/e;->b()J

    .line 129
    .line 130
    .line 131
    move-result-wide v4

    .line 132
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 133
    .line 134
    .line 135
    move-result-object v4

    .line 136
    invoke-interface {p4, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 137
    .line 138
    .line 139
    goto :goto_2

    .line 140
    :cond_5
    invoke-static {p4}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 141
    .line 142
    .line 143
    move-result-object p4

    .line 144
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->b:LfX/b;

    .line 145
    .line 146
    invoke-interface {v3}, LfX/b;->B0()Z

    .line 147
    .line 148
    .line 149
    move-result v3

    .line 150
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->e:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 151
    .line 152
    invoke-interface {v4}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 153
    .line 154
    .line 155
    move-result-object v4

    .line 156
    invoke-virtual {v4}, Lek0/o;->o()Lek0/a;

    .line 157
    .line 158
    .line 159
    move-result-object v4

    .line 160
    invoke-virtual {v4}, Lek0/a;->c()Z

    .line 161
    .line 162
    .line 163
    move-result v4

    .line 164
    iput-object p1, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$0:Ljava/lang/Object;

    .line 165
    .line 166
    iput-boolean p2, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->Z$0:Z

    .line 167
    .line 168
    iput v2, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->label:I

    .line 169
    .line 170
    move-object v5, p3

    .line 171
    move-object v2, p4

    .line 172
    invoke-interface/range {v1 .. v6}, Lu81/b;->j(Ljava/util/Set;ZZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object p4

    .line 176
    if-ne p4, v0, :cond_6

    .line 177
    .line 178
    goto :goto_6

    .line 179
    :cond_6
    :goto_3
    move-object p3, p4

    .line 180
    check-cast p3, Ljava/util/List;

    .line 181
    .line 182
    new-instance p4, Ljava/util/ArrayList;

    .line 183
    .line 184
    invoke-static {p1, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 185
    .line 186
    .line 187
    move-result v1

    .line 188
    invoke-direct {p4, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 189
    .line 190
    .line 191
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 192
    .line 193
    .line 194
    move-result-object p1

    .line 195
    :goto_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 196
    .line 197
    .line 198
    move-result v1

    .line 199
    if-eqz v1, :cond_7

    .line 200
    .line 201
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 202
    .line 203
    .line 204
    move-result-object v1

    .line 205
    check-cast v1, Lg9/e;

    .line 206
    .line 207
    invoke-virtual {v1}, Lg9/e;->b()J

    .line 208
    .line 209
    .line 210
    move-result-wide v1

    .line 211
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 212
    .line 213
    .line 214
    move-result-object v1

    .line 215
    invoke-interface {p4, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 216
    .line 217
    .line 218
    goto :goto_4

    .line 219
    :cond_7
    new-instance p1, Ljava/util/ArrayList;

    .line 220
    .line 221
    invoke-static {p3, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 222
    .line 223
    .line 224
    move-result v1

    .line 225
    invoke-direct {p1, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 226
    .line 227
    .line 228
    invoke-interface {p3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 229
    .line 230
    .line 231
    move-result-object v1

    .line 232
    :goto_5
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 233
    .line 234
    .line 235
    move-result v2

    .line 236
    if-eqz v2, :cond_8

    .line 237
    .line 238
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 239
    .line 240
    .line 241
    move-result-object v2

    .line 242
    check-cast v2, Lorg/xplatform/aggregator/api/model/Game;

    .line 243
    .line 244
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 245
    .line 246
    .line 247
    move-result-wide v2

    .line 248
    invoke-static {v2, v3}, LHc/a;->f(J)Ljava/lang/Long;

    .line 249
    .line 250
    .line 251
    move-result-object v2

    .line 252
    invoke-interface {p1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 253
    .line 254
    .line 255
    goto :goto_5

    .line 256
    :cond_8
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 257
    .line 258
    .line 259
    move-result-object p1

    .line 260
    check-cast p1, Ljava/lang/Iterable;

    .line 261
    .line 262
    invoke-static {p4, p1}, Lkotlin/collections/CollectionsKt;->m1(Ljava/lang/Iterable;Ljava/lang/Iterable;)Ljava/util/Set;

    .line 263
    .line 264
    .line 265
    move-result-object v1

    .line 266
    invoke-static {p4, p1}, Lkotlin/collections/CollectionsKt;->C0(Ljava/lang/Iterable;Ljava/lang/Iterable;)Ljava/util/Set;

    .line 267
    .line 268
    .line 269
    move-result-object p1

    .line 270
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->c:LHT/b;

    .line 271
    .line 272
    check-cast v1, Ljava/lang/Iterable;

    .line 273
    .line 274
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 275
    .line 276
    .line 277
    move-result-object v1

    .line 278
    iput-object p3, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$0:Ljava/lang/Object;

    .line 279
    .line 280
    iput-object p1, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->L$1:Ljava/lang/Object;

    .line 281
    .line 282
    iput-boolean p2, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->Z$0:Z

    .line 283
    .line 284
    iput v7, v6, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$getLastActionGames$1;->label:I

    .line 285
    .line 286
    invoke-interface {p4, v1, v6}, LHT/b;->e(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 287
    .line 288
    .line 289
    move-result-object p4

    .line 290
    if-ne p4, v0, :cond_9

    .line 291
    .line 292
    :goto_6
    return-object v0

    .line 293
    :cond_9
    move v9, p2

    .line 294
    move-object p2, p1

    .line 295
    move p1, v9

    .line 296
    :goto_7
    invoke-static {p3, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 297
    .line 298
    .line 299
    move-result p4

    .line 300
    invoke-static {p4}, Lkotlin/collections/P;->e(I)I

    .line 301
    .line 302
    .line 303
    move-result p4

    .line 304
    const/16 v0, 0x10

    .line 305
    .line 306
    invoke-static {p4, v0}, Lkotlin/ranges/f;->g(II)I

    .line 307
    .line 308
    .line 309
    move-result p4

    .line 310
    new-instance v0, Ljava/util/LinkedHashMap;

    .line 311
    .line 312
    invoke-direct {v0, p4}, Ljava/util/LinkedHashMap;-><init>(I)V

    .line 313
    .line 314
    .line 315
    invoke-interface {p3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 316
    .line 317
    .line 318
    move-result-object p3

    .line 319
    :goto_8
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 320
    .line 321
    .line 322
    move-result p4

    .line 323
    if-eqz p4, :cond_a

    .line 324
    .line 325
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 326
    .line 327
    .line 328
    move-result-object p4

    .line 329
    move-object v1, p4

    .line 330
    check-cast v1, Lorg/xplatform/aggregator/api/model/Game;

    .line 331
    .line 332
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 333
    .line 334
    .line 335
    move-result-wide v1

    .line 336
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 337
    .line 338
    .line 339
    move-result-object v1

    .line 340
    invoke-interface {v0, v1, p4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 341
    .line 342
    .line 343
    goto :goto_8

    .line 344
    :cond_a
    check-cast p2, Ljava/lang/Iterable;

    .line 345
    .line 346
    new-instance p3, Ljava/util/ArrayList;

    .line 347
    .line 348
    invoke-direct {p3}, Ljava/util/ArrayList;-><init>()V

    .line 349
    .line 350
    .line 351
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 352
    .line 353
    .line 354
    move-result-object p2

    .line 355
    :cond_b
    :goto_9
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 356
    .line 357
    .line 358
    move-result p4

    .line 359
    if-eqz p4, :cond_c

    .line 360
    .line 361
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 362
    .line 363
    .line 364
    move-result-object p4

    .line 365
    check-cast p4, Ljava/lang/Number;

    .line 366
    .line 367
    invoke-virtual {p4}, Ljava/lang/Number;->longValue()J

    .line 368
    .line 369
    .line 370
    move-result-wide v1

    .line 371
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 372
    .line 373
    .line 374
    move-result-object p4

    .line 375
    invoke-interface {v0, p4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 376
    .line 377
    .line 378
    move-result-object p4

    .line 379
    check-cast p4, Lorg/xplatform/aggregator/api/model/Game;

    .line 380
    .line 381
    if-eqz p4, :cond_b

    .line 382
    .line 383
    invoke-interface {p3, p4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 384
    .line 385
    .line 386
    goto :goto_9

    .line 387
    :cond_c
    invoke-virtual {p0, p3, p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->c(Ljava/util/List;Z)Ljava/util/List;

    .line 388
    .line 389
    .line 390
    move-result-object p1

    .line 391
    return-object p1
.end method

.method public final c(Ljava/util/List;Z)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;Z)",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;"
        }
    .end annotation

    .line 1
    if-eqz p2, :cond_2

    .line 2
    .line 3
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    const/16 v0, 0x8

    .line 8
    .line 9
    if-gt p2, v0, :cond_0

    .line 10
    .line 11
    move-object p2, p1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 p2, 0x0

    .line 14
    :goto_0
    if-nez p2, :cond_1

    .line 15
    .line 16
    const/4 p2, 0x0

    .line 17
    invoke-interface {p1, p2, v0}, Ljava/util/List;->subList(II)Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    return-object p1

    .line 22
    :cond_1
    return-object p2

    .line 23
    :cond_2
    return-object p1
.end method

.method public final d(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;
    .locals 2
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            ")",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->c:LHT/b;

    .line 2
    .line 3
    sget-object v1, Lcom/xbet/onexuser/domain/entity/onexgame/LastActionType;->AGGREGATOR:Lcom/xbet/onexuser/domain/entity/onexgame/LastActionType;

    .line 4
    .line 5
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/entity/onexgame/LastActionType;->getType()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-interface {v0, v1}, LHT/b;->b(I)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$1;

    .line 14
    .line 15
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 16
    .line 17
    .line 18
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2;

    .line 19
    .line 20
    invoke-direct {v0, v1, p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase$invoke$$inlined$map$2;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;ZLjava/lang/String;)V

    .line 21
    .line 22
    .line 23
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->d:Lm8/a;

    .line 24
    .line 25
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-static {v0, p1}, Lkotlinx/coroutines/flow/g;->Z(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/flow/e;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    return-object p1
.end method
