.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lg31/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$a;,
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;,
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;,
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\u000e\u0008\u0007\u0018\u0000 W2\u00020\u00012\u00020\u0002:\u00033X,B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0003\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0019\u0010\u000e\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000bH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0015\u0010\u0019\u001a\u0008\u0012\u0002\u0008\u0003\u0018\u00010\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001b\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001b\u0010\u000fJ#\u0010!\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u001c2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u001e\u00a2\u0006\u0004\u0008!\u0010\"J\r\u0010#\u001a\u00020\r\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010&\u001a\u0004\u0018\u00010%\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010)\u001a\u0004\u0018\u00010(\u00a2\u0006\u0004\u0008)\u0010*J\u000f\u0010+\u001a\u0004\u0018\u00010(\u00a2\u0006\u0004\u0008+\u0010*J!\u0010,\u001a\u00020\r2\u0006\u0010\u0004\u001a\u00020\u00032\u0008\u0010\u0006\u001a\u0004\u0018\u00010\u0005H\u0002\u00a2\u0006\u0004\u0008,\u0010-J\u0017\u0010/\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020.H\u0002\u00a2\u0006\u0004\u0008/\u00100J\'\u00101\u001a\u00020\r2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001c2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u001eH\u0002\u00a2\u0006\u0004\u00081\u0010\"J\u0019\u00103\u001a\u0004\u0018\u00010\u000b2\u0006\u00102\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u00083\u00104R\u0018\u00107\u001a\u0004\u0018\u0001058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008,\u00106R\u0018\u0010:\u001a\u0004\u0018\u0001088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u00109R\u0018\u0010;\u001a\u0004\u0018\u0001088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u00109R\u0018\u0010>\u001a\u0004\u0018\u00010<8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00081\u0010=R\u0018\u0010A\u001a\u0004\u0018\u00010?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u0010@R\u0014\u0010E\u001a\u00020B8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR$\u0010J\u001a\u00020.2\u0006\u0010F\u001a\u00020.8\u0002@BX\u0082\u000e\u00a2\u0006\u000c\n\u0004\u0008G\u0010H\"\u0004\u0008I\u00100R*\u0010R\u001a\u00020K2\u0006\u0010F\u001a\u00020K8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008L\u0010M\u001a\u0004\u0008N\u0010O\"\u0004\u0008P\u0010QR*\u0010V\u001a\u00020K2\u0006\u0010F\u001a\u00020K8\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008S\u0010M\u001a\u0004\u0008T\u0010O\"\u0004\u0008U\u0010Q\u00a8\u0006Y"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;",
        "Landroid/widget/FrameLayout;",
        "Lg31/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "type",
        "",
        "setTypeInternally",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;",
        "state",
        "setViewState",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V",
        "",
        "showShimmer",
        "setShimmerStateInternally",
        "(Z)V",
        "Lg31/j;",
        "getCurrentView",
        "()Lg31/j;",
        "setType",
        "Lg31/h;",
        "cashbackModel",
        "",
        "Lg31/b;",
        "levels",
        "setCashbackModel",
        "(Lg31/h;Ljava/util/List;)V",
        "e",
        "()V",
        "Landroid/widget/ProgressBar;",
        "getProgressBar",
        "()Landroid/widget/ProgressBar;",
        "Landroid/widget/TextView;",
        "getCurrentProgressTextView",
        "()Landroid/widget/TextView;",
        "getMaxProgressTextView",
        "a",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;",
        "c",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V",
        "d",
        "typeInt",
        "b",
        "(I)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;",
        "vipCashbackStatusLayout",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;",
        "vipCashbackRectangleVLayout",
        "vipCashbackRectangleHLayout",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;",
        "vipCashbackArrowLayout",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;",
        "f",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;",
        "shimmers",
        "value",
        "g",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;",
        "setCashbackState",
        "cashbackState",
        "",
        "h",
        "J",
        "getProgress",
        "()J",
        "setProgress",
        "(J)V",
        "progress",
        "i",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "j",
        "Type",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final j:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final k:I


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;

.field public b:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

.field public c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

.field public d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;

.field public e:Lorg/xbet/uikit/components/shimmer/ShimmerView;

.field public final f:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public h:J

.field public i:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->j:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->k:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p3, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;

    invoke-direct {p3, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;-><init>(Landroid/content/Context;)V

    iput-object p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->f:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;

    .line 6
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    const/16 v5, 0xf

    const/4 v6, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;-><init>(Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 7
    sget-object p3, Landroid/view/ViewOutlineProvider;->BACKGROUND:Landroid/view/ViewOutlineProvider;

    invoke-virtual {p0, p3}, Landroid/view/View;->setOutlineProvider(Landroid/view/ViewOutlineProvider;)V

    const/4 p3, 0x1

    .line 8
    invoke-virtual {p0, p3}, Landroid/view/View;->setClipToOutline(Z)V

    .line 9
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->a(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getCurrentView()Lg31/j;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lg31/j<",
            "*>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->f()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    const/4 v0, -0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$c;->a:[I

    .line 12
    .line 13
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    aget v0, v1, v0

    .line 18
    .line 19
    :goto_0
    const/4 v1, 0x1

    .line 20
    if-eq v0, v1, :cond_4

    .line 21
    .line 22
    const/4 v1, 0x2

    .line 23
    if-eq v0, v1, :cond_3

    .line 24
    .line 25
    const/4 v1, 0x3

    .line 26
    if-eq v0, v1, :cond_2

    .line 27
    .line 28
    const/4 v1, 0x4

    .line 29
    if-eq v0, v1, :cond_1

    .line 30
    .line 31
    const/4 v0, 0x0

    .line 32
    return-object v0

    .line 33
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;

    .line 34
    .line 35
    return-object v0

    .line 36
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 37
    .line 38
    return-object v0

    .line 39
    :cond_3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->b:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 40
    .line 41
    return-object v0

    .line 42
    :cond_4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;

    .line 43
    .line 44
    return-object v0
.end method

.method private final setCashbackState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->c(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method private final setShimmerStateInternally(Z)V
    .locals 4

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 4
    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 8
    .line 9
    .line 10
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->e:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 11
    .line 12
    const/16 v1, 0x8

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v0, :cond_2

    .line 16
    .line 17
    if-eqz p1, :cond_1

    .line 18
    .line 19
    const/4 v3, 0x0

    .line 20
    goto :goto_1

    .line 21
    :cond_1
    const/16 v3, 0x8

    .line 22
    .line 23
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 24
    .line 25
    .line 26
    :cond_2
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_4

    .line 31
    .line 32
    invoke-interface {v0}, Lg31/j;->getView()Landroid/view/View;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    if-eqz v0, :cond_4

    .line 37
    .line 38
    if-nez p1, :cond_3

    .line 39
    .line 40
    const/4 v1, 0x0

    .line 41
    :cond_3
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 42
    .line 43
    .line 44
    :cond_4
    return-void
.end method

.method private final setTypeInternally(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V
    .locals 12

    .line 1
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 2
    .line 3
    .line 4
    if-nez p1, :cond_0

    .line 5
    .line 6
    return-void

    .line 7
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->f:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;

    .line 8
    .line 9
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/b;->e(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->e:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 17
    .line 18
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$c;->a:[I

    .line 19
    .line 20
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    aget p1, v0, p1

    .line 25
    .line 26
    const/4 v0, 0x1

    .line 27
    if-eq p1, v0, :cond_4

    .line 28
    .line 29
    const/4 v1, 0x2

    .line 30
    const/4 v2, -0x2

    .line 31
    const/4 v3, -0x1

    .line 32
    if-eq p1, v1, :cond_3

    .line 33
    .line 34
    const/4 v0, 0x3

    .line 35
    if-eq p1, v0, :cond_2

    .line 36
    .line 37
    const/4 v0, 0x4

    .line 38
    if-ne p1, v0, :cond_1

    .line 39
    .line 40
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    const/4 v5, 0x6

    .line 47
    const/4 v6, 0x0

    .line 48
    const/4 v3, 0x0

    .line 49
    const/4 v4, 0x0

    .line 50
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 51
    .line 52
    .line 53
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;

    .line 54
    .line 55
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 56
    .line 57
    .line 58
    return-void

    .line 59
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 60
    .line 61
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 62
    .line 63
    .line 64
    throw p1

    .line 65
    :cond_2
    new-instance v4, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 66
    .line 67
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    const/4 v8, 0x6

    .line 72
    const/4 v9, 0x0

    .line 73
    const/4 v6, 0x0

    .line 74
    const/4 v7, 0x0

    .line 75
    invoke-direct/range {v4 .. v9}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    const/4 p1, 0x0

    .line 79
    invoke-virtual {v4, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setVertical(Z)V

    .line 80
    .line 81
    .line 82
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 83
    .line 84
    invoke-direct {p1, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 85
    .line 86
    .line 87
    invoke-virtual {v4}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    sget v1, LlZ0/g;->space_4:I

    .line 92
    .line 93
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 94
    .line 95
    .line 96
    move-result v0

    .line 97
    invoke-virtual {v4}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    sget v2, LlZ0/g;->space_16:I

    .line 102
    .line 103
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 104
    .line 105
    .line 106
    move-result v1

    .line 107
    invoke-virtual {v4}, Landroid/view/View;->getPaddingStart()I

    .line 108
    .line 109
    .line 110
    move-result v2

    .line 111
    invoke-virtual {v4}, Landroid/view/View;->getPaddingEnd()I

    .line 112
    .line 113
    .line 114
    move-result v3

    .line 115
    invoke-virtual {v4, v2, v0, v3, v1}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {v4, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 119
    .line 120
    .line 121
    iput-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 122
    .line 123
    invoke-virtual {p0, v4}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 124
    .line 125
    .line 126
    return-void

    .line 127
    :cond_3
    new-instance v5, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 128
    .line 129
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 130
    .line 131
    .line 132
    move-result-object v6

    .line 133
    const/4 v9, 0x6

    .line 134
    const/4 v10, 0x0

    .line 135
    const/4 v7, 0x0

    .line 136
    const/4 v8, 0x0

    .line 137
    invoke-direct/range {v5 .. v10}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 138
    .line 139
    .line 140
    invoke-virtual {v5, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setVertical(Z)V

    .line 141
    .line 142
    .line 143
    new-instance p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 144
    .line 145
    invoke-direct {p1, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 146
    .line 147
    .line 148
    invoke-virtual {v5}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 149
    .line 150
    .line 151
    move-result-object v0

    .line 152
    sget v1, LlZ0/g;->space_8:I

    .line 153
    .line 154
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 155
    .line 156
    .line 157
    move-result v0

    .line 158
    invoke-virtual {v5}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 159
    .line 160
    .line 161
    move-result-object v1

    .line 162
    sget v2, LlZ0/g;->space_16:I

    .line 163
    .line 164
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 165
    .line 166
    .line 167
    move-result v1

    .line 168
    invoke-virtual {v5}, Landroid/view/View;->getPaddingStart()I

    .line 169
    .line 170
    .line 171
    move-result v2

    .line 172
    invoke-virtual {v5}, Landroid/view/View;->getPaddingEnd()I

    .line 173
    .line 174
    .line 175
    move-result v3

    .line 176
    invoke-virtual {v5, v2, v0, v3, v1}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 177
    .line 178
    .line 179
    invoke-virtual {v5, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 180
    .line 181
    .line 182
    iput-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->b:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 183
    .line 184
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 185
    .line 186
    .line 187
    return-void

    .line 188
    :cond_4
    new-instance v6, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;

    .line 189
    .line 190
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 191
    .line 192
    .line 193
    move-result-object v7

    .line 194
    const/4 v10, 0x6

    .line 195
    const/4 v11, 0x0

    .line 196
    const/4 v8, 0x0

    .line 197
    const/4 v9, 0x0

    .line 198
    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 199
    .line 200
    .line 201
    iput-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;

    .line 202
    .line 203
    invoke-virtual {p0, v6}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 204
    .line 205
    .line 206
    return-void
.end method

.method private final setViewState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;

    .line 6
    .line 7
    if-eqz v0, :cond_3

    .line 8
    .line 9
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    .line 10
    .line 11
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/status/AggregatorVipCashbackStatusLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    .line 16
    .line 17
    if-eqz v0, :cond_2

    .line 18
    .line 19
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;->d()Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->b:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 28
    .line 29
    if-eqz v0, :cond_3

    .line 30
    .line 31
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    .line 32
    .line 33
    .line 34
    return-void

    .line 35
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->c:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;

    .line 36
    .line 37
    if-eqz v0, :cond_3

    .line 38
    .line 39
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_2
    instance-of v0, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;

    .line 44
    .line 45
    if-eqz v0, :cond_4

    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->d:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;

    .line 48
    .line 49
    if-eqz v0, :cond_3

    .line 50
    .line 51
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;

    .line 52
    .line 53
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/arrow/AggregatorVipCashbackArrowLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;)V

    .line 54
    .line 55
    .line 56
    :cond_3
    return-void

    .line 57
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 58
    .line 59
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 60
    .line 61
    .line 62
    throw p1
.end method


# virtual methods
.method public final a(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 4

    .line 1
    sget-object v0, LS11/h;->AggregatorVipCashback:[I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget p2, LS11/h;->AggregatorVipCashback_vipCashbackType:I

    .line 9
    .line 10
    const/4 v0, -0x1

    .line 11
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 12
    .line 13
    .line 14
    move-result p2

    .line 15
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->b(I)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setType(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 20
    .line 21
    .line 22
    sget p2, LS11/h;->AggregatorVipCashback_vipCashbackProgress:I

    .line 23
    .line 24
    invoke-virtual {p1, p2, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    int-to-long v2, p2

    .line 29
    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setProgress(J)V

    .line 30
    .line 31
    .line 32
    sget p2, LS11/h;->AggregatorVipCashback_vipCashbackMaxProgress:I

    .line 33
    .line 34
    invoke-virtual {p1, p2, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 35
    .line 36
    .line 37
    move-result p2

    .line 38
    int-to-long v0, p2

    .line 39
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setMaxProgress(J)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final b(I)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;
    .locals 2

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-ltz p1, :cond_0

    .line 6
    .line 7
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-ge p1, v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 20
    .line 21
    return-object p1
.end method

.method public final c(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->f()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setTypeInternally(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->c()Lg31/h;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->d()Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->d(Lg31/h;Ljava/util/List;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->e()Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setShimmerStateInternally(Z)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final d(Lg31/h;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    goto :goto_1

    .line 4
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->f()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    if-nez v0, :cond_1

    .line 11
    .line 12
    const/4 v0, -0x1

    .line 13
    goto :goto_0

    .line 14
    :cond_1
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$c;->a:[I

    .line 15
    .line 16
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    aget v0, v1, v0

    .line 21
    .line 22
    :goto_0
    const/4 v1, 0x1

    .line 23
    if-eq v0, v1, :cond_5

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    if-eq v0, v2, :cond_4

    .line 27
    .line 28
    const/4 v1, 0x3

    .line 29
    if-eq v0, v1, :cond_3

    .line 30
    .line 31
    const/4 v1, 0x4

    .line 32
    if-eq v0, v1, :cond_2

    .line 33
    .line 34
    :goto_1
    return-void

    .line 35
    :cond_2
    invoke-static {p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/a;->a(Lg31/h;Ljava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setViewState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_3
    const/4 v0, 0x0

    .line 44
    invoke-static {p1, v0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/a;->b(Lg31/h;ZLjava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setViewState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V

    .line 49
    .line 50
    .line 51
    return-void

    .line 52
    :cond_4
    invoke-static {p1, v1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/a;->b(Lg31/h;ZLjava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setViewState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V

    .line 57
    .line 58
    .line 59
    return-void

    .line 60
    :cond_5
    invoke-static {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/a;->c(Lg31/h;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setViewState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V

    .line 65
    .line 66
    .line 67
    return-void
.end method

.method public final e()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    const/4 v5, 0x7

    .line 4
    const/4 v6, 0x0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x1

    .line 9
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILjava/lang/Object;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-direct {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setCashbackState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final getCurrentProgressTextView()Landroid/widget/TextView;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0}, Lg31/j;->getCurrentProgressTextView()Landroid/widget/TextView;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    return-object v0
.end method

.method public getMaxProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->i:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final getMaxProgressTextView()Landroid/widget/TextView;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0}, Lg31/j;->getMaxProgressTextView()Landroid/widget/TextView;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    return-object v0
.end method

.method public getProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->h:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final getProgressBar()Landroid/widget/ProgressBar;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0}, Lg31/j;->getProgressBar()Landroid/widget/ProgressBar;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    return-object v0
.end method

.method public final setCashbackModel(Lg31/h;Ljava/util/List;)V
    .locals 7
    .param p1    # Lg31/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    const/4 v5, 0x4

    .line 4
    const/4 v6, 0x0

    .line 5
    const/4 v3, 0x0

    .line 6
    const/4 v4, 0x0

    .line 7
    move-object v1, p1

    .line 8
    move-object v2, p2

    .line 9
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILjava/lang/Object;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setCashbackState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public setMaxProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->i:J

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-interface {v0, p1, p2}, Lg31/c;->setMaxProgress(J)V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public setProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->h:J

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->getCurrentView()Lg31/j;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-interface {v0, p1, p2}, Lg31/c;->setProgress(J)V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public final setType(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->f()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eq p1, v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->g:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 10
    .line 11
    const/16 v6, 0xb

    .line 12
    .line 13
    const/4 v7, 0x0

    .line 14
    const/4 v2, 0x0

    .line 15
    const/4 v3, 0x0

    .line 16
    const/4 v5, 0x0

    .line 17
    move-object v4, p1

    .line 18
    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;->b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;Lg31/h;Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$Type;ZILjava/lang/Object;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback;->setCashbackState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashback$b;)V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method
