.class public LON0/a;
.super LZY0/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LON0/a$a;,
        LON0/a$b;,
        LON0/a$c;,
        LON0/a$d;,
        LON0/a$e;,
        LON0/a$f;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u000c\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010%\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0007\n\u0002\u0008\n\u0008\u0017\u0018\u0000 ^2\u00020\u0001:\u0005L]/-NB\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001f\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u000e\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0011\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\'\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u001f\u0010\u0019\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u001f\u0010\u001d\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\t\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u001f\u0010#\u001a\u00020\u00062\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010&\u001a\u00020\u00062\u0006\u0010%\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u001f\u0010*\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008*\u0010+J\'\u0010-\u001a\u00020\n2\u0006\u0010,\u001a\u00020\u00182\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008-\u0010.J\u001f\u0010/\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008/\u00100J\u001f\u00104\u001a\u00020\u00182\u0006\u00102\u001a\u0002012\u0006\u00103\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00084\u00105J\u0017\u00106\u001a\u00020\n2\u0006\u0010,\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u00086\u00107J\u0015\u0010:\u001a\u00020\n2\u0006\u00109\u001a\u000208\u00a2\u0006\u0004\u0008:\u0010;J\'\u0010<\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0018H\u0004\u00a2\u0006\u0004\u0008<\u0010=J\u0019\u0010@\u001a\u00020\u00062\u0008\u0010?\u001a\u0004\u0018\u00010>H\u0004\u00a2\u0006\u0004\u0008@\u0010AJ\u0017\u0010D\u001a\u00020\u00062\u0006\u0010C\u001a\u00020BH\u0004\u00a2\u0006\u0004\u0008D\u0010EJ\u0017\u0010F\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0006H\u0004\u00a2\u0006\u0004\u0008F\u0010\'J\u001f\u0010J\u001a\u00020\n2\u0006\u0010H\u001a\u00020G2\u0008\u0008\u0003\u0010I\u001a\u00020\u0006\u00a2\u0006\u0004\u0008J\u0010KR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR$\u0010?\u001a\u0004\u0018\u00010>8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008N\u0010O\u001a\u0004\u0008P\u0010Q\"\u0004\u0008R\u0010SR(\u0010[\u001a\u0008\u0012\u0004\u0012\u00020U0T8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008/\u0010V\u001a\u0004\u0008W\u0010X\"\u0004\u0008Y\u0010ZR(\u0010`\u001a\u0008\u0012\u0004\u0012\u00020\\0T8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008]\u0010V\u001a\u0004\u0008^\u0010X\"\u0004\u0008_\u0010ZR.\u0010d\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020a0T0T8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008-\u0010V\u001a\u0004\u0008b\u0010X\"\u0004\u0008c\u0010ZR\u0016\u0010g\u001a\u00020e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u0010fR \u0010j\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060h8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u0010iR\u001a\u0010n\u001a\u00020a8\u0004X\u0084\u0004\u00a2\u0006\u000c\n\u0004\u0008<\u0010k\u001a\u0004\u0008l\u0010mR\u0014\u0010q\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010pR\u0014\u0010r\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010pR\u0016\u0010t\u001a\u00020G8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010sR\u0016\u0010w\u001a\u00020u8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010vR\u0014\u0010y\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010xR\u0014\u0010z\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010xR\u0014\u0010|\u001a\u00020\u00068VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008N\u0010{R\u0014\u0010}\u001a\u00020\u00068VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008]\u0010{R\u0014\u0010~\u001a\u00020\u00068VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008L\u0010{\u00a8\u0006\u007f"
    }
    d2 = {
        "LON0/a;",
        "LZY0/b;",
        "Landroid/content/Context;",
        "context",
        "<init>",
        "(Landroid/content/Context;)V",
        "",
        "pos",
        "LON0/a$a;",
        "viewHolder",
        "",
        "k",
        "(ILON0/a$a;)V",
        "LON0/a$d;",
        "m",
        "(ILON0/a$d;)V",
        "LON0/a$e;",
        "n",
        "(LON0/a$e;)V",
        "row",
        "column",
        "LON0/a$c;",
        "l",
        "(IILON0/a$c;)V",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "j",
        "(ILandroidx/recyclerview/widget/RecyclerView$D;)V",
        "LaZ0/e;",
        "item",
        "i",
        "(LaZ0/e;Landroidx/recyclerview/widget/RecyclerView$D;)V",
        "",
        "text",
        "LaZ0/d;",
        "columnWidth",
        "q",
        "(Ljava/lang/String;LaZ0/d;)I",
        "position",
        "y",
        "(I)I",
        "LaZ0/a$c;",
        "columnTitle",
        "v",
        "(ILaZ0/a$c;)I",
        "holder",
        "e",
        "(Landroidx/recyclerview/widget/RecyclerView$D;II)V",
        "c",
        "(II)I",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "f",
        "(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;",
        "g",
        "(Landroidx/recyclerview/widget/RecyclerView$D;)V",
        "LaZ0/g;",
        "uiPanel",
        "B",
        "(LaZ0/g;)V",
        "h",
        "(LaZ0/e;ILandroidx/recyclerview/widget/RecyclerView$D;)V",
        "LaZ0/c;",
        "title",
        "t",
        "(LaZ0/c;)I",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;",
        "columnGravity",
        "w",
        "(Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)I",
        "p",
        "",
        "extraPadding",
        "maxColumnWidthRes",
        "z",
        "(ZI)V",
        "a",
        "Landroid/content/Context;",
        "b",
        "LaZ0/c;",
        "x",
        "()LaZ0/c;",
        "setTitle",
        "(LaZ0/c;)V",
        "",
        "LaZ0/f;",
        "Ljava/util/List;",
        "u",
        "()Ljava/util/List;",
        "setRowTitles",
        "(Ljava/util/List;)V",
        "rowTitles",
        "LaZ0/a;",
        "d",
        "o",
        "setColumnTitles",
        "columnTitles",
        "LaZ0/b;",
        "r",
        "setDataItems",
        "dataItems",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;",
        "backgroundType",
        "",
        "Ljava/util/Map;",
        "cachedWidthMap",
        "LaZ0/b;",
        "s",
        "()LaZ0/b;",
        "emptyItem",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "titlePaint",
        "dataPaint",
        "Z",
        "extraColumnWidth",
        "",
        "F",
        "maxExtraColumnWidth",
        "I",
        "padding",
        "iconColumnWidth",
        "()I",
        "firstColumnWidth",
        "rowCount",
        "columnCount",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:LON0/a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final p:I


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:LaZ0/c;

.field public c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LaZ0/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "LaZ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "LaZ0/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LaZ0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Z

.field public l:F

.field public final m:I

.field public final n:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LON0/a$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LON0/a$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LON0/a;->o:LON0/a$b;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LON0/a;->p:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, LZY0/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LON0/a;->a:Landroid/content/Context;

    .line 5
    .line 6
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iput-object v0, p0, LON0/a;->c:Ljava/util/List;

    .line 11
    .line 12
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iput-object v0, p0, LON0/a;->d:Ljava/util/List;

    .line 17
    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iput-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 23
    .line 24
    sget-object v0, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;->DEFAULT:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 25
    .line 26
    iput-object v0, p0, LON0/a;->f:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 27
    .line 28
    new-instance v0, Ljava/util/LinkedHashMap;

    .line 29
    .line 30
    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, LON0/a;->g:Ljava/util/Map;

    .line 34
    .line 35
    new-instance v1, LaZ0/b;

    .line 36
    .line 37
    const/16 v6, 0xe

    .line 38
    .line 39
    const/4 v7, 0x0

    .line 40
    const-string v2, ""

    .line 41
    .line 42
    const/4 v3, 0x0

    .line 43
    const/4 v4, 0x0

    .line 44
    const/4 v5, 0x0

    .line 45
    invoke-direct/range {v1 .. v7}, LaZ0/b;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 46
    .line 47
    .line 48
    iput-object v1, p0, LON0/a;->h:LaZ0/b;

    .line 49
    .line 50
    new-instance v0, Landroid/graphics/Paint;

    .line 51
    .line 52
    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 53
    .line 54
    .line 55
    sget v1, Lpb/h;->roboto_regular:I

    .line 56
    .line 57
    invoke-static {p1, v1}, LH0/h;->h(Landroid/content/Context;I)Landroid/graphics/Typeface;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    sget v3, Lpb/f;->text_16:I

    .line 66
    .line 67
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimension(I)F

    .line 68
    .line 69
    .line 70
    move-result v2

    .line 71
    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 75
    .line 76
    .line 77
    iput-object v0, p0, LON0/a;->i:Landroid/graphics/Paint;

    .line 78
    .line 79
    new-instance v0, Landroid/graphics/Paint;

    .line 80
    .line 81
    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 82
    .line 83
    .line 84
    sget v1, Lpb/h;->roboto_regular:I

    .line 85
    .line 86
    invoke-static {p1, v1}, LH0/h;->h(Landroid/content/Context;I)Landroid/graphics/Typeface;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    sget v3, Lpb/f;->text_12:I

    .line 95
    .line 96
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimension(I)F

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 104
    .line 105
    .line 106
    iput-object v0, p0, LON0/a;->j:Landroid/graphics/Paint;

    .line 107
    .line 108
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 109
    .line 110
    .line 111
    move-result-object v0

    .line 112
    sget v1, Lpb/f;->size_0:I

    .line 113
    .line 114
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 115
    .line 116
    .line 117
    move-result v0

    .line 118
    iput v0, p0, LON0/a;->l:F

    .line 119
    .line 120
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    sget v1, Lpb/f;->space_8:I

    .line 125
    .line 126
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    iput v0, p0, LON0/a;->m:I

    .line 131
    .line 132
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    sget v0, LxN0/a;->column_icon_width:I

    .line 137
    .line 138
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 139
    .line 140
    .line 141
    move-result p1

    .line 142
    iput p1, p0, LON0/a;->n:I

    .line 143
    .line 144
    return-void
.end method

.method public static synthetic A(LON0/a;ZIILjava/lang/Object;)V
    .locals 0

    .line 1
    if-nez p4, :cond_1

    .line 2
    .line 3
    and-int/lit8 p3, p3, 0x2

    .line 4
    .line 5
    if-eqz p3, :cond_0

    .line 6
    .line 7
    sget p2, Lpb/f;->size_0:I

    .line 8
    .line 9
    :cond_0
    invoke-virtual {p0, p1, p2}, LON0/a;->z(ZI)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_1
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    .line 14
    .line 15
    const-string p1, "Super calls with default arguments not supported in this target, function: setExtraColumnPadding"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0
.end method

.method private final j(ILandroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 6

    .line 1
    rem-int/lit8 p1, p1, 0x2

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    sget-object v0, Lub/b;->a:Lub/b;

    .line 6
    .line 7
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 8
    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    sget v2, Lpb/c;->background:I

    .line 14
    .line 15
    const/4 v4, 0x4

    .line 16
    const/4 v5, 0x0

    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    sget-object v0, Lub/b;->a:Lub/b;

    .line 24
    .line 25
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 26
    .line 27
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    sget v2, Lpb/c;->contentBackground:I

    .line 32
    .line 33
    const/4 v4, 0x4

    .line 34
    const/4 v5, 0x0

    .line 35
    const/4 v3, 0x0

    .line 36
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    :goto_0
    iget-object p2, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 41
    .line 42
    invoke-virtual {p2, p1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method


# virtual methods
.method public final B(LaZ0/g;)V
    .locals 1
    .param p1    # LaZ0/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LaZ0/g;->d()LaZ0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iput-object v0, p0, LON0/a;->b:LaZ0/c;

    .line 6
    .line 7
    invoke-virtual {p1}, LaZ0/g;->e()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, LON0/a;->c:Ljava/util/List;

    .line 12
    .line 13
    invoke-virtual {p1}, LaZ0/g;->b()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iput-object v0, p0, LON0/a;->d:Ljava/util/List;

    .line 18
    .line 19
    invoke-virtual {p1}, LaZ0/g;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iput-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 24
    .line 25
    invoke-virtual {p1}, LaZ0/g;->a()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iput-object p1, p0, LON0/a;->f:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 30
    .line 31
    iget-object p1, p0, LON0/a;->g:Ljava/util/Map;

    .line 32
    .line 33
    invoke-interface {p1}, Ljava/util/Map;->clear()V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public a()I
    .locals 1

    .line 1
    iget-object v0, p0, LON0/a;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public b()I
    .locals 1

    .line 1
    iget-object v0, p0, LON0/a;->b:LaZ0/c;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, LON0/a;->t(LaZ0/c;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public c(II)I
    .locals 0

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x3

    .line 6
    return p1

    .line 7
    :cond_0
    if-nez p2, :cond_1

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    return p1

    .line 11
    :cond_1
    if-nez p1, :cond_2

    .line 12
    .line 13
    const/4 p1, 0x1

    .line 14
    return p1

    .line 15
    :cond_2
    const/4 p1, 0x2

    .line 16
    return p1
.end method

.method public d()I
    .locals 1

    .line 1
    iget-object v0, p0, LON0/a;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public e(Landroidx/recyclerview/widget/RecyclerView$D;II)V
    .locals 2
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2, p3}, LON0/a;->c(II)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_2

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eq v0, v1, :cond_1

    .line 9
    .line 10
    const/4 v1, 0x3

    .line 11
    if-eq v0, v1, :cond_0

    .line 12
    .line 13
    check-cast p1, LON0/a$c;

    .line 14
    .line 15
    invoke-virtual {p0, p2, p3, p1}, LON0/a;->l(IILON0/a$c;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    check-cast p1, LON0/a$e;

    .line 20
    .line 21
    invoke-virtual {p0, p1}, LON0/a;->n(LON0/a$e;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_1
    check-cast p1, LON0/a$a;

    .line 26
    .line 27
    invoke-virtual {p0, p3, p1}, LON0/a;->k(ILON0/a$a;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_2
    check-cast p1, LON0/a$d;

    .line 32
    .line 33
    invoke-virtual {p0, p2, p1}, LON0/a;->m(ILON0/a$d;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public f(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 3
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz p2, :cond_2

    .line 11
    .line 12
    const/4 v2, 0x1

    .line 13
    if-eq p2, v2, :cond_1

    .line 14
    .line 15
    const/4 v2, 0x3

    .line 16
    if-eq p2, v2, :cond_0

    .line 17
    .line 18
    new-instance p2, LON0/a$c;

    .line 19
    .line 20
    invoke-static {v0, p1, v1}, LDN0/y;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/y;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-direct {p2, p1}, LON0/a$c;-><init>(LDN0/y;)V

    .line 25
    .line 26
    .line 27
    return-object p2

    .line 28
    :cond_0
    new-instance p2, LON0/a$e;

    .line 29
    .line 30
    invoke-static {v0, p1, v1}, LDN0/A;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/A;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-direct {p2, p1}, LON0/a$e;-><init>(LDN0/A;)V

    .line 35
    .line 36
    .line 37
    return-object p2

    .line 38
    :cond_1
    new-instance p2, LON0/a$a;

    .line 39
    .line 40
    invoke-static {v0, p1, v1}, LDN0/x;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/x;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-direct {p2, p1}, LON0/a$a;-><init>(LDN0/x;)V

    .line 45
    .line 46
    .line 47
    return-object p2

    .line 48
    :cond_2
    new-instance p2, LON0/a$d;

    .line 49
    .line 50
    invoke-static {v0, p1, v1}, LDN0/z;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/z;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    invoke-direct {p2, p1}, LON0/a$d;-><init>(LDN0/z;)V

    .line 55
    .line 56
    .line 57
    return-object p2
.end method

.method public g(Landroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 1
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, LZY0/b;->g(Landroidx/recyclerview/widget/RecyclerView$D;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, LON0/a$d;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p1, LON0/a$d;

    .line 9
    .line 10
    invoke-virtual {p1}, LON0/a$d;->e()V

    .line 11
    .line 12
    .line 13
    :cond_0
    return-void
.end method

.method public final h(LaZ0/e;ILandroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 2
    .param p1    # LaZ0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LON0/a;->f:Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/UiPanelBackgroundType;

    .line 2
    .line 3
    sget-object v1, LON0/a$f;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_2

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_1

    .line 16
    .line 17
    const/4 p2, 0x3

    .line 18
    if-ne v0, p2, :cond_0

    .line 19
    .line 20
    invoke-virtual {p0, p1, p3}, LON0/a;->i(LaZ0/e;Landroidx/recyclerview/widget/RecyclerView$D;)V

    .line 21
    .line 22
    .line 23
    return-void

    .line 24
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    invoke-direct {p0, p2, p3}, LON0/a;->j(ILandroidx/recyclerview/widget/RecyclerView$D;)V

    .line 31
    .line 32
    .line 33
    :cond_2
    return-void
.end method

.method public final i(LaZ0/e;Landroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 7

    .line 1
    invoke-virtual {p1}, LaZ0/e;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_2

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_1

    .line 13
    .line 14
    const/4 v2, 0x2

    .line 15
    if-eq v0, v2, :cond_0

    .line 16
    .line 17
    return-void

    .line 18
    :cond_0
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 19
    .line 20
    new-instance v2, LZY0/a;

    .line 21
    .line 22
    sget-object v3, Lub/b;->a:Lub/b;

    .line 23
    .line 24
    iget-object v4, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 25
    .line 26
    invoke-virtual {v4}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    invoke-virtual {p1}, LaZ0/e;->a()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    const/4 v6, 0x0

    .line 35
    invoke-interface {v5, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    check-cast v5, Ljava/lang/Number;

    .line 40
    .line 41
    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    .line 42
    .line 43
    .line 44
    move-result v5

    .line 45
    invoke-virtual {v3, v4, v5}, Lub/b;->d(Landroid/content/Context;I)I

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    iget-object p2, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 50
    .line 51
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    invoke-virtual {p1}, LaZ0/e;->a()Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    check-cast p1, Ljava/lang/Number;

    .line 64
    .line 65
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 66
    .line 67
    .line 68
    move-result p1

    .line 69
    invoke-virtual {v3, p2, p1}, Lub/b;->d(Landroid/content/Context;I)I

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    invoke-direct {v2, v4, p1}, LZY0/a;-><init>(II)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {v0, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 77
    .line 78
    .line 79
    return-void

    .line 80
    :cond_1
    iget-object p2, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 81
    .line 82
    sget-object v0, Lub/b;->a:Lub/b;

    .line 83
    .line 84
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    invoke-virtual {p1}, LaZ0/e;->a()Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    check-cast p1, Ljava/lang/Number;

    .line 97
    .line 98
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 99
    .line 100
    .line 101
    move-result p1

    .line 102
    invoke-virtual {v0, v1, p1}, Lub/b;->d(Landroid/content/Context;I)I

    .line 103
    .line 104
    .line 105
    move-result p1

    .line 106
    invoke-virtual {p2, p1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 107
    .line 108
    .line 109
    return-void

    .line 110
    :cond_2
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 111
    .line 112
    sget-object v0, Lub/b;->a:Lub/b;

    .line 113
    .line 114
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 115
    .line 116
    .line 117
    move-result-object v1

    .line 118
    sget v2, Lpb/c;->contentBackground:I

    .line 119
    .line 120
    const/4 v4, 0x4

    .line 121
    const/4 v5, 0x0

    .line 122
    const/4 v3, 0x0

    .line 123
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 124
    .line 125
    .line 126
    move-result p2

    .line 127
    invoke-virtual {p1, p2}, Landroid/view/View;->setBackgroundColor(I)V

    .line 128
    .line 129
    .line 130
    return-void
.end method

.method public final k(ILON0/a$a;)V
    .locals 6

    .line 1
    iget-object v0, p0, LON0/a;->d:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 p1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, LaZ0/a;

    .line 10
    .line 11
    invoke-virtual {p0, p1}, LON0/a;->p(I)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p2, v0, p1}, LON0/a$a;->d(LaZ0/a;I)V

    .line 16
    .line 17
    .line 18
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 19
    .line 20
    sget-object v0, Lub/b;->a:Lub/b;

    .line 21
    .line 22
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    sget v2, Lpb/c;->background:I

    .line 27
    .line 28
    const/4 v4, 0x4

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v3, 0x0

    .line 31
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 32
    .line 33
    .line 34
    move-result p2

    .line 35
    invoke-virtual {p1, p2}, Landroid/view/View;->setBackgroundColor(I)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final l(IILON0/a$c;)V
    .locals 3

    .line 1
    iget-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v1, p1, -0x1

    .line 8
    .line 9
    if-le v0, v1, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    add-int/lit8 v2, p2, -0x1

    .line 24
    .line 25
    if-le v0, v2, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 28
    .line 29
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Ljava/util/List;

    .line 34
    .line 35
    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, LaZ0/b;

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    iget-object v0, p0, LON0/a;->h:LaZ0/b;

    .line 43
    .line 44
    :goto_0
    add-int/lit8 p2, p2, -0x1

    .line 45
    .line 46
    invoke-virtual {p0, p2}, LON0/a;->p(I)I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    invoke-virtual {v0}, LaZ0/b;->b()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {p0, v1}, LON0/a;->w(Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-virtual {p3, v0, p2, v1}, LON0/a$c;->d(LaZ0/b;II)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0, v0, p1, p3}, LON0/a;->h(LaZ0/e;ILandroidx/recyclerview/widget/RecyclerView$D;)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public final m(ILON0/a$d;)V
    .locals 3

    .line 1
    iget-object v0, p0, LON0/a;->c:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 v1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, LaZ0/f;

    .line 10
    .line 11
    iget-object v1, p0, LON0/a;->b:LaZ0/c;

    .line 12
    .line 13
    invoke-virtual {p0, v1}, LON0/a;->t(LaZ0/c;)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    iget-object v2, p0, LON0/a;->b:LaZ0/c;

    .line 18
    .line 19
    if-eqz v2, :cond_0

    .line 20
    .line 21
    invoke-virtual {v2}, LaZ0/c;->a()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-virtual {p0, v2}, LON0/a;->w(Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const v2, 0x800003

    .line 31
    .line 32
    .line 33
    :goto_0
    invoke-virtual {p2, v0, v1, v2}, LON0/a$d;->d(LaZ0/f;II)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0, v0, p1, p2}, LON0/a;->h(LaZ0/e;ILandroidx/recyclerview/widget/RecyclerView$D;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final n(LON0/a$e;)V
    .locals 3

    .line 1
    iget-object v0, p0, LON0/a;->b:LaZ0/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0, v0}, LON0/a;->t(LaZ0/c;)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0}, LaZ0/c;->a()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {p0, v2}, LON0/a;->w(Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {p1, v0, v1, v2}, LON0/a$e;->d(LaZ0/c;II)V

    .line 18
    .line 19
    .line 20
    :cond_0
    return-void
.end method

.method public final o()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LaZ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LON0/a;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p(I)I
    .locals 4

    .line 1
    iget-object v0, p0, LON0/a;->g:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    if-nez v2, :cond_4

    .line 12
    .line 13
    iget-object v2, p0, LON0/a;->d:Ljava/util/List;

    .line 14
    .line 15
    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    check-cast v2, LaZ0/a;

    .line 20
    .line 21
    instance-of v3, v2, LaZ0/a$d;

    .line 22
    .line 23
    if-eqz v3, :cond_0

    .line 24
    .line 25
    iget-object p1, p0, LON0/a;->a:Landroid/content/Context;

    .line 26
    .line 27
    check-cast v2, LaZ0/a$d;

    .line 28
    .line 29
    invoke-virtual {v2}, LaZ0/a$d;->a()I

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    invoke-virtual {p1, v2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iget-object v2, p0, LON0/a;->i:Landroid/graphics/Paint;

    .line 38
    .line 39
    invoke-virtual {v2, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    float-to-int p1, p1

    .line 44
    iget v2, p0, LON0/a;->m:I

    .line 45
    .line 46
    mul-int/lit8 v2, v2, 0x2

    .line 47
    .line 48
    add-int/2addr p1, v2

    .line 49
    goto :goto_0

    .line 50
    :cond_0
    instance-of v3, v2, LaZ0/a$c;

    .line 51
    .line 52
    if-eqz v3, :cond_1

    .line 53
    .line 54
    check-cast v2, LaZ0/a$c;

    .line 55
    .line 56
    invoke-virtual {p0, p1, v2}, LON0/a;->v(ILaZ0/a$c;)I

    .line 57
    .line 58
    .line 59
    move-result p1

    .line 60
    goto :goto_0

    .line 61
    :cond_1
    instance-of p1, v2, LaZ0/a$a;

    .line 62
    .line 63
    if-eqz p1, :cond_2

    .line 64
    .line 65
    iget p1, p0, LON0/a;->n:I

    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_2
    instance-of p1, v2, LaZ0/a$b;

    .line 69
    .line 70
    if-eqz p1, :cond_3

    .line 71
    .line 72
    iget p1, p0, LON0/a;->n:I

    .line 73
    .line 74
    :goto_0
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 83
    .line 84
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 85
    .line 86
    .line 87
    throw p1

    .line 88
    :cond_4
    :goto_1
    check-cast v2, Ljava/lang/Number;

    .line 89
    .line 90
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 91
    .line 92
    .line 93
    move-result p1

    .line 94
    return p1
.end method

.method public final q(Ljava/lang/String;LaZ0/d;)I
    .locals 1

    .line 1
    instance-of v0, p2, LaZ0/d$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, LON0/a;->a:Landroid/content/Context;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p2, LaZ0/d$b;

    .line 12
    .line 13
    invoke-virtual {p2}, LaZ0/d$b;->a()I

    .line 14
    .line 15
    .line 16
    move-result p2

    .line 17
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    return p1

    .line 22
    :cond_0
    instance-of v0, p2, LaZ0/d$c;

    .line 23
    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    iget-object p2, p0, LON0/a;->i:Landroid/graphics/Paint;

    .line 27
    .line 28
    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    float-to-int p1, p1

    .line 33
    iget p2, p0, LON0/a;->m:I

    .line 34
    .line 35
    mul-int/lit8 p2, p2, 0x2

    .line 36
    .line 37
    add-int/2addr p1, p2

    .line 38
    return p1

    .line 39
    :cond_1
    instance-of p1, p2, LaZ0/d$a;

    .line 40
    .line 41
    if-eqz p1, :cond_2

    .line 42
    .line 43
    const/4 p1, 0x0

    .line 44
    return p1

    .line 45
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 46
    .line 47
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 48
    .line 49
    .line 50
    throw p1
.end method

.method public final r()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/util/List<",
            "LaZ0/b;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s()LaZ0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LON0/a;->h:LaZ0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t(LaZ0/c;)I
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p1, :cond_0

    .line 3
    .line 4
    invoke-virtual {p1}, LaZ0/c;->b()Ljava/lang/String;

    .line 5
    .line 6
    .line 7
    move-result-object v1

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    move-object v1, v0

    .line 10
    :goto_0
    if-nez v1, :cond_1

    .line 11
    .line 12
    const-string v1, ""

    .line 13
    .line 14
    :cond_1
    if-eqz p1, :cond_2

    .line 15
    .line 16
    invoke-virtual {p1}, LaZ0/c;->c()LaZ0/d;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    if-nez p1, :cond_3

    .line 21
    .line 22
    :cond_2
    new-instance p1, LaZ0/d$b;

    .line 23
    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x1

    .line 26
    invoke-direct {p1, v2, v3, v0}, LaZ0/d$b;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    .line 28
    .line 29
    :cond_3
    invoke-virtual {p0, v1, p1}, LON0/a;->q(Ljava/lang/String;LaZ0/d;)I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    return p1
.end method

.method public final u()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LaZ0/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LON0/a;->c:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v(ILaZ0/a$c;)I
    .locals 1

    .line 1
    iget-object v0, p0, LON0/a;->i:Landroid/graphics/Paint;

    .line 2
    .line 3
    invoke-virtual {p2}, LaZ0/a$c;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 8
    .line 9
    .line 10
    move-result p2

    .line 11
    float-to-int p2, p2

    .line 12
    iget-boolean v0, p0, LON0/a;->k:Z

    .line 13
    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    iget p1, p0, LON0/a;->m:I

    .line 17
    .line 18
    :goto_0
    mul-int/lit8 p1, p1, 0x2

    .line 19
    .line 20
    add-int/2addr p2, p1

    .line 21
    return p2

    .line 22
    :cond_0
    invoke-virtual {p0, p1}, LON0/a;->y(I)I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    if-le p2, p1, :cond_1

    .line 27
    .line 28
    goto :goto_1

    .line 29
    :cond_1
    move p2, p1

    .line 30
    :goto_1
    iget p1, p0, LON0/a;->l:F

    .line 31
    .line 32
    const/4 v0, 0x0

    .line 33
    cmpl-float v0, p1, v0

    .line 34
    .line 35
    if-lez v0, :cond_2

    .line 36
    .line 37
    int-to-float v0, p2

    .line 38
    cmpl-float v0, v0, p1

    .line 39
    .line 40
    if-lez v0, :cond_2

    .line 41
    .line 42
    float-to-int p2, p1

    .line 43
    :cond_2
    iget p1, p0, LON0/a;->m:I

    .line 44
    .line 45
    goto :goto_0
.end method

.method public final w(Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;)I
    .locals 2
    .param p1    # Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/scrollable/models/ColumnGravity;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LON0/a$f;->b:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_1

    .line 11
    .line 12
    const/4 v1, 0x2

    .line 13
    if-ne p1, v1, :cond_0

    .line 14
    .line 15
    return v0

    .line 16
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 17
    .line 18
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 19
    .line 20
    .line 21
    throw p1

    .line 22
    :cond_1
    const p1, 0x800003

    .line 23
    .line 24
    .line 25
    return p1
.end method

.method public final x()LaZ0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LON0/a;->b:LaZ0/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y(I)I
    .locals 5

    .line 1
    iget-object v0, p0, LON0/a;->e:Ljava/util/List;

    .line 2
    .line 3
    invoke-static {v0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_2

    .line 26
    .line 27
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, Ljava/util/List;

    .line 32
    .line 33
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-ge p1, v1, :cond_1

    .line 38
    .line 39
    const/4 v2, 0x1

    .line 40
    :cond_2
    :goto_0
    const-string v0, ""

    .line 41
    .line 42
    if-ltz p1, :cond_b

    .line 43
    .line 44
    if-nez v2, :cond_3

    .line 45
    .line 46
    goto/16 :goto_4

    .line 47
    .line 48
    :cond_3
    new-instance v1, Ljava/util/ArrayList;

    .line 49
    .line 50
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 51
    .line 52
    .line 53
    iget-object v2, p0, LON0/a;->e:Ljava/util/List;

    .line 54
    .line 55
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-eqz v3, :cond_6

    .line 64
    .line 65
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    check-cast v3, Ljava/util/List;

    .line 70
    .line 71
    invoke-static {v3, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v3

    .line 75
    check-cast v3, LaZ0/b;

    .line 76
    .line 77
    if-eqz v3, :cond_4

    .line 78
    .line 79
    invoke-virtual {v3}, LaZ0/b;->d()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v3

    .line 83
    if-nez v3, :cond_5

    .line 84
    .line 85
    :cond_4
    move-object v3, v0

    .line 86
    :cond_5
    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 87
    .line 88
    .line 89
    goto :goto_1

    .line 90
    :cond_6
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 95
    .line 96
    .line 97
    move-result v0

    .line 98
    if-nez v0, :cond_7

    .line 99
    .line 100
    const/4 p1, 0x0

    .line 101
    goto :goto_3

    .line 102
    :cond_7
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 107
    .line 108
    .line 109
    move-result v1

    .line 110
    if-nez v1, :cond_8

    .line 111
    .line 112
    :goto_2
    move-object p1, v0

    .line 113
    goto :goto_3

    .line 114
    :cond_8
    move-object v1, v0

    .line 115
    check-cast v1, Ljava/lang/String;

    .line 116
    .line 117
    iget-object v2, p0, LON0/a;->j:Landroid/graphics/Paint;

    .line 118
    .line 119
    invoke-virtual {v2, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 120
    .line 121
    .line 122
    move-result v1

    .line 123
    float-to-int v1, v1

    .line 124
    :cond_9
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object v2

    .line 128
    move-object v3, v2

    .line 129
    check-cast v3, Ljava/lang/String;

    .line 130
    .line 131
    iget-object v4, p0, LON0/a;->j:Landroid/graphics/Paint;

    .line 132
    .line 133
    invoke-virtual {v4, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 134
    .line 135
    .line 136
    move-result v3

    .line 137
    float-to-int v3, v3

    .line 138
    if-ge v1, v3, :cond_a

    .line 139
    .line 140
    move-object v0, v2

    .line 141
    move v1, v3

    .line 142
    :cond_a
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 143
    .line 144
    .line 145
    move-result v2

    .line 146
    if-nez v2, :cond_9

    .line 147
    .line 148
    goto :goto_2

    .line 149
    :goto_3
    move-object v0, p1

    .line 150
    check-cast v0, Ljava/lang/String;

    .line 151
    .line 152
    :cond_b
    :goto_4
    iget-object p1, p0, LON0/a;->j:Landroid/graphics/Paint;

    .line 153
    .line 154
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 155
    .line 156
    .line 157
    move-result p1

    .line 158
    float-to-int p1, p1

    .line 159
    iget v0, p0, LON0/a;->m:I

    .line 160
    .line 161
    add-int/2addr p1, v0

    .line 162
    return p1
.end method

.method public final z(ZI)V
    .locals 0

    .line 1
    iput-boolean p1, p0, LON0/a;->k:Z

    .line 2
    .line 3
    iget-object p1, p0, LON0/a;->a:Landroid/content/Context;

    .line 4
    .line 5
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimension(I)F

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    iput p1, p0, LON0/a;->l:F

    .line 14
    .line 15
    return-void
.end method
