.class public final Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;
.super Lorg/xbet/uikit/components/tag/Tag;
.source "SourceFile"

# interfaces
.implements LE31/j;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0015\n\u0002\u0008\u0002\n\u0002\u0010\r\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001f\u0010\u0014\u001a\u00020\u00132\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R\u0016\u0010\u0012\u001a\u00020\u00118\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "LE31/j;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "extraSpace",
        "",
        "onCreateDrawableState",
        "(I)[I",
        "",
        "text",
        "Lorg/xbet/uikit/core/eventcard/top/TeamNumber;",
        "teamNumber",
        "",
        "setText",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V",
        "f",
        "Lorg/xbet/uikit/core/eventcard/top/TeamNumber;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public f:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit/components/tag/Tag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    sget-object p1, Lorg/xbet/uikit/core/eventcard/top/TeamNumber;->None:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;->f:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, LlZ0/d;->betConstructorHeaderTagStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public onCreateDrawableState(I)[I
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    add-int/lit8 p1, p1, 0x2

    .line 2
    .line 3
    invoke-super {p0, p1}, Landroid/widget/TextView;->onCreateDrawableState(I)[I

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;->f:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 8
    .line 9
    sget-object v1, Lorg/xbet/uikit/core/eventcard/top/TeamNumber;->First:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 10
    .line 11
    if-ne v0, v1, :cond_0

    .line 12
    .line 13
    sget v1, LlZ0/d;->state_first:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    sget v1, LlZ0/d;->state_first:I

    .line 17
    .line 18
    neg-int v1, v1

    .line 19
    :goto_0
    sget-object v2, Lorg/xbet/uikit/core/eventcard/top/TeamNumber;->Second:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 20
    .line 21
    if-ne v0, v2, :cond_1

    .line 22
    .line 23
    sget v0, LlZ0/d;->state_second:I

    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_1
    sget v0, LlZ0/d;->state_second:I

    .line 27
    .line 28
    neg-int v0, v0

    .line 29
    :goto_1
    filled-new-array {v1, v0}, [I

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p1, v0}, Landroid/view/View;->mergeDrawableStates([I[I)[I

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    return-object p1
.end method

.method public final setText(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V
    .locals 0
    .param p2    # Lorg/xbet/uikit/core/eventcard/top/TeamNumber;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;->f:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->refreshDrawableState()V

    .line 7
    .line 8
    .line 9
    return-void
.end method
