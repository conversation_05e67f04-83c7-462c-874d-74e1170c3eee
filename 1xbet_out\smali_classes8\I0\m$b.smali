.class public LI0/m$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LI0/m$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LI0/m;->g(LH0/e$c;I)LH0/e$d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LI0/m$c<",
        "LH0/e$d;",
        ">;"
    }
.end annotation


# instance fields
.field public final synthetic a:LI0/m;


# direct methods
.method public constructor <init>(LI0/m;)V
    .locals 0

    .line 1
    iput-object p1, p0, LI0/m$b;->a:LI0/m;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;)I
    .locals 0

    .line 1
    check-cast p1, LH0/e$d;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LI0/m$b;->c(LH0/e$d;)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LH0/e$d;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LI0/m$b;->d(LH0/e$d;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public c(LH0/e$d;)I
    .locals 0

    .line 1
    invoke-virtual {p1}, LH0/e$d;->e()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public d(LH0/e$d;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, LH0/e$d;->f()Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
