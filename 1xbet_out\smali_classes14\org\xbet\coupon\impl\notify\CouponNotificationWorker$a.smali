.class public final Lorg/xbet/coupon/impl/notify/CouponNotificationWorker$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0011\u0010\u0006\u001a\u00020\u0005*\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0011\u0010\u0008\u001a\u00020\u0005*\u00020\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\u0007R\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000bR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/notify/CouponNotificationWorker$a;",
        "",
        "<init>",
        "()V",
        "Landroidx/work/WorkManager;",
        "",
        "a",
        "(Landroidx/work/WorkManager;)V",
        "b",
        "",
        "NAME",
        "Ljava/lang/String;",
        "",
        "SHOW_COUPON_NOTIFY_DELAY",
        "J",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/coupon/impl/notify/CouponNotificationWorker$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/work/WorkManager;)V
    .locals 7
    .param p1    # Landroidx/work/WorkManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Landroidx/work/d$a;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/work/d$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sget-object v1, Landroidx/work/NetworkType;->NOT_REQUIRED:Landroidx/work/NetworkType;

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroidx/work/d$a;->b(Landroidx/work/NetworkType;)Landroidx/work/d$a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Landroidx/work/d$a;->a()Landroidx/work/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sget-object v1, Landroidx/work/ExistingWorkPolicy;->REPLACE:Landroidx/work/ExistingWorkPolicy;

    .line 17
    .line 18
    new-instance v2, Landroidx/work/u$a;

    .line 19
    .line 20
    const-class v3, Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;

    .line 21
    .line 22
    invoke-direct {v2, v3}, Landroidx/work/u$a;-><init>(Ljava/lang/Class;)V

    .line 23
    .line 24
    .line 25
    const-string v3, "CouponNotificationWorker"

    .line 26
    .line 27
    invoke-virtual {v2, v3}, Landroidx/work/I$a;->a(Ljava/lang/String;)Landroidx/work/I$a;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    check-cast v2, Landroidx/work/u$a;

    .line 32
    .line 33
    const-wide/16 v4, 0x4b0

    .line 34
    .line 35
    sget-object v6, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 36
    .line 37
    invoke-virtual {v2, v4, v5, v6}, Landroidx/work/I$a;->k(JLjava/util/concurrent/TimeUnit;)Landroidx/work/I$a;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    check-cast v2, Landroidx/work/u$a;

    .line 42
    .line 43
    invoke-virtual {v2, v0}, Landroidx/work/I$a;->i(Landroidx/work/d;)Landroidx/work/I$a;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    check-cast v0, Landroidx/work/u$a;

    .line 48
    .line 49
    invoke-virtual {v0}, Landroidx/work/I$a;->b()Landroidx/work/I;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    check-cast v0, Landroidx/work/u;

    .line 54
    .line 55
    invoke-virtual {p1, v3, v1, v0}, Landroidx/work/WorkManager;->g(Ljava/lang/String;Landroidx/work/ExistingWorkPolicy;Landroidx/work/u;)Landroidx/work/v;

    .line 56
    .line 57
    .line 58
    return-void
.end method

.method public final b(Landroidx/work/WorkManager;)V
    .locals 1
    .param p1    # Landroidx/work/WorkManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const-string v0, "CouponNotificationWorker"

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Landroidx/work/WorkManager;->b(Ljava/lang/String;)Landroidx/work/v;

    .line 4
    .line 5
    .line 6
    return-void
.end method
