.class public final LlZ0/c;
.super Ljava/lang/Object;


# static fields
.field public static anim_in:I = 0x7f01000c

.field public static anim_out:I = 0x7f01000d

.field public static bottom_sheet_down:I = 0x7f01000e

.field public static bottom_sheet_up:I = 0x7f01000f

.field public static chevron_down_to_up_animation:I = 0x7f01001d

.field public static chevron_up_to_down_animation:I = 0x7f01001e

.field public static list_checkbox_overlay_interpolator_checked_end:I = 0x7f01003b

.field public static list_checkbox_overlay_interpolator_checked_start:I = 0x7f01003c

.field public static list_checkbox_overlay_interpolator_unchecked_end:I = 0x7f01003d

.field public static list_checkbox_overlay_interpolator_unchecked_start:I = 0x7f01003e

.field public static list_checkbox_primary_interpolator_end:I = 0x7f01003f

.field public static list_checkbox_primary_interpolator_start:I = 0x7f010040

.field public static list_checkbox_to_checked_box_inner_merged_animation:I = 0x7f010041

.field public static list_checkbox_to_checked_box_outer_merged_animation:I = 0x7f010042

.field public static list_checkbox_to_checked_icon_null_animation:I = 0x7f010043

.field public static list_checkbox_to_unchecked_box_inner_merged_animation:I = 0x7f010044

.field public static list_checkbox_to_unchecked_check_path_merged_animation:I = 0x7f010045

.field public static list_checkbox_to_unchecked_icon_null_animation:I = 0x7f010046

.field public static rotation_animation:I = 0x7f010059

.field public static rotation_animation_rtl:I = 0x7f01005a

.field public static shake:I = 0x7f01005b

.field public static shake_interpolator:I = 0x7f01005c


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
