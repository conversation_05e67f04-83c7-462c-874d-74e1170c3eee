.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsFragment$onObserveData$4"
    f = "AggregatorGiftsFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->invoke(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$a;->a()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->A3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$b;

    .line 32
    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->L3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;

    .line 42
    .line 43
    if-eqz v0, :cond_2

    .line 44
    .line 45
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 46
    .line 47
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    new-instance v1, Lma1/c;

    .line 52
    .line 53
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;

    .line 54
    .line 55
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;->a()Lkotlin/Pair;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    invoke-virtual {v2}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    check-cast v2, Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 64
    .line 65
    invoke-direct {v1, v2}, Lma1/c;-><init>(Lorg/xplatform/aggregator/core/AggregatorGame;)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$c;->a()Lkotlin/Pair;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-virtual {p1}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    check-cast p1, Ljava/lang/Number;

    .line 77
    .line 78
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 79
    .line 80
    .line 81
    move-result-wide v2

    .line 82
    invoke-virtual {v0, v1, v2, v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->J5(Lma1/c;J)V

    .line 83
    .line 84
    .line 85
    goto :goto_0

    .line 86
    :cond_2
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$d;

    .line 87
    .line 88
    if-eqz p1, :cond_3

    .line 89
    .line 90
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 91
    .line 92
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->z3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)LS91/G;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iget-object p1, p1, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 97
    .line 98
    const/4 v0, 0x0

    .line 99
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 100
    .line 101
    .line 102
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 103
    .line 104
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    new-instance v1, Ly01/g;

    .line 109
    .line 110
    sget-object v2, Ly01/i$b;->a:Ly01/i$b;

    .line 111
    .line 112
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 113
    .line 114
    sget v3, Lpb/k;->casino_gifts_bonus_activated:I

    .line 115
    .line 116
    invoke-virtual {p1, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v3

    .line 120
    const/16 v8, 0x3c

    .line 121
    .line 122
    const/4 v9, 0x0

    .line 123
    const/4 v4, 0x0

    .line 124
    const/4 v5, 0x0

    .line 125
    const/4 v6, 0x0

    .line 126
    const/4 v7, 0x0

    .line 127
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 128
    .line 129
    .line 130
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 131
    .line 132
    const/16 v10, 0x1fc

    .line 133
    .line 134
    const/4 v11, 0x0

    .line 135
    const/4 v3, 0x0

    .line 136
    const/4 v5, 0x0

    .line 137
    const/4 v6, 0x0

    .line 138
    const/4 v8, 0x0

    .line 139
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 140
    .line 141
    .line 142
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 143
    .line 144
    return-object p1

    .line 145
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 146
    .line 147
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 148
    .line 149
    .line 150
    throw p1

    .line 151
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 152
    .line 153
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 154
    .line 155
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 156
    .line 157
    .line 158
    throw p1
.end method
