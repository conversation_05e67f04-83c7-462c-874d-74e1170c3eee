.class public final Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;
.super Lkotlin/jvm/internal/Lambda;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->c(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "LOc/o<",
        "Landroidx/compose/foundation/lazy/c;",
        "Ljava/lang/Integer;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "<PERSON><PERSON>lin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0010\u0008\u001a\u00020\u0004\"\u0004\u0008\u0000\u0010\u0000*\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0002H\u000b\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "T",
        "Landroidx/compose/foundation/lazy/c;",
        "",
        "it",
        "",
        "invoke",
        "(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V",
        "androidx/compose/foundation/lazy/LazyDslKt$items$4",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $items:Ljava/util/List;

.field final synthetic $onPlayerClick$inlined:Lkotlin/jvm/functions/Function1;


# direct methods
.method public constructor <init>(Ljava/util/List;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;->$items:Ljava/util/List;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;->$onPlayerClick$inlined:Lkotlin/jvm/functions/Function1;

    const/4 p1, 0x4

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/lazy/c;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    check-cast p3, Landroidx/compose/runtime/j;

    check-cast p4, Ljava/lang/Number;

    invoke-virtual {p4}, Ljava/lang/Number;->intValue()I

    move-result p4

    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;->invoke(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V
    .locals 5

    and-int/lit8 v0, p4, 0x6

    const/4 v1, 0x2

    if-nez v0, :cond_1

    invoke-interface {p3, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x4

    goto :goto_0

    :cond_0
    const/4 p1, 0x2

    :goto_0
    or-int/2addr p1, p4

    goto :goto_1

    :cond_1
    move p1, p4

    :goto_1
    and-int/lit8 p4, p4, 0x30

    if-nez p4, :cond_3

    invoke-interface {p3, p2}, Landroidx/compose/runtime/j;->x(I)Z

    move-result p4

    if-eqz p4, :cond_2

    const/16 p4, 0x20

    goto :goto_2

    :cond_2
    const/16 p4, 0x10

    :goto_2
    or-int/2addr p1, p4

    :cond_3
    and-int/lit16 p4, p1, 0x93

    const/16 v0, 0x92

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eq p4, v0, :cond_4

    const/4 p4, 0x1

    goto :goto_3

    :cond_4
    const/4 p4, 0x0

    :goto_3
    and-int/lit8 v0, p1, 0x1

    .line 2
    invoke-interface {p3, p4, v0}, Landroidx/compose/runtime/j;->e(ZI)Z

    move-result p4

    if-eqz p4, :cond_9

    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    move-result p4

    if-eqz p4, :cond_5

    const/4 p4, -0x1

    const-string v0, "androidx.compose.foundation.lazy.items.<anonymous> (LazyDsl.kt:178)"

    const v4, -0x25b7f321

    invoke-static {v4, p1, p4, v0}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;->$items:Ljava/util/List;

    invoke-interface {p1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ls31/a;

    const p2, -0x2dc90677

    .line 3
    invoke-interface {p3, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 4
    instance-of p2, p1, Ls31/a$a;

    if-eqz p2, :cond_6

    const p2, -0x2dc7eb79

    invoke-interface {p3, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 5
    check-cast p1, Ls31/a$a;

    invoke-virtual {p1}, Ls31/a$a;->a()Ljava/lang/String;

    move-result-object p1

    const/4 p2, 0x0

    invoke-static {p1, p2, p3, v2, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/t;->b(Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 6
    invoke-interface {p3}, Landroidx/compose/runtime/j;->q()V

    goto :goto_4

    .line 7
    :cond_6
    instance-of p2, p1, Ls31/a$b;

    if-eqz p2, :cond_8

    const p2, -0x2dc5b407

    invoke-interface {p3, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 8
    invoke-static {}, Landroidx/compose/ui/platform/CompositionLocalsKt;->l()Landroidx/compose/runtime/x0;

    move-result-object p2

    sget-object p4, Landroidx/compose/ui/unit/LayoutDirection;->Ltr:Landroidx/compose/ui/unit/LayoutDirection;

    invoke-virtual {p2, p4}, Landroidx/compose/runtime/x0;->d(Ljava/lang/Object;)Landroidx/compose/runtime/y0;

    move-result-object p2

    .line 9
    new-instance p4, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$a;

    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;->$onPlayerClick$inlined:Lkotlin/jvm/functions/Function1;

    invoke-direct {p4, p1, v0}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$a;-><init>(Ls31/a;Lkotlin/jvm/functions/Function1;)V

    const/16 p1, 0x36

    const v0, 0x109eed70

    invoke-static {v0, v3, p4, p3, p1}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    move-result-object p1

    sget p4, Landroidx/compose/runtime/y0;->i:I

    or-int/lit8 p4, p4, 0x30

    .line 10
    invoke-static {p2, p1, p3, p4}, Landroidx/compose/runtime/CompositionLocalKt;->b(Landroidx/compose/runtime/y0;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/j;I)V

    .line 11
    invoke-interface {p3}, Landroidx/compose/runtime/j;->q()V

    .line 12
    :goto_4
    invoke-interface {p3}, Landroidx/compose/runtime/j;->q()V

    .line 13
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    move-result p1

    if-eqz p1, :cond_7

    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    :cond_7
    return-void

    :cond_8
    const p1, 0x69e0bdba

    .line 14
    invoke-interface {p3, p1}, Landroidx/compose/runtime/j;->t(I)V

    invoke-interface {p3}, Landroidx/compose/runtime/j;->q()V

    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw p1

    .line 15
    :cond_9
    invoke-interface {p3}, Landroidx/compose/runtime/j;->n()V

    return-void
.end method
