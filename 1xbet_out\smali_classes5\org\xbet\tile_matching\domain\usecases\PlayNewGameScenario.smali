.class public final Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u000c\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0018\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u000eH\u0080B\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001b\u00a8\u0006\u001c"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;",
        "",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "getBetSumUseCase",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "getActiveBalanceUseCase",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "getBonusUseCase",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/c;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;LAT0/a;)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "",
        "a",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/core/domain/usecases/bet/d;",
        "b",
        "Lorg/xbet/core/domain/usecases/balance/c;",
        "c",
        "Lorg/xbet/core/domain/usecases/bonus/e;",
        "d",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "e",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/domain/usecases/bet/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/core/domain/usecases/balance/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/core/domain/usecases/bonus/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/balance/c;Lorg/xbet/core/domain/usecases/bonus/e;Lorg/xbet/core/domain/usecases/AddCommandScenario;LAT0/a;)V
    .locals 0
    .param p1    # Lorg/xbet/core/domain/usecases/bet/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/balance/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/core/domain/usecases/bonus/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->a:Lorg/xbet/core/domain/usecases/bet/d;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->b:Lorg/xbet/core/domain/usecases/balance/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->c:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->d:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->e:LAT0/a;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v8, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;-><init>(Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->label:I

    .line 34
    .line 35
    const/4 v9, 0x2

    .line 36
    const/4 v2, 0x1

    .line 37
    if-eqz v1, :cond_3

    .line 38
    .line 39
    if-eq v1, v2, :cond_2

    .line 40
    .line 41
    if-ne v1, v9, :cond_1

    .line 42
    .line 43
    iget-object p1, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast p1, LzT0/e;

    .line 46
    .line 47
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    goto :goto_4

    .line 51
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw p1

    .line 59
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    goto :goto_2

    .line 63
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->e:LAT0/a;

    .line 67
    .line 68
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->c:Lorg/xbet/core/domain/usecases/bonus/e;

    .line 69
    .line 70
    invoke-virtual {p2}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 71
    .line 72
    .line 73
    move-result-object p2

    .line 74
    iget-object v3, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->a:Lorg/xbet/core/domain/usecases/bet/d;

    .line 75
    .line 76
    invoke-virtual {v3}, Lorg/xbet/core/domain/usecases/bet/d;->a()D

    .line 77
    .line 78
    .line 79
    move-result-wide v3

    .line 80
    iget-object v5, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->b:Lorg/xbet/core/domain/usecases/balance/c;

    .line 81
    .line 82
    invoke-virtual {v5}, Lorg/xbet/core/domain/usecases/balance/c;->a()Lorg/xbet/balance/model/BalanceModel;

    .line 83
    .line 84
    .line 85
    move-result-object v5

    .line 86
    if-eqz v5, :cond_6

    .line 87
    .line 88
    invoke-virtual {v5}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 89
    .line 90
    .line 91
    move-result-wide v5

    .line 92
    iput v2, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->label:I

    .line 93
    .line 94
    move-object v7, p1

    .line 95
    move-object v2, p2

    .line 96
    invoke-interface/range {v1 .. v8}, LAT0/a;->f(Lorg/xbet/games_section/api/models/GameBonus;DJLcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p2

    .line 100
    if-ne p2, v0, :cond_4

    .line 101
    .line 102
    goto :goto_3

    .line 103
    :cond_4
    :goto_2
    move-object p1, p2

    .line 104
    check-cast p1, LzT0/e;

    .line 105
    .line 106
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->d:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 107
    .line 108
    sget-object v1, LTv/a$k;->a:LTv/a$k;

    .line 109
    .line 110
    iput-object p1, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 111
    .line 112
    iput v9, v8, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario$invoke$1;->label:I

    .line 113
    .line 114
    invoke-virtual {p2, v1, v8}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object p2

    .line 118
    if-ne p2, v0, :cond_5

    .line 119
    .line 120
    :goto_3
    return-object v0

    .line 121
    :cond_5
    :goto_4
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/PlayNewGameScenario;->e:LAT0/a;

    .line 122
    .line 123
    invoke-interface {p2, p1}, LAT0/a;->i(LzT0/e;)V

    .line 124
    .line 125
    .line 126
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 127
    .line 128
    return-object p1

    .line 129
    :cond_6
    new-instance p1, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;

    .line 130
    .line 131
    const-wide/16 v0, -0x1

    .line 132
    .line 133
    invoke-direct {p1, v0, v1}, Lcom/xbet/onexuser/domain/exceptions/BalanceNotExistException;-><init>(J)V

    .line 134
    .line 135
    .line 136
    throw p1
.end method
