.class public final LHA0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001R\u001c\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0004\u001a\u0004\u0008\u0005\u0010\u0006R\u001c\u0010\u0007\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0004\u001a\u0004\u0008\u0008\u0010\u0006R\u001c\u0010\t\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\u0004\u001a\u0004\u0008\n\u0010\u0006R\u001c\u0010\u000c\u001a\u0004\u0018\u00010\u000b8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u000c\u0010\r\u001a\u0004\u0008\u000e\u0010\u000fR\u001c\u0010\u0010\u001a\u0004\u0018\u00010\u000b8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0010\u0010\r\u001a\u0004\u0008\u0011\u0010\u000fR\u001c\u0010\u0013\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0013\u0010\u0014\u001a\u0004\u0008\u0015\u0010\u0016R\u001c\u0010\u0017\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0014\u001a\u0004\u0008\u0018\u0010\u0016R\u001c\u0010\u001a\u001a\u0004\u0018\u00010\u00198\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001dR\u001c\u0010\u001f\u001a\u0004\u0018\u00010\u001e8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R\u001c\u0010$\u001a\u0004\u0018\u00010#8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'R\"\u0010*\u001a\n\u0012\u0004\u0012\u00020)\u0018\u00010(8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008*\u0010+\u001a\u0004\u0008,\u0010-R\u001c\u0010/\u001a\u0004\u0018\u00010.8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102R\u001c\u00103\u001a\u0004\u0018\u00010.8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u00083\u00100\u001a\u0004\u00084\u00102R\u001c\u00106\u001a\u0004\u0018\u0001058\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109R\u001c\u0010:\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008:\u0010\u0004\u001a\u0004\u0008;\u0010\u0006R\u001c\u0010<\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008<\u0010\u0014\u001a\u0004\u0008=\u0010\u0016R\u001c\u0010>\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008>\u0010\u0014\u001a\u0004\u0008?\u0010\u0016R\u001c\u0010@\u001a\u0004\u0018\u00010\u000b8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008@\u0010\r\u001a\u0004\u0008A\u0010\u000fR\u001c\u0010C\u001a\u0004\u0018\u00010B8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010FR\u001c\u0010G\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008G\u0010\u0004\u001a\u0004\u0008H\u0010\u0006R\u001c\u0010I\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008I\u0010\u0014\u001a\u0004\u0008J\u0010\u0016R\u001c\u0010L\u001a\u0004\u0018\u00010K8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008L\u0010M\u001a\u0004\u0008N\u0010OR\"\u0010Q\u001a\n\u0012\u0004\u0012\u00020P\u0018\u00010(8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008Q\u0010+\u001a\u0004\u0008R\u0010-R\u001c\u0010S\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008S\u0010\u0014\u001a\u0004\u0008T\u0010\u0016R\u001c\u0010V\u001a\u0004\u0018\u00010U8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008V\u0010W\u001a\u0004\u0008X\u0010YR\u001c\u0010Z\u001a\u0004\u0018\u00010\u00128\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008Z\u0010\u0014\u001a\u0004\u0008[\u0010\u0016R\u001c\u0010\\\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008\\\u0010\u0004\u001a\u0004\u0008]\u0010\u0006R\u001c\u0010_\u001a\u0004\u0018\u00010^8\u0006X\u0087\u0004\u00a2\u0006\u000c\n\u0004\u0008_\u0010`\u001a\u0004\u0008a\u0010b\u00a8\u0006c"
    }
    d2 = {
        "LHA0/a;",
        "",
        "",
        "id",
        "Ljava/lang/Long;",
        "l",
        "()Ljava/lang/Long;",
        "mainGameId",
        "n",
        "constId",
        "b",
        "",
        "dopInfo",
        "Ljava/lang/String;",
        "c",
        "()Ljava/lang/String;",
        "fullName",
        "d",
        "",
        "hasTabloStats",
        "Ljava/lang/Boolean;",
        "j",
        "()Ljava/lang/Boolean;",
        "homeAwayFlag",
        "k",
        "LIA0/d;",
        "liga",
        "LIA0/d;",
        "m",
        "()LIA0/d;",
        "LIA0/b;",
        "matchInfo",
        "LIA0/b;",
        "o",
        "()LIA0/b;",
        "LIA0/g;",
        "score",
        "LIA0/g;",
        "s",
        "()LIA0/g;",
        "",
        "LIA0/j;",
        "subGamesForMainGame",
        "Ljava/util/List;",
        "w",
        "()Ljava/util/List;",
        "LIA0/e;",
        "opponent1",
        "LIA0/e;",
        "q",
        "()LIA0/e;",
        "opponent2",
        "r",
        "LIA0/h;",
        "sport",
        "LIA0/h;",
        "t",
        "()LIA0/h;",
        "startTs",
        "u",
        "hasHeadToHead",
        "h",
        "isFinished",
        "B",
        "gameVidName",
        "e",
        "LIA0/c;",
        "video",
        "LIA0/c;",
        "y",
        "()LIA0/c;",
        "nextGameId",
        "p",
        "subscriptionAvailable",
        "x",
        "",
        "zoneId",
        "Ljava/lang/Integer;",
        "A",
        "()Ljava/lang/Integer;",
        "LIA0/a;",
        "alterOpponents",
        "a",
        "hasGraph",
        "g",
        "LIA0/i;",
        "statisticInfo",
        "LIA0/i;",
        "v",
        "()LIA0/i;",
        "hasInsights",
        "i",
        "globalChampId",
        "f",
        "LIA0/k;",
        "weatherOpponents",
        "LIA0/k;",
        "z",
        "()LIA0/k;",
        "core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final alterOpponents:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "alterOpponents"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LIA0/a;",
            ">;"
        }
    .end annotation
.end field

.field private final constId:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "constId"
    .end annotation
.end field

.field private final dopInfo:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "dopInfo"
    .end annotation
.end field

.field private final fullName:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "fullName"
    .end annotation
.end field

.field private final gameVidName:Ljava/lang/String;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "gameVidName"
    .end annotation
.end field

.field private final globalChampId:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "globalChampId"
    .end annotation
.end field

.field private final hasGraph:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "hasGraph"
    .end annotation
.end field

.field private final hasHeadToHead:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "hasHeadToHead"
    .end annotation
.end field

.field private final hasInsights:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "hasInsights"
    .end annotation
.end field

.field private final hasTabloStats:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "hasTabloStats"
    .end annotation
.end field

.field private final homeAwayFlag:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "homeAwayFlag"
    .end annotation
.end field

.field private final id:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "id"
    .end annotation
.end field

.field private final isFinished:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "isFinished"
    .end annotation
.end field

.field private final liga:LIA0/d;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "liga"
    .end annotation
.end field

.field private final mainGameId:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "mainGameId"
    .end annotation
.end field

.field private final matchInfo:LIA0/b;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "matchInfoObj"
    .end annotation
.end field

.field private final nextGameId:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "nextGameId"
    .end annotation
.end field

.field private final opponent1:LIA0/e;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "opponent1"
    .end annotation
.end field

.field private final opponent2:LIA0/e;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "opponent2"
    .end annotation
.end field

.field private final score:LIA0/g;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "scores"
    .end annotation
.end field

.field private final sport:LIA0/h;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "sport"
    .end annotation
.end field

.field private final startTs:Ljava/lang/Long;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "startTs"
    .end annotation
.end field

.field private final statisticInfo:LIA0/i;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "statisticInfo"
    .end annotation
.end field

.field private final subGamesForMainGame:Ljava/util/List;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "subGamesForMainGame"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LIA0/j;",
            ">;"
        }
    .end annotation
.end field

.field private final subscriptionAvailable:Ljava/lang/Boolean;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "subscriptionAvailable"
    .end annotation
.end field

.field private final video:LIA0/c;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "video"
    .end annotation
.end field

.field private final weatherOpponents:LIA0/k;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "opponents"
    .end annotation
.end field

.field private final zoneId:Ljava/lang/Integer;
    .annotation runtime Lcom/google/gson/annotations/SerializedName;
        value = "zonePlay"
    .end annotation
.end field


# virtual methods
.method public final A()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->zoneId:Ljava/lang/Integer;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->isFinished:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final a()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LIA0/a;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LHA0/a;->alterOpponents:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->constId:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->dopInfo:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->fullName:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->gameVidName:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->globalChampId:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->hasGraph:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->hasHeadToHead:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->hasInsights:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->hasTabloStats:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->homeAwayFlag:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->id:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()LIA0/d;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->liga:LIA0/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->mainGameId:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o()LIA0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->matchInfo:LIA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->nextGameId:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q()LIA0/e;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->opponent1:LIA0/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public final r()LIA0/e;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->opponent2:LIA0/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s()LIA0/g;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->score:LIA0/g;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t()LIA0/h;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->sport:LIA0/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u()Ljava/lang/Long;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->startTs:Ljava/lang/Long;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v()LIA0/i;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->statisticInfo:LIA0/i;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LIA0/j;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LHA0/a;->subGamesForMainGame:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x()Ljava/lang/Boolean;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->subscriptionAvailable:Ljava/lang/Boolean;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y()LIA0/c;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->video:LIA0/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final z()LIA0/k;
    .locals 1

    .line 1
    iget-object v0, p0, LHA0/a;->weatherOpponents:LIA0/k;

    .line 2
    .line 3
    return-object v0
.end method
