.class public final Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQ40/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\n\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001c\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\u000cH\u0096@\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;",
        "LQ40/a;",
        "LJ40/b;",
        "jackpotRemoteDateSource",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Lm8/a;",
        "dispatchers",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(LJ40/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lm8/a;Lc8/h;)V",
        "Lkotlin/Pair;",
        "LP40/c;",
        "",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LJ40/b;",
        "b",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "c",
        "Lm8/a;",
        "d",
        "Lc8/h;",
        "jackpot_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LJ40/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LJ40/b;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lm8/a;Lc8/h;)V
    .locals 0
    .param p1    # LJ40/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->a:LJ40/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->c:Lm8/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->d:Lc8/h;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic b(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)LJ40/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->a:LJ40/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)Lc8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->d:Lc8/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "LP40/c;",
            "Ljava/lang/Long;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->c:Lm8/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;-><init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1, p1}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method
