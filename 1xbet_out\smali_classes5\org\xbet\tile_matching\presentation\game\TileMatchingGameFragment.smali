.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000g\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0010\u0006\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0008\u0008*\u0001L\u0018\u0000 Q2\u00020\u0001:\u0001RB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0006\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0003J\u000f\u0010\u0008\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\u0003J\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000cJ!\u0010\u0010\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\t2\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001d\u0010\u0015\u001a\u00020\u00042\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u0012H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001d\u0010\u0018\u001a\u00020\u00042\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0012H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0016J\u001d\u0010\u0019\u001a\u00020\u00042\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0012H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0016J\'\u0010\u001e\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001f\u0010!\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u001f\u0010#\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008#\u0010\"J\u0017\u0010%\u001a\u00020\u00042\u0006\u0010$\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\'\u0010\u0003J\u0017\u0010(\u001a\u00020\u001c2\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010*\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008*\u0010\u0003J\u0019\u0010-\u001a\u00020\u00042\u0008\u0010,\u001a\u0004\u0018\u00010+H\u0014\u00a2\u0006\u0004\u0008-\u0010.J\u000f\u0010/\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008/\u0010\u0003J\u000f\u00100\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u00080\u0010\u0003J\u000f\u00101\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u00081\u0010\u0003J\u000f\u00102\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u00082\u0010\u0003J\u000f\u00103\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u00083\u0010\u0003R\"\u0010;\u001a\u0002048\u0000@\u0000X\u0081.\u00a2\u0006\u0012\n\u0004\u00085\u00106\u001a\u0004\u00087\u00108\"\u0004\u00089\u0010:R\u001b\u0010A\u001a\u00020<8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008=\u0010>\u001a\u0004\u0008?\u0010@R\u001b\u0010G\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010FR\u001c\u0010K\u001a\u0008\u0012\u0004\u0012\u00020H0\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u001b\u0010P\u001a\u00020L8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008M\u0010D\u001a\u0004\u0008N\u0010O\u00a8\u0006S"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "h3",
        "e3",
        "g3",
        "f3",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "T2",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V",
        "W2",
        "",
        "additionalMargin",
        "U2",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;I)V",
        "",
        "",
        "coeffsList",
        "L2",
        "(Ljava/util/List;)V",
        "progressList",
        "M2",
        "N2",
        "viewIndex",
        "coeff",
        "",
        "empty",
        "O2",
        "(IDZ)V",
        "progress",
        "R2",
        "(II)V",
        "Q2",
        "show",
        "i3",
        "(Z)V",
        "P2",
        "S2",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onResume",
        "onPause",
        "onDestroyView",
        "s2",
        "LyT0/c$c;",
        "i0",
        "LyT0/c$c;",
        "b3",
        "()LyT0/c$c;",
        "setTileMatchingGameViewModelFactory$tile_matching_release",
        "(LyT0/c$c;)V",
        "tileMatchingGameViewModelFactory",
        "LxT0/a;",
        "j0",
        "LRc/c;",
        "Z2",
        "()LxT0/a;",
        "binding",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;",
        "k0",
        "Lkotlin/j;",
        "c3",
        "()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;",
        "viewModel",
        "Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;",
        "l0",
        "Ljava/util/List;",
        "coeffViewsList",
        "org/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b",
        "m0",
        "a3",
        "()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;",
        "globalListener",
        "n0",
        "a",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final n0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic o0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:LyT0/c$c;

.field public final j0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l0:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/tile_matching/databinding/FragmentTileMatchingBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->o0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->n0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LrT0/c;->fragment_tile_matching:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$binding$2;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->j0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/i;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/i;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->k0:Lkotlin/j;

    .line 57
    .line 58
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 63
    .line 64
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/j;

    .line 65
    .line 66
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/j;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 67
    .line 68
    .line 69
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->m0:Lkotlin/j;

    .line 74
    .line 75
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->X2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->d3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic C2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->L2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->M2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->N2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->P2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->S2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic H2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->T2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->W2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic K2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->i3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic V2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;IILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const/4 p2, 0x0

    .line 6
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->U2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public static final X2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->c4(II)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final Y2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->f4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final d3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final h3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->V3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeViewState$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeViewState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeViewState$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeViewState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public static final j3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->b3()LyT0/c$c;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->j3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Y2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final L2(Ljava/util/List;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Double;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_2

    .line 10
    :cond_0
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    const/4 v1, 0x0

    .line 15
    if-eqz v0, :cond_2

    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_4

    .line 28
    .line 29
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    add-int/lit8 v2, v1, 0x1

    .line 34
    .line 35
    if-gez v1, :cond_1

    .line 36
    .line 37
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 38
    .line 39
    .line 40
    :cond_1
    check-cast v0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 41
    .line 42
    const-wide/16 v3, 0x0

    .line 43
    .line 44
    const/4 v0, 0x1

    .line 45
    invoke-virtual {p0, v1, v3, v4, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->O2(IDZ)V

    .line 46
    .line 47
    .line 48
    move v1, v2

    .line 49
    goto :goto_0

    .line 50
    :cond_2
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    const/4 v0, 0x0

    .line 55
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 56
    .line 57
    .line 58
    move-result v2

    .line 59
    if-eqz v2, :cond_4

    .line 60
    .line 61
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    add-int/lit8 v3, v0, 0x1

    .line 66
    .line 67
    if-gez v0, :cond_3

    .line 68
    .line 69
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 70
    .line 71
    .line 72
    :cond_3
    check-cast v2, Ljava/lang/Number;

    .line 73
    .line 74
    invoke-virtual {v2}, Ljava/lang/Number;->doubleValue()D

    .line 75
    .line 76
    .line 77
    move-result-wide v4

    .line 78
    invoke-virtual {p0, v0, v4, v5, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->O2(IDZ)V

    .line 79
    .line 80
    .line 81
    move v0, v3

    .line 82
    goto :goto_1

    .line 83
    :cond_4
    :goto_2
    return-void
.end method

.method public final M2(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_2

    .line 10
    :cond_0
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    const/4 v1, 0x0

    .line 15
    if-eqz v0, :cond_2

    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    const/4 v0, 0x0

    .line 24
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_4

    .line 29
    .line 30
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    add-int/lit8 v3, v0, 0x1

    .line 35
    .line 36
    if-gez v0, :cond_1

    .line 37
    .line 38
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 39
    .line 40
    .line 41
    :cond_1
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 42
    .line 43
    invoke-virtual {p0, v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Q2(II)V

    .line 44
    .line 45
    .line 46
    move v0, v3

    .line 47
    goto :goto_0

    .line 48
    :cond_2
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    if-eqz v0, :cond_4

    .line 57
    .line 58
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    add-int/lit8 v2, v1, 0x1

    .line 63
    .line 64
    if-gez v1, :cond_3

    .line 65
    .line 66
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 67
    .line 68
    .line 69
    :cond_3
    check-cast v0, Ljava/lang/Number;

    .line 70
    .line 71
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 72
    .line 73
    .line 74
    move-result v0

    .line 75
    invoke-virtual {p0, v1, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Q2(II)V

    .line 76
    .line 77
    .line 78
    move v1, v2

    .line 79
    goto :goto_1

    .line 80
    :cond_4
    :goto_2
    return-void
.end method

.method public final N2(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_2

    .line 10
    :cond_0
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    const/4 v1, 0x0

    .line 15
    if-eqz v0, :cond_2

    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    const/4 v0, 0x0

    .line 24
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_4

    .line 29
    .line 30
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    add-int/lit8 v3, v0, 0x1

    .line 35
    .line 36
    if-gez v0, :cond_1

    .line 37
    .line 38
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 39
    .line 40
    .line 41
    :cond_1
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 42
    .line 43
    invoke-virtual {p0, v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->R2(II)V

    .line 44
    .line 45
    .line 46
    move v0, v3

    .line 47
    goto :goto_0

    .line 48
    :cond_2
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    if-eqz v0, :cond_4

    .line 57
    .line 58
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    add-int/lit8 v2, v1, 0x1

    .line 63
    .line 64
    if-gez v1, :cond_3

    .line 65
    .line 66
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 67
    .line 68
    .line 69
    :cond_3
    check-cast v0, Ljava/lang/Number;

    .line 70
    .line 71
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 72
    .line 73
    .line 74
    move-result v0

    .line 75
    invoke-virtual {p0, v1, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->R2(II)V

    .line 76
    .line 77
    .line 78
    move v1, v2

    .line 79
    goto :goto_1

    .line 80
    :cond_4
    :goto_2
    return-void
.end method

.method public final O2(IDZ)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 8
    .line 9
    invoke-virtual {p1, p2, p3, p4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->setCoeffValue$tile_matching_release(DZ)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final P2()V
    .locals 10

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/a;->f:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    int-to-double v0, v0

    .line 12
    const-wide v2, 0x3fda3d70a3d70a3dL    # 0.41

    .line 13
    .line 14
    .line 15
    .line 16
    .line 17
    mul-double v0, v0, v2

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    iget-object v2, v2, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 24
    .line 25
    invoke-virtual {v2}, Landroid/view/View;->getHeight()I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    int-to-double v2, v2

    .line 30
    const-wide v4, 0x3fd1eb851eb851ecL    # 0.28

    .line 31
    .line 32
    .line 33
    .line 34
    .line 35
    mul-double v2, v2, v4

    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    iget-object v4, v4, LxT0/a;->f:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 42
    .line 43
    invoke-virtual {v4}, Landroid/view/View;->getTop()I

    .line 44
    .line 45
    .line 46
    move-result v4

    .line 47
    int-to-double v4, v4

    .line 48
    sub-double/2addr v4, v0

    .line 49
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v0, v0, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 54
    .line 55
    invoke-virtual {v0}, Landroid/view/View;->getBottom()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    int-to-double v0, v0

    .line 60
    add-double/2addr v0, v2

    .line 61
    sget-object v6, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 62
    .line 63
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 64
    .line 65
    .line 66
    move-result-object v7

    .line 67
    invoke-virtual {v6, v7}, Lorg/xbet/ui_common/utils/g;->z(Landroid/content/Context;)Z

    .line 68
    .line 69
    .line 70
    move-result v7

    .line 71
    const/4 v8, 0x6

    .line 72
    if-eqz v7, :cond_0

    .line 73
    .line 74
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 75
    .line 76
    .line 77
    move-result-object v7

    .line 78
    iget-object v7, v7, LxT0/a;->h:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 79
    .line 80
    invoke-virtual {v7}, Landroid/view/View;->getLeft()I

    .line 81
    .line 82
    .line 83
    move-result v7

    .line 84
    invoke-static {v8}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 85
    .line 86
    .line 87
    move-result v9

    .line 88
    :goto_0
    sub-int/2addr v7, v9

    .line 89
    goto :goto_1

    .line 90
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 91
    .line 92
    .line 93
    move-result-object v7

    .line 94
    iget-object v7, v7, LxT0/a;->f:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 95
    .line 96
    invoke-virtual {v7}, Landroid/view/View;->getLeft()I

    .line 97
    .line 98
    .line 99
    move-result v7

    .line 100
    invoke-static {v8}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 101
    .line 102
    .line 103
    move-result v9

    .line 104
    goto :goto_0

    .line 105
    :goto_1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 106
    .line 107
    .line 108
    move-result-object v9

    .line 109
    invoke-virtual {v6, v9}, Lorg/xbet/ui_common/utils/g;->z(Landroid/content/Context;)Z

    .line 110
    .line 111
    .line 112
    move-result v6

    .line 113
    if-eqz v6, :cond_1

    .line 114
    .line 115
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 116
    .line 117
    .line 118
    move-result-object v6

    .line 119
    iget-object v6, v6, LxT0/a;->f:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 120
    .line 121
    invoke-virtual {v6}, Landroid/view/View;->getRight()I

    .line 122
    .line 123
    .line 124
    move-result v6

    .line 125
    invoke-static {v8}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 126
    .line 127
    .line 128
    move-result v8

    .line 129
    :goto_2
    add-int/2addr v6, v8

    .line 130
    goto :goto_3

    .line 131
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 132
    .line 133
    .line 134
    move-result-object v6

    .line 135
    iget-object v6, v6, LxT0/a;->h:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 136
    .line 137
    invoke-virtual {v6}, Landroid/view/View;->getRight()I

    .line 138
    .line 139
    .line 140
    move-result v6

    .line 141
    invoke-static {v8}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 142
    .line 143
    .line 144
    move-result v8

    .line 145
    goto :goto_2

    .line 146
    :goto_3
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 147
    .line 148
    .line 149
    move-result-object v8

    .line 150
    iget-object v8, v8, LxT0/a;->c:Landroid/widget/ImageView;

    .line 151
    .line 152
    int-to-float v9, v7

    .line 153
    invoke-virtual {v8, v9}, Landroid/view/View;->setX(F)V

    .line 154
    .line 155
    .line 156
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 157
    .line 158
    .line 159
    move-result-object v8

    .line 160
    iget-object v8, v8, LxT0/a;->c:Landroid/widget/ImageView;

    .line 161
    .line 162
    double-to-float v9, v4

    .line 163
    invoke-virtual {v8, v9}, Landroid/view/View;->setY(F)V

    .line 164
    .line 165
    .line 166
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 167
    .line 168
    .line 169
    move-result-object v8

    .line 170
    iget-object v8, v8, LxT0/a;->c:Landroid/widget/ImageView;

    .line 171
    .line 172
    invoke-virtual {v8}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 173
    .line 174
    .line 175
    move-result-object v8

    .line 176
    sub-int/2addr v6, v7

    .line 177
    iput v6, v8, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 178
    .line 179
    sub-double/2addr v0, v4

    .line 180
    double-to-int v0, v0

    .line 181
    iput v0, v8, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 182
    .line 183
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 184
    .line 185
    .line 186
    move-result-object v0

    .line 187
    iget-object v0, v0, LxT0/a;->c:Landroid/widget/ImageView;

    .line 188
    .line 189
    invoke-virtual {v0, v8}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 190
    .line 191
    .line 192
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 193
    .line 194
    .line 195
    move-result-object v0

    .line 196
    iget-object v0, v0, LxT0/a;->c:Landroid/widget/ImageView;

    .line 197
    .line 198
    const/4 v1, 0x0

    .line 199
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 200
    .line 201
    .line 202
    sget-object v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->FRUIT_BLAST:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 203
    .line 204
    double-to-int v1, v2

    .line 205
    invoke-virtual {p0, v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->U2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;I)V

    .line 206
    .line 207
    .line 208
    return-void
.end method

.method public final Q2(II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 8
    .line 9
    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->s(I)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final R2(II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 8
    .line 9
    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->v(I)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final S2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z
    .locals 1

    .line 1
    sget-object v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->FRUIT_BLAST:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x1

    .line 6
    return p1

    .line 7
    :cond_0
    const/4 p1, 0x0

    .line 8
    return p1
.end method

.method public final T2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const/4 v1, 0x0

    .line 8
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 9
    .line 10
    .line 11
    move-result v2

    .line 12
    if-eqz v2, :cond_1

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    add-int/lit8 v3, v1, 0x1

    .line 19
    .line 20
    if-gez v1, :cond_0

    .line 21
    .line 22
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 23
    .line 24
    .line 25
    :cond_0
    check-cast v2, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 26
    .line 27
    invoke-virtual {v2, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->t(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 28
    .line 29
    .line 30
    invoke-static {p1}, LBT0/a;->g(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)[I

    .line 31
    .line 32
    .line 33
    move-result-object v4

    .line 34
    aget v4, v4, v1

    .line 35
    .line 36
    invoke-virtual {v2, v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->setCoeffImage$tile_matching_release(I)V

    .line 37
    .line 38
    .line 39
    invoke-static {p1}, LBT0/a;->r(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 40
    .line 41
    .line 42
    move-result v4

    .line 43
    invoke-virtual {v2, v4}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->setMaxProgress$tile_matching_release(I)V

    .line 44
    .line 45
    .line 46
    invoke-static {p1}, LBT0/a;->t(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)[I

    .line 47
    .line 48
    .line 49
    move-result-object v4

    .line 50
    aget v1, v4, v1

    .line 51
    .line 52
    invoke-virtual {v2, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->setProgressDrawableTint$tile_matching_release(I)V

    .line 53
    .line 54
    .line 55
    invoke-static {p1}, LBT0/a;->r(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 56
    .line 57
    .line 58
    move-result v1

    .line 59
    invoke-virtual {v2, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->v(I)V

    .line 60
    .line 61
    .line 62
    move v1, v3

    .line 63
    goto :goto_0

    .line 64
    :cond_1
    return-void
.end method

.method public final U2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;I)V
    .locals 6

    .line 1
    new-instance v0, Landroidx/constraintlayout/widget/b;

    .line 2
    .line 3
    invoke-direct {v0}, Landroidx/constraintlayout/widget/b;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    iget-object v1, v1, LxT0/a;->j:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroidx/constraintlayout/widget/b;->p(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 13
    .line 14
    .line 15
    sget v1, LrT0/b;->tvCombination:I

    .line 16
    .line 17
    const/4 v2, 0x3

    .line 18
    invoke-virtual {v0, v1, v2}, Landroidx/constraintlayout/widget/b;->n(II)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-static {p1}, LBT0/a;->i(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    add-int v5, p2, v1

    .line 34
    .line 35
    sget v1, LrT0/b;->tvCombination:I

    .line 36
    .line 37
    sget v3, LrT0/b;->coeffFour:I

    .line 38
    .line 39
    const/4 v4, 0x4

    .line 40
    const/4 v2, 0x3

    .line 41
    invoke-virtual/range {v0 .. v5}, Landroidx/constraintlayout/widget/b;->t(IIIII)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    iget-object p2, p2, LxT0/a;->j:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 49
    .line 50
    invoke-virtual {v0, p2}, Landroidx/constraintlayout/widget/b;->i(Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    iget-object p2, p2, LxT0/a;->m:Landroid/widget/TextView;

    .line 58
    .line 59
    invoke-static {p1}, LBT0/a;->j(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 60
    .line 61
    .line 62
    move-result p1

    .line 63
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 68
    .line 69
    .line 70
    return-void
.end method

.method public final W2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 6
    .line 7
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/g;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/g;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 10
    .line 11
    .line 12
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/h;

    .line 13
    .line 14
    invoke-direct {v2, p0}, Lorg/xbet/tile_matching/presentation/game/h;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {v0, p1, v1, v2}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->y(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->S2(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    if-eqz p1, :cond_0

    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iget-object p1, p1, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->a3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1, v0}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 41
    .line 42
    .line 43
    return-void

    .line 44
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iget-object p1, p1, LxT0/a;->l:Landroid/widget/ImageView;

    .line 49
    .line 50
    const/4 v0, 0x0

    .line 51
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final Z2()LxT0/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->j0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LxT0/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final a3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;

    .line 8
    .line 9
    return-object v0
.end method

.method public final b3()LyT0/c$c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->i0:LyT0/c$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final e3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->S3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCoeffState$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCoeffState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCoeffState$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCoeffState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final f3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->T3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final g3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->U3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final i3(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/a;->b:Landroid/view/View;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LxT0/a;->n:Landroid/widget/TextView;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    const/4 v1, 0x0

    .line 28
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 9
    .line 10
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->a3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 16
    .line 17
    .line 18
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->h4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public s2()V
    .locals 0

    .line 1
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 7

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->c3()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->Y3()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget-object p1, p1, LxT0/a;->f:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget-object v0, v0, LxT0/a;->i:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget-object v1, v1, LxT0/a;->h:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    iget-object v2, v2, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    iget-object v3, v3, LxT0/a;->d:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->Z2()LxT0/a;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    iget-object v4, v4, LxT0/a;->g:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 46
    .line 47
    const/4 v5, 0x6

    .line 48
    new-array v5, v5, [Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 49
    .line 50
    const/4 v6, 0x0

    .line 51
    aput-object p1, v5, v6

    .line 52
    .line 53
    const/4 p1, 0x1

    .line 54
    aput-object v0, v5, p1

    .line 55
    .line 56
    const/4 p1, 0x2

    .line 57
    aput-object v1, v5, p1

    .line 58
    .line 59
    const/4 p1, 0x3

    .line 60
    aput-object v2, v5, p1

    .line 61
    .line 62
    const/4 p1, 0x4

    .line 63
    aput-object v3, v5, p1

    .line 64
    .line 65
    const/4 p1, 0x5

    .line 66
    aput-object v4, v5, p1

    .line 67
    .line 68
    invoke-static {v5}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->l0:Ljava/util/List;

    .line 73
    .line 74
    return-void
.end method

.method public u2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k4()LyT0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-interface {v0, p0}, LyT0/c;->c(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public v2()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->h3()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->e3()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->g3()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->f3()V

    .line 11
    .line 12
    .line 13
    return-void
.end method
