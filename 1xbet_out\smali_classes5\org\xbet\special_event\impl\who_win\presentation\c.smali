.class public final Lorg/xbet/special_event/impl/who_win/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;LSX0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->j0:LSX0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
