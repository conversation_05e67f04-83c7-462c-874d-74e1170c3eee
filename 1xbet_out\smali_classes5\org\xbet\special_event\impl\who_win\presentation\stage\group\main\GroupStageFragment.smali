.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u0000 G2\u00020\u0001:\u0001HB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u000f\u0010\u000b\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J\u0017\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\"\u0010\u0017\u001a\u00020\u00108\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0011\u0010\u0012\u001a\u0004\u0008\u0013\u0010\u0014\"\u0004\u0008\u0015\u0010\u0016R\u001b\u0010\u001d\u001a\u00020\u00188BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u001b\u0010\u001cR\u001b\u0010#\u001a\u00020\u001e8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R\u001b\u0010)\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001a\u0010/\u001a\u00020*8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001b\u00104\u001a\u0002008BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u0010\u001a\u001a\u0004\u00082\u00103R\u0018\u00108\u001a\u0004\u0018\u0001058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u001b\u0010=\u001a\u0002098BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008:\u0010\u001a\u001a\u0004\u0008;\u0010<R\u001b\u0010A\u001a\u00020\u000c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008>\u0010\u001a\u001a\u0004\u0008?\u0010@R\u001b\u0010F\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u0010\u001a\u001a\u0004\u0008D\u0010E\u00a8\u0006I"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onDestroyView",
        "",
        "itemPosition",
        "L2",
        "(I)I",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "R2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;",
        "j0",
        "Lkotlin/j;",
        "Q2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;",
        "viewModel",
        "LGq0/t;",
        "k0",
        "LRc/c;",
        "P2",
        "()LGq0/t;",
        "viewBinding",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
        "l0",
        "LeX0/h;",
        "O2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
        "screenParams",
        "",
        "m0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "LNy0/b;",
        "n0",
        "K2",
        "()LNy0/b;",
        "groupsAdapter",
        "LNy0/a;",
        "o0",
        "LNy0/a;",
        "groupPagesAdapter",
        "Landroidx/recyclerview/widget/LinearLayoutManager;",
        "b1",
        "getLayoutManager",
        "()Landroidx/recyclerview/widget/LinearLayoutManager;",
        "layoutManager",
        "k1",
        "M2",
        "()I",
        "groupsListBetweenItemsOffset",
        "Landroidx/viewpager2/widget/ViewPager2$i;",
        "v1",
        "N2",
        "()Landroidx/viewpager2/widget/ViewPager2$i;",
        "onPageChangeCallback",
        "x1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final F1:I

.field public static final H1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final x1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic y1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LeX0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Z

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o0:LNy0/a;

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xbet/special_event/impl/databinding/FragmentGroupStageBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "screenParams"

    .line 20
    .line 21
    const-string v5, "getScreenParams()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v3, 0x2

    .line 31
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v3, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v2, v3, v0

    .line 37
    .line 38
    sput-object v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->y1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    invoke-direct {v0, v2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->x1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;

    .line 47
    .line 48
    const/16 v0, 0x8

    .line 49
    .line 50
    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->F1:I

    .line 51
    .line 52
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->H1:Ljava/lang/String;

    .line 57
    .line 58
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LUo0/c;->fragment_group_stage:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/a;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$viewBinding$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->k0:LRc/c;

    .line 57
    .line 58
    new-instance v0, LeX0/h;

    .line 59
    .line 60
    const-string v1, "PARAMS_KEY"

    .line 61
    .line 62
    const/4 v2, 0x2

    .line 63
    invoke-direct {v0, v1, v5, v2, v5}, LeX0/h;-><init>(Ljava/lang/String;Landroid/os/Parcelable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 64
    .line 65
    .line 66
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->l0:LeX0/h;

    .line 67
    .line 68
    const/4 v0, 0x1

    .line 69
    iput-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->m0:Z

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/b;

    .line 72
    .line 73
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/b;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 74
    .line 75
    .line 76
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->n0:Lkotlin/j;

    .line 81
    .line 82
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/c;

    .line 83
    .line 84
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/c;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 85
    .line 86
    .line 87
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->b1:Lkotlin/j;

    .line 92
    .line 93
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/d;

    .line 94
    .line 95
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/d;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 96
    .line 97
    .line 98
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->k1:Lkotlin/j;

    .line 103
    .line 104
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/e;

    .line 105
    .line 106
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/e;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 107
    .line 108
    .line 109
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->v1:Lkotlin/j;

    .line 114
    .line 115
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)I
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->T2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)I

    move-result p0

    return p0
.end method

.method public static synthetic B2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->S2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->U2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/recyclerview/widget/LinearLayoutManager;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->o0:LNy0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/b;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->K2()LNy0/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic F2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;I)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->L2(I)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic G2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->getLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->H1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic I2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LGq0/t;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic J2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->Q2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final N2()Landroidx/viewpager2/widget/ViewPager2$i;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/viewpager2/widget/ViewPager2$i;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final S2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)LNy0/b;
    .locals 2

    .line 1
    new-instance v0, LNy0/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$groupsAdapter$2$1;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->Q2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$groupsAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-direct {v0, v1}, LNy0/b;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final T2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)I
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    sget v0, Lpb/f;->space_8:I

    .line 6
    .line 7
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    return p0
.end method

.method public static final U2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 2

    .line 1
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-direct {v0, p0, v1, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final V2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final W2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->R2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final getLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->V2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->W2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final K2()LNy0/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LNy0/b;

    .line 8
    .line 9
    return-object v0
.end method

.method public final L2(I)I
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->getLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getWidth()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    div-int/lit8 v0, v0, 0x2

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->getLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1, p1}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getChildAt(I)Landroid/view/View;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    if-eqz p1, :cond_0

    .line 20
    .line 21
    invoke-virtual {p1}, Landroid/view/View;->getWidth()I

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    div-int/lit8 p1, p1, 0x2

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    const/4 p1, 0x0

    .line 29
    :goto_0
    sub-int/2addr v0, p1

    .line 30
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->M2()I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    sub-int/2addr v0, p1

    .line 35
    return v0
.end method

.method public final M2()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Number;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    return v0
.end method

.method public final O2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->l0:LeX0/h;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/h;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Landroid/os/Parcelable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 13
    .line 14
    return-object v0
.end method

.method public final P2()LGq0/t;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->k0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LGq0/t;

    .line 13
    .line 14
    return-object v0
.end method

.method public final Q2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final R2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LGq0/t;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 12
    .line 13
    .line 14
    iput-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->o0:LNy0/a;

    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, LGq0/t;->c:Landroidx/viewpager2/widget/ViewPager2;

    .line 21
    .line 22
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->N2()Landroidx/viewpager2/widget/ViewPager2$i;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->o(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->m0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 8

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LGq0/t;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->K2()LNy0/b;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    iget-object p1, p1, LGq0/t;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 22
    .line 23
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->getLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object p1, p1, LGq0/t;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 35
    .line 36
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->M2()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    const/4 v4, 0x6

    .line 43
    const/4 v5, 0x0

    .line 44
    const/4 v2, 0x0

    .line 45
    const/4 v3, 0x0

    .line 46
    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iget-object p1, p1, LGq0/t;->c:Landroidx/viewpager2/widget/ViewPager2;

    .line 57
    .line 58
    invoke-virtual {p1}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    if-nez p1, :cond_0

    .line 63
    .line 64
    new-instance v0, LNy0/a;

    .line 65
    .line 66
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 75
    .line 76
    .line 77
    move-result-object v3

    .line 78
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->O2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->G()I

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->O2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b()J

    .line 91
    .line 92
    .line 93
    move-result-wide v5

    .line 94
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->O2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a()I

    .line 99
    .line 100
    .line 101
    move-result v7

    .line 102
    invoke-direct/range {v0 .. v7}, LNy0/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;IJI)V

    .line 103
    .line 104
    .line 105
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->o0:LNy0/a;

    .line 106
    .line 107
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    iget-object p1, p1, LGq0/t;->c:Landroidx/viewpager2/widget/ViewPager2;

    .line 112
    .line 113
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->o0:LNy0/a;

    .line 114
    .line 115
    invoke-virtual {p1, v0}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 116
    .line 117
    .line 118
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->P2()LGq0/t;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    iget-object p1, p1, LGq0/t;->c:Landroidx/viewpager2/widget/ViewPager2;

    .line 123
    .line 124
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->N2()Landroidx/viewpager2/widget/ViewPager2$i;

    .line 125
    .line 126
    .line 127
    move-result-object v0

    .line 128
    invoke-virtual {p1, v0}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 129
    .line 130
    .line 131
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LAy0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LAy0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LAy0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    sget-object v0, Lyy0/c;->a:Lyy0/c;

    .line 56
    .line 57
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->O2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->G()I

    .line 62
    .line 63
    .line 64
    move-result v1

    .line 65
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    invoke-virtual {v3}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v3

    .line 73
    invoke-virtual {v0, v1, v3}, Lyy0/c;->c(ILjava/lang/String;)Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    invoke-virtual {v3}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    invoke-virtual {v0, v1, v3}, Lyy0/c;->d(Ljava/lang/String;Landroid/app/Application;)Lyy0/d;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {v2, v0}, LAy0/d;->a(Lyy0/d;)LAy0/c;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    invoke-interface {v0, p0}, LAy0/c;->a(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;)V

    .line 94
    .line 95
    .line 96
    return-void

    .line 97
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 98
    .line 99
    new-instance v2, Ljava/lang/StringBuilder;

    .line 100
    .line 101
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 102
    .line 103
    .line 104
    const-string v3, "Cannot create dependency "

    .line 105
    .line 106
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 107
    .line 108
    .line 109
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 110
    .line 111
    .line 112
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 121
    .line 122
    .line 123
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->Q2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;

    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    invoke-direct {v5, p0, v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 19
    .line 20
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 29
    .line 30
    const/4 v6, 0x0

    .line 31
    invoke-direct/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v10, 0x3

    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v7, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    move-object v6, v0

    .line 39
    move-object v9, v1

    .line 40
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    return-void
.end method
