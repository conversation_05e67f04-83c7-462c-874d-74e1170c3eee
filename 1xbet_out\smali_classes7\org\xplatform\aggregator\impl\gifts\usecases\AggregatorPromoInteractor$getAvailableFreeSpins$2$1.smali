.class final Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.usecases.AggregatorPromoInteractor$getAvailableFreeSpins$2$1"
    f = "AggregatorPromoInteractor.kt"
    l = {
        0x1d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->g(JILkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "Lxa1/c;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "authToken",
        "",
        "Lxa1/c;",
        "<anonymous>",
        "(Ljava/lang/String;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $accountId:J

.field final synthetic $countryId:I

.field final synthetic $this_runWithRetry:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

.field synthetic L$0:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;JILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
            "JI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$this_runWithRetry:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$accountId:J

    iput p4, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$countryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$this_runWithRetry:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$accountId:J

    iget v4, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$countryId:I

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;JILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    move-object v4, p1

    .line 34
    check-cast v4, Ljava/lang/String;

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$this_runWithRetry:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 37
    .line 38
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->c(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;)Lya1/a;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$accountId:J

    .line 43
    .line 44
    iget v7, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->$countryId:I

    .line 45
    .line 46
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->L$0:Ljava/lang/Object;

    .line 47
    .line 48
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor$getAvailableFreeSpins$2$1;->label:I

    .line 49
    .line 50
    move-object v8, p0

    .line 51
    invoke-interface/range {v3 .. v8}, Lya1/a;->j(Ljava/lang/String;JILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    if-ne v1, v0, :cond_2

    .line 56
    .line 57
    return-object v0

    .line 58
    :cond_2
    move-object v0, p1

    .line 59
    move-object p1, v1

    .line 60
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 61
    .line 62
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->b(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;Ljava/util/List;)Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    return-object p1
.end method
