.class public final LIN0/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a\'\u0010\u0005\u001a\u00020\u00012\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0003H\u0007\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lkotlin/Function0;",
        "",
        "onBackClick",
        "Landroidx/compose/ui/l;",
        "modifier",
        "e",
        "(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LIN0/h;->i(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LIN0/h;->h(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LIN0/h;->g(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/content/Context;)Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
    .locals 0

    .line 1
    invoke-static {p0}, LIN0/h;->f(Landroid/content/Context;)Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 7
    .param p0    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/ui/l;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    const v0, 0x7aefe35d

    .line 2
    .line 3
    .line 4
    invoke-interface {p2, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v4

    .line 8
    and-int/lit8 p2, p4, 0x1

    .line 9
    .line 10
    const/4 v1, 0x4

    .line 11
    if-eqz p2, :cond_0

    .line 12
    .line 13
    or-int/lit8 p2, p3, 0x6

    .line 14
    .line 15
    goto :goto_1

    .line 16
    :cond_0
    and-int/lit8 p2, p3, 0x6

    .line 17
    .line 18
    if-nez p2, :cond_2

    .line 19
    .line 20
    invoke-interface {v4, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result p2

    .line 24
    if-eqz p2, :cond_1

    .line 25
    .line 26
    const/4 p2, 0x4

    .line 27
    goto :goto_0

    .line 28
    :cond_1
    const/4 p2, 0x2

    .line 29
    :goto_0
    or-int/2addr p2, p3

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move p2, p3

    .line 32
    :goto_1
    and-int/lit8 v2, p4, 0x2

    .line 33
    .line 34
    if-eqz v2, :cond_3

    .line 35
    .line 36
    or-int/lit8 p2, p2, 0x30

    .line 37
    .line 38
    goto :goto_3

    .line 39
    :cond_3
    and-int/lit8 v3, p3, 0x30

    .line 40
    .line 41
    if-nez v3, :cond_5

    .line 42
    .line 43
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v3

    .line 47
    if-eqz v3, :cond_4

    .line 48
    .line 49
    const/16 v3, 0x20

    .line 50
    .line 51
    goto :goto_2

    .line 52
    :cond_4
    const/16 v3, 0x10

    .line 53
    .line 54
    :goto_2
    or-int/2addr p2, v3

    .line 55
    :cond_5
    :goto_3
    and-int/lit8 v3, p2, 0x13

    .line 56
    .line 57
    const/16 v5, 0x12

    .line 58
    .line 59
    if-ne v3, v5, :cond_7

    .line 60
    .line 61
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 62
    .line 63
    .line 64
    move-result v3

    .line 65
    if-nez v3, :cond_6

    .line 66
    .line 67
    goto :goto_4

    .line 68
    :cond_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 69
    .line 70
    .line 71
    goto :goto_6

    .line 72
    :cond_7
    :goto_4
    if-eqz v2, :cond_8

    .line 73
    .line 74
    sget-object p1, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 75
    .line 76
    :cond_8
    move-object v2, p1

    .line 77
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    if-eqz p1, :cond_9

    .line 82
    .line 83
    const/4 p1, -0x1

    .line 84
    const-string v3, "org.xbet.statistic.statistic_core.presentation.composable.DsNavigatorBar (DsNavigatorBar.kt:14)"

    .line 85
    .line 86
    invoke-static {v0, p2, p1, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 87
    .line 88
    .line 89
    :cond_9
    const p1, 0x6e3c21fe

    .line 90
    .line 91
    .line 92
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->t(I)V

    .line 93
    .line 94
    .line 95
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    sget-object v0, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 100
    .line 101
    invoke-virtual {v0}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    if-ne p1, v3, :cond_a

    .line 106
    .line 107
    new-instance p1, LIN0/d;

    .line 108
    .line 109
    invoke-direct {p1}, LIN0/d;-><init>()V

    .line 110
    .line 111
    .line 112
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 113
    .line 114
    .line 115
    :cond_a
    check-cast p1, Lkotlin/jvm/functions/Function1;

    .line 116
    .line 117
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 118
    .line 119
    .line 120
    const v3, 0x4c5de2

    .line 121
    .line 122
    .line 123
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 124
    .line 125
    .line 126
    and-int/lit8 v3, p2, 0xe

    .line 127
    .line 128
    if-ne v3, v1, :cond_b

    .line 129
    .line 130
    const/4 v1, 0x1

    .line 131
    goto :goto_5

    .line 132
    :cond_b
    const/4 v1, 0x0

    .line 133
    :goto_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 134
    .line 135
    .line 136
    move-result-object v3

    .line 137
    if-nez v1, :cond_c

    .line 138
    .line 139
    invoke-virtual {v0}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    if-ne v3, v0, :cond_d

    .line 144
    .line 145
    :cond_c
    new-instance v3, LIN0/e;

    .line 146
    .line 147
    invoke-direct {v3, p0}, LIN0/e;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 148
    .line 149
    .line 150
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 151
    .line 152
    .line 153
    :cond_d
    check-cast v3, Lkotlin/jvm/functions/Function1;

    .line 154
    .line 155
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 156
    .line 157
    .line 158
    and-int/lit8 p2, p2, 0x70

    .line 159
    .line 160
    or-int/lit8 v5, p2, 0x6

    .line 161
    .line 162
    const/4 v6, 0x0

    .line 163
    move-object v1, p1

    .line 164
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 165
    .line 166
    .line 167
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 168
    .line 169
    .line 170
    move-result p1

    .line 171
    if-eqz p1, :cond_e

    .line 172
    .line 173
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 174
    .line 175
    .line 176
    :cond_e
    move-object p1, v2

    .line 177
    :goto_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 178
    .line 179
    .line 180
    move-result-object p2

    .line 181
    if-eqz p2, :cond_f

    .line 182
    .line 183
    new-instance v0, LIN0/f;

    .line 184
    .line 185
    invoke-direct {v0, p0, p1, p3, p4}, LIN0/f;-><init>(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;II)V

    .line 186
    .line 187
    .line 188
    invoke-interface {p2, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 189
    .line 190
    .line 191
    :cond_f
    return-void
.end method

.method public static final f(Landroid/content/Context;)Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;
    .locals 5

    .line 1
    new-instance v0, Landroid/view/ContextThemeWrapper;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widgets_StaticNavigationBar_Title:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 6
    .line 7
    .line 8
    new-instance v1, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x2

    .line 12
    invoke-direct {v1, v0, v2, v3, v2}, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    sget v4, Lpb/k;->statistic:I

    .line 20
    .line 21
    invoke-virtual {v0, v4}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {v1, v0}, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;->setTitle(Ljava/lang/CharSequence;)V

    .line 26
    .line 27
    .line 28
    sget v0, LlZ0/d;->uikitStaticWhite:I

    .line 29
    .line 30
    invoke-static {p0, v0, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    invoke-virtual {v1, v0}, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;->setTitleColor(I)V

    .line 35
    .line 36
    .line 37
    sget v0, LlZ0/d;->uikitStaticWhite:I

    .line 38
    .line 39
    invoke-static {p0, v0, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 40
    .line 41
    .line 42
    move-result p0

    .line 43
    invoke-static {p0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    invoke-virtual {v1, p0}, Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;->setBackIconColor(Landroid/content/res/ColorStateList;)V

    .line 48
    .line 49
    .line 50
    return-object v1
.end method

.method public static final g(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;)Lkotlin/Unit;
    .locals 3

    .line 1
    new-instance v0, LIN0/g;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LIN0/g;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    const/4 p0, 0x1

    .line 7
    const/4 v1, 0x0

    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-static {p1, v2, v0, p0, v1}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LIN0/h;->e(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method
