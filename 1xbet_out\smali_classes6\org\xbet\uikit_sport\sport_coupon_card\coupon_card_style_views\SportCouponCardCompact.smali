.class public final Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LU31/a;
.implements LU31/i;
.implements LU31/h;
.implements LU31/j;
.implements LU31/e;
.implements LU31/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ca\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u001f\n\u0002\u0018\u0002\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u00a9\u00012\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u00042\u00020\u00052\u00020\u00062\u00020\u00072\u00020\u0008:\u0001bB\'\u0008\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\n\u0008\u0002\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u0012\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0015\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u0019\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\u001f\u0010\u001e\u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001f\u0010 \u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u001f\u0010!\u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008!\u0010\u001fJ\u000f\u0010\"\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0016J\u0011\u0010$\u001a\u0004\u0018\u00010#H\u0016\u00a2\u0006\u0004\u0008$\u0010%J\u001f\u0010(\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\r2\u0006\u0010\'\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008(\u0010)J7\u00100\u001a\u00020\u00122\u0006\u0010+\u001a\u00020*2\u0006\u0010,\u001a\u00020\r2\u0006\u0010-\u001a\u00020\r2\u0006\u0010.\u001a\u00020\r2\u0006\u0010/\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u00080\u00101J\u0017\u00102\u001a\u00020\u00122\u0006\u0010\u001b\u001a\u00020\u001aH\u0014\u00a2\u0006\u0004\u00082\u00103J\u0017\u00106\u001a\u00020\u00122\u0006\u00105\u001a\u000204H\u0016\u00a2\u0006\u0004\u00086\u00107J%\u0010;\u001a\u00020\u00122\u0014\u0010:\u001a\u0010\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020\u0012\u0018\u000108H\u0016\u00a2\u0006\u0004\u0008;\u0010<J%\u0010=\u001a\u00020\u00122\u0014\u0010:\u001a\u0010\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020\u0012\u0018\u000108H\u0016\u00a2\u0006\u0004\u0008=\u0010<J\u0017\u0010?\u001a\u00020\u00122\u0006\u0010>\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008?\u0010\u0014J\u0017\u0010B\u001a\u00020\u00122\u0006\u0010A\u001a\u00020@H\u0016\u00a2\u0006\u0004\u0008B\u0010CJ\u0019\u0010F\u001a\u00020\u00122\u0008\u0010E\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008F\u0010GJ\u0019\u0010I\u001a\u00020\u00122\u0008\u0010H\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008I\u0010GJ\u0019\u0010K\u001a\u00020\u00122\u0008\u0010J\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008K\u0010GJ!\u0010O\u001a\u00020\u00122\u0008\u0010L\u001a\u0004\u0018\u00010D2\u0006\u0010N\u001a\u00020MH\u0016\u00a2\u0006\u0004\u0008O\u0010PJ\u0019\u0010R\u001a\u00020\u00122\u0008\u0010Q\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008R\u0010GJ\u0019\u0010T\u001a\u00020\u00122\u0008\u0008\u0001\u0010S\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008T\u0010\u0014J\u0019\u0010V\u001a\u00020\u00122\u0008\u0010U\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008V\u0010GJ\u0019\u0010X\u001a\u00020\u00122\u0008\u0008\u0001\u0010W\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008X\u0010\u0014J\u0017\u0010Z\u001a\u00020\u00122\u0006\u0010Y\u001a\u00020*H\u0016\u00a2\u0006\u0004\u0008Z\u0010[J\u000f\u0010\\\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\\\u0010\u0018J\u0019\u0010^\u001a\u00020\u00122\u0008\u0010]\u001a\u0004\u0018\u00010DH\u0016\u00a2\u0006\u0004\u0008^\u0010GJ\u001b\u0010`\u001a\u00020\u00122\n\u0008\u0001\u0010_\u001a\u0004\u0018\u00010\rH\u0016\u00a2\u0006\u0004\u0008`\u0010aJ\u000f\u0010b\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008b\u0010\u0018J\u0017\u0010I\u001a\u00020\u00122\u0008\u0008\u0001\u0010c\u001a\u00020\r\u00a2\u0006\u0004\u0008I\u0010\u0014J\u0017\u0010F\u001a\u00020\u00122\u0008\u0008\u0001\u0010E\u001a\u00020\r\u00a2\u0006\u0004\u0008F\u0010\u0014J\u0017\u0010e\u001a\u00020\u00122\u0008\u0010d\u001a\u0004\u0018\u00010D\u00a2\u0006\u0004\u0008e\u0010GJ\u0017\u0010O\u001a\u00020\u00122\u0008\u0010L\u001a\u0004\u0018\u00010D\u00a2\u0006\u0004\u0008O\u0010GJ\u0015\u0010f\u001a\u00020\u00122\u0006\u0010N\u001a\u00020M\u00a2\u0006\u0004\u0008f\u0010gJ\u0017\u0010V\u001a\u00020\u00122\u0008\u0008\u0001\u0010h\u001a\u00020\r\u00a2\u0006\u0004\u0008V\u0010\u0014J\u0015\u0010X\u001a\u00020\u00122\u0006\u0010W\u001a\u00020#\u00a2\u0006\u0004\u0008X\u0010iJ\u0017\u0010^\u001a\u00020\u00122\u0008\u0008\u0001\u0010]\u001a\u00020\r\u00a2\u0006\u0004\u0008^\u0010\u0014J!\u0010k\u001a\u00020\u00122\u0008\u0010E\u001a\u0004\u0018\u00010D2\u0008\u0010j\u001a\u0004\u0018\u00010D\u00a2\u0006\u0004\u0008k\u0010lR\u001b\u0010q\u001a\u00020m8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008b\u0010n\u001a\u0004\u0008o\u0010pR\u0014\u0010s\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010rR\u0014\u0010u\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008t\u0010rR\u0014\u0010w\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008v\u0010rR\u0014\u0010y\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010rR\u0014\u0010|\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u0014\u0010~\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010{R\u0015\u0010\u0080\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u007f\u0010rR\u0015\u0010\u0081\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010rR\u0015\u0010\u0082\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010rR\u0015\u0010\u0083\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010rR\u0015\u0010\u0084\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010rR\u0016\u0010\u0086\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0085\u0001\u0010rR\u0017\u0010\u0089\u0001\u001a\u00030\u0087\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0019\u0010\u0088\u0001R\u001f\u0010\u008c\u0001\u001a\u00030\u0087\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0017\u0010n\u001a\u0006\u0008\u008a\u0001\u0010\u008b\u0001R\u0017\u0010\u008d\u0001\u001a\u00030\u0087\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008B\u0010\u0088\u0001R\u001f\u0010\u008f\u0001\u001a\u00030\u0087\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0015\u0010n\u001a\u0006\u0008\u008e\u0001\u0010\u008b\u0001R\u001f\u0010\u0093\u0001\u001a\u00030\u0090\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0013\u0010n\u001a\u0006\u0008\u0091\u0001\u0010\u0092\u0001R \u0010\u0098\u0001\u001a\u00030\u0094\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u0095\u0001\u0010n\u001a\u0006\u0008\u0096\u0001\u0010\u0097\u0001R\u0018\u0010\u009c\u0001\u001a\u00030\u0099\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u0018\u0010\u00a0\u0001\u001a\u00030\u009d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0018\u0010\u00a4\u0001\u001a\u00030\u00a1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001R\u001c\u0010\u00a8\u0001\u001a\u0005\u0018\u00010\u00a5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0001\u0010\u00a7\u0001\u00a8\u0006\u00aa\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;",
        "Landroid/widget/FrameLayout;",
        "LU31/a;",
        "LU31/i;",
        "LU31/h;",
        "LU31/j;",
        "LU31/e;",
        "",
        "LU31/b;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "width",
        "",
        "r",
        "(I)V",
        "q",
        "()I",
        "o",
        "()V",
        "n",
        "Landroid/graphics/Canvas;",
        "canvas",
        "",
        "topOffset",
        "k",
        "(Landroid/graphics/Canvas;F)F",
        "l",
        "j",
        "getStartSpaceDependsByIndicator",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/c;",
        "couponCardUiModel",
        "setModel",
        "(LX31/c;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "res",
        "setSportImage",
        "",
        "url",
        "p",
        "(Ljava/lang/String;)V",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "subTitle",
        "setSubTitle",
        "description",
        "setMarketDescription",
        "coef",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "textStyle",
        "setCouponBonusTitleStyle",
        "tagText",
        "setTagText",
        "tagColor",
        "setTagColor",
        "live",
        "b",
        "(Z)V",
        "i",
        "error",
        "setError",
        "marketStyle",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "a",
        "subtitle",
        "header",
        "setHeader",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "tag",
        "(Landroid/content/res/ColorStateList;)V",
        "coefficient",
        "setMarket",
        "(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V",
        "Lorg/xbet/uikit/utils/e;",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "I",
        "indicatorSize",
        "c",
        "sportIconSize",
        "d",
        "buttonWidth",
        "e",
        "shimmerHeight",
        "f",
        "F",
        "shrinkCoefTextSize",
        "g",
        "usualCoefTextSize",
        "h",
        "space4",
        "space6",
        "space8",
        "space10",
        "space12",
        "m",
        "space30",
        "LW31/h;",
        "LW31/h;",
        "titleDelegate",
        "getSubTitleDelegate",
        "()LW31/h;",
        "subTitleDelegate",
        "headerDelegate",
        "getErrorDelegate",
        "errorDelegate",
        "LW31/f;",
        "getTagDelegate",
        "()LW31/f;",
        "tagDelegate",
        "LW31/e;",
        "s",
        "getShimmerDelegate",
        "()LW31/e;",
        "shimmerDelegate",
        "LW31/i;",
        "t",
        "LW31/i;",
        "buttonsDelegate",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "u",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "marketView",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "v",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "sportImageView",
        "Landroid/graphics/Paint;",
        "w",
        "Landroid/graphics/Paint;",
        "strokePaint",
        "x",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final x:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final y:I


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:F

.field public final g:F

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:LW31/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:LW31/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:LW31/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Lorg/xbet/uikit/components/views/LoadableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public w:Landroid/graphics/Paint;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->x:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->y:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 12
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move v7, p3

    .line 5
    invoke-direct/range {p0 .. p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LV31/t;

    invoke-direct {v0, p0}, LV31/t;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->a:Lkotlin/j;

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->b:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_14:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_40:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->d:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_100:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->e:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->text_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v8

    iput v8, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->f:F

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->text_14:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v9

    iput v9, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->g:F

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_6:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v10

    iput v10, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->i:I

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->j:I

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_10:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v11

    iput v11, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->k:I

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_30:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 19
    new-instance v0, LW31/h;

    .line 20
    sget v4, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    const/4 v5, 0x4

    const/4 v6, 0x0

    const/4 v2, 0x3

    const/4 v3, 0x0

    move-object v1, p0

    .line 21
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 22
    new-instance v0, LV31/u;

    invoke-direct {v0, p0}, LV31/u;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->o:Lkotlin/j;

    .line 23
    new-instance v0, LW31/h;

    .line 24
    sget v4, LlZ0/n;->TextStyle_Caption_Medium_L_Secondary:I

    const/4 v2, 0x1

    .line 25
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 26
    new-instance v0, LV31/v;

    invoke-direct {v0, p0}, LV31/v;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->q:Lkotlin/j;

    .line 27
    new-instance v0, LV31/w;

    invoke-direct {v0, p0}, LV31/w;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->r:Lkotlin/j;

    .line 28
    new-instance v0, LV31/x;

    invoke-direct {v0, p0}, LV31/x;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->s:Lkotlin/j;

    .line 29
    new-instance v0, LW31/i;

    .line 30
    sget v2, LlZ0/h;->ic_glyph_move_vertical_large:I

    .line 31
    sget v3, LlZ0/h;->ic_glyph_trash:I

    .line 32
    sget v4, LlZ0/g;->size_40:I

    .line 33
    sget v5, LlZ0/g;->size_32:I

    .line 34
    invoke-direct/range {v0 .. v5}, LW31/i;-><init>(Landroid/view/ViewGroup;IIII)V

    .line 35
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 36
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {v1, v11, v11, v11, v10}, Landroid/view/View;->setPadding(IIII)V

    .line 37
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 38
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 39
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketBlockedIconPosition(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V

    .line 41
    sget v1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionTextStyle(I)V

    .line 42
    sget v1, LlZ0/n;->TextStyle_Text_Bold_TextPrimary:I

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientTextStyle(I)V

    const v1, 0x7fffffff

    .line 43
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionMaxLines(I)V

    .line 44
    invoke-virtual {v0, v9}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMaxTextSize(F)V

    .line 45
    invoke-virtual {v0, v8}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMinTextSize(F)V

    .line 46
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 47
    new-instance v0, Lorg/xbet/uikit/components/views/LoadableImageView;

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/views/LoadableImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 48
    sget-object v2, Lm31/g;->SportCouponCardView:[I

    .line 49
    invoke-virtual {p1, p2, v2, p3, v3}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v2

    .line 50
    sget v4, Lm31/g;->SportCouponCardView_showSkeleton:I

    invoke-virtual {v2, v4, v3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v4

    if-eqz v4, :cond_0

    .line 51
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->a()V

    .line 52
    :cond_0
    sget v4, Lm31/g;->SportCouponCardView_title:I

    invoke-virtual {v2, v4}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTitle(Ljava/lang/CharSequence;)V

    .line 53
    sget v4, Lm31/g;->SportCouponCardView_subtitle:I

    invoke-virtual {v2, v4}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 54
    sget v4, Lm31/g;->SportCouponCardView_tag:I

    invoke-virtual {v2, v4}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {p0, v4}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTagText(Ljava/lang/CharSequence;)V

    .line 55
    sget v4, Lm31/g;->SportCouponCardView_tagColor:I

    invoke-static {v2, p1, v4}, Lorg/xbet/uikit/utils/I;->c(Landroid/content/res/TypedArray;Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    move-result-object v1

    if-eqz v1, :cond_1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTagColor(Landroid/content/res/ColorStateList;)V

    .line 56
    :cond_1
    sget v1, Lm31/g;->SportCouponCardView_error:I

    invoke-virtual {v2, v1}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setError(Ljava/lang/CharSequence;)V

    .line 57
    sget v1, Lm31/g;->SportCouponCardView_couponMarketStyle:I

    invoke-virtual {v2, v1, v3}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarketStyle(Ljava/lang/Integer;)V

    .line 58
    sget v1, Lm31/g;->SportCouponCardView_marketTitle:I

    invoke-virtual {v2, v1}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v1

    .line 59
    sget v3, Lm31/g;->SportCouponCardView_marketCoefficient:I

    invoke-virtual {v2, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    .line 60
    invoke-virtual {p0, v1, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 61
    invoke-virtual {v2}, Landroid/content/res/TypedArray;->recycle()V

    .line 62
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 63
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->s(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getErrorDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->q:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getShimmerDelegate()LW31/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->s:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getStartSpaceDependsByIndicator()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->w:Landroid/graphics/Paint;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 6
    .line 7
    return v0

    .line 8
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 9
    .line 10
    return v0
.end method

.method private final getSubTitleDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->o:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTagDelegate()LW31/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->r:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Warning:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const v2, 0x7fffffff

    .line 8
    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    move-object v1, p0

    .line 12
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final n()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, LW31/h;->c()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    div-int/lit8 v0, v0, 0x2

    .line 18
    .line 19
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 20
    .line 21
    add-int/2addr v0, v3

    .line 22
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 23
    .line 24
    div-int/lit8 v2, v1, 0x2

    .line 25
    .line 26
    sub-int v4, v0, v2

    .line 27
    .line 28
    div-int/lit8 v2, v1, 0x2

    .line 29
    .line 30
    add-int v6, v0, v2

    .line 31
    .line 32
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 33
    .line 34
    add-int v5, v3, v1

    .line 35
    .line 36
    move-object v1, p0

    .line 37
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 38
    .line 39
    .line 40
    :cond_0
    return-void
.end method

.method private final o()V
    .locals 4

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LW31/h;->c()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    add-int/2addr v0, v1

    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->j:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getStartSpaceDependsByIndicator()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 24
    .line 25
    invoke-virtual {v1, v2, v3, v0}, LW31/f;->b(III)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method private final q()I
    .locals 8

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LW31/h;->c()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    const/4 v2, 0x0

    .line 28
    if-nez v1, :cond_0

    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 43
    .line 44
    add-int/2addr v1, v3

    .line 45
    goto :goto_0

    .line 46
    :cond_0
    const/4 v1, 0x0

    .line 47
    :goto_0
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 48
    .line 49
    invoke-virtual {v3}, LW31/h;->e()Z

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    if-nez v3, :cond_1

    .line 54
    .line 55
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 56
    .line 57
    invoke-virtual {v3}, LW31/h;->c()I

    .line 58
    .line 59
    .line 60
    move-result v3

    .line 61
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 62
    .line 63
    add-int/2addr v3, v4

    .line 64
    goto :goto_1

    .line 65
    :cond_1
    const/4 v3, 0x0

    .line 66
    :goto_1
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 67
    .line 68
    invoke-virtual {v4}, LW31/h;->e()Z

    .line 69
    .line 70
    .line 71
    move-result v4

    .line 72
    if-nez v4, :cond_2

    .line 73
    .line 74
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 75
    .line 76
    invoke-virtual {v4}, LW31/h;->c()I

    .line 77
    .line 78
    .line 79
    move-result v4

    .line 80
    goto :goto_2

    .line 81
    :cond_2
    const/4 v4, 0x0

    .line 82
    :goto_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 83
    .line 84
    .line 85
    move-result-object v5

    .line 86
    invoke-virtual {v5}, LW31/h;->e()Z

    .line 87
    .line 88
    .line 89
    move-result v5

    .line 90
    if-nez v5, :cond_3

    .line 91
    .line 92
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 93
    .line 94
    .line 95
    move-result-object v2

    .line 96
    invoke-virtual {v2}, LW31/h;->c()I

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 101
    .line 102
    add-int/2addr v2, v5

    .line 103
    :cond_3
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 104
    .line 105
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 106
    .line 107
    .line 108
    move-result v5

    .line 109
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 110
    .line 111
    .line 112
    move-result-object v6

    .line 113
    invoke-virtual {v6}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 114
    .line 115
    .line 116
    move-result-object v6

    .line 117
    invoke-virtual {v6}, Landroid/view/View;->getVisibility()I

    .line 118
    .line 119
    .line 120
    move-result v6

    .line 121
    if-nez v6, :cond_4

    .line 122
    .line 123
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->e:I

    .line 124
    .line 125
    goto :goto_3

    .line 126
    :cond_4
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 127
    .line 128
    mul-int/lit8 v6, v6, 0x2

    .line 129
    .line 130
    iget v7, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 131
    .line 132
    add-int/2addr v6, v7

    .line 133
    add-int/2addr v6, v0

    .line 134
    add-int/2addr v6, v1

    .line 135
    add-int/2addr v6, v3

    .line 136
    add-int/2addr v6, v4

    .line 137
    add-int/2addr v6, v2

    .line 138
    add-int v0, v6, v5

    .line 139
    .line 140
    :goto_3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    sget v2, LlZ0/g;->size_82:I

    .line 145
    .line 146
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 147
    .line 148
    .line 149
    move-result v1

    .line 150
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 151
    .line 152
    .line 153
    move-result v0

    .line 154
    const/high16 v1, 0x40000000    # 2.0f

    .line 155
    .line 156
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 157
    .line 158
    .line 159
    move-result v0

    .line 160
    return v0
.end method

.method private final r(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->d:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 17
    .line 18
    :goto_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 19
    .line 20
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-nez v1, :cond_1

    .line 25
    .line 26
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 27
    .line 28
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 29
    .line 30
    add-int/2addr v1, v2

    .line 31
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 32
    .line 33
    add-int/2addr v1, v2

    .line 34
    goto :goto_1

    .line 35
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 36
    .line 37
    :goto_1
    sub-int/2addr p1, v1

    .line 38
    sub-int/2addr p1, v0

    .line 39
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-virtual {v0, p1}, LW31/h;->f(I)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 47
    .line 48
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 49
    .line 50
    const/high16 v1, 0x40000000    # 2.0f

    .line 51
    .line 52
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 57
    .line 58
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    invoke-virtual {p1, v0, v1}, Landroid/view/View;->measure(II)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public static final s(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/e;
    .locals 2

    .line 1
    new-instance v0, LW31/e;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->e:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/e;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final t(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_TextPrimary:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    move-object v1, p0

    .line 10
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final u(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;)LW31/f;
    .locals 2

    .line 1
    new-instance v0, LW31/f;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widget_Tag_RectangularS_Red:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/f;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/e;->d()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public b(Z)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    sget p1, LlZ0/d;->uikitStaticRed:I

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    sget p1, LlZ0/d;->uikitSecondary:I

    .line 11
    .line 12
    :goto_0
    const/4 v1, 0x2

    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-static {v0, p1, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 19
    .line 20
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->w:Landroid/graphics/Paint;

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 28
    .line 29
    .line 30
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public i()V
    .locals 5

    .line 1
    new-instance v0, Landroid/graphics/Paint;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    sget v2, LlZ0/d;->uikitSecondary:I

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    const/4 v4, 0x2

    .line 14
    invoke-static {v1, v2, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setColor(I)V

    .line 19
    .line 20
    .line 21
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->b:I

    .line 22
    .line 23
    int-to-float v1, v1

    .line 24
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 25
    .line 26
    .line 27
    const/4 v1, 0x1

    .line 28
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 29
    .line 30
    .line 31
    sget-object v1, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 34
    .line 35
    .line 36
    sget-object v1, Landroid/graphics/Paint$Cap;->ROUND:Landroid/graphics/Paint$Cap;

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setStrokeCap(Landroid/graphics/Paint$Cap;)V

    .line 39
    .line 40
    .line 41
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->w:Landroid/graphics/Paint;

    .line 42
    .line 43
    return-void
.end method

.method public final j(Landroid/graphics/Canvas;F)F
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/h;->e()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getStartSpaceDependsByIndicator()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    int-to-float v1, v1

    .line 20
    invoke-virtual {v0, p1, v1, p2}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 21
    .line 22
    .line 23
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p1}, LW31/h;->c()I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    int-to-float p1, p1

    .line 32
    add-float/2addr p2, p1

    .line 33
    :cond_0
    return p2
.end method

.method public final k(Landroid/graphics/Canvas;F)F
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 28
    .line 29
    add-int/2addr v0, v1

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/4 v0, 0x0

    .line 32
    :goto_0
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 33
    .line 34
    add-int/2addr v0, v1

    .line 35
    int-to-float v0, v0

    .line 36
    add-float/2addr p2, v0

    .line 37
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 38
    .line 39
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 40
    .line 41
    int-to-float v1, v1

    .line 42
    invoke-virtual {v0, p1, v1, p2}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 43
    .line 44
    .line 45
    return p2
.end method

.method public final l(Landroid/graphics/Canvas;F)F
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 4
    .line 5
    int-to-float v1, v1

    .line 6
    invoke-virtual {v0, p1, v1, p2}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 7
    .line 8
    .line 9
    return p2
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 8
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 2
    .line 3
    int-to-float v1, v0

    .line 4
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->c:I

    .line 5
    .line 6
    add-int/2addr v0, v2

    .line 7
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 8
    .line 9
    add-int/2addr v0, v2

    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    int-to-float v0, v0

    .line 15
    invoke-virtual {v2, p1, v0, v1}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 16
    .line 17
    .line 18
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, LW31/h;->c()I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    int-to-float v0, v0

    .line 27
    add-float/2addr v1, v0

    .line 28
    invoke-virtual {p0, p1, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->k(Landroid/graphics/Canvas;F)F

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 33
    .line 34
    invoke-virtual {v1}, LW31/h;->c()I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    int-to-float v1, v1

    .line 39
    add-float/2addr v0, v1

    .line 40
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l(Landroid/graphics/Canvas;F)F

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 45
    .line 46
    invoke-virtual {v1}, LW31/h;->c()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    int-to-float v1, v1

    .line 51
    add-float/2addr v0, v1

    .line 52
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->j(Landroid/graphics/Canvas;F)F

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 57
    .line 58
    int-to-float v1, v1

    .line 59
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 60
    .line 61
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    div-int/lit8 v2, v2, 0x2

    .line 66
    .line 67
    int-to-float v2, v2

    .line 68
    add-float/2addr v1, v2

    .line 69
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 70
    .line 71
    .line 72
    move-result v2

    .line 73
    if-nez v2, :cond_0

    .line 74
    .line 75
    :goto_0
    move v3, v1

    .line 76
    goto :goto_1

    .line 77
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 78
    .line 79
    .line 80
    move-result v2

    .line 81
    int-to-float v2, v2

    .line 82
    sub-float v1, v2, v1

    .line 83
    .line 84
    goto :goto_0

    .line 85
    :goto_1
    iget-object v7, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->w:Landroid/graphics/Paint;

    .line 86
    .line 87
    if-eqz v7, :cond_1

    .line 88
    .line 89
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 90
    .line 91
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    invoke-virtual {v2}, LW31/h;->c()I

    .line 96
    .line 97
    .line 98
    move-result v2

    .line 99
    add-int/2addr v1, v2

    .line 100
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->k:I

    .line 101
    .line 102
    add-int/2addr v1, v2

    .line 103
    int-to-float v4, v1

    .line 104
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->h:I

    .line 105
    .line 106
    int-to-float v1, v1

    .line 107
    sub-float v6, v0, v1

    .line 108
    .line 109
    move v5, v3

    .line 110
    move-object v2, p1

    .line 111
    invoke-virtual/range {v2 .. v7}, Landroid/graphics/Canvas;->drawLine(FFFFLandroid/graphics/Paint;)V

    .line 112
    .line 113
    .line 114
    :cond_1
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, LW31/e;->b()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 28
    .line 29
    sub-int/2addr p1, p2

    .line 30
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 31
    .line 32
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 33
    .line 34
    .line 35
    move-result p2

    .line 36
    sub-int v3, p1, p2

    .line 37
    .line 38
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 39
    .line 40
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 41
    .line 42
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    add-int v4, v2, p1

    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 49
    .line 50
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 51
    .line 52
    .line 53
    move-result p1

    .line 54
    add-int v5, v3, p1

    .line 55
    .line 56
    move-object v0, p0

    .line 57
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 58
    .line 59
    .line 60
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->o()V

    .line 61
    .line 62
    .line 63
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n()V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 67
    .line 68
    .line 69
    move-result p1

    .line 70
    iget p2, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 71
    .line 72
    iget p3, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->j:I

    .line 73
    .line 74
    add-int/2addr p2, p3

    .line 75
    iget-object p3, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 76
    .line 77
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 78
    .line 79
    .line 80
    move-result p3

    .line 81
    add-int/2addr p2, p3

    .line 82
    div-int/lit8 p2, p2, 0x2

    .line 83
    .line 84
    sub-int/2addr p1, p2

    .line 85
    iget-object p2, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 86
    .line 87
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 88
    .line 89
    .line 90
    move-result-object p3

    .line 91
    invoke-virtual {p3}, LW31/h;->c()I

    .line 92
    .line 93
    .line 94
    move-result p3

    .line 95
    div-int/lit8 p3, p3, 0x2

    .line 96
    .line 97
    iget p4, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 98
    .line 99
    add-int/2addr p3, p4

    .line 100
    invoke-virtual {p2, p3, p1}, LW31/i;->l(II)V

    .line 101
    .line 102
    .line 103
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getShimmerDelegate()LW31/e;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p2, v0}, LW31/e;->c(I)V

    .line 24
    .line 25
    .line 26
    goto :goto_1

    .line 27
    :cond_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 28
    .line 29
    invoke-virtual {v1}, LW31/c;->e()Landroid/widget/ImageView;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-nez v1, :cond_1

    .line 38
    .line 39
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->d:I

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 43
    .line 44
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 45
    .line 46
    sub-int v2, v0, v2

    .line 47
    .line 48
    sub-int/2addr v2, v1

    .line 49
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 50
    .line 51
    const/high16 v3, 0x40000000    # 2.0f

    .line 52
    .line 53
    invoke-static {v2, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 54
    .line 55
    .line 56
    move-result v2

    .line 57
    invoke-virtual {v1, v2, p2}, Landroid/view/View;->measure(II)V

    .line 58
    .line 59
    .line 60
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getStartSpaceDependsByIndicator()I

    .line 61
    .line 62
    .line 63
    move-result p2

    .line 64
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    sub-int p2, v0, p2

    .line 69
    .line 70
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 71
    .line 72
    sub-int v2, p2, v2

    .line 73
    .line 74
    invoke-virtual {v1, v2}, LW31/f;->c(I)V

    .line 75
    .line 76
    .line 77
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    .line 78
    .line 79
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 80
    .line 81
    sub-int v2, v0, v2

    .line 82
    .line 83
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 84
    .line 85
    sub-int/2addr v2, v3

    .line 86
    invoke-virtual {v1, v2}, LW31/h;->f(I)V

    .line 87
    .line 88
    .line 89
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 90
    .line 91
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->m:I

    .line 92
    .line 93
    sub-int v2, v0, v2

    .line 94
    .line 95
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 96
    .line 97
    sub-int/2addr v2, v3

    .line 98
    invoke-virtual {v1, v2}, LW31/h;->f(I)V

    .line 99
    .line 100
    .line 101
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    .line 102
    .line 103
    .line 104
    move-result-object v1

    .line 105
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->l:I

    .line 106
    .line 107
    sub-int/2addr p2, v2

    .line 108
    invoke-virtual {v1, p2}, LW31/h;->f(I)V

    .line 109
    .line 110
    .line 111
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->r(I)V

    .line 112
    .line 113
    .line 114
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 115
    .line 116
    invoke-virtual {p2}, LW31/c;->g()V

    .line 117
    .line 118
    .line 119
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->q()I

    .line 120
    .line 121
    .line 122
    move-result p2

    .line 123
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 124
    .line 125
    .line 126
    return-void
.end method

.method public p(Ljava/lang/String;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    const/16 v5, 0xe

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    move-object v1, p1

    .line 10
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->X(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->h(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setCouponBonusTitle(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setCouponBonusTitleStyle(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LW31/h;->i(I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setError(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setError(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setError(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setHeader(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p:LW31/h;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final setMarketCoefficient(Ljava/lang/CharSequence;)V
    .locals 1

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 2
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMarketStyle(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->u:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketStyle(Ljava/lang/Integer;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setModel(LX31/c;)V
    .locals 2
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LX31/c;->j()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->p(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, LX31/c;->m()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTagText(Ljava/lang/CharSequence;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1}, LX31/c;->l()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTagColor(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, LX31/c;->o()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTitle(Ljava/lang/CharSequence;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/c;->k()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1}, LX31/c;->g()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setHeader(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p1}, LX31/c;->c()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setError(Ljava/lang/CharSequence;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p1}, LX31/c;->f()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {p1}, LX31/c;->e()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p1}, LX31/c;->h()I

    .line 62
    .line 63
    .line 64
    move-result v0

    .line 65
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarketStyle(Ljava/lang/Integer;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {p1}, LX31/c;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->i()V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p1}, LX31/c;->d()Z

    .line 83
    .line 84
    .line 85
    move-result p1

    .line 86
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->b(Z)V

    .line 87
    .line 88
    .line 89
    return-void
.end method

.method public setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->t:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->j(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setSportImage(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->v:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSubTitle(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setSubTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setSubTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getSubTitleDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setTagColor(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->d(I)V

    return-void
.end method

.method public final setTagColor(Landroid/content/res/ColorStateList;)V
    .locals 1
    .param p1    # Landroid/content/res/ColorStateList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->e(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public final setTagText(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->f(I)V

    return-void
.end method

.method public setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->g(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;->n:LW31/h;

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method
