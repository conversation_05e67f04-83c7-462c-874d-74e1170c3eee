.class public final LmF0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmF0/a$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LiF0/b;",
        "Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;",
        "teamPagerModel",
        "LnF0/a;",
        "a",
        "(LiF0/b;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)LnF0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LiF0/b;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;)LnF0/a;
    .locals 8
    .param p0    # LiF0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LmF0/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    const/high16 v1, 0x42c80000    # 100.0f

    .line 11
    .line 12
    if-eq p1, v0, :cond_1

    .line 13
    .line 14
    const/4 v0, 0x2

    .line 15
    if-ne p1, v0, :cond_0

    .line 16
    .line 17
    float-to-double v0, v1

    .line 18
    invoke-virtual {p0}, LiF0/b;->a()D

    .line 19
    .line 20
    .line 21
    move-result-wide v2

    .line 22
    sub-double v2, v0, v2

    .line 23
    .line 24
    div-double/2addr v2, v0

    .line 25
    invoke-virtual {p0}, LiF0/b;->b()D

    .line 26
    .line 27
    .line 28
    move-result-wide p0

    .line 29
    :goto_0
    div-double/2addr p0, v0

    .line 30
    goto :goto_1

    .line 31
    :cond_0
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 32
    .line 33
    const-string p1, "undefined team"

    .line 34
    .line 35
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    throw p0

    .line 39
    :cond_1
    invoke-virtual {p0}, LiF0/b;->a()D

    .line 40
    .line 41
    .line 42
    move-result-wide v2

    .line 43
    float-to-double v0, v1

    .line 44
    div-double/2addr v2, v0

    .line 45
    invoke-virtual {p0}, LiF0/b;->b()D

    .line 46
    .line 47
    .line 48
    move-result-wide p0

    .line 49
    sub-double p0, v0, p0

    .line 50
    .line 51
    goto :goto_0

    .line 52
    :goto_1
    const-string v0, "Value should be within a range 0.0 to 1.0"

    .line 53
    .line 54
    const-wide/16 v4, 0x0

    .line 55
    .line 56
    cmpg-double v1, v4, v2

    .line 57
    .line 58
    if-gtz v1, :cond_3

    .line 59
    .line 60
    const-wide/high16 v6, 0x3ff0000000000000L    # 1.0

    .line 61
    .line 62
    cmpg-double v1, v2, v6

    .line 63
    .line 64
    if-gtz v1, :cond_3

    .line 65
    .line 66
    cmpg-double v1, v4, p0

    .line 67
    .line 68
    if-gtz v1, :cond_2

    .line 69
    .line 70
    cmpg-double v1, p0, v6

    .line 71
    .line 72
    if-gtz v1, :cond_2

    .line 73
    .line 74
    new-instance v0, LnF0/a;

    .line 75
    .line 76
    double-to-float v1, v2

    .line 77
    double-to-float p0, p0

    .line 78
    const-wide/high16 v2, 0x4024000000000000L    # 10.0

    .line 79
    .line 80
    invoke-direct {v0, v1, p0, v2, v3}, LnF0/a;-><init>(FFD)V

    .line 81
    .line 82
    .line 83
    return-object v0

    .line 84
    :cond_2
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 85
    .line 86
    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 87
    .line 88
    .line 89
    throw p0

    .line 90
    :cond_3
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 91
    .line 92
    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    throw p0
.end method
