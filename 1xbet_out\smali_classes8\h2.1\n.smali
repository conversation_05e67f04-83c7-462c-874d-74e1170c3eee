.class public final Lh2/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/r;
.implements LN1/M;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lh2/n$a;
    }
.end annotation


# static fields
.field public static final G:LN1/x;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public A:[Lh2/n$a;

.field public B:[[J

.field public C:I

.field public D:J

.field public E:I

.field public F:Lc2/a;

.field public final a:Lk2/s$a;

.field public final b:I

.field public final c:Lt1/G;

.field public final d:Lt1/G;

.field public final e:Lt1/G;

.field public final f:Lt1/G;

.field public final g:Ljava/util/ArrayDeque;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayDeque<",
            "Landroidx/media3/container/d$b;",
            ">;"
        }
    .end annotation
.end field

.field public final h:Lh2/q;

.field public final i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroidx/media3/common/x$a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "LN1/Q;",
            ">;"
        }
    .end annotation
.end field

.field public k:I

.field public l:I

.field public m:J

.field public n:I

.field public o:Lt1/G;

.field public p:I

.field public q:I

.field public r:I

.field public s:I

.field public t:Z

.field public u:Z

.field public v:Z

.field public w:J

.field public x:Z

.field public y:J

.field public z:LN1/t;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lh2/m;

    .line 2
    .line 3
    invoke-direct {v0}, Lh2/m;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lh2/n;->G:LN1/x;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 2
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1
    sget-object v0, Lk2/s$a;->a:Lk2/s$a;

    const/16 v1, 0x10

    invoke-direct {p0, v0, v1}, Lh2/n;-><init>(Lk2/s$a;I)V

    return-void
.end method

.method public constructor <init>(Lk2/s$a;I)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lh2/n;->a:Lk2/s$a;

    .line 4
    iput p2, p0, Lh2/n;->b:I

    .line 5
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    iput-object p1, p0, Lh2/n;->j:Lcom/google/common/collect/ImmutableList;

    and-int/lit8 p1, p2, 0x4

    const/4 p2, 0x0

    if-eqz p1, :cond_0

    const/4 p1, 0x3

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 6
    :goto_0
    iput p1, p0, Lh2/n;->k:I

    .line 7
    new-instance p1, Lh2/q;

    invoke-direct {p1}, Lh2/q;-><init>()V

    iput-object p1, p0, Lh2/n;->h:Lh2/q;

    .line 8
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lh2/n;->i:Ljava/util/List;

    .line 9
    new-instance p1, Lt1/G;

    const/16 v0, 0x10

    invoke-direct {p1, v0}, Lt1/G;-><init>(I)V

    iput-object p1, p0, Lh2/n;->f:Lt1/G;

    .line 10
    new-instance p1, Ljava/util/ArrayDeque;

    invoke-direct {p1}, Ljava/util/ArrayDeque;-><init>()V

    iput-object p1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 11
    new-instance p1, Lt1/G;

    sget-object v0, Landroidx/media3/container/g;->a:[B

    invoke-direct {p1, v0}, Lt1/G;-><init>([B)V

    iput-object p1, p0, Lh2/n;->c:Lt1/G;

    .line 12
    new-instance p1, Lt1/G;

    const/4 v0, 0x6

    invoke-direct {p1, v0}, Lt1/G;-><init>(I)V

    iput-object p1, p0, Lh2/n;->d:Lt1/G;

    .line 13
    new-instance p1, Lt1/G;

    invoke-direct {p1}, Lt1/G;-><init>()V

    iput-object p1, p0, Lh2/n;->e:Lt1/G;

    const/4 p1, -0x1

    .line 14
    iput p1, p0, Lh2/n;->p:I

    .line 15
    sget-object p1, LN1/t;->q0:LN1/t;

    iput-object p1, p0, Lh2/n;->z:LN1/t;

    .line 16
    new-array p1, p2, [Lh2/n$a;

    iput-object p1, p0, Lh2/n;->A:[Lh2/n$a;

    return-void
.end method

.method public static D(Lt1/G;)I
    .locals 1

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lt1/G;->W(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    invoke-static {v0}, Lh2/n;->o(I)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    return v0

    .line 17
    :cond_0
    const/4 v0, 0x4

    .line 18
    invoke-virtual {p0, v0}, Lt1/G;->X(I)V

    .line 19
    .line 20
    .line 21
    :cond_1
    invoke-virtual {p0}, Lt1/G;->a()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-lez v0, :cond_2

    .line 26
    .line 27
    invoke-virtual {p0}, Lt1/G;->q()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    invoke-static {v0}, Lh2/n;->o(I)I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    return v0

    .line 38
    :cond_2
    const/4 p0, 0x0

    .line 39
    return p0
.end method

.method public static K(I)Z
    .locals 1

    .line 1
    const v0, 0x6d6f6f76

    .line 2
    .line 3
    .line 4
    if-eq p0, v0, :cond_1

    .line 5
    .line 6
    const v0, 0x7472616b

    .line 7
    .line 8
    .line 9
    if-eq p0, v0, :cond_1

    .line 10
    .line 11
    const v0, 0x6d646961

    .line 12
    .line 13
    .line 14
    if-eq p0, v0, :cond_1

    .line 15
    .line 16
    const v0, 0x6d696e66

    .line 17
    .line 18
    .line 19
    if-eq p0, v0, :cond_1

    .line 20
    .line 21
    const v0, 0x7374626c

    .line 22
    .line 23
    .line 24
    if-eq p0, v0, :cond_1

    .line 25
    .line 26
    const v0, 0x65647473

    .line 27
    .line 28
    .line 29
    if-eq p0, v0, :cond_1

    .line 30
    .line 31
    const v0, 0x6d657461

    .line 32
    .line 33
    .line 34
    if-eq p0, v0, :cond_1

    .line 35
    .line 36
    const v0, 0x61787465

    .line 37
    .line 38
    .line 39
    if-ne p0, v0, :cond_0

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 p0, 0x0

    .line 43
    return p0

    .line 44
    :cond_1
    :goto_0
    const/4 p0, 0x1

    .line 45
    return p0
.end method

.method public static L(I)Z
    .locals 1

    .line 1
    const v0, 0x6d646864

    .line 2
    .line 3
    .line 4
    if-eq p0, v0, :cond_1

    .line 5
    .line 6
    const v0, 0x6d766864

    .line 7
    .line 8
    .line 9
    if-eq p0, v0, :cond_1

    .line 10
    .line 11
    const v0, 0x68646c72

    .line 12
    .line 13
    .line 14
    if-eq p0, v0, :cond_1

    .line 15
    .line 16
    const v0, 0x73747364

    .line 17
    .line 18
    .line 19
    if-eq p0, v0, :cond_1

    .line 20
    .line 21
    const v0, 0x73747473

    .line 22
    .line 23
    .line 24
    if-eq p0, v0, :cond_1

    .line 25
    .line 26
    const v0, 0x73747373

    .line 27
    .line 28
    .line 29
    if-eq p0, v0, :cond_1

    .line 30
    .line 31
    const v0, 0x63747473

    .line 32
    .line 33
    .line 34
    if-eq p0, v0, :cond_1

    .line 35
    .line 36
    const v0, 0x656c7374

    .line 37
    .line 38
    .line 39
    if-eq p0, v0, :cond_1

    .line 40
    .line 41
    const v0, 0x73747363

    .line 42
    .line 43
    .line 44
    if-eq p0, v0, :cond_1

    .line 45
    .line 46
    const v0, 0x7374737a

    .line 47
    .line 48
    .line 49
    if-eq p0, v0, :cond_1

    .line 50
    .line 51
    const v0, 0x73747a32

    .line 52
    .line 53
    .line 54
    if-eq p0, v0, :cond_1

    .line 55
    .line 56
    const v0, 0x7374636f

    .line 57
    .line 58
    .line 59
    if-eq p0, v0, :cond_1

    .line 60
    .line 61
    const v0, 0x636f3634

    .line 62
    .line 63
    .line 64
    if-eq p0, v0, :cond_1

    .line 65
    .line 66
    const v0, 0x746b6864

    .line 67
    .line 68
    .line 69
    if-eq p0, v0, :cond_1

    .line 70
    .line 71
    const v0, 0x66747970

    .line 72
    .line 73
    .line 74
    if-eq p0, v0, :cond_1

    .line 75
    .line 76
    const v0, 0x75647461

    .line 77
    .line 78
    .line 79
    if-eq p0, v0, :cond_1

    .line 80
    .line 81
    const v0, 0x6b657973

    .line 82
    .line 83
    .line 84
    if-eq p0, v0, :cond_1

    .line 85
    .line 86
    const v0, 0x696c7374

    .line 87
    .line 88
    .line 89
    if-ne p0, v0, :cond_0

    .line 90
    .line 91
    goto :goto_0

    .line 92
    :cond_0
    const/4 p0, 0x0

    .line 93
    return p0

    .line 94
    :cond_1
    :goto_0
    const/4 p0, 0x1

    .line 95
    return p0
.end method

.method public static synthetic m(Lh2/t;)Lh2/t;
    .locals 0

    .line 1
    return-object p0
.end method

.method public static synthetic n()[LN1/r;
    .locals 3

    .line 1
    new-instance v0, Lh2/n;

    .line 2
    .line 3
    sget-object v1, Lk2/s$a;->a:Lk2/s$a;

    .line 4
    .line 5
    const/16 v2, 0x10

    .line 6
    .line 7
    invoke-direct {v0, v1, v2}, Lh2/n;-><init>(Lk2/s$a;I)V

    .line 8
    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    new-array v1, v1, [LN1/r;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    aput-object v0, v1, v2

    .line 15
    .line 16
    return-object v1
.end method

.method public static o(I)I
    .locals 1

    .line 1
    const v0, 0x68656963

    .line 2
    .line 3
    .line 4
    if-eq p0, v0, :cond_1

    .line 5
    .line 6
    const v0, 0x71742020

    .line 7
    .line 8
    .line 9
    if-eq p0, v0, :cond_0

    .line 10
    .line 11
    const/4 p0, 0x0

    .line 12
    return p0

    .line 13
    :cond_0
    const/4 p0, 0x1

    .line 14
    return p0

    .line 15
    :cond_1
    const/4 p0, 0x2

    .line 16
    return p0
.end method

.method public static p([Lh2/n$a;)[[J
    .locals 15

    .line 1
    array-length v0, p0

    .line 2
    new-array v0, v0, [[J

    .line 3
    .line 4
    array-length v1, p0

    .line 5
    new-array v1, v1, [I

    .line 6
    .line 7
    array-length v2, p0

    .line 8
    new-array v2, v2, [J

    .line 9
    .line 10
    array-length v3, p0

    .line 11
    new-array v3, v3, [Z

    .line 12
    .line 13
    const/4 v4, 0x0

    .line 14
    const/4 v5, 0x0

    .line 15
    :goto_0
    array-length v6, p0

    .line 16
    if-ge v5, v6, :cond_0

    .line 17
    .line 18
    aget-object v6, p0, v5

    .line 19
    .line 20
    iget-object v6, v6, Lh2/n$a;->b:Lh2/w;

    .line 21
    .line 22
    iget v6, v6, Lh2/w;->b:I

    .line 23
    .line 24
    new-array v6, v6, [J

    .line 25
    .line 26
    aput-object v6, v0, v5

    .line 27
    .line 28
    aget-object v6, p0, v5

    .line 29
    .line 30
    iget-object v6, v6, Lh2/n$a;->b:Lh2/w;

    .line 31
    .line 32
    iget-object v6, v6, Lh2/w;->f:[J

    .line 33
    .line 34
    aget-wide v7, v6, v4

    .line 35
    .line 36
    aput-wide v7, v2, v5

    .line 37
    .line 38
    add-int/lit8 v5, v5, 0x1

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_0
    const-wide/16 v5, 0x0

    .line 42
    .line 43
    const/4 v7, 0x0

    .line 44
    :goto_1
    array-length v8, p0

    .line 45
    if-ge v7, v8, :cond_4

    .line 46
    .line 47
    const-wide v8, 0x7fffffffffffffffL

    .line 48
    .line 49
    .line 50
    .line 51
    .line 52
    const/4 v10, -0x1

    .line 53
    const/4 v11, 0x0

    .line 54
    :goto_2
    array-length v12, p0

    .line 55
    if-ge v11, v12, :cond_2

    .line 56
    .line 57
    aget-boolean v12, v3, v11

    .line 58
    .line 59
    if-nez v12, :cond_1

    .line 60
    .line 61
    aget-wide v12, v2, v11

    .line 62
    .line 63
    cmp-long v14, v12, v8

    .line 64
    .line 65
    if-gtz v14, :cond_1

    .line 66
    .line 67
    move v10, v11

    .line 68
    move-wide v8, v12

    .line 69
    :cond_1
    add-int/lit8 v11, v11, 0x1

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_2
    aget v8, v1, v10

    .line 73
    .line 74
    aget-object v9, v0, v10

    .line 75
    .line 76
    aput-wide v5, v9, v8

    .line 77
    .line 78
    aget-object v11, p0, v10

    .line 79
    .line 80
    iget-object v11, v11, Lh2/n$a;->b:Lh2/w;

    .line 81
    .line 82
    iget-object v12, v11, Lh2/w;->d:[I

    .line 83
    .line 84
    aget v12, v12, v8

    .line 85
    .line 86
    int-to-long v12, v12

    .line 87
    add-long/2addr v5, v12

    .line 88
    const/4 v12, 0x1

    .line 89
    add-int/2addr v8, v12

    .line 90
    aput v8, v1, v10

    .line 91
    .line 92
    array-length v9, v9

    .line 93
    if-ge v8, v9, :cond_3

    .line 94
    .line 95
    iget-object v9, v11, Lh2/w;->f:[J

    .line 96
    .line 97
    aget-wide v8, v9, v8

    .line 98
    .line 99
    aput-wide v8, v2, v10

    .line 100
    .line 101
    goto :goto_1

    .line 102
    :cond_3
    aput-boolean v12, v3, v10

    .line 103
    .line 104
    add-int/lit8 v7, v7, 0x1

    .line 105
    .line 106
    goto :goto_1

    .line 107
    :cond_4
    return-object v0
.end method

.method public static r(I)I
    .locals 1

    .line 1
    and-int/lit8 v0, p0, 0x1

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/16 v0, 0x20

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 v0, 0x0

    .line 9
    :goto_0
    and-int/lit8 p0, p0, 0x2

    .line 10
    .line 11
    if-eqz p0, :cond_1

    .line 12
    .line 13
    or-int/lit16 p0, v0, 0x80

    .line 14
    .line 15
    return p0

    .line 16
    :cond_1
    return v0
.end method

.method public static w(Lh2/w;J)I
    .locals 2

    .line 1
    invoke-virtual {p0, p1, p2}, Lh2/w;->a(J)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, -0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0, p1, p2}, Lh2/w;->b(J)I

    .line 9
    .line 10
    .line 11
    move-result p0

    .line 12
    return p0

    .line 13
    :cond_0
    return v0
.end method

.method public static y(Lh2/w;JJ)J
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lh2/n;->w(Lh2/w;J)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    const/4 p2, -0x1

    .line 6
    if-ne p1, p2, :cond_0

    .line 7
    .line 8
    return-wide p3

    .line 9
    :cond_0
    iget-object p0, p0, Lh2/w;->c:[J

    .line 10
    .line 11
    aget-wide p1, p0, p1

    .line 12
    .line 13
    invoke-static {p1, p2, p3, p4}, Ljava/lang/Math;->min(JJ)J

    .line 14
    .line 15
    .line 16
    move-result-wide p0

    .line 17
    return-wide p0
.end method


# virtual methods
.method public final A(LN1/s;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lh2/n;->e:Lt1/G;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Lt1/G;->S(I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lh2/n;->e:Lt1/G;

    .line 9
    .line 10
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-interface {p1, v0, v2, v1}, LN1/s;->i([BII)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lh2/n;->e:Lt1/G;

    .line 19
    .line 20
    invoke-static {v0}, Lh2/b;->f(Lt1/G;)V

    .line 21
    .line 22
    .line 23
    iget-object v0, p0, Lh2/n;->e:Lt1/G;

    .line 24
    .line 25
    invoke-virtual {v0}, Lt1/G;->f()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-interface {p1, v0}, LN1/s;->n(I)V

    .line 30
    .line 31
    .line 32
    invoke-interface {p1}, LN1/s;->k()V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final B(J)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    :cond_0
    :goto_0
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x2

    .line 8
    if-nez v0, :cond_2

    .line 9
    .line 10
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 11
    .line 12
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    check-cast v0, Landroidx/media3/container/d$b;

    .line 17
    .line 18
    iget-wide v2, v0, Landroidx/media3/container/d$b;->b:J

    .line 19
    .line 20
    cmp-long v0, v2, p1

    .line 21
    .line 22
    if-nez v0, :cond_2

    .line 23
    .line 24
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 25
    .line 26
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->pop()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Landroidx/media3/container/d$b;

    .line 31
    .line 32
    iget v2, v0, Landroidx/media3/container/d;->a:I

    .line 33
    .line 34
    const v3, 0x6d6f6f76

    .line 35
    .line 36
    .line 37
    if-ne v2, v3, :cond_1

    .line 38
    .line 39
    invoke-virtual {p0, v0}, Lh2/n;->E(Landroidx/media3/container/d$b;)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 43
    .line 44
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    .line 45
    .line 46
    .line 47
    iget-boolean v0, p0, Lh2/n;->v:Z

    .line 48
    .line 49
    if-nez v0, :cond_0

    .line 50
    .line 51
    iput v1, p0, Lh2/n;->k:I

    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_1
    iget-object v1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 55
    .line 56
    invoke-virtual {v1}, Ljava/util/ArrayDeque;->isEmpty()Z

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    if-nez v1, :cond_0

    .line 61
    .line 62
    iget-object v1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 63
    .line 64
    invoke-virtual {v1}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    check-cast v1, Landroidx/media3/container/d$b;

    .line 69
    .line 70
    invoke-virtual {v1, v0}, Landroidx/media3/container/d$b;->b(Landroidx/media3/container/d$b;)V

    .line 71
    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_2
    iget p1, p0, Lh2/n;->k:I

    .line 75
    .line 76
    if-eq p1, v1, :cond_3

    .line 77
    .line 78
    invoke-virtual {p0}, Lh2/n;->s()V

    .line 79
    .line 80
    .line 81
    :cond_3
    return-void
.end method

.method public final C()V
    .locals 5

    .line 1
    const/4 v0, 0x0

    .line 2
    iget v1, p0, Lh2/n;->E:I

    .line 3
    .line 4
    const/4 v2, 0x2

    .line 5
    if-ne v1, v2, :cond_1

    .line 6
    .line 7
    iget v1, p0, Lh2/n;->b:I

    .line 8
    .line 9
    and-int/2addr v1, v2

    .line 10
    if-eqz v1, :cond_1

    .line 11
    .line 12
    iget-object v1, p0, Lh2/n;->z:LN1/t;

    .line 13
    .line 14
    const/4 v2, 0x4

    .line 15
    invoke-interface {v1, v0, v2}, LN1/t;->n(II)LN1/T;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iget-object v2, p0, Lh2/n;->F:Lc2/a;

    .line 20
    .line 21
    if-nez v2, :cond_0

    .line 22
    .line 23
    const/4 v0, 0x0

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    new-instance v3, Landroidx/media3/common/x;

    .line 26
    .line 27
    const/4 v4, 0x1

    .line 28
    new-array v4, v4, [Landroidx/media3/common/x$a;

    .line 29
    .line 30
    aput-object v2, v4, v0

    .line 31
    .line 32
    invoke-direct {v3, v4}, Landroidx/media3/common/x;-><init>([Landroidx/media3/common/x$a;)V

    .line 33
    .line 34
    .line 35
    move-object v0, v3

    .line 36
    :goto_0
    new-instance v2, Landroidx/media3/common/r$b;

    .line 37
    .line 38
    invoke-direct {v2}, Landroidx/media3/common/r$b;-><init>()V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v2, v0}, Landroidx/media3/common/r$b;->n0(Landroidx/media3/common/x;)Landroidx/media3/common/r$b;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {v0}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-interface {v1, v0}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 50
    .line 51
    .line 52
    iget-object v0, p0, Lh2/n;->z:LN1/t;

    .line 53
    .line 54
    invoke-interface {v0}, LN1/t;->l()V

    .line 55
    .line 56
    .line 57
    iget-object v0, p0, Lh2/n;->z:LN1/t;

    .line 58
    .line 59
    new-instance v1, LN1/M$b;

    .line 60
    .line 61
    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
    invoke-direct {v1, v2, v3}, LN1/M$b;-><init>(J)V

    .line 67
    .line 68
    .line 69
    invoke-interface {v0, v1}, LN1/t;->q(LN1/M;)V

    .line 70
    .line 71
    .line 72
    :cond_1
    return-void
.end method

.method public final E(Landroidx/media3/container/d$b;)V
    .locals 26
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/ParserException;
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    const/4 v9, 0x2

    .line 6
    const/4 v10, 0x0

    .line 7
    const v2, 0x6d657461

    .line 8
    .line 9
    .line 10
    invoke-virtual {v1, v2}, Landroidx/media3/container/d$b;->d(I)Landroidx/media3/container/d$b;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    new-instance v3, Ljava/util/ArrayList;

    .line 15
    .line 16
    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 17
    .line 18
    .line 19
    const/4 v11, 0x1

    .line 20
    if-eqz v2, :cond_2

    .line 21
    .line 22
    invoke-static {v2}, Lh2/b;->t(Landroidx/media3/container/d$b;)Landroidx/media3/common/x;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    iget-boolean v4, v0, Lh2/n;->x:Z

    .line 27
    .line 28
    if-eqz v4, :cond_1

    .line 29
    .line 30
    invoke-static {v2}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0, v2}, Lh2/n;->z(Landroidx/media3/common/x;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v0, v2}, Lh2/n;->t(Landroidx/media3/common/x;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    :cond_0
    move-object v13, v2

    .line 41
    move-object v14, v3

    .line 42
    goto :goto_0

    .line 43
    :cond_1
    invoke-virtual {v0, v2}, Lh2/n;->M(Landroidx/media3/common/x;)Z

    .line 44
    .line 45
    .line 46
    move-result v4

    .line 47
    if-eqz v4, :cond_0

    .line 48
    .line 49
    iput-boolean v11, v0, Lh2/n;->v:Z

    .line 50
    .line 51
    return-void

    .line 52
    :cond_2
    move-object v14, v3

    .line 53
    const/4 v13, 0x0

    .line 54
    :goto_0
    new-instance v15, Ljava/util/ArrayList;

    .line 55
    .line 56
    invoke-direct {v15}, Ljava/util/ArrayList;-><init>()V

    .line 57
    .line 58
    .line 59
    iget v2, v0, Lh2/n;->E:I

    .line 60
    .line 61
    if-ne v2, v11, :cond_3

    .line 62
    .line 63
    const/4 v7, 0x1

    .line 64
    goto :goto_1

    .line 65
    :cond_3
    const/4 v7, 0x0

    .line 66
    :goto_1
    new-instance v2, LN1/E;

    .line 67
    .line 68
    invoke-direct {v2}, LN1/E;-><init>()V

    .line 69
    .line 70
    .line 71
    const v3, 0x75647461

    .line 72
    .line 73
    .line 74
    invoke-virtual {v1, v3}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 75
    .line 76
    .line 77
    move-result-object v3

    .line 78
    if-eqz v3, :cond_4

    .line 79
    .line 80
    invoke-static {v3}, Lh2/b;->H(Landroidx/media3/container/d$c;)Landroidx/media3/common/x;

    .line 81
    .line 82
    .line 83
    move-result-object v3

    .line 84
    invoke-virtual {v2, v3}, LN1/E;->c(Landroidx/media3/common/x;)Z

    .line 85
    .line 86
    .line 87
    move-object/from16 v16, v3

    .line 88
    .line 89
    goto :goto_2

    .line 90
    :cond_4
    const/16 v16, 0x0

    .line 91
    .line 92
    :goto_2
    new-instance v3, Landroidx/media3/common/x;

    .line 93
    .line 94
    const v4, 0x6d766864

    .line 95
    .line 96
    .line 97
    invoke-virtual {v1, v4}, Landroidx/media3/container/d$b;->e(I)Landroidx/media3/container/d$c;

    .line 98
    .line 99
    .line 100
    move-result-object v4

    .line 101
    invoke-static {v4}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object v4

    .line 105
    check-cast v4, Landroidx/media3/container/d$c;

    .line 106
    .line 107
    iget-object v4, v4, Landroidx/media3/container/d$c;->b:Lt1/G;

    .line 108
    .line 109
    invoke-static {v4}, Lh2/b;->v(Lt1/G;)Landroidx/media3/container/f;

    .line 110
    .line 111
    .line 112
    move-result-object v4

    .line 113
    new-array v5, v11, [Landroidx/media3/common/x$a;

    .line 114
    .line 115
    aput-object v4, v5, v10

    .line 116
    .line 117
    invoke-direct {v3, v5}, Landroidx/media3/common/x;-><init>([Landroidx/media3/common/x$a;)V

    .line 118
    .line 119
    .line 120
    iget v4, v0, Lh2/n;->b:I

    .line 121
    .line 122
    and-int/2addr v4, v11

    .line 123
    if-eqz v4, :cond_5

    .line 124
    .line 125
    const/4 v6, 0x1

    .line 126
    goto :goto_3

    .line 127
    :cond_5
    const/4 v6, 0x0

    .line 128
    :goto_3
    new-instance v8, Lh2/l;

    .line 129
    .line 130
    invoke-direct {v8}, Lh2/l;-><init>()V

    .line 131
    .line 132
    .line 133
    move-object v5, v3

    .line 134
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    .line 135
    .line 136
    .line 137
    .line 138
    .line 139
    move-object/from16 v17, v5

    .line 140
    .line 141
    const/4 v5, 0x0

    .line 142
    invoke-static/range {v1 .. v8}, Lh2/b;->G(Landroidx/media3/container/d$b;LN1/E;JLandroidx/media3/common/DrmInitData;ZZLcom/google/common/base/Function;)Ljava/util/List;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    iget-boolean v3, v0, Lh2/n;->x:Z

    .line 147
    .line 148
    if-eqz v3, :cond_7

    .line 149
    .line 150
    invoke-interface {v14}, Ljava/util/List;->size()I

    .line 151
    .line 152
    .line 153
    move-result v3

    .line 154
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 155
    .line 156
    .line 157
    move-result v4

    .line 158
    if-ne v3, v4, :cond_6

    .line 159
    .line 160
    const/4 v3, 0x1

    .line 161
    goto :goto_4

    .line 162
    :cond_6
    const/4 v3, 0x0

    .line 163
    :goto_4
    sget-object v4, Ljava/util/Locale;->US:Ljava/util/Locale;

    .line 164
    .line 165
    invoke-interface {v14}, Ljava/util/List;->size()I

    .line 166
    .line 167
    .line 168
    move-result v5

    .line 169
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 170
    .line 171
    .line 172
    move-result-object v5

    .line 173
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 174
    .line 175
    .line 176
    move-result v6

    .line 177
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 178
    .line 179
    .line 180
    move-result-object v6

    .line 181
    new-array v7, v9, [Ljava/lang/Object;

    .line 182
    .line 183
    aput-object v5, v7, v10

    .line 184
    .line 185
    aput-object v6, v7, v11

    .line 186
    .line 187
    const-string v5, "The number of auxiliary track types from metadata (%d) is not same as the number of auxiliary tracks (%d)"

    .line 188
    .line 189
    invoke-static {v4, v5, v7}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 190
    .line 191
    .line 192
    move-result-object v4

    .line 193
    invoke-static {v3, v4}, Lt1/a;->h(ZLjava/lang/Object;)V

    .line 194
    .line 195
    .line 196
    :cond_7
    invoke-static {v1}, Lh2/k;->b(Ljava/util/List;)Ljava/lang/String;

    .line 197
    .line 198
    .line 199
    move-result-object v3

    .line 200
    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    .line 201
    .line 202
    .line 203
    .line 204
    .line 205
    const/4 v7, 0x0

    .line 206
    const/4 v8, -0x1

    .line 207
    const/4 v11, 0x0

    .line 208
    const-wide v18, -0x7fffffffffffffffL    # -4.9E-324

    .line 209
    .line 210
    .line 211
    .line 212
    .line 213
    const/16 v20, 0x1

    .line 214
    .line 215
    :goto_5
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 216
    .line 217
    .line 218
    move-result v12

    .line 219
    if-ge v7, v12, :cond_11

    .line 220
    .line 221
    invoke-interface {v1, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    move-result-object v12

    .line 225
    check-cast v12, Lh2/w;

    .line 226
    .line 227
    const/16 v21, 0x0

    .line 228
    .line 229
    iget v10, v12, Lh2/w;->b:I

    .line 230
    .line 231
    if-nez v10, :cond_8

    .line 232
    .line 233
    move-object/from16 v23, v1

    .line 234
    .line 235
    move-object v9, v2

    .line 236
    move/from16 v22, v11

    .line 237
    .line 238
    const/4 v11, -0x1

    .line 239
    const/4 v12, 0x2

    .line 240
    goto/16 :goto_a

    .line 241
    .line 242
    :cond_8
    iget-object v10, v12, Lh2/w;->a:Lh2/t;

    .line 243
    .line 244
    new-instance v6, Lh2/n$a;

    .line 245
    .line 246
    iget-object v9, v0, Lh2/n;->z:LN1/t;

    .line 247
    .line 248
    add-int/lit8 v22, v11, 0x1

    .line 249
    .line 250
    move-object/from16 v23, v1

    .line 251
    .line 252
    iget v1, v10, Lh2/t;->b:I

    .line 253
    .line 254
    invoke-interface {v9, v11, v1}, LN1/t;->n(II)LN1/T;

    .line 255
    .line 256
    .line 257
    move-result-object v1

    .line 258
    invoke-direct {v6, v10, v12, v1}, Lh2/n$a;-><init>(Lh2/t;Lh2/w;LN1/T;)V

    .line 259
    .line 260
    .line 261
    move-object v9, v2

    .line 262
    iget-wide v1, v10, Lh2/t;->e:J

    .line 263
    .line 264
    cmp-long v11, v1, v18

    .line 265
    .line 266
    if-eqz v11, :cond_9

    .line 267
    .line 268
    goto :goto_6

    .line 269
    :cond_9
    iget-wide v1, v12, Lh2/w;->h:J

    .line 270
    .line 271
    :goto_6
    iget-object v11, v6, Lh2/n$a;->c:LN1/T;

    .line 272
    .line 273
    invoke-interface {v11, v1, v2}, LN1/T;->b(J)V

    .line 274
    .line 275
    .line 276
    invoke-static {v4, v5, v1, v2}, Ljava/lang/Math;->max(JJ)J

    .line 277
    .line 278
    .line 279
    move-result-wide v4

    .line 280
    iget-object v1, v10, Lh2/t;->g:Landroidx/media3/common/r;

    .line 281
    .line 282
    iget-object v1, v1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 283
    .line 284
    const-string v2, "audio/true-hd"

    .line 285
    .line 286
    invoke-virtual {v2, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 287
    .line 288
    .line 289
    move-result v1

    .line 290
    if-eqz v1, :cond_a

    .line 291
    .line 292
    iget v1, v12, Lh2/w;->e:I

    .line 293
    .line 294
    mul-int/lit8 v1, v1, 0x10

    .line 295
    .line 296
    goto :goto_7

    .line 297
    :cond_a
    iget v1, v12, Lh2/w;->e:I

    .line 298
    .line 299
    add-int/lit8 v1, v1, 0x1e

    .line 300
    .line 301
    :goto_7
    iget-object v2, v10, Lh2/t;->g:Landroidx/media3/common/r;

    .line 302
    .line 303
    invoke-virtual {v2}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    .line 304
    .line 305
    .line 306
    move-result-object v2

    .line 307
    invoke-virtual {v2, v1}, Landroidx/media3/common/r$b;->k0(I)Landroidx/media3/common/r$b;

    .line 308
    .line 309
    .line 310
    iget v1, v10, Lh2/t;->b:I

    .line 311
    .line 312
    const/4 v11, 0x2

    .line 313
    if-ne v1, v11, :cond_e

    .line 314
    .line 315
    iget-object v1, v10, Lh2/t;->g:Landroidx/media3/common/r;

    .line 316
    .line 317
    iget v1, v1, Landroidx/media3/common/r;->f:I

    .line 318
    .line 319
    iget v11, v0, Lh2/n;->b:I

    .line 320
    .line 321
    and-int/lit8 v11, v11, 0x8

    .line 322
    .line 323
    if-eqz v11, :cond_c

    .line 324
    .line 325
    const/4 v11, -0x1

    .line 326
    if-ne v8, v11, :cond_b

    .line 327
    .line 328
    const/4 v11, 0x1

    .line 329
    goto :goto_8

    .line 330
    :cond_b
    const/4 v11, 0x2

    .line 331
    :goto_8
    or-int/2addr v1, v11

    .line 332
    :cond_c
    iget-boolean v11, v0, Lh2/n;->x:Z

    .line 333
    .line 334
    if-eqz v11, :cond_d

    .line 335
    .line 336
    const v11, 0x8000

    .line 337
    .line 338
    .line 339
    or-int/2addr v1, v11

    .line 340
    invoke-interface {v14, v7}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 341
    .line 342
    .line 343
    move-result-object v11

    .line 344
    check-cast v11, Ljava/lang/Integer;

    .line 345
    .line 346
    invoke-virtual {v11}, Ljava/lang/Integer;->intValue()I

    .line 347
    .line 348
    .line 349
    move-result v11

    .line 350
    invoke-virtual {v2, v11}, Landroidx/media3/common/r$b;->P(I)Landroidx/media3/common/r$b;

    .line 351
    .line 352
    .line 353
    :cond_d
    invoke-virtual {v2, v1}, Landroidx/media3/common/r$b;->s0(I)Landroidx/media3/common/r$b;

    .line 354
    .line 355
    .line 356
    :cond_e
    iget v1, v10, Lh2/t;->b:I

    .line 357
    .line 358
    invoke-static {v1, v9, v2}, Lh2/j;->l(ILN1/E;Landroidx/media3/common/r$b;)V

    .line 359
    .line 360
    .line 361
    iget v1, v10, Lh2/t;->b:I

    .line 362
    .line 363
    iget-object v11, v10, Lh2/t;->g:Landroidx/media3/common/r;

    .line 364
    .line 365
    iget-object v11, v11, Landroidx/media3/common/r;->l:Landroidx/media3/common/x;

    .line 366
    .line 367
    iget-object v12, v0, Lh2/n;->i:Ljava/util/List;

    .line 368
    .line 369
    invoke-interface {v12}, Ljava/util/List;->isEmpty()Z

    .line 370
    .line 371
    .line 372
    move-result v12

    .line 373
    if-eqz v12, :cond_f

    .line 374
    .line 375
    move-wide/from16 v24, v4

    .line 376
    .line 377
    const/4 v12, 0x0

    .line 378
    goto :goto_9

    .line 379
    :cond_f
    new-instance v12, Landroidx/media3/common/x;

    .line 380
    .line 381
    move-wide/from16 v24, v4

    .line 382
    .line 383
    iget-object v4, v0, Lh2/n;->i:Ljava/util/List;

    .line 384
    .line 385
    invoke-direct {v12, v4}, Landroidx/media3/common/x;-><init>(Ljava/util/List;)V

    .line 386
    .line 387
    .line 388
    :goto_9
    const/4 v4, 0x3

    .line 389
    new-array v4, v4, [Landroidx/media3/common/x;

    .line 390
    .line 391
    aput-object v12, v4, v21

    .line 392
    .line 393
    aput-object v16, v4, v20

    .line 394
    .line 395
    const/4 v12, 0x2

    .line 396
    aput-object v17, v4, v12

    .line 397
    .line 398
    invoke-static {v1, v13, v2, v11, v4}, Lh2/j;->m(ILandroidx/media3/common/x;Landroidx/media3/common/r$b;Landroidx/media3/common/x;[Landroidx/media3/common/x;)V

    .line 399
    .line 400
    .line 401
    invoke-virtual {v2, v3}, Landroidx/media3/common/r$b;->U(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 402
    .line 403
    .line 404
    iget-object v1, v6, Lh2/n$a;->c:LN1/T;

    .line 405
    .line 406
    invoke-virtual {v2}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 407
    .line 408
    .line 409
    move-result-object v2

    .line 410
    invoke-interface {v1, v2}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 411
    .line 412
    .line 413
    iget v1, v10, Lh2/t;->b:I

    .line 414
    .line 415
    const/4 v11, -0x1

    .line 416
    if-ne v1, v12, :cond_10

    .line 417
    .line 418
    if-ne v8, v11, :cond_10

    .line 419
    .line 420
    invoke-interface {v15}, Ljava/util/List;->size()I

    .line 421
    .line 422
    .line 423
    move-result v8

    .line 424
    :cond_10
    invoke-interface {v15, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 425
    .line 426
    .line 427
    move-wide/from16 v4, v24

    .line 428
    .line 429
    :goto_a
    add-int/lit8 v7, v7, 0x1

    .line 430
    .line 431
    move-object v2, v9

    .line 432
    move/from16 v11, v22

    .line 433
    .line 434
    move-object/from16 v1, v23

    .line 435
    .line 436
    const/4 v9, 0x2

    .line 437
    const/4 v10, 0x0

    .line 438
    goto/16 :goto_5

    .line 439
    .line 440
    :cond_11
    const/16 v21, 0x0

    .line 441
    .line 442
    iput v8, v0, Lh2/n;->C:I

    .line 443
    .line 444
    iput-wide v4, v0, Lh2/n;->D:J

    .line 445
    .line 446
    const/4 v1, 0x0

    .line 447
    new-array v1, v1, [Lh2/n$a;

    .line 448
    .line 449
    invoke-interface {v15, v1}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    .line 450
    .line 451
    .line 452
    move-result-object v1

    .line 453
    check-cast v1, [Lh2/n$a;

    .line 454
    .line 455
    iput-object v1, v0, Lh2/n;->A:[Lh2/n$a;

    .line 456
    .line 457
    invoke-static {v1}, Lh2/n;->p([Lh2/n$a;)[[J

    .line 458
    .line 459
    .line 460
    move-result-object v1

    .line 461
    iput-object v1, v0, Lh2/n;->B:[[J

    .line 462
    .line 463
    iget-object v1, v0, Lh2/n;->z:LN1/t;

    .line 464
    .line 465
    invoke-interface {v1}, LN1/t;->l()V

    .line 466
    .line 467
    .line 468
    iget-object v1, v0, Lh2/n;->z:LN1/t;

    .line 469
    .line 470
    invoke-interface {v1, v0}, LN1/t;->q(LN1/M;)V

    .line 471
    .line 472
    .line 473
    return-void
.end method

.method public final F(J)V
    .locals 13

    .line 1
    iget v0, p0, Lh2/n;->l:I

    .line 2
    .line 3
    const v1, 0x6d707664

    .line 4
    .line 5
    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    new-instance v2, Lc2/a;

    .line 9
    .line 10
    iget v0, p0, Lh2/n;->n:I

    .line 11
    .line 12
    int-to-long v3, v0

    .line 13
    add-long v9, p1, v3

    .line 14
    .line 15
    iget-wide v3, p0, Lh2/n;->m:J

    .line 16
    .line 17
    int-to-long v0, v0

    .line 18
    sub-long v11, v3, v0

    .line 19
    .line 20
    const-wide/16 v3, 0x0

    .line 21
    .line 22
    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    .line 23
    .line 24
    .line 25
    .line 26
    .line 27
    move-wide v5, p1

    .line 28
    invoke-direct/range {v2 .. v12}, Lc2/a;-><init>(JJJJJ)V

    .line 29
    .line 30
    .line 31
    iput-object v2, p0, Lh2/n;->F:Lc2/a;

    .line 32
    .line 33
    :cond_0
    return-void
.end method

.method public final G(LN1/s;)Z
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget v0, p0, Lh2/n;->n:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/16 v2, 0x8

    .line 5
    .line 6
    const/4 v3, 0x0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 10
    .line 11
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {p1, v0, v3, v2, v1}, LN1/s;->e([BIIZ)Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-nez v0, :cond_0

    .line 20
    .line 21
    invoke-virtual {p0}, Lh2/n;->C()V

    .line 22
    .line 23
    .line 24
    return v3

    .line 25
    :cond_0
    iput v2, p0, Lh2/n;->n:I

    .line 26
    .line 27
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 28
    .line 29
    invoke-virtual {v0, v3}, Lt1/G;->W(I)V

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 33
    .line 34
    invoke-virtual {v0}, Lt1/G;->J()J

    .line 35
    .line 36
    .line 37
    move-result-wide v4

    .line 38
    iput-wide v4, p0, Lh2/n;->m:J

    .line 39
    .line 40
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 41
    .line 42
    invoke-virtual {v0}, Lt1/G;->q()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    iput v0, p0, Lh2/n;->l:I

    .line 47
    .line 48
    :cond_1
    iget-wide v4, p0, Lh2/n;->m:J

    .line 49
    .line 50
    const-wide/16 v6, 0x1

    .line 51
    .line 52
    cmp-long v0, v4, v6

    .line 53
    .line 54
    if-nez v0, :cond_2

    .line 55
    .line 56
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 57
    .line 58
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-interface {p1, v0, v2, v2}, LN1/s;->readFully([BII)V

    .line 63
    .line 64
    .line 65
    iget v0, p0, Lh2/n;->n:I

    .line 66
    .line 67
    add-int/2addr v0, v2

    .line 68
    iput v0, p0, Lh2/n;->n:I

    .line 69
    .line 70
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 71
    .line 72
    invoke-virtual {v0}, Lt1/G;->O()J

    .line 73
    .line 74
    .line 75
    move-result-wide v4

    .line 76
    iput-wide v4, p0, Lh2/n;->m:J

    .line 77
    .line 78
    goto :goto_0

    .line 79
    :cond_2
    const-wide/16 v6, 0x0

    .line 80
    .line 81
    cmp-long v0, v4, v6

    .line 82
    .line 83
    if-nez v0, :cond_4

    .line 84
    .line 85
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 86
    .line 87
    .line 88
    move-result-wide v4

    .line 89
    const-wide/16 v6, -0x1

    .line 90
    .line 91
    cmp-long v0, v4, v6

    .line 92
    .line 93
    if-nez v0, :cond_3

    .line 94
    .line 95
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 96
    .line 97
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    check-cast v0, Landroidx/media3/container/d$b;

    .line 102
    .line 103
    if-eqz v0, :cond_3

    .line 104
    .line 105
    iget-wide v4, v0, Landroidx/media3/container/d$b;->b:J

    .line 106
    .line 107
    :cond_3
    cmp-long v0, v4, v6

    .line 108
    .line 109
    if-eqz v0, :cond_4

    .line 110
    .line 111
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 112
    .line 113
    .line 114
    move-result-wide v6

    .line 115
    sub-long/2addr v4, v6

    .line 116
    iget v0, p0, Lh2/n;->n:I

    .line 117
    .line 118
    int-to-long v6, v0

    .line 119
    add-long/2addr v4, v6

    .line 120
    iput-wide v4, p0, Lh2/n;->m:J

    .line 121
    .line 122
    :cond_4
    :goto_0
    iget-wide v4, p0, Lh2/n;->m:J

    .line 123
    .line 124
    iget v0, p0, Lh2/n;->n:I

    .line 125
    .line 126
    int-to-long v6, v0

    .line 127
    cmp-long v0, v4, v6

    .line 128
    .line 129
    if-ltz v0, :cond_b

    .line 130
    .line 131
    iget v0, p0, Lh2/n;->l:I

    .line 132
    .line 133
    invoke-static {v0}, Lh2/n;->K(I)Z

    .line 134
    .line 135
    .line 136
    move-result v0

    .line 137
    if-eqz v0, :cond_7

    .line 138
    .line 139
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 140
    .line 141
    .line 142
    move-result-wide v2

    .line 143
    iget-wide v4, p0, Lh2/n;->m:J

    .line 144
    .line 145
    add-long/2addr v2, v4

    .line 146
    iget v0, p0, Lh2/n;->n:I

    .line 147
    .line 148
    int-to-long v6, v0

    .line 149
    sub-long/2addr v2, v6

    .line 150
    int-to-long v6, v0

    .line 151
    cmp-long v0, v4, v6

    .line 152
    .line 153
    if-eqz v0, :cond_5

    .line 154
    .line 155
    iget v0, p0, Lh2/n;->l:I

    .line 156
    .line 157
    const v4, 0x6d657461

    .line 158
    .line 159
    .line 160
    if-ne v0, v4, :cond_5

    .line 161
    .line 162
    invoke-virtual {p0, p1}, Lh2/n;->A(LN1/s;)V

    .line 163
    .line 164
    .line 165
    :cond_5
    iget-object p1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 166
    .line 167
    new-instance v0, Landroidx/media3/container/d$b;

    .line 168
    .line 169
    iget v4, p0, Lh2/n;->l:I

    .line 170
    .line 171
    invoke-direct {v0, v4, v2, v3}, Landroidx/media3/container/d$b;-><init>(IJ)V

    .line 172
    .line 173
    .line 174
    invoke-virtual {p1, v0}, Ljava/util/ArrayDeque;->push(Ljava/lang/Object;)V

    .line 175
    .line 176
    .line 177
    iget-wide v4, p0, Lh2/n;->m:J

    .line 178
    .line 179
    iget p1, p0, Lh2/n;->n:I

    .line 180
    .line 181
    int-to-long v6, p1

    .line 182
    cmp-long p1, v4, v6

    .line 183
    .line 184
    if-nez p1, :cond_6

    .line 185
    .line 186
    invoke-virtual {p0, v2, v3}, Lh2/n;->B(J)V

    .line 187
    .line 188
    .line 189
    goto :goto_3

    .line 190
    :cond_6
    invoke-virtual {p0}, Lh2/n;->s()V

    .line 191
    .line 192
    .line 193
    goto :goto_3

    .line 194
    :cond_7
    iget v0, p0, Lh2/n;->l:I

    .line 195
    .line 196
    invoke-static {v0}, Lh2/n;->L(I)Z

    .line 197
    .line 198
    .line 199
    move-result v0

    .line 200
    if-eqz v0, :cond_a

    .line 201
    .line 202
    iget p1, p0, Lh2/n;->n:I

    .line 203
    .line 204
    if-ne p1, v2, :cond_8

    .line 205
    .line 206
    const/4 p1, 0x1

    .line 207
    goto :goto_1

    .line 208
    :cond_8
    const/4 p1, 0x0

    .line 209
    :goto_1
    invoke-static {p1}, Lt1/a;->g(Z)V

    .line 210
    .line 211
    .line 212
    iget-wide v4, p0, Lh2/n;->m:J

    .line 213
    .line 214
    const-wide/32 v6, 0x7fffffff

    .line 215
    .line 216
    .line 217
    cmp-long p1, v4, v6

    .line 218
    .line 219
    if-gtz p1, :cond_9

    .line 220
    .line 221
    const/4 p1, 0x1

    .line 222
    goto :goto_2

    .line 223
    :cond_9
    const/4 p1, 0x0

    .line 224
    :goto_2
    invoke-static {p1}, Lt1/a;->g(Z)V

    .line 225
    .line 226
    .line 227
    new-instance p1, Lt1/G;

    .line 228
    .line 229
    iget-wide v4, p0, Lh2/n;->m:J

    .line 230
    .line 231
    long-to-int v0, v4

    .line 232
    invoke-direct {p1, v0}, Lt1/G;-><init>(I)V

    .line 233
    .line 234
    .line 235
    iget-object v0, p0, Lh2/n;->f:Lt1/G;

    .line 236
    .line 237
    invoke-virtual {v0}, Lt1/G;->e()[B

    .line 238
    .line 239
    .line 240
    move-result-object v0

    .line 241
    invoke-virtual {p1}, Lt1/G;->e()[B

    .line 242
    .line 243
    .line 244
    move-result-object v4

    .line 245
    invoke-static {v0, v3, v4, v3, v2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 246
    .line 247
    .line 248
    iput-object p1, p0, Lh2/n;->o:Lt1/G;

    .line 249
    .line 250
    iput v1, p0, Lh2/n;->k:I

    .line 251
    .line 252
    goto :goto_3

    .line 253
    :cond_a
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 254
    .line 255
    .line 256
    move-result-wide v2

    .line 257
    iget p1, p0, Lh2/n;->n:I

    .line 258
    .line 259
    int-to-long v4, p1

    .line 260
    sub-long/2addr v2, v4

    .line 261
    invoke-virtual {p0, v2, v3}, Lh2/n;->F(J)V

    .line 262
    .line 263
    .line 264
    const/4 p1, 0x0

    .line 265
    iput-object p1, p0, Lh2/n;->o:Lt1/G;

    .line 266
    .line 267
    iput v1, p0, Lh2/n;->k:I

    .line 268
    .line 269
    :goto_3
    return v1

    .line 270
    :cond_b
    const-string p1, "Atom size less than header length (unsupported)."

    .line 271
    .line 272
    invoke-static {p1}, Landroidx/media3/common/ParserException;->createForUnsupportedContainerFeature(Ljava/lang/String;)Landroidx/media3/common/ParserException;

    .line 273
    .line 274
    .line 275
    move-result-object p1

    .line 276
    throw p1
.end method

.method public final H(LN1/s;LN1/L;)Z
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-wide v0, p0, Lh2/n;->m:J

    .line 2
    .line 3
    iget v2, p0, Lh2/n;->n:I

    .line 4
    .line 5
    int-to-long v2, v2

    .line 6
    sub-long/2addr v0, v2

    .line 7
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 8
    .line 9
    .line 10
    move-result-wide v2

    .line 11
    add-long/2addr v2, v0

    .line 12
    iget-object v4, p0, Lh2/n;->o:Lt1/G;

    .line 13
    .line 14
    const/4 v5, 0x1

    .line 15
    const/4 v6, 0x0

    .line 16
    if-eqz v4, :cond_1

    .line 17
    .line 18
    invoke-virtual {v4}, Lt1/G;->e()[B

    .line 19
    .line 20
    .line 21
    move-result-object v7

    .line 22
    iget v8, p0, Lh2/n;->n:I

    .line 23
    .line 24
    long-to-int v1, v0

    .line 25
    invoke-interface {p1, v7, v8, v1}, LN1/s;->readFully([BII)V

    .line 26
    .line 27
    .line 28
    iget p1, p0, Lh2/n;->l:I

    .line 29
    .line 30
    const v0, 0x66747970

    .line 31
    .line 32
    .line 33
    if-ne p1, v0, :cond_0

    .line 34
    .line 35
    iput-boolean v5, p0, Lh2/n;->u:Z

    .line 36
    .line 37
    invoke-static {v4}, Lh2/n;->D(Lt1/G;)I

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    iput p1, p0, Lh2/n;->E:I

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    iget-object p1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 45
    .line 46
    invoke-virtual {p1}, Ljava/util/ArrayDeque;->isEmpty()Z

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    if-nez p1, :cond_3

    .line 51
    .line 52
    iget-object p1, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 53
    .line 54
    invoke-virtual {p1}, Ljava/util/ArrayDeque;->peek()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    check-cast p1, Landroidx/media3/container/d$b;

    .line 59
    .line 60
    new-instance v0, Landroidx/media3/container/d$c;

    .line 61
    .line 62
    iget v1, p0, Lh2/n;->l:I

    .line 63
    .line 64
    invoke-direct {v0, v1, v4}, Landroidx/media3/container/d$c;-><init>(ILt1/G;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p1, v0}, Landroidx/media3/container/d$b;->c(Landroidx/media3/container/d$c;)V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_1
    iget-boolean v4, p0, Lh2/n;->u:Z

    .line 72
    .line 73
    if-nez v4, :cond_2

    .line 74
    .line 75
    iget v4, p0, Lh2/n;->l:I

    .line 76
    .line 77
    const v7, 0x6d646174

    .line 78
    .line 79
    .line 80
    if-ne v4, v7, :cond_2

    .line 81
    .line 82
    iput v5, p0, Lh2/n;->E:I

    .line 83
    .line 84
    :cond_2
    const-wide/32 v7, 0x40000

    .line 85
    .line 86
    .line 87
    cmp-long v4, v0, v7

    .line 88
    .line 89
    if-gez v4, :cond_4

    .line 90
    .line 91
    long-to-int v1, v0

    .line 92
    invoke-interface {p1, v1}, LN1/s;->n(I)V

    .line 93
    .line 94
    .line 95
    :cond_3
    :goto_0
    const/4 p1, 0x0

    .line 96
    goto :goto_1

    .line 97
    :cond_4
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 98
    .line 99
    .line 100
    move-result-wide v7

    .line 101
    add-long/2addr v7, v0

    .line 102
    iput-wide v7, p2, LN1/L;->a:J

    .line 103
    .line 104
    const/4 p1, 0x1

    .line 105
    :goto_1
    invoke-virtual {p0, v2, v3}, Lh2/n;->B(J)V

    .line 106
    .line 107
    .line 108
    iget-boolean v0, p0, Lh2/n;->v:Z

    .line 109
    .line 110
    if-eqz v0, :cond_5

    .line 111
    .line 112
    iput-boolean v5, p0, Lh2/n;->x:Z

    .line 113
    .line 114
    iget-wide v0, p0, Lh2/n;->w:J

    .line 115
    .line 116
    iput-wide v0, p2, LN1/L;->a:J

    .line 117
    .line 118
    iput-boolean v6, p0, Lh2/n;->v:Z

    .line 119
    .line 120
    const/4 p1, 0x1

    .line 121
    :cond_5
    if-eqz p1, :cond_6

    .line 122
    .line 123
    iget p1, p0, Lh2/n;->k:I

    .line 124
    .line 125
    const/4 p2, 0x2

    .line 126
    if-eq p1, p2, :cond_6

    .line 127
    .line 128
    return v5

    .line 129
    :cond_6
    return v6
.end method

.method public final I(LN1/s;LN1/L;)I
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    invoke-interface {v1}, LN1/s;->getPosition()J

    .line 6
    .line 7
    .line 8
    move-result-wide v2

    .line 9
    iget v4, v0, Lh2/n;->p:I

    .line 10
    .line 11
    const/4 v5, -0x1

    .line 12
    if-ne v4, v5, :cond_0

    .line 13
    .line 14
    invoke-virtual {v0, v2, v3}, Lh2/n;->x(J)I

    .line 15
    .line 16
    .line 17
    move-result v4

    .line 18
    iput v4, v0, Lh2/n;->p:I

    .line 19
    .line 20
    if-ne v4, v5, :cond_0

    .line 21
    .line 22
    return v5

    .line 23
    :cond_0
    iget-object v4, v0, Lh2/n;->A:[Lh2/n$a;

    .line 24
    .line 25
    iget v6, v0, Lh2/n;->p:I

    .line 26
    .line 27
    aget-object v4, v4, v6

    .line 28
    .line 29
    iget-object v6, v4, Lh2/n$a;->c:LN1/T;

    .line 30
    .line 31
    iget v14, v4, Lh2/n$a;->e:I

    .line 32
    .line 33
    iget-object v7, v4, Lh2/n$a;->b:Lh2/w;

    .line 34
    .line 35
    iget-object v8, v7, Lh2/w;->c:[J

    .line 36
    .line 37
    aget-wide v9, v8, v14

    .line 38
    .line 39
    iget-wide v11, v0, Lh2/n;->y:J

    .line 40
    .line 41
    add-long/2addr v9, v11

    .line 42
    iget-object v7, v7, Lh2/w;->d:[I

    .line 43
    .line 44
    aget v7, v7, v14

    .line 45
    .line 46
    iget-object v8, v4, Lh2/n$a;->d:LN1/U;

    .line 47
    .line 48
    sub-long v2, v9, v2

    .line 49
    .line 50
    iget v11, v0, Lh2/n;->q:I

    .line 51
    .line 52
    int-to-long v11, v11

    .line 53
    add-long/2addr v2, v11

    .line 54
    const-wide/16 v11, 0x0

    .line 55
    .line 56
    const/4 v15, 0x1

    .line 57
    cmp-long v13, v2, v11

    .line 58
    .line 59
    if-ltz v13, :cond_1

    .line 60
    .line 61
    const-wide/32 v11, 0x40000

    .line 62
    .line 63
    .line 64
    cmp-long v13, v2, v11

    .line 65
    .line 66
    if-ltz v13, :cond_2

    .line 67
    .line 68
    :cond_1
    move-object/from16 v1, p2

    .line 69
    .line 70
    goto/16 :goto_6

    .line 71
    .line 72
    :cond_2
    iget-object v9, v4, Lh2/n$a;->a:Lh2/t;

    .line 73
    .line 74
    iget v9, v9, Lh2/t;->h:I

    .line 75
    .line 76
    if-ne v9, v15, :cond_3

    .line 77
    .line 78
    const-wide/16 v9, 0x8

    .line 79
    .line 80
    add-long/2addr v2, v9

    .line 81
    add-int/lit8 v7, v7, -0x8

    .line 82
    .line 83
    :cond_3
    long-to-int v3, v2

    .line 84
    invoke-interface {v1, v3}, LN1/s;->n(I)V

    .line 85
    .line 86
    .line 87
    iget-object v2, v4, Lh2/n$a;->a:Lh2/t;

    .line 88
    .line 89
    iget-object v2, v2, Lh2/t;->g:Landroidx/media3/common/r;

    .line 90
    .line 91
    invoke-virtual {v0, v2}, Lh2/n;->q(Landroidx/media3/common/r;)Z

    .line 92
    .line 93
    .line 94
    move-result v2

    .line 95
    if-nez v2, :cond_4

    .line 96
    .line 97
    iput-boolean v15, v0, Lh2/n;->t:Z

    .line 98
    .line 99
    :cond_4
    iget-object v2, v4, Lh2/n$a;->a:Lh2/t;

    .line 100
    .line 101
    iget v3, v2, Lh2/t;->k:I

    .line 102
    .line 103
    const/4 v10, 0x0

    .line 104
    if-eqz v3, :cond_b

    .line 105
    .line 106
    iget-object v2, v0, Lh2/n;->d:Lt1/G;

    .line 107
    .line 108
    invoke-virtual {v2}, Lt1/G;->e()[B

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    aput-byte v10, v2, v10

    .line 113
    .line 114
    aput-byte v10, v2, v15

    .line 115
    .line 116
    const/4 v3, 0x2

    .line 117
    aput-byte v10, v2, v3

    .line 118
    .line 119
    iget-object v3, v4, Lh2/n$a;->a:Lh2/t;

    .line 120
    .line 121
    iget v3, v3, Lh2/t;->k:I

    .line 122
    .line 123
    const/4 v11, 0x4

    .line 124
    rsub-int/lit8 v3, v3, 0x4

    .line 125
    .line 126
    add-int/2addr v7, v3

    .line 127
    :goto_0
    iget v12, v0, Lh2/n;->r:I

    .line 128
    .line 129
    if-ge v12, v7, :cond_9

    .line 130
    .line 131
    iget v12, v0, Lh2/n;->s:I

    .line 132
    .line 133
    if-nez v12, :cond_8

    .line 134
    .line 135
    iget-object v12, v4, Lh2/n$a;->a:Lh2/t;

    .line 136
    .line 137
    iget v13, v12, Lh2/t;->k:I

    .line 138
    .line 139
    iget-boolean v5, v0, Lh2/n;->t:Z

    .line 140
    .line 141
    if-nez v5, :cond_5

    .line 142
    .line 143
    iget-object v5, v12, Lh2/t;->g:Landroidx/media3/common/r;

    .line 144
    .line 145
    invoke-static {v5}, Landroidx/media3/container/g;->o(Landroidx/media3/common/r;)I

    .line 146
    .line 147
    .line 148
    move-result v5

    .line 149
    add-int/2addr v5, v13

    .line 150
    iget-object v12, v4, Lh2/n$a;->b:Lh2/w;

    .line 151
    .line 152
    iget-object v12, v12, Lh2/w;->d:[I

    .line 153
    .line 154
    aget v12, v12, v14

    .line 155
    .line 156
    iget v9, v0, Lh2/n;->q:I

    .line 157
    .line 158
    sub-int/2addr v12, v9

    .line 159
    if-gt v5, v12, :cond_5

    .line 160
    .line 161
    iget-object v5, v4, Lh2/n$a;->a:Lh2/t;

    .line 162
    .line 163
    iget-object v5, v5, Lh2/t;->g:Landroidx/media3/common/r;

    .line 164
    .line 165
    invoke-static {v5}, Landroidx/media3/container/g;->o(Landroidx/media3/common/r;)I

    .line 166
    .line 167
    .line 168
    move-result v5

    .line 169
    iget-object v9, v4, Lh2/n$a;->a:Lh2/t;

    .line 170
    .line 171
    iget v9, v9, Lh2/t;->k:I

    .line 172
    .line 173
    add-int v13, v9, v5

    .line 174
    .line 175
    goto :goto_1

    .line 176
    :cond_5
    const/4 v5, 0x0

    .line 177
    :goto_1
    invoke-interface {v1, v2, v3, v13}, LN1/s;->readFully([BII)V

    .line 178
    .line 179
    .line 180
    iget v9, v0, Lh2/n;->q:I

    .line 181
    .line 182
    add-int/2addr v9, v13

    .line 183
    iput v9, v0, Lh2/n;->q:I

    .line 184
    .line 185
    iget-object v9, v0, Lh2/n;->d:Lt1/G;

    .line 186
    .line 187
    invoke-virtual {v9, v10}, Lt1/G;->W(I)V

    .line 188
    .line 189
    .line 190
    iget-object v9, v0, Lh2/n;->d:Lt1/G;

    .line 191
    .line 192
    invoke-virtual {v9}, Lt1/G;->q()I

    .line 193
    .line 194
    .line 195
    move-result v9

    .line 196
    if-ltz v9, :cond_7

    .line 197
    .line 198
    sub-int/2addr v9, v5

    .line 199
    iput v9, v0, Lh2/n;->s:I

    .line 200
    .line 201
    iget-object v9, v0, Lh2/n;->c:Lt1/G;

    .line 202
    .line 203
    invoke-virtual {v9, v10}, Lt1/G;->W(I)V

    .line 204
    .line 205
    .line 206
    iget-object v9, v0, Lh2/n;->c:Lt1/G;

    .line 207
    .line 208
    invoke-interface {v6, v9, v11}, LN1/T;->a(Lt1/G;I)V

    .line 209
    .line 210
    .line 211
    iget v9, v0, Lh2/n;->r:I

    .line 212
    .line 213
    add-int/2addr v9, v11

    .line 214
    iput v9, v0, Lh2/n;->r:I

    .line 215
    .line 216
    if-lez v5, :cond_6

    .line 217
    .line 218
    iget-object v9, v0, Lh2/n;->d:Lt1/G;

    .line 219
    .line 220
    invoke-interface {v6, v9, v5}, LN1/T;->a(Lt1/G;I)V

    .line 221
    .line 222
    .line 223
    iget v9, v0, Lh2/n;->r:I

    .line 224
    .line 225
    add-int/2addr v9, v5

    .line 226
    iput v9, v0, Lh2/n;->r:I

    .line 227
    .line 228
    iget-object v9, v4, Lh2/n$a;->a:Lh2/t;

    .line 229
    .line 230
    iget-object v9, v9, Lh2/t;->g:Landroidx/media3/common/r;

    .line 231
    .line 232
    invoke-static {v2, v11, v5, v9}, Landroidx/media3/container/g;->k([BIILandroidx/media3/common/r;)Z

    .line 233
    .line 234
    .line 235
    move-result v5

    .line 236
    if-eqz v5, :cond_6

    .line 237
    .line 238
    iput-boolean v15, v0, Lh2/n;->t:Z

    .line 239
    .line 240
    :cond_6
    :goto_2
    const/4 v5, -0x1

    .line 241
    goto :goto_0

    .line 242
    :cond_7
    const-string v1, "Invalid NAL length"

    .line 243
    .line 244
    const/4 v5, 0x0

    .line 245
    invoke-static {v1, v5}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    .line 246
    .line 247
    .line 248
    move-result-object v1

    .line 249
    throw v1

    .line 250
    :cond_8
    const/4 v5, 0x0

    .line 251
    invoke-interface {v6, v1, v12, v10}, LN1/T;->g(Landroidx/media3/common/j;IZ)I

    .line 252
    .line 253
    .line 254
    move-result v9

    .line 255
    iget v12, v0, Lh2/n;->q:I

    .line 256
    .line 257
    add-int/2addr v12, v9

    .line 258
    iput v12, v0, Lh2/n;->q:I

    .line 259
    .line 260
    iget v12, v0, Lh2/n;->r:I

    .line 261
    .line 262
    add-int/2addr v12, v9

    .line 263
    iput v12, v0, Lh2/n;->r:I

    .line 264
    .line 265
    iget v12, v0, Lh2/n;->s:I

    .line 266
    .line 267
    sub-int/2addr v12, v9

    .line 268
    iput v12, v0, Lh2/n;->s:I

    .line 269
    .line 270
    goto :goto_2

    .line 271
    :cond_9
    const/4 v5, 0x0

    .line 272
    :cond_a
    move v11, v7

    .line 273
    goto :goto_4

    .line 274
    :cond_b
    const/4 v5, 0x0

    .line 275
    iget-object v2, v2, Lh2/t;->g:Landroidx/media3/common/r;

    .line 276
    .line 277
    iget-object v2, v2, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 278
    .line 279
    const-string v3, "audio/ac4"

    .line 280
    .line 281
    invoke-virtual {v3, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 282
    .line 283
    .line 284
    move-result v2

    .line 285
    if-eqz v2, :cond_d

    .line 286
    .line 287
    iget v2, v0, Lh2/n;->r:I

    .line 288
    .line 289
    if-nez v2, :cond_c

    .line 290
    .line 291
    iget-object v2, v0, Lh2/n;->e:Lt1/G;

    .line 292
    .line 293
    invoke-static {v7, v2}, LN1/c;->a(ILt1/G;)V

    .line 294
    .line 295
    .line 296
    iget-object v2, v0, Lh2/n;->e:Lt1/G;

    .line 297
    .line 298
    const/4 v3, 0x7

    .line 299
    invoke-interface {v6, v2, v3}, LN1/T;->a(Lt1/G;I)V

    .line 300
    .line 301
    .line 302
    iget v2, v0, Lh2/n;->r:I

    .line 303
    .line 304
    add-int/2addr v2, v3

    .line 305
    iput v2, v0, Lh2/n;->r:I

    .line 306
    .line 307
    :cond_c
    add-int/lit8 v7, v7, 0x7

    .line 308
    .line 309
    goto :goto_3

    .line 310
    :cond_d
    if-eqz v8, :cond_e

    .line 311
    .line 312
    invoke-virtual {v8, v1}, LN1/U;->d(LN1/s;)V

    .line 313
    .line 314
    .line 315
    :cond_e
    :goto_3
    iget v2, v0, Lh2/n;->r:I

    .line 316
    .line 317
    if-ge v2, v7, :cond_a

    .line 318
    .line 319
    sub-int v2, v7, v2

    .line 320
    .line 321
    invoke-interface {v6, v1, v2, v10}, LN1/T;->g(Landroidx/media3/common/j;IZ)I

    .line 322
    .line 323
    .line 324
    move-result v2

    .line 325
    iget v3, v0, Lh2/n;->q:I

    .line 326
    .line 327
    add-int/2addr v3, v2

    .line 328
    iput v3, v0, Lh2/n;->q:I

    .line 329
    .line 330
    iget v3, v0, Lh2/n;->r:I

    .line 331
    .line 332
    add-int/2addr v3, v2

    .line 333
    iput v3, v0, Lh2/n;->r:I

    .line 334
    .line 335
    iget v3, v0, Lh2/n;->s:I

    .line 336
    .line 337
    sub-int/2addr v3, v2

    .line 338
    iput v3, v0, Lh2/n;->s:I

    .line 339
    .line 340
    goto :goto_3

    .line 341
    :goto_4
    iget-object v1, v4, Lh2/n$a;->b:Lh2/w;

    .line 342
    .line 343
    iget-object v2, v1, Lh2/w;->f:[J

    .line 344
    .line 345
    aget-wide v12, v2, v14

    .line 346
    .line 347
    iget-object v1, v1, Lh2/w;->g:[I

    .line 348
    .line 349
    aget v1, v1, v14

    .line 350
    .line 351
    iget-boolean v2, v0, Lh2/n;->t:Z

    .line 352
    .line 353
    if-nez v2, :cond_f

    .line 354
    .line 355
    const/high16 v2, 0x4000000

    .line 356
    .line 357
    or-int/2addr v1, v2

    .line 358
    :cond_f
    move v9, v1

    .line 359
    if-eqz v8, :cond_10

    .line 360
    .line 361
    move-object v7, v6

    .line 362
    move-object v6, v8

    .line 363
    move v10, v9

    .line 364
    move-wide v8, v12

    .line 365
    const/4 v1, 0x0

    .line 366
    const/4 v12, 0x0

    .line 367
    const/4 v13, 0x0

    .line 368
    invoke-virtual/range {v6 .. v13}, LN1/U;->c(LN1/T;JIIILN1/T$a;)V

    .line 369
    .line 370
    .line 371
    add-int/2addr v14, v15

    .line 372
    iget-object v2, v4, Lh2/n$a;->b:Lh2/w;

    .line 373
    .line 374
    iget v2, v2, Lh2/w;->b:I

    .line 375
    .line 376
    if-ne v14, v2, :cond_11

    .line 377
    .line 378
    invoke-virtual {v6, v7, v5}, LN1/U;->a(LN1/T;LN1/T$a;)V

    .line 379
    .line 380
    .line 381
    goto :goto_5

    .line 382
    :cond_10
    move-object v7, v6

    .line 383
    move v2, v9

    .line 384
    move v10, v11

    .line 385
    move-wide v8, v12

    .line 386
    const/4 v1, 0x0

    .line 387
    const/4 v11, 0x0

    .line 388
    const/4 v12, 0x0

    .line 389
    move-wide v7, v8

    .line 390
    move v9, v2

    .line 391
    invoke-interface/range {v6 .. v12}, LN1/T;->d(JIIILN1/T$a;)V

    .line 392
    .line 393
    .line 394
    :cond_11
    :goto_5
    iget v2, v4, Lh2/n$a;->e:I

    .line 395
    .line 396
    add-int/2addr v2, v15

    .line 397
    iput v2, v4, Lh2/n$a;->e:I

    .line 398
    .line 399
    const/4 v2, -0x1

    .line 400
    iput v2, v0, Lh2/n;->p:I

    .line 401
    .line 402
    iput v1, v0, Lh2/n;->q:I

    .line 403
    .line 404
    iput v1, v0, Lh2/n;->r:I

    .line 405
    .line 406
    iput v1, v0, Lh2/n;->s:I

    .line 407
    .line 408
    iput-boolean v1, v0, Lh2/n;->t:Z

    .line 409
    .line 410
    return v1

    .line 411
    :goto_6
    iput-wide v9, v1, LN1/L;->a:J

    .line 412
    .line 413
    return v15
.end method

.method public final J(LN1/s;LN1/L;)I
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lh2/n;->h:Lh2/q;

    .line 2
    .line 3
    iget-object v1, p0, Lh2/n;->i:Ljava/util/List;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2, v1}, Lh2/q;->c(LN1/s;LN1/L;Ljava/util/List;)I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    const/4 v0, 0x1

    .line 10
    if-ne p1, v0, :cond_0

    .line 11
    .line 12
    iget-wide v0, p2, LN1/L;->a:J

    .line 13
    .line 14
    const-wide/16 v2, 0x0

    .line 15
    .line 16
    cmp-long p2, v0, v2

    .line 17
    .line 18
    if-nez p2, :cond_0

    .line 19
    .line 20
    invoke-virtual {p0}, Lh2/n;->s()V

    .line 21
    .line 22
    .line 23
    :cond_0
    return p1
.end method

.method public final M(Landroidx/media3/common/x;)Z
    .locals 5

    .line 1
    const/4 v0, 0x0

    .line 2
    if-nez p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    iget v1, p0, Lh2/n;->b:I

    .line 6
    .line 7
    and-int/lit8 v1, v1, 0x40

    .line 8
    .line 9
    if-eqz v1, :cond_1

    .line 10
    .line 11
    const-string v1, "auxiliary.tracks.offset"

    .line 12
    .line 13
    invoke-static {p1, v1}, Lh2/j;->a(Landroidx/media3/common/x;Ljava/lang/String;)Landroidx/media3/container/b;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    if-eqz p1, :cond_1

    .line 18
    .line 19
    new-instance v1, Lt1/G;

    .line 20
    .line 21
    iget-object p1, p1, Landroidx/media3/container/b;->b:[B

    .line 22
    .line 23
    invoke-direct {v1, p1}, Lt1/G;-><init>([B)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v1}, Lt1/G;->O()J

    .line 27
    .line 28
    .line 29
    move-result-wide v1

    .line 30
    const-wide/16 v3, 0x0

    .line 31
    .line 32
    cmp-long p1, v1, v3

    .line 33
    .line 34
    if-lez p1, :cond_1

    .line 35
    .line 36
    iput-wide v1, p0, Lh2/n;->w:J

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    return p1

    .line 40
    :cond_1
    return v0
.end method

.method public final N(Lh2/n$a;J)V
    .locals 3

    .line 1
    iget-object v0, p1, Lh2/n$a;->b:Lh2/w;

    .line 2
    .line 3
    invoke-virtual {v0, p2, p3}, Lh2/w;->a(J)I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    const/4 v2, -0x1

    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    invoke-virtual {v0, p2, p3}, Lh2/w;->b(J)I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    :cond_0
    iput v1, p1, Lh2/n$a;->e:I

    .line 15
    .line 16
    return-void
.end method

.method public a(JJ)V
    .locals 4

    .line 1
    iget-object v0, p0, Lh2/n;->g:Ljava/util/ArrayDeque;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/util/ArrayDeque;->clear()V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    iput v0, p0, Lh2/n;->n:I

    .line 8
    .line 9
    const/4 v1, -0x1

    .line 10
    iput v1, p0, Lh2/n;->p:I

    .line 11
    .line 12
    iput v0, p0, Lh2/n;->q:I

    .line 13
    .line 14
    iput v0, p0, Lh2/n;->r:I

    .line 15
    .line 16
    iput v0, p0, Lh2/n;->s:I

    .line 17
    .line 18
    iput-boolean v0, p0, Lh2/n;->t:Z

    .line 19
    .line 20
    const-wide/16 v1, 0x0

    .line 21
    .line 22
    cmp-long v3, p1, v1

    .line 23
    .line 24
    if-nez v3, :cond_1

    .line 25
    .line 26
    iget p1, p0, Lh2/n;->k:I

    .line 27
    .line 28
    const/4 p2, 0x3

    .line 29
    if-eq p1, p2, :cond_0

    .line 30
    .line 31
    invoke-virtual {p0}, Lh2/n;->s()V

    .line 32
    .line 33
    .line 34
    return-void

    .line 35
    :cond_0
    iget-object p1, p0, Lh2/n;->h:Lh2/q;

    .line 36
    .line 37
    invoke-virtual {p1}, Lh2/q;->g()V

    .line 38
    .line 39
    .line 40
    iget-object p1, p0, Lh2/n;->i:Ljava/util/List;

    .line 41
    .line 42
    invoke-interface {p1}, Ljava/util/List;->clear()V

    .line 43
    .line 44
    .line 45
    return-void

    .line 46
    :cond_1
    iget-object p1, p0, Lh2/n;->A:[Lh2/n$a;

    .line 47
    .line 48
    array-length p2, p1

    .line 49
    :goto_0
    if-ge v0, p2, :cond_3

    .line 50
    .line 51
    aget-object v1, p1, v0

    .line 52
    .line 53
    invoke-virtual {p0, v1, p3, p4}, Lh2/n;->N(Lh2/n$a;J)V

    .line 54
    .line 55
    .line 56
    iget-object v1, v1, Lh2/n$a;->d:LN1/U;

    .line 57
    .line 58
    if-eqz v1, :cond_2

    .line 59
    .line 60
    invoke-virtual {v1}, LN1/U;->b()V

    .line 61
    .line 62
    .line 63
    :cond_2
    add-int/lit8 v0, v0, 0x1

    .line 64
    .line 65
    goto :goto_0

    .line 66
    :cond_3
    return-void
.end method

.method public b(LN1/t;)V
    .locals 2

    .line 1
    iget v0, p0, Lh2/n;->b:I

    .line 2
    .line 3
    and-int/lit8 v0, v0, 0x10

    .line 4
    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    new-instance v0, Lk2/t;

    .line 8
    .line 9
    iget-object v1, p0, Lh2/n;->a:Lk2/s$a;

    .line 10
    .line 11
    invoke-direct {v0, p1, v1}, Lk2/t;-><init>(LN1/t;Lk2/s$a;)V

    .line 12
    .line 13
    .line 14
    move-object p1, v0

    .line 15
    :cond_0
    iput-object p1, p0, Lh2/n;->z:LN1/t;

    .line 16
    .line 17
    return-void
.end method

.method public c(J)LN1/M$a;
    .locals 1

    .line 1
    const/4 v0, -0x1

    .line 2
    invoke-virtual {p0, p1, p2, v0}, Lh2/n;->u(JI)LN1/M$a;

    .line 3
    .line 4
    .line 5
    move-result-object p1

    .line 6
    return-object p1
.end method

.method public synthetic d()LN1/r;
    .locals 1

    .line 1
    invoke-static {p0}, LN1/q;->b(LN1/r;)LN1/r;

    move-result-object v0

    return-object v0
.end method

.method public e()Z
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    return v0
.end method

.method public bridge synthetic f()Ljava/util/List;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lh2/n;->v()Lcom/google/common/collect/ImmutableList;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public h(LN1/s;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget v0, p0, Lh2/n;->b:I

    .line 2
    .line 3
    and-int/lit8 v0, v0, 0x2

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x1

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 v0, 0x0

    .line 12
    :goto_0
    invoke-static {p1, v0}, Lh2/s;->d(LN1/s;Z)LN1/Q;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    if-eqz p1, :cond_1

    .line 17
    .line 18
    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    goto :goto_1

    .line 23
    :cond_1
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    :goto_1
    iput-object v0, p0, Lh2/n;->j:Lcom/google/common/collect/ImmutableList;

    .line 28
    .line 29
    if-nez p1, :cond_2

    .line 30
    .line 31
    return v2

    .line 32
    :cond_2
    return v1
.end method

.method public j(LN1/s;LN1/L;)I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    :cond_0
    iget v0, p0, Lh2/n;->k:I

    .line 2
    .line 3
    if-eqz v0, :cond_4

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    if-eq v0, v1, :cond_3

    .line 7
    .line 8
    const/4 v1, 0x2

    .line 9
    if-eq v0, v1, :cond_2

    .line 10
    .line 11
    const/4 v1, 0x3

    .line 12
    if-ne v0, v1, :cond_1

    .line 13
    .line 14
    invoke-virtual {p0, p1, p2}, Lh2/n;->J(LN1/s;LN1/L;)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    return p1

    .line 19
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_2
    invoke-virtual {p0, p1, p2}, Lh2/n;->I(LN1/s;LN1/L;)I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    return p1

    .line 30
    :cond_3
    invoke-virtual {p0, p1, p2}, Lh2/n;->H(LN1/s;LN1/L;)Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-eqz v0, :cond_0

    .line 35
    .line 36
    return v1

    .line 37
    :cond_4
    invoke-virtual {p0, p1}, Lh2/n;->G(LN1/s;)Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    if-nez v0, :cond_0

    .line 42
    .line 43
    const/4 p1, -0x1

    .line 44
    return p1
.end method

.method public l()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lh2/n;->D:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final q(Landroidx/media3/common/r;)Z
    .locals 3

    .line 1
    iget-object v0, p1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 2
    .line 3
    const-string v1, "video/avc"

    .line 4
    .line 5
    invoke-static {v0, v1}, Lj$/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    const/4 v2, 0x0

    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    iget p1, p0, Lh2/n;->b:I

    .line 14
    .line 15
    and-int/lit8 p1, p1, 0x20

    .line 16
    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    return v1

    .line 20
    :cond_0
    return v2

    .line 21
    :cond_1
    iget-object p1, p1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 22
    .line 23
    const-string v0, "video/hevc"

    .line 24
    .line 25
    invoke-static {p1, v0}, Lj$/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-eqz p1, :cond_2

    .line 30
    .line 31
    iget p1, p0, Lh2/n;->b:I

    .line 32
    .line 33
    and-int/lit16 p1, p1, 0x80

    .line 34
    .line 35
    if-eqz p1, :cond_2

    .line 36
    .line 37
    return v1

    .line 38
    :cond_2
    return v2
.end method

.method public release()V
    .locals 0

    .line 1
    return-void
.end method

.method public final s()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lh2/n;->k:I

    .line 3
    .line 4
    iput v0, p0, Lh2/n;->n:I

    .line 5
    .line 6
    return-void
.end method

.method public final t(Landroidx/media3/common/x;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/x;",
            ")",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .line 1
    const-string v0, "auxiliary.tracks.map"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lh2/j;->a(Landroidx/media3/common/x;Ljava/lang/String;)Landroidx/media3/container/b;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {p1}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Landroidx/media3/container/b;

    .line 12
    .line 13
    invoke-virtual {p1}, Landroidx/media3/container/b;->d()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    new-instance v0, Ljava/util/ArrayList;

    .line 18
    .line 19
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 24
    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    const/4 v2, 0x0

    .line 28
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 29
    .line 30
    .line 31
    move-result v3

    .line 32
    if-ge v2, v3, :cond_3

    .line 33
    .line 34
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    check-cast v3, Ljava/lang/Integer;

    .line 39
    .line 40
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 41
    .line 42
    .line 43
    move-result v3

    .line 44
    const/4 v4, 0x1

    .line 45
    if-eqz v3, :cond_2

    .line 46
    .line 47
    const/4 v5, 0x2

    .line 48
    if-eq v3, v4, :cond_1

    .line 49
    .line 50
    const/4 v4, 0x3

    .line 51
    if-eq v3, v5, :cond_2

    .line 52
    .line 53
    if-eq v3, v4, :cond_0

    .line 54
    .line 55
    const/4 v4, 0x0

    .line 56
    goto :goto_1

    .line 57
    :cond_0
    const/4 v4, 0x4

    .line 58
    goto :goto_1

    .line 59
    :cond_1
    const/4 v4, 0x2

    .line 60
    :cond_2
    :goto_1
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    add-int/lit8 v2, v2, 0x1

    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_3
    return-object v0
.end method

.method public u(JI)LN1/M$a;
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-wide/from16 v1, p1

    .line 4
    .line 5
    move/from16 v3, p3

    .line 6
    .line 7
    iget-object v4, v0, Lh2/n;->A:[Lh2/n$a;

    .line 8
    .line 9
    array-length v5, v4

    .line 10
    if-nez v5, :cond_0

    .line 11
    .line 12
    new-instance v1, LN1/M$a;

    .line 13
    .line 14
    sget-object v2, LN1/N;->c:LN1/N;

    .line 15
    .line 16
    invoke-direct {v1, v2}, LN1/M$a;-><init>(LN1/N;)V

    .line 17
    .line 18
    .line 19
    return-object v1

    .line 20
    :cond_0
    const/4 v5, -0x1

    .line 21
    if-eq v3, v5, :cond_1

    .line 22
    .line 23
    move v6, v3

    .line 24
    goto :goto_0

    .line 25
    :cond_1
    iget v6, v0, Lh2/n;->C:I

    .line 26
    .line 27
    :goto_0
    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    .line 28
    .line 29
    .line 30
    .line 31
    .line 32
    const-wide/16 v9, -0x1

    .line 33
    .line 34
    if-eq v6, v5, :cond_3

    .line 35
    .line 36
    aget-object v4, v4, v6

    .line 37
    .line 38
    iget-object v4, v4, Lh2/n$a;->b:Lh2/w;

    .line 39
    .line 40
    invoke-static {v4, v1, v2}, Lh2/n;->w(Lh2/w;J)I

    .line 41
    .line 42
    .line 43
    move-result v6

    .line 44
    if-ne v6, v5, :cond_2

    .line 45
    .line 46
    new-instance v1, LN1/M$a;

    .line 47
    .line 48
    sget-object v2, LN1/N;->c:LN1/N;

    .line 49
    .line 50
    invoke-direct {v1, v2}, LN1/M$a;-><init>(LN1/N;)V

    .line 51
    .line 52
    .line 53
    return-object v1

    .line 54
    :cond_2
    iget-object v11, v4, Lh2/w;->f:[J

    .line 55
    .line 56
    aget-wide v12, v11, v6

    .line 57
    .line 58
    iget-object v11, v4, Lh2/w;->c:[J

    .line 59
    .line 60
    aget-wide v14, v11, v6

    .line 61
    .line 62
    cmp-long v11, v12, v1

    .line 63
    .line 64
    if-gez v11, :cond_4

    .line 65
    .line 66
    iget v11, v4, Lh2/w;->b:I

    .line 67
    .line 68
    add-int/lit8 v11, v11, -0x1

    .line 69
    .line 70
    if-ge v6, v11, :cond_4

    .line 71
    .line 72
    invoke-virtual {v4, v1, v2}, Lh2/w;->b(J)I

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    if-eq v1, v5, :cond_4

    .line 77
    .line 78
    if-eq v1, v6, :cond_4

    .line 79
    .line 80
    iget-object v2, v4, Lh2/w;->f:[J

    .line 81
    .line 82
    aget-wide v9, v2, v1

    .line 83
    .line 84
    iget-object v2, v4, Lh2/w;->c:[J

    .line 85
    .line 86
    aget-wide v1, v2, v1

    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_3
    const-wide v14, 0x7fffffffffffffffL

    .line 90
    .line 91
    .line 92
    .line 93
    .line 94
    move-wide v12, v1

    .line 95
    :cond_4
    move-wide v1, v9

    .line 96
    move-wide v9, v7

    .line 97
    :goto_1
    if-ne v3, v5, :cond_7

    .line 98
    .line 99
    const/4 v3, 0x0

    .line 100
    :goto_2
    iget-object v4, v0, Lh2/n;->A:[Lh2/n$a;

    .line 101
    .line 102
    array-length v5, v4

    .line 103
    if-ge v3, v5, :cond_7

    .line 104
    .line 105
    iget v5, v0, Lh2/n;->C:I

    .line 106
    .line 107
    if-eq v3, v5, :cond_6

    .line 108
    .line 109
    aget-object v4, v4, v3

    .line 110
    .line 111
    iget-object v4, v4, Lh2/n$a;->b:Lh2/w;

    .line 112
    .line 113
    invoke-static {v4, v12, v13, v14, v15}, Lh2/n;->y(Lh2/w;JJ)J

    .line 114
    .line 115
    .line 116
    move-result-wide v5

    .line 117
    cmp-long v11, v9, v7

    .line 118
    .line 119
    if-eqz v11, :cond_5

    .line 120
    .line 121
    invoke-static {v4, v9, v10, v1, v2}, Lh2/n;->y(Lh2/w;JJ)J

    .line 122
    .line 123
    .line 124
    move-result-wide v1

    .line 125
    :cond_5
    move-wide v14, v5

    .line 126
    :cond_6
    add-int/lit8 v3, v3, 0x1

    .line 127
    .line 128
    goto :goto_2

    .line 129
    :cond_7
    new-instance v3, LN1/N;

    .line 130
    .line 131
    invoke-direct {v3, v12, v13, v14, v15}, LN1/N;-><init>(JJ)V

    .line 132
    .line 133
    .line 134
    cmp-long v4, v9, v7

    .line 135
    .line 136
    if-nez v4, :cond_8

    .line 137
    .line 138
    new-instance v1, LN1/M$a;

    .line 139
    .line 140
    invoke-direct {v1, v3}, LN1/M$a;-><init>(LN1/N;)V

    .line 141
    .line 142
    .line 143
    return-object v1

    .line 144
    :cond_8
    new-instance v4, LN1/N;

    .line 145
    .line 146
    invoke-direct {v4, v9, v10, v1, v2}, LN1/N;-><init>(JJ)V

    .line 147
    .line 148
    .line 149
    new-instance v1, LN1/M$a;

    .line 150
    .line 151
    invoke-direct {v1, v3, v4}, LN1/M$a;-><init>(LN1/N;LN1/N;)V

    .line 152
    .line 153
    .line 154
    return-object v1
.end method

.method public v()Lcom/google/common/collect/ImmutableList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/google/common/collect/ImmutableList<",
            "LN1/Q;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lh2/n;->j:Lcom/google/common/collect/ImmutableList;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x(J)I
    .locals 22

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v4, -0x1

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v6, -0x1

    .line 6
    const/4 v7, 0x0

    .line 7
    const-wide v8, 0x7fffffffffffffffL

    .line 8
    .line 9
    .line 10
    .line 11
    .line 12
    const/4 v10, 0x1

    .line 13
    const-wide v11, 0x7fffffffffffffffL

    .line 14
    .line 15
    .line 16
    .line 17
    .line 18
    const/4 v13, 0x1

    .line 19
    const-wide v14, 0x7fffffffffffffffL

    .line 20
    .line 21
    .line 22
    .line 23
    .line 24
    const-wide v16, 0x7fffffffffffffffL

    .line 25
    .line 26
    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v1, v0, Lh2/n;->A:[Lh2/n$a;

    .line 30
    .line 31
    array-length v2, v1

    .line 32
    if-ge v7, v2, :cond_7

    .line 33
    .line 34
    aget-object v1, v1, v7

    .line 35
    .line 36
    iget v2, v1, Lh2/n$a;->e:I

    .line 37
    .line 38
    iget-object v1, v1, Lh2/n$a;->b:Lh2/w;

    .line 39
    .line 40
    iget v3, v1, Lh2/w;->b:I

    .line 41
    .line 42
    if-ne v2, v3, :cond_0

    .line 43
    .line 44
    goto :goto_3

    .line 45
    :cond_0
    iget-object v1, v1, Lh2/w;->c:[J

    .line 46
    .line 47
    aget-wide v18, v1, v2

    .line 48
    .line 49
    iget-object v1, v0, Lh2/n;->B:[[J

    .line 50
    .line 51
    invoke-static {v1}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    check-cast v1, [[J

    .line 56
    .line 57
    aget-object v1, v1, v7

    .line 58
    .line 59
    aget-wide v2, v1, v2

    .line 60
    .line 61
    sub-long v18, v18, p1

    .line 62
    .line 63
    const-wide/16 v20, 0x0

    .line 64
    .line 65
    cmp-long v1, v18, v20

    .line 66
    .line 67
    if-ltz v1, :cond_2

    .line 68
    .line 69
    const-wide/32 v20, 0x40000

    .line 70
    .line 71
    .line 72
    cmp-long v1, v18, v20

    .line 73
    .line 74
    if-ltz v1, :cond_1

    .line 75
    .line 76
    goto :goto_1

    .line 77
    :cond_1
    const/4 v1, 0x0

    .line 78
    goto :goto_2

    .line 79
    :cond_2
    :goto_1
    const/4 v1, 0x1

    .line 80
    :goto_2
    if-nez v1, :cond_3

    .line 81
    .line 82
    if-nez v13, :cond_4

    .line 83
    .line 84
    :cond_3
    if-ne v1, v13, :cond_5

    .line 85
    .line 86
    cmp-long v20, v18, v14

    .line 87
    .line 88
    if-gez v20, :cond_5

    .line 89
    .line 90
    :cond_4
    move v13, v1

    .line 91
    move-wide v11, v2

    .line 92
    move v6, v7

    .line 93
    move-wide/from16 v14, v18

    .line 94
    .line 95
    :cond_5
    cmp-long v18, v2, v8

    .line 96
    .line 97
    if-gez v18, :cond_6

    .line 98
    .line 99
    move v10, v1

    .line 100
    move-wide v8, v2

    .line 101
    move v4, v7

    .line 102
    :cond_6
    :goto_3
    add-int/lit8 v7, v7, 0x1

    .line 103
    .line 104
    goto :goto_0

    .line 105
    :cond_7
    cmp-long v1, v8, v16

    .line 106
    .line 107
    if-eqz v1, :cond_9

    .line 108
    .line 109
    if-eqz v10, :cond_9

    .line 110
    .line 111
    const-wide/32 v1, 0xa00000

    .line 112
    .line 113
    .line 114
    add-long/2addr v8, v1

    .line 115
    cmp-long v1, v11, v8

    .line 116
    .line 117
    if-gez v1, :cond_8

    .line 118
    .line 119
    goto :goto_4

    .line 120
    :cond_8
    return v4

    .line 121
    :cond_9
    :goto_4
    return v6
.end method

.method public final z(Landroidx/media3/common/x;)V
    .locals 4

    .line 1
    const-string v0, "auxiliary.tracks.interleaved"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lh2/j;->a(Landroidx/media3/common/x;Ljava/lang/String;)Landroidx/media3/container/b;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    iget-object p1, p1, Landroidx/media3/container/b;->b:[B

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    aget-byte p1, p1, v0

    .line 13
    .line 14
    if-nez p1, :cond_0

    .line 15
    .line 16
    iget-wide v0, p0, Lh2/n;->w:J

    .line 17
    .line 18
    const-wide/16 v2, 0x10

    .line 19
    .line 20
    add-long/2addr v0, v2

    .line 21
    iput-wide v0, p0, Lh2/n;->y:J

    .line 22
    .line 23
    :cond_0
    return-void
.end method
