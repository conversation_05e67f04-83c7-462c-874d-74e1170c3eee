.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$initStepInputLimits$2"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x3fd,
        0x3fe
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->r5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;

    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x1

    .line 11
    if-eqz v2, :cond_2

    .line 12
    .line 13
    if-eq v2, v4, :cond_1

    .line 14
    .line 15
    if-ne v2, v3, :cond_0

    .line 16
    .line 17
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    move-object/from16 v2, p1

    .line 21
    .line 22
    goto :goto_2

    .line 23
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw v1

    .line 31
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    move-object/from16 v2, p1

    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 41
    .line 42
    iput v4, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->label:I

    .line 43
    .line 44
    invoke-static {v2, v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->o4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    if-ne v2, v1, :cond_3

    .line 49
    .line 50
    goto :goto_1

    .line 51
    :cond_3
    :goto_0
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 52
    .line 53
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyId()J

    .line 54
    .line 55
    .line 56
    move-result-wide v5

    .line 57
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 58
    .line 59
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->a4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/GetLimitsScenario;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    iput v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->label:I

    .line 64
    .line 65
    invoke-virtual {v2, v5, v6, v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/GetLimitsScenario;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v2

    .line 69
    if-ne v2, v1, :cond_4

    .line 70
    .line 71
    :goto_1
    return-object v1

    .line 72
    :cond_4
    :goto_2
    check-cast v2, LSw/c;

    .line 73
    .line 74
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 75
    .line 76
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->e4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/x0;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    if-eqz v1, :cond_5

    .line 81
    .line 82
    const/4 v3, 0x0

    .line 83
    invoke-static {v1, v3, v4, v3}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 84
    .line 85
    .line 86
    :cond_5
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$initStepInputLimits$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 87
    .line 88
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->J3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    :cond_6
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    move-object v4, v3

    .line 97
    check-cast v4, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 98
    .line 99
    invoke-virtual {v2}, LSw/c;->e()D

    .line 100
    .line 101
    .line 102
    move-result-wide v11

    .line 103
    invoke-virtual {v2}, LSw/c;->f()Z

    .line 104
    .line 105
    .line 106
    move-result v5

    .line 107
    if-eqz v5, :cond_7

    .line 108
    .line 109
    const-wide v5, 0x42a2309ce53ffffbL    # 9.99999999999999E12

    .line 110
    .line 111
    .line 112
    .line 113
    .line 114
    :goto_3
    move-wide v13, v5

    .line 115
    goto :goto_4

    .line 116
    :cond_7
    invoke-virtual {v2}, LSw/c;->d()D

    .line 117
    .line 118
    .line 119
    move-result-wide v5

    .line 120
    goto :goto_3

    .line 121
    :goto_4
    invoke-virtual {v2}, LSw/c;->f()Z

    .line 122
    .line 123
    .line 124
    move-result v16

    .line 125
    new-instance v5, Lorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;

    .line 126
    .line 127
    invoke-virtual {v2}, LSw/c;->a()Z

    .line 128
    .line 129
    .line 130
    move-result v6

    .line 131
    invoke-virtual {v2}, LSw/c;->d()D

    .line 132
    .line 133
    .line 134
    move-result-wide v7

    .line 135
    invoke-direct {v5, v6, v7, v8}, Lorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;-><init>(ZD)V

    .line 136
    .line 137
    .line 138
    invoke-virtual {v2}, LSw/c;->c()Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v15

    .line 142
    const/16 v26, 0x7e87

    .line 143
    .line 144
    const/16 v27, 0x0

    .line 145
    .line 146
    move-object/from16 v18, v5

    .line 147
    .line 148
    const-wide/16 v5, 0x0

    .line 149
    .line 150
    const-wide/16 v7, 0x0

    .line 151
    .line 152
    const-wide/16 v9, 0x0

    .line 153
    .line 154
    const/16 v17, 0x0

    .line 155
    .line 156
    const/16 v19, 0x0

    .line 157
    .line 158
    const/16 v20, 0x0

    .line 159
    .line 160
    const/16 v21, 0x0

    .line 161
    .line 162
    const/16 v22, 0x0

    .line 163
    .line 164
    const/16 v23, 0x0

    .line 165
    .line 166
    const/16 v24, 0x0

    .line 167
    .line 168
    const/16 v25, 0x0

    .line 169
    .line 170
    invoke-static/range {v4 .. v27}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->b(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;DDDDDLjava/lang/String;ZZLorg/xbet/coupon/impl/make_bet/presentation/model/AutoMaxUiModel;ZZZZZZZILjava/lang/Object;)Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 171
    .line 172
    .line 173
    move-result-object v4

    .line 174
    invoke-interface {v1, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 175
    .line 176
    .line 177
    move-result v3

    .line 178
    if-eqz v3, :cond_6

    .line 179
    .line 180
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 181
    .line 182
    return-object v1
.end method
