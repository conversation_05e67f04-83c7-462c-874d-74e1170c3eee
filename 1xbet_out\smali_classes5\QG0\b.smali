.class public final LQG0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a\'\u0010\u0005\u001a\u00020\u00012\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0003H\u0001\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Lkotlin/Function0;",
        "",
        "onClick",
        "Landroidx/compose/ui/l;",
        "modifier",
        "b",
        "(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LQG0/b;->c(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 10
    .param p0    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/ui/l;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    const v0, 0x73afff3a

    .line 2
    .line 3
    .line 4
    invoke-interface {p2, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v7

    .line 8
    and-int/lit8 p2, p4, 0x1

    .line 9
    .line 10
    if-eqz p2, :cond_0

    .line 11
    .line 12
    or-int/lit8 p2, p3, 0x6

    .line 13
    .line 14
    goto :goto_1

    .line 15
    :cond_0
    and-int/lit8 p2, p3, 0x6

    .line 16
    .line 17
    if-nez p2, :cond_2

    .line 18
    .line 19
    invoke-interface {v7, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result p2

    .line 23
    if-eqz p2, :cond_1

    .line 24
    .line 25
    const/4 p2, 0x4

    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 p2, 0x2

    .line 28
    :goto_0
    or-int/2addr p2, p3

    .line 29
    goto :goto_1

    .line 30
    :cond_2
    move p2, p3

    .line 31
    :goto_1
    and-int/lit8 v1, p4, 0x2

    .line 32
    .line 33
    if-eqz v1, :cond_3

    .line 34
    .line 35
    or-int/lit8 p2, p2, 0x30

    .line 36
    .line 37
    goto :goto_3

    .line 38
    :cond_3
    and-int/lit8 v2, p3, 0x30

    .line 39
    .line 40
    if-nez v2, :cond_5

    .line 41
    .line 42
    invoke-interface {v7, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    if-eqz v2, :cond_4

    .line 47
    .line 48
    const/16 v2, 0x20

    .line 49
    .line 50
    goto :goto_2

    .line 51
    :cond_4
    const/16 v2, 0x10

    .line 52
    .line 53
    :goto_2
    or-int/2addr p2, v2

    .line 54
    :cond_5
    :goto_3
    and-int/lit8 v2, p2, 0x13

    .line 55
    .line 56
    const/16 v3, 0x12

    .line 57
    .line 58
    if-ne v2, v3, :cond_7

    .line 59
    .line 60
    invoke-interface {v7}, Landroidx/compose/runtime/j;->c()Z

    .line 61
    .line 62
    .line 63
    move-result v2

    .line 64
    if-nez v2, :cond_6

    .line 65
    .line 66
    goto :goto_4

    .line 67
    :cond_6
    invoke-interface {v7}, Landroidx/compose/runtime/j;->n()V

    .line 68
    .line 69
    .line 70
    move-object v1, p0

    .line 71
    goto :goto_5

    .line 72
    :cond_7
    :goto_4
    if-eqz v1, :cond_8

    .line 73
    .line 74
    sget-object p1, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 75
    .line 76
    :cond_8
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    if-eqz v1, :cond_9

    .line 81
    .line 82
    const/4 v1, -0x1

    .line 83
    const-string v2, "org.xbet.statistic.main.common.presentation.components.MoreButton (MoreButton.kt:16)"

    .line 84
    .line 85
    invoke-static {v0, p2, v1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 86
    .line 87
    .line 88
    :cond_9
    const/4 v0, 0x0

    .line 89
    const/4 v1, 0x0

    .line 90
    const/4 v2, 0x1

    .line 91
    invoke-static {p1, v0, v2, v1}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    sget-object v1, LA11/a;->a:LA11/a;

    .line 96
    .line 97
    invoke-virtual {v1}, LA11/a;->y0()F

    .line 98
    .line 99
    .line 100
    move-result v1

    .line 101
    invoke-static {v0, v1}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 102
    .line 103
    .line 104
    move-result-object v5

    .line 105
    sget v0, Lpb/k;->tournaments_more:I

    .line 106
    .line 107
    const/4 v1, 0x0

    .line 108
    invoke-static {v0, v7, v1}, Lm0/h;->a(ILandroidx/compose/runtime/j;I)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    sget v4, LlZ0/n;->DSButton_Medium_Secondary:I

    .line 113
    .line 114
    and-int/lit8 p2, p2, 0xe

    .line 115
    .line 116
    or-int/lit16 v8, p2, 0x180

    .line 117
    .line 118
    const/16 v9, 0x20

    .line 119
    .line 120
    const/4 v3, 0x1

    .line 121
    const/4 v6, 0x0

    .line 122
    move-object v1, p0

    .line 123
    invoke-static/range {v1 .. v9}, LFZ0/e;->e(Lkotlin/jvm/functions/Function0;Ljava/lang/String;ZILandroidx/compose/ui/l;ILandroidx/compose/runtime/j;II)V

    .line 124
    .line 125
    .line 126
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 127
    .line 128
    .line 129
    move-result p0

    .line 130
    if-eqz p0, :cond_a

    .line 131
    .line 132
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 133
    .line 134
    .line 135
    :cond_a
    :goto_5
    invoke-interface {v7}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 136
    .line 137
    .line 138
    move-result-object p0

    .line 139
    if-eqz p0, :cond_b

    .line 140
    .line 141
    new-instance p2, LQG0/a;

    .line 142
    .line 143
    invoke-direct {p2, v1, p1, p3, p4}, LQG0/a;-><init>(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;II)V

    .line 144
    .line 145
    .line 146
    invoke-interface {p0, p2}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 147
    .line 148
    .line 149
    :cond_b
    return-void
.end method

.method public static final c(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LQG0/b;->b(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method
