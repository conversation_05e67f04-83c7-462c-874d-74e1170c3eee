.class public final LTB0/c$a$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LTB0/c$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "LTB0/c;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000!\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0000\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001J\u001f\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u0008\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0007J!\u0010\n\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "TB0/c$a$a",
        "Landroidx/recyclerview/widget/i$f;",
        "LTB0/c;",
        "oldItem",
        "newItem",
        "",
        "e",
        "(LTB0/c;LTB0/c;)Z",
        "d",
        "",
        "f",
        "(LTB0/c;LTB0/c;)Ljava/lang/Object;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LTB0/c;

    .line 2
    .line 3
    check-cast p2, LTB0/c;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LTB0/c$a$a;->d(LTB0/c;LTB0/c;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, LTB0/c;

    .line 2
    .line 3
    check-cast p2, LTB0/c;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LTB0/c$a$a;->e(LTB0/c;LTB0/c;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LTB0/c;

    .line 2
    .line 3
    check-cast p2, LTB0/c;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LTB0/c$a$a;->f(LTB0/c;LTB0/c;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public d(LTB0/c;LTB0/c;)Z
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(LTB0/c;LTB0/c;)Z
    .locals 1

    .line 1
    instance-of v0, p1, LTB0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    instance-of v0, p2, LTB0/b;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    sget-object v0, LTB0/b;->j:LTB0/b$a;

    .line 10
    .line 11
    check-cast p1, LTB0/b;

    .line 12
    .line 13
    check-cast p2, LTB0/b;

    .line 14
    .line 15
    invoke-virtual {v0, p1, p2}, LTB0/b$a;->a(LTB0/b;LTB0/b;)Z

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    return p1

    .line 20
    :cond_0
    instance-of v0, p1, LNB0/a;

    .line 21
    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    instance-of v0, p2, LNB0/a;

    .line 25
    .line 26
    if-eqz v0, :cond_1

    .line 27
    .line 28
    sget-object v0, LNB0/a;->e:LNB0/a$a;

    .line 29
    .line 30
    check-cast p1, LNB0/a;

    .line 31
    .line 32
    check-cast p2, LNB0/a;

    .line 33
    .line 34
    invoke-virtual {v0, p1, p2}, LNB0/a$a;->a(LNB0/a;LNB0/a;)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    return p1

    .line 39
    :cond_1
    instance-of v0, p1, LWB0/b;

    .line 40
    .line 41
    if-eqz v0, :cond_2

    .line 42
    .line 43
    instance-of v0, p2, LWB0/b;

    .line 44
    .line 45
    if-eqz v0, :cond_2

    .line 46
    .line 47
    sget-object v0, LWB0/b;->d:LWB0/b$a;

    .line 48
    .line 49
    check-cast p1, LWB0/b;

    .line 50
    .line 51
    check-cast p2, LWB0/b;

    .line 52
    .line 53
    invoke-virtual {v0, p1, p2}, LWB0/b$a;->a(LWB0/b;LWB0/b;)Z

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    return p1

    .line 58
    :cond_2
    instance-of v0, p1, LRB0/a;

    .line 59
    .line 60
    if-eqz v0, :cond_3

    .line 61
    .line 62
    instance-of v0, p2, LRB0/a;

    .line 63
    .line 64
    if-eqz v0, :cond_3

    .line 65
    .line 66
    sget-object v0, LRB0/a;->j:LRB0/a$a;

    .line 67
    .line 68
    check-cast p1, LRB0/a;

    .line 69
    .line 70
    check-cast p2, LRB0/a;

    .line 71
    .line 72
    invoke-virtual {v0, p1, p2}, LRB0/a$a;->a(LRB0/a;LRB0/a;)Z

    .line 73
    .line 74
    .line 75
    move-result p1

    .line 76
    return p1

    .line 77
    :cond_3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 82
    .line 83
    .line 84
    move-result-object p2

    .line 85
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    return p1
.end method

.method public f(LTB0/c;LTB0/c;)Ljava/lang/Object;
    .locals 1

    .line 1
    instance-of v0, p1, LTB0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    instance-of v0, p2, LTB0/b;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    sget-object v0, LTB0/b;->j:LTB0/b$a;

    .line 10
    .line 11
    check-cast p1, LTB0/b;

    .line 12
    .line 13
    check-cast p2, LTB0/b;

    .line 14
    .line 15
    invoke-virtual {v0, p1, p2}, LTB0/b$a;->b(LTB0/b;LTB0/b;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    return-object p1

    .line 20
    :cond_0
    instance-of v0, p1, LNB0/a;

    .line 21
    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    instance-of v0, p2, LNB0/a;

    .line 25
    .line 26
    if-eqz v0, :cond_1

    .line 27
    .line 28
    sget-object v0, LNB0/a;->e:LNB0/a$a;

    .line 29
    .line 30
    check-cast p1, LNB0/a;

    .line 31
    .line 32
    check-cast p2, LNB0/a;

    .line 33
    .line 34
    invoke-virtual {v0, p1, p2}, LNB0/a$a;->b(LNB0/a;LNB0/a;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    return-object p1

    .line 39
    :cond_1
    instance-of v0, p1, LWB0/b;

    .line 40
    .line 41
    if-eqz v0, :cond_2

    .line 42
    .line 43
    instance-of v0, p2, LWB0/b;

    .line 44
    .line 45
    if-eqz v0, :cond_2

    .line 46
    .line 47
    sget-object v0, LWB0/b;->d:LWB0/b$a;

    .line 48
    .line 49
    check-cast p1, LWB0/b;

    .line 50
    .line 51
    check-cast p2, LWB0/b;

    .line 52
    .line 53
    invoke-virtual {v0, p1, p2}, LWB0/b$a;->b(LWB0/b;LWB0/b;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    return-object p1

    .line 58
    :cond_2
    instance-of v0, p1, LRB0/a;

    .line 59
    .line 60
    if-eqz v0, :cond_3

    .line 61
    .line 62
    instance-of v0, p2, LRB0/a;

    .line 63
    .line 64
    if-eqz v0, :cond_3

    .line 65
    .line 66
    sget-object v0, LRB0/a;->j:LRB0/a$a;

    .line 67
    .line 68
    check-cast p1, LRB0/a;

    .line 69
    .line 70
    check-cast p2, LRB0/a;

    .line 71
    .line 72
    invoke-virtual {v0, p1, p2}, LRB0/a$a;->b(LRB0/a;LRB0/a;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1

    .line 77
    :cond_3
    invoke-super {p0, p1, p2}, Landroidx/recyclerview/widget/i$f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    return-object p1
.end method
