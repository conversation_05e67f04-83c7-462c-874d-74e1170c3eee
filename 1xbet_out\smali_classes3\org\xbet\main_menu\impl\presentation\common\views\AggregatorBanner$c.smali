.class public final Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnAttachStateChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->y()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0006\u00a8\u0006\u0008"
    }
    d2 = {
        "org/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c",
        "Landroid/view/View$OnAttachStateChangeListener;",
        "Landroid/view/View;",
        "v",
        "",
        "onViewAttachedToWindow",
        "(Landroid/view/View;)V",
        "onViewDetachedFromWindow",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;->a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onViewAttachedToWindow(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;->a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;->a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;

    .line 10
    .line 11
    invoke-static {p1}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method

.method public onViewDetachedFromWindow(Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;->a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;->a(Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner$c;->a:Lorg/xbet/main_menu/impl/presentation/common/views/AggregatorBanner;

    .line 10
    .line 11
    invoke-static {p1}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method
