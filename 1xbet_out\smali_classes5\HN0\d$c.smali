.class public final LHN0/d$c;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LHN0/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001d\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "LHN0/d$c;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LDN0/B;",
        "binding",
        "<init>",
        "(LDN0/B;)V",
        "LNN0/f;",
        "item",
        "",
        "width",
        "",
        "d",
        "(LNN0/f;I)V",
        "Landroid/widget/TextView;",
        "textView",
        "Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;",
        "gameStatusUiModel",
        "e",
        "(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V",
        "LDN0/B;",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LDN0/B;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDN0/B;)V
    .locals 1
    .param p1    # LDN0/B;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LDN0/B;->b()Landroid/widget/FrameLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LHN0/d$c;->e:LDN0/B;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final d(LNN0/f;I)V
    .locals 7
    .param p1    # LNN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LNN0/f$a;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-eqz v0, :cond_7

    .line 7
    .line 8
    iget-object v0, p0, LHN0/d$c;->e:LDN0/B;

    .line 9
    .line 10
    iget-object v3, v0, LDN0/B;->d:Landroid/widget/LinearLayout;

    .line 11
    .line 12
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    iget-object v3, v0, LDN0/B;->b:Landroid/widget/TextView;

    .line 16
    .line 17
    invoke-virtual {v3, v1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    check-cast p1, LNN0/f$a;

    .line 21
    .line 22
    invoke-virtual {p1}, LNN0/f$a;->b()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    if-eqz v1, :cond_9

    .line 35
    .line 36
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    add-int/lit8 v3, v2, 0x1

    .line 41
    .line 42
    if-gez v2, :cond_0

    .line 43
    .line 44
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 45
    .line 46
    .line 47
    :cond_0
    check-cast v1, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;

    .line 48
    .line 49
    if-eqz v2, :cond_6

    .line 50
    .line 51
    const/4 v4, 0x1

    .line 52
    if-eq v2, v4, :cond_5

    .line 53
    .line 54
    const/4 v4, 0x2

    .line 55
    if-eq v2, v4, :cond_4

    .line 56
    .line 57
    const/4 v4, 0x3

    .line 58
    if-eq v2, v4, :cond_3

    .line 59
    .line 60
    const/4 v4, 0x4

    .line 61
    if-eq v2, v4, :cond_2

    .line 62
    .line 63
    const/4 v4, 0x5

    .line 64
    if-eq v2, v4, :cond_1

    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_1
    iget-object v2, v0, LDN0/B;->j:Landroid/widget/TextView;

    .line 68
    .line 69
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 70
    .line 71
    .line 72
    goto :goto_1

    .line 73
    :cond_2
    iget-object v2, v0, LDN0/B;->f:Landroid/widget/TextView;

    .line 74
    .line 75
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 76
    .line 77
    .line 78
    goto :goto_1

    .line 79
    :cond_3
    iget-object v2, v0, LDN0/B;->h:Landroid/widget/TextView;

    .line 80
    .line 81
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 82
    .line 83
    .line 84
    goto :goto_1

    .line 85
    :cond_4
    iget-object v2, v0, LDN0/B;->k:Landroid/widget/TextView;

    .line 86
    .line 87
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 88
    .line 89
    .line 90
    goto :goto_1

    .line 91
    :cond_5
    iget-object v2, v0, LDN0/B;->i:Landroid/widget/TextView;

    .line 92
    .line 93
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 94
    .line 95
    .line 96
    goto :goto_1

    .line 97
    :cond_6
    iget-object v2, v0, LDN0/B;->g:Landroid/widget/TextView;

    .line 98
    .line 99
    invoke-virtual {p0, v2, v1}, LHN0/d$c;->e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 100
    .line 101
    .line 102
    :goto_1
    move v2, v3

    .line 103
    goto :goto_0

    .line 104
    :cond_7
    instance-of v0, p1, LNN0/f$b;

    .line 105
    .line 106
    if-eqz v0, :cond_c

    .line 107
    .line 108
    iget-object v0, p0, LHN0/d$c;->e:LDN0/B;

    .line 109
    .line 110
    iget-object v0, v0, LDN0/B;->d:Landroid/widget/LinearLayout;

    .line 111
    .line 112
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 113
    .line 114
    .line 115
    iget-object v0, p0, LHN0/d$c;->e:LDN0/B;

    .line 116
    .line 117
    iget-object v0, v0, LDN0/B;->b:Landroid/widget/TextView;

    .line 118
    .line 119
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 120
    .line 121
    .line 122
    iget-object v0, p0, LHN0/d$c;->e:LDN0/B;

    .line 123
    .line 124
    iget-object v0, v0, LDN0/B;->b:Landroid/widget/TextView;

    .line 125
    .line 126
    check-cast p1, LNN0/f$b;

    .line 127
    .line 128
    invoke-virtual {p1}, LNN0/f$b;->c()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 133
    .line 134
    .line 135
    iget-object v0, p0, LHN0/d$c;->e:LDN0/B;

    .line 136
    .line 137
    iget-object v0, v0, LDN0/B;->b:Landroid/widget/TextView;

    .line 138
    .line 139
    invoke-virtual {p1}, LNN0/f$b;->b()I

    .line 140
    .line 141
    .line 142
    move-result v1

    .line 143
    if-eqz v1, :cond_8

    .line 144
    .line 145
    iget-object v1, p0, LHN0/d$c;->e:LDN0/B;

    .line 146
    .line 147
    iget-object v1, v1, LDN0/B;->b:Landroid/widget/TextView;

    .line 148
    .line 149
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    invoke-virtual {p1}, LNN0/f$b;->b()I

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    invoke-static {v1, p1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 158
    .line 159
    .line 160
    move-result p1

    .line 161
    goto :goto_2

    .line 162
    :cond_8
    sget-object v1, Lub/b;->a:Lub/b;

    .line 163
    .line 164
    iget-object p1, p0, LHN0/d$c;->e:LDN0/B;

    .line 165
    .line 166
    iget-object p1, p1, LDN0/B;->b:Landroid/widget/TextView;

    .line 167
    .line 168
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 169
    .line 170
    .line 171
    move-result-object v2

    .line 172
    sget v3, Lpb/c;->textColorSecondary:I

    .line 173
    .line 174
    const/4 v5, 0x4

    .line 175
    const/4 v6, 0x0

    .line 176
    const/4 v4, 0x0

    .line 177
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 178
    .line 179
    .line 180
    move-result p1

    .line 181
    :goto_2
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 182
    .line 183
    .line 184
    :cond_9
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 185
    .line 186
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 187
    .line 188
    .line 189
    move-result-object p1

    .line 190
    iget p1, p1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 191
    .line 192
    if-eq p1, p2, :cond_b

    .line 193
    .line 194
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 195
    .line 196
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    if-eqz v0, :cond_a

    .line 201
    .line 202
    iput p2, v0, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 203
    .line 204
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 205
    .line 206
    .line 207
    return-void

    .line 208
    :cond_a
    new-instance p1, Ljava/lang/NullPointerException;

    .line 209
    .line 210
    const-string p2, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 211
    .line 212
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 213
    .line 214
    .line 215
    throw p1

    .line 216
    :cond_b
    return-void

    .line 217
    :cond_c
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 218
    .line 219
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 220
    .line 221
    .line 222
    throw p1
.end method

.method public final e(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p2}, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;->getTextId()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getText(I)Ljava/lang/CharSequence;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-nez v1, :cond_0

    .line 22
    .line 23
    const/16 p2, 0x8

    .line 24
    .line 25
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    const/4 v1, 0x0

    .line 30
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    sget-object v0, Lub/b;->a:Lub/b;

    .line 37
    .line 38
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {p2}, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;->getColorRes()I

    .line 43
    .line 44
    .line 45
    move-result p2

    .line 46
    invoke-virtual {v0, v1, p2}, Lub/b;->d(Landroid/content/Context;I)I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    invoke-static {p2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-virtual {p1, p2}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 55
    .line 56
    .line 57
    return-void
.end method
