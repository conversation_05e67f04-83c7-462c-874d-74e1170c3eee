.class public final LfP0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LfP0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LfP0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LfP0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LfP0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;IILorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;JLSX0/a;Lc8/h;LHg/d;)LfP0/c;
    .locals 27

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    invoke-static/range {p16 .. p16}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    invoke-static/range {p17 .. p17}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    invoke-static/range {p20 .. p20}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    invoke-static/range {p21 .. p22}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    invoke-static/range {p23 .. p23}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    invoke-static/range {p24 .. p24}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    invoke-static/range {p25 .. p25}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    new-instance v1, LfP0/a$b;

    .line 86
    .line 87
    invoke-static/range {p16 .. p16}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 88
    .line 89
    .line 90
    move-result-object v17

    .line 91
    invoke-static/range {p17 .. p17}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 92
    .line 93
    .line 94
    move-result-object v18

    .line 95
    invoke-static/range {p21 .. p22}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 96
    .line 97
    .line 98
    move-result-object v22

    .line 99
    const/16 v26, 0x0

    .line 100
    .line 101
    move-object/from16 v2, p1

    .line 102
    .line 103
    move-object/from16 v3, p2

    .line 104
    .line 105
    move-object/from16 v4, p3

    .line 106
    .line 107
    move-object/from16 v5, p4

    .line 108
    .line 109
    move-object/from16 v6, p5

    .line 110
    .line 111
    move-object/from16 v7, p6

    .line 112
    .line 113
    move-object/from16 v8, p7

    .line 114
    .line 115
    move-object/from16 v9, p8

    .line 116
    .line 117
    move-object/from16 v10, p9

    .line 118
    .line 119
    move-object/from16 v11, p10

    .line 120
    .line 121
    move-object/from16 v12, p11

    .line 122
    .line 123
    move-object/from16 v13, p12

    .line 124
    .line 125
    move-object/from16 v14, p13

    .line 126
    .line 127
    move-object/from16 v15, p14

    .line 128
    .line 129
    move-object/from16 v16, p15

    .line 130
    .line 131
    move-object/from16 v19, p18

    .line 132
    .line 133
    move-object/from16 v20, p19

    .line 134
    .line 135
    move-object/from16 v21, p20

    .line 136
    .line 137
    move-object/from16 v23, p23

    .line 138
    .line 139
    move-object/from16 v24, p24

    .line 140
    .line 141
    move-object/from16 v25, p25

    .line 142
    .line 143
    invoke-direct/range {v1 .. v26}, LfP0/a$b;-><init>(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;Ljava/lang/Integer;Ljava/lang/Integer;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;Ljava/lang/Long;LSX0/a;Lc8/h;LHg/d;LfP0/b;)V

    .line 144
    .line 145
    .line 146
    return-object v1
.end method
