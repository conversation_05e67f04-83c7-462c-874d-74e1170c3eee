.class final Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.jackpot.presentation.viewmodel.JackpotViewModel$observeConnectionState$1"
    f = "JackpotViewModel.kt"
    l = {
        0x4c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->J3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "connected",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-boolean p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->Z$0:Z

    .line 28
    .line 29
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 30
    .line 31
    invoke-static {v1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->r3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lm8/a;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    new-instance v3, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1$1;

    .line 40
    .line 41
    iget-object v4, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 42
    .line 43
    const/4 v5, 0x0

    .line 44
    invoke-direct {v3, v4, p1, v5}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1$1;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;ZLkotlin/coroutines/e;)V

    .line 45
    .line 46
    .line 47
    iput v2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$observeConnectionState$1;->label:I

    .line 48
    .line 49
    invoke-static {v1, v3, p0}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    if-ne p1, v0, :cond_2

    .line 54
    .line 55
    return-object v0

    .line 56
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 57
    .line 58
    return-object p1
.end method
