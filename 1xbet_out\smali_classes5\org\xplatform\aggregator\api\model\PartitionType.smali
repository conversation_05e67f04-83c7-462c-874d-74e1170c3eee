.class public final enum Lorg/xplatform/aggregator/api/model/PartitionType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/model/PartitionType$a;,
        Lorg/xplatform/aggregator/api/model/PartitionType$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u001e\u0008\u0086\u0081\u0002\u0018\u0000 \u00072\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0008B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018j\u0002\u0008\u0019j\u0002\u0008\u001aj\u0002\u0008\u001bj\u0002\u0008\u001cj\u0002\u0008\u001dj\u0002\u0008\u001ej\u0002\u0008\u001fj\u0002\u0008 j\u0002\u0008!\u00a8\u0006\""
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "",
        "getId",
        "()J",
        "Companion",
        "a",
        "LIVE_AGGREGATOR",
        "SLOTS",
        "BINGO",
        "BINGO_NEW",
        "SPORT",
        "FISHING",
        "IMPERIUM",
        "WISEODDS",
        "SCRATCH_CARDS",
        "TV_GAMES",
        "TV_BET",
        "VULKAN",
        "CRASH",
        "KENO",
        "ASIAN",
        "BOARD_GAMES",
        "SKILL_GAMES",
        "CARD_GAMES",
        "LOTTO",
        "POKER",
        "FAST_BET",
        "NOT_SET",
        "AGGREGATOR",
        "BRAND",
        "MY_AGGREGATOR",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final enum AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final AGGREGATOR_ID:J = 0x17L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum ASIAN:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final ASIAN_ID:J = 0x97L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum BINGO:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final BINGO_ID:J = 0x2fL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final BINGO_ID_NEW:J = 0x8L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum BINGO_NEW:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final enum BOARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final BOARD_GAMES_ID:J = 0x10L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum BRAND:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final BRAND_ID:J = 0x6cL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum CARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final CARD_GAMES_ID:J = 0x63L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum CRASH:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final CRASH_ID:J = 0x95L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field private static final Companion:Lorg/xplatform/aggregator/api/model/PartitionType$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum FAST_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final FAST_BET_ID:J = 0xafL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum FISHING:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final FISHING_ID:J = 0xcL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum IMPERIUM:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final IMPERIUM_ID:J = 0x3aL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum KENO:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final KENO_ID:J = 0x31L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final LIVE_AGGREGATOR_ID:J = 0x25L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum LOTTO:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final LOTTO_ID:J = 0x65L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum MY_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final MY_AGGREGATOR_ID:J = 0x6fL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final NOT_SET_ID:J = 0x0L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum POKER:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final POKER_ID:J = 0x66L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum SCRATCH_CARDS:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final SCRATCH_CARDS_ID:J = 0x42L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum SKILL_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final SKILL_GAMES_ID:J = 0x67L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final SLOTS_ID:J = 0x1L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum SPORT:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final SPORT_ID:J = 0x29L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum TV_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final TV_BET_ID:J = 0x8eL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum TV_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final TV_GAMES_ID:J = 0x33L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum VULKAN:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final VULKAN_ID:J = 0x41L
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final enum WISEODDS:Lorg/xplatform/aggregator/api/model/PartitionType;

.field public static final WISEODDS_ID:J = 0x5cL
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    const-string v1, "LIVE_AGGREGATOR"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 12
    .line 13
    const-string v1, "SLOTS"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 20
    .line 21
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 22
    .line 23
    const-string v1, "BINGO"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->BINGO:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 32
    .line 33
    const-string v1, "BINGO_NEW"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->BINGO_NEW:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 40
    .line 41
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 42
    .line 43
    const-string v1, "SPORT"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SPORT:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 50
    .line 51
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 52
    .line 53
    const-string v1, "FISHING"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->FISHING:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 62
    .line 63
    const-string v1, "IMPERIUM"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->IMPERIUM:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 70
    .line 71
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 72
    .line 73
    const-string v1, "WISEODDS"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->WISEODDS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 82
    .line 83
    const-string v1, "SCRATCH_CARDS"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SCRATCH_CARDS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 91
    .line 92
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 93
    .line 94
    const-string v1, "TV_GAMES"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 102
    .line 103
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 104
    .line 105
    const-string v1, "TV_BET"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 113
    .line 114
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 115
    .line 116
    const-string v1, "VULKAN"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->VULKAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 124
    .line 125
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 126
    .line 127
    const-string v1, "CRASH"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->CRASH:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 135
    .line 136
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 137
    .line 138
    const-string v1, "KENO"

    .line 139
    .line 140
    const/16 v2, 0xd

    .line 141
    .line 142
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 143
    .line 144
    .line 145
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->KENO:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 146
    .line 147
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 148
    .line 149
    const-string v1, "ASIAN"

    .line 150
    .line 151
    const/16 v2, 0xe

    .line 152
    .line 153
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 154
    .line 155
    .line 156
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->ASIAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 157
    .line 158
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 159
    .line 160
    const-string v1, "BOARD_GAMES"

    .line 161
    .line 162
    const/16 v2, 0xf

    .line 163
    .line 164
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 165
    .line 166
    .line 167
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->BOARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 168
    .line 169
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 170
    .line 171
    const-string v1, "SKILL_GAMES"

    .line 172
    .line 173
    const/16 v2, 0x10

    .line 174
    .line 175
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 176
    .line 177
    .line 178
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SKILL_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 179
    .line 180
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 181
    .line 182
    const-string v1, "CARD_GAMES"

    .line 183
    .line 184
    const/16 v2, 0x11

    .line 185
    .line 186
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 187
    .line 188
    .line 189
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->CARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 190
    .line 191
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 192
    .line 193
    const-string v1, "LOTTO"

    .line 194
    .line 195
    const/16 v2, 0x12

    .line 196
    .line 197
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 198
    .line 199
    .line 200
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->LOTTO:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 201
    .line 202
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 203
    .line 204
    const-string v1, "POKER"

    .line 205
    .line 206
    const/16 v2, 0x13

    .line 207
    .line 208
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 209
    .line 210
    .line 211
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->POKER:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 212
    .line 213
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 214
    .line 215
    const-string v1, "FAST_BET"

    .line 216
    .line 217
    const/16 v2, 0x14

    .line 218
    .line 219
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 220
    .line 221
    .line 222
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->FAST_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 223
    .line 224
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 225
    .line 226
    const-string v1, "NOT_SET"

    .line 227
    .line 228
    const/16 v2, 0x15

    .line 229
    .line 230
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 231
    .line 232
    .line 233
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 234
    .line 235
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 236
    .line 237
    const-string v1, "AGGREGATOR"

    .line 238
    .line 239
    const/16 v2, 0x16

    .line 240
    .line 241
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 242
    .line 243
    .line 244
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 245
    .line 246
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 247
    .line 248
    const-string v1, "BRAND"

    .line 249
    .line 250
    const/16 v2, 0x17

    .line 251
    .line 252
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 253
    .line 254
    .line 255
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->BRAND:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 256
    .line 257
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 258
    .line 259
    const-string v1, "MY_AGGREGATOR"

    .line 260
    .line 261
    const/16 v2, 0x18

    .line 262
    .line 263
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/PartitionType;-><init>(Ljava/lang/String;I)V

    .line 264
    .line 265
    .line 266
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->MY_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 267
    .line 268
    invoke-static {}, Lorg/xplatform/aggregator/api/model/PartitionType;->a()[Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->$VALUES:[Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 273
    .line 274
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 275
    .line 276
    .line 277
    move-result-object v0

    .line 278
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->$ENTRIES:Lkotlin/enums/a;

    .line 279
    .line 280
    new-instance v0, Lorg/xplatform/aggregator/api/model/PartitionType$a;

    .line 281
    .line 282
    const/4 v1, 0x0

    .line 283
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/api/model/PartitionType$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 284
    .line 285
    .line 286
    sput-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->Companion:Lorg/xplatform/aggregator/api/model/PartitionType$a;

    .line 287
    .line 288
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/api/model/PartitionType;
    .locals 3

    .line 1
    const/16 v0, 0x19

    new-array v0, v0, [Lorg/xplatform/aggregator/api/model/PartitionType;

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->BINGO:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->BINGO_NEW:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SPORT:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->FISHING:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->IMPERIUM:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->WISEODDS:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SCRATCH_CARDS:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->VULKAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->CRASH:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->KENO:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->ASIAN:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xe

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->BOARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0xf

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SKILL_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x10

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->CARD_GAMES:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x11

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->LOTTO:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x12

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->POKER:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x13

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->FAST_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x14

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x15

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x16

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->BRAND:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x17

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->MY_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    const/16 v2, 0x18

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/api/model/PartitionType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/PartitionType;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/api/model/PartitionType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->$VALUES:[Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getId()J
    .locals 2

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw v0

    .line 18
    :pswitch_0
    const-wide/16 v0, 0x6f

    .line 19
    .line 20
    return-wide v0

    .line 21
    :pswitch_1
    const-wide/16 v0, 0x6c

    .line 22
    .line 23
    return-wide v0

    .line 24
    :pswitch_2
    const-wide/16 v0, 0x17

    .line 25
    .line 26
    return-wide v0

    .line 27
    :pswitch_3
    const-wide/16 v0, 0xaf

    .line 28
    .line 29
    return-wide v0

    .line 30
    :pswitch_4
    const-wide/16 v0, 0x66

    .line 31
    .line 32
    return-wide v0

    .line 33
    :pswitch_5
    const-wide/16 v0, 0x65

    .line 34
    .line 35
    return-wide v0

    .line 36
    :pswitch_6
    const-wide/16 v0, 0x63

    .line 37
    .line 38
    return-wide v0

    .line 39
    :pswitch_7
    const-wide/16 v0, 0x67

    .line 40
    .line 41
    return-wide v0

    .line 42
    :pswitch_8
    const-wide/16 v0, 0x10

    .line 43
    .line 44
    return-wide v0

    .line 45
    :pswitch_9
    const-wide/16 v0, 0x97

    .line 46
    .line 47
    return-wide v0

    .line 48
    :pswitch_a
    const-wide/16 v0, 0x31

    .line 49
    .line 50
    return-wide v0

    .line 51
    :pswitch_b
    const-wide/16 v0, 0x95

    .line 52
    .line 53
    return-wide v0

    .line 54
    :pswitch_c
    const-wide/16 v0, 0x0

    .line 55
    .line 56
    return-wide v0

    .line 57
    :pswitch_d
    const-wide/16 v0, 0x41

    .line 58
    .line 59
    return-wide v0

    .line 60
    :pswitch_e
    const-wide/16 v0, 0x8e

    .line 61
    .line 62
    return-wide v0

    .line 63
    :pswitch_f
    const-wide/16 v0, 0x33

    .line 64
    .line 65
    return-wide v0

    .line 66
    :pswitch_10
    const-wide/16 v0, 0x42

    .line 67
    .line 68
    return-wide v0

    .line 69
    :pswitch_11
    const-wide/16 v0, 0x5c

    .line 70
    .line 71
    return-wide v0

    .line 72
    :pswitch_12
    const-wide/16 v0, 0x3a

    .line 73
    .line 74
    return-wide v0

    .line 75
    :pswitch_13
    const-wide/16 v0, 0xc

    .line 76
    .line 77
    return-wide v0

    .line 78
    :pswitch_14
    const-wide/16 v0, 0x29

    .line 79
    .line 80
    return-wide v0

    .line 81
    :pswitch_15
    const-wide/16 v0, 0x8

    .line 82
    .line 83
    return-wide v0

    .line 84
    :pswitch_16
    const-wide/16 v0, 0x2f

    .line 85
    .line 86
    return-wide v0

    .line 87
    :pswitch_17
    const-wide/16 v0, 0x1

    .line 88
    .line 89
    return-wide v0

    .line 90
    :pswitch_18
    const-wide/16 v0, 0x25

    .line 91
    .line 92
    return-wide v0

    .line 93
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
