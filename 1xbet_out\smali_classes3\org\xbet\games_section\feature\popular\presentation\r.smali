.class public final synthetic Lorg/xbet/games_section/feature/popular/presentation/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/r;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/r;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-static {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->w3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
