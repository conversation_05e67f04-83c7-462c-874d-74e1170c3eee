.class final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.presentation.fragments.MyAggregatorFragment$onObserveData$9"
    f = "MyAggregatorFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "showError",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 14
    .line 15
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->z3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)LS91/W;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object v0, v0, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 20
    .line 21
    const/16 v1, 0x8

    .line 22
    .line 23
    const/4 v2, 0x0

    .line 24
    if-nez p1, :cond_0

    .line 25
    .line 26
    const/4 v3, 0x0

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    const/16 v3, 0x8

    .line 29
    .line 30
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 34
    .line 35
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->z3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)LS91/W;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    iget-object v0, v0, LS91/W;->e:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 40
    .line 41
    if-nez p1, :cond_1

    .line 42
    .line 43
    const/4 v1, 0x0

    .line 44
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 48
    .line 49
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->L3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Z)V

    .line 50
    .line 51
    .line 52
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 53
    .line 54
    return-object p1

    .line 55
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 56
    .line 57
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 58
    .line 59
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    throw p1
.end method
