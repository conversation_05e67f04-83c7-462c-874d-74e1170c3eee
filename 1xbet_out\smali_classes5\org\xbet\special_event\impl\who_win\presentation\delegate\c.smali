.class public final synthetic Lorg/xbet/special_event/impl/who_win/presentation/delegate/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/c;->a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/c;->a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

    check-cast p1, Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

    check-cast p2, Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    invoke-static {v0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->b(Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lorg/xbet/betting/core/coupon/models/SingleBetGame;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
