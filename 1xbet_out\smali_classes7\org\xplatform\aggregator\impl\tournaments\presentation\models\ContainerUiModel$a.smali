.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;
    .locals 8

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Landroid/os/Parcel;->readInt()I

    move-result v3

    if-eqz v3, :cond_0

    const/4 v3, 0x1

    goto :goto_0

    :cond_0
    const/4 v3, 0x0

    :goto_0
    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;->valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    move-result-object v4

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    move-result-object v5

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    move-result-object v6

    const-class v7, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    invoke-virtual {v7}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v7

    invoke-virtual {p1, v7}, Landroid/os/Parcel;->readParcelable(Ljava/lang/ClassLoader;)Landroid/os/Parcelable;

    move-result-object p1

    move-object v7, p1

    check-cast v7, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V

    return-object v0
.end method

.method public final b(I)[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel$a;->a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel$a;->b(I)[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    move-result-object p1

    return-object p1
.end method
