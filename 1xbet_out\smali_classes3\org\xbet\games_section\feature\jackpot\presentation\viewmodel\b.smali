.class public final Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LR40/c;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXv/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LR40/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LR40/c;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LXv/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "LR40/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->j:LBc/a;

    .line 23
    .line 24
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LR40/c;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LXv/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "LR40/a;",
            ">;)",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object/from16 v6, p5

    .line 9
    .line 10
    move-object/from16 v7, p6

    .line 11
    .line 12
    move-object/from16 v8, p7

    .line 13
    .line 14
    move-object/from16 v9, p8

    .line 15
    .line 16
    move-object/from16 v10, p9

    .line 17
    .line 18
    invoke-direct/range {v0 .. v10}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method

.method public static c(LR40/c;Lgk/b;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LXv/a;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;Lak/a;LR40/a;)Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;
    .locals 12

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object/from16 v5, p4

    .line 8
    .line 9
    move-object/from16 v6, p5

    .line 10
    .line 11
    move-object/from16 v7, p6

    .line 12
    .line 13
    move-object/from16 v8, p7

    .line 14
    .line 15
    move-object/from16 v9, p8

    .line 16
    .line 17
    move-object/from16 v10, p9

    .line 18
    .line 19
    move-object/from16 v11, p10

    .line 20
    .line 21
    invoke-direct/range {v0 .. v11}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;-><init>(LR40/c;Lgk/b;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LXv/a;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;Lak/a;LR40/a;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, LR40/c;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lgk/b;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v3, v0

    .line 26
    check-cast v3, Lorg/xbet/ui_common/utils/internet/a;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v4, v0

    .line 35
    check-cast v4, LSX0/a;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v5, v0

    .line 44
    check-cast v5, LXv/a;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v7, v0

    .line 53
    check-cast v7, Lm8/a;

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->g:LBc/a;

    .line 56
    .line 57
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    move-object v8, v0

    .line 62
    check-cast v8, Lorg/xbet/ui_common/utils/M;

    .line 63
    .line 64
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->h:LBc/a;

    .line 65
    .line 66
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    move-object v9, v0

    .line 71
    check-cast v9, Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->i:LBc/a;

    .line 74
    .line 75
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    move-object v10, v0

    .line 80
    check-cast v10, Lak/a;

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->j:LBc/a;

    .line 83
    .line 84
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    move-object v11, v0

    .line 89
    check-cast v11, LR40/a;

    .line 90
    .line 91
    move-object v6, p1

    .line 92
    invoke-static/range {v1 .. v11}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/b;->c(LR40/c;Lgk/b;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;LXv/a;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;Lak/a;LR40/a;)Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    return-object p1
.end method
