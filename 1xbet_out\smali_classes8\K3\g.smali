.class public LK3/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LK3/e;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Z:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "LK3/e<",
        "TZ;TZ;>;"
    }
.end annotation


# static fields
.field public static final a:LK3/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LK3/g<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LK3/g;

    .line 2
    .line 3
    invoke-direct {v0}, LK3/g;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LK3/g;->a:LK3/g;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()LK3/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<Z:",
            "Ljava/lang/Object;",
            ">()",
            "LK3/e<",
            "TZ;TZ;>;"
        }
    .end annotation

    .line 1
    sget-object v0, LK3/g;->a:LK3/g;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public transcode(Lcom/bumptech/glide/load/engine/s;Lz3/e;)Lcom/bumptech/glide/load/engine/s;
    .locals 0
    .param p1    # Lcom/bumptech/glide/load/engine/s;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lz3/e;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bumptech/glide/load/engine/s<",
            "TZ;>;",
            "Lz3/e;",
            ")",
            "Lcom/bumptech/glide/load/engine/s<",
            "TZ;>;"
        }
    .end annotation

    .line 1
    return-object p1
.end method
