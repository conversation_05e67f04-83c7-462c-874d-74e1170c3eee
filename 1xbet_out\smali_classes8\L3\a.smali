.class public LL3/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL3/j;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(LL3/l;)V
    .locals 0
    .param p1    # LL3/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p1}, LL3/l;->c()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(LL3/l;)V
    .locals 0
    .param p1    # LL3/l;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method
