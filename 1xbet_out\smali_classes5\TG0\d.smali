.class public final synthetic LTG0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LUG0/a;


# direct methods
.method public synthetic constructor <init>(LUG0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTG0/d;->a:LUG0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LTG0/d;->a:LUG0/a;

    check-cast p1, Landroid/content/Context;

    invoke-static {v0, p1}, LTG0/f;->a(LUG0/a;Landroid/content/Context;)Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    move-result-object p1

    return-object p1
.end method
