.class public final LrS0/c;
.super Ljava/lang/Object;


# static fields
.field public static item_champ_delegate:I = 0x7f0d059f

.field public static item_filter_header_item:I = 0x7f0d05e7

.field public static item_onboarding_delegate:I = 0x7f0d063a

.field public static item_sport_delegate:I = 0x7f0d06c3

.field public static market_layout:I = 0x7f0d0796

.field public static onboarding_dialog:I = 0x7f0d0810

.field public static sport_filter_item:I = 0x7f0d0964

.field public static sport_filter_shimmer_item:I = 0x7f0d0965

.field public static swipex_card_multi_team_item:I = 0x7f0d0992

.field public static swipex_card_multi_team_item_mini:I = 0x7f0d0993

.field public static swipex_card_single_event_item:I = 0x7f0d0994

.field public static swipex_card_single_event_item_mini:I = 0x7f0d0995

.field public static swipex_card_two_team_item:I = 0x7f0d0996

.field public static swipex_card_two_team_item_mini:I = 0x7f0d0997

.field public static swipex_change_bet_value_dialog:I = 0x7f0d0998

.field public static swipex_filter_fragment:I = 0x7f0d0999

.field public static swipex_fragment:I = 0x7f0d099a

.field public static swipex_onboarding_fragment:I = 0x7f0d099b


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
