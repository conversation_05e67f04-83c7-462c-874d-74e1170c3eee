.class public final LNA0/q;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a!\u0010\u000b\u001a\u00020\n*\u0008\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u001b\u0010\u000e\u001a\u00020\r*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LYA0/a;",
        "",
        "currentTime",
        "LRA0/h;",
        "c",
        "(LYA0/a;J)LRA0/h;",
        "",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
        "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
        "key",
        "",
        "a",
        "(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;",
        "Ll8/b$a$c;",
        "b",
        "(LYA0/a;J)J",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;",
            ">;",
            "Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;",
            ")",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->a()Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    if-ne v2, p1, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_1
    move-object v0, v1

    .line 27
    :goto_0
    check-cast v0, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;

    .line 28
    .line 29
    if-eqz v0, :cond_2

    .line 30
    .line 31
    invoke-virtual {v0}, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel;->b()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    :cond_2
    if-nez v1, :cond_3

    .line 36
    .line 37
    const-string p0, ""

    .line 38
    .line 39
    return-object p0

    .line 40
    :cond_3
    return-object v1
.end method

.method public static final b(LYA0/a;J)J
    .locals 4

    .line 1
    invoke-virtual {p0}, LYA0/a;->M()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const/16 p0, 0x3e8

    .line 6
    .line 7
    int-to-long v2, p0

    .line 8
    div-long/2addr p1, v2

    .line 9
    sub-long/2addr v0, p1

    .line 10
    const-wide/16 p0, 0x0

    .line 11
    .line 12
    invoke-static {v0, v1, p0, p1}, Lkotlin/ranges/f;->h(JJ)J

    .line 13
    .line 14
    .line 15
    move-result-wide p0

    .line 16
    invoke-static {p0, p1}, Ll8/b$a$c;->f(J)J

    .line 17
    .line 18
    .line 19
    move-result-wide p0

    .line 20
    return-wide p0
.end method

.method public static final c(LYA0/a;J)LRA0/h;
    .locals 15
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static/range {p0 .. p2}, LNA0/q;->b(LYA0/a;J)J

    .line 2
    .line 3
    .line 4
    move-result-wide v4

    .line 5
    invoke-virtual {p0}, LYA0/a;->z()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget-object v1, Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;->ADD_TIME:Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;

    .line 10
    .line 11
    invoke-static {v0, v1}, LNA0/q;->a(Ljava/util/List;Lorg/xbet/sportgame/core/domain/models/gamedetails/StatisticItemModel$Key;)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-virtual {p0}, LYA0/a;->s()Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {p0}, LYA0/a;->J()Z

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    invoke-virtual {p0}, LYA0/a;->K()J

    .line 24
    .line 25
    .line 26
    move-result-wide v6

    .line 27
    invoke-virtual {p0}, LYA0/a;->M()J

    .line 28
    .line 29
    .line 30
    move-result-wide v8

    .line 31
    invoke-virtual {p0}, LYA0/a;->q()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_0

    .line 36
    .line 37
    invoke-virtual {p0}, LYA0/a;->L()Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    move v12, v0

    .line 42
    goto :goto_0

    .line 43
    :cond_0
    const/4 v0, 0x1

    .line 44
    const/4 v12, 0x1

    .line 45
    :goto_0
    invoke-virtual {p0}, LYA0/a;->q()Z

    .line 46
    .line 47
    .line 48
    move-result v13

    .line 49
    new-instance v0, LRA0/h;

    .line 50
    .line 51
    const/4 v14, 0x0

    .line 52
    move-wide v10, v4

    .line 53
    invoke-direct/range {v0 .. v14}, LRA0/h;-><init>(ZZLjava/lang/String;JJJJZZLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 54
    .line 55
    .line 56
    return-object v0
.end method
