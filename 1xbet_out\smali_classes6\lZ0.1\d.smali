.class public final LlZ0/d;
.super Ljava/lang/Object;


# static fields
.field public static accordionPrimaryStyle:I = 0x7f040010

.field public static accordionSecondaryStyle:I = 0x7f040011

.field public static accordionStyle:I = 0x7f040012

.field public static accountControlStyle:I = 0x7f040013

.field public static accountCurrencyValueTextStyle:I = 0x7f040014

.field public static accountInfoSizeStyle:I = 0x7f040015

.field public static accountInfoStyle:I = 0x7f040016

.field public static accountSelectionStyle:I = 0x7f040017

.field public static accountTitleText:I = 0x7f040018

.field public static accountTitleType:I = 0x7f040019

.field public static accountValueTextStyle:I = 0x7f04001a

.field public static actionActiveIcon:I = 0x7f04001b

.field public static actionButtonTextColor:I = 0x7f040028

.field public static actionIcon:I = 0x7f04002a

.field public static actionIconTint:I = 0x7f04002c

.field public static activeTabTintColor:I = 0x7f040045

.field public static additionalPaddingStart:I = 0x7f04004c

.field public static allTimeTitle:I = 0x7f040058

.field public static allowClickWhenDisabled:I = 0x7f04005a

.field public static alpha:I = 0x7f04005f

.field public static alpha_anim_enabled:I = 0x7f040060

.field public static amount:I = 0x7f040066

.field public static amountCurrencyMargin:I = 0x7f040067

.field public static amountTextStyle:I = 0x7f040068

.field public static autoScroll:I = 0x7f040082

.field public static autoSizeEnabled:I = 0x7f040084

.field public static autoSizeMaxTextSize:I = 0x7f040085

.field public static autoSizeMinTextSize:I = 0x7f040086

.field public static autoSizeStepGranularity:I = 0x7f040088

.field public static autoSizeTextType:I = 0x7f040089

.field public static autoStart:I = 0x7f04008a

.field public static backIconColor:I = 0x7f040095

.field public static background:I = 0x7f040096

.field public static backgroundActiveTint:I = 0x7f040097

.field public static backgroundColor:I = 0x7f040098

.field public static backgroundTint:I = 0x7f0400a5

.field public static backgroundType:I = 0x7f0400a7

.field public static badge:I = 0x7f0400a9

.field public static badgeAttachGravity:I = 0x7f0400aa

.field public static badgeHorizontalOffset:I = 0x7f0400ad

.field public static badgeRelativeSize:I = 0x7f0400af

.field public static badgeStrokeColor:I = 0x7f0400b2

.field public static badgeVerticalOffset:I = 0x7f0400b7

.field public static balanceViewGroupStyle:I = 0x7f0400c0

.field public static bannerBonusesStyle:I = 0x7f0400c1

.field public static bannerCollectionStyle:I = 0x7f0400c2

.field public static bannerType:I = 0x7f0400c3

.field public static betConstructorHeaderTagStyle:I = 0x7f0400d6

.field public static blocked:I = 0x7f0400d9

.field public static blockedStyle:I = 0x7f0400da

.field public static border:I = 0x7f0400e5

.field public static bottomBarStyle:I = 0x7f0400ec

.field public static bottomBarType:I = 0x7f0400ed

.field public static bulletListColor:I = 0x7f040107

.field public static bulletListTextStyle:I = 0x7f040108

.field public static buttonEnabled:I = 0x7f040110

.field public static buttonIcon:I = 0x7f040114

.field public static buttonIconAutoMirrored:I = 0x7f040115

.field public static buttonIconRes:I = 0x7f040117

.field public static buttonIconTint:I = 0x7f040118

.field public static buttonStyle:I = 0x7f04011c

.field public static buttonText:I = 0x7f04011e

.field public static cancelButtonIconBackground:I = 0x7f040124

.field public static cancelButtonIconTint:I = 0x7f040125

.field public static caption:I = 0x7f040126

.field public static cardButtonEnabled:I = 0x7f040129

.field public static cardButtonText:I = 0x7f04012a

.field public static cardIcon:I = 0x7f04012e

.field public static cardStatus:I = 0x7f040131

.field public static cardStatusTitle:I = 0x7f040132

.field public static cardSubtitle:I = 0x7f040133

.field public static cardTitle:I = 0x7f040134

.field public static cellMenuCompactStyle:I = 0x7f040150

.field public static cellMenuStyle:I = 0x7f040151

.field public static cellMiddleCaption:I = 0x7f040152

.field public static cellMiddleCaptionTextColor:I = 0x7f040153

.field public static cellMiddleCaptionVisible:I = 0x7f040154

.field public static cellRightBannerStyle:I = 0x7f040155

.field public static cellRightButtonStyle:I = 0x7f040156

.field public static cellRightCounterStyle:I = 0x7f040157

.field public static cellRightDragAndDropStyle:I = 0x7f040158

.field public static cellRightListCheckBoxStyle:I = 0x7f040159

.field public static cellRightRadioAccordionStyle:I = 0x7f04015a

.field public static cellRightRadioButtonStyle:I = 0x7f04015b

.field public static cellRightSwitchStyle:I = 0x7f04015c

.field public static cellSettingsStyle:I = 0x7f04015d

.field public static cellShimmersStyle:I = 0x7f04015e

.field public static checked:I = 0x7f04016b

.field public static chevronColor:I = 0x7f040178

.field public static chipEndPadding:I = 0x7f04017b

.field public static chipIcon:I = 0x7f04017d

.field public static chipIconActiveTint:I = 0x7f04017e

.field public static chipIconTint:I = 0x7f040181

.field public static chipMiddleScroll:I = 0x7f040183

.field public static chipStartPadding:I = 0x7f04018a

.field public static codeChevronVisible:I = 0x7f0401b5

.field public static codeHelperText:I = 0x7f0401b6

.field public static codeHint:I = 0x7f0401b7

.field public static codePlaceholder:I = 0x7f0401b8

.field public static codeStartDrawable:I = 0x7f0401b9

.field public static codeText:I = 0x7f0401ba

.field public static coefficient:I = 0x7f0401bb

.field public static coefficientShrinkFactor:I = 0x7f0401bd

.field public static coefficientState:I = 0x7f0401be

.field public static coefficientStyle:I = 0x7f0401bf

.field public static colorsType:I = 0x7f04020e

.field public static count:I = 0x7f040240

.field public static countdownTextColor:I = 0x7f040245

.field public static counterAttachGravity:I = 0x7f040246

.field public static counterBadgeStyle:I = 0x7f040247

.field public static counterExpanded:I = 0x7f040249

.field public static counterHorizontalOffset:I = 0x7f04024a

.field public static counterStyle:I = 0x7f04024e

.field public static counterVerticalOffset:I = 0x7f040251

.field public static couponStyle:I = 0x7f040253

.field public static currency:I = 0x7f040257

.field public static customBadgeAttachGravity:I = 0x7f04025d

.field public static customBadgeHorizontalOffset:I = 0x7f04025e

.field public static customBadgeVerticalOffset:I = 0x7f04025f

.field public static defaultMiddleRightOffset:I = 0x7f04027d

.field public static description:I = 0x7f040288

.field public static descriptionStyle:I = 0x7f040289

.field public static disableAutoPadding:I = 0x7f040294

.field public static dividerColor:I = 0x7f040298

.field public static documentStatus:I = 0x7f0402a2

.field public static drawable:I = 0x7f0402a9

.field public static drawableSelector:I = 0x7f0402ae

.field public static dsAuthorizationButtonLabel:I = 0x7f0402bb

.field public static dsButtonIconRes:I = 0x7f0402bc

.field public static dsButtonLabel:I = 0x7f0402bd

.field public static dsButtonSize:I = 0x7f0402be

.field public static dsButtonStyle:I = 0x7f0402bf

.field public static dsButtonType:I = 0x7f0402c0

.field public static dsCheckBoxStyle:I = 0x7f0402c1

.field public static dsCheckedState:I = 0x7f0402c2

.field public static dsChipActionIconRes:I = 0x7f0402c3

.field public static dsChipEndPadding:I = 0x7f0402c4

.field public static dsChipLabel:I = 0x7f0402c5

.field public static dsChipLeftIconRes:I = 0x7f0402c6

.field public static dsChipRightIconRes:I = 0x7f0402c7

.field public static dsChipStartPadding:I = 0x7f0402c8

.field public static dsChipStyle:I = 0x7f0402c9

.field public static dsChipValueText:I = 0x7f0402ca

.field public static dsLottieCenterInParent:I = 0x7f0402cb

.field public static dsLottieEmptyButtonText:I = 0x7f0402cc

.field public static dsLottieEmptyCaptionText:I = 0x7f0402cd

.field public static dsLottieEmptyColorType:I = 0x7f0402ce

.field public static dsLottieEmptyIconRes:I = 0x7f0402cf

.field public static dsLottieEmptyShowButton:I = 0x7f0402d0

.field public static dsLottieEmptyShowCaption:I = 0x7f0402d1

.field public static dsLottieEmptyShowSubtitle:I = 0x7f0402d2

.field public static dsLottieEmptyShowTitle:I = 0x7f0402d3

.field public static dsLottieEmptyStyleType:I = 0x7f0402d4

.field public static dsLottieEmptySubtitleText:I = 0x7f0402d5

.field public static dsLottieEmptyTitleText:I = 0x7f0402d6

.field public static dsLottieFileName:I = 0x7f0402d7

.field public static dsNavigationBarButtonIcon:I = 0x7f0402d8

.field public static dsNavigationBarButtonStyle:I = 0x7f0402d9

.field public static dsNavigationBarDisable:I = 0x7f0402da

.field public static dsNavigationBarProfileIcon:I = 0x7f0402db

.field public static dsNavigationBarShowProfileInfo:I = 0x7f0402dc

.field public static dsRegistrationButtonLabel:I = 0x7f0402dd

.field public static dynamic:I = 0x7f0402df

.field public static edgeSpace:I = 0x7f0402e1

.field public static ellipsisValue:I = 0x7f0402ea

.field public static enableShowPreviousYear:I = 0x7f0402ee

.field public static enableShowYearsOnRegularDates:I = 0x7f0402ef

.field public static endButtonText:I = 0x7f0402f2

.field public static endDateLong:I = 0x7f0402f4

.field public static endIcon:I = 0x7f0402f7

.field public static errorEnabled:I = 0x7f04030a

.field public static errorHintColor:I = 0x7f04030b

.field public static errorIcon:I = 0x7f04030c

.field public static errorText:I = 0x7f040311

.field public static expanded:I = 0x7f04033c

.field public static fieldType:I = 0x7f040363

.field public static file_name:I = 0x7f040364

.field public static first:I = 0x7f04036a

.field public static firstButtonIcon:I = 0x7f04036c

.field public static firstButtonStyle:I = 0x7f04036d

.field public static firstButtonText:I = 0x7f04036e

.field public static fixedContainerHeightSize:I = 0x7f040370

.field public static footerStyle:I = 0x7f0403a2

.field public static gameHorizontalItemStyle:I = 0x7f0403ae

.field public static gamesToolbarStyle:I = 0x7f0403b4

.field public static halfWidthSizeForTablet:I = 0x7f0403c8

.field public static hasButton:I = 0x7f0403cc

.field public static headerCount:I = 0x7f0403cd

.field public static headerStyle:I = 0x7f0403d3

.field public static headerType:I = 0x7f0403d6

.field public static helperEndIcon:I = 0x7f0403d9

.field public static helperErrorTextColor:I = 0x7f0403da

.field public static helperShimmerVisible:I = 0x7f0403db

.field public static helperText:I = 0x7f0403dc

.field public static helperTextColor:I = 0x7f0403dd

.field public static helperTextSize:I = 0x7f0403df

.field public static hint:I = 0x7f0403ea

.field public static horizontalMargin:I = 0x7f0403f4

.field public static horizontalMarginEnd:I = 0x7f0403f5

.field public static horizontalMarginStart:I = 0x7f0403f6

.field public static icon:I = 0x7f0403fd

.field public static iconMarginEnd:I = 0x7f040408

.field public static iconMarginStart:I = 0x7f040409

.field public static iconTint:I = 0x7f040411

.field public static indicatorColor:I = 0x7f04042a

.field public static indicatorSize:I = 0x7f04042e

.field public static indicatorType:I = 0x7f040430

.field public static inverseBadgeHorizontalOffset:I = 0x7f04043a

.field public static inverseBadgeVerticalOffset:I = 0x7f04043b

.field public static inverseCounterHorizontalOffset:I = 0x7f04043c

.field public static inverseCounterVerticalOffset:I = 0x7f04043d

.field public static inverseCustomBadgeHorizontalOffset:I = 0x7f04043e

.field public static inverseCustomBadgeVerticalOffset:I = 0x7f04043f

.field public static isLoading:I = 0x7f040445

.field public static isStatic:I = 0x7f04044b

.field public static isStaticSearchField:I = 0x7f04044c

.field public static label:I = 0x7f040477

.field public static labelColor:I = 0x7f040479

.field public static labelErrorTextColor:I = 0x7f04047a

.field public static labelTextColor:I = 0x7f04047f

.field public static labelTextSize:I = 0x7f040480

.field public static labelTextStyle:I = 0x7f040481

.field public static large:I = 0x7f040486

.field public static last:I = 0x7f040488

.field public static listCheckBoxStyle:I = 0x7f0404f6

.field public static listCheckBoxType:I = 0x7f0404f7

.field public static listSpacing:I = 0x7f040507

.field public static logo:I = 0x7f04050a

.field public static marginHorizontal:I = 0x7f040527

.field public static marketIcon:I = 0x7f040534

.field public static marketIconSize:I = 0x7f040535

.field public static marketStyle:I = 0x7f040536

.field public static maxCount:I = 0x7f04056d

.field public static maxHeightDp:I = 0x7f040571

.field public static maxIndicatorsCount:I = 0x7f040573

.field public static maxLength:I = 0x7f040574

.field public static maxLines:I = 0x7f040576

.field public static maxTextSizeCoefficient:I = 0x7f04057a

.field public static maxTimeHours:I = 0x7f04057b

.field public static maxTimeMinutes:I = 0x7f04057c

.field public static maxTimeMinutesExtended:I = 0x7f04057d

.field public static maxTimeSecond:I = 0x7f04057e

.field public static menuCellSubtitleMaxLines:I = 0x7f040587

.field public static menuCompactSubtitleMaxLines:I = 0x7f040588

.field public static menuCompactTitleMaxLines:I = 0x7f040589

.field public static menuCompactTitleMinLines:I = 0x7f04058a

.field public static middleScroll:I = 0x7f04058d

.field public static minCount:I = 0x7f040591

.field public static minTextSizeCoefficient:I = 0x7f040596

.field public static navigationBarItemStyle:I = 0x7f0405db

.field public static navigationBarStyle:I = 0x7f0405dc

.field public static navigationIconTint:I = 0x7f0405df

.field public static number:I = 0x7f04060f

.field public static order_first:I = 0x7f040625

.field public static order_last:I = 0x7f040626

.field public static overlayBackground:I = 0x7f04062b

.field public static pageControlStyle:I = 0x7f040637

.field public static password:I = 0x7f04063f

.field public static phoneHelperText:I = 0x7f04064e

.field public static phoneHint:I = 0x7f04064f

.field public static phonePlaceholder:I = 0x7f040650

.field public static phoneText:I = 0x7f040651

.field public static pinCode:I = 0x7f040652

.field public static placeholder:I = 0x7f040654

.field public static placeholderIcon:I = 0x7f040657

.field public static placeholderPicture:I = 0x7f040658

.field public static placeholderTint:I = 0x7f04065d

.field public static preTitle:I = 0x7f040677

.field public static presetButtonIcon:I = 0x7f040687

.field public static presetButtonStyle:I = 0x7f040688

.field public static presetButtonText:I = 0x7f040689

.field public static presetTextFieldHint:I = 0x7f04068a

.field public static profileIcon:I = 0x7f040695

.field public static progressBarStyle:I = 0x7f040698

.field public static promoBanner_label:I = 0x7f04069b

.field public static promoBanner_unit:I = 0x7f04069c

.field public static promoBanner_value:I = 0x7f04069d

.field public static promoBanner_valueVisible:I = 0x7f04069e

.field public static regularIndicator:I = 0x7f0406bf

.field public static removeAccountDescriptionEndPadding:I = 0x7f0406c2

.field public static removeAccountDescriptionStartPadding:I = 0x7f0406c3

.field public static reverse:I = 0x7f0406cb

.field public static reversed:I = 0x7f0406cd

.field public static rightActiveIcon:I = 0x7f0406d1

.field public static rightIcon:I = 0x7f0406d3

.field public static rollingCalendarDateBackgroundStyle:I = 0x7f0406db

.field public static rollingCalendarDateContentBackgroundStyle:I = 0x7f0406dc

.field public static rollingCalendarYearBackgroundStyle:I = 0x7f0406dd

.field public static rollingCalendarYearContentBackgroundStyle:I = 0x7f0406de

.field public static roundCorners:I = 0x7f0406e1

.field public static round_12:I = 0x7f0406e3

.field public static round_16:I = 0x7f0406e4

.field public static round_8:I = 0x7f0406e5

.field public static round_full:I = 0x7f0406e6

.field public static roundedCounterTextColor:I = 0x7f0406e7

.field public static scoreStyle:I = 0x7f0406ef

.field public static searchFieldHint:I = 0x7f0406fb

.field public static searchFieldStyle:I = 0x7f0406fc

.field public static searchFieldType:I = 0x7f0406fd

.field public static secondButtonIcon:I = 0x7f040703

.field public static secondButtonStyle:I = 0x7f040704

.field public static secondButtonText:I = 0x7f040705

.field public static secondaryText:I = 0x7f04070d

.field public static secondaryTextActiveColor:I = 0x7f04070e

.field public static secondaryTextColor:I = 0x7f04070f

.field public static segmentGroupStyle:I = 0x7f040713

.field public static segmentItemStyle:I = 0x7f040714

.field public static segmentStyle:I = 0x7f040715

.field public static selectClosestDate:I = 0x7f040716

.field public static selectedBackgroundTintColor:I = 0x7f04071b

.field public static selectedDateWithYearTintColor:I = 0x7f04071c

.field public static selectedIndicatorColor:I = 0x7f04071d

.field public static selectedMonthTintColor:I = 0x7f04071e

.field public static selectedYearTintColor:I = 0x7f04071f

.field public static selector:I = 0x7f040722

.field public static selectorColor:I = 0x7f040723

.field public static separatorStyle:I = 0x7f040727

.field public static separatorVisible:I = 0x7f040728

.field public static settingsCellSubtitleMaxLines:I = 0x7f04072a

.field public static settingsCellTitleMaxLines:I = 0x7f04072b

.field public static settingsCellTitleMinLines:I = 0x7f04072c

.field public static shimmerViewStyle:I = 0x7f040739

.field public static showAccordion:I = 0x7f040751

.field public static showAllTime:I = 0x7f040752

.field public static showBadge:I = 0x7f040756

.field public static showBlock:I = 0x7f040757

.field public static showButton:I = 0x7f040759

.field public static showButtonMore:I = 0x7f04075a

.field public static showCheckBox:I = 0x7f04075b

.field public static showCounterIcon:I = 0x7f04075c

.field public static showCoupon:I = 0x7f04075d

.field public static showError:I = 0x7f040765

.field public static showIcon:I = 0x7f040768

.field public static showLoading:I = 0x7f040769

.field public static showLongTitle:I = 0x7f04076a

.field public static showMoreStyle:I = 0x7f04076c

.field public static showOnlyYears:I = 0x7f04076e

.field public static showPopular:I = 0x7f040770

.field public static showProfileInfo:I = 0x7f040771

.field public static showSearchFiled:I = 0x7f040772

.field public static showSeparator:I = 0x7f040775

.field public static showShadow:I = 0x7f040776

.field public static showSubtitle:I = 0x7f040779

.field public static showTitle:I = 0x7f04077c

.field public static showTitleIcon:I = 0x7f04077d

.field public static showTodayBadge:I = 0x7f04077e

.field public static showToolbarSeparator:I = 0x7f04077f

.field public static showTrack:I = 0x7f040781

.field public static singleLine:I = 0x7f040799

.field public static skeletonEndMargin:I = 0x7f04079d

.field public static skeletonStartMargin:I = 0x7f04079e

.field public static skeletonStyle:I = 0x7f04079f

.field public static smallRefreshIcon:I = 0x7f0407a1

.field public static snackbarStyle:I = 0x7f0407a3

.field public static spaceBetweenIndicators:I = 0x7f040833

.field public static spaceWidth:I = 0x7f040834

.field public static startButtonText:I = 0x7f040877

.field public static startDateLong:I = 0x7f040879

.field public static startIcon:I = 0x7f04087c

.field public static startIconTint:I = 0x7f040882

.field public static startOffset:I = 0x7f040884

.field public static state_blocked:I = 0x7f04088a

.field public static state_counted:I = 0x7f04088d

.field public static state_first:I = 0x7f040890

.field public static state_higher:I = 0x7f040891

.field public static state_lower:I = 0x7f040895

.field public static state_second:I = 0x7f040897

.field public static staticNavigationBarStyle:I = 0x7f04089a

.field public static status:I = 0x7f04089b

.field public static statusIcon:I = 0x7f0408a0

.field public static statusIconTint:I = 0x7f0408a1

.field public static statusTitle:I = 0x7f0408a2

.field public static strokeColor:I = 0x7f0408a4

.field public static strokeWidth:I = 0x7f0408a5

.field public static style:I = 0x7f0408a8

.field public static subtitle:I = 0x7f0408af

.field public static subtitleAtTheTop:I = 0x7f0408b0

.field public static subtitleMaxLines:I = 0x7f0408b2

.field public static subtitleMenu:I = 0x7f0408b3

.field public static subtitleTextColor:I = 0x7f0408b5

.field public static subtitleTextStyle:I = 0x7f0408b6

.field public static subtitleVisible:I = 0x7f0408b7

.field public static subtitleVisibleAtStart:I = 0x7f0408b8

.field public static tabBarItemStyle:I = 0x7f0408d8

.field public static tabLayoutStyle:I = 0x7f0408e5

.field public static tabType:I = 0x7f0408f5

.field public static tagColor:I = 0x7f0408f8

.field public static tagStyle:I = 0x7f0408f9

.field public static tagText:I = 0x7f0408fa

.field public static text:I = 0x7f040909

.field public static textActiveColor:I = 0x7f04090a

.field public static textColor:I = 0x7f04096a

.field public static textFieldDefaultStyle:I = 0x7f040975

.field public static textFieldEndStyle:I = 0x7f040976

.field public static textFieldStyle:I = 0x7f040977

.field public static textStyle:I = 0x7f040988

.field public static text_res:I = 0x7f04098a

.field public static thirdButtonStyle:I = 0x7f040991

.field public static thirdButtonText:I = 0x7f040992

.field public static timerCaption:I = 0x7f0409c2

.field public static timerStyle:I = 0x7f0409c9

.field public static timerType:I = 0x7f0409cb

.field public static title:I = 0x7f0409cf

.field public static titleChevronColor:I = 0x7f0409d2

.field public static titleHorizontalPadding:I = 0x7f0409d7

.field public static titleMaxLines:I = 0x7f0409de

.field public static titleMenu:I = 0x7f0409df

.field public static titleMinLines:I = 0x7f0409e0

.field public static titleStyle:I = 0x7f0409e5

.field public static titleText:I = 0x7f0409e6

.field public static titleTextColor:I = 0x7f0409e8

.field public static titleTextStyle:I = 0x7f0409ea

.field public static titleVerticalPadding:I = 0x7f0409eb

.field public static toolBarStyle:I = 0x7f0409f1

.field public static topUpButtonStyle:I = 0x7f040a02

.field public static topUpButtonText:I = 0x7f040a03

.field public static uiKitAggregatorBlue:I = 0x7f040a33

.field public static uiKitAggregatorBlue0:I = 0x7f040a34

.field public static uiKitAggregatorBlue80:I = 0x7f040a35

.field public static uiKitAggregatorBrilliant:I = 0x7f040a36

.field public static uiKitAggregatorBrilliantDarkEnd:I = 0x7f040a37

.field public static uiKitAggregatorBrilliantDarkStart:I = 0x7f040a38

.field public static uiKitAggregatorBrilliantEnd:I = 0x7f040a39

.field public static uiKitAggregatorBrilliantStart:I = 0x7f040a3a

.field public static uiKitAggregatorBronze:I = 0x7f040a3b

.field public static uiKitAggregatorBronzeDarkEnd:I = 0x7f040a3c

.field public static uiKitAggregatorBronzeDarkStart:I = 0x7f040a3d

.field public static uiKitAggregatorBronzeEnd:I = 0x7f040a3e

.field public static uiKitAggregatorBronzeStart:I = 0x7f040a3f

.field public static uiKitAggregatorCooper:I = 0x7f040a40

.field public static uiKitAggregatorCooperDarkEnd:I = 0x7f040a41

.field public static uiKitAggregatorCooperDarkStart:I = 0x7f040a42

.field public static uiKitAggregatorCooperEnd:I = 0x7f040a43

.field public static uiKitAggregatorCooperStart:I = 0x7f040a44

.field public static uiKitAggregatorGold:I = 0x7f040a45

.field public static uiKitAggregatorGoldDarkEnd:I = 0x7f040a46

.field public static uiKitAggregatorGoldDarkStart:I = 0x7f040a47

.field public static uiKitAggregatorGoldEnd:I = 0x7f040a48

.field public static uiKitAggregatorGoldStart:I = 0x7f040a49

.field public static uiKitAggregatorGreen:I = 0x7f040a4a

.field public static uiKitAggregatorGreen40:I = 0x7f040a4b

.field public static uiKitAggregatorGreen80:I = 0x7f040a4c

.field public static uiKitAggregatorLavender:I = 0x7f040a4d

.field public static uiKitAggregatorMauve:I = 0x7f040a4e

.field public static uiKitAggregatorPurple:I = 0x7f040a4f

.field public static uiKitAggregatorPurple0:I = 0x7f040a50

.field public static uiKitAggregatorPurple80:I = 0x7f040a51

.field public static uiKitAggregatorRed:I = 0x7f040a52

.field public static uiKitAggregatorRed0:I = 0x7f040a53

.field public static uiKitAggregatorRed80:I = 0x7f040a54

.field public static uiKitAggregatorRuby:I = 0x7f040a55

.field public static uiKitAggregatorRubyDarkEnd:I = 0x7f040a56

.field public static uiKitAggregatorRubyDarkStart:I = 0x7f040a57

.field public static uiKitAggregatorRubyEnd:I = 0x7f040a58

.field public static uiKitAggregatorRubyStart:I = 0x7f040a59

.field public static uiKitAggregatorSand:I = 0x7f040a5a

.field public static uiKitAggregatorSapphire:I = 0x7f040a5b

.field public static uiKitAggregatorSapphireDarkEnd:I = 0x7f040a5c

.field public static uiKitAggregatorSapphireDarkStart:I = 0x7f040a5d

.field public static uiKitAggregatorSapphireEnd:I = 0x7f040a5e

.field public static uiKitAggregatorSapphireStart:I = 0x7f040a5f

.field public static uiKitAggregatorSilver:I = 0x7f040a60

.field public static uiKitAggregatorSilverDarkEnd:I = 0x7f040a61

.field public static uiKitAggregatorSilverDarkStart:I = 0x7f040a62

.field public static uiKitAggregatorSilverEnd:I = 0x7f040a63

.field public static uiKitAggregatorSilverStart:I = 0x7f040a64

.field public static uiKitAggregatorUnknown:I = 0x7f040a65

.field public static uiKitAggregatorUnknownDarkEnd:I = 0x7f040a66

.field public static uiKitAggregatorUnknownDarkStart:I = 0x7f040a67

.field public static uiKitAggregatorUnknownEnd:I = 0x7f040a68

.field public static uiKitAggregatorUnknownStart:I = 0x7f040a69

.field public static uiKitAggregatorViolet:I = 0x7f040a6a

.field public static uiKitAggregatorViolet0:I = 0x7f040a6b

.field public static uiKitAggregatorViolet40:I = 0x7f040a6c

.field public static uiKitAggregatorViolet80:I = 0x7f040a6d

.field public static uiKitAggregatorVip:I = 0x7f040a6e

.field public static uiKitAggregatorVipDarkEnd:I = 0x7f040a6f

.field public static uiKitAggregatorVipDarkStart:I = 0x7f040a70

.field public static uiKitAggregatorVipEnd:I = 0x7f040a71

.field public static uiKitAggregatorVipStart:I = 0x7f040a72

.field public static uiKitCyberBlackTon1A90:I = 0x7f040a73

.field public static uiKitCyberBlackTon2A50:I = 0x7f040a74

.field public static uiKitCyberBlueTon10:I = 0x7f040a75

.field public static uiKitCyberBlueTon11:I = 0x7f040a76

.field public static uiKitCyberBlueTon12:I = 0x7f040a77

.field public static uiKitCyberBlueTon13:I = 0x7f040a78

.field public static uiKitCyberBlueTon14:I = 0x7f040a79

.field public static uiKitCyberBlueTon15:I = 0x7f040a7a

.field public static uiKitCyberBlueTon16:I = 0x7f040a7b

.field public static uiKitCyberBlueTon17:I = 0x7f040a7c

.field public static uiKitCyberBlueTon18:I = 0x7f040a7d

.field public static uiKitCyberBlueTon19A90:I = 0x7f040a7e

.field public static uiKitCyberBlueTon1A60:I = 0x7f040a7f

.field public static uiKitCyberBlueTon2:I = 0x7f040a80

.field public static uiKitCyberBlueTon20:I = 0x7f040a81

.field public static uiKitCyberBlueTon21:I = 0x7f040a82

.field public static uiKitCyberBlueTon22:I = 0x7f040a83

.field public static uiKitCyberBlueTon23:I = 0x7f040a84

.field public static uiKitCyberBlueTon25:I = 0x7f040a85

.field public static uiKitCyberBlueTon26:I = 0x7f040a86

.field public static uiKitCyberBlueTon27:I = 0x7f040a87

.field public static uiKitCyberBlueTon28:I = 0x7f040a88

.field public static uiKitCyberBlueTon29:I = 0x7f040a89

.field public static uiKitCyberBlueTon3:I = 0x7f040a8a

.field public static uiKitCyberBlueTon30:I = 0x7f040a8b

.field public static uiKitCyberBlueTon4:I = 0x7f040a8c

.field public static uiKitCyberBlueTon5A50:I = 0x7f040a8d

.field public static uiKitCyberBlueTon6:I = 0x7f040a8e

.field public static uiKitCyberBlueTon7:I = 0x7f040a8f

.field public static uiKitCyberBlueTon8A90:I = 0x7f040a90

.field public static uiKitCyberBlueTon9:I = 0x7f040a91

.field public static uiKitCyberBrownTon1:I = 0x7f040a92

.field public static uiKitCyberBrownTon10:I = 0x7f040a93

.field public static uiKitCyberBrownTon11:I = 0x7f040a94

.field public static uiKitCyberBrownTon12:I = 0x7f040a95

.field public static uiKitCyberBrownTon13:I = 0x7f040a96

.field public static uiKitCyberBrownTon14:I = 0x7f040a97

.field public static uiKitCyberBrownTon15:I = 0x7f040a98

.field public static uiKitCyberBrownTon16:I = 0x7f040a99

.field public static uiKitCyberBrownTon2:I = 0x7f040a9a

.field public static uiKitCyberBrownTon3A80:I = 0x7f040a9b

.field public static uiKitCyberBrownTon4:I = 0x7f040a9c

.field public static uiKitCyberBrownTon5:I = 0x7f040a9d

.field public static uiKitCyberBrownTon6:I = 0x7f040a9e

.field public static uiKitCyberBrownTon7:I = 0x7f040a9f

.field public static uiKitCyberBrownTon8:I = 0x7f040aa0

.field public static uiKitCyberBrownTon9:I = 0x7f040aa1

.field public static uiKitCyberGrayTon1:I = 0x7f040aa2

.field public static uiKitCyberGrayTon2A50:I = 0x7f040aa3

.field public static uiKitCyberGrayTon3:I = 0x7f040aa4

.field public static uiKitCyberGrayTon4:I = 0x7f040aa5

.field public static uiKitCyberGrayTon5:I = 0x7f040aa6

.field public static uiKitCyberGrayTon6:I = 0x7f040aa7

.field public static uiKitCyberGrayTon7:I = 0x7f040aa8

.field public static uiKitCyberGrayTon8:I = 0x7f040aa9

.field public static uiKitCyberGrayTon9:I = 0x7f040aaa

.field public static uiKitCyberGreenTon1:I = 0x7f040aab

.field public static uiKitCyberGreenTon10:I = 0x7f040aac

.field public static uiKitCyberGreenTon11:I = 0x7f040aad

.field public static uiKitCyberGreenTon12:I = 0x7f040aae

.field public static uiKitCyberGreenTon13:I = 0x7f040aaf

.field public static uiKitCyberGreenTon14:I = 0x7f040ab0

.field public static uiKitCyberGreenTon15:I = 0x7f040ab1

.field public static uiKitCyberGreenTon16:I = 0x7f040ab2

.field public static uiKitCyberGreenTon17:I = 0x7f040ab3

.field public static uiKitCyberGreenTon18:I = 0x7f040ab4

.field public static uiKitCyberGreenTon19:I = 0x7f040ab5

.field public static uiKitCyberGreenTon2:I = 0x7f040ab6

.field public static uiKitCyberGreenTon3:I = 0x7f040ab7

.field public static uiKitCyberGreenTon4:I = 0x7f040ab8

.field public static uiKitCyberGreenTon5:I = 0x7f040ab9

.field public static uiKitCyberGreenTon6:I = 0x7f040aba

.field public static uiKitCyberGreenTon7A50:I = 0x7f040abb

.field public static uiKitCyberGreenTon8:I = 0x7f040abc

.field public static uiKitCyberGreenTon9:I = 0x7f040abd

.field public static uiKitCyberOrangeTon1:I = 0x7f040abe

.field public static uiKitCyberOrangeTon2:I = 0x7f040abf

.field public static uiKitCyberOrangeTon3:I = 0x7f040ac0

.field public static uiKitCyberOrangeTon4:I = 0x7f040ac1

.field public static uiKitCyberOrangeTon5:I = 0x7f040ac2

.field public static uiKitCyberOrangeTon6:I = 0x7f040ac3

.field public static uiKitCyberPinkTon1:I = 0x7f040ac4

.field public static uiKitCyberPinkTon2:I = 0x7f040ac5

.field public static uiKitCyberPinkTon3:I = 0x7f040ac6

.field public static uiKitCyberPinkTon4:I = 0x7f040ac7

.field public static uiKitCyberPurpleTon1:I = 0x7f040ac8

.field public static uiKitCyberPurpleTon10:I = 0x7f040ac9

.field public static uiKitCyberPurpleTon11:I = 0x7f040aca

.field public static uiKitCyberPurpleTon12A70:I = 0x7f040acb

.field public static uiKitCyberPurpleTon13:I = 0x7f040acc

.field public static uiKitCyberPurpleTon14:I = 0x7f040acd

.field public static uiKitCyberPurpleTon15:I = 0x7f040ace

.field public static uiKitCyberPurpleTon16:I = 0x7f040acf

.field public static uiKitCyberPurpleTon2:I = 0x7f040ad0

.field public static uiKitCyberPurpleTon3:I = 0x7f040ad1

.field public static uiKitCyberPurpleTon4:I = 0x7f040ad2

.field public static uiKitCyberPurpleTon5:I = 0x7f040ad3

.field public static uiKitCyberPurpleTon6:I = 0x7f040ad4

.field public static uiKitCyberPurpleTon7:I = 0x7f040ad5

.field public static uiKitCyberPurpleTon8:I = 0x7f040ad6

.field public static uiKitCyberPurpleTon9:I = 0x7f040ad7

.field public static uiKitCyberRedTon1:I = 0x7f040ad8

.field public static uiKitCyberRedTon10:I = 0x7f040ad9

.field public static uiKitCyberRedTon11:I = 0x7f040ada

.field public static uiKitCyberRedTon12A50:I = 0x7f040adb

.field public static uiKitCyberRedTon13:I = 0x7f040adc

.field public static uiKitCyberRedTon14A90:I = 0x7f040add

.field public static uiKitCyberRedTon15:I = 0x7f040ade

.field public static uiKitCyberRedTon16:I = 0x7f040adf

.field public static uiKitCyberRedTon17:I = 0x7f040ae0

.field public static uiKitCyberRedTon18:I = 0x7f040ae1

.field public static uiKitCyberRedTon2:I = 0x7f040ae2

.field public static uiKitCyberRedTon3:I = 0x7f040ae3

.field public static uiKitCyberRedTon4:I = 0x7f040ae4

.field public static uiKitCyberRedTon5A50:I = 0x7f040ae5

.field public static uiKitCyberRedTon6:I = 0x7f040ae6

.field public static uiKitCyberRedTon7:I = 0x7f040ae7

.field public static uiKitCyberRedTon8:I = 0x7f040ae8

.field public static uiKitCyberRedTon9A10:I = 0x7f040ae9

.field public static uiKitCyberYellowTon1:I = 0x7f040aea

.field public static uiKitCyberYellowTon2:I = 0x7f040aeb

.field public static uiKitCyberYellowTon3:I = 0x7f040aec

.field public static uiKitCyberYellowTon4:I = 0x7f040aed

.field public static uikitBackground:I = 0x7f040aee

.field public static uikitBackground0:I = 0x7f040aef

.field public static uikitBackgroundContent:I = 0x7f040af0

.field public static uikitBackgroundContent0:I = 0x7f040af1

.field public static uikitBackgroundDark:I = 0x7f040af2

.field public static uikitBackgroundGroup:I = 0x7f040af3

.field public static uikitBackgroundGroupSecondary:I = 0x7f040af4

.field public static uikitBackgroundLight60:I = 0x7f040af5

.field public static uikitCoefHigher:I = 0x7f040af6

.field public static uikitCoefLower:I = 0x7f040af7

.field public static uikitCommerce:I = 0x7f040af8

.field public static uikitCommerceForeground:I = 0x7f040af9

.field public static uikitCommerceForeground80:I = 0x7f040afa

.field public static uikitCommerceHighlight:I = 0x7f040afb

.field public static uikitCommerceTint:I = 0x7f040afc

.field public static uikitCustomButtonBonusGradientEnd:I = 0x7f040afd

.field public static uikitCustomButtonBonusGradientStart:I = 0x7f040afe

.field public static uikitCustomItsme:I = 0x7f040aff

.field public static uikitCustomPictureSBonusGradient:I = 0x7f040b00

.field public static uikitCustomPromoGradientEnd:I = 0x7f040b01

.field public static uikitCustomPromoGradientStart:I = 0x7f040b02

.field public static uikitCustomQatar:I = 0x7f040b03

.field public static uikitFixedTabsHeight:I = 0x7f040b04

.field public static uikitInputBackground60:I = 0x7f040b05

.field public static uikitPrimary:I = 0x7f040b06

.field public static uikitPrimary0:I = 0x7f040b07

.field public static uikitPrimary60:I = 0x7f040b08

.field public static uikitPrimary80:I = 0x7f040b09

.field public static uikitPrimaryForeground:I = 0x7f040b0a

.field public static uikitPrimaryForeground80:I = 0x7f040b0b

.field public static uikitPrimaryHighlight:I = 0x7f040b0c

.field public static uikitPromoBackground:I = 0x7f040b0d

.field public static uikitPromoCardGradientEnd:I = 0x7f040b0e

.field public static uikitPromoCardGradientStart:I = 0x7f040b0f

.field public static uikitRipple:I = 0x7f040b10

.field public static uikitScrollableTabsHeight:I = 0x7f040b11

.field public static uikitSecondary:I = 0x7f040b12

.field public static uikitSecondary10:I = 0x7f040b13

.field public static uikitSecondary20:I = 0x7f040b14

.field public static uikitSecondary40:I = 0x7f040b15

.field public static uikitSecondary60:I = 0x7f040b16

.field public static uikitSecondaryButtonBackground:I = 0x7f040b17

.field public static uikitSecondaryButtonBackgroundHighlight:I = 0x7f040b18

.field public static uikitSecondaryButtonForeground:I = 0x7f040b19

.field public static uikitSeparator:I = 0x7f040b1a

.field public static uikitSeparator60:I = 0x7f040b1b

.field public static uikitShadowL:I = 0x7f040b1c

.field public static uikitShadowM:I = 0x7f040b1d

.field public static uikitShadowS:I = 0x7f040b1e

.field public static uikitShadowXs:I = 0x7f040b1f

.field public static uikitSnackbarAltBackground:I = 0x7f040b20

.field public static uikitSocialNetworksVerticalBackground:I = 0x7f040b21

.field public static uikitSplashBackground:I = 0x7f040b22

.field public static uikitStaticBlack:I = 0x7f040b23

.field public static uikitStaticBlack10:I = 0x7f040b24

.field public static uikitStaticBlack20:I = 0x7f040b25

.field public static uikitStaticBlack30:I = 0x7f040b26

.field public static uikitStaticBlack40:I = 0x7f040b27

.field public static uikitStaticBlack60:I = 0x7f040b28

.field public static uikitStaticBlack80:I = 0x7f040b29

.field public static uikitStaticBlue:I = 0x7f040b2a

.field public static uikitStaticDarkBlue:I = 0x7f040b2b

.field public static uikitStaticDarkBlue10:I = 0x7f040b2c

.field public static uikitStaticDarkBlue20:I = 0x7f040b2d

.field public static uikitStaticDarkBlue30:I = 0x7f040b2e

.field public static uikitStaticDarkBlue40:I = 0x7f040b2f

.field public static uikitStaticDarkGrey:I = 0x7f040b30

.field public static uikitStaticDarkOrange:I = 0x7f040b31

.field public static uikitStaticDarkOrange10:I = 0x7f040b32

.field public static uikitStaticDarkOrange20:I = 0x7f040b33

.field public static uikitStaticDarkOrange30:I = 0x7f040b34

.field public static uikitStaticDarkOrange40:I = 0x7f040b35

.field public static uikitStaticDarkPink:I = 0x7f040b36

.field public static uikitStaticGamesCoefficient:I = 0x7f040b37

.field public static uikitStaticGamesTextVictory:I = 0x7f040b38

.field public static uikitStaticGray:I = 0x7f040b39

.field public static uikitStaticGray40:I = 0x7f040b3a

.field public static uikitStaticGreen:I = 0x7f040b3b

.field public static uikitStaticGreen10:I = 0x7f040b3c

.field public static uikitStaticGreen20:I = 0x7f040b3d

.field public static uikitStaticGreen30:I = 0x7f040b3e

.field public static uikitStaticGreen40:I = 0x7f040b3f

.field public static uikitStaticLightBrown:I = 0x7f040b40

.field public static uikitStaticOrange:I = 0x7f040b41

.field public static uikitStaticOrange10:I = 0x7f040b42

.field public static uikitStaticOrange20:I = 0x7f040b43

.field public static uikitStaticOrange30:I = 0x7f040b44

.field public static uikitStaticOrange40:I = 0x7f040b45

.field public static uikitStaticOverlayBackground40:I = 0x7f040b46

.field public static uikitStaticPink:I = 0x7f040b47

.field public static uikitStaticPurple:I = 0x7f040b48

.field public static uikitStaticRed:I = 0x7f040b49

.field public static uikitStaticRed10:I = 0x7f040b4a

.field public static uikitStaticRed20:I = 0x7f040b4b

.field public static uikitStaticRed30:I = 0x7f040b4c

.field public static uikitStaticRed40:I = 0x7f040b4d

.field public static uikitStaticTeal:I = 0x7f040b4e

.field public static uikitStaticTransparent:I = 0x7f040b4f

.field public static uikitStaticViolet:I = 0x7f040b50

.field public static uikitStaticWhite:I = 0x7f040b51

.field public static uikitStaticWhite10:I = 0x7f040b52

.field public static uikitStaticWhite20:I = 0x7f040b53

.field public static uikitStaticWhite30:I = 0x7f040b54

.field public static uikitStaticWhite40:I = 0x7f040b55

.field public static uikitStaticWhite60:I = 0x7f040b56

.field public static uikitStaticWhite80:I = 0x7f040b57

.field public static uikitStaticYellow:I = 0x7f040b58

.field public static uikitStaticYellow10:I = 0x7f040b59

.field public static uikitStaticYellow20:I = 0x7f040b5a

.field public static uikitStaticYellow30:I = 0x7f040b5b

.field public static uikitStaticYellow40:I = 0x7f040b5c

.field public static uikitSwitchOff:I = 0x7f040b5d

.field public static uikitTextPrimary:I = 0x7f040b5e

.field public static uikitToolbarLogo:I = 0x7f040b60

.field public static uikitUpdateGradientEnd:I = 0x7f040b61

.field public static uikitUpdateGradientStart:I = 0x7f040b62

.field public static uikitWarning:I = 0x7f040b63

.field public static uikitWarningTint:I = 0x7f040b64

.field public static uikitWarningTintHighlight:I = 0x7f040b65

.field public static uikitWebGamesBackground:I = 0x7f040b66

.field public static uikitWebGamesControlBackground:I = 0x7f040b67

.field public static uikitWebGamesShimmer:I = 0x7f040b68

.field public static uikitWidgetBackground:I = 0x7f040b69

.field public static uikitWidgetContent:I = 0x7f040b6a

.field public static uikitWidgetIcon:I = 0x7f040b6b

.field public static uikitWidgetText:I = 0x7f040b6c

.field public static uikitWidgetUpdateButton:I = 0x7f040b6d

.field public static unchangeableIsSelected:I = 0x7f040b6e

.field public static unchangeableOnCLick:I = 0x7f040b6f

.field public static unit:I = 0x7f040b70

.field public static unselectedBackgroundTintColor:I = 0x7f040b72

.field public static updateFivefoldIconStyle:I = 0x7f040b74

.field public static updateIconType:I = 0x7f040b75

.field public static url:I = 0x7f040b77

.field public static verticalBias:I = 0x7f040b88

.field public static winColor:I = 0x7f040bb9

.field public static winIndicator:I = 0x7f040bbc


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
