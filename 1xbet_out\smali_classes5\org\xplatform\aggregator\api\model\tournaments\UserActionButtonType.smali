.class public final enum Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u000b\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "CanParticipate",
        "AlreadyParticipating",
        "HowToParticipate",
        "Blocked",
        "Details",
        "Games",
        "Game",
        "None",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum AlreadyParticipating:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum Blocked:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum CanParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum Details:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum Game:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum Games:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum HowToParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

.field public static final enum None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 2
    .line 3
    const-string v1, "CanParticipate"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->CanParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 12
    .line 13
    const-string v1, "AlreadyParticipating"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->AlreadyParticipating:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 20
    .line 21
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 22
    .line 23
    const-string v1, "HowToParticipate"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->HowToParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 32
    .line 33
    const-string v1, "Blocked"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Blocked:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 40
    .line 41
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 42
    .line 43
    const-string v1, "Details"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Details:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 50
    .line 51
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 52
    .line 53
    const-string v1, "Games"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Games:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 62
    .line 63
    const-string v1, "Game"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Game:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 70
    .line 71
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 72
    .line 73
    const-string v1, "None"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 80
    .line 81
    invoke-static {}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->a()[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->$VALUES:[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 86
    .line 87
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    sput-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->$ENTRIES:Lkotlin/enums/a;

    .line 92
    .line 93
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
    .locals 3

    .line 1
    const/16 v0, 0x8

    new-array v0, v0, [Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->CanParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->AlreadyParticipating:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->HowToParticipate:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Blocked:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Details:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Games:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->Game:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->$VALUES:[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 8
    .line 9
    return-object v0
.end method
