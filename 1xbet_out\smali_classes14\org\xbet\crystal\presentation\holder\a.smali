.class public final synthetic Lorg/xbet/crystal/presentation/holder/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/crystal/presentation/holder/CrystalFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/crystal/presentation/holder/CrystalFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/crystal/presentation/holder/a;->a:Lorg/xbet/crystal/presentation/holder/CrystalFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/holder/a;->a:Lorg/xbet/crystal/presentation/holder/CrystalFragment;

    invoke-static {v0}, Lorg/xbet/crystal/presentation/holder/CrystalFragment;->f4(Lorg/xbet/crystal/presentation/holder/CrystalFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
