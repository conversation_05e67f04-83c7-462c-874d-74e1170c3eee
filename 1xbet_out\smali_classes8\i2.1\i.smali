.class public abstract Li2/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Li2/i$b;,
        Li2/i$c;
    }
.end annotation


# instance fields
.field public final a:Li2/e;

.field public b:LN1/T;

.field public c:LN1/t;

.field public d:Li2/g;

.field public e:J

.field public f:J

.field public g:J

.field public h:I

.field public i:I

.field public j:Li2/i$b;

.field public k:J

.field public l:Z

.field public m:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Li2/e;

    .line 5
    .line 6
    invoke-direct {v0}, Li2/e;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Li2/i;->a:Li2/e;

    .line 10
    .line 11
    new-instance v0, Li2/i$b;

    .line 12
    .line 13
    invoke-direct {v0}, Li2/i$b;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Li2/i;->j:Li2/i$b;

    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 1

    .line 1
    iget-object v0, p0, Li2/i;->b:LN1/T;

    .line 2
    .line 3
    invoke-static {v0}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Li2/i;->c:LN1/t;

    .line 7
    .line 8
    invoke-static {v0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public b(J)J
    .locals 2

    .line 1
    const-wide/32 v0, 0xf4240

    .line 2
    .line 3
    .line 4
    mul-long p1, p1, v0

    .line 5
    .line 6
    iget v0, p0, Li2/i;->i:I

    .line 7
    .line 8
    int-to-long v0, v0

    .line 9
    div-long/2addr p1, v0

    .line 10
    return-wide p1
.end method

.method public c(J)J
    .locals 2

    .line 1
    iget v0, p0, Li2/i;->i:I

    .line 2
    .line 3
    int-to-long v0, v0

    .line 4
    mul-long v0, v0, p1

    .line 5
    .line 6
    const-wide/32 p1, 0xf4240

    .line 7
    .line 8
    .line 9
    div-long/2addr v0, p1

    .line 10
    return-wide v0
.end method

.method public d(LN1/t;LN1/T;)V
    .locals 0

    .line 1
    iput-object p1, p0, Li2/i;->c:LN1/t;

    .line 2
    .line 3
    iput-object p2, p0, Li2/i;->b:LN1/T;

    .line 4
    .line 5
    const/4 p1, 0x1

    .line 6
    invoke-virtual {p0, p1}, Li2/i;->l(Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public e(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Li2/i;->g:J

    .line 2
    .line 3
    return-void
.end method

.method public abstract f(Lt1/G;)J
.end method

.method public final g(LN1/s;LN1/L;)I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li2/i;->a()V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Li2/i;->h:I

    .line 5
    .line 6
    if-eqz v0, :cond_3

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    const/4 v2, 0x2

    .line 10
    if-eq v0, v1, :cond_2

    .line 11
    .line 12
    if-eq v0, v2, :cond_1

    .line 13
    .line 14
    const/4 p1, 0x3

    .line 15
    if-ne v0, p1, :cond_0

    .line 16
    .line 17
    const/4 p1, -0x1

    .line 18
    return p1

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    iget-object v0, p0, Li2/i;->d:Li2/g;

    .line 26
    .line 27
    invoke-static {v0}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0, p1, p2}, Li2/i;->k(LN1/s;LN1/L;)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    return p1

    .line 35
    :cond_2
    iget-wide v0, p0, Li2/i;->f:J

    .line 36
    .line 37
    long-to-int p2, v0

    .line 38
    invoke-interface {p1, p2}, LN1/s;->n(I)V

    .line 39
    .line 40
    .line 41
    iput v2, p0, Li2/i;->h:I

    .line 42
    .line 43
    const/4 p1, 0x0

    .line 44
    return p1

    .line 45
    :cond_3
    invoke-virtual {p0, p1}, Li2/i;->j(LN1/s;)I

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    return p1
.end method

.method public final h(LN1/s;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    :goto_0
    iget-object v0, p0, Li2/i;->a:Li2/e;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Li2/e;->d(LN1/s;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x3

    .line 10
    iput p1, p0, Li2/i;->h:I

    .line 11
    .line 12
    const/4 p1, 0x0

    .line 13
    return p1

    .line 14
    :cond_0
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 15
    .line 16
    .line 17
    move-result-wide v0

    .line 18
    iget-wide v2, p0, Li2/i;->f:J

    .line 19
    .line 20
    sub-long/2addr v0, v2

    .line 21
    iput-wide v0, p0, Li2/i;->k:J

    .line 22
    .line 23
    iget-object v0, p0, Li2/i;->a:Li2/e;

    .line 24
    .line 25
    invoke-virtual {v0}, Li2/e;->c()Lt1/G;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iget-wide v1, p0, Li2/i;->f:J

    .line 30
    .line 31
    iget-object v3, p0, Li2/i;->j:Li2/i$b;

    .line 32
    .line 33
    invoke-virtual {p0, v0, v1, v2, v3}, Li2/i;->i(Lt1/G;JLi2/i$b;)Z

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    invoke-interface {p1}, LN1/s;->getPosition()J

    .line 40
    .line 41
    .line 42
    move-result-wide v0

    .line 43
    iput-wide v0, p0, Li2/i;->f:J

    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_1
    const/4 p1, 0x1

    .line 47
    return p1
.end method

.method public abstract i(Lt1/G;JLi2/i$b;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public final j(LN1/s;)I
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p1}, Li2/i;->h(LN1/s;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    const/4 v0, -0x1

    .line 8
    return v0

    .line 9
    :cond_0
    iget-object v0, p0, Li2/i;->j:Li2/i$b;

    .line 10
    .line 11
    iget-object v0, v0, Li2/i$b;->a:Landroidx/media3/common/r;

    .line 12
    .line 13
    iget v2, v0, Landroidx/media3/common/r;->F:I

    .line 14
    .line 15
    iput v2, p0, Li2/i;->i:I

    .line 16
    .line 17
    iget-boolean v2, p0, Li2/i;->m:Z

    .line 18
    .line 19
    const/4 v3, 0x1

    .line 20
    if-nez v2, :cond_1

    .line 21
    .line 22
    iget-object v2, p0, Li2/i;->b:LN1/T;

    .line 23
    .line 24
    invoke-interface {v2, v0}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 25
    .line 26
    .line 27
    iput-boolean v3, p0, Li2/i;->m:Z

    .line 28
    .line 29
    :cond_1
    iget-object v0, p0, Li2/i;->j:Li2/i$b;

    .line 30
    .line 31
    iget-object v0, v0, Li2/i$b;->b:Li2/g;

    .line 32
    .line 33
    const/4 v11, 0x0

    .line 34
    if-eqz v0, :cond_2

    .line 35
    .line 36
    iput-object v0, p0, Li2/i;->d:Li2/g;

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_2
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 40
    .line 41
    .line 42
    move-result-wide v4

    .line 43
    const-wide/16 v6, -0x1

    .line 44
    .line 45
    cmp-long v0, v4, v6

    .line 46
    .line 47
    if-nez v0, :cond_3

    .line 48
    .line 49
    new-instance v0, Li2/i$c;

    .line 50
    .line 51
    const/4 v2, 0x0

    .line 52
    invoke-direct {v0, v2}, Li2/i$c;-><init>(Li2/i$a;)V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Li2/i;->d:Li2/g;

    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_3
    iget-object v0, p0, Li2/i;->a:Li2/e;

    .line 59
    .line 60
    invoke-virtual {v0}, Li2/e;->b()Li2/f;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget v2, v0, Li2/f;->b:I

    .line 65
    .line 66
    and-int/lit8 v2, v2, 0x4

    .line 67
    .line 68
    if-eqz v2, :cond_4

    .line 69
    .line 70
    const/4 v10, 0x1

    .line 71
    goto :goto_0

    .line 72
    :cond_4
    const/4 v10, 0x0

    .line 73
    :goto_0
    new-instance v2, Li2/a;

    .line 74
    .line 75
    move-object v4, v2

    .line 76
    iget-wide v2, p0, Li2/i;->f:J

    .line 77
    .line 78
    invoke-interface {p1}, LN1/s;->getLength()J

    .line 79
    .line 80
    .line 81
    move-result-wide v5

    .line 82
    iget v7, v0, Li2/f;->h:I

    .line 83
    .line 84
    iget v8, v0, Li2/f;->i:I

    .line 85
    .line 86
    add-int/2addr v7, v8

    .line 87
    int-to-long v7, v7

    .line 88
    iget-wide v12, v0, Li2/f;->c:J

    .line 89
    .line 90
    move-object v1, p0

    .line 91
    move-object v0, v4

    .line 92
    move-wide v4, v5

    .line 93
    move-wide v6, v7

    .line 94
    move-wide v8, v12

    .line 95
    invoke-direct/range {v0 .. v10}, Li2/a;-><init>(Li2/i;JJJJZ)V

    .line 96
    .line 97
    .line 98
    iput-object v0, p0, Li2/i;->d:Li2/g;

    .line 99
    .line 100
    :goto_1
    const/4 v0, 0x2

    .line 101
    iput v0, p0, Li2/i;->h:I

    .line 102
    .line 103
    iget-object v0, p0, Li2/i;->a:Li2/e;

    .line 104
    .line 105
    invoke-virtual {v0}, Li2/e;->f()V

    .line 106
    .line 107
    .line 108
    return v11
.end method

.method public final k(LN1/s;LN1/L;)I
    .locals 16
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    iget-object v2, v0, Li2/i;->d:Li2/g;

    .line 6
    .line 7
    invoke-interface {v2, v1}, Li2/g;->a(LN1/s;)J

    .line 8
    .line 9
    .line 10
    move-result-wide v2

    .line 11
    const/4 v4, 0x1

    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    cmp-long v7, v2, v5

    .line 15
    .line 16
    if-ltz v7, :cond_0

    .line 17
    .line 18
    move-object/from16 v7, p2

    .line 19
    .line 20
    iput-wide v2, v7, LN1/L;->a:J

    .line 21
    .line 22
    return v4

    .line 23
    :cond_0
    const-wide/16 v7, -0x1

    .line 24
    .line 25
    cmp-long v9, v2, v7

    .line 26
    .line 27
    if-gez v9, :cond_1

    .line 28
    .line 29
    const-wide/16 v9, 0x2

    .line 30
    .line 31
    add-long/2addr v2, v9

    .line 32
    neg-long v2, v2

    .line 33
    invoke-virtual {v0, v2, v3}, Li2/i;->e(J)V

    .line 34
    .line 35
    .line 36
    :cond_1
    iget-boolean v2, v0, Li2/i;->l:Z

    .line 37
    .line 38
    if-nez v2, :cond_2

    .line 39
    .line 40
    iget-object v2, v0, Li2/i;->d:Li2/g;

    .line 41
    .line 42
    invoke-interface {v2}, Li2/g;->b()LN1/M;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-static {v2}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    check-cast v2, LN1/M;

    .line 51
    .line 52
    iget-object v3, v0, Li2/i;->c:LN1/t;

    .line 53
    .line 54
    invoke-interface {v3, v2}, LN1/t;->q(LN1/M;)V

    .line 55
    .line 56
    .line 57
    iget-object v3, v0, Li2/i;->b:LN1/T;

    .line 58
    .line 59
    invoke-interface {v2}, LN1/M;->l()J

    .line 60
    .line 61
    .line 62
    move-result-wide v9

    .line 63
    invoke-interface {v3, v9, v10}, LN1/T;->b(J)V

    .line 64
    .line 65
    .line 66
    iput-boolean v4, v0, Li2/i;->l:Z

    .line 67
    .line 68
    :cond_2
    iget-wide v2, v0, Li2/i;->k:J

    .line 69
    .line 70
    cmp-long v4, v2, v5

    .line 71
    .line 72
    if-gtz v4, :cond_4

    .line 73
    .line 74
    iget-object v2, v0, Li2/i;->a:Li2/e;

    .line 75
    .line 76
    invoke-virtual {v2, v1}, Li2/e;->d(LN1/s;)Z

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    if-eqz v1, :cond_3

    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_3
    const/4 v1, 0x3

    .line 84
    iput v1, v0, Li2/i;->h:I

    .line 85
    .line 86
    const/4 v1, -0x1

    .line 87
    return v1

    .line 88
    :cond_4
    :goto_0
    iput-wide v5, v0, Li2/i;->k:J

    .line 89
    .line 90
    iget-object v1, v0, Li2/i;->a:Li2/e;

    .line 91
    .line 92
    invoke-virtual {v1}, Li2/e;->c()Lt1/G;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-virtual {v0, v1}, Li2/i;->f(Lt1/G;)J

    .line 97
    .line 98
    .line 99
    move-result-wide v2

    .line 100
    cmp-long v4, v2, v5

    .line 101
    .line 102
    if-ltz v4, :cond_5

    .line 103
    .line 104
    iget-wide v4, v0, Li2/i;->g:J

    .line 105
    .line 106
    add-long v9, v4, v2

    .line 107
    .line 108
    iget-wide v11, v0, Li2/i;->e:J

    .line 109
    .line 110
    cmp-long v6, v9, v11

    .line 111
    .line 112
    if-ltz v6, :cond_5

    .line 113
    .line 114
    invoke-virtual {v0, v4, v5}, Li2/i;->b(J)J

    .line 115
    .line 116
    .line 117
    move-result-wide v10

    .line 118
    iget-object v4, v0, Li2/i;->b:LN1/T;

    .line 119
    .line 120
    invoke-virtual {v1}, Lt1/G;->g()I

    .line 121
    .line 122
    .line 123
    move-result v5

    .line 124
    invoke-interface {v4, v1, v5}, LN1/T;->a(Lt1/G;I)V

    .line 125
    .line 126
    .line 127
    iget-object v9, v0, Li2/i;->b:LN1/T;

    .line 128
    .line 129
    invoke-virtual {v1}, Lt1/G;->g()I

    .line 130
    .line 131
    .line 132
    move-result v13

    .line 133
    const/4 v14, 0x0

    .line 134
    const/4 v15, 0x0

    .line 135
    const/4 v12, 0x1

    .line 136
    invoke-interface/range {v9 .. v15}, LN1/T;->d(JIIILN1/T$a;)V

    .line 137
    .line 138
    .line 139
    iput-wide v7, v0, Li2/i;->e:J

    .line 140
    .line 141
    :cond_5
    iget-wide v4, v0, Li2/i;->g:J

    .line 142
    .line 143
    add-long/2addr v4, v2

    .line 144
    iput-wide v4, v0, Li2/i;->g:J

    .line 145
    .line 146
    const/4 v1, 0x0

    .line 147
    return v1
.end method

.method public l(Z)V
    .locals 4

    .line 1
    const-wide/16 v0, 0x0

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    new-instance p1, Li2/i$b;

    .line 6
    .line 7
    invoke-direct {p1}, Li2/i$b;-><init>()V

    .line 8
    .line 9
    .line 10
    iput-object p1, p0, Li2/i;->j:Li2/i$b;

    .line 11
    .line 12
    iput-wide v0, p0, Li2/i;->f:J

    .line 13
    .line 14
    const/4 p1, 0x0

    .line 15
    iput p1, p0, Li2/i;->h:I

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x1

    .line 19
    iput p1, p0, Li2/i;->h:I

    .line 20
    .line 21
    :goto_0
    const-wide/16 v2, -0x1

    .line 22
    .line 23
    iput-wide v2, p0, Li2/i;->e:J

    .line 24
    .line 25
    iput-wide v0, p0, Li2/i;->g:J

    .line 26
    .line 27
    return-void
.end method

.method public final m(JJ)V
    .locals 3

    .line 1
    iget-object v0, p0, Li2/i;->a:Li2/e;

    .line 2
    .line 3
    invoke-virtual {v0}, Li2/e;->e()V

    .line 4
    .line 5
    .line 6
    const-wide/16 v0, 0x0

    .line 7
    .line 8
    cmp-long v2, p1, v0

    .line 9
    .line 10
    if-nez v2, :cond_0

    .line 11
    .line 12
    iget-boolean p1, p0, Li2/i;->l:Z

    .line 13
    .line 14
    xor-int/lit8 p1, p1, 0x1

    .line 15
    .line 16
    invoke-virtual {p0, p1}, Li2/i;->l(Z)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_0
    iget p1, p0, Li2/i;->h:I

    .line 21
    .line 22
    if-eqz p1, :cond_1

    .line 23
    .line 24
    invoke-virtual {p0, p3, p4}, Li2/i;->c(J)J

    .line 25
    .line 26
    .line 27
    move-result-wide p1

    .line 28
    iput-wide p1, p0, Li2/i;->e:J

    .line 29
    .line 30
    iget-object p1, p0, Li2/i;->d:Li2/g;

    .line 31
    .line 32
    invoke-static {p1}, Lt1/a0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    check-cast p1, Li2/g;

    .line 37
    .line 38
    iget-wide p2, p0, Li2/i;->e:J

    .line 39
    .line 40
    invoke-interface {p1, p2, p3}, Li2/g;->c(J)V

    .line 41
    .line 42
    .line 43
    const/4 p1, 0x2

    .line 44
    iput p1, p0, Li2/i;->h:I

    .line 45
    .line 46
    :cond_1
    return-void
.end method
