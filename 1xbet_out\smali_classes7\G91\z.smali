.class public final synthetic LG91/z;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/z;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LG91/z;->b:Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LG91/z;->a:L<PERSON><PERSON>/jvm/functions/Function1;

    iget-object v1, p0, LG91/z;->b:Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeProviderChipsAdapterDelegateKt;->a(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Landroid/view/View;)V

    return-void
.end method
