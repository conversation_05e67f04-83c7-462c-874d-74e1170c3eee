.class public final LML0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LNL0/b;",
        "",
        "isHideStadiumInHeader",
        "LPL0/b;",
        "a",
        "(LNL0/b;Z)LPL0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LNL0/b;Z)LPL0/b;
    .locals 26
    .param p0    # LNL0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LNL0/b;->p()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, ""

    .line 6
    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    move-object v3, v1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object v3, v0

    .line 12
    :goto_0
    invoke-virtual/range {p0 .. p0}, LNL0/b;->b()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-nez v0, :cond_1

    .line 17
    .line 18
    move-object v4, v1

    .line 19
    goto :goto_1

    .line 20
    :cond_1
    move-object v4, v0

    .line 21
    :goto_1
    invoke-virtual/range {p0 .. p0}, LNL0/b;->h()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-nez v0, :cond_2

    .line 26
    .line 27
    move-object v5, v1

    .line 28
    goto :goto_2

    .line 29
    :cond_2
    move-object v5, v0

    .line 30
    :goto_2
    invoke-virtual/range {p0 .. p0}, LNL0/b;->a()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    if-nez v0, :cond_3

    .line 35
    .line 36
    move-object v6, v1

    .line 37
    goto :goto_3

    .line 38
    :cond_3
    move-object v6, v0

    .line 39
    :goto_3
    invoke-virtual/range {p0 .. p0}, LNL0/b;->v()Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    if-nez v0, :cond_4

    .line 44
    .line 45
    move-object v7, v1

    .line 46
    goto :goto_4

    .line 47
    :cond_4
    move-object v7, v0

    .line 48
    :goto_4
    invoke-virtual/range {p0 .. p0}, LNL0/b;->t()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    if-nez v0, :cond_5

    .line 53
    .line 54
    move-object v8, v1

    .line 55
    goto :goto_5

    .line 56
    :cond_5
    move-object v8, v0

    .line 57
    :goto_5
    invoke-virtual/range {p0 .. p0}, LNL0/b;->j()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    if-nez v0, :cond_6

    .line 62
    .line 63
    move-object v9, v1

    .line 64
    goto :goto_6

    .line 65
    :cond_6
    move-object v9, v0

    .line 66
    :goto_6
    invoke-virtual/range {p0 .. p0}, LNL0/b;->u()Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    if-nez v0, :cond_7

    .line 71
    .line 72
    move-object v10, v1

    .line 73
    goto :goto_7

    .line 74
    :cond_7
    move-object v10, v0

    .line 75
    :goto_7
    invoke-virtual/range {p0 .. p0}, LNL0/b;->r()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    if-nez v0, :cond_8

    .line 80
    .line 81
    move-object v11, v1

    .line 82
    goto :goto_8

    .line 83
    :cond_8
    move-object v11, v0

    .line 84
    :goto_8
    invoke-virtual/range {p0 .. p0}, LNL0/b;->c()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    if-nez v0, :cond_9

    .line 89
    .line 90
    move-object v12, v1

    .line 91
    goto :goto_9

    .line 92
    :cond_9
    move-object v12, v0

    .line 93
    :goto_9
    invoke-virtual/range {p0 .. p0}, LNL0/b;->s()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    if-nez v0, :cond_a

    .line 98
    .line 99
    move-object v13, v1

    .line 100
    goto :goto_a

    .line 101
    :cond_a
    move-object v13, v0

    .line 102
    :goto_a
    invoke-virtual/range {p0 .. p0}, LNL0/b;->i()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    if-nez v0, :cond_b

    .line 107
    .line 108
    move-object v14, v1

    .line 109
    goto :goto_b

    .line 110
    :cond_b
    move-object v14, v0

    .line 111
    :goto_b
    invoke-virtual/range {p0 .. p0}, LNL0/b;->m()Ljava/lang/String;

    .line 112
    .line 113
    .line 114
    move-result-object v0

    .line 115
    if-nez v0, :cond_c

    .line 116
    .line 117
    move-object v15, v1

    .line 118
    goto :goto_c

    .line 119
    :cond_c
    move-object v15, v0

    .line 120
    :goto_c
    invoke-virtual/range {p0 .. p0}, LNL0/b;->g()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    if-nez v0, :cond_d

    .line 125
    .line 126
    move-object/from16 v16, v1

    .line 127
    .line 128
    goto :goto_d

    .line 129
    :cond_d
    move-object/from16 v16, v0

    .line 130
    .line 131
    :goto_d
    invoke-virtual/range {p0 .. p0}, LNL0/b;->d()Ljava/lang/String;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    if-nez v0, :cond_e

    .line 136
    .line 137
    move-object/from16 v17, v1

    .line 138
    .line 139
    goto :goto_e

    .line 140
    :cond_e
    move-object/from16 v17, v0

    .line 141
    .line 142
    :goto_e
    invoke-virtual/range {p0 .. p0}, LNL0/b;->f()Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    if-nez v0, :cond_f

    .line 147
    .line 148
    move-object/from16 v18, v1

    .line 149
    .line 150
    goto :goto_f

    .line 151
    :cond_f
    move-object/from16 v18, v0

    .line 152
    .line 153
    :goto_f
    invoke-virtual/range {p0 .. p0}, LNL0/b;->l()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    if-nez v0, :cond_10

    .line 158
    .line 159
    move-object/from16 v19, v1

    .line 160
    .line 161
    goto :goto_10

    .line 162
    :cond_10
    move-object/from16 v19, v0

    .line 163
    .line 164
    :goto_10
    invoke-virtual/range {p0 .. p0}, LNL0/b;->q()Ljava/lang/String;

    .line 165
    .line 166
    .line 167
    move-result-object v0

    .line 168
    if-nez v0, :cond_11

    .line 169
    .line 170
    move-object/from16 v20, v1

    .line 171
    .line 172
    goto :goto_11

    .line 173
    :cond_11
    move-object/from16 v20, v0

    .line 174
    .line 175
    :goto_11
    invoke-virtual/range {p0 .. p0}, LNL0/b;->o()Ljava/lang/String;

    .line 176
    .line 177
    .line 178
    move-result-object v0

    .line 179
    if-nez v0, :cond_12

    .line 180
    .line 181
    move-object/from16 v21, v1

    .line 182
    .line 183
    goto :goto_12

    .line 184
    :cond_12
    move-object/from16 v21, v0

    .line 185
    .line 186
    :goto_12
    invoke-virtual/range {p0 .. p0}, LNL0/b;->e()Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object v0

    .line 190
    if-eqz v0, :cond_15

    .line 191
    .line 192
    new-instance v1, Ljava/util/ArrayList;

    .line 193
    .line 194
    const/16 v2, 0xa

    .line 195
    .line 196
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 197
    .line 198
    .line 199
    move-result v2

    .line 200
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 201
    .line 202
    .line 203
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 204
    .line 205
    .line 206
    move-result-object v0

    .line 207
    :goto_13
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 208
    .line 209
    .line 210
    move-result v2

    .line 211
    if-eqz v2, :cond_13

    .line 212
    .line 213
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 214
    .line 215
    .line 216
    move-result-object v2

    .line 217
    check-cast v2, LNL0/a;

    .line 218
    .line 219
    invoke-static {v2}, LML0/a;->a(LNL0/a;)LPL0/a;

    .line 220
    .line 221
    .line 222
    move-result-object v2

    .line 223
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 224
    .line 225
    .line 226
    goto :goto_13

    .line 227
    :cond_13
    new-instance v0, Ljava/util/ArrayList;

    .line 228
    .line 229
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 230
    .line 231
    .line 232
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 233
    .line 234
    .line 235
    move-result-object v1

    .line 236
    :cond_14
    :goto_14
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 237
    .line 238
    .line 239
    move-result v2

    .line 240
    if-eqz v2, :cond_16

    .line 241
    .line 242
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v2

    .line 246
    move-object/from16 v22, v2

    .line 247
    .line 248
    check-cast v22, LPL0/a;

    .line 249
    .line 250
    invoke-virtual/range {v22 .. v22}, LPL0/a;->b()Ljava/lang/String;

    .line 251
    .line 252
    .line 253
    move-result-object v22

    .line 254
    invoke-interface/range {v22 .. v22}, Ljava/lang/CharSequence;->length()I

    .line 255
    .line 256
    .line 257
    move-result v22

    .line 258
    if-lez v22, :cond_14

    .line 259
    .line 260
    invoke-interface {v0, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 261
    .line 262
    .line 263
    goto :goto_14

    .line 264
    :cond_15
    const/4 v0, 0x0

    .line 265
    :cond_16
    if-nez v0, :cond_17

    .line 266
    .line 267
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 268
    .line 269
    .line 270
    move-result-object v0

    .line 271
    :cond_17
    move-object/from16 v22, v0

    .line 272
    .line 273
    invoke-virtual/range {p0 .. p0}, LNL0/b;->n()Ljava/util/List;

    .line 274
    .line 275
    .line 276
    move-result-object v0

    .line 277
    if-nez v0, :cond_18

    .line 278
    .line 279
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 280
    .line 281
    .line 282
    move-result-object v0

    .line 283
    :cond_18
    move-object/from16 v23, v0

    .line 284
    .line 285
    invoke-virtual/range {p0 .. p0}, LNL0/b;->k()Ljava/lang/Boolean;

    .line 286
    .line 287
    .line 288
    move-result-object v0

    .line 289
    const/4 v1, 0x0

    .line 290
    if-eqz v0, :cond_19

    .line 291
    .line 292
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 293
    .line 294
    .line 295
    move-result v0

    .line 296
    move/from16 v24, v0

    .line 297
    .line 298
    goto :goto_15

    .line 299
    :cond_19
    const/16 v24, 0x0

    .line 300
    .line 301
    :goto_15
    if-nez p1, :cond_1c

    .line 302
    .line 303
    invoke-virtual/range {p0 .. p0}, LNL0/b;->w()Ljava/lang/Boolean;

    .line 304
    .line 305
    .line 306
    move-result-object v0

    .line 307
    if-eqz v0, :cond_1a

    .line 308
    .line 309
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 310
    .line 311
    .line 312
    move-result v0

    .line 313
    goto :goto_16

    .line 314
    :cond_1a
    const/4 v0, 0x0

    .line 315
    :goto_16
    if-eqz v0, :cond_1b

    .line 316
    .line 317
    goto :goto_17

    .line 318
    :cond_1b
    const/16 v25, 0x0

    .line 319
    .line 320
    goto :goto_18

    .line 321
    :cond_1c
    :goto_17
    const/4 v1, 0x1

    .line 322
    const/16 v25, 0x1

    .line 323
    .line 324
    :goto_18
    new-instance v2, LPL0/b;

    .line 325
    .line 326
    invoke-direct/range {v2 .. v25}, LPL0/b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZ)V

    .line 327
    .line 328
    .line 329
    return-object v2
.end method
