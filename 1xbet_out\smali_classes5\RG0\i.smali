.class public final LRG0/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\u001a;\u0010\t\u001a\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u00002\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007H\u0001\u00a2\u0006\u0004\u0008\t\u0010\n\u001a%\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\u00052\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a)\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00102\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007H\u0003\u00a2\u0006\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0015"
    }
    d2 = {
        "Lkotlin/Function1;",
        "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a;",
        "",
        "onActionClick",
        "LWG0/d;",
        "LRG0/j;",
        "forecastUiState",
        "Landroidx/compose/ui/l;",
        "modifier",
        "f",
        "(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "gameEventUiState",
        "Lkotlin/Function0;",
        "onDetailClick",
        "l",
        "(LRG0/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V",
        "",
        "teamLogo",
        "teamName",
        "i",
        "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ljava/lang/String;)Lz31/d;
    .locals 0

    .line 1
    invoke-static {p0}, LRG0/i;->j(Ljava/lang/String;)Lz31/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, LRG0/i;->k(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, LRG0/i;->h(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LRG0/i;->g(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LRG0/j;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LRG0/i;->m(LRG0/j;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 10
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LWG0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a;",
            "Lkotlin/Unit;",
            ">;",
            "LWG0/d<",
            "LRG0/j;",
            ">;",
            "Landroidx/compose/ui/l;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    const v0, 0x64a0e8c9

    .line 2
    .line 3
    .line 4
    invoke-interface {p3, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v7

    .line 8
    and-int/lit8 p3, p5, 0x1

    .line 9
    .line 10
    const/4 v1, 0x4

    .line 11
    if-eqz p3, :cond_0

    .line 12
    .line 13
    or-int/lit8 p3, p4, 0x6

    .line 14
    .line 15
    goto :goto_1

    .line 16
    :cond_0
    and-int/lit8 p3, p4, 0x6

    .line 17
    .line 18
    if-nez p3, :cond_2

    .line 19
    .line 20
    invoke-interface {v7, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    move-result p3

    .line 24
    if-eqz p3, :cond_1

    .line 25
    .line 26
    const/4 p3, 0x4

    .line 27
    goto :goto_0

    .line 28
    :cond_1
    const/4 p3, 0x2

    .line 29
    :goto_0
    or-int/2addr p3, p4

    .line 30
    goto :goto_1

    .line 31
    :cond_2
    move p3, p4

    .line 32
    :goto_1
    and-int/lit8 v2, p5, 0x2

    .line 33
    .line 34
    if-eqz v2, :cond_3

    .line 35
    .line 36
    or-int/lit8 p3, p3, 0x30

    .line 37
    .line 38
    goto :goto_3

    .line 39
    :cond_3
    and-int/lit8 v2, p4, 0x30

    .line 40
    .line 41
    if-nez v2, :cond_5

    .line 42
    .line 43
    invoke-interface {v7, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_4

    .line 48
    .line 49
    const/16 v2, 0x20

    .line 50
    .line 51
    goto :goto_2

    .line 52
    :cond_4
    const/16 v2, 0x10

    .line 53
    .line 54
    :goto_2
    or-int/2addr p3, v2

    .line 55
    :cond_5
    :goto_3
    and-int/lit8 v2, p5, 0x4

    .line 56
    .line 57
    if-eqz v2, :cond_6

    .line 58
    .line 59
    or-int/lit16 p3, p3, 0x180

    .line 60
    .line 61
    goto :goto_5

    .line 62
    :cond_6
    and-int/lit16 v3, p4, 0x180

    .line 63
    .line 64
    if-nez v3, :cond_8

    .line 65
    .line 66
    invoke-interface {v7, p2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    if-eqz v3, :cond_7

    .line 71
    .line 72
    const/16 v3, 0x100

    .line 73
    .line 74
    goto :goto_4

    .line 75
    :cond_7
    const/16 v3, 0x80

    .line 76
    .line 77
    :goto_4
    or-int/2addr p3, v3

    .line 78
    :cond_8
    :goto_5
    and-int/lit16 v3, p3, 0x93

    .line 79
    .line 80
    const/16 v4, 0x92

    .line 81
    .line 82
    if-ne v3, v4, :cond_b

    .line 83
    .line 84
    invoke-interface {v7}, Landroidx/compose/runtime/j;->c()Z

    .line 85
    .line 86
    .line 87
    move-result v3

    .line 88
    if-nez v3, :cond_9

    .line 89
    .line 90
    goto :goto_7

    .line 91
    :cond_9
    invoke-interface {v7}, Landroidx/compose/runtime/j;->n()V

    .line 92
    .line 93
    .line 94
    :cond_a
    :goto_6
    move-object v3, p2

    .line 95
    goto :goto_9

    .line 96
    :cond_b
    :goto_7
    if-eqz v2, :cond_c

    .line 97
    .line 98
    sget-object p2, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 99
    .line 100
    :cond_c
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 101
    .line 102
    .line 103
    move-result v2

    .line 104
    if-eqz v2, :cond_d

    .line 105
    .line 106
    const/4 v2, -0x1

    .line 107
    const-string v3, "org.xbet.statistic.main.common.presentation.forecast.ForecastComponent (ForecastComponent.kt:45)"

    .line 108
    .line 109
    invoke-static {v0, p3, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 110
    .line 111
    .line 112
    :cond_d
    const/4 v0, 0x4

    .line 113
    sget v1, Lpb/k;->statistic_info_forecast:I

    .line 114
    .line 115
    sget v2, LlZ0/h;->ic_glyph_odds_movement:I

    .line 116
    .line 117
    invoke-virtual {p1}, LWG0/d;->e()Z

    .line 118
    .line 119
    .line 120
    move-result v3

    .line 121
    const v4, 0x4c5de2

    .line 122
    .line 123
    .line 124
    invoke-interface {v7, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 125
    .line 126
    .line 127
    and-int/lit8 p3, p3, 0xe

    .line 128
    .line 129
    const/4 v4, 0x0

    .line 130
    const/4 v5, 0x1

    .line 131
    if-ne p3, v0, :cond_e

    .line 132
    .line 133
    const/4 p3, 0x1

    .line 134
    goto :goto_8

    .line 135
    :cond_e
    const/4 p3, 0x0

    .line 136
    :goto_8
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    if-nez p3, :cond_f

    .line 141
    .line 142
    sget-object p3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 143
    .line 144
    invoke-virtual {p3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object p3

    .line 148
    if-ne v0, p3, :cond_10

    .line 149
    .line 150
    :cond_f
    new-instance v0, LRG0/a;

    .line 151
    .line 152
    invoke-direct {v0, p0}, LRG0/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 153
    .line 154
    .line 155
    invoke-interface {v7, v0}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 156
    .line 157
    .line 158
    :cond_10
    check-cast v0, Lkotlin/jvm/functions/Function0;

    .line 159
    .line 160
    invoke-interface {v7}, Landroidx/compose/runtime/j;->q()V

    .line 161
    .line 162
    .line 163
    const/4 p3, 0x3

    .line 164
    const/4 v6, 0x0

    .line 165
    invoke-static {p2, v6, v4, p3, v6}, Landroidx/compose/foundation/layout/SizeKt;->G(Landroidx/compose/ui/l;Landroidx/compose/ui/e;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 166
    .line 167
    .line 168
    move-result-object p3

    .line 169
    new-instance v4, LRG0/i$a;

    .line 170
    .line 171
    invoke-direct {v4, p1, p0}, LRG0/i$a;-><init>(LWG0/d;Lkotlin/jvm/functions/Function1;)V

    .line 172
    .line 173
    .line 174
    const/16 v6, 0x36

    .line 175
    .line 176
    const v8, -0x4565a8e5

    .line 177
    .line 178
    .line 179
    invoke-static {v8, v5, v4, v7, v6}, Landroidx/compose/runtime/internal/b;->d(IZLjava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/internal/a;

    .line 180
    .line 181
    .line 182
    move-result-object v6

    .line 183
    const/high16 v8, 0x30000

    .line 184
    .line 185
    const/4 v9, 0x0

    .line 186
    move-object v5, p3

    .line 187
    move-object v4, v0

    .line 188
    invoke-static/range {v1 .. v9}, LIN0/v;->b(IIZLkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;LOc/n;Landroidx/compose/runtime/j;II)V

    .line 189
    .line 190
    .line 191
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 192
    .line 193
    .line 194
    move-result p3

    .line 195
    if-eqz p3, :cond_a

    .line 196
    .line 197
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 198
    .line 199
    .line 200
    goto :goto_6

    .line 201
    :goto_9
    invoke-interface {v7}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 202
    .line 203
    .line 204
    move-result-object p2

    .line 205
    if-eqz p2, :cond_11

    .line 206
    .line 207
    new-instance v0, LRG0/b;

    .line 208
    .line 209
    move-object v1, p0

    .line 210
    move-object v2, p1

    .line 211
    move v4, p4

    .line 212
    move v5, p5

    .line 213
    invoke-direct/range {v0 .. v5}, LRG0/b;-><init>(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;II)V

    .line 214
    .line 215
    .line 216
    invoke-interface {p2, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 217
    .line 218
    .line 219
    :cond_11
    return-void
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;->FORECAST:Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;->b(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;->a(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, LRG0/i;->f(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final i(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 36

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move/from16 v4, p4

    .line 4
    .line 5
    const/4 v0, 0x2

    .line 6
    const/16 v2, 0x30

    .line 7
    .line 8
    const v3, -0x51d7cbcd

    .line 9
    .line 10
    .line 11
    move-object/from16 v5, p3

    .line 12
    .line 13
    invoke-interface {v5, v3}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 14
    .line 15
    .line 16
    move-result-object v5

    .line 17
    const/4 v6, 0x1

    .line 18
    and-int/lit8 v7, p5, 0x1

    .line 19
    .line 20
    const/4 v8, 0x4

    .line 21
    if-eqz v7, :cond_0

    .line 22
    .line 23
    or-int/lit8 v7, v4, 0x6

    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_0
    and-int/lit8 v7, v4, 0x6

    .line 27
    .line 28
    if-nez v7, :cond_2

    .line 29
    .line 30
    invoke-interface {v5, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    move-result v7

    .line 34
    if-eqz v7, :cond_1

    .line 35
    .line 36
    const/4 v7, 0x4

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    const/4 v7, 0x2

    .line 39
    :goto_0
    or-int/2addr v7, v4

    .line 40
    goto :goto_1

    .line 41
    :cond_2
    move v7, v4

    .line 42
    :goto_1
    and-int/lit8 v0, p5, 0x2

    .line 43
    .line 44
    if-eqz v0, :cond_4

    .line 45
    .line 46
    or-int/2addr v7, v2

    .line 47
    :cond_3
    move-object/from16 v0, p1

    .line 48
    .line 49
    goto :goto_3

    .line 50
    :cond_4
    and-int/lit8 v0, v4, 0x30

    .line 51
    .line 52
    if-nez v0, :cond_3

    .line 53
    .line 54
    move-object/from16 v0, p1

    .line 55
    .line 56
    invoke-interface {v5, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    move-result v9

    .line 60
    if-eqz v9, :cond_5

    .line 61
    .line 62
    const/16 v9, 0x20

    .line 63
    .line 64
    goto :goto_2

    .line 65
    :cond_5
    const/16 v9, 0x10

    .line 66
    .line 67
    :goto_2
    or-int/2addr v7, v9

    .line 68
    :goto_3
    and-int/lit8 v9, p5, 0x4

    .line 69
    .line 70
    if-eqz v9, :cond_7

    .line 71
    .line 72
    or-int/lit16 v7, v7, 0x180

    .line 73
    .line 74
    :cond_6
    move-object/from16 v10, p2

    .line 75
    .line 76
    goto :goto_5

    .line 77
    :cond_7
    and-int/lit16 v10, v4, 0x180

    .line 78
    .line 79
    if-nez v10, :cond_6

    .line 80
    .line 81
    move-object/from16 v10, p2

    .line 82
    .line 83
    invoke-interface {v5, v10}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 84
    .line 85
    .line 86
    move-result v11

    .line 87
    if-eqz v11, :cond_8

    .line 88
    .line 89
    const/16 v11, 0x100

    .line 90
    .line 91
    goto :goto_4

    .line 92
    :cond_8
    const/16 v11, 0x80

    .line 93
    .line 94
    :goto_4
    or-int/2addr v7, v11

    .line 95
    :goto_5
    and-int/lit16 v11, v7, 0x93

    .line 96
    .line 97
    const/16 v12, 0x92

    .line 98
    .line 99
    if-ne v11, v12, :cond_a

    .line 100
    .line 101
    invoke-interface {v5}, Landroidx/compose/runtime/j;->c()Z

    .line 102
    .line 103
    .line 104
    move-result v11

    .line 105
    if-nez v11, :cond_9

    .line 106
    .line 107
    goto :goto_6

    .line 108
    :cond_9
    invoke-interface {v5}, Landroidx/compose/runtime/j;->n()V

    .line 109
    .line 110
    .line 111
    move-object/from16 v31, v5

    .line 112
    .line 113
    move-object v3, v10

    .line 114
    goto/16 :goto_a

    .line 115
    .line 116
    :cond_a
    :goto_6
    if-eqz v9, :cond_b

    .line 117
    .line 118
    sget-object v9, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 119
    .line 120
    goto :goto_7

    .line 121
    :cond_b
    move-object v9, v10

    .line 122
    :goto_7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 123
    .line 124
    .line 125
    move-result v10

    .line 126
    if-eqz v10, :cond_c

    .line 127
    .line 128
    const/4 v10, -0x1

    .line 129
    const-string v11, "org.xbet.statistic.main.common.presentation.forecast.MiddleContainer (ForecastComponent.kt:152)"

    .line 130
    .line 131
    invoke-static {v3, v7, v10, v11}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 132
    .line 133
    .line 134
    :cond_c
    sget-object v3, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 135
    .line 136
    invoke-virtual {v3}, Landroidx/compose/ui/e$a;->g()Landroidx/compose/ui/e$b;

    .line 137
    .line 138
    .line 139
    move-result-object v3

    .line 140
    sget-object v10, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 141
    .line 142
    invoke-virtual {v10}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 143
    .line 144
    .line 145
    move-result-object v10

    .line 146
    invoke-static {v10, v3, v5, v2}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 147
    .line 148
    .line 149
    move-result-object v2

    .line 150
    const/4 v3, 0x0

    .line 151
    invoke-static {v5, v3}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 152
    .line 153
    .line 154
    move-result v10

    .line 155
    invoke-interface {v5}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 156
    .line 157
    .line 158
    move-result-object v11

    .line 159
    invoke-static {v5, v9}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 160
    .line 161
    .line 162
    move-result-object v12

    .line 163
    sget-object v13, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 164
    .line 165
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 166
    .line 167
    .line 168
    move-result-object v14

    .line 169
    invoke-interface {v5}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 170
    .line 171
    .line 172
    move-result-object v15

    .line 173
    invoke-static {v15}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 174
    .line 175
    .line 176
    move-result v15

    .line 177
    if-nez v15, :cond_d

    .line 178
    .line 179
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 180
    .line 181
    .line 182
    :cond_d
    invoke-interface {v5}, Landroidx/compose/runtime/j;->l()V

    .line 183
    .line 184
    .line 185
    invoke-interface {v5}, Landroidx/compose/runtime/j;->B()Z

    .line 186
    .line 187
    .line 188
    move-result v15

    .line 189
    if-eqz v15, :cond_e

    .line 190
    .line 191
    invoke-interface {v5, v14}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 192
    .line 193
    .line 194
    goto :goto_8

    .line 195
    :cond_e
    invoke-interface {v5}, Landroidx/compose/runtime/j;->h()V

    .line 196
    .line 197
    .line 198
    :goto_8
    invoke-static {v5}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 199
    .line 200
    .line 201
    move-result-object v14

    .line 202
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 203
    .line 204
    .line 205
    move-result-object v15

    .line 206
    invoke-static {v14, v2, v15}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 207
    .line 208
    .line 209
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 210
    .line 211
    .line 212
    move-result-object v2

    .line 213
    invoke-static {v14, v11, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 214
    .line 215
    .line 216
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 217
    .line 218
    .line 219
    move-result-object v2

    .line 220
    invoke-interface {v14}, Landroidx/compose/runtime/j;->B()Z

    .line 221
    .line 222
    .line 223
    move-result v11

    .line 224
    if-nez v11, :cond_f

    .line 225
    .line 226
    invoke-interface {v14}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 227
    .line 228
    .line 229
    move-result-object v11

    .line 230
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 231
    .line 232
    .line 233
    move-result-object v15

    .line 234
    invoke-static {v11, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 235
    .line 236
    .line 237
    move-result v11

    .line 238
    if-nez v11, :cond_10

    .line 239
    .line 240
    :cond_f
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 241
    .line 242
    .line 243
    move-result-object v11

    .line 244
    invoke-interface {v14, v11}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 245
    .line 246
    .line 247
    invoke-static {v10}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 248
    .line 249
    .line 250
    move-result-object v10

    .line 251
    invoke-interface {v14, v10, v2}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 252
    .line 253
    .line 254
    :cond_10
    invoke-virtual {v13}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 255
    .line 256
    .line 257
    move-result-object v2

    .line 258
    invoke-static {v14, v12, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 259
    .line 260
    .line 261
    sget-object v2, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 262
    .line 263
    const v2, 0x4c5de2

    .line 264
    .line 265
    .line 266
    invoke-interface {v5, v2}, Landroidx/compose/runtime/j;->t(I)V

    .line 267
    .line 268
    .line 269
    and-int/lit8 v2, v7, 0xe

    .line 270
    .line 271
    if-ne v2, v8, :cond_11

    .line 272
    .line 273
    const/4 v2, 0x1

    .line 274
    goto :goto_9

    .line 275
    :cond_11
    const/4 v2, 0x0

    .line 276
    :goto_9
    invoke-interface {v5}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 277
    .line 278
    .line 279
    move-result-object v8

    .line 280
    if-nez v2, :cond_12

    .line 281
    .line 282
    sget-object v2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 283
    .line 284
    invoke-virtual {v2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 285
    .line 286
    .line 287
    move-result-object v2

    .line 288
    if-ne v8, v2, :cond_13

    .line 289
    .line 290
    :cond_12
    new-instance v8, LRG0/d;

    .line 291
    .line 292
    invoke-direct {v8, v1}, LRG0/d;-><init>(Ljava/lang/String;)V

    .line 293
    .line 294
    .line 295
    invoke-interface {v5, v8}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 296
    .line 297
    .line 298
    :cond_13
    check-cast v8, Lkotlin/jvm/functions/Function0;

    .line 299
    .line 300
    invoke-interface {v5}, Landroidx/compose/runtime/j;->q()V

    .line 301
    .line 302
    .line 303
    const/4 v2, 0x0

    .line 304
    invoke-static {v2, v8, v5, v3, v6}, Lx31/b;->b(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 305
    .line 306
    .line 307
    sget-object v10, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 308
    .line 309
    sget-object v2, LA11/a;->a:LA11/a;

    .line 310
    .line 311
    invoke-virtual {v2}, LA11/a;->L1()F

    .line 312
    .line 313
    .line 314
    move-result v12

    .line 315
    const/16 v15, 0xd

    .line 316
    .line 317
    const/16 v16, 0x0

    .line 318
    .line 319
    const/4 v11, 0x0

    .line 320
    const/4 v13, 0x0

    .line 321
    const/4 v14, 0x0

    .line 322
    invoke-static/range {v10 .. v16}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 323
    .line 324
    .line 325
    move-result-object v6

    .line 326
    sget-object v3, LC11/a;->a:LC11/a;

    .line 327
    .line 328
    invoke-virtual {v3}, LC11/a;->n()Landroidx/compose/ui/text/a0;

    .line 329
    .line 330
    .line 331
    move-result-object v29

    .line 332
    sget-object v3, Landroidx/compose/ui/text/style/s;->b:Landroidx/compose/ui/text/style/s$a;

    .line 333
    .line 334
    invoke-virtual {v3}, Landroidx/compose/ui/text/style/s$a;->b()I

    .line 335
    .line 336
    .line 337
    move-result v24

    .line 338
    invoke-virtual {v2}, LA11/a;->Q1()J

    .line 339
    .line 340
    .line 341
    move-result-wide v13

    .line 342
    invoke-virtual {v2}, LA11/a;->R1()J

    .line 343
    .line 344
    .line 345
    move-result-wide v15

    .line 346
    invoke-static {}, LV01/s;->f()Landroidx/compose/runtime/x0;

    .line 347
    .line 348
    .line 349
    move-result-object v2

    .line 350
    invoke-interface {v5, v2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 351
    .line 352
    .line 353
    move-result-object v2

    .line 354
    check-cast v2, Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 355
    .line 356
    invoke-virtual {v2}, Lorg/xbet/uikit/compose/color/ThemeColors;->getTextPrimary-0d7_KjU()J

    .line 357
    .line 358
    .line 359
    move-result-wide v2

    .line 360
    shr-int/lit8 v7, v7, 0x3

    .line 361
    .line 362
    and-int/lit8 v32, v7, 0xe

    .line 363
    .line 364
    const/16 v34, 0x0

    .line 365
    .line 366
    const v35, 0x16bf38

    .line 367
    .line 368
    .line 369
    move-object v10, v9

    .line 370
    const/4 v9, 0x0

    .line 371
    move-object v7, v10

    .line 372
    const/4 v10, 0x0

    .line 373
    const-wide/16 v11, 0x0

    .line 374
    .line 375
    const/16 v17, 0x0

    .line 376
    .line 377
    const/16 v18, 0x0

    .line 378
    .line 379
    const/16 v19, 0x0

    .line 380
    .line 381
    const-wide/16 v20, 0x0

    .line 382
    .line 383
    const/16 v22, 0x0

    .line 384
    .line 385
    const/16 v23, 0x0

    .line 386
    .line 387
    const/16 v25, 0x0

    .line 388
    .line 389
    const/16 v26, 0x3

    .line 390
    .line 391
    const/16 v27, 0x0

    .line 392
    .line 393
    const/16 v28, 0x0

    .line 394
    .line 395
    const/16 v30, 0x0

    .line 396
    .line 397
    const v33, 0x186000

    .line 398
    .line 399
    .line 400
    move-object/from16 v31, v5

    .line 401
    .line 402
    move-object v5, v0

    .line 403
    move-object v0, v7

    .line 404
    move-wide v7, v2

    .line 405
    invoke-static/range {v5 .. v35}, Lorg/xbet/uikit/compose/utils/a;->f(Ljava/lang/String;Landroidx/compose/ui/l;JLjava/util/List;Lorg/xbet/uikit/compose/utils/SuggestedFontSizesStatus;JJJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/e;IZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;FLandroidx/compose/runtime/j;IIII)V

    .line 406
    .line 407
    .line 408
    invoke-interface/range {v31 .. v31}, Landroidx/compose/runtime/j;->j()V

    .line 409
    .line 410
    .line 411
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 412
    .line 413
    .line 414
    move-result v2

    .line 415
    if-eqz v2, :cond_14

    .line 416
    .line 417
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 418
    .line 419
    .line 420
    :cond_14
    move-object v3, v0

    .line 421
    :goto_a
    invoke-interface/range {v31 .. v31}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 422
    .line 423
    .line 424
    move-result-object v6

    .line 425
    if-eqz v6, :cond_15

    .line 426
    .line 427
    new-instance v0, LRG0/e;

    .line 428
    .line 429
    move-object/from16 v2, p1

    .line 430
    .line 431
    move/from16 v5, p5

    .line 432
    .line 433
    invoke-direct/range {v0 .. v5}, LRG0/e;-><init>(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;II)V

    .line 434
    .line 435
    .line 436
    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 437
    .line 438
    .line 439
    :cond_15
    return-void
.end method

.method public static final j(Ljava/lang/String;)Lz31/d;
    .locals 5

    .line 1
    new-instance v0, Lz31/d$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v1, v2, v1}, Lz31/e$a;->d(Lorg/xbet/uikit/compose/color/ColorKey;ILkotlin/jvm/internal/DefaultConstructorMarker;)Lorg/xbet/uikit/compose/color/ColorKey;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1}, Lz31/e$a;->b(Lorg/xbet/uikit/compose/color/ColorKey;)Lz31/e$a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    sget-object v2, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_36:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 14
    .line 15
    new-instance v3, Lr31/a$b;

    .line 16
    .line 17
    sget v4, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 18
    .line 19
    invoke-direct {v3, p0, v4}, Lr31/a$b;-><init>(Ljava/lang/String;I)V

    .line 20
    .line 21
    .line 22
    invoke-direct {v0, v1, v2, v3}, Lz31/d$a;-><init>(Lz31/e;Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;Lr31/a;)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method

.method public static final k(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, LRG0/i;->i(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final l(LRG0/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V
    .locals 30
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LRG0/j;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "I)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move/from16 v2, p3

    .line 6
    .line 7
    const/16 v3, 0x30

    .line 8
    .line 9
    const v4, -0x27359b94

    .line 10
    .line 11
    .line 12
    move-object/from16 v5, p2

    .line 13
    .line 14
    invoke-interface {v5, v4}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 15
    .line 16
    .line 17
    move-result-object v8

    .line 18
    and-int/lit8 v5, v2, 0x6

    .line 19
    .line 20
    if-nez v5, :cond_1

    .line 21
    .line 22
    invoke-interface {v8, v0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    move-result v5

    .line 26
    if-eqz v5, :cond_0

    .line 27
    .line 28
    const/4 v5, 0x4

    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const/4 v5, 0x2

    .line 31
    :goto_0
    or-int/2addr v5, v2

    .line 32
    goto :goto_1

    .line 33
    :cond_1
    move v5, v2

    .line 34
    :goto_1
    and-int/lit8 v6, v2, 0x30

    .line 35
    .line 36
    if-nez v6, :cond_3

    .line 37
    .line 38
    invoke-interface {v8, v1}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result v6

    .line 42
    if-eqz v6, :cond_2

    .line 43
    .line 44
    const/16 v6, 0x20

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    const/16 v6, 0x10

    .line 48
    .line 49
    :goto_2
    or-int/2addr v5, v6

    .line 50
    :cond_3
    move v12, v5

    .line 51
    and-int/lit8 v5, v12, 0x13

    .line 52
    .line 53
    const/16 v6, 0x12

    .line 54
    .line 55
    if-ne v5, v6, :cond_5

    .line 56
    .line 57
    invoke-interface {v8}, Landroidx/compose/runtime/j;->c()Z

    .line 58
    .line 59
    .line 60
    move-result v5

    .line 61
    if-nez v5, :cond_4

    .line 62
    .line 63
    goto :goto_3

    .line 64
    :cond_4
    invoke-interface {v8}, Landroidx/compose/runtime/j;->n()V

    .line 65
    .line 66
    .line 67
    goto/16 :goto_f

    .line 68
    .line 69
    :cond_5
    :goto_3
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 70
    .line 71
    .line 72
    move-result v5

    .line 73
    if-eqz v5, :cond_6

    .line 74
    .line 75
    const/4 v5, -0x1

    .line 76
    const-string v6, "org.xbet.statistic.main.common.presentation.forecast.SuccessState (ForecastComponent.kt:106)"

    .line 77
    .line 78
    invoke-static {v4, v12, v5, v6}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 79
    .line 80
    .line 81
    :cond_6
    invoke-static {}, Landroidx/compose/ui/platform/CompositionLocalsKt;->l()Landroidx/compose/runtime/x0;

    .line 82
    .line 83
    .line 84
    move-result-object v4

    .line 85
    invoke-interface {v8, v4}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v4

    .line 89
    sget-object v5, Landroidx/compose/ui/unit/LayoutDirection;->Ltr:Landroidx/compose/ui/unit/LayoutDirection;

    .line 90
    .line 91
    const/4 v13, 0x0

    .line 92
    if-ne v4, v5, :cond_7

    .line 93
    .line 94
    const/4 v4, 0x1

    .line 95
    goto :goto_4

    .line 96
    :cond_7
    const/4 v4, 0x0

    .line 97
    :goto_4
    if-eqz v4, :cond_8

    .line 98
    .line 99
    invoke-virtual {v0}, LRG0/j;->a()LND0/k;

    .line 100
    .line 101
    .line 102
    move-result-object v5

    .line 103
    :goto_5
    invoke-virtual {v5}, LND0/k;->f()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v5

    .line 107
    move-object v6, v5

    .line 108
    goto :goto_6

    .line 109
    :cond_8
    invoke-virtual {v0}, LRG0/j;->c()LND0/k;

    .line 110
    .line 111
    .line 112
    move-result-object v5

    .line 113
    goto :goto_5

    .line 114
    :goto_6
    if-nez v4, :cond_9

    .line 115
    .line 116
    invoke-virtual {v0}, LRG0/j;->a()LND0/k;

    .line 117
    .line 118
    .line 119
    move-result-object v5

    .line 120
    :goto_7
    invoke-virtual {v5}, LND0/k;->f()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v5

    .line 124
    move-object v14, v5

    .line 125
    goto :goto_8

    .line 126
    :cond_9
    invoke-virtual {v0}, LRG0/j;->c()LND0/k;

    .line 127
    .line 128
    .line 129
    move-result-object v5

    .line 130
    goto :goto_7

    .line 131
    :goto_8
    if-eqz v4, :cond_a

    .line 132
    .line 133
    invoke-virtual {v0}, LRG0/j;->a()LND0/k;

    .line 134
    .line 135
    .line 136
    move-result-object v5

    .line 137
    :goto_9
    invoke-virtual {v5}, LND0/k;->d()Ljava/lang/String;

    .line 138
    .line 139
    .line 140
    move-result-object v5

    .line 141
    goto :goto_a

    .line 142
    :cond_a
    invoke-virtual {v0}, LRG0/j;->c()LND0/k;

    .line 143
    .line 144
    .line 145
    move-result-object v5

    .line 146
    goto :goto_9

    .line 147
    :goto_a
    if-nez v4, :cond_b

    .line 148
    .line 149
    invoke-virtual {v0}, LRG0/j;->a()LND0/k;

    .line 150
    .line 151
    .line 152
    move-result-object v4

    .line 153
    :goto_b
    invoke-virtual {v4}, LND0/k;->d()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v4

    .line 157
    goto :goto_c

    .line 158
    :cond_b
    invoke-virtual {v0}, LRG0/j;->c()LND0/k;

    .line 159
    .line 160
    .line 161
    move-result-object v4

    .line 162
    goto :goto_b

    .line 163
    :goto_c
    sget-object v15, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 164
    .line 165
    sget-object v7, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 166
    .line 167
    invoke-virtual {v7}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 168
    .line 169
    .line 170
    move-result-object v9

    .line 171
    sget-object v10, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 172
    .line 173
    invoke-virtual {v10}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 174
    .line 175
    .line 176
    move-result-object v11

    .line 177
    invoke-static {v9, v11, v8, v13}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 178
    .line 179
    .line 180
    move-result-object v9

    .line 181
    invoke-static {v8, v13}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 182
    .line 183
    .line 184
    move-result v11

    .line 185
    invoke-interface {v8}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 186
    .line 187
    .line 188
    move-result-object v13

    .line 189
    invoke-static {v8, v15}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 190
    .line 191
    .line 192
    move-result-object v3

    .line 193
    sget-object v17, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 194
    .line 195
    move-object/from16 v22, v4

    .line 196
    .line 197
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 198
    .line 199
    .line 200
    move-result-object v4

    .line 201
    invoke-interface {v8}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 202
    .line 203
    .line 204
    move-result-object v18

    .line 205
    invoke-static/range {v18 .. v18}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 206
    .line 207
    .line 208
    move-result v18

    .line 209
    if-nez v18, :cond_c

    .line 210
    .line 211
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 212
    .line 213
    .line 214
    :cond_c
    invoke-interface {v8}, Landroidx/compose/runtime/j;->l()V

    .line 215
    .line 216
    .line 217
    invoke-interface {v8}, Landroidx/compose/runtime/j;->B()Z

    .line 218
    .line 219
    .line 220
    move-result v18

    .line 221
    if-eqz v18, :cond_d

    .line 222
    .line 223
    invoke-interface {v8, v4}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 224
    .line 225
    .line 226
    goto :goto_d

    .line 227
    :cond_d
    invoke-interface {v8}, Landroidx/compose/runtime/j;->h()V

    .line 228
    .line 229
    .line 230
    :goto_d
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 231
    .line 232
    .line 233
    move-result-object v4

    .line 234
    move-object/from16 v21, v5

    .line 235
    .line 236
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 237
    .line 238
    .line 239
    move-result-object v5

    .line 240
    invoke-static {v4, v9, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 241
    .line 242
    .line 243
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 244
    .line 245
    .line 246
    move-result-object v5

    .line 247
    invoke-static {v4, v13, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 248
    .line 249
    .line 250
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 251
    .line 252
    .line 253
    move-result-object v5

    .line 254
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 255
    .line 256
    .line 257
    move-result v9

    .line 258
    if-nez v9, :cond_e

    .line 259
    .line 260
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 261
    .line 262
    .line 263
    move-result-object v9

    .line 264
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 265
    .line 266
    .line 267
    move-result-object v13

    .line 268
    invoke-static {v9, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 269
    .line 270
    .line 271
    move-result v9

    .line 272
    if-nez v9, :cond_f

    .line 273
    .line 274
    :cond_e
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 275
    .line 276
    .line 277
    move-result-object v9

    .line 278
    invoke-interface {v4, v9}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 279
    .line 280
    .line 281
    invoke-static {v11}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 282
    .line 283
    .line 284
    move-result-object v9

    .line 285
    invoke-interface {v4, v9, v5}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 286
    .line 287
    .line 288
    :cond_f
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 289
    .line 290
    .line 291
    move-result-object v5

    .line 292
    invoke-static {v4, v3, v5}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 293
    .line 294
    .line 295
    sget-object v3, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 296
    .line 297
    invoke-virtual {v10}, Landroidx/compose/ui/e$a;->l()Landroidx/compose/ui/e$c;

    .line 298
    .line 299
    .line 300
    move-result-object v3

    .line 301
    invoke-virtual {v7}, Landroidx/compose/foundation/layout/Arrangement;->g()Landroidx/compose/foundation/layout/Arrangement$e;

    .line 302
    .line 303
    .line 304
    move-result-object v4

    .line 305
    const/16 v5, 0x30

    .line 306
    .line 307
    invoke-static {v4, v3, v8, v5}, Landroidx/compose/foundation/layout/h0;->b(Landroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 308
    .line 309
    .line 310
    move-result-object v3

    .line 311
    const/4 v4, 0x0

    .line 312
    invoke-static {v8, v4}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 313
    .line 314
    .line 315
    move-result v5

    .line 316
    invoke-interface {v8}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 317
    .line 318
    .line 319
    move-result-object v4

    .line 320
    invoke-static {v8, v15}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 321
    .line 322
    .line 323
    move-result-object v7

    .line 324
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 325
    .line 326
    .line 327
    move-result-object v9

    .line 328
    invoke-interface {v8}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 329
    .line 330
    .line 331
    move-result-object v10

    .line 332
    invoke-static {v10}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 333
    .line 334
    .line 335
    move-result v10

    .line 336
    if-nez v10, :cond_10

    .line 337
    .line 338
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 339
    .line 340
    .line 341
    :cond_10
    invoke-interface {v8}, Landroidx/compose/runtime/j;->l()V

    .line 342
    .line 343
    .line 344
    invoke-interface {v8}, Landroidx/compose/runtime/j;->B()Z

    .line 345
    .line 346
    .line 347
    move-result v10

    .line 348
    if-eqz v10, :cond_11

    .line 349
    .line 350
    invoke-interface {v8, v9}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 351
    .line 352
    .line 353
    goto :goto_e

    .line 354
    :cond_11
    invoke-interface {v8}, Landroidx/compose/runtime/j;->h()V

    .line 355
    .line 356
    .line 357
    :goto_e
    invoke-static {v8}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 358
    .line 359
    .line 360
    move-result-object v9

    .line 361
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 362
    .line 363
    .line 364
    move-result-object v10

    .line 365
    invoke-static {v9, v3, v10}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 366
    .line 367
    .line 368
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 369
    .line 370
    .line 371
    move-result-object v3

    .line 372
    invoke-static {v9, v4, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 373
    .line 374
    .line 375
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 376
    .line 377
    .line 378
    move-result-object v3

    .line 379
    invoke-interface {v9}, Landroidx/compose/runtime/j;->B()Z

    .line 380
    .line 381
    .line 382
    move-result v4

    .line 383
    if-nez v4, :cond_12

    .line 384
    .line 385
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 386
    .line 387
    .line 388
    move-result-object v4

    .line 389
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 390
    .line 391
    .line 392
    move-result-object v10

    .line 393
    invoke-static {v4, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 394
    .line 395
    .line 396
    move-result v4

    .line 397
    if-nez v4, :cond_13

    .line 398
    .line 399
    :cond_12
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 400
    .line 401
    .line 402
    move-result-object v4

    .line 403
    invoke-interface {v9, v4}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 404
    .line 405
    .line 406
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 407
    .line 408
    .line 409
    move-result-object v4

    .line 410
    invoke-interface {v9, v4, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 411
    .line 412
    .line 413
    :cond_13
    invoke-virtual/range {v17 .. v17}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 414
    .line 415
    .line 416
    move-result-object v3

    .line 417
    invoke-static {v9, v7, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 418
    .line 419
    .line 420
    move-object/from16 v16, v15

    .line 421
    .line 422
    sget-object v15, Landroidx/compose/foundation/layout/k0;->a:Landroidx/compose/foundation/layout/k0;

    .line 423
    .line 424
    const/16 v19, 0x2

    .line 425
    .line 426
    const/16 v20, 0x0

    .line 427
    .line 428
    const/high16 v17, 0x3f800000    # 1.0f

    .line 429
    .line 430
    const/16 v18, 0x0

    .line 431
    .line 432
    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 433
    .line 434
    .line 435
    move-result-object v23

    .line 436
    move-object v3, v15

    .line 437
    sget-object v4, LA11/a;->a:LA11/a;

    .line 438
    .line 439
    invoke-virtual {v4}, LA11/a;->q1()F

    .line 440
    .line 441
    .line 442
    move-result v24

    .line 443
    const/16 v28, 0xe

    .line 444
    .line 445
    const/16 v29, 0x0

    .line 446
    .line 447
    const/16 v25, 0x0

    .line 448
    .line 449
    const/16 v26, 0x0

    .line 450
    .line 451
    const/16 v27, 0x0

    .line 452
    .line 453
    invoke-static/range {v23 .. v29}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 454
    .line 455
    .line 456
    move-result-object v7

    .line 457
    const/4 v9, 0x0

    .line 458
    const/4 v10, 0x0

    .line 459
    move-object/from16 v5, v21

    .line 460
    .line 461
    invoke-static/range {v5 .. v10}, LRG0/i;->i(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 462
    .line 463
    .line 464
    invoke-virtual {v4}, LA11/a;->l1()F

    .line 465
    .line 466
    .line 467
    move-result v17

    .line 468
    move-object/from16 v15, v16

    .line 469
    .line 470
    invoke-virtual {v4}, LA11/a;->L1()F

    .line 471
    .line 472
    .line 473
    move-result v16

    .line 474
    invoke-virtual {v4}, LA11/a;->L1()F

    .line 475
    .line 476
    .line 477
    move-result v18

    .line 478
    const/16 v20, 0x8

    .line 479
    .line 480
    const/16 v21, 0x0

    .line 481
    .line 482
    const/16 v19, 0x0

    .line 483
    .line 484
    invoke-static/range {v15 .. v21}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 485
    .line 486
    .line 487
    move-result-object v5

    .line 488
    move-object/from16 v16, v15

    .line 489
    .line 490
    new-instance v6, Lorg/xbet/uikit_sport/score/a$a;

    .line 491
    .line 492
    invoke-virtual {v0}, LRG0/j;->b()LuE0/a;

    .line 493
    .line 494
    .line 495
    move-result-object v7

    .line 496
    invoke-virtual {v7}, LuE0/a;->b()I

    .line 497
    .line 498
    .line 499
    move-result v7

    .line 500
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 501
    .line 502
    .line 503
    move-result-object v7

    .line 504
    invoke-virtual {v0}, LRG0/j;->b()LuE0/a;

    .line 505
    .line 506
    .line 507
    move-result-object v9

    .line 508
    invoke-virtual {v9}, LuE0/a;->c()I

    .line 509
    .line 510
    .line 511
    move-result v9

    .line 512
    invoke-static {v9}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 513
    .line 514
    .line 515
    move-result-object v9

    .line 516
    const/4 v10, 0x0

    .line 517
    invoke-direct {v6, v7, v9, v10, v10}, Lorg/xbet/uikit_sport/score/a$a;-><init>(Ljava/lang/String;Ljava/lang/String;ZZ)V

    .line 518
    .line 519
    .line 520
    sget v7, Lorg/xbet/uikit_sport/score/a$a;->f:I

    .line 521
    .line 522
    shl-int/lit8 v10, v7, 0x3

    .line 523
    .line 524
    const/16 v11, 0xc

    .line 525
    .line 526
    const/4 v7, 0x0

    .line 527
    move-object v9, v8

    .line 528
    const/4 v8, 0x0

    .line 529
    invoke-static/range {v5 .. v11}, LI31/d;->d(Landroidx/compose/ui/l;Lorg/xbet/uikit_sport/score/a;Ljava/lang/Integer;ILandroidx/compose/runtime/j;II)V

    .line 530
    .line 531
    .line 532
    move-object v8, v9

    .line 533
    const/16 v19, 0x2

    .line 534
    .line 535
    const/16 v20, 0x0

    .line 536
    .line 537
    const/high16 v17, 0x3f800000    # 1.0f

    .line 538
    .line 539
    const/16 v18, 0x0

    .line 540
    .line 541
    move-object v15, v3

    .line 542
    invoke-static/range {v15 .. v20}, Landroidx/compose/foundation/layout/i0;->a(Landroidx/compose/foundation/layout/j0;Landroidx/compose/ui/l;FZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 543
    .line 544
    .line 545
    move-result-object v23

    .line 546
    move-object/from16 v15, v16

    .line 547
    .line 548
    invoke-virtual {v4}, LA11/a;->q1()F

    .line 549
    .line 550
    .line 551
    move-result v26

    .line 552
    const/16 v28, 0xb

    .line 553
    .line 554
    const/16 v24, 0x0

    .line 555
    .line 556
    invoke-static/range {v23 .. v29}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 557
    .line 558
    .line 559
    move-result-object v7

    .line 560
    const/4 v9, 0x0

    .line 561
    const/4 v10, 0x0

    .line 562
    move-object v6, v14

    .line 563
    move-object/from16 v5, v22

    .line 564
    .line 565
    invoke-static/range {v5 .. v10}, LRG0/i;->i(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 566
    .line 567
    .line 568
    invoke-interface {v8}, Landroidx/compose/runtime/j;->j()V

    .line 569
    .line 570
    .line 571
    invoke-virtual {v4}, LA11/a;->l1()F

    .line 572
    .line 573
    .line 574
    move-result v3

    .line 575
    invoke-virtual {v4}, LA11/a;->l1()F

    .line 576
    .line 577
    .line 578
    move-result v5

    .line 579
    invoke-virtual {v4}, LA11/a;->u1()F

    .line 580
    .line 581
    .line 582
    move-result v6

    .line 583
    invoke-virtual {v4}, LA11/a;->l1()F

    .line 584
    .line 585
    .line 586
    move-result v4

    .line 587
    invoke-static {v15, v3, v6, v5, v4}, Landroidx/compose/foundation/layout/PaddingKt;->l(Landroidx/compose/ui/l;FFFF)Landroidx/compose/ui/l;

    .line 588
    .line 589
    .line 590
    move-result-object v3

    .line 591
    shr-int/lit8 v4, v12, 0x3

    .line 592
    .line 593
    and-int/lit8 v4, v4, 0xe

    .line 594
    .line 595
    const/4 v10, 0x0

    .line 596
    invoke-static {v1, v3, v8, v4, v10}, LQG0/b;->b(Lkotlin/jvm/functions/Function0;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 597
    .line 598
    .line 599
    invoke-interface {v8}, Landroidx/compose/runtime/j;->j()V

    .line 600
    .line 601
    .line 602
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 603
    .line 604
    .line 605
    move-result v3

    .line 606
    if-eqz v3, :cond_14

    .line 607
    .line 608
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 609
    .line 610
    .line 611
    :cond_14
    :goto_f
    invoke-interface {v8}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 612
    .line 613
    .line 614
    move-result-object v3

    .line 615
    if-eqz v3, :cond_15

    .line 616
    .line 617
    new-instance v4, LRG0/c;

    .line 618
    .line 619
    invoke-direct {v4, v0, v1, v2}, LRG0/c;-><init>(LRG0/j;Lkotlin/jvm/functions/Function0;I)V

    .line 620
    .line 621
    .line 622
    invoke-interface {v3, v4}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 623
    .line 624
    .line 625
    :cond_15
    return-void
.end method

.method public static final m(LRG0/j;Lkotlin/jvm/functions/Function0;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p3, p2}, LRG0/i;->l(LRG0/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final synthetic n(LRG0/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LRG0/i;->l(LRG0/j;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method
