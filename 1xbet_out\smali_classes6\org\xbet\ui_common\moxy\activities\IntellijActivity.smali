.class public abstract Lorg/xbet/ui_common/moxy/activities/IntellijActivity;
.super Lmoxy/MvpAppCompatActivity;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/ui_common/moxy/views/BaseNewView;
.implements LfX0/g;
.implements Lorg/xbet/ui_common/dialogs/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/ui_common/moxy/activities/IntellijActivity$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ae\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\'\u0018\u0000 e2\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u0004:\u0001fB\u0007\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u000f\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0011\u0010\u000b\u001a\u0004\u0018\u00010\nH\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\u000e\u001a\u00020\rH\u0015\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u0015H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\r\u0010\u0019\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0019\u0010\u0006J\r\u0010\u001a\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u001a\u0010\u0006J\u0015\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u000f\u0010\u001e\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u0008\u001e\u0010\u0006J\u0017\u0010 \u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008 \u0010\u0014J\u000f\u0010\"\u001a\u00020!H\u0016\u00a2\u0006\u0004\u0008\"\u0010#J\u0019\u0010&\u001a\u00020\u00122\u0008\u0010%\u001a\u0004\u0018\u00010$H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010)\u001a\u00020\u00122\u0006\u0010(\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008)\u0010\u0014J\u000f\u0010*\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0006J\u000f\u0010+\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008+\u0010\u0006J\u000f\u0010,\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u0008,\u0010\u0006J\u000f\u0010-\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u0008-\u0010\u0006J\u0017\u0010/\u001a\u00020\u00122\u0008\u0008\u0002\u0010.\u001a\u00020\u0010\u00a2\u0006\u0004\u0008/\u0010\u0014J\u000f\u00100\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u00080\u0010\u0006J\u000f\u00101\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u00081\u0010\u0006J\u0019\u00104\u001a\u00020\u00122\u0008\u00103\u001a\u0004\u0018\u000102H\u0014\u00a2\u0006\u0004\u00084\u00105J\u000f\u00106\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u00086\u0010\u0006J\u000f\u00107\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u00087\u0010\u0006R\u001c\u0010=\u001a\u0004\u0018\u0001088\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u00089\u0010:\u001a\u0004\u0008;\u0010<R\u001b\u0010C\u001a\u00020>8DX\u0084\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008?\u0010@\u001a\u0004\u0008A\u0010BR\u001b\u0010H\u001a\u00020D8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008E\u0010@\u001a\u0004\u0008F\u0010GR\u001b\u0010M\u001a\u00020I8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008J\u0010@\u001a\u0004\u0008K\u0010LR\u0016\u0010Q\u001a\u00020N8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u001a\u0010U\u001a\u0008\u0012\u0004\u0012\u00020\u00100R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0018\u0010Y\u001a\u0004\u0018\u00010V8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\u0018\u0010]\u001a\u0004\u0018\u00010Z8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0018\u0010`\u001a\u0004\u0018\u00010\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0011\u0010d\u001a\u00020a8F\u00a2\u0006\u0006\u001a\u0004\u0008b\u0010c\u00a8\u0006g"
    }
    d2 = {
        "Lorg/xbet/ui_common/moxy/activities/IntellijActivity;",
        "Lmoxy/MvpAppCompatActivity;",
        "Lorg/xbet/ui_common/moxy/views/BaseNewView;",
        "LfX0/g;",
        "Lorg/xbet/ui_common/dialogs/d;",
        "<init>",
        "()V",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "g0",
        "()Lcom/xbet/onexcore/utils/ext/c;",
        "Landroid/view/View;",
        "Q",
        "()Landroid/view/View;",
        "",
        "k0",
        "()I",
        "",
        "show",
        "",
        "C",
        "(Z)V",
        "",
        "throwable",
        "onError",
        "(Ljava/lang/Throwable;)V",
        "j0",
        "R",
        "",
        "T",
        "(Ljava/lang/Throwable;)Ljava/lang/String;",
        "Z",
        "isAvailable",
        "b0",
        "Landroidx/appcompat/app/d;",
        "getDelegate",
        "()Landroidx/appcompat/app/d;",
        "Landroid/content/res/Configuration;",
        "overrideConfiguration",
        "applyOverrideConfiguration",
        "(Landroid/content/res/Configuration;)V",
        "needMargin",
        "f0",
        "x",
        "z",
        "onStart",
        "onResume",
        "isEnable",
        "h0",
        "onStop",
        "finish",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "a0",
        "onDestroy",
        "Landroidx/appcompat/widget/Toolbar;",
        "e",
        "Landroidx/appcompat/widget/Toolbar;",
        "X",
        "()Landroidx/appcompat/widget/Toolbar;",
        "toolbar",
        "LPW0/a;",
        "k",
        "Lkotlin/j;",
        "W",
        "()LPW0/a;",
        "rootBinding",
        "Lorg/xbet/ui_common/moxy/activities/h;",
        "l",
        "U",
        "()Lorg/xbet/ui_common/moxy/activities/h;",
        "component",
        "Lorg/xbet/ui_common/moxy/activities/k;",
        "m",
        "Y",
        "()Lorg/xbet/ui_common/moxy/activities/k;",
        "viewModel",
        "Lio/reactivex/disposables/a;",
        "n",
        "Lio/reactivex/disposables/a;",
        "connectStatusObserver",
        "Lio/reactivex/subjects/PublishSubject;",
        "o",
        "Lio/reactivex/subjects/PublishSubject;",
        "connectionStatusSubject",
        "Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;",
        "p",
        "Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;",
        "receiver",
        "Landroidx/appcompat/app/ViewPumpAppCompatDelegate;",
        "q",
        "Landroidx/appcompat/app/ViewPumpAppCompatDelegate;",
        "delegate",
        "r",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "networkConnectionUtil",
        "LhX0/b;",
        "V",
        "()LhX0/b;",
        "lockingAggregator",
        "s",
        "a",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final s:Lorg/xbet/ui_common/moxy/activities/IntellijActivity$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final t:I


# instance fields
.field public final e:Landroidx/appcompat/widget/Toolbar;

.field public final k:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Lio/reactivex/disposables/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lio/reactivex/subjects/PublishSubject;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lio/reactivex/subjects/PublishSubject<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;

.field public q:Landroidx/appcompat/app/ViewPumpAppCompatDelegate;

.field public r:Lcom/xbet/onexcore/utils/ext/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->s:Lorg/xbet/ui_common/moxy/activities/IntellijActivity$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->t:I

    .line 12
    .line 13
    const/4 v0, 0x1

    .line 14
    invoke-static {v0}, Landroidx/appcompat/app/d;->G(Z)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    invoke-direct {p0}, Lmoxy/MvpAppCompatActivity;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 5
    .line 6
    new-instance v1, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$b;-><init>(Landroid/app/Activity;)V

    .line 9
    .line 10
    .line 11
    invoke-static {v0, v1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->k:Lkotlin/j;

    .line 16
    .line 17
    new-instance v0, Lorg/xbet/ui_common/moxy/activities/c;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lorg/xbet/ui_common/moxy/activities/c;-><init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->l:Lkotlin/j;

    .line 27
    .line 28
    new-instance v0, Lorg/xbet/ui_common/moxy/activities/d;

    .line 29
    .line 30
    invoke-direct {v0, p0}, Lorg/xbet/ui_common/moxy/activities/d;-><init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)V

    .line 31
    .line 32
    .line 33
    new-instance v1, Landroidx/lifecycle/d0;

    .line 34
    .line 35
    const-class v2, Lorg/xbet/ui_common/moxy/activities/k;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$special$$inlined$viewModels$default$2;

    .line 42
    .line 43
    invoke-direct {v3, p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$special$$inlined$viewModels$default$2;-><init>(Landroidx/activity/ComponentActivity;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$special$$inlined$viewModels$default$3;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Landroidx/activity/ComponentActivity;)V

    .line 50
    .line 51
    .line 52
    invoke-direct {v1, v2, v3, v0, v4}, Landroidx/lifecycle/d0;-><init>(Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 53
    .line 54
    .line 55
    iput-object v1, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->m:Lkotlin/j;

    .line 56
    .line 57
    new-instance v0, Lio/reactivex/disposables/a;

    .line 58
    .line 59
    invoke-direct {v0}, Lio/reactivex/disposables/a;-><init>()V

    .line 60
    .line 61
    .line 62
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->n:Lio/reactivex/disposables/a;

    .line 63
    .line 64
    invoke-static {}, Lio/reactivex/subjects/PublishSubject;->Z()Lio/reactivex/subjects/PublishSubject;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->o:Lio/reactivex/subjects/PublishSubject;

    .line 69
    .line 70
    return-void
.end method

.method public static synthetic L(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->l0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic M(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Lorg/xbet/ui_common/moxy/activities/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->S(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Lorg/xbet/ui_common/moxy/activities/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic N(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->d0(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V

    return-void
.end method

.method public static synthetic O(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;Ljava/lang/Boolean;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->c0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;Ljava/lang/Boolean;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic P(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->e0(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V

    return-void
.end method

.method public static final S(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Lorg/xbet/ui_common/moxy/activities/h;
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    instance-of v0, p0, LQW0/b;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    check-cast p0, LQW0/b;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object p0, v1

    .line 14
    :goto_0
    const-class v0, Lorg/xbet/ui_common/moxy/activities/i;

    .line 15
    .line 16
    if-eqz p0, :cond_3

    .line 17
    .line 18
    invoke-interface {p0}, LQW0/b;->O1()Ljava/util/Map;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    invoke-interface {p0, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    check-cast p0, LBc/a;

    .line 27
    .line 28
    if-eqz p0, :cond_1

    .line 29
    .line 30
    invoke-interface {p0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, LQW0/a;

    .line 35
    .line 36
    goto :goto_1

    .line 37
    :cond_1
    move-object p0, v1

    .line 38
    :goto_1
    instance-of v2, p0, Lorg/xbet/ui_common/moxy/activities/i;

    .line 39
    .line 40
    if-nez v2, :cond_2

    .line 41
    .line 42
    goto :goto_2

    .line 43
    :cond_2
    move-object v1, p0

    .line 44
    :goto_2
    check-cast v1, Lorg/xbet/ui_common/moxy/activities/i;

    .line 45
    .line 46
    if-eqz v1, :cond_3

    .line 47
    .line 48
    invoke-virtual {v1}, Lorg/xbet/ui_common/moxy/activities/i;->a()Lorg/xbet/ui_common/moxy/activities/h;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    return-object p0

    .line 53
    :cond_3
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 54
    .line 55
    new-instance v1, Ljava/lang/StringBuilder;

    .line 56
    .line 57
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 58
    .line 59
    .line 60
    const-string v2, "Cannot create dependency "

    .line 61
    .line 62
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    throw p0
.end method

.method public static final c0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;Ljava/lang/Boolean;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->b0(Z)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->h0(Z)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final d0(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e0(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic i0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;ZILjava/lang/Object;)V
    .locals 0

    .line 1
    if-nez p3, :cond_1

    .line 2
    .line 3
    and-int/lit8 p2, p2, 0x1

    .line 4
    .line 5
    if-eqz p2, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->g0()Lcom/xbet/onexcore/utils/ext/c;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p1}, Lcom/xbet/onexcore/utils/ext/c;->a()Z

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->h0(Z)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_1
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    .line 20
    .line 21
    const-string p1, "Super calls with default arguments not supported in this target, function: showDisableNetworkView"

    .line 22
    .line 23
    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p0
.end method

.method public static final l0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->U()Lorg/xbet/ui_common/moxy/activities/h;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lorg/xbet/ui_common/moxy/activities/h;->a()Landroidx/lifecycle/e0$c;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method


# virtual methods
.method public C(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->W()LPW0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LPW0/a;->d:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/16 p1, 0x8

    .line 12
    .line 13
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public Q()Landroid/view/View;
    .locals 1

    .line 1
    const/4 v0, 0x0

    return-object v0
.end method

.method public final R()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, LhX0/b;->h()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final T(Ljava/lang/Throwable;)Ljava/lang/String;
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/ui_common/exception/UIResourcesException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xbet/ui_common/exception/UIResourcesException;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/ui_common/exception/UIResourcesException;->getResId()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    goto :goto_2

    .line 16
    :cond_0
    instance-of v0, p1, Lorg/xbet/ui_common/exception/UIOpenRulesException;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    check-cast p1, Lorg/xbet/ui_common/exception/UIOpenRulesException;

    .line 21
    .line 22
    invoke-virtual {p1}, Lorg/xbet/ui_common/exception/UIOpenRulesException;->getResId()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    goto :goto_2

    .line 31
    :cond_1
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 32
    .line 33
    if-nez v0, :cond_8

    .line 34
    .line 35
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 36
    .line 37
    if-nez v0, :cond_8

    .line 38
    .line 39
    instance-of v0, p1, Ljavax/net/ssl/SSLHandshakeException;

    .line 40
    .line 41
    if-nez v0, :cond_8

    .line 42
    .line 43
    instance-of v0, p1, Ljava/net/ConnectException;

    .line 44
    .line 45
    if-nez v0, :cond_8

    .line 46
    .line 47
    instance-of v0, p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    if-eqz v0, :cond_2

    .line 50
    .line 51
    goto :goto_1

    .line 52
    :cond_2
    instance-of v0, p1, Lorg/xbet/ui_common/exception/ParseResponseException;

    .line 53
    .line 54
    if-nez v0, :cond_7

    .line 55
    .line 56
    instance-of v0, p1, Ljava/io/EOFException;

    .line 57
    .line 58
    if-eqz v0, :cond_3

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_3
    instance-of v0, p1, Lretrofit2/HttpException;

    .line 62
    .line 63
    if-eqz v0, :cond_4

    .line 64
    .line 65
    sget p1, Lpb/k;->service_is_unavailable:I

    .line 66
    .line 67
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    goto :goto_2

    .line 72
    :cond_4
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 73
    .line 74
    if-eqz v0, :cond_5

    .line 75
    .line 76
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    goto :goto_2

    .line 81
    :cond_5
    instance-of v0, p1, Ld8/b;

    .line 82
    .line 83
    if-eqz v0, :cond_6

    .line 84
    .line 85
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    goto :goto_2

    .line 90
    :cond_6
    const/4 p1, 0x0

    .line 91
    goto :goto_2

    .line 92
    :cond_7
    :goto_0
    sget p1, Lpb/k;->unknown_service_error:I

    .line 93
    .line 94
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    goto :goto_2

    .line 99
    :cond_8
    :goto_1
    sget p1, Lpb/k;->no_connection_check_network:I

    .line 100
    .line 101
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    :goto_2
    if-eqz p1, :cond_9

    .line 106
    .line 107
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 108
    .line 109
    .line 110
    move-result v0

    .line 111
    if-nez v0, :cond_a

    .line 112
    .line 113
    :cond_9
    sget p1, Lpb/k;->unknown_error:I

    .line 114
    .line 115
    invoke-virtual {p0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    :cond_a
    return-object p1
.end method

.method public final U()Lorg/xbet/ui_common/moxy/activities/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->l:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/ui_common/moxy/activities/h;

    .line 8
    .line 9
    return-object v0
.end method

.method public final V()LhX0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LhX0/a;

    .line 6
    .line 7
    invoke-interface {v0}, LhX0/a;->f()LhX0/b;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final W()LPW0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->k:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LPW0/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public X()Landroidx/appcompat/widget/Toolbar;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->e:Landroidx/appcompat/widget/Toolbar;

    .line 2
    .line 3
    return-object v0
.end method

.method public final Y()Lorg/xbet/ui_common/moxy/activities/k;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->m:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/ui_common/moxy/activities/k;

    .line 8
    .line 9
    return-object v0
.end method

.method public Z()V
    .locals 0

    .line 1
    return-void
.end method

.method public a0()V
    .locals 0

    .line 1
    return-void
.end method

.method public applyOverrideConfiguration(Landroid/content/res/Configuration;)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget v0, p1, Landroid/content/res/Configuration;->uiMode:I

    .line 4
    .line 5
    iput v0, p1, Landroid/content/res/Configuration;->uiMode:I

    .line 6
    .line 7
    :cond_0
    invoke-super {p0, p1}, Landroid/app/Activity;->applyOverrideConfiguration(Landroid/content/res/Configuration;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public b0(Z)V
    .locals 0

    .line 1
    return-void
.end method

.method public f0(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p1}, LhX0/b;->u(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public finish()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/app/Activity;->finish()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->n:Lio/reactivex/disposables/a;

    .line 5
    .line 6
    invoke-virtual {v0}, Lio/reactivex/disposables/a;->d()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final g0()Lcom/xbet/onexcore/utils/ext/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->r:Lcom/xbet/onexcore/utils/ext/c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    new-instance v0, Lorg/xbet/ui_common/utils/f0;

    .line 6
    .line 7
    invoke-direct {v0, p0}, Lorg/xbet/ui_common/utils/f0;-><init>(Landroid/content/Context;)V

    .line 8
    .line 9
    .line 10
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->r:Lcom/xbet/onexcore/utils/ext/c;

    .line 11
    .line 12
    :cond_0
    return-object v0
.end method

.method public getDelegate()Landroidx/appcompat/app/d;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->q:Landroidx/appcompat/app/ViewPumpAppCompatDelegate;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    invoke-super {p0}, Landroidx/appcompat/app/AppCompatActivity;->getDelegate()Landroidx/appcompat/app/d;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p0, v0}, Lorg/xbet/onexlocalization/s;->d(Landroid/content/Context;Landroidx/appcompat/app/d;)Landroidx/appcompat/app/ViewPumpAppCompatDelegate;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->q:Landroidx/appcompat/app/ViewPumpAppCompatDelegate;

    .line 15
    .line 16
    return-object v0
.end method

.method public final h0(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p1}, LhX0/b;->f(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final j0()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, LhX0/b;->w()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public k0()I
    .locals 1

    .line 1
    const/4 v0, 0x0

    return v0
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 12

    .line 1
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Ljava/lang/StringBuilder;

    .line 10
    .line 11
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 12
    .line 13
    .line 14
    const-string v2, "Current screen: "

    .line 15
    .line 16
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 17
    .line 18
    .line 19
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 20
    .line 21
    .line 22
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    const-string v1, "onCreate"

    .line 27
    .line 28
    invoke-static {v1, v0}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->Y()Lorg/xbet/ui_common/moxy/activities/k;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    invoke-virtual {v0}, Lorg/xbet/ui_common/moxy/activities/k;->p3()Lkotlinx/coroutines/flow/e;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    .line 40
    .line 41
    new-instance v5, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$onCreate$1;

    .line 42
    .line 43
    const/4 v0, 0x0

    .line 44
    invoke-direct {v5, p0, v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$onCreate$1;-><init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;Lkotlin/coroutines/e;)V

    .line 45
    .line 46
    .line 47
    invoke-static {p0}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    new-instance v1, Lorg/xbet/ui_common/utils/CoroutineUtilsKt$observeWithLifecycle$2;

    .line 52
    .line 53
    const/4 v6, 0x0

    .line 54
    move-object v3, p0

    .line 55
    invoke-direct/range {v1 .. v6}, Lorg/xbet/ui_common/utils/CoroutineUtilsKt$observeWithLifecycle$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 56
    .line 57
    .line 58
    const/4 v10, 0x3

    .line 59
    const/4 v11, 0x0

    .line 60
    const/4 v7, 0x0

    .line 61
    const/4 v8, 0x0

    .line 62
    move-object v6, v0

    .line 63
    move-object v9, v1

    .line 64
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 65
    .line 66
    .line 67
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->a0()V

    .line 68
    .line 69
    .line 70
    invoke-static {p0}, LBX0/b;->a(Landroid/content/Context;)I

    .line 71
    .line 72
    .line 73
    move-result v0

    .line 74
    invoke-virtual {p0, v0}, Landroidx/appcompat/app/AppCompatActivity;->setTheme(I)V

    .line 75
    .line 76
    .line 77
    invoke-super {p0, p1}, Lmoxy/MvpAppCompatActivity;->onCreate(Landroid/os/Bundle;)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->Q()Landroid/view/View;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    if-eqz p1, :cond_0

    .line 85
    .line 86
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->W()LPW0/a;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    iget-object p1, p1, LPW0/a;->c:Landroid/widget/FrameLayout;

    .line 91
    .line 92
    invoke-virtual {p0, p1}, Landroidx/appcompat/app/AppCompatActivity;->setContentView(Landroid/view/View;)V

    .line 93
    .line 94
    .line 95
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->W()LPW0/a;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iget-object p1, p1, LPW0/a;->b:Landroid/widget/FrameLayout;

    .line 100
    .line 101
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->Q()Landroid/view/View;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 106
    .line 107
    .line 108
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->X()Landroidx/appcompat/widget/Toolbar;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    if-eqz p1, :cond_1

    .line 113
    .line 114
    invoke-virtual {p0, p1}, Landroidx/appcompat/app/AppCompatActivity;->setSupportActionBar(Landroidx/appcompat/widget/Toolbar;)V

    .line 115
    .line 116
    .line 117
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->k0()I

    .line 118
    .line 119
    .line 120
    move-result p1

    .line 121
    if-eqz p1, :cond_2

    .line 122
    .line 123
    invoke-virtual {p0}, Landroidx/appcompat/app/AppCompatActivity;->getSupportActionBar()Landroidx/appcompat/app/ActionBar;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    if-eqz p1, :cond_3

    .line 128
    .line 129
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->k0()I

    .line 130
    .line 131
    .line 132
    move-result v0

    .line 133
    invoke-virtual {p1, v0}, Landroidx/appcompat/app/ActionBar;->w(I)V

    .line 134
    .line 135
    .line 136
    goto :goto_0

    .line 137
    :cond_2
    invoke-virtual {p0}, Landroidx/appcompat/app/AppCompatActivity;->getSupportActionBar()Landroidx/appcompat/app/ActionBar;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    if-eqz p1, :cond_3

    .line 142
    .line 143
    const-string v0, ""

    .line 144
    .line 145
    invoke-virtual {p1, v0}, Landroidx/appcompat/app/ActionBar;->x(Ljava/lang/CharSequence;)V

    .line 146
    .line 147
    .line 148
    :cond_3
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->Z()V

    .line 149
    .line 150
    .line 151
    return-void
.end method

.method public onDestroy()V
    .locals 1

    .line 1
    invoke-super {p0}, Lmoxy/MvpAppCompatActivity;->onDestroy()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->q:Landroidx/appcompat/app/ViewPumpAppCompatDelegate;

    .line 6
    .line 7
    return-void
.end method

.method public onError(Ljava/lang/Throwable;)V
    .locals 1
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->C(Z)V

    .line 3
    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->T(Ljava/lang/Throwable;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-interface {v0, p1}, LhX0/b;->t(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, LhX0/b;->q(Landroidx/fragment/app/FragmentActivity;)V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Lmoxy/MvpAppCompatActivity;->onResume()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->g0()Lcom/xbet/onexcore/utils/ext/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, Lcom/xbet/onexcore/utils/ext/c;->a()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->h0(Z)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->b0(Z)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public onStart()V
    .locals 11

    .line 1
    :try_start_0
    invoke-super {p0}, Lmoxy/MvpAppCompatActivity;->onStart()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;

    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->o:Lio/reactivex/subjects/PublishSubject;

    .line 7
    .line 8
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;-><init>(Lio/reactivex/subjects/PublishSubject;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Landroid/content/IntentFilter;

    .line 12
    .line 13
    const-string v2, "android.net.conn.CONNECTIVITY_CHANGE"

    .line 14
    .line 15
    invoke-direct {v1, v2}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0, v0, v1}, Landroid/content/Context;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 19
    .line 20
    .line 21
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->p:Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->n:Lio/reactivex/disposables/a;

    .line 24
    .line 25
    iget-object v1, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->o:Lio/reactivex/subjects/PublishSubject;

    .line 26
    .line 27
    const-wide/16 v2, 0x1

    .line 28
    .line 29
    invoke-virtual {v1, v2, v3}, Lrc/n;->K(J)Lrc/n;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    sget-object v4, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 34
    .line 35
    invoke-virtual {v1, v2, v3, v4}, Lrc/n;->h(JLjava/util/concurrent/TimeUnit;)Lrc/n;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    const/4 v9, 0x7

    .line 40
    const/4 v10, 0x0

    .line 41
    const/4 v6, 0x0

    .line 42
    const/4 v7, 0x0

    .line 43
    const/4 v8, 0x0

    .line 44
    invoke-static/range {v5 .. v10}, LKX0/m;->r(Lrc/n;Lrc/s;Lrc/s;Lrc/s;ILjava/lang/Object;)Lrc/n;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    new-instance v2, Lorg/xbet/ui_common/moxy/activities/e;

    .line 49
    .line 50
    invoke-direct {v2, p0}, Lorg/xbet/ui_common/moxy/activities/e;-><init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)V

    .line 51
    .line 52
    .line 53
    new-instance v3, Lorg/xbet/ui_common/moxy/activities/f;

    .line 54
    .line 55
    invoke-direct {v3, v2}, Lorg/xbet/ui_common/moxy/activities/f;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 56
    .line 57
    .line 58
    sget-object v2, Lorg/xbet/ui_common/moxy/activities/IntellijActivity$onStart$3;->INSTANCE:Lorg/xbet/ui_common/moxy/activities/IntellijActivity$onStart$3;

    .line 59
    .line 60
    new-instance v4, Lorg/xbet/ui_common/moxy/activities/g;

    .line 61
    .line 62
    invoke-direct {v4, v2}, Lorg/xbet/ui_common/moxy/activities/g;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {v1, v3, v4}, Lrc/n;->N(Lvc/g;Lvc/g;)Lio/reactivex/disposables/b;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-virtual {v0, v1}, Lio/reactivex/disposables/a;->c(Lio/reactivex/disposables/b;)Z

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :catch_0
    move-exception v0

    .line 74
    invoke-virtual {p0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-static {v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->m(Landroidx/fragment/app/FragmentManager;)Landroidx/fragment/app/Fragment;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    instance-of v2, v0, Ljava/lang/IllegalStateException;

    .line 83
    .line 84
    const-string v3, " "

    .line 85
    .line 86
    if-nez v2, :cond_1

    .line 87
    .line 88
    instance-of v2, v0, Ljava/lang/ClassCastException;

    .line 89
    .line 90
    if-nez v2, :cond_1

    .line 91
    .line 92
    instance-of v2, v0, Landroid/os/BadParcelableException;

    .line 93
    .line 94
    if-eqz v2, :cond_0

    .line 95
    .line 96
    goto :goto_0

    .line 97
    :cond_0
    new-instance v2, Ljava/lang/Throwable;

    .line 98
    .line 99
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v4

    .line 103
    new-instance v5, Ljava/lang/StringBuilder;

    .line 104
    .line 105
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 106
    .line 107
    .line 108
    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 112
    .line 113
    .line 114
    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 115
    .line 116
    .line 117
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    invoke-direct {v2, v1, v0}, Ljava/lang/Throwable;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 122
    .line 123
    .line 124
    goto :goto_1

    .line 125
    :cond_1
    :goto_0
    new-instance v2, Lorg/xbet/ui_common/moxy/activities/OnCreateException;

    .line 126
    .line 127
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 128
    .line 129
    .line 130
    move-result-object v0

    .line 131
    new-instance v4, Ljava/lang/StringBuilder;

    .line 132
    .line 133
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 134
    .line 135
    .line 136
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 137
    .line 138
    .line 139
    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 140
    .line 141
    .line 142
    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 143
    .line 144
    .line 145
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    invoke-direct {v2, v0}, Lorg/xbet/ui_common/moxy/activities/OnCreateException;-><init>(Ljava/lang/String;)V

    .line 150
    .line 151
    .line 152
    :goto_1
    throw v2
.end method

.method public onStop()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->p:Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0, v0}, Landroid/content/Context;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->p:Lorg/xbet/ui_common/moxy/activities/ConnectionStatusReceiver;

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->n:Lio/reactivex/disposables/a;

    .line 12
    .line 13
    invoke-virtual {v0}, Lio/reactivex/disposables/a;->d()V

    .line 14
    .line 15
    .line 16
    invoke-super {p0}, Lmoxy/MvpAppCompatActivity;->onStop()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public x()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->V()LhX0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, LhX0/b;->i()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public z()V
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    const/4 v2, 0x0

    .line 4
    invoke-static {p0, v2, v0, v1}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->i0(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;ZILjava/lang/Object;)V

    .line 5
    .line 6
    .line 7
    return-void
.end method
