.class public final synthetic LL1/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/c;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL1/l;->a:Landroidx/media3/exoplayer/video/c;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LL1/l;->a:Landroidx/media3/exoplayer/video/c;

    invoke-static {v0}, Landroidx/media3/exoplayer/video/c;->b(Landroidx/media3/exoplayer/video/c;)V

    return-void
.end method
