.class public final synthetic LTG0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:Lorg/xbet/uikit/components/lottie_empty/n;

.field public final synthetic d:I

.field public final synthetic e:Ljava/util/List;

.field public final synthetic f:I


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LTG0/b;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LTG0/b;->b:Lkotlin/jvm/functions/Function0;

    iput-object p3, p0, LTG0/b;->c:Lorg/xbet/uikit/components/lottie_empty/n;

    iput p4, p0, LTG0/b;->d:I

    iput-object p5, p0, LTG0/b;->e:Ljava/util/List;

    iput p6, p0, LTG0/b;->f:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget-object v0, p0, LTG0/b;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LTG0/b;->b:Lkotlin/jvm/functions/Function0;

    iget-object v2, p0, LTG0/b;->c:Lorg/xbet/uikit/components/lottie_empty/n;

    iget v3, p0, LTG0/b;->d:I

    iget-object v4, p0, LTG0/b;->e:Ljava/util/List;

    iget v5, p0, LTG0/b;->f:I

    move-object v6, p1

    check-cast v6, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v7

    invoke-static/range {v0 .. v7}, LTG0/c;->b(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/lottie_empty/n;ILjava/util/List;ILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
