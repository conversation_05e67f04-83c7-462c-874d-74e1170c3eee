.class final synthetic Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$1;
.super Lkotlin/jvm/internal/AdaptedFunctionReference;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;-><init>(Lorg/xbet/core/domain/usecases/u;LwX0/c;LxX0/a;Lak/a;Lm8/a;Lorg/xbet/core/domain/usecases/game_state/l;Lorg/xbet/core/domain/usecases/AddCommandScenario;Lorg/xbet/core/domain/usecases/bet/d;Lorg/xbet/core/domain/usecases/bet/h;Lorg/xbet/core/domain/usecases/bet/o;Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;LWv/b;Lorg/xbet/core/domain/usecases/d;Lorg/xbet/core/domain/usecases/game_state/a;Lorg/xbet/core/domain/usecases/balance/a;LVv/d;Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;Lorg/xbet/tile_matching/domain/usecases/c;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/AdaptedFunctionReference;",
        "Lkotlin/jvm/functions/Function2<",
        "LTv/d;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "handleCommand(Lorg/xbet/core/domain/GameCommand;)V"

    const/4 v6, 0x4

    const/4 v1, 0x2

    const-class v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    const-string v4, "handleCommand"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/AdaptedFunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public final invoke(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LTv/d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lkotlin/jvm/internal/AdaptedFunctionReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    invoke-static {v0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LTv/d;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$1;->invoke(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
