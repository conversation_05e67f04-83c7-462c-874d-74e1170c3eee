.class public final Lv21/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv21/e;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lv21/s;",
        "Lv21/e;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "styleType",
        "<init>",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;)V",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "()Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lv21/s;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lv21/s;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 2
    .line 3
    return-object v0
.end method
