.class public final Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 2

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    move-result-object p1

    invoke-direct {v0, v1, p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;)V

    return-object v0
.end method

.method public final b(I)[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel$a;->a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel$a;->b(I)[Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    move-result-object p1

    return-object p1
.end method
