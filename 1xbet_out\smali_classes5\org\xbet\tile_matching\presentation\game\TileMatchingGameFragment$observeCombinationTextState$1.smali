.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingGameFragment$observeCombinationTextState$1"
    f = "TileMatchingGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->f3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$a;

    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 21
    .line 22
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$a;

    .line 23
    .line 24
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$a;->a()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    invoke-static {v0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->G2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)Z

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    if-nez p1, :cond_3

    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 35
    .line 36
    sget-object v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->ODYSSEY:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 37
    .line 38
    const/4 v2, 0x2

    .line 39
    const/4 v3, 0x0

    .line 40
    invoke-static {p1, v0, v1, v2, v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->V2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;IILjava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_0
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 45
    .line 46
    if-eqz v0, :cond_2

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeCombinationTextState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 49
    .line 50
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    iget-object v0, v0, LxT0/a;->m:Landroid/widget/TextView;

    .line 55
    .line 56
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;

    .line 57
    .line 58
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$c;->a()Z

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    if-eqz p1, :cond_1

    .line 63
    .line 64
    goto :goto_0

    .line 65
    :cond_1
    const/16 v1, 0x8

    .line 66
    .line 67
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 68
    .line 69
    .line 70
    goto :goto_1

    .line 71
    :cond_2
    instance-of p1, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$b$b;

    .line 72
    .line 73
    if-eqz p1, :cond_4

    .line 74
    .line 75
    :cond_3
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 76
    .line 77
    return-object p1

    .line 78
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 79
    .line 80
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 81
    .line 82
    .line 83
    throw p1

    .line 84
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 85
    .line 86
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 87
    .line 88
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw p1
.end method
