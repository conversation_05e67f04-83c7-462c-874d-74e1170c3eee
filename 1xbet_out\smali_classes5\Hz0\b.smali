.class public final LHz0/b;
.super Ljava/lang/Object;


# static fields
.field public static actionBack:I = 0x7f0a005e

.field public static actionMenu:I = 0x7f0a0065

.field public static actionQuickBet:I = 0x7f0a0069

.field public static bOneTeamEvents:I = 0x7f0a013b

.field public static bOneTeamImages:I = 0x7f0a013c

.field public static bScoreEnd:I = 0x7f0a013e

.field public static bScoreStart:I = 0x7f0a013f

.field public static bTeamImagesBottom:I = 0x7f0a0140

.field public static bTeamImagesTop:I = 0x7f0a0141

.field public static bTeamNamesBottom:I = 0x7f0a0142

.field public static bTeamNamesTop:I = 0x7f0a0143

.field public static bTeamsLogo:I = 0x7f0a0144

.field public static bTotalScore:I = 0x7f0a0145

.field public static bTotalScoreDividers:I = 0x7f0a0146

.field public static bTwoTeamEvents:I = 0x7f0a0147

.field public static bTwoTeamImages:I = 0x7f0a0148

.field public static bettingContainer:I = 0x7f0a01eb

.field public static btnExpand:I = 0x7f0a02b1

.field public static btnTryAgain:I = 0x7f0a02f7

.field public static btvTeamsName:I = 0x7f0a0319

.field public static ciBroadcasting:I = 0x7f0a0465

.field public static ciInformation:I = 0x7f0a0466

.field public static clGameRootContainer:I = 0x7f0a04a5

.field public static clGameScore:I = 0x7f0a04a6

.field public static constraintContainer:I = 0x7f0a0559

.field public static content_layout:I = 0x7f0a058a

.field public static dividerGuideline:I = 0x7f0a0678

.field public static fCardsContainer:I = 0x7f0a07a7

.field public static fHumidity:I = 0x7f0a07a9

.field public static fPressure:I = 0x7f0a07aa

.field public static fTabsContainer:I = 0x7f0a07ab

.field public static fTemperature:I = 0x7f0a07ac

.field public static fTimer:I = 0x7f0a07ad

.field public static fWeatherContainer:I = 0x7f0a07ae

.field public static fWind:I = 0x7f0a07af

.field public static firstTeamLogo:I = 0x7f0a0840

.field public static firstTeamVerticalSeparatorView:I = 0x7f0a0847

.field public static flFirstTeamInfo:I = 0x7f0a0886

.field public static flFirstTeamLogo:I = 0x7f0a0887

.field public static flSecondTeamInfo:I = 0x7f0a089b

.field public static flSecondTeamLogo:I = 0x7f0a089c

.field public static flTeamOneLogos:I = 0x7f0a08a5

.field public static flTeamOneResult:I = 0x7f0a08a6

.field public static flTeamTwoLogos:I = 0x7f0a08a7

.field public static flTeamTwoResult:I = 0x7f0a08a8

.field public static footballField:I = 0x7f0a08c3

.field public static gLHeader:I = 0x7f0a08fa

.field public static gLogosPairTeams:I = 0x7f0a08fe

.field public static gLogosTeams:I = 0x7f0a08ff

.field public static gOneTeamAssistant:I = 0x7f0a0900

.field public static gOneTeamCommon:I = 0x7f0a0901

.field public static gTvTeamPointsScores:I = 0x7f0a0903

.field public static gTvTeamScores:I = 0x7f0a0904

.field public static gTwoTeamAssistant:I = 0x7f0a0905

.field public static gTwoTeamCommon:I = 0x7f0a0906

.field public static glBottom:I = 0x7f0a0966

.field public static grHors:I = 0x7f0a0979

.field public static ivBackground:I = 0x7f0a0be8

.field public static ivCorners:I = 0x7f0a0c1f

.field public static ivEventType:I = 0x7f0a0c45

.field public static ivFirstPlayerOneTeamImage:I = 0x7f0a0c5d

.field public static ivFirstPlayerPairTeamLogo:I = 0x7f0a0c5e

.field public static ivFirstPlayerTwoTeamImage:I = 0x7f0a0c63

.field public static ivFirstTeam:I = 0x7f0a0c66

.field public static ivFirstTeamFavorite:I = 0x7f0a0c67

.field public static ivFirstTeamImage:I = 0x7f0a0c69

.field public static ivFullScreen:I = 0x7f0a0c73

.field public static ivHumidity:I = 0x7f0a0c82

.field public static ivMain:I = 0x7f0a0c95

.field public static ivOne:I = 0x7f0a0ca8

.field public static ivOneTeam:I = 0x7f0a0caa

.field public static ivOneTeamAssistantEvent:I = 0x7f0a0cab

.field public static ivOneTeamAssistantPlayer:I = 0x7f0a0cac

.field public static ivOneTeamCommonEvent:I = 0x7f0a0cad

.field public static ivOneTeamCommonPlayer:I = 0x7f0a0cae

.field public static ivOneTeamFirstPlayer:I = 0x7f0a0caf

.field public static ivOneTeamLogo:I = 0x7f0a0cb0

.field public static ivOneTeamOneCommonEvent:I = 0x7f0a0cb1

.field public static ivOneTeamSecondPlayer:I = 0x7f0a0cb2

.field public static ivPairTeamFirstPlayerOneTeamFavorite:I = 0x7f0a0cb6

.field public static ivPairTeamFirstPlayerTwoTeamFavorite:I = 0x7f0a0cb7

.field public static ivPairTeamSecondPlayerOneTeamFavorite:I = 0x7f0a0cb8

.field public static ivPairTeamSecondPlayerTwoTeamFavorite:I = 0x7f0a0cb9

.field public static ivPressure:I = 0x7f0a0cd2

.field public static ivRedCards:I = 0x7f0a0ce0

.field public static ivSecondPlayerOneTeamImage:I = 0x7f0a0cfa

.field public static ivSecondPlayerPairTeamLogo:I = 0x7f0a0cfb

.field public static ivSecondPlayerTwoTeamImage:I = 0x7f0a0d00

.field public static ivSecondTeam:I = 0x7f0a0d03

.field public static ivSecondTeamFavorite:I = 0x7f0a0d04

.field public static ivSecondTeamImage:I = 0x7f0a0d06

.field public static ivTeamIcon:I = 0x7f0a0d2a

.field public static ivTeamLogo:I = 0x7f0a0d2c

.field public static ivTeamOneInning:I = 0x7f0a0d31

.field public static ivTeamOneLogo:I = 0x7f0a0d32

.field public static ivTeamOneLogoContainer:I = 0x7f0a0d33

.field public static ivTeamOneSecondPlayerLogo:I = 0x7f0a0d36

.field public static ivTeamTwoInning:I = 0x7f0a0d3e

.field public static ivTeamTwoLogo:I = 0x7f0a0d3f

.field public static ivTeamTwoLogoContainer:I = 0x7f0a0d40

.field public static ivTeamTwoSecondPlayerLogo:I = 0x7f0a0d43

.field public static ivTemperature:I = 0x7f0a0d46

.field public static ivThree:I = 0x7f0a0d4c

.field public static ivTwo:I = 0x7f0a0d58

.field public static ivTwoTeam:I = 0x7f0a0d59

.field public static ivTwoTeamAssistantEvent:I = 0x7f0a0d5a

.field public static ivTwoTeamAssistantPlayer:I = 0x7f0a0d5b

.field public static ivTwoTeamCommonEvent:I = 0x7f0a0d5c

.field public static ivTwoTeamCommonPlayer:I = 0x7f0a0d5d

.field public static ivTwoTeamFirstPlayer:I = 0x7f0a0d5e

.field public static ivTwoTeamLogo:I = 0x7f0a0d5f

.field public static ivTwoTeamOneCommonEvent:I = 0x7f0a0d60

.field public static ivTwoTeamSecondPlayer:I = 0x7f0a0d61

.field public static ivWind:I = 0x7f0a0d71

.field public static ivYellowCards:I = 0x7f0a0d73

.field public static llErrorContent:I = 0x7f0a0e6e

.field public static llOneTeamPairContainerImages:I = 0x7f0a0e82

.field public static llPairTeamContainerLogos:I = 0x7f0a0e83

.field public static llShotResults:I = 0x7f0a0e96

.field public static llTwoTeamPairContainerImages:I = 0x7f0a0ea9

.field public static matchInfoContainer:I = 0x7f0a0f27

.field public static mathInfoGroup:I = 0x7f0a0f42

.field public static recyclerView:I = 0x7f0a11ad

.field public static relatedContainer:I = 0x7f0a11db

.field public static rvCompressedCard:I = 0x7f0a1269

.field public static rvContent:I = 0x7f0a126b

.field public static rvEvents:I = 0x7f0a126f

.field public static rvHostVsGuest:I = 0x7f0a1279

.field public static rvLineStatisticHeader:I = 0x7f0a1281

.field public static rvLineStatisticInfo:I = 0x7f0a1282

.field public static rvMatchInfoCards:I = 0x7f0a1287

.field public static rvOneTeam:I = 0x7f0a128f

.field public static rvPeriods:I = 0x7f0a1292

.field public static rvShortInfo:I = 0x7f0a12ab

.field public static rvTwoTeam:I = 0x7f0a12c1

.field public static secondTeamLogo:I = 0x7f0a1378

.field public static secondTeamVerticalSeparatorView:I = 0x7f0a137f

.field public static svShotResults:I = 0x7f0a1653

.field public static teamOneLogo:I = 0x7f0a16e4

.field public static teamTwoLogo:I = 0x7f0a16f4

.field public static teamsHorizontalSeparatorView:I = 0x7f0a1701

.field public static toolbar:I = 0x7f0a183e

.field public static toolbarTitle:I = 0x7f0a184a

.field public static tvAction:I = 0x7f0a1920

.field public static tvActiveZonesTitle:I = 0x7f0a1925

.field public static tvBroadcasting:I = 0x7f0a198d

.field public static tvCentralActionZone:I = 0x7f0a19a1

.field public static tvColonOne:I = 0x7f0a19d2

.field public static tvColonTwo:I = 0x7f0a19d3

.field public static tvCorners:I = 0x7f0a19e4

.field public static tvData:I = 0x7f0a1a0a

.field public static tvErrorMessage:I = 0x7f0a1a4a

.field public static tvErrorText:I = 0x7f0a1a4b

.field public static tvFirstName:I = 0x7f0a1a69

.field public static tvFirstTeamName:I = 0x7f0a1a85

.field public static tvFirstTeamRedCards:I = 0x7f0a1a88

.field public static tvFirstTeamScore:I = 0x7f0a1a89

.field public static tvGuest:I = 0x7f0a1acc

.field public static tvHeader:I = 0x7f0a1ace

.field public static tvHost:I = 0x7f0a1ad8

.field public static tvHours:I = 0x7f0a1adb

.field public static tvHumidity:I = 0x7f0a1ade

.field public static tvImageCount:I = 0x7f0a1ae1

.field public static tvInfo:I = 0x7f0a1ae2

.field public static tvInformation:I = 0x7f0a1ae4

.field public static tvLeftActionZone:I = 0x7f0a1af5

.field public static tvLocation:I = 0x7f0a1aff

.field public static tvMatchBaseInfo:I = 0x7f0a1b0f

.field public static tvMatchDescription:I = 0x7f0a1b10

.field public static tvMatchName:I = 0x7f0a1b11

.field public static tvMatchPeriodInfo:I = 0x7f0a1b12

.field public static tvMatchScore:I = 0x7f0a1b14

.field public static tvMinutes:I = 0x7f0a1b23

.field public static tvName:I = 0x7f0a1b31

.field public static tvOneTeamAssistantPlayerName:I = 0x7f0a1b55

.field public static tvOneTeamCommonPlayerName:I = 0x7f0a1b56

.field public static tvOneTeamGroupName:I = 0x7f0a1b57

.field public static tvOneTeamName:I = 0x7f0a1b58

.field public static tvPenaltyName:I = 0x7f0a1b69

.field public static tvPenaltyScore:I = 0x7f0a1b6a

.field public static tvPeriodTitle:I = 0x7f0a1b6d

.field public static tvPeriodsName:I = 0x7f0a1b6e

.field public static tvPointsTitle:I = 0x7f0a1b98

.field public static tvPressure:I = 0x7f0a1ba1

.field public static tvRedCards:I = 0x7f0a1bc4

.field public static tvRightActionZone:I = 0x7f0a1bdb

.field public static tvScore:I = 0x7f0a1bec

.field public static tvSecondName:I = 0x7f0a1c00

.field public static tvSecondTeamName:I = 0x7f0a1c1c

.field public static tvSecondTeamRedCards:I = 0x7f0a1c1f

.field public static tvSecondTeamScore:I = 0x7f0a1c20

.field public static tvSeconds:I = 0x7f0a1c27

.field public static tvTeamName:I = 0x7f0a1c76

.field public static tvTeamOneName:I = 0x7f0a1c79

.field public static tvTeamOnePointScore:I = 0x7f0a1c7b

.field public static tvTeamOneScore:I = 0x7f0a1c7c

.field public static tvTeamOneTotalScore:I = 0x7f0a1c7d

.field public static tvTeamOneValue:I = 0x7f0a1c7e

.field public static tvTeamTwoName:I = 0x7f0a1c82

.field public static tvTeamTwoPointScore:I = 0x7f0a1c84

.field public static tvTeamTwoScore:I = 0x7f0a1c85

.field public static tvTeamTwoTotalScore:I = 0x7f0a1c86

.field public static tvTeamTwoValue:I = 0x7f0a1c87

.field public static tvTeams:I = 0x7f0a1c88

.field public static tvTemperature:I = 0x7f0a1c8b

.field public static tvTimeEvent:I = 0x7f0a1c9f

.field public static tvTimeLeft:I = 0x7f0a1ca1

.field public static tvTimerInfo:I = 0x7f0a1ca4

.field public static tvTotalScoreName:I = 0x7f0a1cc2

.field public static tvTwoTeamAssistantPlayerName:I = 0x7f0a1cd3

.field public static tvTwoTeamCommonPlayerName:I = 0x7f0a1cd4

.field public static tvTwoTeamGroupName:I = 0x7f0a1cd5

.field public static tvTwoTeamName:I = 0x7f0a1cd6

.field public static tvWind:I = 0x7f0a1d0e

.field public static tvYellowCards:I = 0x7f0a1d38

.field public static vBottomDivider:I = 0x7f0a1e05

.field public static vFirstWinnerIndicate:I = 0x7f0a1e96

.field public static vHeaderBackground:I = 0x7f0a1ea2

.field public static vMatchTimer:I = 0x7f0a1ead

.field public static vScoreDivider:I = 0x7f0a1ec2

.field public static vScoreHeight:I = 0x7f0a1ec3

.field public static vSecondWinnerIndicate:I = 0x7f0a1ec8

.field public static vTeamOneFootballEvents:I = 0x7f0a1ede

.field public static vTeamOneTotalScoreDivider:I = 0x7f0a1edf

.field public static vTeamOneValueLine:I = 0x7f0a1ee0

.field public static vTeamTwoFootballEvents:I = 0x7f0a1ee2

.field public static vTeamTwoTotalScoreDivider:I = 0x7f0a1ee3

.field public static vTeamTwoValueLine:I = 0x7f0a1ee4

.field public static vTeamsDivider:I = 0x7f0a1ee5

.field public static vTopDivider:I = 0x7f0a1eeb

.field public static vTotalScoreNameDivider:I = 0x7f0a1eec

.field public static viewCardsShimmer:I = 0x7f0a1f34

.field public static vpBroadcasts:I = 0x7f0a1fb7


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
