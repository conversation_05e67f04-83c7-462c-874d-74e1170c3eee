.class public final Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0007\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J%\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\r\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u000eR\u0014\u0010\u0010\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000e\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$a;",
        "",
        "<init>",
        "()V",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "",
        "requestKey",
        "",
        "fromMakeBet",
        "",
        "a",
        "(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Z)V",
        "TAG",
        "Ljava/lang/String;",
        "REQUEST_KEY",
        "FROM_MAKE_BET_KEY",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Z)V
    .locals 1
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->S2(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;Ljava/lang/String;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p3}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->R2(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;Z)V

    .line 10
    .line 11
    .line 12
    const-string p2, "SelectPromoCodeDialogFragment"

    .line 13
    .line 14
    invoke-virtual {v0, p1, p2}, Landroidx/fragment/app/l;->show(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method
