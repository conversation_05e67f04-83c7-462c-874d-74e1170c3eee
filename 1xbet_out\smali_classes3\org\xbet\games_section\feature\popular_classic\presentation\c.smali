.class public final synthetic Lorg/xbet/games_section/feature/popular_classic/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/c;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/c;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;

    invoke-static {v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;->y2(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
