.class public final synthetic LKX0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Lkotlin/jvm/internal/Ref$IntRef;

.field public final synthetic c:J


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Lkotlin/jvm/internal/Ref$IntRef;J)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKX0/l;->a:Ljava/lang/String;

    iput-object p2, p0, LKX0/l;->b:Lkotlin/jvm/internal/Ref$IntRef;

    iput-wide p3, p0, LKX0/l;->c:J

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LKX0/l;->a:Ljava/lang/String;

    iget-object v1, p0, LKX0/l;->b:Lkotlin/jvm/internal/Ref$IntRef;

    iget-wide v2, p0, LKX0/l;->c:J

    check-cast p1, Ljava/lang/Long;

    invoke-static {v0, v1, v2, v3, p1}, LKX0/m;->k(Ljava/lang/String;Lkotlin/jvm/internal/Ref$IntRef;JLjava/lang/Long;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
