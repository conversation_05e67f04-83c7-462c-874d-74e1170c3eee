.class public final Lh2/b$d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lh2/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation


# instance fields
.field public final a:Lh2/b$g;


# direct methods
.method public constructor <init>(Lh2/b$g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lh2/b$d;->a:Lh2/b$g;

    .line 5
    .line 6
    return-void
.end method

.method public static synthetic a(Lh2/b$d;)Lh2/b$g;
    .locals 0

    .line 1
    iget-object p0, p0, Lh2/b$d;->a:Lh2/b$g;

    .line 2
    .line 3
    return-object p0
.end method
