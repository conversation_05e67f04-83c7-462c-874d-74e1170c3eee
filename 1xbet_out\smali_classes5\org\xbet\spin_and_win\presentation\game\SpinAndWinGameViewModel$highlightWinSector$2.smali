.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameViewModel$highlightWinSector$2"
    f = "SpinAndWinGameViewModel.kt"
    l = {
        0x15d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->n4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;

    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Ldz0/b;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->E3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/d;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-virtual {p1}, Lez0/d;->a()Ldz0/b;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {p1}, Ldz0/b;->d()Lorg/xbet/spin_and_win/domain/model/SpinAndWinGameStateEnum;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    sget-object v3, Lorg/xbet/spin_and_win/domain/model/SpinAndWinGameStateEnum;->WIN:Lorg/xbet/spin_and_win/domain/model/SpinAndWinGameStateEnum;

    .line 46
    .line 47
    if-ne v1, v3, :cond_5

    .line 48
    .line 49
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 50
    .line 51
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->G3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/f;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-virtual {v1}, Lez0/f;->a()Lkotlinx/coroutines/flow/e;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    iput v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->label:I

    .line 62
    .line 63
    invoke-static {v1, p0}, Lkotlinx/coroutines/flow/g;->N(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    if-ne v1, v0, :cond_2

    .line 68
    .line 69
    return-object v0

    .line 70
    :cond_2
    move-object v0, p1

    .line 71
    move-object p1, v1

    .line 72
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 73
    .line 74
    if-eqz p1, :cond_5

    .line 75
    .line 76
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$highlightWinSector$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 77
    .line 78
    new-instance v3, Ljava/util/ArrayList;

    .line 79
    .line 80
    const/16 v4, 0xa

    .line 81
    .line 82
    invoke-static {p1, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 83
    .line 84
    .line 85
    move-result v4

    .line 86
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 87
    .line 88
    .line 89
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 94
    .line 95
    .line 96
    move-result v4

    .line 97
    if-eqz v4, :cond_4

    .line 98
    .line 99
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 100
    .line 101
    .line 102
    move-result-object v4

    .line 103
    move-object v5, v4

    .line 104
    check-cast v5, Ldz0/a;

    .line 105
    .line 106
    invoke-virtual {v5}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 107
    .line 108
    .line 109
    move-result-object v4

    .line 110
    invoke-virtual {v0}, Ldz0/b;->e()Ljava/util/List;

    .line 111
    .line 112
    .line 113
    move-result-object v6

    .line 114
    invoke-static {v6}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v6

    .line 118
    if-eq v4, v6, :cond_3

    .line 119
    .line 120
    const/16 v12, 0xf

    .line 121
    .line 122
    const/4 v13, 0x0

    .line 123
    const-wide/16 v6, 0x0

    .line 124
    .line 125
    const/4 v8, 0x0

    .line 126
    const/4 v9, 0x0

    .line 127
    const/4 v10, 0x0

    .line 128
    const/4 v11, 0x0

    .line 129
    invoke-static/range {v5 .. v13}, Ldz0/a;->b(Ldz0/a;DLorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/String;Lorg/xbet/games_section/api/models/GameBonusType;ZILjava/lang/Object;)Ldz0/a;

    .line 130
    .line 131
    .line 132
    move-result-object v5

    .line 133
    :cond_3
    invoke-interface {v3, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    goto :goto_1

    .line 137
    :cond_4
    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 138
    .line 139
    invoke-direct {p1, v2}, Lorg/xbet/spin_and_win/presentation/game/a$b;-><init>(Z)V

    .line 140
    .line 141
    .line 142
    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/a$c;

    .line 143
    .line 144
    invoke-direct {p1, v3}, Lorg/xbet/spin_and_win/presentation/game/a$c;-><init>(Ljava/util/List;)V

    .line 145
    .line 146
    .line 147
    invoke-static {v1, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->W3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/a;)V

    .line 148
    .line 149
    .line 150
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 151
    .line 152
    return-object p1
.end method
