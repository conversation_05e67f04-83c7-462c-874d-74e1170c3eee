.class public Lorg/spongycastle/util/test/FixedSecureRandom$a;
.super Lorg/spongycastle/util/test/FixedSecureRandom$c;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/util/test/FixedSecureRandom;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public constructor <init>(I[B)V
    .locals 0

    .line 2
    invoke-static {p1, p2}, Lorg/spongycastle/util/test/FixedSecureRandom;->access$000(I[B)[B

    move-result-object p1

    invoke-direct {p0, p1}, Lorg/spongycastle/util/test/FixedSecureRandom$c;-><init>([B)V

    return-void
.end method

.method public constructor <init>([B)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/spongycastle/util/test/FixedSecureRandom$c;-><init>([B)V

    return-void
.end method
