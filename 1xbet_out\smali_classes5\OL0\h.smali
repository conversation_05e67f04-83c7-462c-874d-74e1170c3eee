.class public interface abstract LOL0/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LOL0/h$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u0008J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\u0008\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\nH&\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\rH&\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "LOL0/h;",
        "",
        "Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;",
        "fragment",
        "",
        "c",
        "(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;)V",
        "Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;",
        "a",
        "(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;)V",
        "Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;",
        "b",
        "(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;)V",
        "Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;",
        "d",
        "(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;)V
    .param p1    # Lorg/xbet/statistic/stadium/impl/track/fragment/TrackFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;)V
    .param p1    # Lorg/xbet/statistic/stadium/impl/arena/fragment/ArenaFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;)V
    .param p1    # Lorg/xbet/statistic/stadium/impl/statium/fragment/StadiumFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract d(Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;)V
    .param p1    # Lorg/xbet/statistic/stadium/impl/route/fragment/RouteFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
