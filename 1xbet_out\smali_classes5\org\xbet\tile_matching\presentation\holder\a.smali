.class public final synthetic Lorg/xbet/tile_matching/presentation/holder/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/holder/a;->a:Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/a;->a:Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->g4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
