.class public interface abstract LfP0/c$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LfP0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008g\u0018\u00002\u00020\u0001J\u00f3\u0001\u00101\u001a\u0002002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u00122\u0008\u0008\u0001\u0010\u0015\u001a\u00020\u00142\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00162\u0008\u0008\u0001\u0010\u0019\u001a\u00020\u00182\u0008\u0008\u0001\u0010\u001b\u001a\u00020\u001a2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u001e\u001a\u00020\u001d2\u0008\u0008\u0001\u0010 \u001a\u00020\u001f2\u0008\u0008\u0001\u0010!\u001a\u00020\u001f2\u0008\u0008\u0001\u0010#\u001a\u00020\"2\u0008\u0008\u0001\u0010%\u001a\u00020$2\u0008\u0008\u0001\u0010\'\u001a\u00020&2\u0008\u0008\u0001\u0010)\u001a\u00020(2\u0008\u0008\u0001\u0010+\u001a\u00020*2\u0008\u0008\u0001\u0010-\u001a\u00020,2\u0008\u0008\u0001\u0010/\u001a\u00020.H&\u00a2\u0006\u0004\u00081\u00102\u00a8\u00063"
    }
    d2 = {
        "LfP0/c$a;",
        "",
        "LQW0/c;",
        "coroutinesLib",
        "LiR/a;",
        "fatmanFeature",
        "LJo0/a;",
        "specialEventFeature",
        "LEN0/f;",
        "statisticCoreFeature",
        "LGL0/a;",
        "stadiumFeature",
        "LLD0/a;",
        "statisticFeature",
        "",
        "screenName",
        "LHX0/e;",
        "resourceManager",
        "LwX0/c;",
        "router",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LTn/a;",
        "sportRepository",
        "Lorg/xbet/onexdatabase/OnexDatabase;",
        "onexDatabase",
        "gameId",
        "Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;",
        "typeParam",
        "",
        "eventId",
        "teamClId",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Li8/l;",
        "getThemeStreamUseCase",
        "Lorg/xbet/analytics/domain/scope/StatisticAnalytics;",
        "statisticAnalytics",
        "",
        "sportId",
        "LSX0/a;",
        "lottieConfigurator",
        "Lc8/h;",
        "requestParamsDataSource",
        "LHg/d;",
        "specialEventAnalytics",
        "LfP0/c;",
        "a",
        "(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;IILorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;JLSX0/a;Lc8/h;LHg/d;)LfP0/c;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(LQW0/c;LiR/a;LJo0/a;LEN0/f;LGL0/a;LLD0/a;Ljava/lang/String;LHX0/e;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;IILorg/xbet/ui_common/utils/internet/a;Li8/l;Lorg/xbet/analytics/domain/scope/StatisticAnalytics;JLSX0/a;Lc8/h;LHg/d;)LfP0/c;
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LiR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LJo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LEN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LGL0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LLD0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LTn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/onexdatabase/OnexDatabase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/statistic/team/impl/team_statistic/presentation/models/TypeParam;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lorg/xbet/analytics/domain/scope/StatisticAnalytics;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LHg/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
