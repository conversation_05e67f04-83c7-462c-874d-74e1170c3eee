.class public final Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0086B\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;",
        "",
        "Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;",
        "dailyRepository",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;)V",
        "",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 54
    .line 55
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;->q()Z

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    if-eqz p1, :cond_4

    .line 60
    .line 61
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 62
    .line 63
    iput v3, v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase$invoke$1;->label:I

    .line 64
    .line 65
    invoke-virtual {p1, v0}, Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;->n(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v1, :cond_3

    .line 70
    .line 71
    return-object v1

    .line 72
    :cond_3
    :goto_1
    check-cast p1, Lp40/a;

    .line 73
    .line 74
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadDayPrizesUseCase;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 75
    .line 76
    invoke-virtual {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;->r(Lp40/a;)V

    .line 77
    .line 78
    .line 79
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 80
    .line 81
    return-object p1
.end method
