.class public final synthetic Lorg/xbet/african_roulette/presentation/game/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/a;->a:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->z2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
