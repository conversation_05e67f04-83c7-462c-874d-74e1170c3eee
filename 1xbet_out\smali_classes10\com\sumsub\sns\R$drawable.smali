.class public final Lcom/sumsub/sns/R$drawable;
.super Ljava/lang/Object;


# static fields
.field public static abc_ab_share_pack_mtrl_alpha:I = 0x7f08014f

.field public static abc_action_bar_item_background_material:I = 0x7f080150

.field public static abc_btn_borderless_material:I = 0x7f080151

.field public static abc_btn_check_material:I = 0x7f080152

.field public static abc_btn_check_material_anim:I = 0x7f080153

.field public static abc_btn_check_to_on_mtrl_000:I = 0x7f080154

.field public static abc_btn_check_to_on_mtrl_015:I = 0x7f080155

.field public static abc_btn_colored_material:I = 0x7f080156

.field public static abc_btn_default_mtrl_shape:I = 0x7f080157

.field public static abc_btn_radio_material:I = 0x7f080158

.field public static abc_btn_radio_material_anim:I = 0x7f080159

.field public static abc_btn_radio_to_on_mtrl_000:I = 0x7f08015a

.field public static abc_btn_radio_to_on_mtrl_015:I = 0x7f08015b

.field public static abc_btn_switch_to_on_mtrl_00001:I = 0x7f08015c

.field public static abc_btn_switch_to_on_mtrl_00012:I = 0x7f08015d

.field public static abc_cab_background_internal_bg:I = 0x7f08015e

.field public static abc_cab_background_top_material:I = 0x7f08015f

.field public static abc_cab_background_top_mtrl_alpha:I = 0x7f080160

.field public static abc_control_background_material:I = 0x7f080161

.field public static abc_dialog_material_background:I = 0x7f080162

.field public static abc_edit_text_material:I = 0x7f080163

.field public static abc_ic_ab_back_material:I = 0x7f080164

.field public static abc_ic_arrow_drop_right_black_24dp:I = 0x7f080165

.field public static abc_ic_clear_material:I = 0x7f080166

.field public static abc_ic_commit_search_api_mtrl_alpha:I = 0x7f080167

.field public static abc_ic_go_search_api_material:I = 0x7f080168

.field public static abc_ic_menu_copy_mtrl_am_alpha:I = 0x7f080169

.field public static abc_ic_menu_cut_mtrl_alpha:I = 0x7f08016a

.field public static abc_ic_menu_overflow_material:I = 0x7f08016b

.field public static abc_ic_menu_paste_mtrl_am_alpha:I = 0x7f08016c

.field public static abc_ic_menu_selectall_mtrl_alpha:I = 0x7f08016d

.field public static abc_ic_menu_share_mtrl_alpha:I = 0x7f08016e

.field public static abc_ic_search_api_material:I = 0x7f08016f

.field public static abc_ic_voice_search_api_material:I = 0x7f080170

.field public static abc_item_background_holo_dark:I = 0x7f080171

.field public static abc_item_background_holo_light:I = 0x7f080172

.field public static abc_list_divider_material:I = 0x7f080173

.field public static abc_list_divider_mtrl_alpha:I = 0x7f080174

.field public static abc_list_focused_holo:I = 0x7f080175

.field public static abc_list_longpressed_holo:I = 0x7f080176

.field public static abc_list_pressed_holo_dark:I = 0x7f080177

.field public static abc_list_pressed_holo_light:I = 0x7f080178

.field public static abc_list_selector_background_transition_holo_dark:I = 0x7f080179

.field public static abc_list_selector_background_transition_holo_light:I = 0x7f08017a

.field public static abc_list_selector_disabled_holo_dark:I = 0x7f08017b

.field public static abc_list_selector_disabled_holo_light:I = 0x7f08017c

.field public static abc_list_selector_holo_dark:I = 0x7f08017d

.field public static abc_list_selector_holo_light:I = 0x7f08017e

.field public static abc_menu_hardkey_panel_mtrl_mult:I = 0x7f08017f

.field public static abc_popup_background_mtrl_mult:I = 0x7f080180

.field public static abc_ratingbar_indicator_material:I = 0x7f080181

.field public static abc_ratingbar_material:I = 0x7f080182

.field public static abc_ratingbar_small_material:I = 0x7f080183

.field public static abc_scrubber_control_off_mtrl_alpha:I = 0x7f080184

.field public static abc_scrubber_control_to_pressed_mtrl_000:I = 0x7f080185

.field public static abc_scrubber_control_to_pressed_mtrl_005:I = 0x7f080186

.field public static abc_scrubber_primary_mtrl_alpha:I = 0x7f080187

.field public static abc_scrubber_track_mtrl_alpha:I = 0x7f080188

.field public static abc_seekbar_thumb_material:I = 0x7f080189

.field public static abc_seekbar_tick_mark_material:I = 0x7f08018a

.field public static abc_seekbar_track_material:I = 0x7f08018b

.field public static abc_spinner_mtrl_am_alpha:I = 0x7f08018c

.field public static abc_spinner_textfield_background_material:I = 0x7f08018d

.field public static abc_star_black_48dp:I = 0x7f08018e

.field public static abc_star_half_black_48dp:I = 0x7f08018f

.field public static abc_switch_thumb_material:I = 0x7f080190

.field public static abc_switch_track_mtrl_alpha:I = 0x7f080191

.field public static abc_tab_indicator_material:I = 0x7f080192

.field public static abc_tab_indicator_mtrl_alpha:I = 0x7f080193

.field public static abc_text_cursor_material:I = 0x7f080194

.field public static abc_text_select_handle_left_mtrl:I = 0x7f080195

.field public static abc_text_select_handle_middle_mtrl:I = 0x7f080196

.field public static abc_text_select_handle_right_mtrl:I = 0x7f080197

.field public static abc_textfield_activated_mtrl_alpha:I = 0x7f080198

.field public static abc_textfield_default_mtrl_alpha:I = 0x7f080199

.field public static abc_textfield_search_activated_mtrl_alpha:I = 0x7f08019a

.field public static abc_textfield_search_default_mtrl_alpha:I = 0x7f08019b

.field public static abc_textfield_search_material:I = 0x7f08019c

.field public static abc_vector_test:I = 0x7f08019d

.field public static avd_hide_password:I = 0x7f0801e7

.field public static avd_show_password:I = 0x7f0801e8

.field public static btn_checkbox_checked_mtrl:I = 0x7f08030b

.field public static btn_checkbox_checked_to_unchecked_mtrl_animation:I = 0x7f08030c

.field public static btn_checkbox_unchecked_mtrl:I = 0x7f08030d

.field public static btn_checkbox_unchecked_to_checked_mtrl_animation:I = 0x7f08030e

.field public static btn_radio_off_mtrl:I = 0x7f080311

.field public static btn_radio_off_to_on_mtrl_animation:I = 0x7f080312

.field public static btn_radio_on_mtrl:I = 0x7f080313

.field public static btn_radio_on_to_off_mtrl_animation:I = 0x7f080314

.field public static circle_shape:I = 0x7f080356

.field public static circular_progress_bar_countdown:I = 0x7f080359

.field public static circular_progress_bar_recording:I = 0x7f08035a

.field public static common_full_open_on_phone:I = 0x7f080361

.field public static common_google_signin_btn_icon_dark:I = 0x7f080362

.field public static common_google_signin_btn_icon_dark_focused:I = 0x7f080363

.field public static common_google_signin_btn_icon_dark_normal:I = 0x7f080364

.field public static common_google_signin_btn_icon_dark_normal_background:I = 0x7f080365

.field public static common_google_signin_btn_icon_disabled:I = 0x7f080366

.field public static common_google_signin_btn_icon_light:I = 0x7f080367

.field public static common_google_signin_btn_icon_light_focused:I = 0x7f080368

.field public static common_google_signin_btn_icon_light_normal:I = 0x7f080369

.field public static common_google_signin_btn_icon_light_normal_background:I = 0x7f08036a

.field public static common_google_signin_btn_text_dark:I = 0x7f08036b

.field public static common_google_signin_btn_text_dark_focused:I = 0x7f08036c

.field public static common_google_signin_btn_text_dark_normal:I = 0x7f08036d

.field public static common_google_signin_btn_text_dark_normal_background:I = 0x7f08036e

.field public static common_google_signin_btn_text_disabled:I = 0x7f08036f

.field public static common_google_signin_btn_text_light:I = 0x7f080370

.field public static common_google_signin_btn_text_light_focused:I = 0x7f080371

.field public static common_google_signin_btn_text_light_normal:I = 0x7f080372

.field public static common_google_signin_btn_text_light_normal_background:I = 0x7f080373

.field public static design_fab_background:I = 0x7f0804af

.field public static design_ic_visibility:I = 0x7f0804b0

.field public static design_ic_visibility_off:I = 0x7f0804b1

.field public static design_password_eye:I = 0x7f0804b2

.field public static design_snackbar_background:I = 0x7f0804b3

.field public static googleg_disabled_color_18:I = 0x7f080697

.field public static googleg_standard_color_18:I = 0x7f080698

.field public static ic_arrow_back_black_24:I = 0x7f080711

.field public static ic_call_answer:I = 0x7f08078e

.field public static ic_call_answer_low:I = 0x7f08078f

.field public static ic_call_answer_video:I = 0x7f080790

.field public static ic_call_answer_video_low:I = 0x7f080791

.field public static ic_call_decline:I = 0x7f080793

.field public static ic_call_decline_low:I = 0x7f080794

.field public static ic_clear_black_24:I = 0x7f0807f0

.field public static ic_clock_black_24dp:I = 0x7f0807f4

.field public static ic_done:I = 0x7f08085b

.field public static ic_keyboard_black_24dp:I = 0x7f080b19

.field public static ic_m3_chip_check:I = 0x7f080b37

.field public static ic_m3_chip_checked_circle:I = 0x7f080b38

.field public static ic_m3_chip_close:I = 0x7f080b39

.field public static ic_mtrl_checked_circle:I = 0x7f080b4a

.field public static ic_mtrl_chip_checked_black:I = 0x7f080b4b

.field public static ic_mtrl_chip_checked_circle:I = 0x7f080b4c

.field public static ic_mtrl_chip_close_circle:I = 0x7f080b4d

.field public static ic_search_black_24:I = 0x7f080c3b

.field public static m3_avd_hide_password:I = 0x7f080e0b

.field public static m3_avd_show_password:I = 0x7f080e0c

.field public static m3_bottom_sheet_drag_handle:I = 0x7f080e0d

.field public static m3_password_eye:I = 0x7f080e0e

.field public static m3_popupmenu_background_overlay:I = 0x7f080e0f

.field public static m3_radiobutton_ripple:I = 0x7f080e10

.field public static m3_selection_control_ripple:I = 0x7f080e11

.field public static m3_tabs_background:I = 0x7f080e12

.field public static m3_tabs_line_indicator:I = 0x7f080e13

.field public static m3_tabs_rounded_line_indicator:I = 0x7f080e14

.field public static m3_tabs_transparent_background:I = 0x7f080e15

.field public static material_cursor_drawable:I = 0x7f080e24

.field public static material_ic_calendar_black_24dp:I = 0x7f080e25

.field public static material_ic_clear_black_24dp:I = 0x7f080e26

.field public static material_ic_edit_black_24dp:I = 0x7f080e27

.field public static material_ic_keyboard_arrow_left_black_24dp:I = 0x7f080e28

.field public static material_ic_keyboard_arrow_next_black_24dp:I = 0x7f080e29

.field public static material_ic_keyboard_arrow_previous_black_24dp:I = 0x7f080e2a

.field public static material_ic_keyboard_arrow_right_black_24dp:I = 0x7f080e2b

.field public static material_ic_menu_arrow_down_black_24dp:I = 0x7f080e2c

.field public static material_ic_menu_arrow_up_black_24dp:I = 0x7f080e2d

.field public static mtrl_bottomsheet_drag_handle:I = 0x7f080e58

.field public static mtrl_checkbox_button:I = 0x7f080e59

.field public static mtrl_checkbox_button_checked_unchecked:I = 0x7f080e5a

.field public static mtrl_checkbox_button_icon:I = 0x7f080e5b

.field public static mtrl_checkbox_button_icon_checked_indeterminate:I = 0x7f080e5c

.field public static mtrl_checkbox_button_icon_checked_unchecked:I = 0x7f080e5d

.field public static mtrl_checkbox_button_icon_indeterminate_checked:I = 0x7f080e5e

.field public static mtrl_checkbox_button_icon_indeterminate_unchecked:I = 0x7f080e5f

.field public static mtrl_checkbox_button_icon_unchecked_checked:I = 0x7f080e60

.field public static mtrl_checkbox_button_icon_unchecked_indeterminate:I = 0x7f080e61

.field public static mtrl_checkbox_button_unchecked_checked:I = 0x7f080e62

.field public static mtrl_dialog_background:I = 0x7f080e63

.field public static mtrl_dropdown_arrow:I = 0x7f080e64

.field public static mtrl_ic_arrow_drop_down:I = 0x7f080e65

.field public static mtrl_ic_arrow_drop_up:I = 0x7f080e66

.field public static mtrl_ic_cancel:I = 0x7f080e67

.field public static mtrl_ic_check_mark:I = 0x7f080e68

.field public static mtrl_ic_checkbox_checked:I = 0x7f080e69

.field public static mtrl_ic_checkbox_unchecked:I = 0x7f080e6a

.field public static mtrl_ic_error:I = 0x7f080e6b

.field public static mtrl_ic_indeterminate:I = 0x7f080e6c

.field public static mtrl_navigation_bar_item_background:I = 0x7f080e6d

.field public static mtrl_popupmenu_background:I = 0x7f080e6e

.field public static mtrl_popupmenu_background_overlay:I = 0x7f080e6f

.field public static mtrl_switch_thumb:I = 0x7f080e70

.field public static mtrl_switch_thumb_checked:I = 0x7f080e71

.field public static mtrl_switch_thumb_checked_pressed:I = 0x7f080e72

.field public static mtrl_switch_thumb_checked_unchecked:I = 0x7f080e73

.field public static mtrl_switch_thumb_pressed:I = 0x7f080e74

.field public static mtrl_switch_thumb_pressed_checked:I = 0x7f080e75

.field public static mtrl_switch_thumb_pressed_unchecked:I = 0x7f080e76

.field public static mtrl_switch_thumb_unchecked:I = 0x7f080e77

.field public static mtrl_switch_thumb_unchecked_checked:I = 0x7f080e78

.field public static mtrl_switch_thumb_unchecked_pressed:I = 0x7f080e79

.field public static mtrl_switch_track:I = 0x7f080e7a

.field public static mtrl_switch_track_decoration:I = 0x7f080e7b

.field public static mtrl_tabs_default_indicator:I = 0x7f080e7c

.field public static navigation_empty_icon:I = 0x7f080e7f

.field public static notification_action_background:I = 0x7f080e89

.field public static notification_bg:I = 0x7f080e8a

.field public static notification_bg_low:I = 0x7f080e8b

.field public static notification_bg_low_normal:I = 0x7f080e8c

.field public static notification_bg_low_pressed:I = 0x7f080e8d

.field public static notification_bg_normal:I = 0x7f080e8e

.field public static notification_bg_normal_pressed:I = 0x7f080e8f

.field public static notification_icon_background:I = 0x7f080e90

.field public static notification_oversize_large_icon_bg:I = 0x7f080e91

.field public static notification_template_icon_bg:I = 0x7f080e92

.field public static notification_template_icon_low_bg:I = 0x7f080e93

.field public static notification_tile_bg:I = 0x7f080e94

.field public static notify_panel_notification_icon_bg:I = 0x7f080e95

.field public static sns_bg_bottom_sheet_dialog_fragment:I = 0x7f080ffa

.field public static sns_bg_circle:I = 0x7f080ffb

.field public static sns_bg_file_item:I = 0x7f080ffc

.field public static sns_bg_iddoc_warning:I = 0x7f080ffd

.field public static sns_bg_image_button:I = 0x7f080ffe

.field public static sns_bg_rounded_divider:I = 0x7f080fff

.field public static sns_bg_selfie_rounded_rectangle:I = 0x7f081000

.field public static sns_bg_selfie_stop_rectangle:I = 0x7f081001

.field public static sns_bg_white_transparent_gradient:I = 0x7f081002

.field public static sns_divider_small:I = 0x7f081003

.field public static sns_file_items_separator:I = 0x7f081004

.field public static sns_flip:I = 0x7f081005

.field public static sns_ic_attachment:I = 0x7f081006

.field public static sns_ic_back:I = 0x7f081007

.field public static sns_ic_calendar:I = 0x7f081008

.field public static sns_ic_camera:I = 0x7f081009

.field public static sns_ic_capture:I = 0x7f08100a

.field public static sns_ic_close:I = 0x7f08100b

.field public static sns_ic_close_white:I = 0x7f08100c

.field public static sns_ic_delete:I = 0x7f08100d

.field public static sns_ic_earth:I = 0x7f08100e

.field public static sns_ic_eid_can:I = 0x7f08100f

.field public static sns_ic_email:I = 0x7f081010

.field public static sns_ic_error:I = 0x7f081011

.field public static sns_ic_error_refresh:I = 0x7f081012

.field public static sns_ic_error_robot:I = 0x7f081013

.field public static sns_ic_fatal:I = 0x7f081014

.field public static sns_ic_fatal_error:I = 0x7f081015

.field public static sns_ic_fatal_x:I = 0x7f081016

.field public static sns_ic_flag_ad:I = 0x7f081017

.field public static sns_ic_flag_ae:I = 0x7f081018

.field public static sns_ic_flag_af:I = 0x7f081019

.field public static sns_ic_flag_ag:I = 0x7f08101a

.field public static sns_ic_flag_ai:I = 0x7f08101b

.field public static sns_ic_flag_al:I = 0x7f08101c

.field public static sns_ic_flag_am:I = 0x7f08101d

.field public static sns_ic_flag_an:I = 0x7f08101e

.field public static sns_ic_flag_ao:I = 0x7f08101f

.field public static sns_ic_flag_aq:I = 0x7f081020

.field public static sns_ic_flag_ar:I = 0x7f081021

.field public static sns_ic_flag_as:I = 0x7f081022

.field public static sns_ic_flag_at:I = 0x7f081023

.field public static sns_ic_flag_au:I = 0x7f081024

.field public static sns_ic_flag_aw:I = 0x7f081025

.field public static sns_ic_flag_ax:I = 0x7f081026

.field public static sns_ic_flag_az:I = 0x7f081027

.field public static sns_ic_flag_ba:I = 0x7f081028

.field public static sns_ic_flag_bb:I = 0x7f081029

.field public static sns_ic_flag_bd:I = 0x7f08102a

.field public static sns_ic_flag_be:I = 0x7f08102b

.field public static sns_ic_flag_bf:I = 0x7f08102c

.field public static sns_ic_flag_bg:I = 0x7f08102d

.field public static sns_ic_flag_bh:I = 0x7f08102e

.field public static sns_ic_flag_bi:I = 0x7f08102f

.field public static sns_ic_flag_bj:I = 0x7f081030

.field public static sns_ic_flag_bl:I = 0x7f081031

.field public static sns_ic_flag_bm:I = 0x7f081032

.field public static sns_ic_flag_bn:I = 0x7f081033

.field public static sns_ic_flag_bo:I = 0x7f081034

.field public static sns_ic_flag_bq:I = 0x7f081035

.field public static sns_ic_flag_bq_bo:I = 0x7f081036

.field public static sns_ic_flag_bq_sa:I = 0x7f081037

.field public static sns_ic_flag_bq_se:I = 0x7f081038

.field public static sns_ic_flag_br:I = 0x7f081039

.field public static sns_ic_flag_bs:I = 0x7f08103a

.field public static sns_ic_flag_bt:I = 0x7f08103b

.field public static sns_ic_flag_bv:I = 0x7f08103c

.field public static sns_ic_flag_bw:I = 0x7f08103d

.field public static sns_ic_flag_by:I = 0x7f08103e

.field public static sns_ic_flag_bz:I = 0x7f08103f

.field public static sns_ic_flag_ca:I = 0x7f081040

.field public static sns_ic_flag_cc:I = 0x7f081041

.field public static sns_ic_flag_cd:I = 0x7f081042

.field public static sns_ic_flag_cf:I = 0x7f081043

.field public static sns_ic_flag_cg:I = 0x7f081044

.field public static sns_ic_flag_ch:I = 0x7f081045

.field public static sns_ic_flag_ci:I = 0x7f081046

.field public static sns_ic_flag_ck:I = 0x7f081047

.field public static sns_ic_flag_cl:I = 0x7f081048

.field public static sns_ic_flag_cm:I = 0x7f081049

.field public static sns_ic_flag_cn:I = 0x7f08104a

.field public static sns_ic_flag_co:I = 0x7f08104b

.field public static sns_ic_flag_cr:I = 0x7f08104c

.field public static sns_ic_flag_cu:I = 0x7f08104d

.field public static sns_ic_flag_cv:I = 0x7f08104e

.field public static sns_ic_flag_cw:I = 0x7f08104f

.field public static sns_ic_flag_cx:I = 0x7f081050

.field public static sns_ic_flag_cy:I = 0x7f081051

.field public static sns_ic_flag_cz:I = 0x7f081052

.field public static sns_ic_flag_de:I = 0x7f081053

.field public static sns_ic_flag_dj:I = 0x7f081054

.field public static sns_ic_flag_dk:I = 0x7f081055

.field public static sns_ic_flag_dm:I = 0x7f081056

.field public static sns_ic_flag_do:I = 0x7f081057

.field public static sns_ic_flag_dz:I = 0x7f081058

.field public static sns_ic_flag_ec:I = 0x7f081059

.field public static sns_ic_flag_ee:I = 0x7f08105a

.field public static sns_ic_flag_eg:I = 0x7f08105b

.field public static sns_ic_flag_eh:I = 0x7f08105c

.field public static sns_ic_flag_er:I = 0x7f08105d

.field public static sns_ic_flag_es:I = 0x7f08105e

.field public static sns_ic_flag_et:I = 0x7f08105f

.field public static sns_ic_flag_eu:I = 0x7f081060

.field public static sns_ic_flag_fi:I = 0x7f081061

.field public static sns_ic_flag_fj:I = 0x7f081062

.field public static sns_ic_flag_fk:I = 0x7f081063

.field public static sns_ic_flag_fm:I = 0x7f081064

.field public static sns_ic_flag_fo:I = 0x7f081065

.field public static sns_ic_flag_fr:I = 0x7f081066

.field public static sns_ic_flag_ga:I = 0x7f081067

.field public static sns_ic_flag_gb:I = 0x7f081068

.field public static sns_ic_flag_gd:I = 0x7f081069

.field public static sns_ic_flag_ge:I = 0x7f08106a

.field public static sns_ic_flag_gf:I = 0x7f08106b

.field public static sns_ic_flag_gg:I = 0x7f08106c

.field public static sns_ic_flag_gh:I = 0x7f08106d

.field public static sns_ic_flag_gi:I = 0x7f08106e

.field public static sns_ic_flag_gl:I = 0x7f08106f

.field public static sns_ic_flag_gm:I = 0x7f081070

.field public static sns_ic_flag_gn:I = 0x7f081071

.field public static sns_ic_flag_gp:I = 0x7f081072

.field public static sns_ic_flag_gq:I = 0x7f081073

.field public static sns_ic_flag_gr:I = 0x7f081074

.field public static sns_ic_flag_gs:I = 0x7f081075

.field public static sns_ic_flag_gt:I = 0x7f081076

.field public static sns_ic_flag_gu:I = 0x7f081077

.field public static sns_ic_flag_gw:I = 0x7f081078

.field public static sns_ic_flag_gy:I = 0x7f081079

.field public static sns_ic_flag_hk:I = 0x7f08107a

.field public static sns_ic_flag_hm:I = 0x7f08107b

.field public static sns_ic_flag_hn:I = 0x7f08107c

.field public static sns_ic_flag_hr:I = 0x7f08107d

.field public static sns_ic_flag_ht:I = 0x7f08107e

.field public static sns_ic_flag_hu:I = 0x7f08107f

.field public static sns_ic_flag_id:I = 0x7f081080

.field public static sns_ic_flag_ie:I = 0x7f081081

.field public static sns_ic_flag_il:I = 0x7f081082

.field public static sns_ic_flag_im:I = 0x7f081083

.field public static sns_ic_flag_in:I = 0x7f081084

.field public static sns_ic_flag_io:I = 0x7f081085

.field public static sns_ic_flag_iq:I = 0x7f081086

.field public static sns_ic_flag_ir:I = 0x7f081087

.field public static sns_ic_flag_is:I = 0x7f081088

.field public static sns_ic_flag_it:I = 0x7f081089

.field public static sns_ic_flag_je:I = 0x7f08108a

.field public static sns_ic_flag_jm:I = 0x7f08108b

.field public static sns_ic_flag_jo:I = 0x7f08108c

.field public static sns_ic_flag_jp:I = 0x7f08108d

.field public static sns_ic_flag_ke:I = 0x7f08108e

.field public static sns_ic_flag_kg:I = 0x7f08108f

.field public static sns_ic_flag_kh:I = 0x7f081090

.field public static sns_ic_flag_ki:I = 0x7f081091

.field public static sns_ic_flag_km:I = 0x7f081092

.field public static sns_ic_flag_kn:I = 0x7f081093

.field public static sns_ic_flag_kp:I = 0x7f081094

.field public static sns_ic_flag_kr:I = 0x7f081095

.field public static sns_ic_flag_kw:I = 0x7f081096

.field public static sns_ic_flag_ky:I = 0x7f081097

.field public static sns_ic_flag_kz:I = 0x7f081098

.field public static sns_ic_flag_la:I = 0x7f081099

.field public static sns_ic_flag_lb:I = 0x7f08109a

.field public static sns_ic_flag_lc:I = 0x7f08109b

.field public static sns_ic_flag_li:I = 0x7f08109c

.field public static sns_ic_flag_lk:I = 0x7f08109d

.field public static sns_ic_flag_lr:I = 0x7f08109e

.field public static sns_ic_flag_ls:I = 0x7f08109f

.field public static sns_ic_flag_lt:I = 0x7f0810a0

.field public static sns_ic_flag_lu:I = 0x7f0810a1

.field public static sns_ic_flag_lv:I = 0x7f0810a2

.field public static sns_ic_flag_ly:I = 0x7f0810a3

.field public static sns_ic_flag_ma:I = 0x7f0810a4

.field public static sns_ic_flag_mc:I = 0x7f0810a5

.field public static sns_ic_flag_md:I = 0x7f0810a6

.field public static sns_ic_flag_me:I = 0x7f0810a7

.field public static sns_ic_flag_mf:I = 0x7f0810a8

.field public static sns_ic_flag_mg:I = 0x7f0810a9

.field public static sns_ic_flag_mh:I = 0x7f0810aa

.field public static sns_ic_flag_mk:I = 0x7f0810ab

.field public static sns_ic_flag_ml:I = 0x7f0810ac

.field public static sns_ic_flag_mm:I = 0x7f0810ad

.field public static sns_ic_flag_mn:I = 0x7f0810ae

.field public static sns_ic_flag_mo:I = 0x7f0810af

.field public static sns_ic_flag_mp:I = 0x7f0810b0

.field public static sns_ic_flag_mq:I = 0x7f0810b1

.field public static sns_ic_flag_mqa:I = 0x7f0810b2

.field public static sns_ic_flag_mqb:I = 0x7f0810b3

.field public static sns_ic_flag_mr:I = 0x7f0810b4

.field public static sns_ic_flag_ms:I = 0x7f0810b5

.field public static sns_ic_flag_mt:I = 0x7f0810b6

.field public static sns_ic_flag_mu:I = 0x7f0810b7

.field public static sns_ic_flag_mv:I = 0x7f0810b8

.field public static sns_ic_flag_mw:I = 0x7f0810b9

.field public static sns_ic_flag_mx:I = 0x7f0810ba

.field public static sns_ic_flag_my:I = 0x7f0810bb

.field public static sns_ic_flag_mz:I = 0x7f0810bc

.field public static sns_ic_flag_na:I = 0x7f0810bd

.field public static sns_ic_flag_nc:I = 0x7f0810be

.field public static sns_ic_flag_ne:I = 0x7f0810bf

.field public static sns_ic_flag_nf:I = 0x7f0810c0

.field public static sns_ic_flag_ng:I = 0x7f0810c1

.field public static sns_ic_flag_ni:I = 0x7f0810c2

.field public static sns_ic_flag_nl:I = 0x7f0810c3

.field public static sns_ic_flag_no:I = 0x7f0810c4

.field public static sns_ic_flag_np:I = 0x7f0810c5

.field public static sns_ic_flag_nr:I = 0x7f0810c6

.field public static sns_ic_flag_nu:I = 0x7f0810c7

.field public static sns_ic_flag_nz:I = 0x7f0810c8

.field public static sns_ic_flag_om:I = 0x7f0810c9

.field public static sns_ic_flag_pa:I = 0x7f0810ca

.field public static sns_ic_flag_pe:I = 0x7f0810cb

.field public static sns_ic_flag_pf:I = 0x7f0810cc

.field public static sns_ic_flag_pg:I = 0x7f0810cd

.field public static sns_ic_flag_ph:I = 0x7f0810ce

.field public static sns_ic_flag_pk:I = 0x7f0810cf

.field public static sns_ic_flag_pl:I = 0x7f0810d0

.field public static sns_ic_flag_placeholder:I = 0x7f0810d1

.field public static sns_ic_flag_pm:I = 0x7f0810d2

.field public static sns_ic_flag_pn:I = 0x7f0810d3

.field public static sns_ic_flag_pr:I = 0x7f0810d4

.field public static sns_ic_flag_ps:I = 0x7f0810d5

.field public static sns_ic_flag_pt:I = 0x7f0810d6

.field public static sns_ic_flag_pw:I = 0x7f0810d7

.field public static sns_ic_flag_py:I = 0x7f0810d8

.field public static sns_ic_flag_qa:I = 0x7f0810d9

.field public static sns_ic_flag_re:I = 0x7f0810da

.field public static sns_ic_flag_ro:I = 0x7f0810db

.field public static sns_ic_flag_rs:I = 0x7f0810dc

.field public static sns_ic_flag_ru:I = 0x7f0810dd

.field public static sns_ic_flag_rw:I = 0x7f0810de

.field public static sns_ic_flag_sa:I = 0x7f0810df

.field public static sns_ic_flag_sb:I = 0x7f0810e0

.field public static sns_ic_flag_sc:I = 0x7f0810e1

.field public static sns_ic_flag_sd:I = 0x7f0810e2

.field public static sns_ic_flag_se:I = 0x7f0810e3

.field public static sns_ic_flag_sg:I = 0x7f0810e4

.field public static sns_ic_flag_sh:I = 0x7f0810e5

.field public static sns_ic_flag_si:I = 0x7f0810e6

.field public static sns_ic_flag_sj:I = 0x7f0810e7

.field public static sns_ic_flag_sk:I = 0x7f0810e8

.field public static sns_ic_flag_sl:I = 0x7f0810e9

.field public static sns_ic_flag_sm:I = 0x7f0810ea

.field public static sns_ic_flag_sn:I = 0x7f0810eb

.field public static sns_ic_flag_so:I = 0x7f0810ec

.field public static sns_ic_flag_sr:I = 0x7f0810ed

.field public static sns_ic_flag_ss:I = 0x7f0810ee

.field public static sns_ic_flag_st:I = 0x7f0810ef

.field public static sns_ic_flag_sv:I = 0x7f0810f0

.field public static sns_ic_flag_sx:I = 0x7f0810f1

.field public static sns_ic_flag_sy:I = 0x7f0810f2

.field public static sns_ic_flag_sz:I = 0x7f0810f3

.field public static sns_ic_flag_tc:I = 0x7f0810f4

.field public static sns_ic_flag_td:I = 0x7f0810f5

.field public static sns_ic_flag_tf:I = 0x7f0810f6

.field public static sns_ic_flag_tg:I = 0x7f0810f7

.field public static sns_ic_flag_th:I = 0x7f0810f8

.field public static sns_ic_flag_tj:I = 0x7f0810f9

.field public static sns_ic_flag_tk:I = 0x7f0810fa

.field public static sns_ic_flag_tl:I = 0x7f0810fb

.field public static sns_ic_flag_tm:I = 0x7f0810fc

.field public static sns_ic_flag_tn:I = 0x7f0810fd

.field public static sns_ic_flag_to:I = 0x7f0810fe

.field public static sns_ic_flag_tr:I = 0x7f0810ff

.field public static sns_ic_flag_tt:I = 0x7f081100

.field public static sns_ic_flag_tv:I = 0x7f081101

.field public static sns_ic_flag_tw:I = 0x7f081102

.field public static sns_ic_flag_tz:I = 0x7f081103

.field public static sns_ic_flag_ua:I = 0x7f081104

.field public static sns_ic_flag_ug:I = 0x7f081105

.field public static sns_ic_flag_um:I = 0x7f081106

.field public static sns_ic_flag_us:I = 0x7f081107

.field public static sns_ic_flag_uy:I = 0x7f081108

.field public static sns_ic_flag_uz:I = 0x7f081109

.field public static sns_ic_flag_va:I = 0x7f08110a

.field public static sns_ic_flag_vc:I = 0x7f08110b

.field public static sns_ic_flag_ve:I = 0x7f08110c

.field public static sns_ic_flag_vg:I = 0x7f08110d

.field public static sns_ic_flag_vi:I = 0x7f08110e

.field public static sns_ic_flag_vn:I = 0x7f08110f

.field public static sns_ic_flag_vu:I = 0x7f081110

.field public static sns_ic_flag_wf:I = 0x7f081111

.field public static sns_ic_flag_ws:I = 0x7f081112

.field public static sns_ic_flag_xk:I = 0x7f081113

.field public static sns_ic_flag_ye:I = 0x7f081114

.field public static sns_ic_flag_yt:I = 0x7f081115

.field public static sns_ic_flag_za:I = 0x7f081116

.field public static sns_ic_flag_zm:I = 0x7f081117

.field public static sns_ic_flag_zw:I = 0x7f081118

.field public static sns_ic_flash_off:I = 0x7f081119

.field public static sns_ic_flash_on:I = 0x7f08111a

.field public static sns_ic_gallery:I = 0x7f08111b

.field public static sns_ic_id_hand:I = 0x7f08111c

.field public static sns_ic_iddoc_driving_license:I = 0x7f08111d

.field public static sns_ic_iddoc_error:I = 0x7f08111e

.field public static sns_ic_iddoc_id_card:I = 0x7f08111f

.field public static sns_ic_iddoc_passport:I = 0x7f081120

.field public static sns_ic_iddoc_proof_of_address:I = 0x7f081121

.field public static sns_ic_iddoc_residence_permit:I = 0x7f081122

.field public static sns_ic_iddoc_warning:I = 0x7f081123

.field public static sns_ic_image:I = 0x7f081124

.field public static sns_ic_intro_do:I = 0x7f081125

.field public static sns_ic_intro_do_back:I = 0x7f081126

.field public static sns_ic_intro_do_passport:I = 0x7f081127

.field public static sns_ic_intro_dont:I = 0x7f081128

.field public static sns_ic_intro_dont_back:I = 0x7f081129

.field public static sns_ic_intro_dont_passport:I = 0x7f08112a

.field public static sns_ic_intro_liveness:I = 0x7f08112b

.field public static sns_ic_launch:I = 0x7f08112c

.field public static sns_ic_light:I = 0x7f08112d

.field public static sns_ic_location_off:I = 0x7f08112e

.field public static sns_ic_location_on:I = 0x7f08112f

.field public static sns_ic_mrtd_hand:I = 0x7f081130

.field public static sns_ic_mrtd_id_card:I = 0x7f081131

.field public static sns_ic_mrtd_passport:I = 0x7f081132

.field public static sns_ic_nfc_id:I = 0x7f081133

.field public static sns_ic_nfc_logo:I = 0x7f081134

.field public static sns_ic_notify:I = 0x7f081135

.field public static sns_ic_persons:I = 0x7f081136

.field public static sns_ic_photo_frame:I = 0x7f081137

.field public static sns_ic_pin:I = 0x7f081138

.field public static sns_ic_recording:I = 0x7f081139

.field public static sns_ic_rejected:I = 0x7f08113a

.field public static sns_ic_reverse_v:I = 0x7f08113b

.field public static sns_ic_reverse_with_background:I = 0x7f08113c

.field public static sns_ic_rotate_ccw:I = 0x7f08113d

.field public static sns_ic_rotate_cw:I = 0x7f08113e

.field public static sns_ic_search:I = 0x7f08113f

.field public static sns_ic_sign:I = 0x7f081140

.field public static sns_ic_step_applicant_data:I = 0x7f081141

.field public static sns_ic_step_ekyc:I = 0x7f081142

.field public static sns_ic_step_email:I = 0x7f081143

.field public static sns_ic_step_identity:I = 0x7f081144

.field public static sns_ic_step_open:I = 0x7f081145

.field public static sns_ic_step_phone:I = 0x7f081146

.field public static sns_ic_step_poa:I = 0x7f081147

.field public static sns_ic_step_questionnaire:I = 0x7f081148

.field public static sns_ic_step_selfie:I = 0x7f081149

.field public static sns_ic_step_video_ident:I = 0x7f08114a

.field public static sns_ic_submitted:I = 0x7f08114b

.field public static sns_ic_success:I = 0x7f08114c

.field public static sns_ic_success_check:I = 0x7f08114d

.field public static sns_ic_success_dow_arrow:I = 0x7f08114e

.field public static sns_ic_upload:I = 0x7f08114f

.field public static sns_ic_videoident_intro_face:I = 0x7f081150

.field public static sns_ic_warning:I = 0x7f081151

.field public static sns_ic_warning_outline:I = 0x7f081152

.field public static sns_ic_warning_triangle:I = 0x7f081153

.field public static sns_ic_wifi:I = 0x7f081154

.field public static sns_items_divider_medium:I = 0x7f081155

.field public static sns_list_divider:I = 0x7f081156

.field public static sns_pincode_cursor:I = 0x7f081157

.field public static sns_recording_bg:I = 0x7f081158

.field public static sns_round_rect:I = 0x7f081159

.field public static sns_round_rect_background:I = 0x7f08115a

.field public static sns_selfie_border:I = 0x7f08115b

.field public static sns_selfie_frame:I = 0x7f08115c

.field public static sns_selfie_mask:I = 0x7f08115d

.field public static sns_text_handle:I = 0x7f08115e

.field public static test_level_drawable:I = 0x7f0811b2

.field public static tooltip_frame_dark:I = 0x7f0811ca

.field public static tooltip_frame_light:I = 0x7f0811cb


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
