.class public final LsS0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LsS0/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 \u00172\u00020\u0001:\u0001\u0010B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001b\u0010\u000c\u001a\u00020\u000b2\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001b\u0010\u0010\u001a\u00020\u000b2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0008\u00a2\u0006\u0004\u0008\u0010\u0010\rJ\u0013\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0013\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0008\u00a2\u0006\u0004\u0008\u0013\u0010\u0012J\u001b\u0010\u0014\u001a\u00020\u000b2\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\u0014\u0010\rJ\u001b\u0010\u0015\u001a\u00020\u000b2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0008\u00a2\u0006\u0004\u0008\u0015\u0010\rJ\u0013\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u0008\u00a2\u0006\u0004\u0008\u0016\u0010\u0012J\u0013\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\u0017\u0010\u0012J\u0015\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\r\u0010\u001e\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001b\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020!0\u00082\u0006\u0010 \u001a\u00020\u0018\u00a2\u0006\u0004\u0008\"\u0010#J\u0015\u0010&\u001a\u00020\u000b2\u0006\u0010%\u001a\u00020$\u00a2\u0006\u0004\u0008&\u0010\'J\r\u0010(\u001a\u00020$\u00a2\u0006\u0004\u0008(\u0010)J\u0015\u0010+\u001a\u00020\u000b2\u0006\u0010*\u001a\u00020\u0018\u00a2\u0006\u0004\u0008+\u0010\u001bJ\u0013\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u00180,\u00a2\u0006\u0004\u0008-\u0010.J\u0015\u00101\u001a\u00020\u000b2\u0006\u00100\u001a\u00020/\u00a2\u0006\u0004\u00081\u0010\'J\r\u00102\u001a\u00020/\u00a2\u0006\u0004\u00082\u0010)R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u00103R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u00104R \u00107\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u0008058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u00106R \u00108\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\u0008058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u00106R\u0016\u0010:\u001a\u00020$8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008-\u00109R\u001c\u0010;\u001a\u0008\u0012\u0004\u0012\u00020\u0018058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u00106R\u0016\u0010<\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0011\u00109\u00a8\u0006="
    }
    d2 = {
        "LsS0/a;",
        "",
        "LRf0/l;",
        "prefs",
        "Lcom/google/gson/Gson;",
        "gson",
        "<init>",
        "(LRf0/l;Lcom/google/gson/Gson;)V",
        "",
        "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
        "sports",
        "",
        "b",
        "(Ljava/util/List;)V",
        "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
        "champs",
        "a",
        "g",
        "()Ljava/util/List;",
        "c",
        "o",
        "l",
        "d",
        "h",
        "",
        "passed",
        "n",
        "(Z)V",
        "installed",
        "p",
        "j",
        "()Z",
        "ruImages",
        "",
        "i",
        "(Z)Ljava/util/List;",
        "",
        "time",
        "m",
        "(J)V",
        "f",
        "()J",
        "value",
        "q",
        "Lkotlinx/coroutines/flow/e;",
        "e",
        "()Lkotlinx/coroutines/flow/e;",
        "Ll8/b$a$c;",
        "timeStamp",
        "r",
        "k",
        "LRf0/l;",
        "Lcom/google/gson/Gson;",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlinx/coroutines/flow/V;",
        "filterSports",
        "filterChamps",
        "J",
        "lastTimeUpdate",
        "filtersChangedState",
        "cardRequestTimestamp",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:LsS0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LRf0/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/google/gson/Gson;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:J

.field public f:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:J


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LsS0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LsS0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LsS0/a;->h:LsS0/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(LRf0/l;Lcom/google/gson/Gson;)V
    .locals 0
    .param p1    # LRf0/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/google/gson/Gson;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LsS0/a;->a:LRf0/l;

    .line 5
    .line 6
    iput-object p2, p0, LsS0/a;->b:Lcom/google/gson/Gson;

    .line 7
    .line 8
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iput-object p1, p0, LsS0/a;->c:Lkotlinx/coroutines/flow/V;

    .line 17
    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iput-object p1, p0, LsS0/a;->d:Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 29
    .line 30
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, LsS0/a;->f:Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    const-wide/16 p1, 0x0

    .line 37
    .line 38
    invoke-static {p1, p2}, Ll8/b$a$c;->f(J)J

    .line 39
    .line 40
    .line 41
    move-result-wide p1

    .line 42
    iput-wide p1, p0, LsS0/a;->g:J

    .line 43
    .line 44
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->d:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final b(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->c:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->d:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/List;

    .line 8
    .line 9
    return-object v0
.end method

.method public final d()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->b:Lcom/google/gson/Gson;

    .line 2
    .line 3
    iget-object v1, p0, LsS0/a;->a:LRf0/l;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x2

    .line 7
    const-string v4, "KEY_SAVED_SWIPE_X_CHAMPS"

    .line 8
    .line 9
    invoke-static {v1, v4, v2, v3, v2}, LRf0/l;->k(LRf0/l;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    new-instance v2, LsS0/a$b;

    .line 14
    .line 15
    invoke-direct {v2}, LsS0/a$b;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v2}, Lcom/google/gson/reflect/TypeToken;->e()Ljava/lang/reflect/Type;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v0, v1, v2}, Lcom/google/gson/Gson;->o(Ljava/lang/String;Ljava/lang/reflect/Type;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Ljava/util/List;

    .line 27
    .line 28
    if-nez v0, :cond_0

    .line 29
    .line 30
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    :cond_0
    return-object v0
.end method

.method public final e()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->f:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()J
    .locals 2

    .line 1
    iget-wide v0, p0, LsS0/a;->e:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final g()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->c:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/List;

    .line 8
    .line 9
    return-object v0
.end method

.method public final h()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->b:Lcom/google/gson/Gson;

    .line 2
    .line 3
    iget-object v1, p0, LsS0/a;->a:LRf0/l;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x2

    .line 7
    const-string v4, "KEY_SAVED_SWIPE_X_SPORTS"

    .line 8
    .line 9
    invoke-static {v1, v4, v2, v3, v2}, LRf0/l;->k(LRf0/l;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    new-instance v2, LsS0/a$c;

    .line 14
    .line 15
    invoke-direct {v2}, LsS0/a$c;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v2}, Lcom/google/gson/reflect/TypeToken;->e()Ljava/lang/reflect/Type;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v0, v1, v2}, Lcom/google/gson/Gson;->o(Ljava/lang/String;Ljava/lang/reflect/Type;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Ljava/util/List;

    .line 27
    .line 28
    if-nez v0, :cond_0

    .line 29
    .line 30
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    :cond_0
    return-object v0
.end method

.method public final i(Z)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    const-string p1, "/static/img/android/instructions/swipex/ru/2"

    .line 4
    .line 5
    const-string v0, "/static/img/android/instructions/swipex/ru/3"

    .line 6
    .line 7
    const-string v1, "/static/img/android/instructions/swipex/ru/1"

    .line 8
    .line 9
    filled-new-array {v1, p1, v0}, [Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1

    .line 18
    :cond_0
    const-string p1, "/static/img/android/instructions/swipex/en/2"

    .line 19
    .line 20
    const-string v0, "/static/img/android/instructions/swipex/en/3"

    .line 21
    .line 22
    const-string v1, "/static/img/android/instructions/swipex/en/1"

    .line 23
    .line 24
    filled-new-array {v1, p1, v0}, [Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method

.method public final j()Z
    .locals 3

    .line 1
    iget-object v0, p0, LsS0/a;->a:LRf0/l;

    .line 2
    .line 3
    const-string v1, "KEY_PREF_SWIPE_STAKE_STATE"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-virtual {v0, v1, v2}, LRf0/l;->c(Ljava/lang/String;Z)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    return v0
.end method

.method public final k()J
    .locals 2

    .line 1
    iget-wide v0, p0, LsS0/a;->g:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final l(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->a:LRf0/l;

    .line 2
    .line 3
    iget-object v1, p0, LsS0/a;->b:Lcom/google/gson/Gson;

    .line 4
    .line 5
    invoke-virtual {v1, p1}, Lcom/google/gson/Gson;->x(Ljava/lang/Object;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    const-string v1, "KEY_SAVED_SWIPE_X_CHAMPS"

    .line 10
    .line 11
    invoke-virtual {v0, v1, p1}, LRf0/l;->q(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final m(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, LsS0/a;->e:J

    .line 2
    .line 3
    return-void
.end method

.method public final n(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, LsS0/a;->a:LRf0/l;

    .line 2
    .line 3
    const-string v1, "KEY_PREF_SWIPE_ONBOARDING_STATE"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/l;->n(Ljava/lang/String;Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final o(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterSportsModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LsS0/a;->a:LRf0/l;

    .line 2
    .line 3
    iget-object v1, p0, LsS0/a;->b:Lcom/google/gson/Gson;

    .line 4
    .line 5
    invoke-virtual {v1, p1}, Lcom/google/gson/Gson;->x(Ljava/lang/Object;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    const-string v1, "KEY_SAVED_SWIPE_X_SPORTS"

    .line 10
    .line 11
    invoke-virtual {v0, v1, p1}, LRf0/l;->q(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final p(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, LsS0/a;->a:LRf0/l;

    .line 2
    .line 3
    const-string v1, "KEY_PREF_SWIPE_STAKE_STATE"

    .line 4
    .line 5
    invoke-virtual {v0, v1, p1}, LRf0/l;->n(Ljava/lang/String;Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final q(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, LsS0/a;->f:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final r(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, LsS0/a;->g:J

    .line 2
    .line 3
    return-void
.end method
