.class public final Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Li50/a;",
        "clickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Li50/a;Ljava/lang/String;)LA4/c;",
        "popular_classic_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Li50/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt;->g(Li50/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Li50/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt;->h(LB4/a;Li50/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Li50/a;Ljava/lang/String;)LA4/c;
    .locals 3
    .param p0    # Li50/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li50/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lh50/s;

    .line 2
    .line 3
    invoke-direct {v0}, Lh50/s;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lh50/t;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lh50/t;-><init>(Li50/a;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt$getLargePopularOneXGameBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt$getLargePopularOneXGameBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt$getLargePopularOneXGameBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/popular_classic/presentation/delegates/PopularClassicLargeOneXGameBannerViewHolderKt$getLargePopularOneXGameBannerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LPv/o;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LPv/o;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LPv/o;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Li50/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 7

    .line 1
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, Lh50/u;

    .line 4
    .line 5
    invoke-direct {v1, p2, p0, p1}, Lh50/u;-><init>(LB4/a;Li50/a;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    const/4 p0, 0x1

    .line 9
    const/4 p1, 0x0

    .line 10
    invoke-static {v0, p1, v1, p0, p1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    sget p1, Lpb/d;->isTablet:I

    .line 22
    .line 23
    invoke-virtual {p0, p1}, Landroid/content/res/Resources;->getBoolean(I)Z

    .line 24
    .line 25
    .line 26
    move-result p0

    .line 27
    if-eqz p0, :cond_0

    .line 28
    .line 29
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    check-cast p0, LPv/o;

    .line 34
    .line 35
    iget-object p0, p0, LPv/o;->c:Landroidx/constraintlayout/widget/Guideline;

    .line 36
    .line 37
    invoke-virtual {p0}, Landroid/view/View;->getId()I

    .line 38
    .line 39
    .line 40
    move-result p0

    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 p0, 0x0

    .line 43
    :goto_0
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, LPv/o;

    .line 48
    .line 49
    iget-object p1, p1, LPv/o;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 50
    .line 51
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    if-eqz v0, :cond_1

    .line 56
    .line 57
    check-cast v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;

    .line 58
    .line 59
    iput p0, v0, Landroidx/constraintlayout/widget/ConstraintLayout$LayoutParams;->v:I

    .line 60
    .line 61
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 65
    .line 66
    .line 67
    move-result-object p0

    .line 68
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    sget p1, Lpb/f;->space_8:I

    .line 73
    .line 74
    invoke-virtual {p0, p1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 75
    .line 76
    .line 77
    move-result v2

    .line 78
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 79
    .line 80
    const/16 v5, 0xd

    .line 81
    .line 82
    const/4 v6, 0x0

    .line 83
    const/4 v1, 0x0

    .line 84
    const/4 v3, 0x0

    .line 85
    const/4 v4, 0x0

    .line 86
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 87
    .line 88
    .line 89
    new-instance p0, Lh50/v;

    .line 90
    .line 91
    invoke-direct {p0, p2}, Lh50/v;-><init>(LB4/a;)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 95
    .line 96
    .line 97
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 98
    .line 99
    return-object p0

    .line 100
    :cond_1
    new-instance p0, Ljava/lang/NullPointerException;

    .line 101
    .line 102
    const-string p1, "null cannot be cast to non-null type androidx.constraintlayout.widget.ConstraintLayout.LayoutParams"

    .line 103
    .line 104
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw p0
.end method

.method public static final h(LB4/a;Li50/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, Lk50/g$c;

    .line 6
    .line 7
    invoke-virtual {p3}, Lk50/g$c;->e()Lk50/f;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, Lk50/g$c;

    .line 16
    .line 17
    invoke-virtual {p0}, Lk50/g$c;->d()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    const/4 v0, 0x1

    .line 22
    invoke-interface {p1, p2, p3, p0, v0}, Li50/a;->Q1(Ljava/lang/String;Lk50/f;Ljava/lang/String;Z)V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 12

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LPv/o;

    .line 6
    .line 7
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 8
    .line 9
    iget-object v1, p1, LPv/o;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, Lk50/g$c;

    .line 16
    .line 17
    invoke-virtual {v2}, Lk50/g$c;->e()Lk50/f;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {v2}, Lk50/f;->a()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    sget v3, Lpb/g;->ic_games_placeholder:I

    .line 26
    .line 27
    const/4 v4, 0x0

    .line 28
    new-array v6, v4, [LYW0/d;

    .line 29
    .line 30
    const/16 v10, 0xec

    .line 31
    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    const/4 v8, 0x0

    .line 36
    const/4 v9, 0x0

    .line 37
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    iget-object v0, p1, LPv/o;->f:Landroid/widget/TextView;

    .line 41
    .line 42
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    check-cast v1, Lk50/g$c;

    .line 47
    .line 48
    invoke-virtual {v1}, Lk50/g$c;->e()Lk50/f;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    invoke-virtual {v1}, Lk50/f;->b()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 57
    .line 58
    .line 59
    iget-object p1, p1, LPv/o;->e:Landroid/widget/TextView;

    .line 60
    .line 61
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    check-cast p0, Lk50/g$c;

    .line 66
    .line 67
    invoke-virtual {p0}, Lk50/g$c;->f()Lorg/xbet/uikit/components/header/a$a;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    invoke-virtual {p0}, Lorg/xbet/uikit/components/header/a$a;->h()Ljava/lang/CharSequence;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    invoke-virtual {p1, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 76
    .line 77
    .line 78
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 79
    .line 80
    return-object p0
.end method
