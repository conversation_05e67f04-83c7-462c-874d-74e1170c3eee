.class public final synthetic Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/appcompat/widget/Toolbar$g;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/f;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    return-void
.end method


# virtual methods
.method public final onMenuItemClick(Landroid/view/MenuItem;)Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/f;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/MenuItem;)Z

    move-result p1

    return p1
.end method
