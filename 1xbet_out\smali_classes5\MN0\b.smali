.class public final LMN0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LGN0/c;",
        "",
        "sportId",
        "LMD0/a;",
        "a",
        "(LGN0/c;J)LMD0/a;",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LGN0/c;J)LMD0/a;
    .locals 19
    .param p0    # LGN0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LGN0/c;->e()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lkotlin/text/StringsKt;->H1(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    const-string v0, " : "

    .line 14
    .line 15
    filled-new-array {v0}, [Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    const/4 v5, 0x2

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x2

    .line 23
    invoke-static/range {v1 .. v6}, Lkotlin/text/StringsKt;->split$default(Ljava/lang/CharSequence;[Ljava/lang/String;ZIILjava/lang/Object;)Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    new-instance v1, LMD0/a;

    .line 28
    .line 29
    invoke-virtual/range {p0 .. p0}, LGN0/c;->f()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual/range {p0 .. p0}, LGN0/c;->b()J

    .line 34
    .line 35
    .line 36
    move-result-wide v6

    .line 37
    invoke-virtual/range {p0 .. p0}, LGN0/c;->e()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v8

    .line 41
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    check-cast v3, Ljava/lang/String;

    .line 46
    .line 47
    const/4 v4, 0x0

    .line 48
    if-eqz v3, :cond_0

    .line 49
    .line 50
    invoke-static {v3}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    if-eqz v3, :cond_0

    .line 55
    .line 56
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    move v9, v3

    .line 61
    goto :goto_0

    .line 62
    :cond_0
    const/4 v9, 0x0

    .line 63
    :goto_0
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->K0(Ljava/util/List;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    check-cast v0, Ljava/lang/String;

    .line 68
    .line 69
    if-eqz v0, :cond_1

    .line 70
    .line 71
    invoke-static {v0}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    if-eqz v0, :cond_1

    .line 76
    .line 77
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    move v10, v0

    .line 82
    goto :goto_1

    .line 83
    :cond_1
    const/4 v10, 0x0

    .line 84
    :goto_1
    invoke-virtual/range {p0 .. p0}, LGN0/c;->g()LND0/k;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    invoke-static {v0}, LMN0/c;->b(LND0/k;)LMD0/b;

    .line 89
    .line 90
    .line 91
    move-result-object v11

    .line 92
    invoke-virtual/range {p0 .. p0}, LGN0/c;->h()LND0/k;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    invoke-static {v0}, LMN0/c;->b(LND0/k;)LMD0/b;

    .line 97
    .line 98
    .line 99
    move-result-object v12

    .line 100
    invoke-virtual/range {p0 .. p0}, LGN0/c;->d()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    sget-object v3, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->GAME_STATUS_RESULT:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 105
    .line 106
    const/4 v5, 0x1

    .line 107
    if-eq v0, v3, :cond_3

    .line 108
    .line 109
    invoke-virtual/range {p0 .. p0}, LGN0/c;->d()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    sget-object v13, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->GAME_STATUS_LIVE:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 114
    .line 115
    if-ne v0, v13, :cond_2

    .line 116
    .line 117
    goto :goto_2

    .line 118
    :cond_2
    const/4 v15, 0x0

    .line 119
    goto :goto_3

    .line 120
    :cond_3
    :goto_2
    const/4 v15, 0x1

    .line 121
    :goto_3
    invoke-virtual/range {p0 .. p0}, LGN0/c;->i()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v16

    .line 125
    invoke-virtual/range {p0 .. p0}, LGN0/c;->d()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    if-ne v0, v3, :cond_4

    .line 130
    .line 131
    const/16 v17, 0x1

    .line 132
    .line 133
    goto :goto_4

    .line 134
    :cond_4
    const/16 v17, 0x0

    .line 135
    .line 136
    :goto_4
    invoke-virtual/range {p0 .. p0}, LGN0/c;->d()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    sget-object v3, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->GAME_STATUS_LIVE:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 141
    .line 142
    if-ne v0, v3, :cond_5

    .line 143
    .line 144
    const/16 v18, 0x1

    .line 145
    .line 146
    goto :goto_5

    .line 147
    :cond_5
    const/16 v18, 0x0

    .line 148
    .line 149
    :goto_5
    const-string v5, ""

    .line 150
    .line 151
    const/4 v13, 0x0

    .line 152
    const/4 v14, 0x0

    .line 153
    move-wide/from16 v3, p1

    .line 154
    .line 155
    invoke-direct/range {v1 .. v18}, LMD0/a;-><init>(Ljava/lang/String;JLjava/lang/String;JLjava/lang/String;IILMD0/b;LMD0/b;IIZLjava/lang/String;ZZ)V

    .line 156
    .line 157
    .line 158
    return-object v1
.end method
