.class final Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.delegate.WhoWinCardFragmentDelegateImpl$setup$2"
    f = "WhoWinCardFragmentDelegateImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->a(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "event",
        "",
        "<anonymous>",
        "(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $entryPointType:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

.field final synthetic $fragment:Landroidx/fragment/app/Fragment;

.field final synthetic $whoWinCardViewModel:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;",
            "Landroidx/fragment/app/Fragment;",
            "Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$entryPointType:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$whoWinCardViewModel:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$entryPointType:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$whoWinCardViewModel:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;Landroidx/fragment/app/Fragment;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->invoke(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;->a:Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$a;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-nez v0, :cond_2

    .line 22
    .line 23
    instance-of v0, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;

    .line 24
    .line 25
    if-eqz v0, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;

    .line 28
    .line 29
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 30
    .line 31
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;

    .line 36
    .line 37
    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$entryPointType:Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;

    .line 38
    .line 39
    invoke-static {v0, v1, p1, v2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->c(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;Landroidx/fragment/app/FragmentManager;Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    instance-of v0, p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$b;

    .line 44
    .line 45
    if-eqz v0, :cond_1

    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;

    .line 48
    .line 49
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 50
    .line 51
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$b;

    .line 52
    .line 53
    invoke-virtual {p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$b;->a()LNn/d;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-virtual {v0, v1, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl;->d(Landroidx/fragment/app/Fragment;LNn/d;)V

    .line 58
    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 62
    .line 63
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 64
    .line 65
    .line 66
    throw p1

    .line 67
    :cond_2
    :goto_0
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardFragmentDelegateImpl$setup$2;->$whoWinCardViewModel:Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;

    .line 68
    .line 69
    invoke-interface {p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->j1()V

    .line 70
    .line 71
    .line 72
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 73
    .line 74
    return-object p1

    .line 75
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 76
    .line 77
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 78
    .line 79
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    throw p1
.end method
