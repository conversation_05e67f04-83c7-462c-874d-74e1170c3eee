.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$observeUnreadMessages$1"
    f = "MainMenuContainerViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Integer;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0008\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "unreadMessagesCount",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic I$0:I

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;

    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    iput p1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->I$0:I

    return-object v0
.end method

.method public final invoke(ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->invoke(ILkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_2

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->I$0:I

    .line 14
    .line 15
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 16
    .line 17
    invoke-static {v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    :cond_0
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    move-object v4, v3

    .line 26
    check-cast v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 27
    .line 28
    invoke-static {v1}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object v5

    .line 32
    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    .line 33
    .line 34
    .line 35
    move-result v6

    .line 36
    if-lez v6, :cond_1

    .line 37
    .line 38
    :goto_0
    move-object v12, v5

    .line 39
    goto :goto_1

    .line 40
    :cond_1
    const/4 v5, 0x0

    .line 41
    goto :goto_0

    .line 42
    :goto_1
    const/16 v18, 0x1f7f

    .line 43
    .line 44
    const/16 v19, 0x0

    .line 45
    .line 46
    const/4 v5, 0x0

    .line 47
    const/4 v6, 0x0

    .line 48
    const/4 v7, 0x0

    .line 49
    const/4 v8, 0x0

    .line 50
    const/4 v9, 0x0

    .line 51
    const/4 v10, 0x0

    .line 52
    const/4 v11, 0x0

    .line 53
    const/4 v13, 0x0

    .line 54
    const/4 v14, 0x0

    .line 55
    const/4 v15, 0x0

    .line 56
    const/16 v16, 0x0

    .line 57
    .line 58
    const/16 v17, 0x0

    .line 59
    .line 60
    invoke-static/range {v4 .. v19}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 61
    .line 62
    .line 63
    move-result-object v4

    .line 64
    invoke-interface {v2, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result v3

    .line 68
    if-eqz v3, :cond_0

    .line 69
    .line 70
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object v1

    .line 73
    :cond_2
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 74
    .line 75
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 76
    .line 77
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 78
    .line 79
    .line 80
    throw v1
.end method
