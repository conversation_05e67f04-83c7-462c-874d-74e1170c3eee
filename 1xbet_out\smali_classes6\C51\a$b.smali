.class public final LC51/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LC51/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LC51/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LC51/a$b;


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LC51/a$b;->a:LC51/a$b;

    return-void
.end method

.method public synthetic constructor <init>(LC51/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LC51/a$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lw51/a;
    .locals 1

    .line 1
    new-instance v0, LH51/a;

    .line 2
    .line 3
    invoke-direct {v0}, LH51/a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
