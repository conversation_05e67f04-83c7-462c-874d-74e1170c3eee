.class public interface abstract LN1/T;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LN1/T$a;
    }
.end annotation


# virtual methods
.method public abstract a(Lt1/G;I)V
.end method

.method public abstract b(J)V
.end method

.method public abstract c(Landroidx/media3/common/j;IZI)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract d(JIIILN1/T$a;)V
.end method

.method public abstract e(Landroidx/media3/common/r;)V
.end method

.method public abstract f(Lt1/G;II)V
.end method

.method public abstract g(Landroidx/media3/common/j;IZ)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
