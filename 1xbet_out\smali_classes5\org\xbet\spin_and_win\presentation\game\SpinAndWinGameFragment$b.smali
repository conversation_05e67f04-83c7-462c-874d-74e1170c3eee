.class public final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0011\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "org/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b",
        "Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;",
        "",
        "onGlobalLayout",
        "()V",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static synthetic a(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->b(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->G2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->r4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public onGlobalLayout()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 17
    .line 18
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 25
    .line 26
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iget-object v1, v1, Lbz0/c;->g:Landroidx/constraintlayout/widget/Guideline;

    .line 31
    .line 32
    invoke-virtual {v1}, Landroid/view/View;->getTop()I

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    iget-object v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 37
    .line 38
    invoke-static {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    iget-object v2, v2, Lbz0/c;->e:Landroidx/constraintlayout/widget/Guideline;

    .line 43
    .line 44
    invoke-virtual {v2}, Landroid/view/View;->getTop()I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    iget-object v3, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 49
    .line 50
    invoke-static {v3}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    iget-object v3, v3, Lbz0/c;->f:Landroidx/constraintlayout/widget/Guideline;

    .line 55
    .line 56
    invoke-virtual {v3}, Landroid/view/View;->getLeft()I

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    invoke-virtual {v0, v1, v2, v3}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->f(III)V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 64
    .line 65
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 70
    .line 71
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 72
    .line 73
    new-instance v2, Lorg/xbet/spin_and_win/presentation/game/i;

    .line 74
    .line 75
    invoke-direct {v2, v1}, Lorg/xbet/spin_and_win/presentation/game/i;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {v0, v2}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->setAnimationEndListener(Lkotlin/jvm/functions/Function0;)V

    .line 79
    .line 80
    .line 81
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 82
    .line 83
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->E2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Z

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    if-nez v0, :cond_0

    .line 88
    .line 89
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 90
    .line 91
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 96
    .line 97
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 98
    .line 99
    .line 100
    move-result v0

    .line 101
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 102
    .line 103
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    iget-object v1, v1, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 108
    .line 109
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 110
    .line 111
    .line 112
    move-result v1

    .line 113
    sub-int/2addr v0, v1

    .line 114
    neg-int v0, v0

    .line 115
    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 116
    .line 117
    invoke-static {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->G2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    invoke-virtual {v1, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->u4(I)V

    .line 122
    .line 123
    .line 124
    :cond_0
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 125
    .line 126
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->G2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->w4()V

    .line 131
    .line 132
    .line 133
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 134
    .line 135
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->G2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    const/4 v1, 0x1

    .line 140
    invoke-virtual {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->c4(Z)V

    .line 141
    .line 142
    .line 143
    return-void
.end method
