.class public abstract Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        ">",
        "LXW0/a;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008 \u0018\u0000 ]*\u0008\u0008\u0000\u0010\u0002*\u00020\u00012\u00020\u0003:\u0001^B\u0011\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u000f\u0010\u0010\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000eJ\u000f\u0010\u0011\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u000eJ\u0019\u0010\u0014\u001a\u00020\n2\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0012H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0019\u0010\u0016\u001a\u00020\n2\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0012H\u0015\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u000f\u0010\u0018\u001a\u00020\u0017H\u0004\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u000eJ\u000f\u0010\u001b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u000eJ\u0011\u0010\u001d\u001a\u0004\u0018\u00010\u001cH \u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010 \u001a\u00020\u001fH$\u00a2\u0006\u0004\u0008 \u0010!J\u001f\u0010%\u001a\u00020\n2\u0006\u0010#\u001a\u00020\"2\u0006\u0010$\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008\'\u0010\u000eJ\u0017\u0010*\u001a\u00020\n2\u0006\u0010)\u001a\u00020(H\u0004\u00a2\u0006\u0004\u0008*\u0010+J\u000f\u0010,\u001a\u00020\nH\u0004\u00a2\u0006\u0004\u0008,\u0010\u000eJ\u000f\u0010-\u001a\u00020\nH\u0004\u00a2\u0006\u0004\u0008-\u0010\u000eJ\u001d\u00100\u001a\u00020\n2\u000c\u0010/\u001a\u0008\u0012\u0004\u0012\u00020\n0.H\u0004\u00a2\u0006\u0004\u00080\u00101J\u001b\u00104\u001a\u00020\n*\u0002022\u0006\u00103\u001a\u00020\u0017H\u0016\u00a2\u0006\u0004\u00084\u00105R\"\u0010=\u001a\u0002068\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<R\"\u0010E\u001a\u00020>8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008?\u0010@\u001a\u0004\u0008A\u0010B\"\u0004\u0008C\u0010DR\"\u0010M\u001a\u00020F8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008G\u0010H\u001a\u0004\u0008I\u0010J\"\u0004\u0008K\u0010LR\u001a\u0010S\u001a\u00020N8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008O\u0010P\u001a\u0004\u0008Q\u0010RR\u001a\u0010Y\u001a\u00020T8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008U\u0010V\u001a\u0004\u0008W\u0010XR\u0014\u0010\\\u001a\u00028\u00008$X\u00a4\u0004\u00a2\u0006\u0006\u001a\u0004\u0008Z\u0010[\u00a8\u0006_"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "T",
        "LXW0/a;",
        "",
        "layout",
        "<init>",
        "(I)V",
        "",
        "description",
        "",
        "h3",
        "(Ljava/lang/String;)V",
        "f3",
        "()V",
        "S2",
        "Y2",
        "g3",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "t2",
        "",
        "R2",
        "()Z",
        "onResume",
        "onDestroyView",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "balanceTitle",
        "c3",
        "(Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V",
        "U2",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "e3",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "d3",
        "k3",
        "Lkotlin/Function0;",
        "runFunction",
        "i3",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lcom/google/android/material/appbar/CollapsingToolbarLayout;",
        "enable",
        "I2",
        "(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V",
        "LTZ0/a;",
        "i0",
        "LTZ0/a;",
        "J2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "j0",
        "LzX0/k;",
        "P2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lck/a;",
        "k0",
        "Lck/a;",
        "L2",
        "()Lck/a;",
        "setChangeBalanceDialogProvider",
        "(Lck/a;)V",
        "changeBalanceDialogProvider",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "l0",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "m0",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "Q2",
        "()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "viewModel",
        "n0",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final n0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public i0:LTZ0/a;

.field public j0:LzX0/k;

.field public k0:Lck/a;

.field public final l0:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->n0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$a;

    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LXW0/a;-><init>(I)V

    .line 2
    .line 3
    .line 4
    sget-object p1, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 5
    .line 6
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->l0:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 7
    .line 8
    sget-object p1, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 9
    .line 10
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->m0:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Z2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->b3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->j3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->a3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->X2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->T2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static final synthetic G2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Y2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->h3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final S2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/s;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/s;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 12
    .line 13
    .line 14
    const-string v2, "REQUEST_KEY_CLOSE_GAME"

    .line 15
    .line 16
    invoke-virtual {v0, v2, p0, v1}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public static final T2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->p0()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->j4()V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final V2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-virtual {v0, p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->T3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final W2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LXW0/d;->h(Landroidx/fragment/app/Fragment;)Ljava/lang/Boolean;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final X2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->f3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final Z2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->g3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final a3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Y3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final b3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-virtual {v0, p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->U3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/DepositCallScreenType;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method private final f3()V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->L2()Lck/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 8
    .line 9
    .line 10
    move-result-object v5

    .line 11
    const/16 v11, 0x2ee

    .line 12
    .line 13
    const/4 v12, 0x0

    .line 14
    const/4 v2, 0x0

    .line 15
    const/4 v3, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v6, 0x0

    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v8, 0x0

    .line 20
    const-string v9, "REQUEST_CHANGE_BALANCE_KEY"

    .line 21
    .line 22
    const/4 v10, 0x0

    .line 23
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final h3(Ljava/lang/String;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->error:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->ok_new:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 22
    .line 23
    const/16 v15, 0xbf8

    .line 24
    .line 25
    const/16 v16, 0x0

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    const/4 v9, 0x0

    .line 31
    const/4 v10, 0x0

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v12, 0x0

    .line 34
    const/4 v14, 0x0

    .line 35
    move-object/from16 v4, p1

    .line 36
    .line 37
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public static final j3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->V2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->W2(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V
    .locals 2
    .param p1    # Lcom/google/android/material/appbar/CollapsingToolbarLayout;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lcom/google/android/material/appbar/AppBarLayout$LayoutParams;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lcom/google/android/material/appbar/AppBarLayout$LayoutParams;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_2

    .line 14
    .line 15
    if-eqz p2, :cond_1

    .line 16
    .line 17
    const/16 p2, 0x15

    .line 18
    .line 19
    goto :goto_1

    .line 20
    :cond_1
    const/4 p2, 0x0

    .line 21
    :goto_1
    invoke-virtual {v0, p2}, Lcom/google/android/material/appbar/AppBarLayout$LayoutParams;->setScrollFlags(I)V

    .line 22
    .line 23
    .line 24
    :cond_2
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final J2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public abstract K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
.end method

.method public final L2()Lck/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k0:Lck/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->m0:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public abstract N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->l0:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final P2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->j0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public abstract Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TT;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public final R2()Z
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    :goto_0
    if-eqz v0, :cond_1

    .line 6
    .line 7
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/base/presentation/o;

    .line 8
    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    move-object v1, v0

    .line 12
    check-cast v1, Lorg/xplatform/aggregator/impl/base/presentation/o;

    .line 13
    .line 14
    invoke-interface {v1}, Lorg/xplatform/aggregator/impl/base/presentation/o;->L1()Z

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-eqz v1, :cond_0

    .line 19
    .line 20
    const/4 v0, 0x1

    .line 21
    return v0

    .line 22
    :cond_0
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorMainFragment;

    .line 23
    .line 24
    if-nez v1, :cond_1

    .line 25
    .line 26
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    goto :goto_0

    .line 31
    :cond_1
    const/4 v0, 0x0

    .line 32
    return v0
.end method

.method public U2()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v3, Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;->ACTIVE:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 6
    .line 7
    sget v4, Lpb/g;->ic_search_new:I

    .line 8
    .line 9
    new-instance v1, LM01/c;

    .line 10
    .line 11
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/n;

    .line 12
    .line 13
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/n;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)V

    .line 14
    .line 15
    .line 16
    const/16 v13, 0x3f0

    .line 17
    .line 18
    const/4 v14, 0x0

    .line 19
    const-string v2, "ic_search_new"

    .line 20
    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v8, 0x0

    .line 24
    const/4 v9, 0x0

    .line 25
    const/4 v10, 0x0

    .line 26
    const/4 v11, 0x0

    .line 27
    const/4 v12, 0x0

    .line 28
    invoke-direct/range {v1 .. v14}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 29
    .line 30
    .line 31
    const/4 v2, 0x1

    .line 32
    new-array v3, v2, [LM01/c;

    .line 33
    .line 34
    const/4 v4, 0x0

    .line 35
    aput-object v1, v3, v4

    .line 36
    .line 37
    invoke-static {v3}, Lkotlin/collections/v;->h([Ljava/lang/Object;)Ljava/util/ArrayList;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtons(Ljava/util/ArrayList;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sget v3, LlZ0/d;->uikitSecondary:I

    .line 49
    .line 50
    const/4 v5, 0x2

    .line 51
    const/4 v6, 0x0

    .line 52
    invoke-static {v1, v3, v6, v5, v6}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtonsColorStateList(Landroid/content/res/ColorStateList;)V

    .line 61
    .line 62
    .line 63
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/o;

    .line 64
    .line 65
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/o;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 66
    .line 67
    .line 68
    invoke-static {v0, v4, v1, v2, v6}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final Y2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/p;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/p;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 10
    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x1

    .line 14
    invoke-static {v0, v2, v1, v3, v2}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/q;

    .line 18
    .line 19
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/q;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0, v2, v1, v3, v2}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setUpdateClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/r;

    .line 26
    .line 27
    invoke-direct {v1, p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/r;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v2, v1, v3, v2}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setTopUpAccountClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    :cond_0
    return-void
.end method

.method public c3(Lorg/xbet/balance/model/BalanceModel;Ljava/lang/String;)V
    .locals 6
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountTitle(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    if-eqz p2, :cond_1

    .line 15
    .line 16
    sget-object v0, Ll8/j;->a:Ll8/j;

    .line 17
    .line 18
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getMoney()D

    .line 19
    .line 20
    .line 21
    move-result-wide v1

    .line 22
    const/4 v4, 0x2

    .line 23
    const/4 v5, 0x0

    .line 24
    const/4 v3, 0x0

    .line 25
    invoke-static/range {v0 .. v5}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p2, v0, p1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setBalanceValue(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    :cond_1
    return-void
.end method

.method public final d3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final e3(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, "OPEN_GAME_ITEM"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0, v1, p1}, Landroid/os/Bundle;->putSerializable(Ljava/lang/String;Ljava/io/Serializable;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x1

    .line 18
    new-array v0, v0, [Lkotlin/Pair;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    aput-object p1, v0, v1

    .line 22
    .line 23
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 28
    .line 29
    .line 30
    :goto_0
    sget-object p1, LKW0/b;->a:LKW0/b;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p1, p0, v0}, LKW0/b;->c(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final g3()V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->L2()Lck/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    sget v3, Lpb/k;->gift_balance_dialog_description:I

    .line 12
    .line 13
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    const/16 v11, 0x2e6

    .line 22
    .line 23
    const/4 v12, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const-string v9, "REQUEST_CHANGE_BALANCE_DIALOG_KEY"

    .line 30
    .line 31
    const/4 v10, 0x0

    .line 32
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final i3(Lkotlin/jvm/functions/Function0;)V
    .locals 3
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/core/presentation/l;

    .line 8
    .line 9
    invoke-direct {v2, p1}, Lorg/xplatform/aggregator/impl/core/presentation/l;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, p0, v2, v1}, LKW0/b;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function0;LTZ0/a;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final k3()V
    .locals 2

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, p0, v1}, LKW0/b;->f(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/core/presentation/m;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/m;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;)V

    .line 7
    .line 8
    .line 9
    const-string v0, "REQUEST_BONUS_BALANCE_WARNING_DIALOG_KEY"

    .line 10
    .line 11
    invoke-static {p0, v0, p1}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->K3()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->J3()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->U2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->I3()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v14, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    move-object v2, v14

    .line 35
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 36
    .line 37
    .line 38
    const/4 v15, 0x3

    .line 39
    const/16 v16, 0x0

    .line 40
    .line 41
    const/4 v12, 0x0

    .line 42
    const/4 v13, 0x0

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->i4()V

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->c4()Lkotlinx/coroutines/flow/f0;

    .line 58
    .line 59
    .line 60
    move-result-object v8

    .line 61
    new-instance v11, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;

    .line 62
    .line 63
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 67
    .line 68
    .line 69
    move-result-object v9

    .line 70
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$2;

    .line 75
    .line 76
    move-object v7, v5

    .line 77
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 78
    .line 79
    .line 80
    const/4 v6, 0x3

    .line 81
    const/4 v7, 0x0

    .line 82
    const/4 v3, 0x0

    .line 83
    const/4 v4, 0x0

    .line 84
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 85
    .line 86
    .line 87
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 88
    .line 89
    .line 90
    move-result-object v2

    .line 91
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->g4()Lkotlinx/coroutines/flow/e;

    .line 92
    .line 93
    .line 94
    move-result-object v8

    .line 95
    new-instance v11, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$3;

    .line 96
    .line 97
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$3;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 98
    .line 99
    .line 100
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 101
    .line 102
    .line 103
    move-result-object v9

    .line 104
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    new-instance v5, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$3;

    .line 109
    .line 110
    move-object v7, v5

    .line 111
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 112
    .line 113
    .line 114
    const/4 v7, 0x0

    .line 115
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 116
    .line 117
    .line 118
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 119
    .line 120
    .line 121
    move-result-object v2

    .line 122
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->P3()Lkotlinx/coroutines/flow/Z;

    .line 123
    .line 124
    .line 125
    move-result-object v8

    .line 126
    new-instance v11, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$4;

    .line 127
    .line 128
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$4;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 129
    .line 130
    .line 131
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 132
    .line 133
    .line 134
    move-result-object v9

    .line 135
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    new-instance v4, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$4;

    .line 140
    .line 141
    move-object v7, v4

    .line 142
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment$onInitView$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 143
    .line 144
    .line 145
    const/4 v5, 0x3

    .line 146
    const/4 v6, 0x0

    .line 147
    const/4 v2, 0x0

    .line 148
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 149
    .line 150
    .line 151
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->S2()V

    .line 152
    .line 153
    .line 154
    return-void
.end method
