.class public interface abstract Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$DailyTasksScreen;,
        Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$None;,
        Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;,
        Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;,
        Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0005\u0002\u0003\u0004\u0005\u0006\u0082\u0001\u0005\u0007\u0008\t\n\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;",
        "Ljava/io/Serializable;",
        "Tournaments",
        "Prizes",
        "Promocode",
        "None",
        "DailyTasksScreen",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$DailyTasksScreen;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$None;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Promocode;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
