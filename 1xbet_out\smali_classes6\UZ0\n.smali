.class public final synthetic LUZ0/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroid/content/Context;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LUZ0/n;->a:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LUZ0/n;->a:Landroid/content/Context;

    invoke-static {v0}, Lorg/xbet/uikit/components/dialog/stylyableviews/CustomLottieAlertDialogView;->c(Landroid/content/Context;)Lorg/xbet/uikit/components/buttons/DSButton;

    move-result-object v0

    return-object v0
.end method
