.class public final Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00b0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a9\u0010\u000b\u001a\u00020\n*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001aA\u0010\u000e\u001a\u00020\n*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u00082\u0006\u0010\t\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001aA\u0010\u0010\u001a\u00020\n*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u00082\u0006\u0010\t\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u0010\u0010\u000f\u001aI\u0010\u0012\u001a\u00020\n*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u00082\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u0012\u0010\u0013\u001aE\u0010\u0019\u001a\u00020\n*\u00020\u00142\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001aE\u0010\u001b\u001a\u00020\n*\u00020\u00142\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001a\u001a=\u0010\u001d\u001a\u00020\n*\u00020\u001c2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001e\u001a=\u0010 \u001a\u00020\n*\u00020\u001f2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008 \u0010!\u001a=\u0010#\u001a\u00020\n*\u00020\"2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008#\u0010$\u001a=\u0010%\u001a\u00020\n*\u00020\"2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008%\u0010$\u001aE\u0010\'\u001a\u00020\n*\u00020&2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\'\u0010(\u001aE\u0010)\u001a\u00020\n*\u00020&2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008)\u0010(\u001aE\u0010+\u001a\u00020\n*\u00020*2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008+\u0010,\u001aE\u0010-\u001a\u00020\n*\u00020*2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008-\u0010,\u001aA\u0010.\u001a\u00020\n2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008.\u0010/\u001aE\u00100\u001a\u00020\n*\u00020\u001f2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u00080\u00101\u001aA\u00102\u001a\u00020\n2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u00082\u0010/\u001aE\u00103\u001a\u00020\n*\u00020\u001f2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u00083\u00101\u001a=\u00105\u001a\u00020\n*\u0002042\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00085\u00106\u001a=\u00107\u001a\u00020\n*\u0002042\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00087\u00106\u001a=\u00108\u001a\u00020\n*\u0002042\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00088\u00106\u001a\'\u0010;\u001a\u00020:*\u0002092\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u0015H\u0002\u00a2\u0006\u0004\u0008;\u0010<\u001a)\u0010>\u001a\u00020\n*\u00020=2\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008>\u0010?\u001a)\u0010@\u001a\u00020\n*\u00020=2\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008@\u0010?\u001a=\u0010B\u001a\u00020\n*\u00020A2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008B\u0010C\u001a=\u0010D\u001a\u00020\n*\u00020A2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008D\u0010C\u001a=\u0010F\u001a\u00020\n*\u00020E2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008F\u0010G\u001a=\u0010H\u001a\u00020\n*\u00020E2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008H\u0010G\u001aE\u0010J\u001a\u00020\n*\u00020I2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008J\u0010K\u001aE\u0010L\u001a\u00020\n*\u00020I2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008L\u0010K\u001aM\u0010O\u001a\u00020\n*\u00020M2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010N\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008O\u0010P\u001aM\u0010Q\u001a\u00020\n*\u00020M2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010N\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008Q\u0010P\u001a=\u0010S\u001a\u00020\n*\u00020R2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008S\u0010T\u001a=\u0010U\u001a\u00020\n*\u00020R2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00170\u00152\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008U\u0010T\u001a\u0017\u0010V\u001a\u00020\u00082\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008V\u0010W\"\u0014\u0010[\u001a\u00020X8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008Y\u0010Z\u00a8\u0006\\"
    }
    d2 = {
        "LD80/a;",
        "LHX0/e;",
        "resourceManager",
        "",
        "Lcom/xbet/onexcore/configs/MenuItemModel;",
        "markedItems",
        "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
        "menuSectionType",
        "",
        "infoFilled",
        "LN80/c;",
        "J",
        "(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "addMyVirtualSubtitle",
        "g",
        "(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;",
        "v",
        "isSmallDevice",
        "I",
        "(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZZ)LN80/c;",
        "LD80/a$k;",
        "Lkotlin/Function1;",
        "",
        "",
        "getString",
        "t",
        "(LD80/a$k;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "G",
        "LD80/a$m;",
        "j",
        "(LD80/a$m;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "LD80/a$f;",
        "i",
        "(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "LD80/a$e;",
        "o",
        "(LD80/a$e;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "B",
        "LD80/a$i;",
        "r",
        "(LD80/a$i;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "E",
        "LD80/a$h;",
        "q",
        "(LD80/a$h;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "D",
        "e",
        "(Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "a",
        "(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "d",
        "c",
        "LD80/a$g;",
        "w",
        "(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "p",
        "C",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;",
        "Ln41/m;",
        "h",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;Lkotlin/jvm/functions/Function1;)Ln41/m;",
        "LD80/a$l;",
        "u",
        "(LD80/a$l;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "H",
        "LD80/a$c;",
        "m",
        "(LD80/a$c;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "z",
        "LD80/a$d;",
        "n",
        "(LD80/a$d;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "A",
        "LD80/a$j;",
        "s",
        "(LD80/a$j;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;",
        "F",
        "LD80/a$a;",
        "addSubtitle",
        "k",
        "(LD80/a$a;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;",
        "x",
        "LD80/a$b;",
        "l",
        "(LD80/a$b;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;",
        "y",
        "f",
        "(LHX0/e;)Z",
        "LN80/c$r;",
        "b",
        "()LN80/c$r;",
        "emptyMenuUiItem",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final A(LD80/a$d;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$d;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$d;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v2

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    const/4 v3, 0x0

    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    :cond_0
    const/4 p2, 0x1

    .line 20
    const/4 v1, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    if-eqz v0, :cond_0

    .line 31
    .line 32
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    invoke-virtual {p0}, LD80/a$d;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    if-ne v0, v4, :cond_2

    .line 43
    .line 44
    const/4 p2, 0x1

    .line 45
    :goto_0
    new-instance v0, Ln8/a;

    .line 46
    .line 47
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 48
    .line 49
    .line 50
    sget-object v4, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 51
    .line 52
    sget-object v4, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 53
    .line 54
    invoke-virtual {p0}, LD80/a$d;->a()J

    .line 55
    .line 56
    .line 57
    move-result-wide v5

    .line 58
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    new-array v5, p2, [Ljava/lang/Object;

    .line 63
    .line 64
    aput-object p0, v5, v3

    .line 65
    .line 66
    invoke-static {v5, p2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    const-string p2, "static/img/android/icons_currency/v2/%d.svg"

    .line 71
    .line 72
    invoke-static {v4, p2, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-virtual {v0, p0}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    invoke-virtual {p0}, Ln8/a;->a()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v4

    .line 84
    sget v5, LlZ0/h;->ic_glyph_currency_placeholder:I

    .line 85
    .line 86
    sget p0, Lpb/k;->balance_management_title:I

    .line 87
    .line 88
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p0

    .line 96
    move-object v6, p0

    .line 97
    check-cast v6, Ljava/lang/String;

    .line 98
    .line 99
    sget p0, Lpb/k;->balance_managment_description:I

    .line 100
    .line 101
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 102
    .line 103
    .line 104
    move-result-object p0

    .line 105
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object p0

    .line 109
    move-object v7, p0

    .line 110
    check-cast v7, Ljava/lang/String;

    .line 111
    .line 112
    new-instance v0, LN80/c$k;

    .line 113
    .line 114
    move-object v3, p3

    .line 115
    invoke-direct/range {v0 .. v7}, LN80/c$k;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 116
    .line 117
    .line 118
    return-object v0
.end method

.method public static final B(LD80/a$e;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$e;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_1

    .line 7
    .line 8
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    :cond_0
    const/4 v4, 0x0

    .line 15
    goto :goto_0

    .line 16
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 31
    .line 32
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    if-ne v0, v2, :cond_2

    .line 37
    .line 38
    const/4 v1, 0x1

    .line 39
    const/4 v4, 0x1

    .line 40
    :goto_0
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 41
    .line 42
    .line 43
    move-result-object p2

    .line 44
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    .line 45
    .line 46
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    aget p2, v0, p2

    .line 51
    .line 52
    const/16 v0, 0x25

    .line 53
    .line 54
    if-eq p2, v0, :cond_4

    .line 55
    .line 56
    const/16 v0, 0x26

    .line 57
    .line 58
    if-eq p2, v0, :cond_3

    .line 59
    .line 60
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    return-object p0

    .line 65
    :cond_3
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    sget v6, LlZ0/h;->ic_glyph_toto:I

    .line 70
    .line 71
    sget p0, Lpb/k;->toto_name:I

    .line 72
    .line 73
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 74
    .line 75
    .line 76
    move-result-object p0

    .line 77
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object p0

    .line 81
    check-cast p0, Ljava/lang/String;

    .line 82
    .line 83
    sget-object p2, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 84
    .line 85
    invoke-virtual {p0, p2}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v7

    .line 89
    sget p0, Lpb/k;->menu_toto_description:I

    .line 90
    .line 91
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 92
    .line 93
    .line 94
    move-result-object p0

    .line 95
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p0

    .line 99
    move-object v8, p0

    .line 100
    check-cast v8, Ljava/lang/String;

    .line 101
    .line 102
    new-instance v2, LN80/c$o;

    .line 103
    .line 104
    move-object v5, p3

    .line 105
    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    return-object v2

    .line 109
    :cond_4
    move-object v5, p3

    .line 110
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 111
    .line 112
    .line 113
    move-result-object v3

    .line 114
    sget v6, LlZ0/h;->ic_glyph_games:I

    .line 115
    .line 116
    invoke-virtual {p0}, LD80/a$e;->b()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v7

    .line 120
    sget p0, Lpb/k;->menu_one_x_games_description:I

    .line 121
    .line 122
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 123
    .line 124
    .line 125
    move-result-object p0

    .line 126
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object p0

    .line 130
    move-object v8, p0

    .line 131
    check-cast v8, Ljava/lang/String;

    .line 132
    .line 133
    new-instance v2, LN80/c$o;

    .line 134
    .line 135
    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 136
    .line 137
    .line 138
    return-object v2
.end method

.method public static final C(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$g;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v2

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-ne v0, v3, :cond_1

    .line 40
    .line 41
    const/4 v1, 0x1

    .line 42
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_games:I

    .line 43
    .line 44
    sget p2, Lpb/k;->all_games:I

    .line 45
    .line 46
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    move-object v5, p2

    .line 55
    check-cast v5, Ljava/lang/String;

    .line 56
    .line 57
    sget p2, Lpb/k;->menu_one_x_games_description:I

    .line 58
    .line 59
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object p2

    .line 63
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    move-object v6, p2

    .line 68
    check-cast v6, Ljava/lang/String;

    .line 69
    .line 70
    invoke-virtual {p0}, LD80/a$g;->a()Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    new-instance v7, Ljava/util/ArrayList;

    .line 75
    .line 76
    const/16 p2, 0xa

    .line 77
    .line 78
    invoke-static {p0, p2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 79
    .line 80
    .line 81
    move-result p2

    .line 82
    invoke-direct {v7, p2}, Ljava/util/ArrayList;-><init>(I)V

    .line 83
    .line 84
    .line 85
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 86
    .line 87
    .line 88
    move-result-object p0

    .line 89
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 90
    .line 91
    .line 92
    move-result p2

    .line 93
    if-eqz p2, :cond_3

    .line 94
    .line 95
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p2

    .line 99
    check-cast p2, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 100
    .line 101
    invoke-static {p2, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;Lkotlin/jvm/functions/Function1;)Ln41/m;

    .line 102
    .line 103
    .line 104
    move-result-object p2

    .line 105
    invoke-interface {v7, p2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 106
    .line 107
    .line 108
    goto :goto_1

    .line 109
    :cond_3
    new-instance v0, LN80/c$q;

    .line 110
    .line 111
    move-object v3, p3

    .line 112
    invoke-direct/range {v0 .. v7}, LN80/c$q;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;Ljava/util/List;)V

    .line 113
    .line 114
    .line 115
    return-object v0
.end method

.method public static final D(LD80/a$h;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$h;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    const/4 v1, 0x3

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 6
    .line 7
    .line 8
    move-result-object v4

    .line 9
    sget v7, LlZ0/d;->uikitStaticWhite:I

    .line 10
    .line 11
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    const/4 v5, 0x0

    .line 16
    if-eqz v3, :cond_1

    .line 17
    .line 18
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    if-eqz v3, :cond_1

    .line 23
    .line 24
    :cond_0
    const/4 v3, 0x0

    .line 25
    goto :goto_0

    .line 26
    :cond_1
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    :cond_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result v6

    .line 34
    if-eqz v6, :cond_0

    .line 35
    .line 36
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v6

    .line 40
    check-cast v6, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 41
    .line 42
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 43
    .line 44
    .line 45
    move-result-object v8

    .line 46
    if-ne v6, v8, :cond_2

    .line 47
    .line 48
    const/4 v3, 0x0

    .line 49
    const/4 v5, 0x1

    .line 50
    :goto_0
    sget v9, LlZ0/h;->ic_glyph_cart:I

    .line 51
    .line 52
    sget v10, LlZ0/d;->uikitStaticWhite:I

    .line 53
    .line 54
    sget v11, LlZ0/d;->uikitStaticWhite20:I

    .line 55
    .line 56
    sget v6, Lpb/k;->promo_codes_title:I

    .line 57
    .line 58
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v6

    .line 62
    invoke-interface {v0, v6}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v6

    .line 66
    move-object v12, v6

    .line 67
    check-cast v12, Ljava/lang/String;

    .line 68
    .line 69
    sget-object v6, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 70
    .line 71
    sget-object v6, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 72
    .line 73
    sget v8, Lpb/k;->placeholder_variant_5:I

    .line 74
    .line 75
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 76
    .line 77
    .line 78
    move-result-object v8

    .line 79
    invoke-interface {v0, v8}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v8

    .line 83
    check-cast v8, Ljava/lang/String;

    .line 84
    .line 85
    sget v13, Lpb/k;->menu_promo_subtitle:I

    .line 86
    .line 87
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 88
    .line 89
    .line 90
    move-result-object v13

    .line 91
    invoke-interface {v0, v13}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v13

    .line 95
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->c()I

    .line 96
    .line 97
    .line 98
    move-result v14

    .line 99
    invoke-static {v14}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v14

    .line 103
    sget v15, Lpb/k;->pts_symbol:I

    .line 104
    .line 105
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 106
    .line 107
    .line 108
    move-result-object v15

    .line 109
    invoke-interface {v0, v15}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    new-array v15, v1, [Ljava/lang/Object;

    .line 114
    .line 115
    aput-object v13, v15, v3

    .line 116
    .line 117
    aput-object v14, v15, v2

    .line 118
    .line 119
    const/4 v2, 0x2

    .line 120
    aput-object v0, v15, v2

    .line 121
    .line 122
    invoke-static {v15, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-static {v6, v8, v0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 127
    .line 128
    .line 129
    move-result-object v13

    .line 130
    if-nez p4, :cond_3

    .line 131
    .line 132
    sget v0, Lpb/g;->banner_promo:I

    .line 133
    .line 134
    move v14, v0

    .line 135
    goto :goto_1

    .line 136
    :cond_3
    const/4 v14, 0x0

    .line 137
    :goto_1
    sget v15, Lpb/g;->promo_shop_item_background:I

    .line 138
    .line 139
    sget v8, LlZ0/d;->uikitStaticWhite:I

    .line 140
    .line 141
    new-instance v3, LN80/c$l;

    .line 142
    .line 143
    move-object/from16 v6, p3

    .line 144
    .line 145
    invoke-direct/range {v3 .. v15}, LN80/c$l;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIIIILjava/lang/String;Ljava/lang/String;II)V

    .line 146
    .line 147
    .line 148
    return-object v3
.end method

.method public static final E(LD80/a$i;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$i;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$i;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    sget v4, LlZ0/d;->uikitStaticWhite:I

    .line 6
    .line 7
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    :cond_0
    const/4 p0, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    if-eqz v0, :cond_0

    .line 31
    .line 32
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    invoke-virtual {p0}, LD80/a$i;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    if-ne v0, v3, :cond_2

    .line 43
    .line 44
    const/4 p0, 0x1

    .line 45
    :goto_0
    sget v6, LlZ0/h;->ic_glyph_balloons:I

    .line 46
    .line 47
    sget v7, LlZ0/d;->uikitStaticDarkOrange:I

    .line 48
    .line 49
    sget v8, LlZ0/d;->uikitStaticWhite:I

    .line 50
    .line 51
    sget p2, Lpb/k;->news_promo:I

    .line 52
    .line 53
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    move-object v9, p2

    .line 62
    check-cast v9, Ljava/lang/String;

    .line 63
    .line 64
    sget p2, Lpb/k;->menu_promo_description:I

    .line 65
    .line 66
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 67
    .line 68
    .line 69
    move-result-object p2

    .line 70
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    move-object v10, p1

    .line 75
    check-cast v10, Ljava/lang/String;

    .line 76
    .line 77
    sget v12, Lpb/g;->main_menu_promotion_item_background:I

    .line 78
    .line 79
    if-nez p4, :cond_3

    .line 80
    .line 81
    sget v2, LlZ0/h;->ic_banner_events:I

    .line 82
    .line 83
    move v11, v2

    .line 84
    goto :goto_1

    .line 85
    :cond_3
    const/4 v11, 0x0

    .line 86
    :goto_1
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 87
    .line 88
    new-instance v0, LN80/c$l;

    .line 89
    .line 90
    move v2, p0

    .line 91
    move-object/from16 v3, p3

    .line 92
    .line 93
    invoke-direct/range {v0 .. v12}, LN80/c$l;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIIIILjava/lang/String;Ljava/lang/String;II)V

    .line 94
    .line 95
    .line 96
    return-object v0
.end method

.method public static final F(LD80/a$j;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$j;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$j;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    :cond_0
    const/4 p0, 0x0

    .line 19
    goto :goto_0

    .line 20
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    invoke-virtual {p0}, LD80/a$j;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    if-ne v0, v3, :cond_2

    .line 41
    .line 42
    const/4 p0, 0x1

    .line 43
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_lock:I

    .line 44
    .line 45
    sget p2, Lpb/k;->increase_security:I

    .line 46
    .line 47
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    move-object v7, p2

    .line 56
    check-cast v7, Ljava/lang/String;

    .line 57
    .line 58
    sget p2, Lpb/k;->menu_increase_security_description:I

    .line 59
    .line 60
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    move-object v8, p1

    .line 69
    check-cast v8, Ljava/lang/String;

    .line 70
    .line 71
    if-nez p4, :cond_3

    .line 72
    .line 73
    sget v2, Lpb/g;->banner_security:I

    .line 74
    .line 75
    move v9, v2

    .line 76
    goto :goto_1

    .line 77
    :cond_3
    const/4 v9, 0x0

    .line 78
    :goto_1
    sget v5, LlZ0/d;->uikitPrimary:I

    .line 79
    .line 80
    sget v6, LlZ0/d;->uikitStaticWhite:I

    .line 81
    .line 82
    new-instance v0, LN80/c$m;

    .line 83
    .line 84
    move v10, v5

    .line 85
    move v2, p0

    .line 86
    move-object v3, p3

    .line 87
    invoke-direct/range {v0 .. v10}, LN80/c$m;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIILjava/lang/String;Ljava/lang/String;II)V

    .line 88
    .line 89
    .line 90
    return-object v0
.end method

.method public static final G(LD80/a$k;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$k;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v4, 0x0

    goto :goto_0

    .line 2
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 3
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v2

    if-ne v0, v2, :cond_2

    const/4 v1, 0x1

    const/4 v4, 0x1

    .line 4
    :goto_0
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object p2

    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    move-result p2

    aget p2, v0, p2

    packed-switch p2, :pswitch_data_0

    .line 5
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    move-result-object p0

    return-object p0

    .line 6
    :pswitch_0
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 7
    sget v6, LlZ0/h;->ic_glyph_shopping_bag:I

    .line 8
    sget p0, Lpb/k;->promo:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 9
    sget p0, Lpb/k;->promo_settings_subtitle:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 10
    new-instance v2, LN80/c$o;

    move-object v5, p3

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1
    move-object v5, p3

    .line 11
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 12
    sget v6, LlZ0/h;->ic_glyph_financial:I

    .line 13
    sget p0, Lpb/k;->finance_bets:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 14
    sget p0, Lpb/k;->menu_finbets_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 15
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_2
    move-object v5, p3

    .line 16
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 17
    sget v6, LlZ0/h;->ic_glyph_betconstructor:I

    .line 18
    sget p0, Lpb/k;->betconstructor:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 19
    sget p0, Lpb/k;->menu_betconstructor_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 20
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_3
    move-object v5, p3

    .line 21
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    if-eqz p4, :cond_3

    .line 22
    sget p0, LlZ0/h;->ic_glyph_info_circle_filled:I

    :goto_1
    move v6, p0

    goto :goto_2

    :cond_3
    sget p0, LlZ0/h;->ic_glyph_info:I

    goto :goto_1

    .line 23
    :goto_2
    sget p0, Lpb/k;->info:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 24
    sget p0, Lpb/k;->menu_info_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 25
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_4
    move-object v5, p3

    .line 26
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 27
    sget v6, LlZ0/h;->ic_glyph_qr_scanning:I

    .line 28
    sget p0, Lpb/k;->scanner:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 29
    sget p0, Lpb/k;->menu_coupon_scanner_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 30
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_5
    move-object v5, p3

    .line 31
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 32
    sget v6, LlZ0/h;->ic_glyph_notification_active:I

    .line 33
    sget p0, Lpb/k;->notifications_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 34
    sget p0, Lpb/k;->menu_notifications_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 35
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_6
    move-object v5, p3

    .line 36
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 37
    sget v6, LlZ0/h;->ic_glyph_favourite_active:I

    .line 38
    sget p0, Lpb/k;->favorites_name:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 39
    sget p0, Lpb/k;->menu_one_x_games_favorites_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 40
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_7
    move-object v5, p3

    .line 41
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 42
    sget v6, LlZ0/h;->ic_glyph_cashback:I

    .line 43
    sget p0, Lpb/k;->cashback:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 44
    sget p0, Lpb/k;->menu_one_x_games_item_cashback_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 45
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_8
    move-object v5, p3

    .line 46
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 47
    sget v6, LlZ0/h;->ic_glyph_promo_games:I

    .line 48
    sget p0, Lpb/k;->bonuses:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 49
    sget p0, Lpb/k;->menu_one_x_games_promo_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 50
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_9
    move-object v5, p3

    .line 51
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 52
    sget v6, LlZ0/h;->ic_glyph_providers:I

    .line 53
    sget p0, Lpb/k;->providers:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 54
    sget p0, Lpb/k;->casino_providers_menu_desription:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 55
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_a
    move-object v5, p3

    .line 56
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 57
    sget v6, LlZ0/h;->ic_glyph_tv_bet:I

    .line 58
    sget p0, Lpb/k;->tv_bet_casino_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 59
    sget p0, Lpb/k;->menu_tvbet_description_item:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 60
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_b
    move-object v5, p3

    .line 61
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 62
    sget v6, LlZ0/h;->ic_glyph_promo_aggregator:I

    .line 63
    sget p0, Lpb/k;->casino_promo_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 64
    sget p0, Lpb/k;->casino_promo_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 65
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_c
    move-object v5, p3

    .line 66
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 67
    sget v6, LlZ0/h;->ic_glyph_tournaments:I

    .line 68
    sget p0, Lpb/k;->casino_tour_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 69
    sget p0, Lpb/k;->casino_tour_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 70
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_d
    move-object v5, p3

    .line 71
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 72
    sget v6, LlZ0/h;->ic_glyph_categories:I

    .line 73
    sget p0, Lpb/k;->casino_category_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 74
    sget p0, Lpb/k;->casino_category_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 75
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_e
    move-object v5, p3

    .line 76
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 77
    sget v6, LlZ0/h;->ic_glyph_virtual:I

    .line 78
    sget p0, Lpb/k;->my_virtual:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 79
    sget p0, Lpb/k;->casino_my_virtual_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 80
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_f
    move-object v5, p3

    .line 81
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 82
    sget v6, LlZ0/h;->ic_glyph_cards:I

    .line 83
    sget p0, Lpb/k;->my_casino:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 84
    sget p0, Lpb/k;->casino_my_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 85
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_10
    move-object v5, p3

    .line 86
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 87
    sget v6, LlZ0/h;->ic_glyph_bets_on_yours:I

    .line 88
    sget p0, Lpb/k;->bets_on_yours:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 89
    sget p0, Lpb/k;->menu_bet_on_favorites_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 90
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_11
    move-object v5, p3

    .line 91
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 92
    sget v6, LlZ0/h;->ic_glyph_results:I

    .line 93
    sget p0, Lpb/k;->results:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 94
    sget p0, Lpb/k;->menu_results_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 95
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_12
    move-object v5, p3

    .line 96
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 97
    sget v6, LlZ0/h;->ic_glyph_stream:I

    .line 98
    sget p0, Lpb/k;->stream_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 99
    sget p0, Lpb/k;->menu_stream_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 100
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_13
    move-object v5, p3

    .line 101
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 102
    sget v6, LlZ0/h;->ic_glyph_express:I

    .line 103
    sget p0, Lpb/k;->day_express:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 104
    sget p0, Lpb/k;->menu_day_express_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 105
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_14
    move-object v5, p3

    .line 106
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 107
    sget v6, LlZ0/h;->ic_glyph_authenticator:I

    .line 108
    sget p0, Lpb/k;->authenticator:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 109
    sget p0, Lpb/k;->authenticator_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 110
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_15
    move-object v5, p3

    .line 111
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 112
    sget v6, LlZ0/h;->ic_glyph_support:I

    .line 113
    sget p0, Lpb/k;->support:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 114
    sget p0, Lpb/k;->menu_support_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 115
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_16
    move-object v5, p3

    .line 116
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 117
    sget v6, LlZ0/h;->ic_glyph_quest:I

    .line 118
    sget p0, Lpb/k;->daily_task_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 119
    sget p0, Lpb/k;->daily_task_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 120
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_17
    move-object v5, p3

    .line 121
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 122
    sget v6, LlZ0/h;->ic_glyph_peak:I

    .line 123
    sget p0, Lpb/k;->live_casino_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 124
    sget p0, Lpb/k;->menu_live_casino_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 125
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_18
    move-object v5, p3

    .line 126
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 127
    sget v6, LlZ0/h;->ic_glyph_slots:I

    .line 128
    sget p0, Lpb/k;->cases_slots:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 129
    sget p0, Lpb/k;->menu_slots_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 130
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_19
    move-object v5, p3

    .line 131
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 132
    sget v6, LlZ0/h;->ic_glyph_cyber:I

    .line 133
    sget p0, Lpb/k;->cyber_sport:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 134
    sget p0, Lpb/k;->menu_cyber_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 135
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1a
    move-object v5, p3

    .line 136
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 137
    sget v6, LlZ0/h;->ic_glyph_line:I

    .line 138
    sget p0, Lpb/k;->line:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 139
    sget p0, Lpb/k;->menu_line_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 140
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1b
    move-object v5, p3

    .line 141
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 142
    sget v6, LlZ0/h;->ic_glyph_live:I

    .line 143
    sget p0, Lpb/k;->live_new:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 144
    sget p0, Lpb/k;->menu_live_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 145
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1c
    move-object v5, p3

    .line 146
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 147
    sget v6, LlZ0/h;->ic_glyph_responsible_gambling:I

    .line 148
    sget p0, Lpb/k;->responsible_game:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 149
    sget p0, Lpb/k;->menu_responsible_gaming_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 150
    new-instance v2, LN80/c$o;

    invoke-direct/range {v2 .. v8}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final H(LD80/a$l;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$l;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v4, Lcom/xbet/onexcore/configs/MenuItemModel;->LIVE:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    const/4 v2, 0x1

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    :cond_0
    const/4 p1, 0x0

    .line 18
    goto :goto_0

    .line 19
    :cond_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->LIVE:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    if-ne v0, v3, :cond_2

    .line 38
    .line 39
    const/4 p1, 0x0

    .line 40
    const/4 v1, 0x1

    .line 41
    :goto_0
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {v0}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 50
    .line 51
    sget-object v0, LDX0/d;->a:LDX0/d;

    .line 52
    .line 53
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    invoke-virtual {v6}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getSportId()J

    .line 58
    .line 59
    .line 60
    move-result-wide v6

    .line 61
    invoke-virtual {v0, v6, v7}, LDX0/d;->c(J)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v6

    .line 65
    new-instance v0, Ln8/a;

    .line 66
    .line 67
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 68
    .line 69
    .line 70
    sget-object v7, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 71
    .line 72
    sget-object v7, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 73
    .line 74
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 75
    .line 76
    .line 77
    move-result-object v8

    .line 78
    invoke-virtual {v8}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 79
    .line 80
    .line 81
    move-result v8

    .line 82
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 83
    .line 84
    .line 85
    move-result-object v8

    .line 86
    new-array v9, v2, [Ljava/lang/Object;

    .line 87
    .line 88
    aput-object v8, v9, p1

    .line 89
    .line 90
    invoke-static {v9, v2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v8

    .line 94
    const-string v9, "static/img/android/TopChamps/%d/backgrounds/Menu_Icon.webp"

    .line 95
    .line 96
    invoke-static {v7, v9, v8}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v8

    .line 100
    invoke-virtual {v0, v8}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    sget v8, LlZ0/d;->uikitSecondary:I

    .line 109
    .line 110
    sget v9, Lpb/g;->sport_new:I

    .line 111
    .line 112
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 113
    .line 114
    .line 115
    move-result-object v10

    .line 116
    invoke-virtual {v10}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getTitle()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v10

    .line 120
    new-instance v11, Ln8/a;

    .line 121
    .line 122
    invoke-direct {v11}, Ln8/a;-><init>()V

    .line 123
    .line 124
    .line 125
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 126
    .line 127
    .line 128
    move-result-object p0

    .line 129
    invoke-virtual {p0}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 130
    .line 131
    .line 132
    move-result p0

    .line 133
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 134
    .line 135
    .line 136
    move-result-object p0

    .line 137
    new-array v12, v2, [Ljava/lang/Object;

    .line 138
    .line 139
    aput-object p0, v12, p1

    .line 140
    .line 141
    invoke-static {v12, v2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 142
    .line 143
    .line 144
    move-result-object p0

    .line 145
    const-string p1, "static/img/android/TopChamps/%d/backgrounds/Menu_Illustration.png"

    .line 146
    .line 147
    invoke-static {v7, p1, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object p0

    .line 151
    invoke-virtual {v11, p0}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 152
    .line 153
    .line 154
    move-result-object p0

    .line 155
    invoke-virtual {p0}, Ln8/a;->a()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v12

    .line 159
    move-object v7, v0

    .line 160
    new-instance v0, LN80/c$t;

    .line 161
    .line 162
    const-string v11, ""

    .line 163
    .line 164
    move-object v2, p2

    .line 165
    invoke-direct/range {v0 .. v12}, LN80/c$t;-><init>(ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILcom/xbet/onexcore/configs/MenuItemModel;ILjava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 166
    .line 167
    .line 168
    return-object v0
.end method

.method public static final I(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZZ)LN80/c;
    .locals 6
    .param p0    # LD80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/main_menu/api/domain/models/MenuSectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a;",
            "LHX0/e;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "ZZZ)",
            "LN80/c;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    instance-of v0, p0, LD80/a$k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p0, LD80/a$k;

    .line 6
    .line 7
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$1;

    .line 8
    .line 9
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-static {p0, p4, p2, p3, p5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->G(LD80/a$k;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0

    .line 17
    :cond_0
    instance-of p5, p0, LD80/a$e;

    .line 18
    .line 19
    if-eqz p5, :cond_1

    .line 20
    .line 21
    check-cast p0, LD80/a$e;

    .line 22
    .line 23
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$2;

    .line 24
    .line 25
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$2;-><init>(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->B(LD80/a$e;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    return-object p0

    .line 33
    :cond_1
    instance-of p5, p0, LD80/a$h;

    .line 34
    .line 35
    if-eqz p5, :cond_2

    .line 36
    .line 37
    check-cast p0, LD80/a$h;

    .line 38
    .line 39
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$3;

    .line 40
    .line 41
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$3;-><init>(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, p4, p2, p3, p6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->D(LD80/a$h;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0

    .line 49
    :cond_2
    instance-of p5, p0, LD80/a$i;

    .line 50
    .line 51
    if-eqz p5, :cond_3

    .line 52
    .line 53
    check-cast p0, LD80/a$i;

    .line 54
    .line 55
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$4;

    .line 56
    .line 57
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$4;-><init>(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    invoke-static {p0, p4, p2, p3, p6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->E(LD80/a$i;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    return-object p0

    .line 65
    :cond_3
    instance-of p5, p0, LD80/a$m;

    .line 66
    .line 67
    if-eqz p5, :cond_4

    .line 68
    .line 69
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$5;

    .line 70
    .line 71
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$5;-><init>(Ljava/lang/Object;)V

    .line 72
    .line 73
    .line 74
    invoke-static {p0, p2, p3, p6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->d(Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    return-object p0

    .line 79
    :cond_4
    instance-of p5, p0, LD80/a$g;

    .line 80
    .line 81
    if-eqz p5, :cond_5

    .line 82
    .line 83
    check-cast p0, LD80/a$g;

    .line 84
    .line 85
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$6;

    .line 86
    .line 87
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$6;-><init>(Ljava/lang/Object;)V

    .line 88
    .line 89
    .line 90
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->C(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 91
    .line 92
    .line 93
    move-result-object p0

    .line 94
    return-object p0

    .line 95
    :cond_5
    instance-of p5, p0, LD80/a$l;

    .line 96
    .line 97
    if-eqz p5, :cond_6

    .line 98
    .line 99
    check-cast p0, LD80/a$l;

    .line 100
    .line 101
    invoke-static {p0, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->H(LD80/a$l;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 102
    .line 103
    .line 104
    move-result-object p0

    .line 105
    return-object p0

    .line 106
    :cond_6
    instance-of p5, p0, LD80/a$c;

    .line 107
    .line 108
    if-eqz p5, :cond_7

    .line 109
    .line 110
    check-cast p0, LD80/a$c;

    .line 111
    .line 112
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$7;

    .line 113
    .line 114
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$7;-><init>(Ljava/lang/Object;)V

    .line 115
    .line 116
    .line 117
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->z(LD80/a$c;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 118
    .line 119
    .line 120
    move-result-object p0

    .line 121
    return-object p0

    .line 122
    :cond_7
    instance-of p5, p0, LD80/a$d;

    .line 123
    .line 124
    if-eqz p5, :cond_8

    .line 125
    .line 126
    check-cast p0, LD80/a$d;

    .line 127
    .line 128
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$8;

    .line 129
    .line 130
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$8;-><init>(Ljava/lang/Object;)V

    .line 131
    .line 132
    .line 133
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->A(LD80/a$d;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 134
    .line 135
    .line 136
    move-result-object p0

    .line 137
    return-object p0

    .line 138
    :cond_8
    instance-of p5, p0, LD80/a$j;

    .line 139
    .line 140
    if-eqz p5, :cond_9

    .line 141
    .line 142
    check-cast p0, LD80/a$j;

    .line 143
    .line 144
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$9;

    .line 145
    .line 146
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$9;-><init>(Ljava/lang/Object;)V

    .line 147
    .line 148
    .line 149
    invoke-static {p0, p4, p2, p3, p6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->F(LD80/a$j;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 150
    .line 151
    .line 152
    move-result-object p0

    .line 153
    return-object p0

    .line 154
    :cond_9
    instance-of p5, p0, LD80/a$b;

    .line 155
    .line 156
    if-eqz p5, :cond_a

    .line 157
    .line 158
    check-cast p0, LD80/a$b;

    .line 159
    .line 160
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$10;

    .line 161
    .line 162
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$10;-><init>(Ljava/lang/Object;)V

    .line 163
    .line 164
    .line 165
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->y(LD80/a$b;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 166
    .line 167
    .line 168
    move-result-object p0

    .line 169
    return-object p0

    .line 170
    :cond_a
    instance-of p5, p0, LD80/a$a;

    .line 171
    .line 172
    if-eqz p5, :cond_b

    .line 173
    .line 174
    move-object v0, p0

    .line 175
    check-cast v0, LD80/a$a;

    .line 176
    .line 177
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$11;

    .line 178
    .line 179
    invoke-direct {v1, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$11;-><init>(Ljava/lang/Object;)V

    .line 180
    .line 181
    .line 182
    move-object v2, p2

    .line 183
    move-object v3, p3

    .line 184
    move v4, p4

    .line 185
    move v5, p6

    .line 186
    invoke-static/range {v0 .. v5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->x(LD80/a$a;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;

    .line 187
    .line 188
    .line 189
    move-result-object p0

    .line 190
    return-object p0

    .line 191
    :cond_b
    move-object v2, p2

    .line 192
    move-object v3, p3

    .line 193
    move v5, p6

    .line 194
    instance-of p2, p0, LD80/a$f;

    .line 195
    .line 196
    if-eqz p2, :cond_c

    .line 197
    .line 198
    check-cast p0, LD80/a$f;

    .line 199
    .line 200
    new-instance p2, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$12;

    .line 201
    .line 202
    invoke-direct {p2, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toSingleMenuUiItem$12;-><init>(Ljava/lang/Object;)V

    .line 203
    .line 204
    .line 205
    invoke-static {p0, p2, v2, v3, v5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->c(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 206
    .line 207
    .line 208
    move-result-object p0

    .line 209
    return-object p0

    .line 210
    :cond_c
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    .line 211
    .line 212
    .line 213
    move-result-object p0

    .line 214
    return-object p0
.end method

.method public static final J(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 7
    .param p0    # LD80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/main_menu/api/domain/models/MenuSectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a;",
            "LHX0/e;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p3}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    const/4 v4, 0x1

    .line 19
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 20
    .line 21
    .line 22
    move-result v6

    .line 23
    move-object v0, p0

    .line 24
    move-object v1, p1

    .line 25
    move-object v2, p2

    .line 26
    move-object v3, p3

    .line 27
    move v5, p4

    .line 28
    invoke-static/range {v0 .. v6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->I(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZZ)LN80/c;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    return-object p0

    .line 33
    :pswitch_1
    move-object v0, p0

    .line 34
    move-object v1, p1

    .line 35
    move-object v2, p2

    .line 36
    move-object v3, p3

    .line 37
    move v5, p4

    .line 38
    instance-of p0, v0, LD80/a$m;

    .line 39
    .line 40
    if-eqz p0, :cond_0

    .line 41
    .line 42
    move-object p0, v0

    .line 43
    check-cast p0, LD80/a$m;

    .line 44
    .line 45
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toTabbedGridMenuUiItem$1;

    .line 46
    .line 47
    invoke-direct {p1, v1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toTabbedGridMenuUiItem$1;-><init>(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    invoke-static {p0, p1, v2, v3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->j(LD80/a$m;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    return-object p0

    .line 55
    :cond_0
    instance-of p0, v0, LD80/a$f;

    .line 56
    .line 57
    if-eqz p0, :cond_1

    .line 58
    .line 59
    move-object p0, v0

    .line 60
    check-cast p0, LD80/a$f;

    .line 61
    .line 62
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toTabbedGridMenuUiItem$2;

    .line 63
    .line 64
    invoke-direct {p1, v1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toTabbedGridMenuUiItem$2;-><init>(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    invoke-static {p0, p1, v2, v3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->i(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    return-object p0

    .line 72
    :cond_1
    const/4 v4, 0x1

    .line 73
    invoke-static/range {v0 .. v5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->v(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;

    .line 74
    .line 75
    .line 76
    move-result-object p0

    .line 77
    return-object p0

    .line 78
    nop

    .line 79
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public static final a(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$f;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v1, Lcom/xbet/onexcore/configs/MenuItemModel;->CYBER_SPORT:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_2

    .line 26
    .line 27
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 32
    .line 33
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->CYBER_SPORT:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    if-ne v0, v3, :cond_1

    .line 36
    .line 37
    const/4 v2, 0x1

    .line 38
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_cyber:I

    .line 39
    .line 40
    sget p2, Lpb/k;->cyber_sport:I

    .line 41
    .line 42
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    move-object v5, p2

    .line 51
    check-cast v5, Ljava/lang/String;

    .line 52
    .line 53
    sget p2, Lpb/k;->menu_cyber_description:I

    .line 54
    .line 55
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    move-object v6, p1

    .line 64
    check-cast v6, Ljava/lang/String;

    .line 65
    .line 66
    if-nez p4, :cond_3

    .line 67
    .line 68
    invoke-virtual {p0}, LD80/a$f;->a()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    :goto_1
    move-object v7, p0

    .line 73
    goto :goto_2

    .line 74
    :cond_3
    const-string p0, ""

    .line 75
    .line 76
    goto :goto_1

    .line 77
    :goto_2
    new-instance v0, LN80/c$d;

    .line 78
    .line 79
    move-object v3, p3

    .line 80
    invoke-direct/range {v0 .. v7}, LN80/c$d;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 81
    .line 82
    .line 83
    return-object v0
.end method

.method public static final b()LN80/c$r;
    .locals 7

    .line 1
    sget-object v1, Lcom/xbet/onexcore/configs/MenuItemModel;->ERROR:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    sget-object v3, Lorg/xbet/main_menu/api/domain/models/MenuSectionType;->SPORTS:Lorg/xbet/main_menu/api/domain/models/MenuSectionType;

    .line 4
    .line 5
    new-instance v0, LN80/c$r;

    .line 6
    .line 7
    const-string v5, ""

    .line 8
    .line 9
    const-string v6, ""

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    invoke-direct/range {v0 .. v6}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public static final c(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$f;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v1, Lcom/xbet/onexcore/configs/MenuItemModel;->CYBER_SPORT:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_2

    .line 26
    .line 27
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 32
    .line 33
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->CYBER_SPORT:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    if-ne v0, v3, :cond_1

    .line 36
    .line 37
    const/4 v2, 0x1

    .line 38
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_cyber:I

    .line 39
    .line 40
    sget p2, Lpb/k;->cyber_sport:I

    .line 41
    .line 42
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    move-object v5, p2

    .line 51
    check-cast v5, Ljava/lang/String;

    .line 52
    .line 53
    sget p2, Lpb/k;->menu_cyber_description:I

    .line 54
    .line 55
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    move-object v6, p1

    .line 64
    check-cast v6, Ljava/lang/String;

    .line 65
    .line 66
    if-nez p4, :cond_3

    .line 67
    .line 68
    invoke-virtual {p0}, LD80/a$f;->a()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    :goto_1
    move-object v7, p0

    .line 73
    goto :goto_2

    .line 74
    :cond_3
    const-string p0, ""

    .line 75
    .line 76
    goto :goto_1

    .line 77
    :goto_2
    new-instance v0, LN80/c$d;

    .line 78
    .line 79
    move-object v3, p3

    .line 80
    invoke-direct/range {v0 .. v7}, LN80/c$d;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 81
    .line 82
    .line 83
    return-object v0
.end method

.method public static final d(Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v1, Lcom/xbet/onexcore/configs/MenuItemModel;->SWIPEX:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v0, :cond_1

    .line 9
    .line 10
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    goto :goto_0

    .line 18
    :cond_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_0

    .line 27
    .line 28
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 33
    .line 34
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->SWIPEX:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    if-ne v0, v3, :cond_2

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_swipex:I

    .line 40
    .line 41
    sget v0, Lpb/k;->swipex:I

    .line 42
    .line 43
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    move-object v5, v0

    .line 52
    check-cast v5, Ljava/lang/String;

    .line 53
    .line 54
    sget v0, Lpb/k;->bet_with_one_swipe:I

    .line 55
    .line 56
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    move-object v6, p0

    .line 65
    check-cast v6, Ljava/lang/String;

    .line 66
    .line 67
    if-nez p3, :cond_3

    .line 68
    .line 69
    sget v2, Lpb/g;->swipex_banner:I

    .line 70
    .line 71
    move v7, v2

    .line 72
    goto :goto_1

    .line 73
    :cond_3
    const/4 v7, 0x0

    .line 74
    :goto_1
    new-instance v0, LN80/c$n;

    .line 75
    .line 76
    move v2, p1

    .line 77
    move-object v3, p2

    .line 78
    invoke-direct/range {v0 .. v7}, LN80/c$n;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;I)V

    .line 79
    .line 80
    .line 81
    return-object v0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v1, Lcom/xbet/onexcore/configs/MenuItemModel;->SWIPEX:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v0, :cond_1

    .line 9
    .line 10
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    goto :goto_0

    .line 18
    :cond_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_0

    .line 27
    .line 28
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 33
    .line 34
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->SWIPEX:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    if-ne v0, v3, :cond_2

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_swipex:I

    .line 40
    .line 41
    sget v0, Lpb/k;->swipex:I

    .line 42
    .line 43
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    move-object v5, v0

    .line 52
    check-cast v5, Ljava/lang/String;

    .line 53
    .line 54
    sget v0, Lpb/k;->bet_with_one_swipe:I

    .line 55
    .line 56
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    move-object v6, p0

    .line 65
    check-cast v6, Ljava/lang/String;

    .line 66
    .line 67
    if-nez p3, :cond_3

    .line 68
    .line 69
    sget v2, Lpb/g;->swipex_banner:I

    .line 70
    .line 71
    move v7, v2

    .line 72
    goto :goto_1

    .line 73
    :cond_3
    const/4 v7, 0x0

    .line 74
    :goto_1
    new-instance v0, LN80/c$i;

    .line 75
    .line 76
    move v2, p1

    .line 77
    move-object v3, p2

    .line 78
    invoke-direct/range {v0 .. v7}, LN80/c$i;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;I)V

    .line 79
    .line 80
    .line 81
    return-object v0
.end method

.method public static final f(LHX0/e;)Z
    .locals 1

    .line 1
    invoke-interface {p0}, LHX0/e;->d()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    const/16 v0, 0x17c

    .line 6
    .line 7
    if-ge p0, v0, :cond_0

    .line 8
    .line 9
    const/4 p0, 0x1

    .line 10
    return p0

    .line 11
    :cond_0
    const/4 p0, 0x0

    .line 12
    return p0
.end method

.method public static final g(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;
    .locals 7
    .param p0    # LD80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/main_menu/api/domain/models/MenuSectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a;",
            "LHX0/e;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "ZZ)",
            "LN80/c;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p3}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    packed-switch v0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 19
    .line 20
    .line 21
    move-result v6

    .line 22
    move-object v0, p0

    .line 23
    move-object v1, p1

    .line 24
    move-object v2, p2

    .line 25
    move-object v3, p3

    .line 26
    move v4, p4

    .line 27
    move v5, p5

    .line 28
    invoke-static/range {v0 .. v6}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->I(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZZ)LN80/c;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    return-object p0

    .line 33
    :pswitch_1
    move-object v0, p0

    .line 34
    move-object v1, p1

    .line 35
    move-object v2, p2

    .line 36
    move-object v3, p3

    .line 37
    move v4, p4

    .line 38
    move v5, p5

    .line 39
    instance-of p0, v0, LD80/a$g;

    .line 40
    .line 41
    if-eqz p0, :cond_0

    .line 42
    .line 43
    move-object p0, v0

    .line 44
    check-cast p0, LD80/a$g;

    .line 45
    .line 46
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toAccordionMenuUiItem$1;

    .line 47
    .line 48
    invoke-direct {p1, v1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toAccordionMenuUiItem$1;-><init>(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    invoke-static {p0, p1, v2, v3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->w(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 52
    .line 53
    .line 54
    move-result-object p0

    .line 55
    return-object p0

    .line 56
    :cond_0
    invoke-static/range {v0 .. v5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->v(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    return-object p0

    .line 61
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_1
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public static final h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;Lkotlin/jvm/functions/Function1;)Ln41/m;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;)",
            "Ln41/m;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->c(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    new-instance p1, Lkotlin/Triple;

    .line 12
    .line 13
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->g()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;->TechnicalWorks:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;->Picture:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 23
    .line 24
    :goto_0
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->e()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-static {v1}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-static {v1}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->c()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    invoke-direct {p1, v0, v1, v2}, Lkotlin/Triple;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    new-instance v0, Lkotlin/Triple;

    .line 45
    .line 46
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;->Action:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 47
    .line 48
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->d()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    invoke-static {v2}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v2

    .line 56
    invoke-static {v2}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    sget v3, Lpb/k;->all_games:I

    .line 61
    .line 62
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v3

    .line 66
    invoke-interface {p1, v3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-direct {v0, v1, v2, p1}, Lkotlin/Triple;-><init>(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    move-object p1, v0

    .line 74
    :goto_1
    invoke-virtual {p1}, Lkotlin/Triple;->component1()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    move-object v4, v0

    .line 79
    check-cast v4, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 80
    .line 81
    invoke-virtual {p1}, Lkotlin/Triple;->component2()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    check-cast v0, LL11/c$d;

    .line 86
    .line 87
    invoke-virtual {v0}, LL11/c$d;->i()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    invoke-virtual {p1}, Lkotlin/Triple;->component3()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    move-object v6, p1

    .line 96
    check-cast v6, Ljava/lang/String;

    .line 97
    .line 98
    new-instance v1, Ln41/m;

    .line 99
    .line 100
    invoke-virtual {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 101
    .line 102
    .line 103
    move-result-object p0

    .line 104
    invoke-static {p0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 105
    .line 106
    .line 107
    move-result-wide v2

    .line 108
    invoke-static {v0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 109
    .line 110
    .line 111
    move-result-object v5

    .line 112
    const/16 v9, 0x30

    .line 113
    .line 114
    const/4 v10, 0x0

    .line 115
    const/4 v7, 0x0

    .line 116
    const/4 v8, 0x0

    .line 117
    invoke-direct/range {v1 .. v10}, Ln41/m;-><init>(JLorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;LL11/c;Ljava/lang/String;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 118
    .line 119
    .line 120
    return-object v1
.end method

.method public static final i(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$f;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$f;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    sget v4, LlZ0/h;->ic_glyph_cyber:I

    .line 6
    .line 7
    sget v0, Lpb/k;->cyber_sport:I

    .line 8
    .line 9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    move-object v5, v0

    .line 18
    check-cast v5, Ljava/lang/String;

    .line 19
    .line 20
    sget v0, Lpb/k;->menu_cyber_description:I

    .line 21
    .line 22
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    move-object v6, p1

    .line 31
    check-cast v6, Ljava/lang/String;

    .line 32
    .line 33
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    const/4 v0, 0x0

    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 41
    .line 42
    .line 43
    move-result p1

    .line 44
    if-eqz p1, :cond_1

    .line 45
    .line 46
    :cond_0
    const/4 v2, 0x0

    .line 47
    goto :goto_0

    .line 48
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 53
    .line 54
    .line 55
    move-result p2

    .line 56
    if-eqz p2, :cond_0

    .line 57
    .line 58
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object p2

    .line 62
    check-cast p2, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 63
    .line 64
    invoke-virtual {p0}, LD80/a$f;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    if-ne p2, v2, :cond_2

    .line 69
    .line 70
    const/4 v0, 0x1

    .line 71
    const/4 v2, 0x1

    .line 72
    :goto_0
    new-instance v0, LN80/c$r;

    .line 73
    .line 74
    move-object v3, p3

    .line 75
    invoke-direct/range {v0 .. v6}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 76
    .line 77
    .line 78
    return-object v0
.end method

.method public static final j(LD80/a$m;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$m;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$m;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    sget v4, LlZ0/h;->ic_glyph_swipex:I

    .line 6
    .line 7
    sget v0, Lpb/k;->swipex:I

    .line 8
    .line 9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    move-object v5, v0

    .line 18
    check-cast v5, Ljava/lang/String;

    .line 19
    .line 20
    sget v0, Lpb/k;->bet_with_one_swipe:I

    .line 21
    .line 22
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    move-object v6, p1

    .line 31
    check-cast v6, Ljava/lang/String;

    .line 32
    .line 33
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    const/4 v0, 0x0

    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 41
    .line 42
    .line 43
    move-result p1

    .line 44
    if-eqz p1, :cond_1

    .line 45
    .line 46
    :cond_0
    const/4 v2, 0x0

    .line 47
    goto :goto_0

    .line 48
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 53
    .line 54
    .line 55
    move-result p2

    .line 56
    if-eqz p2, :cond_0

    .line 57
    .line 58
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object p2

    .line 62
    check-cast p2, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 63
    .line 64
    invoke-virtual {p0}, LD80/a$m;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    if-ne p2, v2, :cond_2

    .line 69
    .line 70
    const/4 v0, 0x1

    .line 71
    const/4 v2, 0x1

    .line 72
    :goto_0
    new-instance v0, LN80/c$r;

    .line 73
    .line 74
    move-object v3, p3

    .line 75
    invoke-direct/range {v0 .. v6}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 76
    .line 77
    .line 78
    return-object v0
.end method

.method public static final k(LD80/a$a;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$a;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "ZZ)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$a;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    :cond_0
    const/4 p0, 0x0

    .line 19
    goto :goto_0

    .line 20
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    invoke-virtual {p0}, LD80/a$a;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    if-ne v0, v3, :cond_2

    .line 41
    .line 42
    const/4 p0, 0x1

    .line 43
    :goto_0
    sget p2, Lpb/k;->my_virtual:I

    .line 44
    .line 45
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p2

    .line 53
    move-object v7, p2

    .line 54
    check-cast v7, Ljava/lang/String;

    .line 55
    .line 56
    if-eqz p4, :cond_3

    .line 57
    .line 58
    sget p2, Lpb/k;->casino_my_virtual_description:I

    .line 59
    .line 60
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, Ljava/lang/String;

    .line 69
    .line 70
    :goto_1
    move-object v8, p1

    .line 71
    goto :goto_2

    .line 72
    :cond_3
    const-string p1, ""

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :goto_2
    if-nez p5, :cond_4

    .line 76
    .line 77
    sget v2, Lpb/g;->my_aggregator_banner:I

    .line 78
    .line 79
    move v9, v2

    .line 80
    goto :goto_3

    .line 81
    :cond_4
    const/4 v9, 0x0

    .line 82
    :goto_3
    sget v5, LlZ0/d;->uiKitAggregatorLavender:I

    .line 83
    .line 84
    sget v4, LlZ0/h;->ic_glyph_virtual:I

    .line 85
    .line 86
    sget v6, LlZ0/d;->uikitStaticWhite:I

    .line 87
    .line 88
    new-instance v0, LN80/c$g;

    .line 89
    .line 90
    move v10, v5

    .line 91
    move v2, p0

    .line 92
    move-object v3, p3

    .line 93
    invoke-direct/range {v0 .. v10}, LN80/c$g;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIILjava/lang/String;Ljava/lang/String;II)V

    .line 94
    .line 95
    .line 96
    return-object v0
.end method

.method public static final l(LD80/a$b;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 19
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$b;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    aget v1, v2, v1

    .line 14
    .line 15
    const/16 v2, 0x27

    .line 16
    .line 17
    const/4 v3, 0x1

    .line 18
    const/4 v4, 0x0

    .line 19
    if-ne v1, v2, :cond_3

    .line 20
    .line 21
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    if-eqz v1, :cond_1

    .line 36
    .line 37
    :cond_0
    const/4 v7, 0x0

    .line 38
    goto :goto_0

    .line 39
    :cond_1
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    :cond_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_0

    .line 48
    .line 49
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    check-cast v2, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 54
    .line 55
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 56
    .line 57
    .line 58
    move-result-object v5

    .line 59
    if-ne v2, v5, :cond_2

    .line 60
    .line 61
    const/4 v7, 0x1

    .line 62
    :goto_0
    sget v9, LlZ0/h;->ic_glyph_fast_bet:I

    .line 63
    .line 64
    sget v1, Lpb/k;->fast_bet_title:I

    .line 65
    .line 66
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-interface {v0, v1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    move-object v10, v1

    .line 75
    check-cast v10, Ljava/lang/String;

    .line 76
    .line 77
    sget v1, Lpb/k;->fast_bet_description:I

    .line 78
    .line 79
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-interface {v0, v1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    move-object v11, v0

    .line 88
    check-cast v11, Ljava/lang/String;

    .line 89
    .line 90
    new-instance v5, LN80/c$r;

    .line 91
    .line 92
    move-object/from16 v8, p3

    .line 93
    .line 94
    invoke-direct/range {v5 .. v11}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    return-object v5

    .line 98
    :cond_3
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    invoke-virtual {v0}, Lg81/b;->h()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 107
    .line 108
    .line 109
    move-result v0

    .line 110
    if-lez v0, :cond_7

    .line 111
    .line 112
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 113
    .line 114
    .line 115
    move-result-object v8

    .line 116
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    move-result v0

    .line 120
    if-eqz v0, :cond_5

    .line 121
    .line 122
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 123
    .line 124
    .line 125
    move-result v0

    .line 126
    if-eqz v0, :cond_5

    .line 127
    .line 128
    :cond_4
    const/4 v7, 0x0

    .line 129
    goto :goto_1

    .line 130
    :cond_5
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    :cond_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 135
    .line 136
    .line 137
    move-result v1

    .line 138
    if-eqz v1, :cond_4

    .line 139
    .line 140
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    check-cast v1, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 145
    .line 146
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 147
    .line 148
    .line 149
    move-result-object v2

    .line 150
    if-ne v1, v2, :cond_6

    .line 151
    .line 152
    const/4 v7, 0x1

    .line 153
    :goto_1
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    invoke-virtual {v0}, Lg81/b;->g()J

    .line 158
    .line 159
    .line 160
    move-result-wide v10

    .line 161
    new-instance v0, Ln8/a;

    .line 162
    .line 163
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 164
    .line 165
    .line 166
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    invoke-virtual {v1}, Lg81/b;->h()Ljava/lang/String;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    invoke-virtual {v0, v1}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 175
    .line 176
    .line 177
    move-result-object v0

    .line 178
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object v14

    .line 182
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 183
    .line 184
    .line 185
    move-result-object v0

    .line 186
    invoke-virtual {v0}, Lg81/b;->n()Ljava/lang/String;

    .line 187
    .line 188
    .line 189
    move-result-object v15

    .line 190
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    invoke-virtual {v0}, Lg81/b;->c()Ljava/lang/String;

    .line 195
    .line 196
    .line 197
    move-result-object v16

    .line 198
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 199
    .line 200
    .line 201
    move-result-object v0

    .line 202
    invoke-virtual {v0}, Lg81/b;->l()J

    .line 203
    .line 204
    .line 205
    move-result-wide v17

    .line 206
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-virtual {v0}, Lg81/b;->e()J

    .line 211
    .line 212
    .line 213
    move-result-wide v12

    .line 214
    new-instance v6, LN80/c$v;

    .line 215
    .line 216
    move-object/from16 v9, p3

    .line 217
    .line 218
    invoke-direct/range {v6 .. v18}, LN80/c$v;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;JJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V

    .line 219
    .line 220
    .line 221
    return-object v6

    .line 222
    :cond_7
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 223
    .line 224
    .line 225
    move-result-object v8

    .line 226
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 227
    .line 228
    .line 229
    move-result v0

    .line 230
    if-eqz v0, :cond_9

    .line 231
    .line 232
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 233
    .line 234
    .line 235
    move-result v0

    .line 236
    if-eqz v0, :cond_9

    .line 237
    .line 238
    :cond_8
    const/4 v7, 0x0

    .line 239
    goto :goto_2

    .line 240
    :cond_9
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 241
    .line 242
    .line 243
    move-result-object v0

    .line 244
    :cond_a
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 245
    .line 246
    .line 247
    move-result v1

    .line 248
    if-eqz v1, :cond_8

    .line 249
    .line 250
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 251
    .line 252
    .line 253
    move-result-object v1

    .line 254
    check-cast v1, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 255
    .line 256
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 257
    .line 258
    .line 259
    move-result-object v2

    .line 260
    if-ne v1, v2, :cond_a

    .line 261
    .line 262
    const/4 v7, 0x1

    .line 263
    :goto_2
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 264
    .line 265
    .line 266
    move-result-object v0

    .line 267
    invoke-virtual {v0}, Lg81/b;->g()J

    .line 268
    .line 269
    .line 270
    move-result-wide v10

    .line 271
    new-instance v0, Ln8/a;

    .line 272
    .line 273
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 274
    .line 275
    .line 276
    sget-object v1, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 277
    .line 278
    sget-object v1, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 279
    .line 280
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 281
    .line 282
    .line 283
    move-result-object v2

    .line 284
    invoke-virtual {v2}, Lg81/b;->g()J

    .line 285
    .line 286
    .line 287
    move-result-wide v5

    .line 288
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 289
    .line 290
    .line 291
    move-result-object v2

    .line 292
    new-array v5, v3, [Ljava/lang/Object;

    .line 293
    .line 294
    aput-object v2, v5, v4

    .line 295
    .line 296
    invoke-static {v5, v3}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 297
    .line 298
    .line 299
    move-result-object v2

    .line 300
    const-string v3, "static/img/android/agregator/category/%d.svg"

    .line 301
    .line 302
    invoke-static {v1, v3, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 303
    .line 304
    .line 305
    move-result-object v1

    .line 306
    invoke-virtual {v0, v1}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 307
    .line 308
    .line 309
    move-result-object v0

    .line 310
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 311
    .line 312
    .line 313
    move-result-object v14

    .line 314
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 315
    .line 316
    .line 317
    move-result-object v0

    .line 318
    invoke-virtual {v0}, Lg81/b;->n()Ljava/lang/String;

    .line 319
    .line 320
    .line 321
    move-result-object v15

    .line 322
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 323
    .line 324
    .line 325
    move-result-object v0

    .line 326
    invoke-virtual {v0}, Lg81/b;->l()J

    .line 327
    .line 328
    .line 329
    move-result-wide v16

    .line 330
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 331
    .line 332
    .line 333
    move-result-object v0

    .line 334
    invoke-virtual {v0}, Lg81/b;->e()J

    .line 335
    .line 336
    .line 337
    move-result-wide v12

    .line 338
    new-instance v6, LN80/c$w;

    .line 339
    .line 340
    move-object/from16 v9, p3

    .line 341
    .line 342
    invoke-direct/range {v6 .. v17}, LN80/c$w;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;JJLjava/lang/String;Ljava/lang/String;J)V

    .line 343
    .line 344
    .line 345
    return-object v6
.end method

.method public static final m(LD80/a$c;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$c;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$c;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    invoke-virtual {p0}, LD80/a$c;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-ne v0, v3, :cond_1

    .line 40
    .line 41
    const/4 v2, 0x1

    .line 42
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_authenticator:I

    .line 43
    .line 44
    sget p2, Lpb/k;->authenticator:I

    .line 45
    .line 46
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    move-object v5, p2

    .line 55
    check-cast v5, Ljava/lang/String;

    .line 56
    .line 57
    invoke-virtual {p0}, LD80/a$c;->a()Z

    .line 58
    .line 59
    .line 60
    move-result p2

    .line 61
    if-eqz p2, :cond_3

    .line 62
    .line 63
    sget p2, Lpb/k;->account_secured:I

    .line 64
    .line 65
    goto :goto_1

    .line 66
    :cond_3
    sget p2, Lpb/k;->authenticator_description:I

    .line 67
    .line 68
    :goto_1
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 69
    .line 70
    .line 71
    move-result-object p2

    .line 72
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    move-object v6, p1

    .line 77
    check-cast v6, Ljava/lang/String;

    .line 78
    .line 79
    sget v7, LlZ0/h;->ic_status_green_check_circle:I

    .line 80
    .line 81
    invoke-virtual {p0}, LD80/a$c;->a()Z

    .line 82
    .line 83
    .line 84
    move-result v8

    .line 85
    new-instance v0, LN80/c$a;

    .line 86
    .line 87
    move-object v3, p3

    .line 88
    invoke-direct/range {v0 .. v8}, LN80/c$a;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;IZ)V

    .line 89
    .line 90
    .line 91
    return-object v0
.end method

.method public static final n(LD80/a$d;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$d;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$d;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v2

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    const/4 v3, 0x0

    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    :cond_0
    const/4 p2, 0x1

    .line 20
    const/4 v1, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    if-eqz v0, :cond_0

    .line 31
    .line 32
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    invoke-virtual {p0}, LD80/a$d;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    if-ne v0, v4, :cond_2

    .line 43
    .line 44
    const/4 p2, 0x1

    .line 45
    :goto_0
    new-instance v0, Ln8/a;

    .line 46
    .line 47
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 48
    .line 49
    .line 50
    sget-object v4, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 51
    .line 52
    sget-object v4, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 53
    .line 54
    invoke-virtual {p0}, LD80/a$d;->a()J

    .line 55
    .line 56
    .line 57
    move-result-wide v5

    .line 58
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    new-array v5, p2, [Ljava/lang/Object;

    .line 63
    .line 64
    aput-object p0, v5, v3

    .line 65
    .line 66
    invoke-static {v5, p2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    const-string p2, "static/img/android/icons_currency/v2/%d.svg"

    .line 71
    .line 72
    invoke-static {v4, p2, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-virtual {v0, p0}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    invoke-virtual {p0}, Ln8/a;->a()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v4

    .line 84
    sget v5, LlZ0/h;->ic_glyph_currency_placeholder:I

    .line 85
    .line 86
    sget p0, Lpb/k;->balance_management_title:I

    .line 87
    .line 88
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p0

    .line 96
    move-object v6, p0

    .line 97
    check-cast v6, Ljava/lang/String;

    .line 98
    .line 99
    sget p0, Lpb/k;->balance_managment_description:I

    .line 100
    .line 101
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 102
    .line 103
    .line 104
    move-result-object p0

    .line 105
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object p0

    .line 109
    move-object v7, p0

    .line 110
    check-cast v7, Ljava/lang/String;

    .line 111
    .line 112
    new-instance v0, LN80/c$b;

    .line 113
    .line 114
    move-object v3, p3

    .line 115
    invoke-direct/range {v0 .. v7}, LN80/c$b;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 116
    .line 117
    .line 118
    return-object v0
.end method

.method public static final o(LD80/a$e;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$e;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_1

    .line 7
    .line 8
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    :cond_0
    const/4 v4, 0x0

    .line 15
    goto :goto_0

    .line 16
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 31
    .line 32
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    if-ne v0, v2, :cond_2

    .line 37
    .line 38
    const/4 v1, 0x1

    .line 39
    const/4 v4, 0x1

    .line 40
    :goto_0
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 41
    .line 42
    .line 43
    move-result-object p2

    .line 44
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    .line 45
    .line 46
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    aget p2, v0, p2

    .line 51
    .line 52
    const/16 v0, 0x25

    .line 53
    .line 54
    if-eq p2, v0, :cond_4

    .line 55
    .line 56
    const/16 v0, 0x26

    .line 57
    .line 58
    if-eq p2, v0, :cond_3

    .line 59
    .line 60
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    return-object p0

    .line 65
    :cond_3
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    sget v6, LlZ0/h;->ic_glyph_toto:I

    .line 70
    .line 71
    invoke-virtual {p0}, LD80/a$e;->b()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v7

    .line 75
    sget p0, Lpb/k;->menu_toto_description:I

    .line 76
    .line 77
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 78
    .line 79
    .line 80
    move-result-object p0

    .line 81
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object p0

    .line 85
    move-object v8, p0

    .line 86
    check-cast v8, Ljava/lang/String;

    .line 87
    .line 88
    new-instance v2, LN80/c$r;

    .line 89
    .line 90
    move-object v5, p3

    .line 91
    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    return-object v2

    .line 95
    :cond_4
    move-object v5, p3

    .line 96
    invoke-virtual {p0}, LD80/a$e;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 97
    .line 98
    .line 99
    move-result-object v3

    .line 100
    sget v6, LlZ0/h;->ic_glyph_games:I

    .line 101
    .line 102
    invoke-virtual {p0}, LD80/a$e;->b()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v7

    .line 106
    sget p0, Lpb/k;->menu_one_x_games_description:I

    .line 107
    .line 108
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 109
    .line 110
    .line 111
    move-result-object p0

    .line 112
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object p0

    .line 116
    move-object v8, p0

    .line 117
    check-cast v8, Ljava/lang/String;

    .line 118
    .line 119
    new-instance v2, LN80/c$r;

    .line 120
    .line 121
    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 122
    .line 123
    .line 124
    return-object v2
.end method

.method public static final p(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$g;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v2

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-ne v0, v3, :cond_1

    .line 40
    .line 41
    const/4 v1, 0x1

    .line 42
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_games:I

    .line 43
    .line 44
    sget p2, Lpb/k;->all_games:I

    .line 45
    .line 46
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    move-object v5, p2

    .line 55
    check-cast v5, Ljava/lang/String;

    .line 56
    .line 57
    sget p2, Lpb/k;->menu_one_x_games_description:I

    .line 58
    .line 59
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object p2

    .line 63
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    move-object v6, p2

    .line 68
    check-cast v6, Ljava/lang/String;

    .line 69
    .line 70
    invoke-virtual {p0}, LD80/a$g;->a()Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    new-instance v7, Ljava/util/ArrayList;

    .line 75
    .line 76
    const/16 p2, 0xa

    .line 77
    .line 78
    invoke-static {p0, p2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 79
    .line 80
    .line 81
    move-result p2

    .line 82
    invoke-direct {v7, p2}, Ljava/util/ArrayList;-><init>(I)V

    .line 83
    .line 84
    .line 85
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 86
    .line 87
    .line 88
    move-result-object p0

    .line 89
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 90
    .line 91
    .line 92
    move-result p2

    .line 93
    if-eqz p2, :cond_3

    .line 94
    .line 95
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object p2

    .line 99
    check-cast p2, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 100
    .line 101
    invoke-static {p2, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;Lkotlin/jvm/functions/Function1;)Ln41/m;

    .line 102
    .line 103
    .line 104
    move-result-object p2

    .line 105
    invoke-interface {v7, p2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 106
    .line 107
    .line 108
    goto :goto_1

    .line 109
    :cond_3
    new-instance v0, LN80/c$x;

    .line 110
    .line 111
    move-object v3, p3

    .line 112
    invoke-direct/range {v0 .. v7}, LN80/c$x;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;Ljava/util/List;)V

    .line 113
    .line 114
    .line 115
    return-object v0
.end method

.method public static final q(LD80/a$h;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$h;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    const/4 v1, 0x3

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 6
    .line 7
    .line 8
    move-result-object v4

    .line 9
    sget v7, LlZ0/d;->uikitStaticWhite:I

    .line 10
    .line 11
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    const/4 v5, 0x0

    .line 16
    if-eqz v3, :cond_1

    .line 17
    .line 18
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    if-eqz v3, :cond_1

    .line 23
    .line 24
    :cond_0
    const/4 v3, 0x0

    .line 25
    goto :goto_0

    .line 26
    :cond_1
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    :cond_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result v6

    .line 34
    if-eqz v6, :cond_0

    .line 35
    .line 36
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v6

    .line 40
    check-cast v6, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 41
    .line 42
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 43
    .line 44
    .line 45
    move-result-object v8

    .line 46
    if-ne v6, v8, :cond_2

    .line 47
    .line 48
    const/4 v3, 0x0

    .line 49
    const/4 v5, 0x1

    .line 50
    :goto_0
    sget v9, LlZ0/h;->ic_glyph_cart:I

    .line 51
    .line 52
    sget v10, LlZ0/d;->uikitStaticWhite:I

    .line 53
    .line 54
    sget v11, LlZ0/d;->uikitStaticWhite20:I

    .line 55
    .line 56
    sget v6, Lpb/k;->promo_codes_title:I

    .line 57
    .line 58
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v6

    .line 62
    invoke-interface {v0, v6}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v6

    .line 66
    move-object v12, v6

    .line 67
    check-cast v12, Ljava/lang/String;

    .line 68
    .line 69
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->a()Z

    .line 70
    .line 71
    .line 72
    move-result v6

    .line 73
    if-eqz v6, :cond_3

    .line 74
    .line 75
    sget-object v6, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 76
    .line 77
    sget-object v6, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 78
    .line 79
    sget v8, Lpb/k;->placeholder_variant_5:I

    .line 80
    .line 81
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 82
    .line 83
    .line 84
    move-result-object v8

    .line 85
    invoke-interface {v0, v8}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v8

    .line 89
    check-cast v8, Ljava/lang/String;

    .line 90
    .line 91
    sget v13, Lpb/k;->menu_promo_subtitle:I

    .line 92
    .line 93
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 94
    .line 95
    .line 96
    move-result-object v13

    .line 97
    invoke-interface {v0, v13}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object v13

    .line 101
    invoke-virtual/range {p0 .. p0}, LD80/a$h;->c()I

    .line 102
    .line 103
    .line 104
    move-result v14

    .line 105
    invoke-static {v14}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v14

    .line 109
    sget v15, Lpb/k;->pts_symbol:I

    .line 110
    .line 111
    invoke-static {v15}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 112
    .line 113
    .line 114
    move-result-object v15

    .line 115
    invoke-interface {v0, v15}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    new-array v15, v1, [Ljava/lang/Object;

    .line 120
    .line 121
    aput-object v13, v15, v3

    .line 122
    .line 123
    aput-object v14, v15, v2

    .line 124
    .line 125
    const/4 v2, 0x2

    .line 126
    aput-object v0, v15, v2

    .line 127
    .line 128
    invoke-static {v15, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 129
    .line 130
    .line 131
    move-result-object v0

    .line 132
    invoke-static {v6, v8, v0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    :goto_1
    move-object v13, v0

    .line 137
    goto :goto_2

    .line 138
    :cond_3
    const-string v0, ""

    .line 139
    .line 140
    goto :goto_1

    .line 141
    :goto_2
    if-nez p4, :cond_4

    .line 142
    .line 143
    sget v0, Lpb/g;->banner_promo:I

    .line 144
    .line 145
    move v14, v0

    .line 146
    goto :goto_3

    .line 147
    :cond_4
    const/4 v14, 0x0

    .line 148
    :goto_3
    sget v15, Lpb/g;->promo_shop_item_background:I

    .line 149
    .line 150
    sget v8, LlZ0/d;->uikitStaticWhite:I

    .line 151
    .line 152
    new-instance v3, LN80/c$e;

    .line 153
    .line 154
    move-object/from16 v6, p3

    .line 155
    .line 156
    invoke-direct/range {v3 .. v15}, LN80/c$e;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIIIILjava/lang/String;Ljava/lang/String;II)V

    .line 157
    .line 158
    .line 159
    return-object v3
.end method

.method public static final r(LD80/a$i;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$i;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$i;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    sget v4, LlZ0/d;->uikitStaticWhite:I

    .line 6
    .line 7
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    :cond_0
    const/4 p0, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    if-eqz v0, :cond_0

    .line 31
    .line 32
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    invoke-virtual {p0}, LD80/a$i;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    if-ne v0, v3, :cond_2

    .line 43
    .line 44
    const/4 p0, 0x1

    .line 45
    :goto_0
    sget v6, LlZ0/h;->ic_glyph_balloons:I

    .line 46
    .line 47
    sget v7, LlZ0/d;->uikitStaticDarkOrange:I

    .line 48
    .line 49
    sget v8, LlZ0/d;->uikitStaticWhite:I

    .line 50
    .line 51
    sget p2, Lpb/k;->news_promo:I

    .line 52
    .line 53
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 54
    .line 55
    .line 56
    move-result-object p2

    .line 57
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    move-object v9, p2

    .line 62
    check-cast v9, Ljava/lang/String;

    .line 63
    .line 64
    sget p2, Lpb/k;->menu_promo_description:I

    .line 65
    .line 66
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 67
    .line 68
    .line 69
    move-result-object p2

    .line 70
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    move-object v10, p1

    .line 75
    check-cast v10, Ljava/lang/String;

    .line 76
    .line 77
    sget v12, Lpb/g;->main_menu_promotion_item_background:I

    .line 78
    .line 79
    if-nez p4, :cond_3

    .line 80
    .line 81
    sget v2, LlZ0/h;->ic_banner_events:I

    .line 82
    .line 83
    move v11, v2

    .line 84
    goto :goto_1

    .line 85
    :cond_3
    const/4 v11, 0x0

    .line 86
    :goto_1
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 87
    .line 88
    new-instance v0, LN80/c$e;

    .line 89
    .line 90
    move v2, p0

    .line 91
    move-object/from16 v3, p3

    .line 92
    .line 93
    invoke-direct/range {v0 .. v12}, LN80/c$e;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIIIILjava/lang/String;Ljava/lang/String;II)V

    .line 94
    .line 95
    .line 96
    return-object v0
.end method

.method public static final s(LD80/a$j;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$j;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$j;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    :cond_0
    const/4 p0, 0x0

    .line 19
    goto :goto_0

    .line 20
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    invoke-virtual {p0}, LD80/a$j;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    if-ne v0, v3, :cond_2

    .line 41
    .line 42
    const/4 p0, 0x1

    .line 43
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_lock:I

    .line 44
    .line 45
    sget p2, Lpb/k;->increase_security:I

    .line 46
    .line 47
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    move-object v7, p2

    .line 56
    check-cast v7, Ljava/lang/String;

    .line 57
    .line 58
    sget p2, Lpb/k;->menu_increase_security_description:I

    .line 59
    .line 60
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    move-object v8, p1

    .line 69
    check-cast v8, Ljava/lang/String;

    .line 70
    .line 71
    if-nez p4, :cond_3

    .line 72
    .line 73
    sget v2, Lpb/g;->banner_security:I

    .line 74
    .line 75
    move v9, v2

    .line 76
    goto :goto_1

    .line 77
    :cond_3
    const/4 v9, 0x0

    .line 78
    :goto_1
    sget v5, LlZ0/d;->uikitPrimary:I

    .line 79
    .line 80
    sget v6, LlZ0/d;->uikitStaticWhite:I

    .line 81
    .line 82
    new-instance v0, LN80/c$f;

    .line 83
    .line 84
    move v10, v5

    .line 85
    move v2, p0

    .line 86
    move-object v3, p3

    .line 87
    invoke-direct/range {v0 .. v10}, LN80/c$f;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIILjava/lang/String;Ljava/lang/String;II)V

    .line 88
    .line 89
    .line 90
    return-object v0
.end method

.method public static final t(LD80/a$k;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$k;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "Z)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    :cond_0
    const/4 v4, 0x0

    goto :goto_0

    .line 2
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 3
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v2

    if-ne v0, v2, :cond_2

    const/4 v1, 0x1

    const/4 v4, 0x1

    .line 4
    :goto_0
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object p2

    sget-object v0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    move-result p2

    aget p2, v0, p2

    packed-switch p2, :pswitch_data_0

    .line 5
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    move-result-object p0

    return-object p0

    .line 6
    :pswitch_0
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 7
    sget v6, LlZ0/h;->ic_glyph_aviator:I

    .line 8
    sget p0, Lpb/k;->menu_aviator_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 9
    sget p0, Lpb/k;->menu_aviator_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 10
    new-instance v2, LN80/c$r;

    move-object v5, p3

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1
    move-object v5, p3

    .line 11
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 12
    sget v6, LlZ0/h;->ic_glyph_bingo:I

    .line 13
    sget p0, Lpb/k;->bingo_top_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 14
    sget p0, Lpb/k;->bingo_top_body:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 15
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_2
    move-object v5, p3

    .line 16
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 17
    sget v6, LlZ0/h;->ic_glyph_peak:I

    .line 18
    sget p0, Lpb/k;->live_casino_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 19
    sget p0, Lpb/k;->menu_casino_live_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 20
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_3
    move-object v5, p3

    .line 21
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 22
    sget v6, LlZ0/h;->ic_glyph_slots:I

    .line 23
    sget p0, Lpb/k;->cases_slots:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 24
    sget p0, Lpb/k;->menu_casino_slots_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 25
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_4
    move-object v5, p3

    .line 26
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 27
    sget v6, Lpb/g;->ic_fast_games:I

    .line 28
    sget p0, Lpb/k;->fast_games_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 29
    sget p0, Lpb/k;->fast_games_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 30
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_5
    move-object v5, p3

    .line 31
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 32
    sget v6, LlZ0/h;->ic_glyph_cashback:I

    .line 33
    sget p0, Lpb/k;->sport_cashback:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 34
    sget p0, Lpb/k;->sport_cashback_subtitle:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 35
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_6
    move-object v5, p3

    .line 36
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 37
    sget v6, LlZ0/h;->ic_glyph_toto:I

    .line 38
    sget p0, Lpb/k;->toto_jackpot_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 39
    sget p0, Lpb/k;->menu_toto_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 40
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_7
    move-object v5, p3

    .line 41
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 42
    sget v6, LlZ0/h;->ic_glyph_shopping_bag:I

    .line 43
    sget p0, Lpb/k;->promo_shop_name:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 44
    sget p0, Lpb/k;->promo_settings_subtitle:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 45
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_8
    move-object v5, p3

    .line 46
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 47
    sget v6, LlZ0/h;->ic_glyph_financial:I

    .line 48
    sget p0, Lpb/k;->finance_bets:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 49
    sget p0, Lpb/k;->menu_finbets_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 50
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_9
    move-object v5, p3

    .line 51
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 52
    sget v6, LlZ0/h;->ic_glyph_betconstructor:I

    .line 53
    sget p0, Lpb/k;->betconstructor:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 54
    sget p0, Lpb/k;->menu_betconstructor_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 55
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_a
    move-object v5, p3

    .line 56
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    if-eqz p4, :cond_3

    .line 57
    sget p0, LlZ0/h;->ic_glyph_info_circle_filled:I

    :goto_1
    move v6, p0

    goto :goto_2

    :cond_3
    sget p0, LlZ0/h;->ic_glyph_info:I

    goto :goto_1

    .line 58
    :goto_2
    sget p0, Lpb/k;->info:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 59
    sget p0, Lpb/k;->menu_info_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 60
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_b
    move-object v5, p3

    .line 61
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 62
    sget v6, LlZ0/h;->ic_glyph_qr_scanning:I

    .line 63
    sget p0, Lpb/k;->scanner:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 64
    sget p0, Lpb/k;->menu_coupon_scanner_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 65
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_c
    move-object v5, p3

    .line 66
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 67
    sget v6, LlZ0/h;->ic_glyph_notification_active:I

    .line 68
    sget p0, Lpb/k;->notifications_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 69
    sget p0, Lpb/k;->menu_notifications_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 70
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_d
    move-object v5, p3

    .line 71
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 72
    sget v6, LlZ0/h;->ic_glyph_favourite_active:I

    .line 73
    sget p0, Lpb/k;->favorites_name:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 74
    sget p0, Lpb/k;->menu_one_x_games_favorites_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 75
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_e
    move-object v5, p3

    .line 76
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 77
    sget v6, LlZ0/h;->ic_glyph_cashback:I

    .line 78
    sget p0, Lpb/k;->cashback:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 79
    sget p0, Lpb/k;->menu_one_x_games_item_cashback_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 80
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_f
    move-object v5, p3

    .line 81
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 82
    sget v6, LlZ0/h;->ic_glyph_promo_games:I

    .line 83
    sget p0, Lpb/k;->bonuses:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 84
    sget p0, Lpb/k;->menu_one_x_games_promo_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 85
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_10
    move-object v5, p3

    .line 86
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 87
    sget v6, LlZ0/h;->ic_glyph_providers:I

    .line 88
    sget p0, Lpb/k;->providers:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 89
    sget p0, Lpb/k;->casino_providers_menu_desription:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 90
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_11
    move-object v5, p3

    .line 91
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 92
    sget v6, LlZ0/h;->ic_glyph_tv_bet:I

    .line 93
    sget p0, Lpb/k;->tv_bet_casino_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 94
    sget p0, Lpb/k;->menu_tvbet_description_item:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 95
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_12
    move-object v5, p3

    .line 96
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 97
    sget v6, LlZ0/h;->ic_glyph_promo_aggregator:I

    .line 98
    sget p0, Lpb/k;->casino_promo_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 99
    sget p0, Lpb/k;->casino_promo_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 100
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_13
    move-object v5, p3

    .line 101
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 102
    sget v6, LlZ0/h;->ic_glyph_tournaments:I

    .line 103
    sget p0, Lpb/k;->casino_tour_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 104
    sget p0, Lpb/k;->casino_tour_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 105
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_14
    move-object v5, p3

    .line 106
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 107
    sget v6, LlZ0/h;->ic_glyph_categories:I

    .line 108
    sget p0, Lpb/k;->casino_category_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 109
    sget p0, Lpb/k;->casino_category_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 110
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_15
    move-object v5, p3

    .line 111
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 112
    sget v6, LlZ0/h;->ic_glyph_virtual:I

    .line 113
    sget p0, Lpb/k;->my_virtual:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 114
    sget p0, Lpb/k;->casino_my_virtual_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 115
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_16
    move-object v5, p3

    .line 116
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 117
    sget v6, LlZ0/h;->ic_glyph_cards:I

    .line 118
    sget p0, Lpb/k;->my_casino:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 119
    sget p0, Lpb/k;->casino_my_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 120
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_17
    move-object v5, p3

    .line 121
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 122
    sget v6, LlZ0/h;->ic_glyph_bets_on_yours:I

    .line 123
    sget p0, Lpb/k;->bets_on_yours:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 124
    sget p0, Lpb/k;->menu_bet_on_favorites_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 125
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_18
    move-object v5, p3

    .line 126
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 127
    sget v6, LlZ0/h;->ic_glyph_results:I

    .line 128
    sget p0, Lpb/k;->results:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 129
    sget p0, Lpb/k;->menu_results_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 130
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_19
    move-object v5, p3

    .line 131
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 132
    sget v6, LlZ0/h;->ic_glyph_stream:I

    .line 133
    sget p0, Lpb/k;->stream_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 134
    sget p0, Lpb/k;->menu_stream_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 135
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1a
    move-object v5, p3

    .line 136
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 137
    sget v6, LlZ0/h;->ic_glyph_express:I

    .line 138
    sget p0, Lpb/k;->day_express:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 139
    sget p0, Lpb/k;->menu_day_express_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 140
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1b
    move-object v5, p3

    .line 141
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 142
    sget v6, LlZ0/h;->ic_glyph_authenticator:I

    .line 143
    sget p0, Lpb/k;->authenticator:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 144
    sget p0, Lpb/k;->authenticator_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 145
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1c
    move-object v5, p3

    .line 146
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 147
    sget v6, LlZ0/h;->ic_glyph_support:I

    .line 148
    sget p0, Lpb/k;->support:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 149
    sget p0, Lpb/k;->menu_support_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 150
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1d
    move-object v5, p3

    .line 151
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 152
    sget v6, LlZ0/h;->ic_glyph_quest:I

    .line 153
    sget p0, Lpb/k;->daily_task_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 154
    sget p0, Lpb/k;->daily_task_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 155
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1e
    move-object v5, p3

    .line 156
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 157
    sget v6, LlZ0/h;->ic_glyph_peak:I

    .line 158
    sget p0, Lpb/k;->live_casino_title:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 159
    sget p0, Lpb/k;->menu_live_casino_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 160
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_1f
    move-object v5, p3

    .line 161
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 162
    sget v6, LlZ0/h;->ic_glyph_slots:I

    .line 163
    sget p0, Lpb/k;->cases_slots:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 164
    sget p0, Lpb/k;->menu_slots_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 165
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_20
    move-object v5, p3

    .line 166
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 167
    sget v6, LlZ0/h;->ic_glyph_cyber:I

    .line 168
    sget p0, Lpb/k;->cyber_sport:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 169
    sget p0, Lpb/k;->menu_cyber_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 170
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_21
    move-object v5, p3

    .line 171
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 172
    sget v6, LlZ0/h;->ic_glyph_line:I

    .line 173
    sget p0, Lpb/k;->line:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 174
    sget p0, Lpb/k;->menu_line_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 175
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_22
    move-object v5, p3

    .line 176
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 177
    sget v6, LlZ0/h;->ic_glyph_live:I

    .line 178
    sget p0, Lpb/k;->live_new:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 179
    sget p0, Lpb/k;->menu_live_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 180
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_23
    move-object v5, p3

    .line 181
    invoke-virtual {p0}, LD80/a$k;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    move-result-object v3

    .line 182
    sget v6, LlZ0/h;->ic_glyph_responsible_gambling:I

    .line 183
    sget p0, Lpb/k;->responsible_game:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v7, p0

    check-cast v7, Ljava/lang/String;

    .line 184
    sget p0, Lpb/k;->menu_responsible_gaming_description:I

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    move-object v8, p0

    check-cast v8, Ljava/lang/String;

    .line 185
    new-instance v2, LN80/c$r;

    invoke-direct/range {v2 .. v8}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    return-object v2

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_23
        :pswitch_22
        :pswitch_21
        :pswitch_20
        :pswitch_1f
        :pswitch_1e
        :pswitch_1d
        :pswitch_1c
        :pswitch_1b
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final u(LD80/a$l;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$l;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    sget-object v4, Lcom/xbet/onexcore/configs/MenuItemModel;->LIVE:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    const/4 v2, 0x1

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    :cond_0
    const/4 p1, 0x0

    .line 18
    goto :goto_0

    .line 19
    :cond_1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :cond_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    sget-object v3, Lcom/xbet/onexcore/configs/MenuItemModel;->LIVE:Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    if-ne v0, v3, :cond_2

    .line 38
    .line 39
    const/4 p1, 0x0

    .line 40
    const/4 v1, 0x1

    .line 41
    :goto_0
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {v0}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    sget v5, LlZ0/d;->uikitStaticWhite:I

    .line 50
    .line 51
    sget-object v0, LDX0/d;->a:LDX0/d;

    .line 52
    .line 53
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    invoke-virtual {v6}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getSportId()J

    .line 58
    .line 59
    .line 60
    move-result-wide v6

    .line 61
    invoke-virtual {v0, v6, v7}, LDX0/d;->c(J)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v6

    .line 65
    new-instance v0, Ln8/a;

    .line 66
    .line 67
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 68
    .line 69
    .line 70
    sget-object v7, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 71
    .line 72
    sget-object v7, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 73
    .line 74
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 75
    .line 76
    .line 77
    move-result-object v8

    .line 78
    invoke-virtual {v8}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 79
    .line 80
    .line 81
    move-result v8

    .line 82
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 83
    .line 84
    .line 85
    move-result-object v8

    .line 86
    new-array v9, v2, [Ljava/lang/Object;

    .line 87
    .line 88
    aput-object v8, v9, p1

    .line 89
    .line 90
    invoke-static {v9, v2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object v8

    .line 94
    const-string v9, "static/img/android/TopChamps/%d/backgrounds/Menu_Icon.webp"

    .line 95
    .line 96
    invoke-static {v7, v9, v8}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v8

    .line 100
    invoke-virtual {v0, v8}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    sget v8, LlZ0/d;->uikitSecondary:I

    .line 109
    .line 110
    sget v9, Lpb/g;->sport_new:I

    .line 111
    .line 112
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 113
    .line 114
    .line 115
    move-result-object v10

    .line 116
    invoke-virtual {v10}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getTitle()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v10

    .line 120
    new-instance v11, Ln8/a;

    .line 121
    .line 122
    invoke-direct {v11}, Ln8/a;-><init>()V

    .line 123
    .line 124
    .line 125
    invoke-virtual {p0}, LD80/a$l;->a()Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;

    .line 126
    .line 127
    .line 128
    move-result-object p0

    .line 129
    invoke-virtual {p0}, Lorg/xbet/special_event/api/main/domain/eventinfo/model/SpecialEventInfoModel;->getId()I

    .line 130
    .line 131
    .line 132
    move-result p0

    .line 133
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 134
    .line 135
    .line 136
    move-result-object p0

    .line 137
    new-array v12, v2, [Ljava/lang/Object;

    .line 138
    .line 139
    aput-object p0, v12, p1

    .line 140
    .line 141
    invoke-static {v12, v2}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 142
    .line 143
    .line 144
    move-result-object p0

    .line 145
    const-string p1, "static/img/android/TopChamps/%d/backgrounds/Menu_Illustration.png"

    .line 146
    .line 147
    invoke-static {v7, p1, p0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object p0

    .line 151
    invoke-virtual {v11, p0}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 152
    .line 153
    .line 154
    move-result-object p0

    .line 155
    invoke-virtual {p0}, Ln8/a;->a()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v12

    .line 159
    move-object v7, v0

    .line 160
    new-instance v0, LN80/c$s;

    .line 161
    .line 162
    const-string v11, ""

    .line 163
    .line 164
    move-object v2, p2

    .line 165
    invoke-direct/range {v0 .. v12}, LN80/c$s;-><init>(ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILcom/xbet/onexcore/configs/MenuItemModel;ILjava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 166
    .line 167
    .line 168
    return-object v0
.end method

.method public static final v(LD80/a;LHX0/e;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;
    .locals 6
    .param p0    # LD80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/main_menu/api/domain/models/MenuSectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a;",
            "LHX0/e;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "ZZ)",
            "LN80/c;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    instance-of v0, p0, LD80/a$k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p0, LD80/a$k;

    .line 6
    .line 7
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$1;

    .line 8
    .line 9
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-static {p0, p4, p2, p3, p5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->t(LD80/a$k;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0

    .line 17
    :cond_0
    instance-of p5, p0, LD80/a$e;

    .line 18
    .line 19
    if-eqz p5, :cond_1

    .line 20
    .line 21
    check-cast p0, LD80/a$e;

    .line 22
    .line 23
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$2;

    .line 24
    .line 25
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$2;-><init>(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->o(LD80/a$e;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    return-object p0

    .line 33
    :cond_1
    instance-of p5, p0, LD80/a$h;

    .line 34
    .line 35
    if-eqz p5, :cond_2

    .line 36
    .line 37
    check-cast p0, LD80/a$h;

    .line 38
    .line 39
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$3;

    .line 40
    .line 41
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$3;-><init>(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    invoke-static {p0, p4, p2, p3, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->q(LD80/a$h;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    return-object p0

    .line 53
    :cond_2
    instance-of p5, p0, LD80/a$i;

    .line 54
    .line 55
    if-eqz p5, :cond_3

    .line 56
    .line 57
    check-cast p0, LD80/a$i;

    .line 58
    .line 59
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$4;

    .line 60
    .line 61
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$4;-><init>(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    invoke-static {p0, p4, p2, p3, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->r(LD80/a$i;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    return-object p0

    .line 73
    :cond_3
    instance-of p5, p0, LD80/a$m;

    .line 74
    .line 75
    if-eqz p5, :cond_4

    .line 76
    .line 77
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$5;

    .line 78
    .line 79
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$5;-><init>(Ljava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 83
    .line 84
    .line 85
    move-result p1

    .line 86
    invoke-static {p0, p2, p3, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->e(Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 87
    .line 88
    .line 89
    move-result-object p0

    .line 90
    return-object p0

    .line 91
    :cond_4
    instance-of p5, p0, LD80/a$g;

    .line 92
    .line 93
    if-eqz p5, :cond_5

    .line 94
    .line 95
    check-cast p0, LD80/a$g;

    .line 96
    .line 97
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$6;

    .line 98
    .line 99
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$6;-><init>(Ljava/lang/Object;)V

    .line 100
    .line 101
    .line 102
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->p(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 103
    .line 104
    .line 105
    move-result-object p0

    .line 106
    return-object p0

    .line 107
    :cond_5
    instance-of p5, p0, LD80/a$l;

    .line 108
    .line 109
    if-eqz p5, :cond_6

    .line 110
    .line 111
    check-cast p0, LD80/a$l;

    .line 112
    .line 113
    invoke-static {p0, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->u(LD80/a$l;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 114
    .line 115
    .line 116
    move-result-object p0

    .line 117
    return-object p0

    .line 118
    :cond_6
    instance-of p5, p0, LD80/a$c;

    .line 119
    .line 120
    if-eqz p5, :cond_7

    .line 121
    .line 122
    check-cast p0, LD80/a$c;

    .line 123
    .line 124
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$7;

    .line 125
    .line 126
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$7;-><init>(Ljava/lang/Object;)V

    .line 127
    .line 128
    .line 129
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->m(LD80/a$c;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 130
    .line 131
    .line 132
    move-result-object p0

    .line 133
    return-object p0

    .line 134
    :cond_7
    instance-of p5, p0, LD80/a$d;

    .line 135
    .line 136
    if-eqz p5, :cond_8

    .line 137
    .line 138
    check-cast p0, LD80/a$d;

    .line 139
    .line 140
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$8;

    .line 141
    .line 142
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$8;-><init>(Ljava/lang/Object;)V

    .line 143
    .line 144
    .line 145
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->n(LD80/a$d;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 146
    .line 147
    .line 148
    move-result-object p0

    .line 149
    return-object p0

    .line 150
    :cond_8
    instance-of p5, p0, LD80/a$j;

    .line 151
    .line 152
    if-eqz p5, :cond_9

    .line 153
    .line 154
    check-cast p0, LD80/a$j;

    .line 155
    .line 156
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$9;

    .line 157
    .line 158
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$9;-><init>(Ljava/lang/Object;)V

    .line 159
    .line 160
    .line 161
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 162
    .line 163
    .line 164
    move-result p1

    .line 165
    invoke-static {p0, p4, p2, p3, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->s(LD80/a$j;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 166
    .line 167
    .line 168
    move-result-object p0

    .line 169
    return-object p0

    .line 170
    :cond_9
    instance-of p5, p0, LD80/a$b;

    .line 171
    .line 172
    if-eqz p5, :cond_a

    .line 173
    .line 174
    check-cast p0, LD80/a$b;

    .line 175
    .line 176
    new-instance p4, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$10;

    .line 177
    .line 178
    invoke-direct {p4, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$10;-><init>(Ljava/lang/Object;)V

    .line 179
    .line 180
    .line 181
    invoke-static {p0, p4, p2, p3}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->l(LD80/a$b;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;

    .line 182
    .line 183
    .line 184
    move-result-object p0

    .line 185
    return-object p0

    .line 186
    :cond_a
    instance-of p5, p0, LD80/a$a;

    .line 187
    .line 188
    if-eqz p5, :cond_b

    .line 189
    .line 190
    move-object v0, p0

    .line 191
    check-cast v0, LD80/a$a;

    .line 192
    .line 193
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$11;

    .line 194
    .line 195
    invoke-direct {v1, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$11;-><init>(Ljava/lang/Object;)V

    .line 196
    .line 197
    .line 198
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 199
    .line 200
    .line 201
    move-result v5

    .line 202
    move-object v2, p2

    .line 203
    move-object v3, p3

    .line 204
    move v4, p4

    .line 205
    invoke-static/range {v0 .. v5}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->k(LD80/a$a;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;

    .line 206
    .line 207
    .line 208
    move-result-object p0

    .line 209
    return-object p0

    .line 210
    :cond_b
    move-object v2, p2

    .line 211
    move-object v3, p3

    .line 212
    instance-of p2, p0, LD80/a$f;

    .line 213
    .line 214
    if-eqz p2, :cond_c

    .line 215
    .line 216
    check-cast p0, LD80/a$f;

    .line 217
    .line 218
    new-instance p2, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$12;

    .line 219
    .line 220
    invoke-direct {p2, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$toMenuUiItem$12;-><init>(Ljava/lang/Object;)V

    .line 221
    .line 222
    .line 223
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->f(LHX0/e;)Z

    .line 224
    .line 225
    .line 226
    move-result p1

    .line 227
    invoke-static {p0, p2, v2, v3, p1}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->a(LD80/a$f;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;Z)LN80/c;

    .line 228
    .line 229
    .line 230
    move-result-object p0

    .line 231
    return-object p0

    .line 232
    :cond_c
    invoke-static {}, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt;->b()LN80/c$r;

    .line 233
    .line 234
    .line 235
    move-result-object p0

    .line 236
    return-object p0
.end method

.method public static final w(LD80/a$g;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$g;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    invoke-virtual {p0}, LD80/a$g;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-ne v0, v3, :cond_1

    .line 40
    .line 41
    const/4 v2, 0x1

    .line 42
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_games:I

    .line 43
    .line 44
    sget p0, Lpb/k;->all_games:I

    .line 45
    .line 46
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object p0

    .line 50
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    move-object v5, p0

    .line 55
    check-cast v5, Ljava/lang/String;

    .line 56
    .line 57
    sget p0, Lpb/k;->menu_one_x_games_description:I

    .line 58
    .line 59
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object p0

    .line 63
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    move-object v6, p0

    .line 68
    check-cast v6, Ljava/lang/String;

    .line 69
    .line 70
    new-instance v0, LN80/c$r;

    .line 71
    .line 72
    move-object v3, p3

    .line 73
    invoke-direct/range {v0 .. v6}, LN80/c$r;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method

.method public static final x(LD80/a$a;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;ZZ)LN80/c;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$a;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            "ZZ)",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$a;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    :cond_0
    const/4 p0, 0x0

    .line 19
    goto :goto_0

    .line 20
    :cond_1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    :cond_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 35
    .line 36
    invoke-virtual {p0}, LD80/a$a;->a()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    if-ne v0, v3, :cond_2

    .line 41
    .line 42
    const/4 p0, 0x1

    .line 43
    :goto_0
    sget p2, Lpb/k;->my_virtual:I

    .line 44
    .line 45
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p2

    .line 53
    move-object v7, p2

    .line 54
    check-cast v7, Ljava/lang/String;

    .line 55
    .line 56
    if-eqz p4, :cond_3

    .line 57
    .line 58
    sget p2, Lpb/k;->casino_my_virtual_description:I

    .line 59
    .line 60
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, Ljava/lang/String;

    .line 69
    .line 70
    :goto_1
    move-object v8, p1

    .line 71
    goto :goto_2

    .line 72
    :cond_3
    const-string p1, ""

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :goto_2
    if-nez p5, :cond_4

    .line 76
    .line 77
    sget v2, Lpb/g;->my_aggregator_banner:I

    .line 78
    .line 79
    move v9, v2

    .line 80
    goto :goto_3

    .line 81
    :cond_4
    const/4 v9, 0x0

    .line 82
    :goto_3
    sget v5, LlZ0/d;->uiKitAggregatorLavender:I

    .line 83
    .line 84
    sget v4, LlZ0/h;->ic_glyph_virtual:I

    .line 85
    .line 86
    sget v6, LlZ0/d;->uikitStaticWhite:I

    .line 87
    .line 88
    new-instance v0, LN80/c$m;

    .line 89
    .line 90
    move v10, v5

    .line 91
    move v2, p0

    .line 92
    move-object v3, p3

    .line 93
    invoke-direct/range {v0 .. v10}, LN80/c$m;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;IIILjava/lang/String;Ljava/lang/String;II)V

    .line 94
    .line 95
    .line 96
    return-object v0
.end method

.method public static final y(LD80/a$b;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$b;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/mappers/MenuUiItemMapperKt$a;->b:[I

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    aget v1, v2, v1

    .line 14
    .line 15
    const/16 v2, 0x27

    .line 16
    .line 17
    const/4 v3, 0x1

    .line 18
    const/4 v4, 0x0

    .line 19
    if-ne v1, v2, :cond_3

    .line 20
    .line 21
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    if-eqz v1, :cond_1

    .line 36
    .line 37
    :cond_0
    const/4 v7, 0x0

    .line 38
    goto :goto_0

    .line 39
    :cond_1
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    :cond_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_0

    .line 48
    .line 49
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    check-cast v2, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 54
    .line 55
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 56
    .line 57
    .line 58
    move-result-object v5

    .line 59
    if-ne v2, v5, :cond_2

    .line 60
    .line 61
    const/4 v7, 0x1

    .line 62
    :goto_0
    sget v9, LlZ0/h;->ic_glyph_fast_bet:I

    .line 63
    .line 64
    sget v1, Lpb/k;->fast_bet_title:I

    .line 65
    .line 66
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-interface {v0, v1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    move-object v10, v1

    .line 75
    check-cast v10, Ljava/lang/String;

    .line 76
    .line 77
    sget v1, Lpb/k;->fast_bet_description:I

    .line 78
    .line 79
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-interface {v0, v1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    move-object v11, v0

    .line 88
    check-cast v11, Ljava/lang/String;

    .line 89
    .line 90
    new-instance v5, LN80/c$o;

    .line 91
    .line 92
    move-object/from16 v8, p3

    .line 93
    .line 94
    invoke-direct/range {v5 .. v11}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    return-object v5

    .line 98
    :cond_3
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 99
    .line 100
    .line 101
    move-result-object v8

    .line 102
    invoke-static/range {p2 .. p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    move-result v0

    .line 106
    if-eqz v0, :cond_5

    .line 107
    .line 108
    invoke-interface/range {p2 .. p2}, Ljava/util/Collection;->isEmpty()Z

    .line 109
    .line 110
    .line 111
    move-result v0

    .line 112
    if-eqz v0, :cond_5

    .line 113
    .line 114
    :cond_4
    const/4 v7, 0x0

    .line 115
    goto :goto_1

    .line 116
    :cond_5
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    :cond_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 121
    .line 122
    .line 123
    move-result v1

    .line 124
    if-eqz v1, :cond_4

    .line 125
    .line 126
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    check-cast v1, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 131
    .line 132
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 133
    .line 134
    .line 135
    move-result-object v2

    .line 136
    if-ne v1, v2, :cond_6

    .line 137
    .line 138
    const/4 v7, 0x1

    .line 139
    :goto_1
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    invoke-virtual {v0}, Lg81/b;->g()J

    .line 144
    .line 145
    .line 146
    move-result-wide v10

    .line 147
    new-instance v0, Ln8/a;

    .line 148
    .line 149
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 150
    .line 151
    .line 152
    sget-object v1, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 153
    .line 154
    sget-object v1, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 155
    .line 156
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 157
    .line 158
    .line 159
    move-result-object v2

    .line 160
    invoke-virtual {v2}, Lg81/b;->g()J

    .line 161
    .line 162
    .line 163
    move-result-wide v5

    .line 164
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 165
    .line 166
    .line 167
    move-result-object v2

    .line 168
    new-array v5, v3, [Ljava/lang/Object;

    .line 169
    .line 170
    aput-object v2, v5, v4

    .line 171
    .line 172
    invoke-static {v5, v3}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v2

    .line 176
    const-string v3, "static/img/android/agregator/category/%d.svg"

    .line 177
    .line 178
    invoke-static {v1, v3, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object v1

    .line 182
    invoke-virtual {v0, v1}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 183
    .line 184
    .line 185
    move-result-object v0

    .line 186
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 187
    .line 188
    .line 189
    move-result-object v14

    .line 190
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    invoke-virtual {v0}, Lg81/b;->n()Ljava/lang/String;

    .line 195
    .line 196
    .line 197
    move-result-object v15

    .line 198
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 199
    .line 200
    .line 201
    move-result-object v0

    .line 202
    invoke-virtual {v0}, Lg81/b;->l()J

    .line 203
    .line 204
    .line 205
    move-result-wide v16

    .line 206
    invoke-virtual/range {p0 .. p0}, LD80/a$b;->a()Lg81/b;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-virtual {v0}, Lg81/b;->e()J

    .line 211
    .line 212
    .line 213
    move-result-wide v12

    .line 214
    new-instance v6, LN80/c$p;

    .line 215
    .line 216
    move-object/from16 v9, p3

    .line 217
    .line 218
    invoke-direct/range {v6 .. v17}, LN80/c$p;-><init>(ZLcom/xbet/onexcore/configs/MenuItemModel;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;JJLjava/lang/String;Ljava/lang/String;J)V

    .line 219
    .line 220
    .line 221
    return-object v6
.end method

.method public static final z(LD80/a$c;Lkotlin/jvm/functions/Function1;Ljava/util/List;Lorg/xbet/main_menu/api/domain/models/MenuSectionType;)LN80/c;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD80/a$c;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lcom/xbet/onexcore/configs/MenuItemModel;",
            ">;",
            "Lorg/xbet/main_menu/api/domain/models/MenuSectionType;",
            ")",
            "LN80/c;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD80/a$c;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    :cond_1
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 34
    .line 35
    invoke-virtual {p0}, LD80/a$c;->b()Lcom/xbet/onexcore/configs/MenuItemModel;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-ne v0, v3, :cond_1

    .line 40
    .line 41
    const/4 v2, 0x1

    .line 42
    :cond_2
    :goto_0
    sget v4, LlZ0/h;->ic_glyph_authenticator:I

    .line 43
    .line 44
    sget p0, Lpb/k;->authenticator:I

    .line 45
    .line 46
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 47
    .line 48
    .line 49
    move-result-object p0

    .line 50
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    move-object v5, p0

    .line 55
    check-cast v5, Ljava/lang/String;

    .line 56
    .line 57
    sget p0, Lpb/k;->authenticator_description:I

    .line 58
    .line 59
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object p0

    .line 63
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    move-object v6, p0

    .line 68
    check-cast v6, Ljava/lang/String;

    .line 69
    .line 70
    new-instance v0, LN80/c$o;

    .line 71
    .line 72
    move-object v3, p3

    .line 73
    invoke-direct/range {v0 .. v6}, LN80/c$o;-><init>(Lcom/xbet/onexcore/configs/MenuItemModel;ZLorg/xbet/main_menu/api/domain/models/MenuSectionType;ILjava/lang/String;Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method
