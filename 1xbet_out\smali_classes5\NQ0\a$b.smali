.class public final LNQ0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LNQ0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNQ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LNQ0/a$b$a;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/k;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/s;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/e;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LSX0/a;

.field public final b:LNQ0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/e;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/TennisWinLossRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LOQ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/q;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/i;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/g;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/y;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/A;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/C;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/w;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/u;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/o;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQQ0/m;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LNQ0/a$b;->b:LNQ0/a$b;

    .line 4
    iput-object p9, p0, LNQ0/a$b;->a:LSX0/a;

    .line 5
    invoke-virtual/range {p0 .. p11}, LNQ0/a$b;->e(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V

    .line 6
    invoke-virtual/range {p0 .. p11}, LNQ0/a$b;->f(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;LNQ0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p11}, LNQ0/a$b;-><init>(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNQ0/a$b;->i(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNQ0/a$b;->h(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNQ0/a$b;->j(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public d(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNQ0/a$b;->g(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final e(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iput-object v1, v0, LNQ0/a$b;->c:Ldagger/internal/h;

    .line 8
    .line 9
    invoke-static {v1}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/f;->a(LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/f;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iput-object v1, v0, LNQ0/a$b;->d:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iput-object v1, v0, LNQ0/a$b;->e:Ldagger/internal/h;

    .line 20
    .line 21
    new-instance v1, LNQ0/a$b$a;

    .line 22
    .line 23
    move-object/from16 v2, p1

    .line 24
    .line 25
    invoke-direct {v1, v2}, LNQ0/a$b$a;-><init>(LQW0/c;)V

    .line 26
    .line 27
    .line 28
    iput-object v1, v0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 29
    .line 30
    invoke-static {}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/c;->a()Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/c;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    iget-object v2, v0, LNQ0/a$b;->d:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v3, v0, LNQ0/a$b;->e:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object v4, v0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 39
    .line 40
    invoke-static {v1, v2, v3, v4}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/g;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/data/g;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    iput-object v1, v0, LNQ0/a$b;->g:Ldagger/internal/h;

    .line 45
    .line 46
    invoke-static {v1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    iput-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 51
    .line 52
    invoke-static {v1}, LQQ0/r;->a(LBc/a;)LQQ0/r;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    iput-object v1, v0, LNQ0/a$b;->i:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {v1}, LQQ0/j;->a(LBc/a;)LQQ0/j;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    iput-object v1, v0, LNQ0/a$b;->j:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {v1}, LQQ0/h;->a(LBc/a;)LQQ0/h;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    iput-object v1, v0, LNQ0/a$b;->k:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {v1}, LQQ0/z;->a(LBc/a;)LQQ0/z;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    iput-object v1, v0, LNQ0/a$b;->l:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {v1}, LQQ0/B;->a(LBc/a;)LQQ0/B;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    iput-object v1, v0, LNQ0/a$b;->m:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static {v1}, LQQ0/D;->a(LBc/a;)LQQ0/D;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    iput-object v1, v0, LNQ0/a$b;->n:Ldagger/internal/h;

    .line 97
    .line 98
    iget-object v2, v0, LNQ0/a$b;->l:Ldagger/internal/h;

    .line 99
    .line 100
    iget-object v3, v0, LNQ0/a$b;->m:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static {v2, v3, v1}, LQQ0/x;->a(LBc/a;LBc/a;LBc/a;)LQQ0/x;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    iput-object v1, v0, LNQ0/a$b;->o:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 109
    .line 110
    invoke-static {v1}, LQQ0/v;->a(LBc/a;)LQQ0/v;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    iput-object v1, v0, LNQ0/a$b;->p:Ldagger/internal/h;

    .line 115
    .line 116
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 117
    .line 118
    invoke-static {v1}, LQQ0/p;->a(LBc/a;)LQQ0/p;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    iput-object v1, v0, LNQ0/a$b;->q:Ldagger/internal/h;

    .line 123
    .line 124
    invoke-static/range {p2 .. p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 125
    .line 126
    .line 127
    move-result-object v1

    .line 128
    iput-object v1, v0, LNQ0/a$b;->r:Ldagger/internal/h;

    .line 129
    .line 130
    invoke-static/range {p3 .. p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    iput-object v1, v0, LNQ0/a$b;->s:Ldagger/internal/h;

    .line 135
    .line 136
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 137
    .line 138
    .line 139
    move-result-object v1

    .line 140
    iput-object v1, v0, LNQ0/a$b;->t:Ldagger/internal/h;

    .line 141
    .line 142
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    iput-object v1, v0, LNQ0/a$b;->u:Ldagger/internal/h;

    .line 147
    .line 148
    invoke-static/range {p4 .. p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    iput-object v1, v0, LNQ0/a$b;->v:Ldagger/internal/h;

    .line 153
    .line 154
    invoke-static/range {p5 .. p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    iput-object v1, v0, LNQ0/a$b;->w:Ldagger/internal/h;

    .line 159
    .line 160
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 161
    .line 162
    .line 163
    move-result-object v15

    .line 164
    iput-object v15, v0, LNQ0/a$b;->x:Ldagger/internal/h;

    .line 165
    .line 166
    iget-object v2, v0, LNQ0/a$b;->i:Ldagger/internal/h;

    .line 167
    .line 168
    iget-object v3, v0, LNQ0/a$b;->j:Ldagger/internal/h;

    .line 169
    .line 170
    iget-object v4, v0, LNQ0/a$b;->k:Ldagger/internal/h;

    .line 171
    .line 172
    iget-object v5, v0, LNQ0/a$b;->o:Ldagger/internal/h;

    .line 173
    .line 174
    iget-object v6, v0, LNQ0/a$b;->p:Ldagger/internal/h;

    .line 175
    .line 176
    iget-object v7, v0, LNQ0/a$b;->q:Ldagger/internal/h;

    .line 177
    .line 178
    iget-object v8, v0, LNQ0/a$b;->r:Ldagger/internal/h;

    .line 179
    .line 180
    iget-object v9, v0, LNQ0/a$b;->s:Ldagger/internal/h;

    .line 181
    .line 182
    iget-object v10, v0, LNQ0/a$b;->t:Ldagger/internal/h;

    .line 183
    .line 184
    iget-object v11, v0, LNQ0/a$b;->u:Ldagger/internal/h;

    .line 185
    .line 186
    iget-object v12, v0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 187
    .line 188
    iget-object v13, v0, LNQ0/a$b;->v:Ldagger/internal/h;

    .line 189
    .line 190
    iget-object v14, v0, LNQ0/a$b;->w:Ldagger/internal/h;

    .line 191
    .line 192
    invoke-static/range {v2 .. v15}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/n;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/n;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    iput-object v1, v0, LNQ0/a$b;->y:Ldagger/internal/h;

    .line 197
    .line 198
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 199
    .line 200
    invoke-static {v1}, LQQ0/n;->a(LBc/a;)LQQ0/n;

    .line 201
    .line 202
    .line 203
    move-result-object v1

    .line 204
    iput-object v1, v0, LNQ0/a$b;->z:Ldagger/internal/h;

    .line 205
    .line 206
    iget-object v1, v0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 207
    .line 208
    invoke-static {v1}, LQQ0/l;->a(LBc/a;)LQQ0/l;

    .line 209
    .line 210
    .line 211
    move-result-object v1

    .line 212
    iput-object v1, v0, LNQ0/a$b;->A:Ldagger/internal/h;

    .line 213
    .line 214
    return-void
.end method

.method public final f(LQW0/c;Ljava/lang/String;Ljava/lang/String;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LSX0/a;LHX0/e;Lc8/h;)V
    .locals 13

    .line 1
    iget-object p1, p0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-static {p1}, LQQ0/b;->a(LBc/a;)LQQ0/b;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iput-object p1, p0, LNQ0/a$b;->B:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object p1, p0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static {p1}, LQQ0/t;->a(LBc/a;)LQQ0/t;

    .line 12
    .line 13
    .line 14
    move-result-object v7

    .line 15
    iput-object v7, p0, LNQ0/a$b;->C:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v0, p0, LNQ0/a$b;->k:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v1, p0, LNQ0/a$b;->z:Ldagger/internal/h;

    .line 20
    .line 21
    iget-object v2, p0, LNQ0/a$b;->A:Ldagger/internal/h;

    .line 22
    .line 23
    iget-object v3, p0, LNQ0/a$b;->n:Ldagger/internal/h;

    .line 24
    .line 25
    iget-object v4, p0, LNQ0/a$b;->m:Ldagger/internal/h;

    .line 26
    .line 27
    iget-object v5, p0, LNQ0/a$b;->B:Ldagger/internal/h;

    .line 28
    .line 29
    iget-object v6, p0, LNQ0/a$b;->l:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object v8, p0, LNQ0/a$b;->j:Ldagger/internal/h;

    .line 32
    .line 33
    iget-object v9, p0, LNQ0/a$b;->t:Ldagger/internal/h;

    .line 34
    .line 35
    iget-object v10, p0, LNQ0/a$b;->v:Ldagger/internal/h;

    .line 36
    .line 37
    iget-object v11, p0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object v12, p0, LNQ0/a$b;->w:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static/range {v0 .. v12}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/m;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/m;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, LNQ0/a$b;->D:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object p1, p0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static {p1}, LQQ0/d;->a(LBc/a;)LQQ0/d;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iput-object p1, p0, LNQ0/a$b;->E:Ldagger/internal/h;

    .line 54
    .line 55
    iget-object v0, p0, LNQ0/a$b;->A:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object v1, p0, LNQ0/a$b;->z:Ldagger/internal/h;

    .line 58
    .line 59
    iget-object v2, p0, LNQ0/a$b;->w:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object v3, p0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static {p1, v0, v1, v2, v3}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/k;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/k;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, LNQ0/a$b;->F:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object p1, p0, LNQ0/a$b;->h:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static {p1}, LQQ0/f;->a(LBc/a;)LQQ0/f;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    iput-object p1, p0, LNQ0/a$b;->G:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object v0, p0, LNQ0/a$b;->A:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object v1, p0, LNQ0/a$b;->z:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object v2, p0, LNQ0/a$b;->w:Ldagger/internal/h;

    .line 82
    .line 83
    iget-object v3, p0, LNQ0/a$b;->f:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {p1, v0, v1, v2, v3}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/k;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/k;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    iput-object p1, p0, LNQ0/a$b;->H:Ldagger/internal/h;

    .line 90
    .line 91
    return-void
.end method

.method public final g(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNQ0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/j;->a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNQ0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/f;->a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final i(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNQ0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/f;->a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final j(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;)Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNQ0/a$b;->l()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/g;->b(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LNQ0/a$b;->a:LSX0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/g;->a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossFragment;LSX0/a;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final k()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x4

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/TennisWinLossViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LNQ0/a$b;->y:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/filter/FilterWinLossViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LNQ0/a$b;->D:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/match_types/MatchTypesBottomSheetViewModel;

    .line 23
    .line 24
    iget-object v2, p0, LNQ0/a$b;->F:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const-class v1, Lorg/xbet/statistic/tennis/impl/wins_and_losses/presentation/seasons/SeasonsBottomSheetViewModel;

    .line 31
    .line 32
    iget-object v2, p0, LNQ0/a$b;->H:Ldagger/internal/h;

    .line 33
    .line 34
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    return-object v0
.end method

.method public final l()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LNQ0/a$b;->k()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
