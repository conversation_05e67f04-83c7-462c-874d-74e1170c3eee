.class public final enum Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0007\u0008\u0080\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "PRIZE",
        "STAGE",
        "PROVIDER",
        "OTHER",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

.field public static final enum OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

.field public static final enum PRIZE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

.field public static final enum PROVIDER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

.field public static final enum STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 2
    .line 3
    const-string v1, "PRIZE"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PRIZE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 12
    .line 13
    const-string v1, "STAGE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 20
    .line 21
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 22
    .line 23
    const-string v1, "PROVIDER"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PROVIDER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 32
    .line 33
    const-string v1, "OTHER"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 40
    .line 41
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->a()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->$VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->$ENTRIES:Lkotlin/enums/a;

    .line 52
    .line 53
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;
    .locals 3

    .line 1
    const/4 v0, 0x4

    new-array v0, v0, [Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PRIZE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PROVIDER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->$VALUES:[Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 8
    .line 9
    return-object v0
.end method
