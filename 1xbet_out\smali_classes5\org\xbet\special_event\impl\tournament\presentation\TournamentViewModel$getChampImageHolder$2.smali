.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$getChampImageHolder$2"
    f = "TournamentViewModel.kt"
    l = {
        0x648
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->q4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 33

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_2

    .line 11
    .line 12
    if-ne v2, v3, :cond_1

    .line 13
    .line 14
    iget-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$3:Ljava/lang/Object;

    .line 15
    .line 16
    check-cast v2, LZx0/k;

    .line 17
    .line 18
    iget-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$2:Ljava/lang/Object;

    .line 19
    .line 20
    iget-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$1:Ljava/lang/Object;

    .line 21
    .line 22
    check-cast v5, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 23
    .line 24
    iget-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$0:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v6, Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-object/from16 v7, p1

    .line 32
    .line 33
    :cond_0
    move-object v8, v2

    .line 34
    goto :goto_0

    .line 35
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 36
    .line 37
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 38
    .line 39
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    throw v1

    .line 43
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    iget-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 47
    .line 48
    invoke-static {v2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->b4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/flow/V;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    iget-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 53
    .line 54
    move-object v6, v2

    .line 55
    move-object v5, v4

    .line 56
    :cond_3
    invoke-interface {v6}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    move-object v2, v4

    .line 61
    check-cast v2, LZx0/k;

    .line 62
    .line 63
    invoke-static {v5}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->I3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LJZ/a;

    .line 64
    .line 65
    .line 66
    move-result-object v7

    .line 67
    iput-object v6, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$0:Ljava/lang/Object;

    .line 68
    .line 69
    iput-object v5, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$1:Ljava/lang/Object;

    .line 70
    .line 71
    iput-object v4, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$2:Ljava/lang/Object;

    .line 72
    .line 73
    iput-object v2, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->L$3:Ljava/lang/Object;

    .line 74
    .line 75
    iput v3, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$getChampImageHolder$2;->label:I

    .line 76
    .line 77
    invoke-interface {v7, v0}, LJZ/a;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v7

    .line 81
    if-ne v7, v1, :cond_0

    .line 82
    .line 83
    return-object v1

    .line 84
    :goto_0
    move-object/from16 v29, v7

    .line 85
    .line 86
    check-cast v29, LNZ/a;

    .line 87
    .line 88
    const v31, 0x2fffff

    .line 89
    .line 90
    .line 91
    const/16 v32, 0x0

    .line 92
    .line 93
    const/4 v9, 0x0

    .line 94
    const/4 v10, 0x0

    .line 95
    const/4 v11, 0x0

    .line 96
    const/4 v12, 0x0

    .line 97
    const/4 v13, 0x0

    .line 98
    const/4 v14, 0x0

    .line 99
    const/4 v15, 0x0

    .line 100
    const/16 v16, 0x0

    .line 101
    .line 102
    const/16 v17, 0x0

    .line 103
    .line 104
    const/16 v18, 0x0

    .line 105
    .line 106
    const/16 v19, 0x0

    .line 107
    .line 108
    const/16 v20, 0x0

    .line 109
    .line 110
    const/16 v21, 0x0

    .line 111
    .line 112
    const/16 v22, 0x0

    .line 113
    .line 114
    const/16 v23, 0x0

    .line 115
    .line 116
    const/16 v24, 0x0

    .line 117
    .line 118
    const/16 v25, 0x0

    .line 119
    .line 120
    const/16 v26, 0x0

    .line 121
    .line 122
    const/16 v27, 0x0

    .line 123
    .line 124
    const/16 v28, 0x0

    .line 125
    .line 126
    const/16 v30, 0x0

    .line 127
    .line 128
    invoke-static/range {v8 .. v32}, LZx0/k;->b(LZx0/k;Ljava/util/List;LUw0/a;LUw0/a;LUw0/a;Lmy0/a;Ley0/a;Ljy0/a;Loy0/a;Lly0/a;Lhy0/a;Lpy0/a;Ldy0/a;Lfy0/a;Lay0/a;Lay0/b;Liy0/a;Lgy0/a;Lcy0/a;Lky0/a;Lby0/a;LNZ/a;Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;ILjava/lang/Object;)LZx0/k;

    .line 129
    .line 130
    .line 131
    move-result-object v2

    .line 132
    invoke-interface {v6, v4, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 133
    .line 134
    .line 135
    move-result v2

    .line 136
    if-eqz v2, :cond_3

    .line 137
    .line 138
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 139
    .line 140
    return-object v1
.end method
