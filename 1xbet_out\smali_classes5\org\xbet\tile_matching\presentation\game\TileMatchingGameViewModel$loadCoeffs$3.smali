.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingGameViewModel$loadCoeffs$3"
    f = "TileMatchingGameViewModel.kt"
    l = {
        0x100
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->Z3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->v3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 37
    .line 38
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->E3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    iget-object v3, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    .line 43
    .line 44
    invoke-static {v3}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->z3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)LTv/e;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    invoke-virtual {v3}, LTv/e;->j()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->L$0:Ljava/lang/Object;

    .line 53
    .line 54
    iput v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$loadCoeffs$3;->label:I

    .line 55
    .line 56
    invoke-virtual {v1, v3, p0}, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;->a(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    if-ne v1, v0, :cond_2

    .line 61
    .line 62
    return-object v0

    .line 63
    :cond_2
    move-object v0, p1

    .line 64
    move-object p1, v1

    .line 65
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 66
    .line 67
    invoke-static {v0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->u3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;Ljava/util/List;)V

    .line 68
    .line 69
    .line 70
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object p1
.end method
