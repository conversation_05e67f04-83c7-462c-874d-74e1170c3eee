.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt;->c(LHy0/b;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LGy0/c;

.field public final synthetic b:LB4/a;

.field public final synthetic c:LGy0/c;

.field public final synthetic d:LB4/a;


# direct methods
.method public constructor <init>(LGy0/c;LB4/a;LGy0/c;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->a:LGy0/c;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->b:LB4/a;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->c:LGy0/c;

    .line 6
    .line 7
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->d:LB4/a;

    .line 8
    .line 9
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->a:LGy0/c;

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->b:LB4/a;

    .line 10
    .line 11
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, LIy0/h;

    .line 16
    .line 17
    invoke-virtual {v0}, LIy0/h;->d()LIy0/h$a$a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, LIy0/h$a$a;->a()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p1, v0}, LA4/e;->setItems(Ljava/util/List;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 30
    .line 31
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 32
    .line 33
    .line 34
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    if-eqz v1, :cond_1

    .line 43
    .line 44
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    check-cast v1, Ljava/util/Collection;

    .line 49
    .line 50
    check-cast v1, Ljava/lang/Iterable;

    .line 51
    .line 52
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 53
    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 61
    .line 62
    .line 63
    move-result v0

    .line 64
    if-eqz v0, :cond_3

    .line 65
    .line 66
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    check-cast v0, LIy0/h$a;

    .line 71
    .line 72
    instance-of v0, v0, LIy0/h$a$a;

    .line 73
    .line 74
    if-eqz v0, :cond_2

    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->c:LGy0/c;

    .line 77
    .line 78
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->d:LB4/a;

    .line 79
    .line 80
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    check-cast v1, LIy0/h;

    .line 85
    .line 86
    invoke-virtual {v1}, LIy0/h;->d()LIy0/h$a$a;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-virtual {v1}, LIy0/h$a$a;->a()Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    invoke-virtual {v0, v1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 95
    .line 96
    .line 97
    goto :goto_1

    .line 98
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 99
    .line 100
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 101
    .line 102
    .line 103
    throw p1

    .line 104
    :cond_3
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
