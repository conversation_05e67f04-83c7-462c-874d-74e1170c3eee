.class public final synthetic LIN0/B;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Ljava/lang/String;

.field public final synthetic d:Z

.field public final synthetic e:Landroidx/compose/ui/l;

.field public final synthetic f:I

.field public final synthetic g:I


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIN0/B;->a:Ljava/lang/String;

    iput-object p2, p0, LIN0/B;->b:Ljava/lang/String;

    iput-object p3, p0, LIN0/B;->c:Ljava/lang/String;

    iput-boolean p4, p0, LIN0/B;->d:Z

    iput-object p5, p0, LIN0/B;->e:Landroidx/compose/ui/l;

    iput p6, p0, LIN0/B;->f:I

    iput p7, p0, LIN0/B;->g:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    iget-object v0, p0, LIN0/B;->a:Ljava/lang/String;

    iget-object v1, p0, LIN0/B;->b:Ljava/lang/String;

    iget-object v2, p0, LIN0/B;->c:Ljava/lang/String;

    iget-boolean v3, p0, LIN0/B;->d:Z

    iget-object v4, p0, LIN0/B;->e:Landroidx/compose/ui/l;

    iget v5, p0, LIN0/B;->f:I

    iget v6, p0, LIN0/B;->g:I

    move-object v7, p1

    check-cast v7, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v8

    invoke-static/range {v0 .. v8}, LIN0/C;->b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
