.class public final Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;
.super Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment<",
        "LC7/b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0011\u0008\u0000\u0018\u0000 82\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u00019B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u000f\u0010\u000e\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u0004J\u000f\u0010\u000f\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0004J\u000f\u0010\u0010\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0004R\"\u0010\u0018\u001a\u00020\u00118\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0012\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015\"\u0004\u0008\u0016\u0010\u0017R\u001b\u0010\u001e\u001a\u00020\u00198BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001dR\u001b\u0010#\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R\u001b\u0010(\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010\u001b\u001a\u0004\u0008&\u0010\'R+\u00101\u001a\u00020)2\u0006\u0010*\u001a\u00020)8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\"\u0004\u0008/\u00100R+\u00107\u001a\u00020\u00052\u0006\u0010*\u001a\u00020\u00058B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105\"\u0004\u00086\u0010\t\u00a8\u0006:"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;",
        "Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;",
        "LC7/b;",
        "<init>",
        "()V",
        "",
        "promoCode",
        "",
        "Z2",
        "(Ljava/lang/String;)V",
        "",
        "D2",
        "()I",
        "q2",
        "z2",
        "y2",
        "Y2",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "k0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "X2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;",
        "l0",
        "Lkotlin/j;",
        "W2",
        "()Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;",
        "viewModel",
        "m0",
        "LRc/c;",
        "T2",
        "()LC7/b;",
        "binding",
        "LOx/a;",
        "n0",
        "S2",
        "()LOx/a;",
        "adapter",
        "",
        "<set-?>",
        "o0",
        "LeX0/a;",
        "U2",
        "()Z",
        "setFromMakeBet",
        "(Z)V",
        "fromMakeBet",
        "b1",
        "LeX0/k;",
        "V2",
        "()Ljava/lang/String;",
        "setRequestKey",
        "requestKey",
        "k1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k1:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 4
    .line 5
    const-string v2, "binding"

    .line 6
    .line 7
    const-string v3, "getBinding()Lcom/xbet/coupon/impl/databinding/BottomsheetFragmentPromocodeBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "fromMakeBet"

    .line 20
    .line 21
    const-string v5, "getFromMakeBet()Z"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "requestKey"

    .line 33
    .line 34
    const-string v6, "getRequestKey()Ljava/lang/String;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->v1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->k1:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/a;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lorg/xbet/coupon/impl/promocode/presentation/a;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)V

    .line 7
    .line 8
    .line 9
    new-instance v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$1;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 12
    .line 13
    .line 14
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 15
    .line 16
    new-instance v3, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$2;

    .line 17
    .line 18
    invoke-direct {v3, v1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    const-class v3, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 26
    .line 27
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    new-instance v4, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$3;

    .line 32
    .line 33
    invoke-direct {v4, v1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 34
    .line 35
    .line 36
    new-instance v5, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$4;

    .line 37
    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-direct {v5, v6, v1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 40
    .line 41
    .line 42
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->l0:Lkotlin/j;

    .line 47
    .line 48
    sget-object v0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$binding$2;->INSTANCE:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$binding$2;

    .line 49
    .line 50
    invoke-static {p0, v0}, LLX0/j;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    iput-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->m0:LRc/c;

    .line 55
    .line 56
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/b;

    .line 57
    .line 58
    invoke-direct {v0, p0}, Lorg/xbet/coupon/impl/promocode/presentation/b;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)V

    .line 59
    .line 60
    .line 61
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    iput-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->n0:Lkotlin/j;

    .line 66
    .line 67
    new-instance v0, LeX0/a;

    .line 68
    .line 69
    const-string v1, "FROM_MAKE_BET_KEY"

    .line 70
    .line 71
    const/4 v2, 0x0

    .line 72
    const/4 v3, 0x2

    .line 73
    invoke-direct {v0, v1, v2, v3, v6}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 74
    .line 75
    .line 76
    iput-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->o0:LeX0/a;

    .line 77
    .line 78
    new-instance v0, LeX0/k;

    .line 79
    .line 80
    const-string v1, "REQUEST_KEY"

    .line 81
    .line 82
    invoke-direct {v0, v1, v6, v3, v6}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 83
    .line 84
    .line 85
    iput-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->b1:LeX0/k;

    .line 86
    .line 87
    return-void
.end method

.method public static synthetic N2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->R2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic O2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->a3(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic P2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->S2()LOx/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Q2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->Z2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final R2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;
    .locals 2

    .line 1
    new-instance v0, LOx/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$adapter$2$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$adapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, LOx/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method private final V2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->b1:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final Z2(Ljava/lang/String;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->V2()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->V2()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    const/4 v1, 0x1

    .line 14
    new-array v1, v1, [Lkotlin/Pair;

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    aput-object p1, v1, v2

    .line 18
    .line 19
    invoke-static {v1}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {p0, v0, p1}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final a3(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->X2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public D2()I
    .locals 1

    .line 1
    sget v0, LB7/b;->parent:I

    .line 2
    .line 3
    return v0
.end method

.method public final S2()LOx/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LOx/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public T2()LC7/b;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->m0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LC7/b;

    .line 13
    .line 14
    return-object v0
.end method

.method public final U2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->o0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final W2()Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final X2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->k0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Y2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->W2()Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->C3()Lkotlinx/coroutines/flow/f0;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public q2()I
    .locals 1

    .line 1
    sget v0, Lpb/c;->contentBackground:I

    .line 2
    .line 3
    return v0
.end method

.method public bridge synthetic u2()LL2/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public y2()V
    .locals 3

    .line 1
    invoke-super {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->y2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LC7/b;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->S2()LOx/a;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 15
    .line 16
    .line 17
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/j;

    .line 18
    .line 19
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    sget v2, Lpb/g;->divider_drawable_opacity_with_spaces:I

    .line 24
    .line 25
    invoke-static {v1, v2}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/j;-><init>(Landroid/graphics/drawable/Drawable;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    iget-object v1, v1, LC7/b;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 37
    .line 38
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->Y2()V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public z2()V
    .locals 4

    .line 1
    invoke-super {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->z2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LPx/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LPx/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LPx/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    const/4 v0, 0x1

    .line 56
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->U2()Z

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    invoke-virtual {v2, v0, v1}, LPx/d;->a(ZZ)LPx/c;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    invoke-interface {v0, p0}, LPx/c;->a(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)V

    .line 65
    .line 66
    .line 67
    return-void

    .line 68
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 69
    .line 70
    new-instance v2, Ljava/lang/StringBuilder;

    .line 71
    .line 72
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 73
    .line 74
    .line 75
    const-string v3, "Cannot create dependency "

    .line 76
    .line 77
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    throw v0
.end method
