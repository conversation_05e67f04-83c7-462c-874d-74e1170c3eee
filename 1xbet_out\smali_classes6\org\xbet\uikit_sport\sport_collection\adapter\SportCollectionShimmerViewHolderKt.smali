.class public final Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LP31/i;",
        "e",
        "()LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;->i(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;->h(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;->g(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/Q;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/Q;

    move-result-object p0

    return-object p0
.end method

.method public static final e()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LP31/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LO31/c;

    .line 2
    .line 3
    invoke-direct {v0}, LO31/c;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LO31/d;

    .line 7
    .line 8
    invoke-direct {v1}, LO31/d;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt$sportCollectionShimmerDelegateAdapter$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt$sportCollectionShimmerDelegateAdapter$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt$sportCollectionShimmerDelegateAdapter$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt$sportCollectionShimmerDelegateAdapter$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/Q;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/Q;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/Q;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LO31/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LO31/e;-><init>(LB4/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    new-instance v0, LO31/f;

    .line 10
    .line 11
    invoke-direct {v0, p0}, LO31/f;-><init>(LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final h(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LC31/Q;

    .line 6
    .line 7
    iget-object p0, p0, LC31/Q;->b:Landroid/widget/FrameLayout;

    .line 8
    .line 9
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final i(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LC31/Q;

    .line 6
    .line 7
    iget-object p0, p0, LC31/Q;->b:Landroid/widget/FrameLayout;

    .line 8
    .line 9
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method
