.class public final LtK0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtK0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtK0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LtK0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LtK0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;LSX0/a;Lc8/h;)LtK0/c;
    .locals 9

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static {p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static {p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static {p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    new-instance v0, LtK0/a$b;

    .line 23
    .line 24
    const/4 v8, 0x0

    .line 25
    move-object v1, p1

    .line 26
    move-object v2, p2

    .line 27
    move-object v3, p3

    .line 28
    move-object v4, p4

    .line 29
    move-object v5, p5

    .line 30
    move-object v6, p6

    .line 31
    move-object/from16 v7, p7

    .line 32
    .line 33
    invoke-direct/range {v0 .. v8}, LtK0/a$b;-><init>(LQW0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;LSX0/a;Lc8/h;LtK0/b;)V

    .line 34
    .line 35
    .line 36
    return-object v0
.end method
