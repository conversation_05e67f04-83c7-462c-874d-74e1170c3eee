.class public interface abstract LH1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a(J)Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Lcom/google/common/collect/ImmutableList<",
            "Ls1/a;",
            ">;"
        }
    .end annotation
.end method

.method public abstract b(J)J
.end method

.method public abstract c(J)V
.end method

.method public abstract clear()V
.end method

.method public abstract d(J)J
.end method

.method public abstract e(Lk2/e;J)Z
.end method
