.class public final LsQ0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LtQ0/e;",
        "Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a;",
        "a",
        "(LtQ0/e;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LtQ0/e;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a;
    .locals 1
    .param p0    # LtQ0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtQ0/e;->f()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, LtQ0/e;->e()LnQ0/b;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    sget-object p0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$c;->a:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$c;

    .line 14
    .line 15
    return-object p0

    .line 16
    :cond_0
    invoke-virtual {p0}, LtQ0/e;->e()LnQ0/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {p0}, LtQ0/e;->e()LnQ0/b;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, LnQ0/b;->c()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-eqz v0, :cond_1

    .line 35
    .line 36
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$a;

    .line 37
    .line 38
    invoke-virtual {p0}, LtQ0/e;->c()Lorg/xbet/uikit/components/lottie/a;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-direct {v0, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$a;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 43
    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_1
    invoke-virtual {p0}, LtQ0/e;->e()LnQ0/b;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    if-eqz v0, :cond_2

    .line 51
    .line 52
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$d;

    .line 53
    .line 54
    invoke-virtual {p0}, LtQ0/e;->e()LnQ0/b;

    .line 55
    .line 56
    .line 57
    move-result-object p0

    .line 58
    invoke-static {p0}, LsQ0/b;->a(LnQ0/b;)Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    invoke-direct {v0, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$d;-><init>(Ljava/util/List;)V

    .line 63
    .line 64
    .line 65
    return-object v0

    .line 66
    :cond_2
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$b;

    .line 67
    .line 68
    invoke-virtual {p0}, LtQ0/e;->d()Lorg/xbet/uikit/components/lottie/a;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    invoke-direct {v0, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersMenuViewModel$a$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 73
    .line 74
    .line 75
    return-object v0
.end method
