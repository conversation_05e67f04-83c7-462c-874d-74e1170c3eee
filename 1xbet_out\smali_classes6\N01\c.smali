.class public final synthetic LN01/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/c;->a:Lkotlin/jvm/functions/Function0;

    iput-object p2, p0, LN01/c;->b:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LN01/c;->a:Lkotlin/jvm/functions/Function0;

    iget-object v1, p0, LN01/c;->b:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;

    invoke-static {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;->x(Lkotlin/jvm/functions/Function0;Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
