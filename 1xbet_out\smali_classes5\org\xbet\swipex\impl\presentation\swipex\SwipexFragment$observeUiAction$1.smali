.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexFragment$observeUiAction$1"
    f = "SwipexFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->n3(Lkotlinx/coroutines/flow/e;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;",
        "action",
        "",
        "<anonymous>",
        "(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;

    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->invoke(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_c

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;

    .line 16
    .line 17
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$h;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$h;

    .line 18
    .line 19
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    if-eqz v2, :cond_0

    .line 24
    .line 25
    sget-object v1, Lorg/xbet/swipex/impl/presentation/onboarding/SwipeXOnboardingBottomSheetDialog;->b1:Lorg/xbet/swipex/impl/presentation/onboarding/SwipeXOnboardingBottomSheetDialog$a;

    .line 26
    .line 27
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 28
    .line 29
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual {v1, v2}, Lorg/xbet/swipex/impl/presentation/onboarding/SwipeXOnboardingBottomSheetDialog$a;->a(Landroidx/fragment/app/FragmentManager;)V

    .line 34
    .line 35
    .line 36
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 37
    .line 38
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    iget-object v1, v1, LwS0/q;->s:Landroid/view/View;

    .line 43
    .line 44
    const/4 v2, 0x0

    .line 45
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 46
    .line 47
    .line 48
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 49
    .line 50
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    iget-object v1, v1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 55
    .line 56
    const/16 v2, 0x8

    .line 57
    .line 58
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 59
    .line 60
    .line 61
    goto/16 :goto_0

    .line 62
    .line 63
    :cond_0
    instance-of v2, v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$b;

    .line 64
    .line 65
    if-eqz v2, :cond_1

    .line 66
    .line 67
    sget-object v1, Lorg/xbet/swipex/impl/presentation/dialogs/change_bet_sum/SwipexChangeBetValueBottomSheetDialog;->n0:Lorg/xbet/swipex/impl/presentation/dialogs/change_bet_sum/SwipexChangeBetValueBottomSheetDialog$a;

    .line 68
    .line 69
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 70
    .line 71
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    invoke-virtual {v1, v2}, Lorg/xbet/swipex/impl/presentation/dialogs/change_bet_sum/SwipexChangeBetValueBottomSheetDialog$a;->a(Landroidx/fragment/app/FragmentManager;)V

    .line 76
    .line 77
    .line 78
    goto/16 :goto_0

    .line 79
    .line 80
    :cond_1
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$f;

    .line 81
    .line 82
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    move-result v2

    .line 86
    if-eqz v2, :cond_2

    .line 87
    .line 88
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 89
    .line 90
    invoke-virtual {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Y2()LTZ0/a;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 95
    .line 96
    iget-object v3, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 97
    .line 98
    sget v4, Lpb/k;->suggest_to_change_filters:I

    .line 99
    .line 100
    invoke-virtual {v3, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    iget-object v4, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 105
    .line 106
    sget v5, Lpb/k;->swipe_x_change_filter_text:I

    .line 107
    .line 108
    invoke-virtual {v4, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v4

    .line 112
    iget-object v5, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 113
    .line 114
    sget v6, Lpb/k;->go_to_filter_screen:I

    .line 115
    .line 116
    invoke-virtual {v5, v6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v5

    .line 120
    iget-object v6, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 121
    .line 122
    sget v7, Lpb/k;->later:I

    .line 123
    .line 124
    invoke-virtual {v6, v7}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v6

    .line 128
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 129
    .line 130
    const/16 v15, 0xbd0

    .line 131
    .line 132
    const/16 v16, 0x0

    .line 133
    .line 134
    const/4 v7, 0x0

    .line 135
    const-string v8, "CHANGE_FILTERS_KEY"

    .line 136
    .line 137
    const/4 v9, 0x0

    .line 138
    const/4 v10, 0x0

    .line 139
    const/4 v11, 0x0

    .line 140
    const/4 v12, 0x0

    .line 141
    const/4 v14, 0x0

    .line 142
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 143
    .line 144
    .line 145
    iget-object v3, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 146
    .line 147
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 148
    .line 149
    .line 150
    move-result-object v3

    .line 151
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 152
    .line 153
    .line 154
    goto/16 :goto_0

    .line 155
    .line 156
    :cond_2
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$j;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$j;

    .line 157
    .line 158
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 159
    .line 160
    .line 161
    move-result v2

    .line 162
    if-eqz v2, :cond_3

    .line 163
    .line 164
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/dialogs/y;->e0:Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;

    .line 165
    .line 166
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 167
    .line 168
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 169
    .line 170
    .line 171
    move-result-object v2

    .line 172
    invoke-virtual {v1, v2}, Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;->c(Landroidx/fragment/app/FragmentManager;)V

    .line 173
    .line 174
    .line 175
    goto/16 :goto_0

    .line 176
    .line 177
    :cond_3
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$k;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$k;

    .line 178
    .line 179
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 180
    .line 181
    .line 182
    move-result v2

    .line 183
    if-eqz v2, :cond_4

    .line 184
    .line 185
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/dialogs/y;->e0:Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;

    .line 186
    .line 187
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 188
    .line 189
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 190
    .line 191
    .line 192
    move-result-object v2

    .line 193
    invoke-virtual {v1, v2}, Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;->a(Landroidx/fragment/app/FragmentManager;)V

    .line 194
    .line 195
    .line 196
    goto/16 :goto_0

    .line 197
    .line 198
    :cond_4
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$c;

    .line 199
    .line 200
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 201
    .line 202
    .line 203
    move-result v2

    .line 204
    if-eqz v2, :cond_5

    .line 205
    .line 206
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 207
    .line 208
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 209
    .line 210
    .line 211
    move-result-object v1

    .line 212
    iget-object v1, v1, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 213
    .line 214
    invoke-virtual {v1}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;->h()V

    .line 215
    .line 216
    .line 217
    goto/16 :goto_0

    .line 218
    .line 219
    :cond_5
    instance-of v2, v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$d;

    .line 220
    .line 221
    if-eqz v2, :cond_6

    .line 222
    .line 223
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 224
    .line 225
    invoke-virtual {v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->d3()LzX0/k;

    .line 226
    .line 227
    .line 228
    move-result-object v3

    .line 229
    new-instance v4, Ly01/g;

    .line 230
    .line 231
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 232
    .line 233
    check-cast v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$d;

    .line 234
    .line 235
    invoke-virtual {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$d;->a()Ljava/lang/String;

    .line 236
    .line 237
    .line 238
    move-result-object v6

    .line 239
    const/16 v11, 0x3c

    .line 240
    .line 241
    const/4 v12, 0x0

    .line 242
    const/4 v7, 0x0

    .line 243
    const/4 v8, 0x0

    .line 244
    const/4 v9, 0x0

    .line 245
    const/4 v10, 0x0

    .line 246
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 247
    .line 248
    .line 249
    iget-object v5, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 250
    .line 251
    const/16 v13, 0x1fc

    .line 252
    .line 253
    const/4 v14, 0x0

    .line 254
    const/4 v6, 0x0

    .line 255
    const/4 v8, 0x0

    .line 256
    const/4 v9, 0x0

    .line 257
    const/4 v11, 0x0

    .line 258
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 259
    .line 260
    .line 261
    goto/16 :goto_0

    .line 262
    .line 263
    :cond_6
    instance-of v2, v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$e;

    .line 264
    .line 265
    if-eqz v2, :cond_7

    .line 266
    .line 267
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 268
    .line 269
    invoke-virtual {v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->d3()LzX0/k;

    .line 270
    .line 271
    .line 272
    move-result-object v3

    .line 273
    new-instance v4, Ly01/g;

    .line 274
    .line 275
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 276
    .line 277
    check-cast v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$e;

    .line 278
    .line 279
    invoke-virtual {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$e;->a()Ljava/lang/String;

    .line 280
    .line 281
    .line 282
    move-result-object v6

    .line 283
    invoke-virtual {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$e;->b()I

    .line 284
    .line 285
    .line 286
    move-result v1

    .line 287
    invoke-static {v1}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 288
    .line 289
    .line 290
    move-result-object v10

    .line 291
    const/16 v11, 0x1c

    .line 292
    .line 293
    const/4 v12, 0x0

    .line 294
    const/4 v7, 0x0

    .line 295
    const/4 v8, 0x0

    .line 296
    const/4 v9, 0x0

    .line 297
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 298
    .line 299
    .line 300
    iget-object v5, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 301
    .line 302
    const/16 v13, 0x1fc

    .line 303
    .line 304
    const/4 v14, 0x0

    .line 305
    const/4 v6, 0x0

    .line 306
    const/4 v8, 0x0

    .line 307
    const/4 v9, 0x0

    .line 308
    const/4 v10, 0x0

    .line 309
    const/4 v11, 0x0

    .line 310
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 311
    .line 312
    .line 313
    goto :goto_0

    .line 314
    :cond_7
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$g;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$g;

    .line 315
    .line 316
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 317
    .line 318
    .line 319
    move-result v2

    .line 320
    if-eqz v2, :cond_8

    .line 321
    .line 322
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 323
    .line 324
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->S2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 325
    .line 326
    .line 327
    goto :goto_0

    .line 328
    :cond_8
    instance-of v2, v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;

    .line 329
    .line 330
    if-eqz v2, :cond_a

    .line 331
    .line 332
    sget v2, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 333
    .line 334
    const/16 v3, 0x1e

    .line 335
    .line 336
    if-lt v2, v3, :cond_9

    .line 337
    .line 338
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 339
    .line 340
    invoke-static {v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;

    .line 341
    .line 342
    .line 343
    move-result-object v2

    .line 344
    invoke-virtual {v2}, LwS0/q;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 345
    .line 346
    .line 347
    move-result-object v2

    .line 348
    const/16 v3, 0x10

    .line 349
    .line 350
    invoke-virtual {v2, v3}, Landroid/view/View;->performHapticFeedback(I)Z

    .line 351
    .line 352
    .line 353
    :cond_9
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 354
    .line 355
    invoke-virtual {v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->d3()LzX0/k;

    .line 356
    .line 357
    .line 358
    move-result-object v3

    .line 359
    new-instance v4, Ly01/g;

    .line 360
    .line 361
    sget-object v5, Ly01/i$b;->a:Ly01/i$b;

    .line 362
    .line 363
    iget-object v2, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 364
    .line 365
    check-cast v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;

    .line 366
    .line 367
    invoke-static {v2, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->O2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;)Ljava/lang/String;

    .line 368
    .line 369
    .line 370
    move-result-object v6

    .line 371
    const/16 v11, 0x3c

    .line 372
    .line 373
    const/4 v12, 0x0

    .line 374
    const/4 v7, 0x0

    .line 375
    const/4 v8, 0x0

    .line 376
    const/4 v9, 0x0

    .line 377
    const/4 v10, 0x0

    .line 378
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 379
    .line 380
    .line 381
    iget-object v5, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 382
    .line 383
    const/16 v13, 0x1fc

    .line 384
    .line 385
    const/4 v14, 0x0

    .line 386
    const/4 v6, 0x0

    .line 387
    const/4 v8, 0x0

    .line 388
    const/4 v9, 0x0

    .line 389
    const/4 v11, 0x0

    .line 390
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 391
    .line 392
    .line 393
    goto :goto_0

    .line 394
    :cond_a
    sget-object v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$a;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$a;

    .line 395
    .line 396
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 397
    .line 398
    .line 399
    move-result v1

    .line 400
    if-eqz v1, :cond_b

    .line 401
    .line 402
    iget-object v1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 403
    .line 404
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->L2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;

    .line 405
    .line 406
    .line 407
    move-result-object v1

    .line 408
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 409
    .line 410
    .line 411
    move-result-object v2

    .line 412
    invoke-virtual {v1, v2}, LA4/e;->setItems(Ljava/util/List;)V

    .line 413
    .line 414
    .line 415
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 416
    .line 417
    return-object v1

    .line 418
    :cond_b
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 419
    .line 420
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 421
    .line 422
    .line 423
    throw v1

    .line 424
    :cond_c
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 425
    .line 426
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 427
    .line 428
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 429
    .line 430
    .line 431
    throw v1
.end method
