.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a)\u0010\u0006\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a1\u0010\u000c\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r*$\u0008\u0000\u0010\u000e\"\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u00082\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008\u00a8\u0006\u000f"
    }
    d2 = {
        "Lkotlin/Function0;",
        "",
        "onClick",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function0;)LA4/c;",
        "LB4/a;",
        "Lza1/a$h$a;",
        "LS91/N0;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolder;",
        "i",
        "(LB4/a;Lkotlin/jvm/functions/Function0;)V",
        "PromoPlainTournamentViewHolder",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->j(Lkotlin/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/N0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/N0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->h(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->g(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function0;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LDa1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LDa1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LDa1/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LDa1/b;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt$promoPlainTournamentViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt$promoPlainTournamentViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt$promoPlainTournamentViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt$promoPlainTournamentViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/N0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/N0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/N0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p1, p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoPlainTournamentViewHolderKt;->i(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    new-instance p0, LDa1/c;

    .line 5
    .line 6
    invoke-direct {p0, p1}, LDa1/c;-><init>(LB4/a;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final h(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LS91/N0;

    .line 6
    .line 7
    iget-object p1, p1, LS91/N0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/DsAggregatorTournamentsBannerOld;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lza1/a$h$a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lza1/a$h$a;->e()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/DsAggregatorTournamentsBannerOld;->setStyle(Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LS91/N0;

    .line 27
    .line 28
    iget-object p1, p1, LS91/N0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/DsAggregatorTournamentsBannerOld;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    check-cast p0, Lza1/a$h$a;

    .line 35
    .line 36
    invoke-virtual {p0}, Lza1/a$h$a;->d()Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/a;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-virtual {p1, p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/DsAggregatorTournamentsBannerOld;->setModel(Lorg/xbet/uikit_aggregator/aggregatorTournamentsBannerOld/a;)V

    .line 41
    .line 42
    .line 43
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 44
    .line 45
    return-object p0
.end method

.method public static final i(LB4/a;Lkotlin/jvm/functions/Function0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$h$a;",
            "LS91/N0;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LS91/N0;

    .line 6
    .line 7
    invoke-virtual {p0}, LS91/N0;->b()Landroid/widget/FrameLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    new-instance v0, LDa1/d;

    .line 12
    .line 13
    invoke-direct {v0, p1}, LDa1/d;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 14
    .line 15
    .line 16
    const/4 p1, 0x1

    .line 17
    const/4 v1, 0x0

    .line 18
    invoke-static {p0, v1, v0, p1, v1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public static final j(Lkotlin/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method
