.class public final LG4/d;
.super Ljava/lang/Object;


# static fields
.field public static HuaweiIdAuthButton:[I = null

.field public static HuaweiIdAuthButton_hwid_button_theme:I = 0x0

.field public static HuaweiIdAuthButton_hwid_color_policy:I = 0x1

.field public static HuaweiIdAuthButton_hwid_corner_radius:I = 0x2


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    .line 1
    const v0, 0x7f0403fb

    .line 2
    .line 3
    .line 4
    const v1, 0x7f0403fc

    .line 5
    .line 6
    .line 7
    const v2, 0x7f0403fa

    .line 8
    .line 9
    .line 10
    filled-new-array {v2, v0, v1}, [I

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    sput-object v0, LG4/d;->HuaweiIdAuthButton:[I

    .line 15
    .line 16
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
