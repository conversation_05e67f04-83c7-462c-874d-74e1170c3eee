.class public final synthetic Lorg/xbet/themesettings/impl/presentation/theme/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/b;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    return-void
.end method


# virtual methods
.method public final onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/b;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    invoke-static {v0, p1, p2}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->B2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Landroid/widget/CompoundButton;Z)V

    return-void
.end method
