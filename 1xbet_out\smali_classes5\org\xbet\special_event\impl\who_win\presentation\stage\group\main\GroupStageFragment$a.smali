.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0006\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u000b\u001a\u0004\u0008\u000c\u0010\rR\u0014\u0010\u000e\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000b\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
        "params",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;",
        "b",
        "(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;",
        "",
        "SCREEN_NAME",
        "Ljava/lang/String;",
        "a",
        "()Ljava/lang/String;",
        "PARAMS_KEY",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;->H2()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final b(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;
    .locals 3
    .param p1    # Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/GroupStageFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    const-string v1, "PARAMS_KEY"

    .line 7
    .line 8
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    const/4 v1, 0x1

    .line 13
    new-array v1, v1, [Lkotlin/Pair;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    aput-object p1, v1, v2

    .line 17
    .line 18
    invoke-static {v1}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-virtual {v0, p1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method
