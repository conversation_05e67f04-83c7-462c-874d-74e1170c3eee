.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getButtonHelp()Lorg/xbet/uikit/components/buttons/DSButton;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/lang/Object;",
        "Ljava/lang/Boolean;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;

    invoke-direct {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;-><init>()V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Object;)Ljava/lang/Boolean;
    .locals 0

    .line 1
    instance-of p1, p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;->a(Ljava/lang/Object;)Ljava/lang/Boolean;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method
