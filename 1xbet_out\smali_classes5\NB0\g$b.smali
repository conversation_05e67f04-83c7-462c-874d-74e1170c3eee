.class public final LNB0/g$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/compose/ui/layout/J;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LNB0/g;->d(Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LNB0/g$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LNB0/g$b;

    .line 2
    .line 3
    invoke-direct {v0}, LNB0/g$b;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LNB0/g$b;->a:LNB0/g$b;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic e(Ljava/util/List;ILandroidx/compose/ui/layout/g0$a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LNB0/g$b;->g(Ljava/util/List;ILandroidx/compose/ui/layout/g0$a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Ljava/util/List;ILandroidx/compose/ui/layout/g0$a;)Lkotlin/Unit;
    .locals 11

    .line 1
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v5, 0x0

    .line 8
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 9
    .line 10
    .line 11
    move-result v3

    .line 12
    if-eqz v3, :cond_2

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    add-int/lit8 v10, v2, 0x1

    .line 19
    .line 20
    if-gez v2, :cond_0

    .line 21
    .line 22
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 23
    .line 24
    .line 25
    :cond_0
    move-object v4, v3

    .line 26
    check-cast v4, Landroidx/compose/ui/layout/g0;

    .line 27
    .line 28
    const/4 v8, 0x4

    .line 29
    const/4 v9, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x0

    .line 32
    move-object v3, p2

    .line 33
    invoke-static/range {v3 .. v9}, Landroidx/compose/ui/layout/g0$a;->m(Landroidx/compose/ui/layout/g0$a;Landroidx/compose/ui/layout/g0;IIFILjava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v4}, Landroidx/compose/ui/layout/g0;->F0()I

    .line 37
    .line 38
    .line 39
    move-result p2

    .line 40
    invoke-static {p0}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    if-eq v2, v4, :cond_1

    .line 45
    .line 46
    move v2, p1

    .line 47
    goto :goto_1

    .line 48
    :cond_1
    const/4 v2, 0x0

    .line 49
    :goto_1
    add-int/2addr p2, v2

    .line 50
    add-int/2addr v5, p2

    .line 51
    move-object p2, v3

    .line 52
    move v2, v10

    .line 53
    goto :goto_0

    .line 54
    :cond_2
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 55
    .line 56
    return-object p0
.end method


# virtual methods
.method public final a(Landroidx/compose/ui/layout/N;Ljava/util/List;J)Landroidx/compose/ui/layout/L;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/layout/N;",
            "Ljava/util/List<",
            "+",
            "Landroidx/compose/ui/layout/H;",
            ">;J)",
            "Landroidx/compose/ui/layout/L;"
        }
    .end annotation

    .line 1
    sget-object v0, LA11/a;->a:LA11/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-interface {p1, v0}, Lt0/e;->m1(F)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-static/range {p3 .. p4}, Lt0/b;->l(J)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    add-int/lit8 v2, v2, -0x1

    .line 20
    .line 21
    mul-int v2, v2, v0

    .line 22
    .line 23
    sub-int/2addr v1, v2

    .line 24
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    div-int v5, v1, v2

    .line 29
    .line 30
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    if-eqz v2, :cond_3

    .line 39
    .line 40
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    check-cast v2, Landroidx/compose/ui/layout/H;

    .line 45
    .line 46
    invoke-static/range {p3 .. p4}, Lt0/b;->k(J)I

    .line 47
    .line 48
    .line 49
    move-result v8

    .line 50
    const/4 v7, 0x0

    .line 51
    move v6, v5

    .line 52
    move-wide/from16 v3, p3

    .line 53
    .line 54
    invoke-static/range {v3 .. v8}, Lt0/b;->c(JIIII)J

    .line 55
    .line 56
    .line 57
    move-result-wide v6

    .line 58
    invoke-interface {v2, v6, v7}, Landroidx/compose/ui/layout/H;->k0(J)Landroidx/compose/ui/layout/g0;

    .line 59
    .line 60
    .line 61
    move-result-object v2

    .line 62
    invoke-virtual {v2}, Landroidx/compose/ui/layout/g0;->y0()I

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    if-eqz v3, :cond_1

    .line 71
    .line 72
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v3

    .line 76
    move-object v9, v3

    .line 77
    check-cast v9, Landroidx/compose/ui/layout/H;

    .line 78
    .line 79
    invoke-static/range {p3 .. p4}, Lt0/b;->k(J)I

    .line 80
    .line 81
    .line 82
    move-result v8

    .line 83
    const/4 v7, 0x0

    .line 84
    move v6, v5

    .line 85
    move-wide/from16 v3, p3

    .line 86
    .line 87
    invoke-static/range {v3 .. v8}, Lt0/b;->c(JIIII)J

    .line 88
    .line 89
    .line 90
    move-result-wide v6

    .line 91
    invoke-interface {v9, v6, v7}, Landroidx/compose/ui/layout/H;->k0(J)Landroidx/compose/ui/layout/g0;

    .line 92
    .line 93
    .line 94
    move-result-object v3

    .line 95
    invoke-virtual {v3}, Landroidx/compose/ui/layout/g0;->y0()I

    .line 96
    .line 97
    .line 98
    move-result v3

    .line 99
    if-ge v2, v3, :cond_0

    .line 100
    .line 101
    move v2, v3

    .line 102
    goto :goto_0

    .line 103
    :cond_1
    new-instance v1, Ljava/util/ArrayList;

    .line 104
    .line 105
    const/16 v3, 0xa

    .line 106
    .line 107
    invoke-static {p2, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 108
    .line 109
    .line 110
    move-result v3

    .line 111
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 112
    .line 113
    .line 114
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 115
    .line 116
    .line 117
    move-result-object v9

    .line 118
    :goto_1
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    .line 119
    .line 120
    .line 121
    move-result v3

    .line 122
    if-eqz v3, :cond_2

    .line 123
    .line 124
    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object v3

    .line 128
    move-object v10, v3

    .line 129
    check-cast v10, Landroidx/compose/ui/layout/H;

    .line 130
    .line 131
    move v6, v5

    .line 132
    move v8, v2

    .line 133
    move-wide/from16 v3, p3

    .line 134
    .line 135
    move v7, v2

    .line 136
    invoke-static/range {v3 .. v8}, Lt0/b;->c(JIIII)J

    .line 137
    .line 138
    .line 139
    move-result-wide v11

    .line 140
    invoke-interface {v10, v11, v12}, Landroidx/compose/ui/layout/H;->k0(J)Landroidx/compose/ui/layout/g0;

    .line 141
    .line 142
    .line 143
    move-result-object v2

    .line 144
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 145
    .line 146
    .line 147
    move v2, v7

    .line 148
    goto :goto_1

    .line 149
    :cond_2
    move v7, v2

    .line 150
    invoke-static/range {p3 .. p4}, Lt0/b;->l(J)I

    .line 151
    .line 152
    .line 153
    move-result v2

    .line 154
    new-instance v10, LNB0/h;

    .line 155
    .line 156
    invoke-direct {v10, v1, v0}, LNB0/h;-><init>(Ljava/util/List;I)V

    .line 157
    .line 158
    .line 159
    const/4 v11, 0x4

    .line 160
    const/4 v12, 0x0

    .line 161
    const/4 v9, 0x0

    .line 162
    move-object v6, p1

    .line 163
    move v8, v7

    .line 164
    move v7, v2

    .line 165
    invoke-static/range {v6 .. v12}, Landroidx/compose/ui/layout/M;->b(Landroidx/compose/ui/layout/N;IILjava/util/Map;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroidx/compose/ui/layout/L;

    .line 166
    .line 167
    .line 168
    move-result-object p1

    .line 169
    return-object p1

    .line 170
    :cond_3
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 171
    .line 172
    invoke-direct {p1}, Ljava/util/NoSuchElementException;-><init>()V

    .line 173
    .line 174
    .line 175
    throw p1
.end method

.method public synthetic b(Landroidx/compose/ui/layout/o;Ljava/util/List;I)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/I;->c(Landroidx/compose/ui/layout/J;Landroidx/compose/ui/layout/o;Ljava/util/List;I)I

    move-result p1

    return p1
.end method

.method public synthetic c(Landroidx/compose/ui/layout/o;Ljava/util/List;I)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/I;->d(Landroidx/compose/ui/layout/J;Landroidx/compose/ui/layout/o;Ljava/util/List;I)I

    move-result p1

    return p1
.end method

.method public synthetic d(Landroidx/compose/ui/layout/o;Ljava/util/List;I)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/I;->a(Landroidx/compose/ui/layout/J;Landroidx/compose/ui/layout/o;Ljava/util/List;I)I

    move-result p1

    return p1
.end method

.method public synthetic f(Landroidx/compose/ui/layout/o;Ljava/util/List;I)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/layout/I;->b(Landroidx/compose/ui/layout/J;Landroidx/compose/ui/layout/o;Ljava/util/List;I)I

    move-result p1

    return p1
.end method
