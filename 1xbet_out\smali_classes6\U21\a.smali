.class public final LU21/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;",
        "a",
        "(I)Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;",
        "uikit_aggregator_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(I)Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p0, :cond_2

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-eq p0, v0, :cond_1

    .line 5
    .line 6
    const/4 v0, 0x2

    .line 7
    if-eq p0, v0, :cond_0

    .line 8
    .line 9
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;->TITLE_STYLE:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;

    .line 10
    .line 11
    return-object p0

    .line 12
    :cond_0
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;->GRADIENT_STYLE:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;

    .line 13
    .line 14
    return-object p0

    .line 15
    :cond_1
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;->CARD_STYLE:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;

    .line 16
    .line 17
    return-object p0

    .line 18
    :cond_2
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;->TITLE_STYLE:Lorg/xbet/uikit_aggregator/aggregatortournamentcardsold/AggregatorTournamentCardsOldStyle;

    .line 19
    .line 20
    return-object p0
.end method
