.class public final synthetic Lorg/xbet/uikit_sport/eventcard/bottom/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/internal/Ref$BooleanRef;

.field public final synthetic b:Lkotlin/jvm/internal/Ref$IntRef;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/k;->a:Lkotlin/jvm/internal/Ref$BooleanRef;

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/k;->b:Lkotlin/jvm/internal/Ref$IntRef;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/k;->a:Lkotlin/jvm/internal/Ref$BooleanRef;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/k;->b:Lkotlin/jvm/internal/Ref$IntRef;

    invoke-static {v0, v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
