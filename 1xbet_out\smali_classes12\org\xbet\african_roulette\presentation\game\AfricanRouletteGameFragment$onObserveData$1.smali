.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteGameFragment$onObserveData$1"
    f = "AfricanRouletteGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->invoke(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 26
    .line 27
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;->a()Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_0

    .line 34
    .line 35
    const/4 p1, 0x0

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    const/16 p1, 0x8

    .line 38
    .line 39
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    goto :goto_1

    .line 43
    :cond_1
    instance-of v0, p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;

    .line 44
    .line 45
    if-eqz v0, :cond_4

    .line 46
    .line 47
    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->c()Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_2

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 62
    .line 63
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->a()F

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->b()F

    .line 68
    .line 69
    .line 70
    move-result p1

    .line 71
    invoke-virtual {v0, v1, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->u(FF)V

    .line 72
    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_2
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->a()F

    .line 76
    .line 77
    .line 78
    move-result v0

    .line 79
    const/4 v1, 0x0

    .line 80
    cmpg-float v0, v0, v1

    .line 81
    .line 82
    if-nez v0, :cond_3

    .line 83
    .line 84
    goto :goto_1

    .line 85
    :cond_3
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment$onObserveData$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;

    .line 86
    .line 87
    invoke-static {v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;->F2(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteGameFragment;)Lgg/b;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iget-object v0, v0, Lgg/b;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;

    .line 92
    .line 93
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->a()F

    .line 94
    .line 95
    .line 96
    move-result v1

    .line 97
    invoke-virtual {p1}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;->b()F

    .line 98
    .line 99
    .line 100
    move-result p1

    .line 101
    invoke-virtual {v0, v1, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteWheel;->s(FF)V

    .line 102
    .line 103
    .line 104
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 105
    .line 106
    return-object p1

    .line 107
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 108
    .line 109
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 110
    .line 111
    .line 112
    throw p1

    .line 113
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 114
    .line 115
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 116
    .line 117
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 118
    .line 119
    .line 120
    throw p1
.end method
