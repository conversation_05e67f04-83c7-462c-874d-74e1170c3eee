.class public final LJb1/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LJb1/l;",
        ">;"
    }
.end annotation


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lau/a;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LTZ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LYU/a;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LS8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/d;",
            ">;"
        }
    .end annotation
.end field

.field public final J:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LzX0/k;",
            ">;"
        }
    .end annotation
.end field

.field public final K:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWa0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final L:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Leu/i;",
            ">;"
        }
    .end annotation
.end field

.field public final M:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ldu/e;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc81/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltf0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LJT/a;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc81/c;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lak/b;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lhf0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/g;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LQW0/c;",
            ">;",
            "LBc/a<",
            "Lc81/a;",
            ">;",
            "LBc/a<",
            "Ltf0/a;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Li8/l;",
            ">;",
            "LBc/a<",
            "LJT/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LDg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "Lc81/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LSR/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lak/b;",
            ">;",
            "LBc/a<",
            "Lhf0/a;",
            ">;",
            "LBc/a<",
            "Lv81/g;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "Lau/a;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;",
            "LBc/a<",
            "LTZ0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LYU/a;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "LzX0/k;",
            ">;",
            "LBc/a<",
            "LWa0/a;",
            ">;",
            "LBc/a<",
            "Leu/i;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LJb1/m;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LJb1/m;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, LJb1/m;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, LJb1/m;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, LJb1/m;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, LJb1/m;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, LJb1/m;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, LJb1/m;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, LJb1/m;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, LJb1/m;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, LJb1/m;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, LJb1/m;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, LJb1/m;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, LJb1/m;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, LJb1/m;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LJb1/m;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LJb1/m;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LJb1/m;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LJb1/m;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LJb1/m;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LJb1/m;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LJb1/m;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, LJb1/m;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, LJb1/m;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, LJb1/m;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, LJb1/m;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, LJb1/m;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, LJb1/m;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, LJb1/m;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, LJb1/m;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, LJb1/m;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, LJb1/m;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, LJb1/m;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, LJb1/m;->H:LBc/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, LJb1/m;->I:LBc/a;

    .line 113
    .line 114
    move-object/from16 p1, p36

    .line 115
    .line 116
    iput-object p1, p0, LJb1/m;->J:LBc/a;

    .line 117
    .line 118
    move-object/from16 p1, p37

    .line 119
    .line 120
    iput-object p1, p0, LJb1/m;->K:LBc/a;

    .line 121
    .line 122
    move-object/from16 p1, p38

    .line 123
    .line 124
    iput-object p1, p0, LJb1/m;->L:LBc/a;

    .line 125
    .line 126
    move-object/from16 p1, p39

    .line 127
    .line 128
    iput-object p1, p0, LJb1/m;->M:LBc/a;

    .line 129
    .line 130
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LJb1/m;
    .locals 40
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LQW0/c;",
            ">;",
            "LBc/a<",
            "Lc81/a;",
            ">;",
            "LBc/a<",
            "Ltf0/a;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Li8/l;",
            ">;",
            "LBc/a<",
            "LJT/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LDg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "Lc81/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LSR/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lak/b;",
            ">;",
            "LBc/a<",
            "Lhf0/a;",
            ">;",
            "LBc/a<",
            "Lv81/g;",
            ">;",
            "LBc/a<",
            "Lak/a;",
            ">;",
            "LBc/a<",
            "Lau/a;",
            ">;",
            "LBc/a<",
            "Li8/j;",
            ">;",
            "LBc/a<",
            "LTZ0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LYU/a;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "LzX0/k;",
            ">;",
            "LBc/a<",
            "LWa0/a;",
            ">;",
            "LBc/a<",
            "Leu/i;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;)",
            "LJb1/m;"
        }
    .end annotation

    .line 1
    new-instance v0, LJb1/m;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    invoke-direct/range {v0 .. v39}, LJb1/m;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 82
    .line 83
    .line 84
    return-object v0
.end method

.method public static c(LQW0/c;Lc81/a;Ltf0/a;Lf8/g;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;Li8/l;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;Lorg/xbet/analytics/domain/scope/g0;LDg/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lak/b;Lhf0/a;Lv81/g;Lak/a;Lau/a;Li8/j;LTZ0/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;LWa0/a;Leu/i;Ldu/e;)LJb1/l;
    .locals 40

    .line 1
    new-instance v0, LJb1/l;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    invoke-direct/range {v0 .. v39}, LJb1/l;-><init>(LQW0/c;Lc81/a;Ltf0/a;Lf8/g;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;Li8/l;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;Lorg/xbet/analytics/domain/scope/g0;LDg/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lak/b;Lhf0/a;Lv81/g;Lak/a;Lau/a;Li8/j;LTZ0/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;LWa0/a;Leu/i;Ldu/e;)V

    .line 82
    .line 83
    .line 84
    return-object v0
.end method


# virtual methods
.method public b()LJb1/l;
    .locals 41

    move-object/from16 v0, p0

    .line 1
    iget-object v1, v0, LJb1/m;->a:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, LQW0/c;

    iget-object v1, v0, LJb1/m;->b:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v3, v1

    check-cast v3, Lc81/a;

    iget-object v1, v0, LJb1/m;->c:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, Ltf0/a;

    iget-object v1, v0, LJb1/m;->d:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lf8/g;

    iget-object v1, v0, LJb1/m;->e:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, LSX0/c;

    iget-object v1, v0, LJb1/m;->f:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v7, v1

    check-cast v7, Lorg/xbet/ui_common/utils/internet/a;

    iget-object v1, v0, LJb1/m;->g:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v8, v1

    check-cast v8, Lorg/xbet/ui_common/utils/M;

    iget-object v1, v0, LJb1/m;->h:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, Li8/l;

    iget-object v1, v0, LJb1/m;->i:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v10, v1

    check-cast v10, LJT/a;

    iget-object v1, v0, LJb1/m;->j:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v11, v1

    check-cast v11, Lcom/xbet/onexuser/domain/user/c;

    iget-object v1, v0, LJb1/m;->k:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v12, v1

    check-cast v12, Lp9/c;

    iget-object v1, v0, LJb1/m;->l:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v13, v1

    check-cast v13, Lorg/xbet/analytics/domain/scope/g0;

    iget-object v1, v0, LJb1/m;->m:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v14, v1

    check-cast v14, LDg/a;

    iget-object v1, v0, LJb1/m;->n:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v15, v1

    check-cast v15, Lorg/xbet/remoteconfig/domain/usecases/i;

    iget-object v1, v0, LJb1/m;->o:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v16, v1

    check-cast v16, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    iget-object v1, v0, LJb1/m;->p:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v17, v1

    check-cast v17, LwX0/C;

    iget-object v1, v0, LJb1/m;->q:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v18, v1

    check-cast v18, Lc81/c;

    iget-object v1, v0, LJb1/m;->r:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v19, v1

    check-cast v19, Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    iget-object v1, v0, LJb1/m;->s:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v20, v1

    check-cast v20, LHX0/e;

    iget-object v1, v0, LJb1/m;->t:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v21, v1

    check-cast v21, Lc8/h;

    iget-object v1, v0, LJb1/m;->u:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v22, v1

    check-cast v22, LfX/b;

    iget-object v1, v0, LJb1/m;->v:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v23, v1

    check-cast v23, LSR/a;

    iget-object v1, v0, LJb1/m;->w:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v24, v1

    check-cast v24, LnR/a;

    iget-object v1, v0, LJb1/m;->x:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v25, v1

    check-cast v25, Lak/b;

    iget-object v1, v0, LJb1/m;->y:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v26, v1

    check-cast v26, Lhf0/a;

    iget-object v1, v0, LJb1/m;->z:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v27, v1

    check-cast v27, Lv81/g;

    iget-object v1, v0, LJb1/m;->A:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v28, v1

    check-cast v28, Lak/a;

    iget-object v1, v0, LJb1/m;->B:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v29, v1

    check-cast v29, Lau/a;

    iget-object v1, v0, LJb1/m;->C:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v30, v1

    check-cast v30, Li8/j;

    iget-object v1, v0, LJb1/m;->D:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v31, v1

    check-cast v31, LTZ0/a;

    iget-object v1, v0, LJb1/m;->E:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v32, v1

    check-cast v32, Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    iget-object v1, v0, LJb1/m;->F:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v33, v1

    check-cast v33, LYU/a;

    iget-object v1, v0, LJb1/m;->G:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v34, v1

    check-cast v34, LS8/a;

    iget-object v1, v0, LJb1/m;->H:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v35, v1

    check-cast v35, Lcom/xbet/onexuser/data/profile/b;

    iget-object v1, v0, LJb1/m;->I:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v36, v1

    check-cast v36, LnR/d;

    iget-object v1, v0, LJb1/m;->J:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v37, v1

    check-cast v37, LzX0/k;

    iget-object v1, v0, LJb1/m;->K:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v38, v1

    check-cast v38, LWa0/a;

    iget-object v1, v0, LJb1/m;->L:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v39, v1

    check-cast v39, Leu/i;

    iget-object v1, v0, LJb1/m;->M:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v40, v1

    check-cast v40, Ldu/e;

    invoke-static/range {v2 .. v40}, LJb1/m;->c(LQW0/c;Lc81/a;Ltf0/a;Lf8/g;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;Li8/l;LJT/a;Lcom/xbet/onexuser/domain/user/c;Lp9/c;Lorg/xbet/analytics/domain/scope/g0;LDg/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LwX0/C;Lc81/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LHX0/e;Lc8/h;LfX/b;LSR/a;LnR/a;Lak/b;Lhf0/a;Lv81/g;Lak/a;Lau/a;Li8/j;LTZ0/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LYU/a;LS8/a;Lcom/xbet/onexuser/data/profile/b;LnR/d;LzX0/k;LWa0/a;Leu/i;Ldu/e;)LJb1/l;

    move-result-object v1

    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LJb1/m;->b()LJb1/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
