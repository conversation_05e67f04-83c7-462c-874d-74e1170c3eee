.class public interface abstract LN1/x;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:LN1/x;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LN1/v;

    .line 2
    .line 3
    invoke-direct {v0}, LN1/v;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LN1/x;->a:LN1/x;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public abstract a(Lk2/s$a;)LN1/x;
.end method

.method public abstract b(I)LN1/x;
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation
.end method

.method public abstract c(Z)LN1/x;
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract d(Landroid/net/Uri;Ljava/util/Map;)[LN1/r;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/net/Uri;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;)[",
            "LN1/r;"
        }
    .end annotation
.end method

.method public abstract e()[LN1/r;
.end method
