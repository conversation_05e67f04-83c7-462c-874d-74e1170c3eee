.class public final LC51/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LC51/a$a;,
        LC51/a$b;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()LC51/e$a;
    .locals 2

    .line 1
    new-instance v0, LC51/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LC51/a$a;-><init>(LC51/b;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method
