.class public final LKa1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a=\u0010\t\u001a\u001a\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00060\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\u0013\u0010\u000c\u001a\u00020\u0008*\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LL91/e;",
        "",
        "endPoint",
        "",
        "hasAggregatorBrands",
        "Lkotlin/Pair;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "Lorg/xplatform/aggregator/api/domain/model/GameCategory;",
        "b",
        "(LL91/e;Ljava/lang/String;Z)Lkotlin/Pair;",
        "LL91/c;",
        "a",
        "(LL91/c;)Lorg/xplatform/aggregator/api/domain/model/GameCategory;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LL91/c;)Lorg/xplatform/aggregator/api/domain/model/GameCategory;
    .locals 21

    .line 1
    invoke-virtual/range {p0 .. p0}, LL91/c;->b()Ljava/lang/Long;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-wide/16 v1, 0x0

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 10
    .line 11
    .line 12
    move-result-wide v3

    .line 13
    move-wide v6, v3

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    move-wide v6, v1

    .line 16
    :goto_0
    invoke-virtual/range {p0 .. p0}, LL91/c;->h()Ljava/lang/Long;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 23
    .line 24
    .line 25
    move-result-wide v3

    .line 26
    move-wide/from16 v16, v3

    .line 27
    .line 28
    goto :goto_1

    .line 29
    :cond_1
    move-wide/from16 v16, v1

    .line 30
    .line 31
    :goto_1
    invoke-virtual/range {p0 .. p0}, LL91/c;->d()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    const-string v3, ""

    .line 36
    .line 37
    if-nez v0, :cond_2

    .line 38
    .line 39
    move-object v8, v3

    .line 40
    goto :goto_2

    .line 41
    :cond_2
    move-object v8, v0

    .line 42
    :goto_2
    invoke-virtual/range {p0 .. p0}, LL91/c;->e()Ljava/lang/Boolean;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    const/4 v4, 0x0

    .line 47
    if-eqz v0, :cond_3

    .line 48
    .line 49
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    move/from16 v18, v0

    .line 54
    .line 55
    goto :goto_3

    .line 56
    :cond_3
    const/16 v18, 0x0

    .line 57
    .line 58
    :goto_3
    invoke-virtual/range {p0 .. p0}, LL91/c;->f()Ljava/lang/Boolean;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    if-eqz v0, :cond_4

    .line 63
    .line 64
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 65
    .line 66
    .line 67
    move-result v4

    .line 68
    move/from16 v19, v4

    .line 69
    .line 70
    goto :goto_4

    .line 71
    :cond_4
    const/16 v19, 0x0

    .line 72
    .line 73
    :goto_4
    invoke-virtual/range {p0 .. p0}, LL91/c;->c()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    if-nez v0, :cond_5

    .line 78
    .line 79
    move-object v9, v3

    .line 80
    goto :goto_5

    .line 81
    :cond_5
    move-object v9, v0

    .line 82
    :goto_5
    invoke-virtual/range {p0 .. p0}, LL91/c;->i()Ljava/lang/Long;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    if-eqz v0, :cond_6

    .line 87
    .line 88
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 89
    .line 90
    .line 91
    move-result-wide v4

    .line 92
    move-wide v10, v4

    .line 93
    goto :goto_6

    .line 94
    :cond_6
    move-wide v10, v1

    .line 95
    :goto_6
    invoke-virtual/range {p0 .. p0}, LL91/c;->g()Ljava/lang/Long;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    if-eqz v0, :cond_7

    .line 100
    .line 101
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 102
    .line 103
    .line 104
    move-result-wide v4

    .line 105
    move-wide v12, v4

    .line 106
    goto :goto_7

    .line 107
    :cond_7
    move-wide v12, v1

    .line 108
    :goto_7
    invoke-virtual/range {p0 .. p0}, LL91/c;->a()Ljava/lang/Long;

    .line 109
    .line 110
    .line 111
    move-result-object v0

    .line 112
    if-eqz v0, :cond_8

    .line 113
    .line 114
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 115
    .line 116
    .line 117
    move-result-wide v1

    .line 118
    :cond_8
    move-wide v14, v1

    .line 119
    invoke-virtual/range {p0 .. p0}, LL91/c;->j()Ljava/lang/String;

    .line 120
    .line 121
    .line 122
    move-result-object v0

    .line 123
    if-nez v0, :cond_9

    .line 124
    .line 125
    move-object/from16 v20, v3

    .line 126
    .line 127
    goto :goto_8

    .line 128
    :cond_9
    move-object/from16 v20, v0

    .line 129
    .line 130
    :goto_8
    new-instance v5, Lorg/xplatform/aggregator/api/domain/model/GameCategory;

    .line 131
    .line 132
    invoke-direct/range {v5 .. v20}, Lorg/xplatform/aggregator/api/domain/model/GameCategory;-><init>(JLjava/lang/String;Ljava/lang/String;JJJJZZLjava/lang/String;)V

    .line 133
    .line 134
    .line 135
    return-object v5
.end method

.method public static final b(LL91/e;Ljava/lang/String;Z)Lkotlin/Pair;
    .locals 5
    .param p0    # LL91/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL91/e;",
            "Ljava/lang/String;",
            "Z)",
            "Lkotlin/Pair<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/domain/model/GameCategory;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LL91/e;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/16 v2, 0xa

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    new-instance v3, Ljava/util/ArrayList;

    .line 11
    .line 12
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 13
    .line 14
    .line 15
    move-result v4

    .line 16
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-eqz v4, :cond_1

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v4

    .line 33
    check-cast v4, LL91/d;

    .line 34
    .line 35
    invoke-static {v4, p1, p2}, LA91/d;->c(LL91/d;Ljava/lang/String;Z)Lorg/xplatform/aggregator/api/model/Game;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    move-object v3, v1

    .line 44
    :cond_1
    if-nez v3, :cond_2

    .line 45
    .line 46
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    :cond_2
    invoke-virtual {p0}, LL91/e;->a()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    if-eqz p0, :cond_3

    .line 55
    .line 56
    new-instance v1, Ljava/util/ArrayList;

    .line 57
    .line 58
    invoke-static {p0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    invoke-direct {v1, p1}, Ljava/util/ArrayList;-><init>(I)V

    .line 63
    .line 64
    .line 65
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    if-eqz p1, :cond_3

    .line 74
    .line 75
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    check-cast p1, LL91/c;

    .line 80
    .line 81
    invoke-static {p1}, LKa1/a;->a(LL91/c;)Lorg/xplatform/aggregator/api/domain/model/GameCategory;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    invoke-interface {v1, p1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_3
    if-nez v1, :cond_4

    .line 90
    .line 91
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    :cond_4
    new-instance p0, Lkotlin/Pair;

    .line 96
    .line 97
    invoke-direct {p0, v3, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 98
    .line 99
    .line 100
    return-object p0
.end method
