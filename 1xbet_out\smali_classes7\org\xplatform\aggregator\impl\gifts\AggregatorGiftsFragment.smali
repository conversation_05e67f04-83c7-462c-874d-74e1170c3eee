.class public final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a3\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u001e\n\u0002\u0018\u0002\n\u0002\u0008\u000b*\u0001\u001d\u0008\u0000\u0018\u0000 \u0081\u00012\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u0082\u0001B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001d\u0010\u0011\u001a\u00020\u00072\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000eH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0004J\u0017\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0004J\u0013\u0010\u001a\u001a\u00020\u0007*\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001c\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0004J\u000f\u0010\u001e\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001d\u0010$\u001a\u00020#2\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020!0 H\u0002\u00a2\u0006\u0004\u0008$\u0010%J\u0017\u0010\'\u001a\u00020\u00072\u0006\u0010&\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\'\u0010\rJ\u000f\u0010(\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008(\u0010\u0004J\u000f\u0010)\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008)\u0010\u0004J\u000f\u0010+\u001a\u00020*H\u0010\u00a2\u0006\u0004\u0008+\u0010,J\u000f\u0010.\u001a\u00020-H\u0014\u00a2\u0006\u0004\u0008.\u0010/J\u000f\u00100\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u00080\u0010\u0004J\u0019\u00103\u001a\u00020\u00072\u0008\u00102\u001a\u0004\u0018\u000101H\u0016\u00a2\u0006\u0004\u00083\u00104J\u0019\u00105\u001a\u00020\u00072\u0008\u00102\u001a\u0004\u0018\u000101H\u0014\u00a2\u0006\u0004\u00085\u00104J\u000f\u00106\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u00086\u0010\u0004J\u000f\u00107\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u00087\u0010\u0004J\u000f\u00108\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u00088\u0010\u0004J\u000f\u00109\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u00089\u0010\u0004J\u000f\u0010:\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008:\u0010\u0004R\"\u0010B\u001a\u00020;8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?\"\u0004\u0008@\u0010AR\"\u0010J\u001a\u00020C8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008D\u0010E\u001a\u0004\u0008F\u0010G\"\u0004\u0008H\u0010IR\"\u0010R\u001a\u00020K8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008L\u0010M\u001a\u0004\u0008N\u0010O\"\u0004\u0008P\u0010QR\u001b\u0010X\u001a\u00020S8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008T\u0010U\u001a\u0004\u0008V\u0010WR+\u0010a\u001a\u00020Y2\u0006\u0010Z\u001a\u00020Y8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008[\u0010\\\u001a\u0004\u0008]\u0010^\"\u0004\u0008_\u0010`R+\u0010e\u001a\u00020Y2\u0006\u0010Z\u001a\u00020Y8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008b\u0010\\\u001a\u0004\u0008c\u0010^\"\u0004\u0008d\u0010`R+\u0010i\u001a\u00020Y2\u0006\u0010Z\u001a\u00020Y8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008f\u0010\\\u001a\u0004\u0008g\u0010^\"\u0004\u0008h\u0010`R+\u0010o\u001a\u00020\n2\u0006\u0010Z\u001a\u00020\n8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008j\u0010k\u001a\u0004\u0008l\u0010m\"\u0004\u0008n\u0010\rR\u0014\u0010r\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010qR\u001b\u0010w\u001a\u00020#8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008s\u0010t\u001a\u0004\u0008u\u0010vR\u001b\u0010|\u001a\u00020x8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008y\u0010t\u001a\u0004\u0008z\u0010{R\u001c\u0010\u0080\u0001\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008}\u0010t\u001a\u0004\u0008~\u0010\u007f\u00a8\u0006\u0083\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;",
        "state",
        "",
        "w4",
        "(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;)V",
        "",
        "noConnection",
        "S3",
        "(Z)V",
        "",
        "LVX0/i;",
        "giftsList",
        "B4",
        "(Ljava/util/List;)V",
        "x4",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;",
        "params",
        "k4",
        "(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V",
        "A4",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "l4",
        "(Landroidx/recyclerview/widget/RecyclerView;)V",
        "r4",
        "org/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b",
        "Q3",
        "()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;",
        "Landroidx/recyclerview/widget/RecyclerView$Adapter;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "adapter",
        "Lorg/xplatform/aggregator/impl/gifts/f;",
        "T3",
        "(Landroidx/recyclerview/widget/RecyclerView$Adapter;)Lorg/xplatform/aggregator/impl/gifts/f;",
        "show",
        "e",
        "u1",
        "q4",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "t2",
        "U2",
        "onResume",
        "onPause",
        "onDestroyView",
        "v2",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "o0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i4",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "b1",
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "Y3",
        "()Lorg/xplatform/aggregator/impl/core/presentation/c;",
        "setAggregatorCategoriesDelegate",
        "(Lorg/xplatform/aggregator/impl/core/presentation/c;)V",
        "aggregatorCategoriesDelegate",
        "LSX0/c;",
        "k1",
        "LSX0/c;",
        "f4",
        "()LSX0/c;",
        "setLottieEmptyConfigurator",
        "(LSX0/c;)V",
        "lottieEmptyConfigurator",
        "LS91/G;",
        "v1",
        "LRc/c;",
        "g4",
        "()LS91/G;",
        "viewBinding",
        "",
        "<set-?>",
        "x1",
        "LeX0/d;",
        "b4",
        "()I",
        "t4",
        "(I)V",
        "bundleBonusesCount",
        "y1",
        "c4",
        "u4",
        "bundleFreeSpinsCount",
        "F1",
        "d4",
        "v4",
        "bundleGiftTypeId",
        "H1",
        "LeX0/a;",
        "a4",
        "()Z",
        "s4",
        "bundleAfterAuth",
        "I1",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;",
        "giftsAppBarObserver",
        "P1",
        "Lkotlin/j;",
        "e4",
        "()Lorg/xplatform/aggregator/impl/gifts/f;",
        "giftsLoaderObserver",
        "Laa1/a;",
        "S1",
        "Z3",
        "()Laa1/a;",
        "aggregatorGiftsAdapter",
        "V1",
        "h4",
        "()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
        "viewModel",
        "b2",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b2:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v2:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final F1:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b1:Lorg/xplatform/aggregator/impl/core/presentation/c;

.field public k1:LSX0/c;

.field public o0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final v1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 9

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAggregatorGiftsBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bundleBonusesCount"

    .line 20
    .line 21
    const-string v5, "getBundleBonusesCount()I"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "bundleFreeSpinsCount"

    .line 33
    .line 34
    const-string v6, "getBundleFreeSpinsCount()I"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "bundleGiftTypeId"

    .line 46
    .line 47
    const-string v7, "getBundleGiftTypeId()I"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    new-instance v6, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 57
    .line 58
    const-string v7, "bundleAfterAuth"

    .line 59
    .line 60
    const-string v8, "getBundleAfterAuth()Z"

    .line 61
    .line 62
    invoke-direct {v6, v1, v7, v8, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 63
    .line 64
    .line 65
    invoke-static {v6}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v6, 0x5

    .line 70
    new-array v6, v6, [Lkotlin/reflect/m;

    .line 71
    .line 72
    aput-object v0, v6, v4

    .line 73
    .line 74
    const/4 v0, 0x1

    .line 75
    aput-object v2, v6, v0

    .line 76
    .line 77
    const/4 v0, 0x2

    .line 78
    aput-object v3, v6, v0

    .line 79
    .line 80
    const/4 v0, 0x3

    .line 81
    aput-object v5, v6, v0

    .line 82
    .line 83
    const/4 v0, 0x4

    .line 84
    aput-object v1, v6, v0

    .line 85
    .line 86
    sput-object v6, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 87
    .line 88
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;

    .line 89
    .line 90
    const/4 v1, 0x0

    .line 91
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->b2:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$a;

    .line 95
    .line 96
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lu91/c;->fragment_aggregator_gifts:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v1:LRc/c;

    .line 13
    .line 14
    new-instance v0, LeX0/d;

    .line 15
    .line 16
    const-string v1, "BONUSES_COUNT"

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    const/4 v3, 0x2

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 22
    .line 23
    .line 24
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->x1:LeX0/d;

    .line 25
    .line 26
    new-instance v0, LeX0/d;

    .line 27
    .line 28
    const-string v1, "FREE_SPINS_COUNT"

    .line 29
    .line 30
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->y1:LeX0/d;

    .line 34
    .line 35
    new-instance v0, LeX0/d;

    .line 36
    .line 37
    const-string v1, "GIFT_TYPE_ID"

    .line 38
    .line 39
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->F1:LeX0/d;

    .line 43
    .line 44
    new-instance v0, LeX0/a;

    .line 45
    .line 46
    const-string v1, "AFTER_AUTH"

    .line 47
    .line 48
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 49
    .line 50
    .line 51
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->H1:LeX0/a;

    .line 52
    .line 53
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Q3()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->I1:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;

    .line 58
    .line 59
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/j;

    .line 60
    .line 61
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/j;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 62
    .line 63
    .line 64
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 65
    .line 66
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->P1:Lkotlin/j;

    .line 71
    .line 72
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/k;

    .line 73
    .line 74
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/k;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 75
    .line 76
    .line 77
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->S1:Lkotlin/j;

    .line 82
    .line 83
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/l;

    .line 84
    .line 85
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/l;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 86
    .line 87
    .line 88
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$1;

    .line 89
    .line 90
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 91
    .line 92
    .line 93
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$2;

    .line 94
    .line 95
    invoke-direct {v3, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 96
    .line 97
    .line 98
    invoke-static {v1, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    const-class v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 103
    .line 104
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 105
    .line 106
    .line 107
    move-result-object v2

    .line 108
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$3;

    .line 109
    .line 110
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 111
    .line 112
    .line 113
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$4;

    .line 114
    .line 115
    invoke-direct {v5, v4, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 116
    .line 117
    .line 118
    invoke-static {p0, v2, v3, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->V1:Lkotlin/j;

    .line 123
    .line 124
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->k4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u1()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->p4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final C4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->i4()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->q4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->r4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->s4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->t4(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u4(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v4(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->d3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->e3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->A4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->e(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->B4(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final R3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Laa1/a;
    .locals 10

    .line 1
    new-instance v0, Laa1/a;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->f4()LSX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$1;

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$2;

    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 19
    .line 20
    .line 21
    move-result-object v4

    .line 22
    invoke-direct {v3, v4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$2;-><init>(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$3;

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object v5

    .line 31
    invoke-direct {v4, v5}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$3;-><init>(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$4;

    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 37
    .line 38
    .line 39
    move-result-object v6

    .line 40
    invoke-direct {v5, v6}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$4;-><init>(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$5;

    .line 44
    .line 45
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 46
    .line 47
    .line 48
    move-result-object v7

    .line 49
    invoke-direct {v6, v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$5;-><init>(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$6;

    .line 53
    .line 54
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 55
    .line 56
    .line 57
    move-result-object v8

    .line 58
    invoke-direct {v7, v8}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$6;-><init>(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    new-instance v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$7;

    .line 62
    .line 63
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 64
    .line 65
    .line 66
    move-result-object v9

    .line 67
    invoke-direct {v8, v9}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$7;-><init>(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    new-instance v9, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$8;

    .line 71
    .line 72
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$aggregatorGiftsAdapter$2$8;-><init>(Ljava/lang/Object;)V

    .line 77
    .line 78
    .line 79
    invoke-direct/range {v0 .. v9}, Laa1/a;-><init>(LSX0/c;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;LOc/n;Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function0;)V

    .line 80
    .line 81
    .line 82
    return-object v0
.end method

.method public static final U3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u1()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final V3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u1()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final W3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u1()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final X3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->u1()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final b4()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->x1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final c4()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->y1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final e(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/G;->f:Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LS91/G;->j:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    const/4 v1, 0x0

    .line 28
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static final j4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->T3(Landroidx/recyclerview/widget/RecyclerView$Adapter;)Lorg/xplatform/aggregator/impl/gifts/f;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->j4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->U3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final m4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->H5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic n3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final n4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LXW0/d;->h(Landroidx/fragment/app/Fragment;)Ljava/lang/Boolean;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->n4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final o4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->K5(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->V3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->w4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->y4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Laa1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->R3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Laa1/a;

    move-result-object p0

    return-object p0
.end method

.method private final r4()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$c;

    .line 8
    .line 9
    invoke-direct {v2, p0, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$c;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;LS91/G;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v1, v2}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->C4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->m4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final t4(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->x1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final u1()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/G;->j:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LS91/G;->f:Lorg/xbet/ui_common/viewcomponents/layouts/linear/ShimmerLinearLayout;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->e4()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->l()V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public static synthetic u3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->o4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final u4(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->y1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic v3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->X3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;III)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->W3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Laa1/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->e4()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final y4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->j5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)LS91/G;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->T5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final A4()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->confirmation:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->refuse_bonus:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    sget v5, Lpb/k;->yes:I

    .line 22
    .line 23
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    sget v6, Lpb/k;->no:I

    .line 28
    .line 29
    invoke-virtual {v0, v6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v6

    .line 33
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->INFO:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 34
    .line 35
    const/16 v15, 0xbd0

    .line 36
    .line 37
    const/16 v16, 0x0

    .line 38
    .line 39
    const/4 v7, 0x0

    .line 40
    const-string v8, "REQUEST_REFUSE_BONUS"

    .line 41
    .line 42
    const/4 v9, 0x0

    .line 43
    const/4 v10, 0x0

    .line 44
    const/4 v11, 0x0

    .line 45
    const/4 v12, 0x0

    .line 46
    const/4 v14, 0x0

    .line 47
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final B4(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    if-nez v1, :cond_1

    .line 11
    .line 12
    iget-object v1, v0, LS91/G;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 13
    .line 14
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-nez v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v1, 0x1

    .line 22
    goto :goto_1

    .line 23
    :cond_1
    :goto_0
    const/4 v1, 0x0

    .line 24
    :goto_1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    invoke-virtual {v3, p1}, Laa1/a;->o(Ljava/util/List;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, v0, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 32
    .line 33
    if-eqz v1, :cond_2

    .line 34
    .line 35
    goto :goto_2

    .line 36
    :cond_2
    const/16 v2, 0x8

    .line 37
    .line 38
    :goto_2
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/G;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/G;->h:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final Q3()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final S3(Z)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LS91/G;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v4, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, v0, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 20
    .line 21
    if-nez p1, :cond_1

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, LA4/e;->getItems()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    if-nez v1, :cond_1

    .line 36
    .line 37
    const/4 v1, 0x1

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    const/4 v1, 0x0

    .line 40
    :goto_1
    if-eqz v1, :cond_2

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    :cond_2
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    if-eqz p1, :cond_3

    .line 47
    .line 48
    invoke-direct {p0, v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->e(Z)V

    .line 49
    .line 50
    .line 51
    :cond_3
    return-void
.end method

.method public final T3(Landroidx/recyclerview/widget/RecyclerView$Adapter;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/recyclerview/widget/RecyclerView$Adapter<",
            "Landroidx/recyclerview/widget/RecyclerView$D;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/gifts/f;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$createLoaderObserver$1;

    .line 4
    .line 5
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$createLoaderObserver$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/q;

    .line 9
    .line 10
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/gifts/q;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 11
    .line 12
    .line 13
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/r;

    .line 14
    .line 15
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/gifts/r;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 16
    .line 17
    .line 18
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/h;

    .line 19
    .line 20
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/gifts/h;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 21
    .line 22
    .line 23
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/i;

    .line 24
    .line 25
    invoke-direct {v6, p0}, Lorg/xplatform/aggregator/impl/gifts/i;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 26
    .line 27
    .line 28
    move-object v1, p1

    .line 29
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/f;-><init>(Landroidx/recyclerview/widget/RecyclerView$Adapter;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 30
    .line 31
    .line 32
    return-object v0
.end method

.method public U2()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v3, Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;->ACTIVE:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 6
    .line 7
    sget v4, Lpb/g;->ic_info_new:I

    .line 8
    .line 9
    new-instance v1, LM01/c;

    .line 10
    .line 11
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/o;

    .line 12
    .line 13
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/gifts/o;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 14
    .line 15
    .line 16
    const/16 v13, 0x3f0

    .line 17
    .line 18
    const/4 v14, 0x0

    .line 19
    const-string v2, "ic_info_new"

    .line 20
    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v8, 0x0

    .line 24
    const/4 v9, 0x0

    .line 25
    const/4 v10, 0x0

    .line 26
    const/4 v11, 0x0

    .line 27
    const/4 v12, 0x0

    .line 28
    invoke-direct/range {v1 .. v14}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 29
    .line 30
    .line 31
    const/4 v2, 0x1

    .line 32
    new-array v3, v2, [LM01/c;

    .line 33
    .line 34
    const/4 v4, 0x0

    .line 35
    aput-object v1, v3, v4

    .line 36
    .line 37
    invoke-static {v3}, Lkotlin/collections/v;->h([Ljava/lang/Object;)Ljava/util/ArrayList;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtons(Ljava/util/ArrayList;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sget v3, LlZ0/d;->uikitSecondary:I

    .line 49
    .line 50
    const/4 v5, 0x2

    .line 51
    const/4 v6, 0x0

    .line 52
    invoke-static {v1, v3, v6, v5, v6}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    invoke-static {v1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtonsColorStateList(Landroid/content/res/ColorStateList;)V

    .line 61
    .line 62
    .line 63
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/p;

    .line 64
    .line 65
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/p;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 66
    .line 67
    .line 68
    invoke-static {v0, v4, v1, v2, v6}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final Y3()Lorg/xplatform/aggregator/impl/core/presentation/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->b1:Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Z3()Laa1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->S1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Laa1/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final a4()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->H1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final d4()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->F1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final e4()Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->P1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public final f4()LSX0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->k1:LSX0/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final g4()LS91/G;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/G;

    .line 13
    .line 14
    return-object v0
.end method

.method public h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->V1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final i4()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->o0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final k4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V
    .locals 14

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Y3()Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->c()J

    .line 9
    .line 10
    .line 11
    move-result-wide v1

    .line 12
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->b()J

    .line 13
    .line 14
    .line 15
    move-result-wide v3

    .line 16
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->a()J

    .line 17
    .line 18
    .line 19
    move-result-wide v5

    .line 20
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->d()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v7

    .line 24
    sget v8, Lpb/k;->casino_category_folder_and_section_description:I

    .line 25
    .line 26
    invoke-virtual {p0, v8}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v8

    .line 30
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->a()J

    .line 31
    .line 32
    .line 33
    move-result-wide v9

    .line 34
    sget-object v11, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 35
    .line 36
    invoke-virtual {v11}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 37
    .line 38
    .line 39
    move-result-wide v11

    .line 40
    cmp-long v13, v9, v11

    .line 41
    .line 42
    if-nez v13, :cond_0

    .line 43
    .line 44
    sget-object p1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 45
    .line 46
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 47
    .line 48
    .line 49
    move-result-wide v9

    .line 50
    goto :goto_0

    .line 51
    :cond_0
    sget-object v11, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 52
    .line 53
    invoke-virtual {v11}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 54
    .line 55
    .line 56
    move-result-wide v11

    .line 57
    cmp-long v13, v9, v11

    .line 58
    .line 59
    if-nez v13, :cond_1

    .line 60
    .line 61
    const-wide v9, 0x7fffffffffffffffL

    .line 62
    .line 63
    .line 64
    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;->a()J

    .line 68
    .line 69
    .line 70
    move-result-wide v9

    .line 71
    :goto_0
    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    invoke-static {p1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object v10

    .line 79
    const/16 v12, 0x80

    .line 80
    .line 81
    const/4 v13, 0x0

    .line 82
    const/4 v9, 0x0

    .line 83
    const/4 v11, 0x0

    .line 84
    invoke-static/range {v0 .. v13}, Lorg/xplatform/aggregator/impl/core/presentation/c;->d(Lorg/xplatform/aggregator/impl/core/presentation/c;JJJLjava/lang/String;Ljava/lang/String;ZLjava/util/List;Ljava/lang/String;ILjava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    return-void
.end method

.method public final l4(Landroidx/recyclerview/widget/RecyclerView;)V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 6
    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 10
    .line 11
    .line 12
    const/4 v0, 0x1

    .line 13
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setHasFixedSize(Z)V

    .line 14
    .line 15
    .line 16
    new-instance v1, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 17
    .line 18
    const/16 v11, 0x1ca

    .line 19
    .line 20
    const/4 v12, 0x0

    .line 21
    const/4 v2, 0x0

    .line 22
    const/4 v3, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    const/4 v7, 0x1

    .line 27
    const/4 v8, 0x0

    .line 28
    const/4 v9, 0x0

    .line 29
    const/4 v10, 0x0

    .line 30
    invoke-direct/range {v1 .. v12}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/g;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/gifts/g;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->e4()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->l()V

    .line 13
    .line 14
    .line 15
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->I1:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 8
    .line 9
    .line 10
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Z3()Laa1/a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->I1:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->n5()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final q4()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final s4(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->H1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 11
    .line 12
    .line 13
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->x4()V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->r4()V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g6()V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iget-object p1, p1, LS91/G;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 31
    .line 32
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->l4(Landroidx/recyclerview/widget/RecyclerView;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public u2()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lia1/b;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lia1/b;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lia1/b;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    new-instance v0, Lia1/f;

    .line 53
    .line 54
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->b4()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->c4()I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->d4()I

    .line 63
    .line 64
    .line 65
    move-result v4

    .line 66
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->a4()Z

    .line 67
    .line 68
    .line 69
    move-result v5

    .line 70
    invoke-direct {v0, v1, v3, v4, v5}, Lia1/f;-><init>(IIIZ)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v2, v0}, Lia1/b;->a(Lia1/f;)Lia1/a;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-interface {v0, p0}, Lia1/a;->a(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 78
    .line 79
    .line 80
    return-void

    .line 81
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 82
    .line 83
    new-instance v2, Ljava/lang/StringBuilder;

    .line 84
    .line 85
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 86
    .line 87
    .line 88
    const-string v3, "Cannot create dependency "

    .line 89
    .line 90
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw v0
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r5()Lkotlinx/coroutines/flow/f0;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 15
    .line 16
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$1;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v8

    .line 30
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/4 v11, 0x3

    .line 37
    const/4 v12, 0x0

    .line 38
    move-object v7, v8

    .line 39
    const/4 v8, 0x0

    .line 40
    const/4 v9, 0x0

    .line 41
    move-object v10, v2

    .line 42
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d6()Lkotlinx/coroutines/flow/e;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$2;

    .line 54
    .line 55
    invoke-direct {v7, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$2;-><init>(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    sget-object v11, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 59
    .line 60
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 61
    .line 62
    .line 63
    move-result-object v5

    .line 64
    invoke-static {v5}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 65
    .line 66
    .line 67
    move-result-object v12

    .line 68
    new-instance v15, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 69
    .line 70
    move-object v6, v11

    .line 71
    move-object v3, v15

    .line 72
    invoke-direct/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 73
    .line 74
    .line 75
    const/16 v16, 0x3

    .line 76
    .line 77
    const/16 v17, 0x0

    .line 78
    .line 79
    const/4 v13, 0x0

    .line 80
    const/4 v14, 0x0

    .line 81
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 82
    .line 83
    .line 84
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->N5()Lkotlinx/coroutines/flow/e;

    .line 89
    .line 90
    .line 91
    move-result-object v9

    .line 92
    new-instance v12, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$3;

    .line 93
    .line 94
    invoke-direct {v12, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    .line 95
    .line 96
    .line 97
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 98
    .line 99
    .line 100
    move-result-object v10

    .line 101
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 102
    .line 103
    .line 104
    move-result-object v2

    .line 105
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 106
    .line 107
    move-object v8, v5

    .line 108
    invoke-direct/range {v8 .. v13}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 109
    .line 110
    .line 111
    const/4 v6, 0x3

    .line 112
    const/4 v7, 0x0

    .line 113
    const/4 v3, 0x0

    .line 114
    const/4 v4, 0x0

    .line 115
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 116
    .line 117
    .line 118
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 119
    .line 120
    .line 121
    move-result-object v2

    .line 122
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->m5()Lkotlinx/coroutines/flow/e;

    .line 123
    .line 124
    .line 125
    move-result-object v9

    .line 126
    new-instance v12, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;

    .line 127
    .line 128
    invoke-direct {v12, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    .line 129
    .line 130
    .line 131
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 132
    .line 133
    .line 134
    move-result-object v10

    .line 135
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 136
    .line 137
    .line 138
    move-result-object v2

    .line 139
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 140
    .line 141
    move-object v8, v5

    .line 142
    invoke-direct/range {v8 .. v13}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 143
    .line 144
    .line 145
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 146
    .line 147
    .line 148
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->h4()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 149
    .line 150
    .line 151
    move-result-object v2

    .line 152
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->z5()Lkotlinx/coroutines/flow/e;

    .line 153
    .line 154
    .line 155
    move-result-object v9

    .line 156
    new-instance v12, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;

    .line 157
    .line 158
    invoke-direct {v12, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lkotlin/coroutines/e;)V

    .line 159
    .line 160
    .line 161
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 162
    .line 163
    .line 164
    move-result-object v10

    .line 165
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 170
    .line 171
    move-object v8, v4

    .line 172
    invoke-direct/range {v8 .. v13}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 173
    .line 174
    .line 175
    const/4 v5, 0x3

    .line 176
    const/4 v6, 0x0

    .line 177
    const/4 v2, 0x0

    .line 178
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 179
    .line 180
    .line 181
    return-void
.end method

.method public final v4(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->F1:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final w4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->g4()LS91/G;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;

    .line 6
    .line 7
    if-eqz v1, :cond_1

    .line 8
    .line 9
    iget-object v1, v0, LS91/G;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 10
    .line 11
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    return-void

    .line 18
    :cond_0
    iget-object v0, v0, LS91/G;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 19
    .line 20
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;

    .line 21
    .line 22
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    sget v1, Lpb/k;->update_again_after:I

    .line 27
    .line 28
    const-wide/16 v2, 0x2710

    .line 29
    .line 30
    invoke-virtual {v0, p1, v1, v2, v3}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 31
    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->S3(Z)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_1
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 39
    .line 40
    if-eqz p1, :cond_3

    .line 41
    .line 42
    iget-object p1, v0, LS91/G;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 43
    .line 44
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    if-nez p1, :cond_2

    .line 49
    .line 50
    const/4 p1, 0x0

    .line 51
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->S3(Z)V

    .line 52
    .line 53
    .line 54
    :cond_2
    return-void

    .line 55
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 56
    .line 57
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 58
    .line 59
    .line 60
    throw p1
.end method

.method public final x4()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/m;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/m;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    const-string v1, "REQUEST_REFUSE_BONUS"

    .line 7
    .line 8
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/n;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/n;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 14
    .line 15
    .line 16
    invoke-static {p0, v1, v0}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method
