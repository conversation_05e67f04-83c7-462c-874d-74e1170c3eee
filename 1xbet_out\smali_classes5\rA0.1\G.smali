.class public final synthetic LrA0/G;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/G;->a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LrA0/G;->a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    check-cast p1, Landroid/graphics/drawable/Drawable;

    invoke-static {v0, p1}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/StadiumInfoAdapterDelegateKt;->c(Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
