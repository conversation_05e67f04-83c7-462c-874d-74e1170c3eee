.class public final LNR0/b$a;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNR0/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0002\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\u000c\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\r\u0010\u000e\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u0010R \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "LNR0/b$a;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LiR0/e;",
        "binding",
        "Lkotlin/Function1;",
        "",
        "",
        "onItemClick",
        "<init>",
        "(LiR0/e;Lkotlin/jvm/functions/Function1;)V",
        "LOR0/b;",
        "item",
        "e",
        "(LOR0/b;)V",
        "g",
        "()V",
        "LiR0/e;",
        "f",
        "Lkotlin/jvm/functions/Function1;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LiR0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LiR0/e;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # LiR0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LiR0/e;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LiR0/e;->b()Landroid/widget/LinearLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LNR0/b$a;->e:LiR0/e;

    .line 9
    .line 10
    iput-object p2, p0, LNR0/b$a;->f:Lkotlin/jvm/functions/Function1;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic d(LNR0/b$a;LOR0/b;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LNR0/b$a;->f(LNR0/b$a;LOR0/b;Landroid/view/View;)V

    return-void
.end method

.method public static final f(LNR0/b$a;LOR0/b;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, LNR0/b$a;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-virtual {p1}, LOR0/b;->b()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final e(LOR0/b;)V
    .locals 14
    .param p1    # LOR0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p1}, LOR0/b;->a()Ljava/lang/String;

    .line 3
    .line 4
    .line 5
    move-result-object v1

    .line 6
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-nez v1, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, LNR0/b$a;->e:LiR0/e;

    .line 13
    .line 14
    iget-object v0, v0, LiR0/e;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 15
    .line 16
    const/16 v1, 0x8

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    iget-object v1, p0, LNR0/b$a;->e:LiR0/e;

    .line 23
    .line 24
    iget-object v1, v1, LiR0/e;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 25
    .line 26
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 27
    .line 28
    .line 29
    sget-object v2, LCX0/l;->a:LCX0/l;

    .line 30
    .line 31
    iget-object v1, p0, LNR0/b$a;->e:LiR0/e;

    .line 32
    .line 33
    iget-object v3, v1, LiR0/e;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 34
    .line 35
    sget-object v1, LPN0/b;->a:LPN0/b;

    .line 36
    .line 37
    invoke-virtual {p1}, LOR0/b;->a()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    invoke-virtual {v1, v4}, LPN0/b;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    const/4 v1, 0x1

    .line 46
    new-array v8, v1, [LYW0/d;

    .line 47
    .line 48
    sget-object v1, LYW0/d$c;->a:LYW0/d$c;

    .line 49
    .line 50
    aput-object v1, v8, v0

    .line 51
    .line 52
    const/16 v12, 0xee

    .line 53
    .line 54
    const/4 v13, 0x0

    .line 55
    const/4 v5, 0x0

    .line 56
    const/4 v6, 0x0

    .line 57
    const/4 v7, 0x0

    .line 58
    const/4 v9, 0x0

    .line 59
    const/4 v10, 0x0

    .line 60
    const/4 v11, 0x0

    .line 61
    invoke-static/range {v2 .. v13}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    :goto_0
    iget-object v0, p0, LNR0/b$a;->e:LiR0/e;

    .line 65
    .line 66
    iget-object v0, v0, LiR0/e;->c:Landroid/widget/TextView;

    .line 67
    .line 68
    invoke-virtual {p1}, LOR0/b;->c()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 73
    .line 74
    .line 75
    iget-object v0, p0, LNR0/b$a;->e:LiR0/e;

    .line 76
    .line 77
    iget-object v0, v0, LiR0/e;->d:Landroid/widget/TextView;

    .line 78
    .line 79
    invoke-virtual {p1}, LOR0/b;->d()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 84
    .line 85
    .line 86
    iget-object v0, p0, LNR0/b$a;->e:LiR0/e;

    .line 87
    .line 88
    invoke-virtual {v0}, LiR0/e;->b()Landroid/widget/LinearLayout;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    new-instance v1, LNR0/a;

    .line 93
    .line 94
    invoke-direct {v1, p0, p1}, LNR0/a;-><init>(LNR0/b$a;LOR0/b;)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 98
    .line 99
    .line 100
    return-void
.end method

.method public final g()V
    .locals 2

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    iget-object v1, p0, LNR0/b$a;->e:LiR0/e;

    .line 4
    .line 5
    iget-object v1, v1, LiR0/e;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, LCX0/l;->j(Landroid/widget/ImageView;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
