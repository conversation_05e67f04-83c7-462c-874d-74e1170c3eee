.class public final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/c;
.source "SourceFile"

# interfaces
.implements LZ40/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;,
        Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$b;,
        Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c;,
        Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0094\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008>\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008-\u0018\u0000 \u0098\u00022\u00020\u00012\u00020\u0002:\u0008\u0099\u0002\u009a\u0002\u009b\u0002\u009c\u0002B\u0085\u0002\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\u0006\u0010\u0018\u001a\u00020\u0017\u0012\u0006\u0010\u001a\u001a\u00020\u0019\u0012\u0006\u0010\u001c\u001a\u00020\u001b\u0012\u0006\u0010\u001e\u001a\u00020\u001d\u0012\u0006\u0010 \u001a\u00020\u001f\u0012\u0006\u0010\"\u001a\u00020!\u0012\u0006\u0010$\u001a\u00020#\u0012\u0006\u0010&\u001a\u00020%\u0012\u0006\u0010(\u001a\u00020\'\u0012\u0006\u0010*\u001a\u00020)\u0012\u0006\u0010,\u001a\u00020+\u0012\u0006\u0010.\u001a\u00020-\u0012\u0006\u00100\u001a\u00020/\u0012\u0006\u00102\u001a\u000201\u0012\u0006\u00104\u001a\u000203\u0012\u0006\u00106\u001a\u000205\u0012\u0006\u00108\u001a\u000207\u0012\u0008\u0008\u0001\u0010:\u001a\u000209\u0012\u0008\u0008\u0001\u0010<\u001a\u00020;\u0012\u0006\u0010>\u001a\u00020=\u0012\u0006\u0010@\u001a\u00020?\u00a2\u0006\u0004\u0008A\u0010BJ\u001f\u0010H\u001a\u00020G2\u0006\u0010D\u001a\u00020C2\u0006\u0010F\u001a\u00020EH\u0002\u00a2\u0006\u0004\u0008H\u0010IJ\u000f\u0010J\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008J\u0010KJ\u001d\u0010N\u001a\u00020G2\u000c\u0010M\u001a\u0008\u0012\u0004\u0012\u00020C0LH\u0002\u00a2\u0006\u0004\u0008N\u0010OJ\u000f\u0010P\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008P\u0010KJ\u000f\u0010Q\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008Q\u0010KJ\u0017\u0010R\u001a\u00020G2\u0006\u0010D\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008R\u0010SJ\u0018\u0010T\u001a\u00020G2\u0006\u0010F\u001a\u00020EH\u0082@\u00a2\u0006\u0004\u0008T\u0010UJ\u0017\u0010X\u001a\u00020G2\u0006\u0010W\u001a\u00020VH\u0002\u00a2\u0006\u0004\u0008X\u0010YJ%\u0010\\\u001a\u00020G2\u000c\u0010[\u001a\u0008\u0012\u0004\u0012\u00020Z0L2\u0006\u0010W\u001a\u00020VH\u0002\u00a2\u0006\u0004\u0008\\\u0010]J\u0017\u0010^\u001a\u00020G2\u0006\u0010D\u001a\u00020CH\u0002\u00a2\u0006\u0004\u0008^\u0010SJ\u000f\u0010_\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008_\u0010KJ\u000f\u0010`\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008`\u0010KJ\u000f\u0010a\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008a\u0010KJ\u000f\u0010b\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008b\u0010KJ\u000f\u0010c\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008c\u0010KJ\u001d\u0010f\u001a\u00020G2\u000c\u0010e\u001a\u0008\u0012\u0004\u0012\u00020d0LH\u0002\u00a2\u0006\u0004\u0008f\u0010OJ\u000f\u0010g\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008g\u0010KJ\u000f\u0010h\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008h\u0010KJ\u0010\u0010j\u001a\u00020iH\u0082@\u00a2\u0006\u0004\u0008j\u0010kJ#\u0010l\u001a\u0008\u0012\u0004\u0012\u00020d0L2\u000c\u0010e\u001a\u0008\u0012\u0004\u0012\u00020d0LH\u0002\u00a2\u0006\u0004\u0008l\u0010mJ\u001f\u0010r\u001a\u00020G2\u0006\u0010o\u001a\u00020n2\u0006\u0010q\u001a\u00020pH\u0002\u00a2\u0006\u0004\u0008r\u0010sJ^\u0010\u0080\u0001\u001a\u0008\u0012\u0004\u0012\u00020\u007f0L2\u000c\u0010v\u001a\u0008\u0012\u0004\u0012\u00020u0t2\u000c\u0010x\u001a\u0008\u0012\u0004\u0012\u00020w0t2\u000c\u0010z\u001a\u0008\u0012\u0004\u0012\u00020y0t2\u000c\u0010|\u001a\u0008\u0012\u0004\u0012\u00020{0t2\u000c\u0010~\u001a\u0008\u0012\u0004\u0012\u00020}0tH\u0002\u00a2\u0006\u0006\u0008\u0080\u0001\u0010\u0081\u0001J\u001b\u0010\u0083\u0001\u001a\u00020G2\u0007\u0010\u0082\u0001\u001a\u00020iH\u0002\u00a2\u0006\u0006\u0008\u0083\u0001\u0010\u0084\u0001J\u001b\u0010\u0086\u0001\u001a\u00020G2\u0007\u0010\u0085\u0001\u001a\u00020iH\u0002\u00a2\u0006\u0006\u0008\u0086\u0001\u0010\u0084\u0001J\u001c\u0010\u0089\u0001\u001a\u00020G2\u0008\u0010\u0088\u0001\u001a\u00030\u0087\u0001H\u0002\u00a2\u0006\u0006\u0008\u0089\u0001\u0010\u008a\u0001J7\u0010\u008f\u0001\u001a\u00020G2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u00012\u0006\u0010F\u001a\u00020E2\u0008\u0010\u008d\u0001\u001a\u00030\u008b\u00012\u0007\u0010\u008e\u0001\u001a\u00020iH\u0016\u00a2\u0006\u0006\u0008\u008f\u0001\u0010\u0090\u0001J&\u0010\u0091\u0001\u001a\u00020G2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u00012\u0008\u0010\u008d\u0001\u001a\u00030\u008b\u0001H\u0016\u00a2\u0006\u0006\u0008\u0091\u0001\u0010\u0092\u0001J%\u0010\u0094\u0001\u001a\u00020G2\u0007\u0010\u0093\u0001\u001a\u00020p2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u0001H\u0016\u00a2\u0006\u0006\u0008\u0094\u0001\u0010\u0095\u0001J-\u0010\u0096\u0001\u001a\u00020G2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u00012\u0007\u0010\u0093\u0001\u001a\u00020p2\u0006\u0010q\u001a\u00020pH\u0016\u00a2\u0006\u0006\u0008\u0096\u0001\u0010\u0097\u0001J\u0018\u0010\u009a\u0001\u001a\n\u0012\u0005\u0012\u00030\u0099\u00010\u0098\u0001\u00a2\u0006\u0006\u0008\u009a\u0001\u0010\u009b\u0001J\u0018\u0010\u009e\u0001\u001a\n\u0012\u0005\u0012\u00030\u009d\u00010\u009c\u0001\u00a2\u0006\u0006\u0008\u009e\u0001\u0010\u009f\u0001J\u001d\u0010\u00a1\u0001\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u007f0L0\u00a0\u0001\u00a2\u0006\u0006\u0008\u00a1\u0001\u0010\u00a2\u0001J\u0018\u0010\u00a4\u0001\u001a\n\u0012\u0005\u0012\u00030\u00a3\u00010\u009c\u0001\u00a2\u0006\u0006\u0008\u00a4\u0001\u0010\u009f\u0001J\u0011\u0010\u00a5\u0001\u001a\u00020GH\u0000\u00a2\u0006\u0005\u0008\u00a5\u0001\u0010KJ\u0011\u0010\u00a6\u0001\u001a\u00020GH\u0000\u00a2\u0006\u0005\u0008\u00a6\u0001\u0010KR\u0016\u0010\u0004\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001R\u0016\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a9\u0001\u0010\u00aa\u0001R\u0016\u0010\u0008\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ab\u0001\u0010\u00ac\u0001R\u0016\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0001\u0010\u00ae\u0001R\u0016\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0001\u0010\u00b0\u0001R\u0016\u0010\u000e\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00b2\u0001R\u0016\u0010\u0010\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u0016\u0010\u0012\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b5\u0001\u0010\u00b6\u0001R\u0016\u0010\u0014\u001a\u00020\u00138\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b7\u0001\u0010\u00b8\u0001R\u0016\u0010\u0016\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001R\u0016\u0010\u0018\u001a\u00020\u00178\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bb\u0001\u0010\u00bc\u0001R\u0016\u0010\u001a\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bd\u0001\u0010\u00be\u0001R\u0016\u0010\u001c\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0094\u0001\u0010\u00bf\u0001R\u0016\u0010\u001e\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00c1\u0001R\u0016\u0010 \u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0001\u0010\u00c3\u0001R\u0016\u0010\"\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c4\u0001\u0010\u00c5\u0001R\u0016\u0010$\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0001\u0010\u00c7\u0001R\u0016\u0010&\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c8\u0001\u0010\u00c9\u0001R\u0016\u0010(\u001a\u00020\'8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ca\u0001\u0010\u00cb\u0001R\u0016\u0010*\u001a\u00020)8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cc\u0001\u0010\u00cd\u0001R\u0016\u0010,\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ce\u0001\u0010\u00cf\u0001R\u0016\u0010.\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d0\u0001\u0010\u00d1\u0001R\u0016\u00100\u001a\u00020/8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d2\u0001\u0010\u00d3\u0001R\u0015\u00102\u001a\u0002018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008h\u0010\u00d4\u0001R\u0015\u00104\u001a\u0002038\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008T\u0010\u00d5\u0001R\u0016\u00106\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d6\u0001\u0010\u00d7\u0001R\u0016\u00108\u001a\u0002078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d8\u0001\u0010\u00d9\u0001R\u0016\u0010:\u001a\u0002098\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00da\u0001\u0010\u00db\u0001R\u0016\u0010<\u001a\u00020;8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00dc\u0001\u0010\u00dd\u0001R\u0016\u0010>\u001a\u00020=8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00de\u0001\u0010\u00df\u0001R\u0016\u0010@\u001a\u00020?8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e0\u0001\u0010\u00e1\u0001R\u001f\u0010\u00e5\u0001\u001a\n\u0012\u0005\u0012\u00030\u0099\u00010\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e3\u0001\u0010\u00e4\u0001R\u001f\u0010\u00e9\u0001\u001a\n\u0012\u0005\u0012\u00030\u009d\u00010\u00e6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e7\u0001\u0010\u00e8\u0001R\u001f\u0010\u00eb\u0001\u001a\n\u0012\u0005\u0012\u00030\u00a3\u00010\u00e6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ea\u0001\u0010\u00e8\u0001R\u0018\u0010\u00ef\u0001\u001a\u00030\u00ec\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ed\u0001\u0010\u00ee\u0001R\u001c\u0010\u00f3\u0001\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00f1\u0001\u0010\u00f2\u0001R\u0019\u0010\u00f6\u0001\u001a\u00020i8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00f4\u0001\u0010\u00f5\u0001R\u001f\u0010\u00f9\u0001\u001a\u0008\u0012\u0004\u0012\u00020n0L8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00f7\u0001\u0010\u00f8\u0001R\u001c\u0010\u00fb\u0001\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00fa\u0001\u0010\u00f2\u0001R\u001c\u0010\u00fd\u0001\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00fc\u0001\u0010\u00f2\u0001R\u001c\u0010\u00ff\u0001\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00fe\u0001\u0010\u00f2\u0001R\u001c\u0010\u0081\u0002\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0080\u0002\u0010\u00f2\u0001R\u001c\u0010\u0083\u0002\u001a\u0005\u0018\u00010\u00f0\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0082\u0002\u0010\u00f2\u0001R\u0019\u0010\u0085\u0002\u001a\u00020i8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0002\u0010\u00f5\u0001R\u0019\u0010\u0087\u0002\u001a\u00020i8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0086\u0002\u0010\u00f5\u0001R\u001b\u0010\u008a\u0002\u001a\u0004\u0018\u00010C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0002\u0010\u0089\u0002R$\u0010\u008c\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020u0t0\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0002\u0010\u00e4\u0001R$\u0010\u008e\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020w0t0\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0002\u0010\u00e4\u0001R$\u0010\u0090\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020y0t0\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0002\u0010\u00e4\u0001R$\u0010\u0092\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020{0t0\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0002\u0010\u00e4\u0001R$\u0010\u0094\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020}0t0\u00e2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0002\u0010\u00e4\u0001R$\u0010\u0097\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u007f0L0\u00a0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0002\u0010\u0096\u0002\u00a8\u0006\u009d\u0002"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/c;",
        "LZ40/a;",
        "LDg/c;",
        "oneXGamesAnalytics",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lv30/b;",
        "getGameItemsByCategoryScenario",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Li8/j;",
        "getServiceUseCase",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "getGameMetaUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "LJT/c;",
        "addOneXGameLastActionUseCase",
        "Lhf0/a;",
        "getBannerFeedEnableUseCase",
        "LwX0/a;",
        "appScreensProvider",
        "LHX0/e;",
        "resourceManager",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "Lw30/o;",
        "getGamesSectionWalletUseCase",
        "LR40/c;",
        "jackpotUseCase",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lkc1/a;",
        "getActionBannerListScenario",
        "Lw30/i;",
        "getDemoAvailableForGameScenario",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lw30/b;",
        "clearAllGamesInfoUseCase",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LSR/a;",
        "popularFatmanLogger",
        "LpS/b;",
        "oneXGamesFatmanLogger",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "newsAnalytics",
        "Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;",
        "popularOneXGamesLuckyWheelUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lv30/a;",
        "getCenterOfAttentionGameScenario",
        "LwX0/c;",
        "router",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "Lp30/a;",
        "oneXGamesUpdateGameStatusesViewModelDelegate",
        "Lorg/xbet/games_section/feature/popular/presentation/b;",
        "buildContentDelegate",
        "<init>",
        "(LDg/c;Lorg/xbet/core/domain/usecases/d;Lv30/b;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lm8/a;LJT/c;Lhf0/a;LwX0/a;LHX0/e;LVg0/a;Lw30/o;LR40/c;Lgk/b;Lkc1/a;Lw30/i;Lp9/c;Lw30/b;LSX0/c;LSR/a;LpS/b;Lcom/xbet/onexuser/domain/user/c;Lorg/xbet/analytics/domain/scope/NewsAnalytics;Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lorg/xbet/remoteconfig/domain/usecases/i;Lv30/a;LwX0/c;Landroidx/lifecycle/Q;Lp30/a;Lorg/xbet/games_section/feature/popular/presentation/b;)V",
        "",
        "gameId",
        "Lb50/g;",
        "oneXGameWithCategoryUiModel",
        "",
        "W4",
        "(JLb50/g;)V",
        "t4",
        "()V",
        "",
        "gameIdList",
        "f5",
        "(Ljava/util/List;)V",
        "M4",
        "c5",
        "r4",
        "(J)V",
        "X4",
        "(Lb50/g;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
        "gameType",
        "S4",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "Lg9/i;",
        "balances",
        "a5",
        "(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "Y4",
        "O4",
        "A4",
        "F4",
        "w4",
        "C4",
        "Ls30/b;",
        "gameItemsWithCategoryList",
        "s4",
        "y4",
        "H4",
        "",
        "N4",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "u4",
        "(Ljava/util/List;)Ljava/util/List;",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banner",
        "",
        "position",
        "U4",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V",
        "Lorg/xbet/games_section/feature/popular/presentation/a;",
        "Lb50/c;",
        "jackpotState",
        "Lb50/e;",
        "bannersState",
        "Lb50/i;",
        "gamesState",
        "Lb50/a;",
        "centerOfAttentionGameState",
        "Lb50/d;",
        "luckyWheelState",
        "LVX0/i;",
        "E4",
        "(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;",
        "emptyGamesEvents",
        "d5",
        "(Z)V",
        "emptyGameEvents",
        "b5",
        "",
        "throwable",
        "L4",
        "(Ljava/lang/Throwable;)V",
        "",
        "screenName",
        "categoryId",
        "fromBanner",
        "i1",
        "(Ljava/lang/String;Lb50/g;Ljava/lang/String;Z)V",
        "k",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "bannerId",
        "F2",
        "(ILjava/lang/String;)V",
        "M1",
        "(Ljava/lang/String;II)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;",
        "K4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c;",
        "J4",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lkotlinx/coroutines/flow/f0;",
        "B4",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;",
        "v4",
        "Q4",
        "R4",
        "x1",
        "LDg/c;",
        "y1",
        "Lorg/xbet/core/domain/usecases/d;",
        "F1",
        "Lv30/b;",
        "H1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "I1",
        "Li8/j;",
        "P1",
        "Lorg/xbet/core/domain/usecases/game_info/n;",
        "S1",
        "Lm8/a;",
        "V1",
        "LJT/c;",
        "b2",
        "Lhf0/a;",
        "v2",
        "LwX0/a;",
        "x2",
        "LHX0/e;",
        "y2",
        "LVg0/a;",
        "Lw30/o;",
        "H2",
        "LR40/c;",
        "I2",
        "Lgk/b;",
        "P2",
        "Lkc1/a;",
        "S2",
        "Lw30/i;",
        "V2",
        "Lp9/c;",
        "X2",
        "Lw30/b;",
        "F3",
        "LSX0/c;",
        "H3",
        "LSR/a;",
        "I3",
        "LpS/b;",
        "S3",
        "Lcom/xbet/onexuser/domain/user/c;",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;",
        "v5",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "w5",
        "Lv30/a;",
        "x5",
        "LwX0/c;",
        "y5",
        "Landroidx/lifecycle/Q;",
        "z5",
        "Lp30/a;",
        "A5",
        "Lorg/xbet/games_section/feature/popular/presentation/b;",
        "Lkotlinx/coroutines/flow/V;",
        "B5",
        "Lkotlinx/coroutines/flow/V;",
        "viewStateFlow",
        "Lkotlinx/coroutines/flow/U;",
        "C5",
        "Lkotlinx/coroutines/flow/U;",
        "notification",
        "D5",
        "actionBannerEvent",
        "Lek0/o;",
        "E5",
        "Lek0/o;",
        "remoteConfig",
        "Lkotlinx/coroutines/x0;",
        "F5",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "G5",
        "Z",
        "currentConnectionState",
        "H5",
        "Ljava/util/List;",
        "bannerList",
        "I5",
        "loadJackpotJob",
        "J5",
        "loadBannersJob",
        "K5",
        "loadGamesJob",
        "L5",
        "loadLuckyWheelJob",
        "M5",
        "loadCenterOfAttentionJob",
        "N5",
        "showBanners",
        "O5",
        "gameRequestWithAuth",
        "P5",
        "Ljava/lang/Long;",
        "lastUpdateJackpotTime",
        "Q5",
        "jackpotInfoModelFlow",
        "R5",
        "bannersFlow",
        "S5",
        "oneXGamesWithCategoryListFlow",
        "T5",
        "centerOfAttentionFlow",
        "U5",
        "luckyWheelModelFlow",
        "V5",
        "Lkotlinx/coroutines/flow/f0;",
        "mutableContentListsState",
        "W5",
        "d",
        "c",
        "a",
        "b",
        "popular_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final W5:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xbet/games_section/feature/popular/presentation/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F1:Lv30/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lw30/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F5:Lkotlinx/coroutines/x0;

.field public G5:Z

.field public final H1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:LR40/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:LSR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lorg/xbet/analytics/domain/scope/NewsAnalytics;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H5:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:LpS/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I5:Lkotlinx/coroutines/x0;

.field public J5:Lkotlinx/coroutines/x0;

.field public K5:Lkotlinx/coroutines/x0;

.field public L5:Lkotlinx/coroutines/x0;

.field public M5:Lkotlinx/coroutines/x0;

.field public N5:Z

.field public O5:Z

.field public final P1:Lorg/xbet/core/domain/usecases/game_info/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lkc1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public P5:Ljava/lang/Long;

.field public final Q5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/c;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/e;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lw30/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final U5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "Lb50/d;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LJT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V5:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lw30/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lhf0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lv30/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LDg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lp30/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->W5:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$b;

    return-void
.end method

.method public constructor <init>(LDg/c;Lorg/xbet/core/domain/usecases/d;Lv30/b;Lorg/xbet/ui_common/utils/internet/a;Li8/j;Lorg/xbet/core/domain/usecases/game_info/n;Lm8/a;LJT/c;Lhf0/a;LwX0/a;LHX0/e;LVg0/a;Lw30/o;LR40/c;Lgk/b;Lkc1/a;Lw30/i;Lp9/c;Lw30/b;LSX0/c;LSR/a;LpS/b;Lcom/xbet/onexuser/domain/user/c;Lorg/xbet/analytics/domain/scope/NewsAnalytics;Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;Lorg/xbet/remoteconfig/domain/usecases/i;Lv30/a;LwX0/c;Landroidx/lifecycle/Q;Lp30/a;Lorg/xbet/games_section/feature/popular/presentation/b;)V
    .locals 5
    .param p1    # LDg/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lv30/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/core/domain/usecases/game_info/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LJT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lhf0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lw30/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LR40/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lkc1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lw30/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lw30/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LSR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # LpS/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/analytics/domain/scope/NewsAnalytics;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lv30/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lp30/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # Lorg/xbet/games_section/feature/popular/presentation/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p29

    .line 2
    .line 3
    move-object/from16 v1, p30

    .line 4
    .line 5
    move-object/from16 v2, p31

    .line 6
    .line 7
    const/4 v3, 0x2

    .line 8
    new-array v3, v3, [Lorg/xbet/ui_common/viewmodel/core/k;

    .line 9
    .line 10
    const/4 v4, 0x0

    .line 11
    aput-object v1, v3, v4

    .line 12
    .line 13
    const/4 v4, 0x1

    .line 14
    aput-object v2, v3, v4

    .line 15
    .line 16
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-direct {p0, v0, v3}, Lorg/xbet/ui_common/viewmodel/core/c;-><init>(Landroidx/lifecycle/Q;Ljava/util/List;)V

    .line 21
    .line 22
    .line 23
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x1:LDg/c;

    .line 24
    .line 25
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y1:Lorg/xbet/core/domain/usecases/d;

    .line 26
    .line 27
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F1:Lv30/b;

    .line 28
    .line 29
    iput-object p4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 30
    .line 31
    iput-object p5, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I1:Li8/j;

    .line 32
    .line 33
    iput-object p6, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P1:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 34
    .line 35
    iput-object p7, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 36
    .line 37
    iput-object p8, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V1:LJT/c;

    .line 38
    .line 39
    iput-object p9, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->b2:Lhf0/a;

    .line 40
    .line 41
    iput-object p10, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v2:LwX0/a;

    .line 42
    .line 43
    move-object/from16 p2, p11

    .line 44
    .line 45
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x2:LHX0/e;

    .line 46
    .line 47
    move-object/from16 p2, p12

    .line 48
    .line 49
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y2:LVg0/a;

    .line 50
    .line 51
    move-object/from16 p2, p13

    .line 52
    .line 53
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F2:Lw30/o;

    .line 54
    .line 55
    move-object/from16 p2, p14

    .line 56
    .line 57
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H2:LR40/c;

    .line 58
    .line 59
    move-object/from16 p2, p15

    .line 60
    .line 61
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I2:Lgk/b;

    .line 62
    .line 63
    move-object/from16 p2, p16

    .line 64
    .line 65
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P2:Lkc1/a;

    .line 66
    .line 67
    move-object/from16 p2, p17

    .line 68
    .line 69
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S2:Lw30/i;

    .line 70
    .line 71
    move-object/from16 p2, p18

    .line 72
    .line 73
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V2:Lp9/c;

    .line 74
    .line 75
    move-object/from16 p2, p19

    .line 76
    .line 77
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X2:Lw30/b;

    .line 78
    .line 79
    move-object/from16 p2, p20

    .line 80
    .line 81
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F3:LSX0/c;

    .line 82
    .line 83
    move-object/from16 p2, p21

    .line 84
    .line 85
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H3:LSR/a;

    .line 86
    .line 87
    move-object/from16 p2, p22

    .line 88
    .line 89
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I3:LpS/b;

    .line 90
    .line 91
    move-object/from16 p2, p23

    .line 92
    .line 93
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S3:Lcom/xbet/onexuser/domain/user/c;

    .line 94
    .line 95
    move-object/from16 p2, p24

    .line 96
    .line 97
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 98
    .line 99
    move-object/from16 p2, p25

    .line 100
    .line 101
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X4:Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;

    .line 102
    .line 103
    move-object/from16 p2, p26

    .line 104
    .line 105
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v5:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 106
    .line 107
    move-object/from16 p3, p27

    .line 108
    .line 109
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->w5:Lv30/a;

    .line 110
    .line 111
    move-object/from16 p3, p28

    .line 112
    .line 113
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 114
    .line 115
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y5:Landroidx/lifecycle/Q;

    .line 116
    .line 117
    iput-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->z5:Lp30/a;

    .line 118
    .line 119
    iput-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->A5:Lorg/xbet/games_section/feature/popular/presentation/b;

    .line 120
    .line 121
    sget-object p3, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;

    .line 122
    .line 123
    invoke-static {p3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 124
    .line 125
    .line 126
    move-result-object p3

    .line 127
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 128
    .line 129
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 130
    .line 131
    .line 132
    move-result-object p3

    .line 133
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 134
    .line 135
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 136
    .line 137
    .line 138
    move-result-object p3

    .line 139
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->D5:Lkotlinx/coroutines/flow/U;

    .line 140
    .line 141
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 142
    .line 143
    .line 144
    move-result-object p2

    .line 145
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->E5:Lek0/o;

    .line 146
    .line 147
    iput-boolean v4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G5:Z

    .line 148
    .line 149
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 150
    .line 151
    .line 152
    move-result-object p2

    .line 153
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 154
    .line 155
    sget-object p2, Lorg/xbet/games_section/feature/popular/presentation/a$a;->a:Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 156
    .line 157
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 158
    .line 159
    .line 160
    move-result-object p3

    .line 161
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 162
    .line 163
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 164
    .line 165
    .line 166
    move-result-object p4

    .line 167
    iput-object p4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 168
    .line 169
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 170
    .line 171
    .line 172
    move-result-object p5

    .line 173
    iput-object p5, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 174
    .line 175
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 176
    .line 177
    .line 178
    move-result-object p6

    .line 179
    iput-object p6, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T5:Lkotlinx/coroutines/flow/V;

    .line 180
    .line 181
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 182
    .line 183
    .line 184
    move-result-object p2

    .line 185
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 186
    .line 187
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;

    .line 188
    .line 189
    const/4 v1, 0x0

    .line 190
    invoke-direct {v0, p0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 191
    .line 192
    .line 193
    move-object/from16 p12, p2

    .line 194
    .line 195
    move-object p8, p3

    .line 196
    move-object p9, p4

    .line 197
    move-object p10, p5

    .line 198
    move-object/from16 p11, p6

    .line 199
    .line 200
    move-object/from16 p13, v0

    .line 201
    .line 202
    invoke-static/range {p8 .. p13}, Lkotlinx/coroutines/flow/g;->r(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/q;)Lkotlinx/coroutines/flow/e;

    .line 203
    .line 204
    .line 205
    move-result-object p2

    .line 206
    new-instance p3, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$2;

    .line 207
    .line 208
    invoke-direct {p3, p0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$mutableContentListsState$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 209
    .line 210
    .line 211
    invoke-static {p2, p3}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 212
    .line 213
    .line 214
    move-result-object p2

    .line 215
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 216
    .line 217
    .line 218
    move-result-object p3

    .line 219
    invoke-interface {p7}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 220
    .line 221
    .line 222
    move-result-object p1

    .line 223
    invoke-static {p3, p1}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 224
    .line 225
    .line 226
    move-result-object p1

    .line 227
    sget-object p3, Lkotlinx/coroutines/flow/d0;->a:Lkotlinx/coroutines/flow/d0$a;

    .line 228
    .line 229
    const/4 p4, 0x3

    .line 230
    const/4 p5, 0x0

    .line 231
    const-wide/16 v0, 0x0

    .line 232
    .line 233
    const-wide/16 v2, 0x0

    .line 234
    .line 235
    move-object p9, p5

    .line 236
    move-wide p4, v0

    .line 237
    move-wide p6, v2

    .line 238
    const/4 p8, 0x3

    .line 239
    invoke-static/range {p3 .. p9}, Lkotlinx/coroutines/flow/d0$a;->b(Lkotlinx/coroutines/flow/d0$a;JJILjava/lang/Object;)Lkotlinx/coroutines/flow/d0;

    .line 240
    .line 241
    .line 242
    move-result-object p3

    .line 243
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 244
    .line 245
    .line 246
    move-result-object p4

    .line 247
    invoke-static {p2, p1, p3, p4}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 248
    .line 249
    .line 250
    move-result-object p1

    .line 251
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V5:Lkotlinx/coroutines/flow/f0;

    .line 252
    .line 253
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->O4()V

    .line 254
    .line 255
    .line 256
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->r4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->s4(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->u4(Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/U;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->D5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final D4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$2$1;

    .line 9
    .line 10
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y1:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$2$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LJT/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V1:LJT/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LwX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v2:LwX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final F4()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 20
    .line 21
    if-nez v0, :cond_1

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 26
    .line 27
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    :cond_1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v10

    .line 40
    new-instance v8, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {v8, p0, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/q;

    .line 47
    .line 48
    invoke-direct {v11, p0}, Lorg/xbet/games_section/feature/popular/presentation/q;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 49
    .line 50
    .line 51
    const/16 v13, 0x128

    .line 52
    .line 53
    const/4 v14, 0x0

    .line 54
    const-string v3, "PopularOneXGamesViewModel.getJackpot"

    .line 55
    .line 56
    const/4 v4, 0x3

    .line 57
    const-wide/16 v5, 0x3

    .line 58
    .line 59
    const/4 v7, 0x0

    .line 60
    const/4 v9, 0x0

    .line 61
    const/4 v12, 0x0

    .line 62
    invoke-static/range {v2 .. v14}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 67
    .line 68
    return-void
.end method

.method public static final synthetic G3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final G4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$2$1;

    .line 9
    .line 10
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y1:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getJackpot$2$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final I4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y1:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->A4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->O5:Z

    .line 2
    .line 3
    return p0
.end method

.method private final L4(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$handleGameError$1;->INSTANCE:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$handleGameError$1;

    .line 12
    .line 13
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$handleGameError$2;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$handleGameError$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xa

    .line 20
    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final synthetic M3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkc1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P2:Lkc1/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V2:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method private final N4(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I1:Li8/j;

    .line 54
    .line 55
    invoke-interface {p1}, Li8/j;->invoke()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P1:Lorg/xbet/core/domain/usecases/game_info/n;

    .line 60
    .line 61
    sget-object v4, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 62
    .line 63
    invoke-virtual {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v4

    .line 67
    iput v3, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$luckyWheelEnabled$1;->label:I

    .line 68
    .line 69
    invoke-virtual {v2, v4, v5, p1, v0}, Lorg/xbet/core/domain/usecases/game_info/n;->a(JLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    if-ne p1, v1, :cond_3

    .line 74
    .line 75
    return-object v1

    .line 76
    :cond_3
    :goto_1
    check-cast p1, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 77
    .line 78
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getEnable()Z

    .line 79
    .line 80
    .line 81
    move-result p1

    .line 82
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    return-object p1
.end method

.method public static final synthetic O3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lv30/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->w5:Lv30/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final O4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;

    .line 17
    .line 18
    invoke-direct {v2, p0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$2;

    .line 26
    .line 27
    invoke-direct {v2, p0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 39
    .line 40
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    return-void
.end method

.method public static final synthetic P3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I2:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final P4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lw30/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S2:Lw30/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lv30/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F1:Lv30/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic S3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lw30/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F2:Lw30/o;

    .line 2
    .line 3
    return-object p0
.end method

.method private final S4(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onWebGameClicked$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onWebGameClicked$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v2, Lorg/xbet/games_section/feature/popular/presentation/r;

    .line 17
    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/popular/presentation/r;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 19
    .line 20
    .line 21
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onWebGameClicked$3;

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-direct {v5, p0, p1, v4}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onWebGameClicked$3;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/16 v6, 0x8

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public static final synthetic T3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->E4(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final T4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$c;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$c;-><init>(Z)V

    .line 7
    .line 8
    .line 9
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final synthetic U3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final U4(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/k;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/k;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static final synthetic V3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LR40/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H2:LR40/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final V4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$1$1;->INSTANCE:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$1$1;

    .line 6
    .line 7
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$1$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openBannerInfo$1$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    const/16 v6, 0xe

    .line 14
    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method

.method public static final synthetic W3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Ljava/lang/Long;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P5:Ljava/lang/Long;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic X3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Y3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Z3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X4:Lorg/xbet/games_section/feature/popular/domain/usecases/PopularOneXGamesLuckyWheelUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final Z4(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic a4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LVg0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y2:LVg0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final a5(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lg9/i;",
            ">;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-nez p1, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 8
    .line 9
    sget-object p2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c$a;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c$a;

    .line 10
    .line 11
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-static {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 16
    .line 17
    .line 18
    move-result-wide p1

    .line 19
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Y4(J)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final synthetic b4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lcom/xbet/onexuser/domain/user/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S3:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->L4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e5(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->b5(Z)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic f4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->N4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final f5(Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->z5:Lp30/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/h;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/h;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 6
    .line 7
    .line 8
    const-string v2, "PopularOneXGamesViewModel.updateGamesWorkStatus"

    .line 9
    .line 10
    invoke-virtual {v0, p1, v2, v1}, Lp30/a;->l(Ljava/util/List;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public static final synthetic g4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S4(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final g5(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->M4()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic h4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U4(Lorg/xplatform/banners/api/domain/models/BannerModel;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lb50/g;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X4(Lb50/g;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic j4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Y4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic k4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->a5(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic l4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic m4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->c5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic n4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic o4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->O5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Long;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P5:Ljava/lang/Long;

    .line 2
    .line 3
    return-void
.end method

.method public static synthetic q3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d5(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic r3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->D4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final r4(J)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$addLastAction$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$addLastAction$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$addLastAction$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$addLastAction$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;JLkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static synthetic s3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final s4(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ls30/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/LinkedHashSet;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/LinkedHashSet;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_3

    .line 15
    .line 16
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    check-cast v1, Ls30/b;

    .line 21
    .line 22
    invoke-virtual {v1}, Ls30/b;->e()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    if-eqz v2, :cond_0

    .line 35
    .line 36
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    check-cast v2, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 41
    .line 42
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->g()Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-nez v3, :cond_2

    .line 47
    .line 48
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->b()Z

    .line 49
    .line 50
    .line 51
    move-result v3

    .line 52
    if-nez v3, :cond_1

    .line 53
    .line 54
    :cond_2
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    invoke-static {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 59
    .line 60
    .line 61
    move-result-wide v2

    .line 62
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    invoke-interface {v0, v2}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_3
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    if-nez p1, :cond_4

    .line 75
    .line 76
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->f5(Ljava/util/List;)V

    .line 81
    .line 82
    .line 83
    :cond_4
    return-void
.end method

.method public static synthetic t3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->e5(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->z4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic v3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Z4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final w4()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->J5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 16
    .line 17
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 21
    .line 22
    .line 23
    move-result-object v2

    .line 24
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 25
    .line 26
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 27
    .line 28
    .line 29
    move-result-object v10

    .line 30
    new-instance v8, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getBanners$1;

    .line 31
    .line 32
    const/4 v0, 0x0

    .line 33
    invoke-direct {v8, p0, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getBanners$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/m;

    .line 37
    .line 38
    invoke-direct {v11, p0}, Lorg/xbet/games_section/feature/popular/presentation/m;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 39
    .line 40
    .line 41
    const/16 v13, 0x128

    .line 42
    .line 43
    const/4 v14, 0x0

    .line 44
    const-string v3, "PopularOneXGamesViewModel.getBanners"

    .line 45
    .line 46
    const/4 v4, 0x3

    .line 47
    const-wide/16 v5, 0x3

    .line 48
    .line 49
    const/4 v7, 0x0

    .line 50
    const/4 v9, 0x0

    .line 51
    const/4 v12, 0x0

    .line 52
    invoke-static/range {v2 .. v14}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->J5:Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic x3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->P4(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final x4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 4
    .line 5
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    new-instance p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getBanners$2$1;

    .line 9
    .line 10
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y1:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    invoke-direct {p1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getBanners$2$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static synthetic y3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->g5(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final z4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final A4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 8
    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->w4()V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Q5:Lkotlinx/coroutines/flow/V;

    .line 15
    .line 16
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 21
    .line 22
    if-nez v0, :cond_1

    .line 23
    .line 24
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F4()V

    .line 25
    .line 26
    .line 27
    :cond_1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 28
    .line 29
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 34
    .line 35
    if-nez v0, :cond_2

    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C4()V

    .line 38
    .line 39
    .line 40
    :cond_2
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 47
    .line 48
    if-nez v0, :cond_3

    .line 49
    .line 50
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4()V

    .line 51
    .line 52
    .line 53
    :cond_3
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->T5:Lkotlinx/coroutines/flow/V;

    .line 54
    .line 55
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 60
    .line 61
    if-nez v0, :cond_4

    .line 62
    .line 63
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y4()V

    .line 64
    .line 65
    .line 66
    :cond_4
    return-void
.end method

.method public final B4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->V5:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final C4()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->K5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 20
    .line 21
    if-nez v0, :cond_1

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$a;->a:Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 26
    .line 27
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    :cond_1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v10

    .line 40
    new-instance v8, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;

    .line 41
    .line 42
    const/4 v0, 0x0

    .line 43
    invoke-direct {v8, p0, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getGames$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/l;

    .line 47
    .line 48
    invoke-direct {v11, p0}, Lorg/xbet/games_section/feature/popular/presentation/l;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 49
    .line 50
    .line 51
    const/16 v13, 0x128

    .line 52
    .line 53
    const/4 v14, 0x0

    .line 54
    const-string v3, "PopularOneXGamesViewModel.getGames"

    .line 55
    .line 56
    const/4 v4, 0x3

    .line 57
    const-wide/16 v5, 0x3

    .line 58
    .line 59
    const/4 v7, 0x0

    .line 60
    const/4 v9, 0x0

    .line 61
    const/4 v12, 0x0

    .line 62
    invoke-static/range {v2 .. v14}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->K5:Lkotlinx/coroutines/x0;

    .line 67
    .line 68
    return-void
.end method

.method public final E4(Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;Lorg/xbet/games_section/feature/popular/presentation/a;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/c;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/e;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/i;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/a;",
            ">;",
            "Lorg/xbet/games_section/feature/popular/presentation/a<",
            "+",
            "Lb50/d;",
            ">;)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;

    .line 12
    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    const/4 p1, 0x0

    .line 16
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d5(Z)V

    .line 17
    .line 18
    .line 19
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1

    .line 24
    :cond_1
    instance-of v0, p3, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 25
    .line 26
    if-eqz v0, :cond_3

    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;

    .line 35
    .line 36
    if-nez p1, :cond_2

    .line 37
    .line 38
    const/4 p1, 0x1

    .line 39
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d5(Z)V

    .line 40
    .line 41
    .line 42
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    return-object p1

    .line 47
    :cond_3
    instance-of v0, p3, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 48
    .line 49
    if-eqz v0, :cond_4

    .line 50
    .line 51
    instance-of v1, p1, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 52
    .line 53
    if-eqz v1, :cond_5

    .line 54
    .line 55
    :cond_4
    instance-of v1, p2, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 56
    .line 57
    if-eqz v1, :cond_6

    .line 58
    .line 59
    :cond_5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 60
    .line 61
    sget-object v2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$b;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$b;

    .line 62
    .line 63
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    :cond_6
    instance-of v1, p2, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 67
    .line 68
    if-eqz v1, :cond_7

    .line 69
    .line 70
    check-cast p2, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/games_section/feature/popular/presentation/a$d;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    check-cast p2, Lb50/e;

    .line 77
    .line 78
    :goto_0
    move-object v4, p2

    .line 79
    goto :goto_2

    .line 80
    :cond_7
    instance-of v1, p2, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 81
    .line 82
    if-nez v1, :cond_a

    .line 83
    .line 84
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$a;->a:Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 85
    .line 86
    invoke-static {p2, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 87
    .line 88
    .line 89
    move-result v1

    .line 90
    if-eqz v1, :cond_8

    .line 91
    .line 92
    goto :goto_1

    .line 93
    :cond_8
    instance-of p2, p2, Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 94
    .line 95
    if-eqz p2, :cond_9

    .line 96
    .line 97
    sget-object p2, Lb50/e$c;->a:Lb50/e$c;

    .line 98
    .line 99
    goto :goto_0

    .line 100
    :cond_9
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 101
    .line 102
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 103
    .line 104
    .line 105
    throw p1

    .line 106
    :cond_a
    :goto_1
    sget-object p2, Lb50/e$b;->a:Lb50/e$b;

    .line 107
    .line 108
    goto :goto_0

    .line 109
    :goto_2
    instance-of p2, p1, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 110
    .line 111
    if-eqz p2, :cond_b

    .line 112
    .line 113
    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 114
    .line 115
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/popular/presentation/a$d;->a()Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    check-cast p1, Lb50/c;

    .line 120
    .line 121
    :goto_3
    move-object v7, p1

    .line 122
    goto :goto_5

    .line 123
    :cond_b
    instance-of p2, p1, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 124
    .line 125
    if-nez p2, :cond_e

    .line 126
    .line 127
    sget-object p2, Lorg/xbet/games_section/feature/popular/presentation/a$a;->a:Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 128
    .line 129
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 130
    .line 131
    .line 132
    move-result p2

    .line 133
    if-eqz p2, :cond_c

    .line 134
    .line 135
    goto :goto_4

    .line 136
    :cond_c
    instance-of p1, p1, Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 137
    .line 138
    if-eqz p1, :cond_d

    .line 139
    .line 140
    sget-object p1, Lb50/c$c;->a:Lb50/c$c;

    .line 141
    .line 142
    goto :goto_3

    .line 143
    :cond_d
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 144
    .line 145
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 146
    .line 147
    .line 148
    throw p1

    .line 149
    :cond_e
    :goto_4
    sget-object p1, Lb50/c$b;->a:Lb50/c$b;

    .line 150
    .line 151
    goto :goto_3

    .line 152
    :goto_5
    if-eqz v0, :cond_f

    .line 153
    .line 154
    check-cast p3, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 155
    .line 156
    invoke-virtual {p3}, Lorg/xbet/games_section/feature/popular/presentation/a$d;->a()Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    check-cast p1, Lb50/i;

    .line 161
    .line 162
    :goto_6
    move-object v5, p1

    .line 163
    goto :goto_7

    .line 164
    :cond_f
    sget-object p1, Lb50/i$b;->a:Lb50/i$b;

    .line 165
    .line 166
    goto :goto_6

    .line 167
    :goto_7
    instance-of p1, p5, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 168
    .line 169
    if-eqz p1, :cond_10

    .line 170
    .line 171
    check-cast p5, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 172
    .line 173
    invoke-virtual {p5}, Lorg/xbet/games_section/feature/popular/presentation/a$d;->a()Ljava/lang/Object;

    .line 174
    .line 175
    .line 176
    move-result-object p1

    .line 177
    check-cast p1, Lb50/d;

    .line 178
    .line 179
    :goto_8
    move-object v8, p1

    .line 180
    goto :goto_a

    .line 181
    :cond_10
    instance-of p1, p5, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 182
    .line 183
    if-nez p1, :cond_12

    .line 184
    .line 185
    sget-object p1, Lorg/xbet/games_section/feature/popular/presentation/a$a;->a:Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 186
    .line 187
    invoke-static {p5, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move-result p1

    .line 191
    if-nez p1, :cond_12

    .line 192
    .line 193
    sget-object p1, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 194
    .line 195
    invoke-static {p5, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 196
    .line 197
    .line 198
    move-result p1

    .line 199
    if-eqz p1, :cond_11

    .line 200
    .line 201
    goto :goto_9

    .line 202
    :cond_11
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 203
    .line 204
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 205
    .line 206
    .line 207
    throw p1

    .line 208
    :cond_12
    :goto_9
    sget-object p1, Lb50/d$b;->a:Lb50/d$b;

    .line 209
    .line 210
    goto :goto_8

    .line 211
    :goto_a
    instance-of p1, p4, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 212
    .line 213
    if-eqz p1, :cond_13

    .line 214
    .line 215
    check-cast p4, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 216
    .line 217
    invoke-virtual {p4}, Lorg/xbet/games_section/feature/popular/presentation/a$d;->a()Ljava/lang/Object;

    .line 218
    .line 219
    .line 220
    move-result-object p1

    .line 221
    check-cast p1, Lb50/a;

    .line 222
    .line 223
    :goto_b
    move-object v6, p1

    .line 224
    goto :goto_d

    .line 225
    :cond_13
    instance-of p1, p4, Lorg/xbet/games_section/feature/popular/presentation/a$a;

    .line 226
    .line 227
    if-nez p1, :cond_15

    .line 228
    .line 229
    instance-of p1, p4, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 230
    .line 231
    if-nez p1, :cond_15

    .line 232
    .line 233
    instance-of p1, p4, Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 234
    .line 235
    if-eqz p1, :cond_14

    .line 236
    .line 237
    goto :goto_c

    .line 238
    :cond_14
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 239
    .line 240
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 241
    .line 242
    .line 243
    throw p1

    .line 244
    :cond_15
    :goto_c
    sget-object p1, Lb50/a$b;->a:Lb50/a$b;

    .line 245
    .line 246
    goto :goto_b

    .line 247
    :goto_d
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->A5:Lorg/xbet/games_section/feature/popular/presentation/b;

    .line 248
    .line 249
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 250
    .line 251
    iget-boolean v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->N5:Z

    .line 252
    .line 253
    invoke-virtual/range {v1 .. v8}, Lorg/xbet/games_section/feature/popular/presentation/b;->l(Lkotlinx/coroutines/flow/V;ZLb50/e;Lb50/i;Lb50/a;Lb50/c;Lb50/d;)Ljava/util/List;

    .line 254
    .line 255
    .line 256
    move-result-object p1

    .line 257
    return-object p1
.end method

.method public F2(ILjava/lang/String;)V
    .locals 3
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x1

    .line 2
    if-eq p1, v0, :cond_1

    .line 3
    .line 4
    const/4 p2, 0x2

    .line 5
    if-eq p1, p2, :cond_0

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 9
    .line 10
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v2:LwX0/a;

    .line 11
    .line 12
    invoke-interface {p2}, LwX0/a;->u()Lq4/q;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    invoke-virtual {p1, p2}, LwX0/c;->m(Lq4/q;)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I3:LpS/b;

    .line 21
    .line 22
    sget-object v0, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 23
    .line 24
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 25
    .line 26
    .line 27
    move-result-wide v1

    .line 28
    long-to-int v2, v1

    .line 29
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_XGAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 30
    .line 31
    invoke-interface {p1, p2, v2, v1}, LpS/b;->e(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 35
    .line 36
    .line 37
    move-result-wide p1

    .line 38
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->r4(J)V

    .line 39
    .line 40
    .line 41
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 42
    .line 43
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v2:LwX0/a;

    .line 44
    .line 45
    invoke-interface {p2}, LwX0/a;->s()Lq4/q;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    invoke-virtual {p1, p2}, LwX0/c;->m(Lq4/q;)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method public final H4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->L5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 20
    .line 21
    if-nez v0, :cond_1

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U5:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$b;->a:Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 26
    .line 27
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    :cond_1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 35
    .line 36
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    new-instance v3, Lorg/xbet/games_section/feature/popular/presentation/n;

    .line 41
    .line 42
    invoke-direct {v3, p0}, Lorg/xbet/games_section/feature/popular/presentation/n;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 43
    .line 44
    .line 45
    new-instance v7, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;

    .line 46
    .line 47
    const/4 v0, 0x0

    .line 48
    invoke-direct {v7, p0, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getLuckyWheel$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 49
    .line 50
    .line 51
    const/16 v8, 0xa

    .line 52
    .line 53
    const/4 v9, 0x0

    .line 54
    const/4 v4, 0x0

    .line 55
    const/4 v6, 0x0

    .line 56
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->L5:Lkotlinx/coroutines/x0;

    .line 61
    .line 62
    return-void
.end method

.method public final J4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final K4()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getViewState$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getViewState$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public M1(Ljava/lang/String;II)V
    .locals 11
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_1

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v3, v1

    .line 19
    check-cast v3, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 20
    .line 21
    invoke-virtual {v3}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 22
    .line 23
    .line 24
    move-result v3

    .line 25
    if-ne v3, p2, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    move-object v1, v2

    .line 29
    :goto_0
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 30
    .line 31
    if-nez v1, :cond_2

    .line 32
    .line 33
    return-void

    .line 34
    :cond_2
    iget-object v3, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H3:LSR/a;

    .line 35
    .line 36
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 41
    .line 42
    .line 43
    move-result v4

    .line 44
    invoke-static {v0, v4}, LTo0/a;->a(Ljava/lang/String;Z)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v6

    .line 48
    sget-object v8, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_XGAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 49
    .line 50
    move-object v4, p1

    .line 51
    move v5, p2

    .line 52
    move v7, p3

    .line 53
    invoke-interface/range {v3 .. v8}, LSR/a;->d(Ljava/lang/String;ILjava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 54
    .line 55
    .line 56
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 57
    .line 58
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object p2

    .line 62
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 63
    .line 64
    .line 65
    move-result p3

    .line 66
    invoke-static {p2, p3}, LTo0/a;->a(Ljava/lang/String;Z)Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object p2

    .line 70
    const-string p3, "popular_new_games"

    .line 71
    .line 72
    invoke-virtual {p1, v5, p2, v7, p3}, Lorg/xbet/analytics/domain/scope/NewsAnalytics;->g(ILjava/lang/String;ILjava/lang/String;)V

    .line 73
    .line 74
    .line 75
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 76
    .line 77
    .line 78
    move-result-object v3

    .line 79
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/i;

    .line 80
    .line 81
    invoke-direct {v4}, Lorg/xbet/games_section/feature/popular/presentation/i;-><init>()V

    .line 82
    .line 83
    .line 84
    new-instance v8, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;

    .line 85
    .line 86
    invoke-direct {v8, v1, p0, v7, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$onActionBannerClick$2;-><init>(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;ILkotlin/coroutines/e;)V

    .line 87
    .line 88
    .line 89
    const/16 v9, 0xe

    .line 90
    .line 91
    const/4 v10, 0x0

    .line 92
    const/4 v5, 0x0

    .line 93
    const/4 v6, 0x0

    .line 94
    const/4 v7, 0x0

    .line 95
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 96
    .line 97
    .line 98
    return-void
.end method

.method public final M4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->K5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C4()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->L5:Lkotlinx/coroutines/x0;

    .line 10
    .line 11
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4()V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final Q4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->b2:Lhf0/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lhf0/a;->invoke()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->E5:Lek0/o;

    .line 10
    .line 11
    invoke-virtual {v0}, Lek0/o;->b2()Lek0/k;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lek0/k;->a()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    const/4 v0, 0x1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 v0, 0x0

    .line 24
    :goto_0
    iput-boolean v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->N5:Z

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$b;

    .line 33
    .line 34
    if-nez v0, :cond_1

    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->c5()V

    .line 37
    .line 38
    .line 39
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->t4()V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public final R4()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->z5:Lp30/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp30/a;->k()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 7
    .line 8
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final W4(JLb50/g;)V
    .locals 10

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openGame$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openGame$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v4, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openGame$2;

    .line 17
    .line 18
    const/4 v9, 0x0

    .line 19
    move-object v5, p0

    .line 20
    move-wide v6, p1

    .line 21
    move-object v8, p3

    .line 22
    invoke-direct/range {v4 .. v9}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openGame$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;JLb50/g;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    const/16 v6, 0xa

    .line 26
    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v2, 0x0

    .line 29
    move-object v5, v4

    .line 30
    const/4 v4, 0x0

    .line 31
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final X4(Lb50/g;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lb50/g;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->J$0:J

    .line 39
    .line 40
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast p1, Lb30/L;

    .line 43
    .line 44
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    move-object v4, p1

    .line 48
    move-wide v5, v1

    .line 49
    goto :goto_1

    .line 50
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p1}, Lb50/g;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 66
    .line 67
    .line 68
    move-result-wide p1

    .line 69
    sget-object v2, Lb30/L;->a:Lb30/L;

    .line 70
    .line 71
    iget-object v4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S2:Lw30/i;

    .line 72
    .line 73
    iput-object v2, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    iput-wide p1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->J$0:J

    .line 76
    .line 77
    iput v3, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openNativeGame$1;->label:I

    .line 78
    .line 79
    invoke-interface {v4, p1, p2, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    if-ne v0, v1, :cond_3

    .line 84
    .line 85
    return-object v1

    .line 86
    :cond_3
    move-wide v5, p1

    .line 87
    move-object p2, v0

    .line 88
    move-object v4, v2

    .line 89
    :goto_1
    check-cast p2, Ljava/lang/Boolean;

    .line 90
    .line 91
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 92
    .line 93
    .line 94
    move-result p1

    .line 95
    xor-int/lit8 v8, p1, 0x1

    .line 96
    .line 97
    const/4 v9, 0x2

    .line 98
    const/4 v10, 0x0

    .line 99
    const/4 v7, 0x0

    .line 100
    invoke-static/range {v4 .. v10}, Lb30/L;->b(Lb30/L;JLorg/xbet/games_section/api/models/GameBonus;ZILjava/lang/Object;)LwX0/B;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    if-eqz p1, :cond_4

    .line 105
    .line 106
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 107
    .line 108
    invoke-virtual {p2, p1}, LwX0/c;->m(Lq4/q;)V

    .line 109
    .line 110
    .line 111
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 112
    .line 113
    return-object p1
.end method

.method public final Y4(J)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/p;

    .line 6
    .line 7
    invoke-direct {v1}, Lorg/xbet/games_section/feature/popular/presentation/p;-><init>()V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openWebPage$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$openWebPage$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;JLkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final b5(Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d5(Z)V

    .line 2
    .line 3
    .line 4
    iget-boolean p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G5:Z

    .line 5
    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->A4()V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public final c5()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->R5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    iget-boolean v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->N5:Z

    .line 4
    .line 5
    if-eqz v1, :cond_1

    .line 6
    .line 7
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 8
    .line 9
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-nez v1, :cond_1

    .line 14
    .line 15
    sget-object v1, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;->Companion:Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->E5:Lek0/o;

    .line 18
    .line 19
    invoke-virtual {v2}, Lek0/o;->P1()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H5:Ljava/util/List;

    .line 28
    .line 29
    new-instance v3, Ljava/util/ArrayList;

    .line 30
    .line 31
    const/16 v4, 0xa

    .line 32
    .line 33
    invoke-static {v2, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 38
    .line 39
    .line 40
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v4

    .line 48
    if-eqz v4, :cond_0

    .line 49
    .line 50
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v4

    .line 54
    check-cast v4, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 55
    .line 56
    invoke-static {v4, v1}, La50/a;->a(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/uikit/components/bannercollection/BannerCollectionStyle;)LrZ0/b;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_0
    new-instance v1, Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 65
    .line 66
    invoke-direct {v1, v3}, Lorg/xbet/uikit/components/bannercollection/a$a;-><init>(Ljava/util/List;)V

    .line 67
    .line 68
    .line 69
    new-instance v2, Lb50/e$a;

    .line 70
    .line 71
    invoke-direct {v2, v1}, Lb50/e$a;-><init>(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 72
    .line 73
    .line 74
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 75
    .line 76
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/popular/presentation/a$d;-><init>(Ljava/lang/Object;)V

    .line 77
    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_1
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/a$c;->a:Lorg/xbet/games_section/feature/popular/presentation/a$c;

    .line 81
    .line 82
    :goto_1
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public final d5(Z)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F3:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    sget v4, Lpb/k;->currently_no_events:I

    .line 12
    .line 13
    :goto_0
    move v8, v4

    .line 14
    goto :goto_1

    .line 15
    :cond_0
    sget v4, Lpb/k;->data_retrieval_error:I

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :goto_1
    if-eqz p1, :cond_1

    .line 19
    .line 20
    sget v4, Lpb/k;->refresh_data:I

    .line 21
    .line 22
    :goto_2
    move v10, v4

    .line 23
    goto :goto_3

    .line 24
    :cond_1
    sget v4, Lpb/k;->try_again_text:I

    .line 25
    .line 26
    goto :goto_2

    .line 27
    :goto_3
    new-instance v11, Lorg/xbet/games_section/feature/popular/presentation/o;

    .line 28
    .line 29
    invoke-direct {v11, p0, p1}, Lorg/xbet/games_section/feature/popular/presentation/o;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V

    .line 30
    .line 31
    .line 32
    const/16 v12, 0x5e

    .line 33
    .line 34
    const/4 v13, 0x0

    .line 35
    const/4 v4, 0x0

    .line 36
    const/4 v5, 0x0

    .line 37
    const/4 v6, 0x0

    .line 38
    const/4 v7, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 45
    .line 46
    .line 47
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public i1(Ljava/lang/String;Lb50/g;Ljava/lang/String;Z)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lb50/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p2}, Lb50/g;->d()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_2

    .line 6
    .line 7
    invoke-virtual {p2}, Lb50/g;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 12
    .line 13
    .line 14
    move-result-wide v0

    .line 15
    if-eqz p4, :cond_0

    .line 16
    .line 17
    iget-object p4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x1:LDg/c;

    .line 18
    .line 19
    sget-object v2, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 20
    .line 21
    invoke-virtual {p4, v0, v1, v2}, LDg/c;->p(JLorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 22
    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    iget-object p4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x1:LDg/c;

    .line 26
    .line 27
    sget-object v2, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 28
    .line 29
    invoke-virtual {p4, v0, v1, v2, p3}, LDg/c;->r(JLorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    :goto_0
    iget-object p4, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I3:LpS/b;

    .line 33
    .line 34
    long-to-int v2, v0

    .line 35
    sget-object v3, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_XGAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 36
    .line 37
    invoke-static {p3}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object p3

    .line 41
    if-eqz p3, :cond_1

    .line 42
    .line 43
    invoke-virtual {p3}, Ljava/lang/Integer;->intValue()I

    .line 44
    .line 45
    .line 46
    move-result p3

    .line 47
    goto :goto_1

    .line 48
    :cond_1
    const/4 p3, 0x0

    .line 49
    :goto_1
    invoke-interface {p4, p1, v2, v3, p3}, LpS/b;->m(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;I)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0, v0, v1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->W4(JLb50/g;)V

    .line 53
    .line 54
    .line 55
    :cond_2
    return-void
.end method

.method public k(Ljava/lang/String;Ljava/lang/String;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p2}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-nez v0, :cond_1

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x1:LDg/c;

    .line 16
    .line 17
    sget-object v2, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->Main:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 18
    .line 19
    invoke-virtual {v1, v2}, LDg/c;->d(Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 20
    .line 21
    .line 22
    :cond_1
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x1:LDg/c;

    .line 23
    .line 24
    sget-object v2, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 25
    .line 26
    invoke-virtual {v1, p2, v2}, LDg/c;->e(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 27
    .line 28
    .line 29
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->I3:LpS/b;

    .line 30
    .line 31
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_XGAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 32
    .line 33
    invoke-interface {p2, p1, v0, v1}, LpS/b;->d(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->x5:LwX0/c;

    .line 37
    .line 38
    iget-object p2, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->v2:LwX0/a;

    .line 39
    .line 40
    invoke-interface {p2, v0}, LwX0/a;->C(I)Lq4/q;

    .line 41
    .line 42
    .line 43
    move-result-object p2

    .line 44
    invoke-virtual {p1, p2}, LwX0/c;->m(Lq4/q;)V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public final t4()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->G5:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S3:Lcom/xbet/onexuser/domain/user/c;

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_2

    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->F4()V

    .line 15
    .line 16
    .line 17
    iget-boolean v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->O5:Z

    .line 18
    .line 19
    if-nez v0, :cond_1

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S5:Lkotlinx/coroutines/flow/V;

    .line 22
    .line 23
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 28
    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->X2:Lw30/b;

    .line 32
    .line 33
    invoke-interface {v0}, Lw30/b;->invoke()V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y4()V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C4()V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$b;

    .line 50
    .line 51
    if-eqz v0, :cond_2

    .line 52
    .line 53
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->y4()V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->C4()V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H4()V

    .line 60
    .line 61
    .line 62
    :cond_2
    :goto_0
    return-void
.end method

.method public final u4(Ljava/util/List;)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ls30/b;",
            ">;)",
            "Ljava/util/List<",
            "Ls30/b;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_2

    .line 21
    .line 22
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    move-object v2, v1

    .line 27
    check-cast v2, Ls30/b;

    .line 28
    .line 29
    invoke-virtual {v2}, Ls30/b;->e()Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    new-instance v3, Ljava/util/ArrayList;

    .line 34
    .line 35
    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 36
    .line 37
    .line 38
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    :cond_0
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v4

    .line 46
    if-eqz v4, :cond_1

    .line 47
    .line 48
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    move-object v5, v4

    .line 53
    check-cast v5, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;

    .line 54
    .line 55
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->f()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 56
    .line 57
    .line 58
    move-result-object v6

    .line 59
    invoke-static {v6}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 60
    .line 61
    .line 62
    move-result-wide v6

    .line 63
    sget-object v8, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 64
    .line 65
    invoke-virtual {v8}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v8

    .line 69
    cmp-long v10, v6, v8

    .line 70
    .line 71
    if-eqz v10, :cond_0

    .line 72
    .line 73
    invoke-virtual {v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/a;->b()Z

    .line 74
    .line 75
    .line 76
    move-result v5

    .line 77
    if-eqz v5, :cond_0

    .line 78
    .line 79
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    goto :goto_1

    .line 83
    :cond_1
    const/4 v6, 0x6

    .line 84
    const/4 v7, 0x0

    .line 85
    const/4 v4, 0x0

    .line 86
    const/4 v5, 0x0

    .line 87
    invoke-static/range {v2 .. v7}, Ls30/b;->b(Ls30/b;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ls30/b;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    goto :goto_0

    .line 95
    :cond_2
    return-object v0
.end method

.method public final v4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->D5:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->S1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/popular/presentation/j;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/popular/presentation/j;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getCenterOfAttentionGame$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$getCenterOfAttentionGame$2;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    iput-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->M5:Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    return-void
.end method
