.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0000\n\u0002\u0008\u0016\u0008\u0081\u0008\u0018\u00002\u00020\u0001B?\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001d\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\r\u0010\u0018\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0010\u0010\u001a\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0010\u0010\u001c\u001a\u00020\u0013H\u00d6\u0001\u00a2\u0006\u0004\u0008\u001c\u0010\u0019J\u001a\u0010\u001f\u001a\u00020\u00052\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u00d6\u0003\u00a2\u0006\u0004\u0008\u001f\u0010 R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010\u001bR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008$\u0010\"\u001a\u0004\u0008%\u0010\u001bR\u0017\u0010\u0006\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u0008&\u0010\'\u001a\u0004\u0008&\u0010(R\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008#\u0010)\u001a\u0004\u0008*\u0010+R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008%\u0010,\u001a\u0004\u0008-\u0010.R\u0017\u0010\u000c\u001a\u00020\u000b8\u0006\u00a2\u0006\u000c\n\u0004\u0008*\u0010/\u001a\u0004\u0008$\u00100R\u0017\u0010\u000e\u001a\u00020\r8\u0006\u00a2\u0006\u000c\n\u0004\u0008-\u00101\u001a\u0004\u0008!\u00102\u00a8\u00063"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
        "Landroid/os/Parcelable;",
        "",
        "headerImage",
        "headerTitle",
        "",
        "hasStages",
        "Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;",
        "status",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "tournamentKind",
        "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "currentPage",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "buttonAction",
        "<init>",
        "(Ljava/lang/String;Ljava/lang/String;ZLorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V",
        "Landroid/os/Parcel;",
        "dest",
        "",
        "flags",
        "",
        "writeToParcel",
        "(Landroid/os/Parcel;I)V",
        "describeContents",
        "()I",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/lang/String;",
        "d",
        "b",
        "e",
        "c",
        "Z",
        "()Z",
        "Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;",
        "f",
        "()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "g",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Z

.field public final d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel$a;

    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel$a;-><init>()V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->CREATOR:Landroid/os/Parcelable$Creator;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;ZLorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    .line 7
    .line 8
    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public final a()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Lorg/xplatform/aggregator/api/navigation/TournamentsPage;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    .line 2
    .line 3
    return v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final e()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    iget-boolean v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    if-eq v1, v3, :cond_6

    return v2

    :cond_6
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    if-eq v1, v3, :cond_7

    return v2

    :cond_7
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_8

    return v2

    :cond_8
    return v0
.end method

.method public final f()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 9
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "ContainerUiModel(headerImage="

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", headerTitle="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", hasStages="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", status="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", tournamentKind="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", currentPage="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", buttonAction="

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 1
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a:Ljava/lang/String;

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->b:Ljava/lang/String;

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->c:Z

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeInt(I)V

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    invoke-virtual {v0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    invoke-virtual {v0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->f:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    invoke-virtual {v0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {p1, v0, p2}, Landroid/os/Parcel;->writeParcelable(Landroid/os/Parcelable;I)V

    return-void
.end method
