.class public final LOP0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LPP0/e;",
        "LRP0/e;",
        "a",
        "(LPP0/e;)LRP0/e;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LPP0/e;)LRP0/e;
    .locals 9
    .param p0    # LPP0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LPP0/e;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    new-instance v1, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_1

    .line 27
    .line 28
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    check-cast v2, LPP0/d;

    .line 33
    .line 34
    invoke-static {v2}, LOP0/d;->a(LPP0/d;)LRP0/d;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 v1, 0x0

    .line 43
    :cond_1
    if-nez v1, :cond_2

    .line 44
    .line 45
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    :cond_2
    move-object v3, v1

    .line 50
    invoke-virtual {p0}, LPP0/e;->b()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    const-string v1, "-"

    .line 55
    .line 56
    if-nez v0, :cond_3

    .line 57
    .line 58
    move-object v4, v1

    .line 59
    goto :goto_1

    .line 60
    :cond_3
    move-object v4, v0

    .line 61
    :goto_1
    invoke-virtual {p0}, LPP0/e;->c()Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    if-nez v0, :cond_4

    .line 66
    .line 67
    move-object v5, v1

    .line 68
    goto :goto_2

    .line 69
    :cond_4
    move-object v5, v0

    .line 70
    :goto_2
    invoke-virtual {p0}, LPP0/e;->d()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    if-nez v0, :cond_5

    .line 75
    .line 76
    move-object v6, v1

    .line 77
    goto :goto_3

    .line 78
    :cond_5
    move-object v6, v0

    .line 79
    :goto_3
    invoke-virtual {p0}, LPP0/e;->e()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    if-nez v0, :cond_6

    .line 84
    .line 85
    move-object v7, v1

    .line 86
    goto :goto_4

    .line 87
    :cond_6
    move-object v7, v0

    .line 88
    :goto_4
    invoke-virtual {p0}, LPP0/e;->f()Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    if-nez p0, :cond_7

    .line 93
    .line 94
    move-object v8, v1

    .line 95
    goto :goto_5

    .line 96
    :cond_7
    move-object v8, p0

    .line 97
    :goto_5
    new-instance v2, LRP0/e;

    .line 98
    .line 99
    invoke-direct/range {v2 .. v8}, LRP0/e;-><init>(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    return-object v2
.end method
