.class public LCY0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQY0/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0017\u0018\u00002\u00020\u0001B%\u0012\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ5\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0012\u001a\u00020\u0011H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u000b\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u0019H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ5\u0010\u001f\u001a\u00020\u00132\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\'\u0010$\u001a\u00020!2\u0006\u0010\"\u001a\u00020!2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010#\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008$\u0010%J-\u0010&\u001a\u00020\u00132\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000eH\u0002\u00a2\u0006\u0004\u0008&\u0010\'R\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+R\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006\u00a2\u0006\u000c\n\u0004\u00080\u00101\u001a\u0004\u00082\u00103R\u0014\u00106\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\"\u0010=\u001a\u00020!8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<R0\u0010E\u001a\u0010\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020\u0013\u0018\u00010>8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001b\u0010@\u001a\u0004\u0008A\u0010B\"\u0004\u0008C\u0010DR\"\u0010M\u001a\u00020F8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008G\u0010H\u001a\u0004\u0008I\u0010J\"\u0004\u0008K\u0010LR\u0018\u0010P\u001a\u00020!*\u00020\u00028BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008N\u0010O\u00a8\u0006Q"
    }
    d2 = {
        "LCY0/b;",
        "LQY0/c;",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "label",
        "LAY0/a;",
        "indicator",
        "LDY0/a;",
        "guideline",
        "<init>",
        "(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;LAY0/a;LDY0/a;)V",
        "LIY0/b;",
        "context",
        "Landroid/graphics/RectF;",
        "bounds",
        "",
        "LQY0/c$b;",
        "markedEntries",
        "LyY0/d;",
        "chartValuesProvider",
        "",
        "n",
        "(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;LyY0/d;)V",
        "LIY0/d;",
        "LwY0/c;",
        "outInsets",
        "LtY0/a;",
        "horizontalDimensions",
        "f",
        "(LIY0/d;LwY0/c;LtY0/a;)V",
        "LyY0/b;",
        "chartValues",
        "p",
        "(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;LyY0/b;)V",
        "",
        "xPosition",
        "halfOfTextWidth",
        "s",
        "(FLandroid/graphics/RectF;F)F",
        "h",
        "(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;)V",
        "a",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "getLabel",
        "()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "b",
        "LAY0/a;",
        "getIndicator",
        "()LAY0/a;",
        "c",
        "LDY0/a;",
        "getGuideline",
        "()LDY0/a;",
        "d",
        "Landroid/graphics/RectF;",
        "tempBounds",
        "e",
        "F",
        "getIndicatorSizeDp",
        "()F",
        "t",
        "(F)V",
        "indicatorSizeDp",
        "Lkotlin/Function1;",
        "",
        "Lkotlin/jvm/functions/Function1;",
        "getOnApplyEntryColor",
        "()Lkotlin/jvm/functions/Function1;",
        "v",
        "(Lkotlin/jvm/functions/Function1;)V",
        "onApplyEntryColor",
        "LQY0/d;",
        "g",
        "LQY0/d;",
        "getLabelFormatter",
        "()LQY0/d;",
        "u",
        "(LQY0/d;)V",
        "labelFormatter",
        "r",
        "(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)F",
        "tickSizeDp",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

.field public final b:LAY0/a;

.field public final c:LDY0/a;

.field public final d:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:F

.field public f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public g:LQY0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;LAY0/a;LDY0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 5
    .line 6
    iput-object p2, p0, LCY0/b;->b:LAY0/a;

    .line 7
    .line 8
    iput-object p3, p0, LCY0/b;->c:LDY0/a;

    .line 9
    .line 10
    new-instance p1, Landroid/graphics/RectF;

    .line 11
    .line 12
    invoke-direct {p1}, Landroid/graphics/RectF;-><init>()V

    .line 13
    .line 14
    .line 15
    iput-object p1, p0, LCY0/b;->d:Landroid/graphics/RectF;

    .line 16
    .line 17
    sget-object p1, LQY0/b;->a:LQY0/b;

    .line 18
    .line 19
    iput-object p1, p0, LCY0/b;->g:LQY0/d;

    .line 20
    .line 21
    return-void
.end method

.method public static synthetic c(LQY0/c$b;)F
    .locals 0

    .line 1
    invoke-static {p0}, LCY0/b;->q(LQY0/c$b;)F

    move-result p0

    return p0
.end method

.method public static final q(LQY0/c$b;)F
    .locals 2

    .line 1
    invoke-virtual {p0}, LQY0/c$b;->c()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, LRY0/a;->e(J)F

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    return p0
.end method


# virtual methods
.method public f(LIY0/d;LwY0/c;LtY0/a;)V
    .locals 8
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwY0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/16 v6, 0x1e

    .line 6
    .line 7
    const/4 v7, 0x0

    .line 8
    const/4 v2, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x0

    .line 12
    move-object v1, p1

    .line 13
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;->g(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;LIY0/d;Ljava/lang/CharSequence;IIFILjava/lang/Object;)F

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    iget-object p3, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 18
    .line 19
    invoke-virtual {p0, p3}, LCY0/b;->r(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)F

    .line 20
    .line 21
    .line 22
    move-result p3

    .line 23
    invoke-interface {v1, p3}, LIY0/d;->N(F)F

    .line 24
    .line 25
    .line 26
    move-result p3

    .line 27
    add-float/2addr p1, p3

    .line 28
    invoke-virtual {p2, p1}, LwY0/c;->p(F)V

    .line 29
    .line 30
    .line 31
    :cond_0
    return-void
.end method

.method public final h(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LIY0/b;",
            "Landroid/graphics/RectF;",
            "Ljava/util/List<",
            "LQY0/c$b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p3, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p3

    .line 16
    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, LQY0/c$b;

    .line 27
    .line 28
    invoke-virtual {v1}, LQY0/c$b;->c()J

    .line 29
    .line 30
    .line 31
    move-result-wide v1

    .line 32
    invoke-static {v1, v2}, LRY0/a;->e(J)F

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 45
    .line 46
    .line 47
    move-result-object p3

    .line 48
    check-cast p3, Ljava/lang/Iterable;

    .line 49
    .line 50
    invoke-interface {p3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object p3

    .line 54
    :goto_1
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    if-eqz v0, :cond_2

    .line 59
    .line 60
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    check-cast v0, Ljava/lang/Number;

    .line 65
    .line 66
    invoke-virtual {v0}, Ljava/lang/Number;->floatValue()F

    .line 67
    .line 68
    .line 69
    move-result v5

    .line 70
    iget-object v1, p0, LCY0/b;->c:LDY0/a;

    .line 71
    .line 72
    if-eqz v1, :cond_1

    .line 73
    .line 74
    iget v3, p2, Landroid/graphics/RectF;->top:F

    .line 75
    .line 76
    iget v4, p2, Landroid/graphics/RectF;->bottom:F

    .line 77
    .line 78
    const/16 v7, 0x10

    .line 79
    .line 80
    const/4 v8, 0x0

    .line 81
    const/4 v6, 0x0

    .line 82
    move-object v2, p1

    .line 83
    invoke-static/range {v1 .. v8}, LDY0/a;->p(LDY0/a;LIY0/b;FFFFILjava/lang/Object;)V

    .line 84
    .line 85
    .line 86
    goto :goto_2

    .line 87
    :cond_1
    move-object v2, p1

    .line 88
    :goto_2
    move-object p1, v2

    .line 89
    goto :goto_1

    .line 90
    :cond_2
    return-void
.end method

.method public m(LIY0/d;FLwY0/b;)V
    .locals 0
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1, p2, p3}, LQY0/c$a;->a(LQY0/c;LIY0/d;FLwY0/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public n(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;LyY0/d;)V
    .locals 11
    .param p1    # LIY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LyY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LIY0/b;",
            "Landroid/graphics/RectF;",
            "Ljava/util/List<",
            "LQY0/c$b;",
            ">;",
            "LyY0/d;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p3}, LCY0/b;->h(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    iget v0, p0, LCY0/b;->e:F

    .line 5
    .line 6
    const/4 v2, 0x2

    .line 7
    int-to-float v2, v2

    .line 8
    div-float/2addr v0, v2

    .line 9
    invoke-interface {p1, v0}, LIY0/d;->N(F)F

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    invoke-interface {p3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 14
    .line 15
    .line 16
    move-result-object v7

    .line 17
    const/4 v0, 0x0

    .line 18
    :goto_0
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    if-eqz v2, :cond_3

    .line 23
    .line 24
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    add-int/lit8 v8, v0, 0x1

    .line 29
    .line 30
    if-gez v0, :cond_0

    .line 31
    .line 32
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 33
    .line 34
    .line 35
    :cond_0
    check-cast v2, LQY0/c$b;

    .line 36
    .line 37
    iget-object v0, p0, LCY0/b;->f:Lkotlin/jvm/functions/Function1;

    .line 38
    .line 39
    if-eqz v0, :cond_1

    .line 40
    .line 41
    invoke-virtual {v2}, LQY0/c$b;->a()I

    .line 42
    .line 43
    .line 44
    move-result v3

    .line 45
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    invoke-interface {v0, v3}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    :cond_1
    iget-object v0, p0, LCY0/b;->b:LAY0/a;

    .line 53
    .line 54
    if-eqz v0, :cond_2

    .line 55
    .line 56
    invoke-virtual {v2}, LQY0/c$b;->c()J

    .line 57
    .line 58
    .line 59
    move-result-wide v3

    .line 60
    invoke-static {v3, v4}, LRY0/a;->e(J)F

    .line 61
    .line 62
    .line 63
    move-result v3

    .line 64
    sub-float/2addr v3, v6

    .line 65
    invoke-virtual {v2}, LQY0/c$b;->c()J

    .line 66
    .line 67
    .line 68
    move-result-wide v4

    .line 69
    invoke-static {v4, v5}, LRY0/a;->f(J)F

    .line 70
    .line 71
    .line 72
    move-result v4

    .line 73
    sub-float/2addr v4, v6

    .line 74
    invoke-virtual {v2}, LQY0/c$b;->c()J

    .line 75
    .line 76
    .line 77
    move-result-wide v9

    .line 78
    invoke-static {v9, v10}, LRY0/a;->e(J)F

    .line 79
    .line 80
    .line 81
    move-result v5

    .line 82
    add-float/2addr v5, v6

    .line 83
    invoke-virtual {v2}, LQY0/c$b;->c()J

    .line 84
    .line 85
    .line 86
    move-result-wide v9

    .line 87
    invoke-static {v9, v10}, LRY0/a;->f(J)F

    .line 88
    .line 89
    .line 90
    move-result v2

    .line 91
    add-float/2addr v2, v6

    .line 92
    move v1, v5

    .line 93
    move v5, v2

    .line 94
    move v2, v3

    .line 95
    move v3, v4

    .line 96
    move v4, v1

    .line 97
    move-object v1, p1

    .line 98
    invoke-virtual/range {v0 .. v5}, LAY0/a;->b(LIY0/b;FFFF)V

    .line 99
    .line 100
    .line 101
    :cond_2
    move v0, v8

    .line 102
    goto :goto_0

    .line 103
    :cond_3
    invoke-interface {p4}, LyY0/d;->a()LyY0/b;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    move-object v3, p3

    .line 108
    invoke-virtual {p0, p1, p2, p3, v0}, LCY0/b;->p(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;LyY0/b;)V

    .line 109
    .line 110
    .line 111
    return-void
.end method

.method public final p(LIY0/b;Landroid/graphics/RectF;Ljava/util/List;LyY0/b;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LIY0/b;",
            "Landroid/graphics/RectF;",
            "Ljava/util/List<",
            "LQY0/c$b;",
            ">;",
            "LyY0/b;",
            ")V"
        }
    .end annotation

    .line 1
    move-object v0, p3

    .line 2
    iget-object v1, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 3
    .line 4
    if-eqz v1, :cond_0

    .line 5
    .line 6
    iget-object v1, p0, LCY0/b;->g:LQY0/d;

    .line 7
    .line 8
    move-object/from16 v2, p4

    .line 9
    .line 10
    invoke-interface {v1, p3, v2}, LQY0/d;->a(Ljava/util/List;LyY0/b;)Ljava/lang/CharSequence;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    new-instance v1, LCY0/a;

    .line 15
    .line 16
    invoke-direct {v1}, LCY0/a;-><init>()V

    .line 17
    .line 18
    .line 19
    invoke-static {p3, v1}, LMY0/c;->a(Ljava/util/Collection;Lkotlin/jvm/functions/Function1;)F

    .line 20
    .line 21
    .line 22
    move-result v11

    .line 23
    iget-object v0, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 24
    .line 25
    iget-object v5, p0, LCY0/b;->d:Landroid/graphics/RectF;

    .line 26
    .line 27
    const/16 v8, 0x6c

    .line 28
    .line 29
    const/4 v9, 0x0

    .line 30
    const/4 v3, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    move-object v1, p1

    .line 35
    invoke-static/range {v0 .. v9}, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;->m(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;LIY0/d;Ljava/lang/CharSequence;IILandroid/graphics/RectF;ZFILjava/lang/Object;)Landroid/graphics/RectF;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-virtual {v0}, Landroid/graphics/RectF;->width()F

    .line 40
    .line 41
    .line 42
    move-result v3

    .line 43
    const/4 v4, 0x2

    .line 44
    int-to-float v4, v4

    .line 45
    div-float/2addr v3, v4

    .line 46
    invoke-virtual {p0, v11, p2, v3}, LCY0/b;->s(FLandroid/graphics/RectF;F)F

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    invoke-static {v11}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 51
    .line 52
    .line 53
    move-result-object v4

    .line 54
    const-string v5, "tickX"

    .line 55
    .line 56
    invoke-interface {p1, v5, v4}, LIY0/c;->set(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    move-object v4, v0

    .line 60
    iget-object v0, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 61
    .line 62
    iget v5, p2, Landroid/graphics/RectF;->top:F

    .line 63
    .line 64
    invoke-virtual {v4}, Landroid/graphics/RectF;->height()F

    .line 65
    .line 66
    .line 67
    move-result v4

    .line 68
    sub-float/2addr v5, v4

    .line 69
    iget-object v4, p0, LCY0/b;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 70
    .line 71
    invoke-virtual {p0, v4}, LCY0/b;->r(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)F

    .line 72
    .line 73
    .line 74
    move-result v4

    .line 75
    invoke-interface {p1, v4}, LIY0/d;->N(F)F

    .line 76
    .line 77
    .line 78
    move-result v4

    .line 79
    sub-float v4, v5, v4

    .line 80
    .line 81
    sget-object v6, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/VerticalPosition;->Bottom:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/VerticalPosition;

    .line 82
    .line 83
    const/16 v10, 0x1d0

    .line 84
    .line 85
    const/4 v11, 0x0

    .line 86
    const/4 v5, 0x0

    .line 87
    const/4 v7, 0x0

    .line 88
    const/4 v8, 0x0

    .line 89
    const/4 v9, 0x0

    .line 90
    invoke-static/range {v0 .. v11}, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;->d(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;LIY0/b;Ljava/lang/CharSequence;FFLorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/HorizontalPosition;Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/VerticalPosition;IIFILjava/lang/Object;)V

    .line 91
    .line 92
    .line 93
    :cond_0
    return-void
.end method

.method public final r(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)F
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;->e()LAY0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    instance-of v0, p1, LDY0/c;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    check-cast p1, LDY0/c;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object p1, v1

    .line 14
    :goto_0
    if-eqz p1, :cond_1

    .line 15
    .line 16
    invoke-virtual {p1}, LDY0/c;->h()LDY0/b;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    goto :goto_1

    .line 21
    :cond_1
    move-object p1, v1

    .line 22
    :goto_1
    instance-of v0, p1, LEY0/e;

    .line 23
    .line 24
    if-eqz v0, :cond_2

    .line 25
    .line 26
    check-cast p1, LEY0/e;

    .line 27
    .line 28
    goto :goto_2

    .line 29
    :cond_2
    move-object p1, v1

    .line 30
    :goto_2
    if-eqz p1, :cond_3

    .line 31
    .line 32
    invoke-virtual {p1}, LEY0/e;->h()F

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    :cond_3
    if-eqz v1, :cond_4

    .line 41
    .line 42
    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    return p1

    .line 47
    :cond_4
    const/4 p1, 0x0

    .line 48
    return p1
.end method

.method public final s(FLandroid/graphics/RectF;F)F
    .locals 2

    .line 1
    sub-float v0, p1, p3

    .line 2
    .line 3
    iget v1, p2, Landroid/graphics/RectF;->left:F

    .line 4
    .line 5
    cmpg-float v0, v0, v1

    .line 6
    .line 7
    if-gez v0, :cond_0

    .line 8
    .line 9
    add-float/2addr v1, p3

    .line 10
    return v1

    .line 11
    :cond_0
    add-float v0, p1, p3

    .line 12
    .line 13
    iget p2, p2, Landroid/graphics/RectF;->right:F

    .line 14
    .line 15
    cmpl-float v0, v0, p2

    .line 16
    .line 17
    if-lez v0, :cond_1

    .line 18
    .line 19
    sub-float/2addr p2, p3

    .line 20
    return p2

    .line 21
    :cond_1
    return p1
.end method

.method public final t(F)V
    .locals 0

    .line 1
    iput p1, p0, LCY0/b;->e:F

    .line 2
    .line 3
    return-void
.end method

.method public final u(LQY0/d;)V
    .locals 0
    .param p1    # LQY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, LCY0/b;->g:LQY0/d;

    .line 2
    .line 3
    return-void
.end method

.method public final v(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LCY0/b;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-void
.end method
