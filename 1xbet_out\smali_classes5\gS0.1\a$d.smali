.class public final LgS0/a$d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LgS0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgS0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LgS0/a$d$b;,
        LgS0/a$d$a;,
        LgS0/a$d$e;,
        LgS0/a$d$f;,
        LgS0/a$d$d;,
        LgS0/a$d$i;,
        LgS0/a$d$k;,
        LgS0/a$d$n;,
        LgS0/a$d$m;,
        LgS0/a$d$h;,
        LgS0/a$d$g;,
        LgS0/a$d$c;,
        LgS0/a$d$o;,
        LgS0/a$d$l;,
        LgS0/a$d$j;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/b;",
            ">;"
        }
    .end annotation
.end field

.field public A0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/swamp_land/data/repositories/SwampLandRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/h;",
            ">;"
        }
    .end annotation
.end field

.field public B0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LiS0/a;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/d;",
            ">;"
        }
    .end annotation
.end field

.field public C0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/e;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/j;",
            ">;"
        }
    .end annotation
.end field

.field public D0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/l;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;"
        }
    .end annotation
.end field

.field public E0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/l;",
            ">;"
        }
    .end annotation
.end field

.field public F0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/g;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/f;",
            ">;"
        }
    .end annotation
.end field

.field public G0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/q;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;"
        }
    .end annotation
.end field

.field public H0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/o;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/b;",
            ">;"
        }
    .end annotation
.end field

.field public I0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/m;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/c;",
            ">;"
        }
    .end annotation
.end field

.field public J0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/k;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;"
        }
    .end annotation
.end field

.field public K0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/i;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/e;",
            ">;"
        }
    .end annotation
.end field

.field public L0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjS0/a;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/y;",
            ">;"
        }
    .end annotation
.end field

.field public M0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/swamp_land/presentation/game/SwampLandViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/g;",
            ">;"
        }
    .end annotation
.end field

.field public N0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/j;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/e;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/f;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/x;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/q;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/n;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/h;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/j;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/a;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/h;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LQv/v;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKv/a;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LQv/w;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/E;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LgS0/a$d;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/k;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/A;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/d;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/c;",
            ">;"
        }
    .end annotation
.end field

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/w;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTv/e;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LUv/a;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/a;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/e;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexcore/utils/ext/c;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/G;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/b;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/h;",
            ">;"
        }
    .end annotation
.end field

.field public n0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/a;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public p0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/s;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/f;",
            ">;"
        }
    .end annotation
.end field

.field public r0:Lorg/xbet/core/presentation/holder/s;

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/d;",
            ">;"
        }
    .end annotation
.end field

.field public s0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQv/a$s;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/c;",
            ">;"
        }
    .end annotation
.end field

.field public t0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/repositories/FactorsRepository;",
            ">;"
        }
    .end annotation
.end field

.field public u0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/p;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/f;",
            ">;"
        }
    .end annotation
.end field

.field public v0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/c;",
            ">;"
        }
    .end annotation
.end field

.field public w0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/e;",
            ">;"
        }
    .end annotation
.end field

.field public x0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LbS0/c;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/b;",
            ">;"
        }
    .end annotation
.end field

.field public y0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LbS0/a;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;"
        }
    .end annotation
.end field

.field public z0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LgS0/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LgS0/a$d;->c:LgS0/a$d;

    .line 4
    iput-object p3, p0, LgS0/a$d;->a:LQv/v;

    .line 5
    iput-object p2, p0, LgS0/a$d;->b:LQv/w;

    .line 6
    invoke-virtual {p0, p1, p2, p3, p4}, LgS0/a$d;->Y(LgS0/d;LQv/w;LQv/v;LwX0/c;)V

    .line 7
    invoke-virtual {p0, p1, p2, p3, p4}, LgS0/a$d;->Z(LgS0/d;LQv/w;LQv/v;LwX0/c;)V

    .line 8
    invoke-virtual {p0, p1, p2, p3, p4}, LgS0/a$d;->a0(LgS0/d;LQv/w;LQv/v;LwX0/c;)V

    .line 9
    invoke-virtual {p0, p1, p2, p3, p4}, LgS0/a$d;->b0(LgS0/d;LQv/w;LQv/v;LwX0/c;)V

    return-void
.end method

.method public synthetic constructor <init>(LgS0/d;LQv/w;LQv/v;LwX0/c;LgS0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, LgS0/a$d;-><init>(LgS0/d;LQv/w;LQv/v;LwX0/c;)V

    return-void
.end method

.method public static bridge synthetic A(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->G:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic B(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->H:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic C(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->x:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic D(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->m:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic E(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->m0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic F(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->v:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic G(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->O:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic H(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->E:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic I(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->B:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic J(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->M:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic K(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->q0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic L(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->c0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic M(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->T:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic N(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->v0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic O(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->n:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic P(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->D:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic Q(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->u0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic R(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->F:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic S(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->z:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic T(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->U:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic U(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->V:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic V(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->l0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic W(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->w0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic X(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->g0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic d(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->d:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic e(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->i0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic f(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->i:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic g(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->g:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic h(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->h:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic i(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->u:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic j(LgS0/a$d;)LQv/v;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->a:LQv/v;

    return-object p0
.end method

.method public static bridge synthetic k(LgS0/a$d;)LQv/w;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->b:LQv/w;

    return-object p0
.end method

.method public static bridge synthetic l(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->j:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic m(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->N0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic n(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->f:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic o(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->K:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic p(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->o:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic q(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->y:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic r(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->k:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic s(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->t0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic t(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->j0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic u(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->h0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic v(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->P:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic w(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->w:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic x(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->L:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic y(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->A:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic z(LgS0/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LgS0/a$d;->C:Ldagger/internal/h;

    return-object p0
.end method


# virtual methods
.method public final Y(LgS0/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 2

    .line 1
    new-instance p1, LgS0/a$d$b;

    .line 2
    .line 3
    invoke-direct {p1, p3}, LgS0/a$d$b;-><init>(LQv/v;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, LgS0/a$d;->d:Ldagger/internal/h;

    .line 7
    .line 8
    new-instance p1, LgS0/a$d$a;

    .line 9
    .line 10
    invoke-direct {p1, p3}, LgS0/a$d$a;-><init>(LQv/v;)V

    .line 11
    .line 12
    .line 13
    iput-object p1, p0, LgS0/a$d;->e:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {p1}, LDg/d;->a(LBc/a;)LDg/d;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    iput-object p1, p0, LgS0/a$d;->f:Ldagger/internal/h;

    .line 20
    .line 21
    new-instance p1, LgS0/a$d$e;

    .line 22
    .line 23
    invoke-direct {p1, p3}, LgS0/a$d$e;-><init>(LQv/v;)V

    .line 24
    .line 25
    .line 26
    iput-object p1, p0, LgS0/a$d;->g:Ldagger/internal/h;

    .line 27
    .line 28
    new-instance p1, LgS0/a$d$f;

    .line 29
    .line 30
    invoke-direct {p1, p3}, LgS0/a$d$f;-><init>(LQv/v;)V

    .line 31
    .line 32
    .line 33
    iput-object p1, p0, LgS0/a$d;->h:Ldagger/internal/h;

    .line 34
    .line 35
    new-instance p1, LgS0/a$d$d;

    .line 36
    .line 37
    invoke-direct {p1, p3}, LgS0/a$d$d;-><init>(LQv/v;)V

    .line 38
    .line 39
    .line 40
    iput-object p1, p0, LgS0/a$d;->i:Ldagger/internal/h;

    .line 41
    .line 42
    new-instance p1, LgS0/a$d$i;

    .line 43
    .line 44
    invoke-direct {p1, p3}, LgS0/a$d$i;-><init>(LQv/v;)V

    .line 45
    .line 46
    .line 47
    iput-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static {p2, p1}, LQv/F;->a(LQv/w;LBc/a;)LQv/F;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    iput-object p1, p0, LgS0/a$d;->k:Ldagger/internal/h;

    .line 58
    .line 59
    new-instance p1, LgS0/a$d$k;

    .line 60
    .line 61
    invoke-direct {p1, p3}, LgS0/a$d$k;-><init>(LQv/v;)V

    .line 62
    .line 63
    .line 64
    iput-object p1, p0, LgS0/a$d;->l:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p4, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p2, p4, p1}, LQv/X;->a(LQv/w;LBc/a;LBc/a;)LQv/X;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LgS0/a$d;->m:Ldagger/internal/h;

    .line 77
    .line 78
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 79
    .line 80
    invoke-static {p2, p1}, LQv/H0;->a(LQv/w;LBc/a;)LQv/H0;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LgS0/a$d;->n:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static {p2, p1}, LQv/D;->a(LQv/w;LBc/a;)LQv/D;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LgS0/a$d;->o:Ldagger/internal/h;

    .line 101
    .line 102
    new-instance p1, LgS0/a$d$n;

    .line 103
    .line 104
    invoke-direct {p1, p3}, LgS0/a$d$n;-><init>(LQv/v;)V

    .line 105
    .line 106
    .line 107
    iput-object p1, p0, LgS0/a$d;->p:Ldagger/internal/h;

    .line 108
    .line 109
    new-instance p1, LgS0/a$d$m;

    .line 110
    .line 111
    invoke-direct {p1, p3}, LgS0/a$d$m;-><init>(LQv/v;)V

    .line 112
    .line 113
    .line 114
    iput-object p1, p0, LgS0/a$d;->q:Ldagger/internal/h;

    .line 115
    .line 116
    invoke-static {p1}, Lorg/xbet/core/data/data_source/g;->a(LBc/a;)Lorg/xbet/core/data/data_source/g;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    iput-object p1, p0, LgS0/a$d;->r:Ldagger/internal/h;

    .line 121
    .line 122
    new-instance p1, LgS0/a$d$h;

    .line 123
    .line 124
    invoke-direct {p1, p3}, LgS0/a$d$h;-><init>(LQv/v;)V

    .line 125
    .line 126
    .line 127
    iput-object p1, p0, LgS0/a$d;->s:Ldagger/internal/h;

    .line 128
    .line 129
    new-instance p1, LgS0/a$d$g;

    .line 130
    .line 131
    invoke-direct {p1, p3}, LgS0/a$d$g;-><init>(LQv/v;)V

    .line 132
    .line 133
    .line 134
    iput-object p1, p0, LgS0/a$d;->t:Ldagger/internal/h;

    .line 135
    .line 136
    iget-object p3, p0, LgS0/a$d;->p:Ldagger/internal/h;

    .line 137
    .line 138
    iget-object p4, p0, LgS0/a$d;->h:Ldagger/internal/h;

    .line 139
    .line 140
    iget-object v0, p0, LgS0/a$d;->r:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object v1, p0, LgS0/a$d;->s:Ldagger/internal/h;

    .line 143
    .line 144
    invoke-static {p3, p4, v0, v1, p1}, Lorg/xbet/core/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/core/data/repositories/a;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    iput-object p1, p0, LgS0/a$d;->u:Ldagger/internal/h;

    .line 149
    .line 150
    invoke-static {p2, p1}, LQv/Z;->a(LQv/w;LBc/a;)LQv/Z;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iput-object p1, p0, LgS0/a$d;->v:Ldagger/internal/h;

    .line 159
    .line 160
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 161
    .line 162
    invoke-static {p2, p1}, LQv/N;->a(LQv/w;LBc/a;)LQv/N;

    .line 163
    .line 164
    .line 165
    move-result-object p1

    .line 166
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    iput-object p1, p0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 171
    .line 172
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 173
    .line 174
    invoke-static {p2, p1}, LQv/V;->a(LQv/w;LBc/a;)LQv/V;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    iput-object p1, p0, LgS0/a$d;->x:Ldagger/internal/h;

    .line 183
    .line 184
    iget-object p3, p0, LgS0/a$d;->v:Ldagger/internal/h;

    .line 185
    .line 186
    iget-object p4, p0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 187
    .line 188
    iget-object v0, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 189
    .line 190
    invoke-static {p2, p3, p4, p1, v0}, LQv/E;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/E;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 195
    .line 196
    .line 197
    move-result-object p1

    .line 198
    iput-object p1, p0, LgS0/a$d;->y:Ldagger/internal/h;

    .line 199
    .line 200
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 201
    .line 202
    invoke-static {p2, p1}, LQv/U0;->a(LQv/w;LBc/a;)LQv/U0;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    iput-object p1, p0, LgS0/a$d;->z:Ldagger/internal/h;

    .line 211
    .line 212
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 213
    .line 214
    invoke-static {p2, p1}, LQv/P;->a(LQv/w;LBc/a;)LQv/P;

    .line 215
    .line 216
    .line 217
    move-result-object p1

    .line 218
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 219
    .line 220
    .line 221
    move-result-object p1

    .line 222
    iput-object p1, p0, LgS0/a$d;->A:Ldagger/internal/h;

    .line 223
    .line 224
    iget-object p1, p0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 225
    .line 226
    invoke-static {p2, p1}, LQv/x0;->a(LQv/w;LBc/a;)LQv/x0;

    .line 227
    .line 228
    .line 229
    move-result-object p1

    .line 230
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 231
    .line 232
    .line 233
    move-result-object p1

    .line 234
    iput-object p1, p0, LgS0/a$d;->B:Ldagger/internal/h;

    .line 235
    .line 236
    return-void
.end method

.method public final Z(LgS0/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {v1, v2}, LQv/Q;->a(LQv/w;LBc/a;)LQv/Q;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, LgS0/a$d;->C:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {v1, v2}, LQv/M0;->a(LQv/w;LBc/a;)LQv/M0;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iput-object v2, v0, LgS0/a$d;->D:Ldagger/internal/h;

    .line 28
    .line 29
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {v1, v2}, LQv/j0;->a(LQv/w;LBc/a;)LQv/j0;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    iput-object v2, v0, LgS0/a$d;->E:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 42
    .line 43
    invoke-static {v1, v2}, LQv/R0;->a(LQv/w;LBc/a;)LQv/R0;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    iput-object v2, v0, LgS0/a$d;->F:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {v1, v2}, LQv/S;->a(LQv/w;LBc/a;)LQv/S;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    iput-object v2, v0, LgS0/a$d;->G:Ldagger/internal/h;

    .line 64
    .line 65
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 66
    .line 67
    invoke-static {v1, v2}, LQv/T;->a(LQv/w;LBc/a;)LQv/T;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    iput-object v2, v0, LgS0/a$d;->H:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {v1, v2}, LQv/z;->a(LQv/w;LBc/a;)LQv/z;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 84
    .line 85
    .line 86
    move-result-object v2

    .line 87
    iput-object v2, v0, LgS0/a$d;->I:Ldagger/internal/h;

    .line 88
    .line 89
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static {v1, v2}, LQv/K;->a(LQv/w;LBc/a;)LQv/K;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    iput-object v2, v0, LgS0/a$d;->J:Ldagger/internal/h;

    .line 100
    .line 101
    move-object/from16 v20, v2

    .line 102
    .line 103
    iget-object v2, v0, LgS0/a$d;->m:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v3, v0, LgS0/a$d;->n:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v4, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v5, v0, LgS0/a$d;->o:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v6, v0, LgS0/a$d;->y:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v7, v0, LgS0/a$d;->z:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v8, v0, LgS0/a$d;->A:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object v9, v0, LgS0/a$d;->B:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object v10, v0, LgS0/a$d;->C:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v11, v0, LgS0/a$d;->D:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v12, v0, LgS0/a$d;->E:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v13, v0, LgS0/a$d;->k:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v14, v0, LgS0/a$d;->F:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v15, v0, LgS0/a$d;->x:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v1, v0, LgS0/a$d;->G:Ldagger/internal/h;

    .line 132
    .line 133
    move-object/from16 v16, v1

    .line 134
    .line 135
    iget-object v1, v0, LgS0/a$d;->H:Ldagger/internal/h;

    .line 136
    .line 137
    move-object/from16 v17, v1

    .line 138
    .line 139
    iget-object v1, v0, LgS0/a$d;->I:Ldagger/internal/h;

    .line 140
    .line 141
    move-object/from16 v18, v1

    .line 142
    .line 143
    iget-object v1, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 144
    .line 145
    move-object/from16 v19, v1

    .line 146
    .line 147
    move-object/from16 v1, p2

    .line 148
    .line 149
    invoke-static/range {v1 .. v20}, LQv/x;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/x;

    .line 150
    .line 151
    .line 152
    move-result-object v2

    .line 153
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 154
    .line 155
    .line 156
    move-result-object v2

    .line 157
    iput-object v2, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 160
    .line 161
    invoke-static {v1, v2}, LQv/O;->a(LQv/w;LBc/a;)LQv/O;

    .line 162
    .line 163
    .line 164
    move-result-object v2

    .line 165
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 166
    .line 167
    .line 168
    move-result-object v2

    .line 169
    iput-object v2, v0, LgS0/a$d;->L:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 172
    .line 173
    invoke-static {v1, v2}, LQv/z0;->a(LQv/w;LBc/a;)LQv/z0;

    .line 174
    .line 175
    .line 176
    move-result-object v2

    .line 177
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 178
    .line 179
    .line 180
    move-result-object v2

    .line 181
    iput-object v2, v0, LgS0/a$d;->M:Ldagger/internal/h;

    .line 182
    .line 183
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 184
    .line 185
    invoke-static {v1, v2}, LQv/u0;->a(LQv/w;LBc/a;)LQv/u0;

    .line 186
    .line 187
    .line 188
    move-result-object v2

    .line 189
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 190
    .line 191
    .line 192
    move-result-object v2

    .line 193
    iput-object v2, v0, LgS0/a$d;->N:Ldagger/internal/h;

    .line 194
    .line 195
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 196
    .line 197
    invoke-static {v1, v2}, LQv/c0;->a(LQv/w;LBc/a;)LQv/c0;

    .line 198
    .line 199
    .line 200
    move-result-object v2

    .line 201
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 202
    .line 203
    .line 204
    move-result-object v5

    .line 205
    iput-object v5, v0, LgS0/a$d;->O:Ldagger/internal/h;

    .line 206
    .line 207
    iget-object v2, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 208
    .line 209
    iget-object v3, v0, LgS0/a$d;->M:Ldagger/internal/h;

    .line 210
    .line 211
    iget-object v4, v0, LgS0/a$d;->N:Ldagger/internal/h;

    .line 212
    .line 213
    iget-object v6, v0, LgS0/a$d;->L:Ldagger/internal/h;

    .line 214
    .line 215
    invoke-static/range {v1 .. v6}, LQv/L;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/L;

    .line 216
    .line 217
    .line 218
    move-result-object v2

    .line 219
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 220
    .line 221
    .line 222
    move-result-object v7

    .line 223
    iput-object v7, v0, LgS0/a$d;->P:Ldagger/internal/h;

    .line 224
    .line 225
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 226
    .line 227
    iget-object v3, v0, LgS0/a$d;->k:Ldagger/internal/h;

    .line 228
    .line 229
    iget-object v4, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 230
    .line 231
    iget-object v5, v0, LgS0/a$d;->L:Ldagger/internal/h;

    .line 232
    .line 233
    iget-object v6, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 234
    .line 235
    invoke-static/range {v1 .. v7}, LQv/b1;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/b1;

    .line 236
    .line 237
    .line 238
    move-result-object v2

    .line 239
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 240
    .line 241
    .line 242
    move-result-object v2

    .line 243
    iput-object v2, v0, LgS0/a$d;->Q:Ldagger/internal/h;

    .line 244
    .line 245
    iget-object v3, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 246
    .line 247
    invoke-static {v1, v2, v3}, LQv/t0;->a(LQv/w;LBc/a;LBc/a;)LQv/t0;

    .line 248
    .line 249
    .line 250
    move-result-object v2

    .line 251
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 252
    .line 253
    .line 254
    move-result-object v2

    .line 255
    iput-object v2, v0, LgS0/a$d;->R:Ldagger/internal/h;

    .line 256
    .line 257
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 258
    .line 259
    invoke-static {v2}, Lorg/xbet/core/domain/usecases/y;->a(LBc/a;)Lorg/xbet/core/domain/usecases/y;

    .line 260
    .line 261
    .line 262
    move-result-object v2

    .line 263
    iput-object v2, v0, LgS0/a$d;->S:Ldagger/internal/h;

    .line 264
    .line 265
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 266
    .line 267
    invoke-static {v1, v2}, LQv/E0;->a(LQv/w;LBc/a;)LQv/E0;

    .line 268
    .line 269
    .line 270
    move-result-object v2

    .line 271
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 272
    .line 273
    .line 274
    move-result-object v2

    .line 275
    iput-object v2, v0, LgS0/a$d;->T:Ldagger/internal/h;

    .line 276
    .line 277
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 278
    .line 279
    invoke-static {v1, v2}, LQv/W0;->a(LQv/w;LBc/a;)LQv/W0;

    .line 280
    .line 281
    .line 282
    move-result-object v2

    .line 283
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 284
    .line 285
    .line 286
    move-result-object v2

    .line 287
    iput-object v2, v0, LgS0/a$d;->U:Ldagger/internal/h;

    .line 288
    .line 289
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 290
    .line 291
    invoke-static {v1, v2}, LQv/X0;->a(LQv/w;LBc/a;)LQv/X0;

    .line 292
    .line 293
    .line 294
    move-result-object v2

    .line 295
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 296
    .line 297
    .line 298
    move-result-object v2

    .line 299
    iput-object v2, v0, LgS0/a$d;->V:Ldagger/internal/h;

    .line 300
    .line 301
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 302
    .line 303
    invoke-static {v1, v2}, LQv/w0;->a(LQv/w;LBc/a;)LQv/w0;

    .line 304
    .line 305
    .line 306
    move-result-object v2

    .line 307
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 308
    .line 309
    .line 310
    move-result-object v2

    .line 311
    iput-object v2, v0, LgS0/a$d;->W:Ldagger/internal/h;

    .line 312
    .line 313
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 314
    .line 315
    invoke-static {v1, v2}, LQv/Q0;->a(LQv/w;LBc/a;)LQv/Q0;

    .line 316
    .line 317
    .line 318
    move-result-object v2

    .line 319
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 320
    .line 321
    .line 322
    move-result-object v2

    .line 323
    iput-object v2, v0, LgS0/a$d;->X:Ldagger/internal/h;

    .line 324
    .line 325
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 326
    .line 327
    invoke-static {v1, v2}, LQv/y;->a(LQv/w;LBc/a;)LQv/y;

    .line 328
    .line 329
    .line 330
    move-result-object v2

    .line 331
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 332
    .line 333
    .line 334
    move-result-object v2

    .line 335
    iput-object v2, v0, LgS0/a$d;->Y:Ldagger/internal/h;

    .line 336
    .line 337
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 338
    .line 339
    invoke-static {v1, v2}, LQv/K0;->a(LQv/w;LBc/a;)LQv/K0;

    .line 340
    .line 341
    .line 342
    move-result-object v2

    .line 343
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 344
    .line 345
    .line 346
    move-result-object v2

    .line 347
    iput-object v2, v0, LgS0/a$d;->Z:Ldagger/internal/h;

    .line 348
    .line 349
    iget-object v2, v0, LgS0/a$d;->t:Ldagger/internal/h;

    .line 350
    .line 351
    invoke-static {v1, v2}, LQv/M;->a(LQv/w;LBc/a;)LQv/M;

    .line 352
    .line 353
    .line 354
    move-result-object v1

    .line 355
    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 356
    .line 357
    .line 358
    move-result-object v1

    .line 359
    iput-object v1, v0, LgS0/a$d;->a0:Ldagger/internal/h;

    .line 360
    .line 361
    return-void
.end method

.method public a()LQv/a$a;
    .locals 3

    .line 1
    new-instance v0, LgS0/a$b;

    .line 2
    .line 3
    iget-object v1, p0, LgS0/a$d;->c:LgS0/a$d;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, LgS0/a$b;-><init>(LgS0/a$d;LgS0/b;)V

    .line 7
    .line 8
    .line 9
    return-object v0
.end method

.method public final a0(LgS0/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 47

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    move-object/from16 v8, p3

    .line 6
    .line 7
    iget-object v2, v0, LgS0/a$d;->a0:Ldagger/internal/h;

    .line 8
    .line 9
    invoke-static {v1, v2}, LQv/V0;->a(LQv/w;LBc/a;)LQv/V0;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iput-object v2, v0, LgS0/a$d;->b0:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static {v1, v2}, LQv/D0;->a(LQv/w;LBc/a;)LQv/D0;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    iput-object v2, v0, LgS0/a$d;->c0:Ldagger/internal/h;

    .line 30
    .line 31
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {v1, v2}, LQv/G0;->a(LQv/w;LBc/a;)LQv/G0;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    iput-object v2, v0, LgS0/a$d;->d0:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {v1, v2}, LQv/S0;->a(LQv/w;LBc/a;)LQv/S0;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    iput-object v2, v0, LgS0/a$d;->e0:Ldagger/internal/h;

    .line 54
    .line 55
    iget-object v3, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static {v1, v2, v3}, LQv/J;->a(LQv/w;LBc/a;LBc/a;)LQv/J;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    iput-object v2, v0, LgS0/a$d;->f0:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v2, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object v3, v0, LgS0/a$d;->O:Ldagger/internal/h;

    .line 70
    .line 71
    iget-object v4, v0, LgS0/a$d;->L:Ldagger/internal/h;

    .line 72
    .line 73
    iget-object v5, v0, LgS0/a$d;->M:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object v6, v0, LgS0/a$d;->N:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static/range {v1 .. v6}, LQv/a1;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/a1;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    iput-object v2, v0, LgS0/a$d;->g0:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p1 .. p1}, LgS0/e;->a(LgS0/d;)LgS0/e;

    .line 88
    .line 89
    .line 90
    move-result-object v2

    .line 91
    invoke-static {v2}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    iput-object v2, v0, LgS0/a$d;->h0:Ldagger/internal/h;

    .line 96
    .line 97
    new-instance v2, LgS0/a$d$c;

    .line 98
    .line 99
    invoke-direct {v2, v8}, LgS0/a$d$c;-><init>(LQv/v;)V

    .line 100
    .line 101
    .line 102
    iput-object v2, v0, LgS0/a$d;->i0:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object v2, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object v3, v0, LgS0/a$d;->M:Ldagger/internal/h;

    .line 107
    .line 108
    invoke-static {v1, v2, v3}, LQv/H;->a(LQv/w;LBc/a;LBc/a;)LQv/H;

    .line 109
    .line 110
    .line 111
    move-result-object v2

    .line 112
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 113
    .line 114
    .line 115
    move-result-object v2

    .line 116
    iput-object v2, v0, LgS0/a$d;->j0:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object v2, v0, LgS0/a$d;->a0:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static {v1, v2}, LQv/I;->a(LQv/w;LBc/a;)LQv/I;

    .line 121
    .line 122
    .line 123
    move-result-object v2

    .line 124
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 125
    .line 126
    .line 127
    move-result-object v2

    .line 128
    iput-object v2, v0, LgS0/a$d;->k0:Ldagger/internal/h;

    .line 129
    .line 130
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 131
    .line 132
    invoke-static {v1, v2}, LQv/Y0;->a(LQv/w;LBc/a;)LQv/Y0;

    .line 133
    .line 134
    .line 135
    move-result-object v2

    .line 136
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 137
    .line 138
    .line 139
    move-result-object v2

    .line 140
    iput-object v2, v0, LgS0/a$d;->l0:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object v2, v0, LgS0/a$d;->i0:Ldagger/internal/h;

    .line 143
    .line 144
    iget-object v3, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 145
    .line 146
    invoke-static {v1, v2, v3}, LQv/Y;->a(LQv/w;LBc/a;LBc/a;)LQv/Y;

    .line 147
    .line 148
    .line 149
    move-result-object v2

    .line 150
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 151
    .line 152
    .line 153
    move-result-object v2

    .line 154
    iput-object v2, v0, LgS0/a$d;->m0:Ldagger/internal/h;

    .line 155
    .line 156
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 157
    .line 158
    invoke-static {v2}, Lorg/xbet/core/domain/usecases/k;->a(LBc/a;)Lorg/xbet/core/domain/usecases/k;

    .line 159
    .line 160
    .line 161
    move-result-object v2

    .line 162
    iput-object v2, v0, LgS0/a$d;->n0:Ldagger/internal/h;

    .line 163
    .line 164
    new-instance v2, LgS0/a$d$o;

    .line 165
    .line 166
    invoke-direct {v2, v8}, LgS0/a$d$o;-><init>(LQv/v;)V

    .line 167
    .line 168
    .line 169
    iput-object v2, v0, LgS0/a$d;->o0:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v3, v0, LgS0/a$d;->i0:Ldagger/internal/h;

    .line 172
    .line 173
    invoke-static {v3, v2}, Lorg/xbet/core/domain/usecases/balance/g;->a(LBc/a;LBc/a;)Lorg/xbet/core/domain/usecases/balance/g;

    .line 174
    .line 175
    .line 176
    move-result-object v2

    .line 177
    iput-object v2, v0, LgS0/a$d;->p0:Ldagger/internal/h;

    .line 178
    .line 179
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 180
    .line 181
    invoke-static {v1, v2}, LQv/C0;->a(LQv/w;LBc/a;)LQv/C0;

    .line 182
    .line 183
    .line 184
    move-result-object v2

    .line 185
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 186
    .line 187
    .line 188
    move-result-object v2

    .line 189
    iput-object v2, v0, LgS0/a$d;->q0:Ldagger/internal/h;

    .line 190
    .line 191
    iget-object v9, v0, LgS0/a$d;->d:Ldagger/internal/h;

    .line 192
    .line 193
    iget-object v10, v0, LgS0/a$d;->f:Ldagger/internal/h;

    .line 194
    .line 195
    iget-object v11, v0, LgS0/a$d;->g:Ldagger/internal/h;

    .line 196
    .line 197
    iget-object v12, v0, LgS0/a$d;->h:Ldagger/internal/h;

    .line 198
    .line 199
    iget-object v13, v0, LgS0/a$d;->i:Ldagger/internal/h;

    .line 200
    .line 201
    iget-object v14, v0, LgS0/a$d;->R:Ldagger/internal/h;

    .line 202
    .line 203
    iget-object v15, v0, LgS0/a$d;->C:Ldagger/internal/h;

    .line 204
    .line 205
    iget-object v3, v0, LgS0/a$d;->S:Ldagger/internal/h;

    .line 206
    .line 207
    iget-object v4, v0, LgS0/a$d;->T:Ldagger/internal/h;

    .line 208
    .line 209
    iget-object v5, v0, LgS0/a$d;->U:Ldagger/internal/h;

    .line 210
    .line 211
    iget-object v6, v0, LgS0/a$d;->E:Ldagger/internal/h;

    .line 212
    .line 213
    iget-object v7, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 214
    .line 215
    move-object/from16 v46, v2

    .line 216
    .line 217
    iget-object v2, v0, LgS0/a$d;->x:Ldagger/internal/h;

    .line 218
    .line 219
    move-object/from16 v21, v2

    .line 220
    .line 221
    iget-object v2, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 222
    .line 223
    move-object/from16 v22, v2

    .line 224
    .line 225
    iget-object v2, v0, LgS0/a$d;->V:Ldagger/internal/h;

    .line 226
    .line 227
    move-object/from16 v23, v2

    .line 228
    .line 229
    iget-object v2, v0, LgS0/a$d;->W:Ldagger/internal/h;

    .line 230
    .line 231
    move-object/from16 v24, v2

    .line 232
    .line 233
    iget-object v2, v0, LgS0/a$d;->X:Ldagger/internal/h;

    .line 234
    .line 235
    move-object/from16 v25, v2

    .line 236
    .line 237
    iget-object v2, v0, LgS0/a$d;->Y:Ldagger/internal/h;

    .line 238
    .line 239
    move-object/from16 v26, v2

    .line 240
    .line 241
    iget-object v2, v0, LgS0/a$d;->Z:Ldagger/internal/h;

    .line 242
    .line 243
    move-object/from16 v27, v2

    .line 244
    .line 245
    iget-object v2, v0, LgS0/a$d;->b0:Ldagger/internal/h;

    .line 246
    .line 247
    move-object/from16 v28, v2

    .line 248
    .line 249
    iget-object v2, v0, LgS0/a$d;->B:Ldagger/internal/h;

    .line 250
    .line 251
    move-object/from16 v29, v2

    .line 252
    .line 253
    iget-object v2, v0, LgS0/a$d;->k:Ldagger/internal/h;

    .line 254
    .line 255
    move-object/from16 v30, v2

    .line 256
    .line 257
    iget-object v2, v0, LgS0/a$d;->M:Ldagger/internal/h;

    .line 258
    .line 259
    move-object/from16 v31, v2

    .line 260
    .line 261
    iget-object v2, v0, LgS0/a$d;->c0:Ldagger/internal/h;

    .line 262
    .line 263
    move-object/from16 v32, v2

    .line 264
    .line 265
    iget-object v2, v0, LgS0/a$d;->d0:Ldagger/internal/h;

    .line 266
    .line 267
    move-object/from16 v33, v2

    .line 268
    .line 269
    iget-object v2, v0, LgS0/a$d;->f0:Ldagger/internal/h;

    .line 270
    .line 271
    move-object/from16 v34, v2

    .line 272
    .line 273
    iget-object v2, v0, LgS0/a$d;->g0:Ldagger/internal/h;

    .line 274
    .line 275
    move-object/from16 v35, v2

    .line 276
    .line 277
    iget-object v2, v0, LgS0/a$d;->h0:Ldagger/internal/h;

    .line 278
    .line 279
    move-object/from16 v36, v2

    .line 280
    .line 281
    iget-object v2, v0, LgS0/a$d;->i0:Ldagger/internal/h;

    .line 282
    .line 283
    move-object/from16 v37, v2

    .line 284
    .line 285
    iget-object v2, v0, LgS0/a$d;->j0:Ldagger/internal/h;

    .line 286
    .line 287
    move-object/from16 v38, v2

    .line 288
    .line 289
    iget-object v2, v0, LgS0/a$d;->D:Ldagger/internal/h;

    .line 290
    .line 291
    move-object/from16 v39, v2

    .line 292
    .line 293
    iget-object v2, v0, LgS0/a$d;->k0:Ldagger/internal/h;

    .line 294
    .line 295
    move-object/from16 v40, v2

    .line 296
    .line 297
    iget-object v2, v0, LgS0/a$d;->l0:Ldagger/internal/h;

    .line 298
    .line 299
    move-object/from16 v41, v2

    .line 300
    .line 301
    iget-object v2, v0, LgS0/a$d;->m0:Ldagger/internal/h;

    .line 302
    .line 303
    move-object/from16 v42, v2

    .line 304
    .line 305
    iget-object v2, v0, LgS0/a$d;->l:Ldagger/internal/h;

    .line 306
    .line 307
    move-object/from16 v43, v2

    .line 308
    .line 309
    iget-object v2, v0, LgS0/a$d;->n0:Ldagger/internal/h;

    .line 310
    .line 311
    move-object/from16 v44, v2

    .line 312
    .line 313
    iget-object v2, v0, LgS0/a$d;->p0:Ldagger/internal/h;

    .line 314
    .line 315
    move-object/from16 v45, v2

    .line 316
    .line 317
    move-object/from16 v16, v3

    .line 318
    .line 319
    move-object/from16 v17, v4

    .line 320
    .line 321
    move-object/from16 v18, v5

    .line 322
    .line 323
    move-object/from16 v19, v6

    .line 324
    .line 325
    move-object/from16 v20, v7

    .line 326
    .line 327
    invoke-static/range {v9 .. v46}, Lorg/xbet/core/presentation/holder/s;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/core/presentation/holder/s;

    .line 328
    .line 329
    .line 330
    move-result-object v2

    .line 331
    iput-object v2, v0, LgS0/a$d;->r0:Lorg/xbet/core/presentation/holder/s;

    .line 332
    .line 333
    invoke-static {v2}, LQv/s;->c(Lorg/xbet/core/presentation/holder/s;)Ldagger/internal/h;

    .line 334
    .line 335
    .line 336
    move-result-object v2

    .line 337
    iput-object v2, v0, LgS0/a$d;->s0:Ldagger/internal/h;

    .line 338
    .line 339
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 340
    .line 341
    invoke-static {v1, v2}, LQv/G;->a(LQv/w;LBc/a;)LQv/G;

    .line 342
    .line 343
    .line 344
    move-result-object v2

    .line 345
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 346
    .line 347
    .line 348
    move-result-object v2

    .line 349
    iput-object v2, v0, LgS0/a$d;->t0:Ldagger/internal/h;

    .line 350
    .line 351
    iget-object v2, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 352
    .line 353
    invoke-static {v1, v2}, LQv/N0;->a(LQv/w;LBc/a;)LQv/N0;

    .line 354
    .line 355
    .line 356
    move-result-object v2

    .line 357
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 358
    .line 359
    .line 360
    move-result-object v2

    .line 361
    iput-object v2, v0, LgS0/a$d;->u0:Ldagger/internal/h;

    .line 362
    .line 363
    iget-object v3, v0, LgS0/a$d;->j:Ldagger/internal/h;

    .line 364
    .line 365
    invoke-static {v1, v2, v3}, LQv/F0;->a(LQv/w;LBc/a;LBc/a;)LQv/F0;

    .line 366
    .line 367
    .line 368
    move-result-object v2

    .line 369
    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 370
    .line 371
    .line 372
    move-result-object v7

    .line 373
    iput-object v7, v0, LgS0/a$d;->v0:Ldagger/internal/h;

    .line 374
    .line 375
    iget-object v2, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 376
    .line 377
    iget-object v3, v0, LgS0/a$d;->i0:Ldagger/internal/h;

    .line 378
    .line 379
    iget-object v4, v0, LgS0/a$d;->t0:Ldagger/internal/h;

    .line 380
    .line 381
    iget-object v5, v0, LgS0/a$d;->H:Ldagger/internal/h;

    .line 382
    .line 383
    iget-object v6, v0, LgS0/a$d;->y:Ldagger/internal/h;

    .line 384
    .line 385
    invoke-static/range {v1 .. v7}, LQv/Z0;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/Z0;

    .line 386
    .line 387
    .line 388
    move-result-object v1

    .line 389
    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 390
    .line 391
    .line 392
    move-result-object v1

    .line 393
    iput-object v1, v0, LgS0/a$d;->w0:Ldagger/internal/h;

    .line 394
    .line 395
    iget-object v1, v0, LgS0/a$d;->q:Ldagger/internal/h;

    .line 396
    .line 397
    invoke-static {v1}, LbS0/d;->a(LBc/a;)LbS0/d;

    .line 398
    .line 399
    .line 400
    move-result-object v1

    .line 401
    iput-object v1, v0, LgS0/a$d;->x0:Ldagger/internal/h;

    .line 402
    .line 403
    invoke-static/range {p1 .. p1}, LgS0/f;->a(LgS0/d;)LgS0/f;

    .line 404
    .line 405
    .line 406
    move-result-object v1

    .line 407
    invoke-static {v1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 408
    .line 409
    .line 410
    move-result-object v1

    .line 411
    iput-object v1, v0, LgS0/a$d;->y0:Ldagger/internal/h;

    .line 412
    .line 413
    new-instance v1, LgS0/a$d$l;

    .line 414
    .line 415
    invoke-direct {v1, v8}, LgS0/a$d$l;-><init>(LQv/v;)V

    .line 416
    .line 417
    .line 418
    iput-object v1, v0, LgS0/a$d;->z0:Ldagger/internal/h;

    .line 419
    .line 420
    return-void
.end method

.method public b(Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LgS0/a$d;->c0(Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;)Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b0(LgS0/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LgS0/a$d;->x0:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object v2, v0, LgS0/a$d;->y0:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object v3, v0, LgS0/a$d;->z0:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object v4, v0, LgS0/a$d;->p:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static {v1, v2, v3, v4}, Lorg/xbet/swamp_land/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/swamp_land/data/repositories/a;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iput-object v1, v0, LgS0/a$d;->A0:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iput-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static {v1}, LjS0/f;->a(LBc/a;)LjS0/f;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iput-object v1, v0, LgS0/a$d;->C0:Ldagger/internal/h;

    .line 28
    .line 29
    iget-object v1, v0, LgS0/a$d;->a0:Ldagger/internal/h;

    .line 30
    .line 31
    move-object/from16 v2, p2

    .line 32
    .line 33
    invoke-static {v2, v1}, LQv/g0;->a(LQv/w;LBc/a;)LQv/g0;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object v1, v0, LgS0/a$d;->D0:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v2, v0, LgS0/a$d;->C0:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object v3, v0, LgS0/a$d;->H:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object v4, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v5, v0, LgS0/a$d;->x:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {v2, v3, v4, v5, v1}, LjS0/d;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LjS0/d;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, v0, LgS0/a$d;->E0:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 58
    .line 59
    iget-object v2, v0, LgS0/a$d;->h0:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static {v1, v2}, LjS0/h;->a(LBc/a;LBc/a;)LjS0/h;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iput-object v1, v0, LgS0/a$d;->F0:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object v2, v0, LgS0/a$d;->h0:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static {v1, v2}, LjS0/r;->a(LBc/a;LBc/a;)LjS0/r;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    iput-object v1, v0, LgS0/a$d;->G0:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object v2, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {v1, v2}, LjS0/p;->a(LBc/a;LBc/a;)LjS0/p;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    iput-object v1, v0, LgS0/a$d;->H0:Ldagger/internal/h;

    .line 84
    .line 85
    iget-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 86
    .line 87
    iget-object v2, v0, LgS0/a$d;->h0:Ldagger/internal/h;

    .line 88
    .line 89
    invoke-static {v1, v2}, LjS0/n;->a(LBc/a;LBc/a;)LjS0/n;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    iput-object v1, v0, LgS0/a$d;->I0:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object v2, v0, LgS0/a$d;->w:Ldagger/internal/h;

    .line 96
    .line 97
    invoke-static {v1, v2}, LjS0/l;->a(LBc/a;LBc/a;)LjS0/l;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    iput-object v1, v0, LgS0/a$d;->J0:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static {v1}, LjS0/j;->a(LBc/a;)LjS0/j;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    iput-object v1, v0, LgS0/a$d;->K0:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v1, v0, LgS0/a$d;->B0:Ldagger/internal/h;

    .line 112
    .line 113
    invoke-static {v1}, LjS0/b;->a(LBc/a;)LjS0/b;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    iput-object v1, v0, LgS0/a$d;->L0:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object v2, v0, LgS0/a$d;->j0:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object v3, v0, LgS0/a$d;->T:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v4, v0, LgS0/a$d;->k:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object v5, v0, LgS0/a$d;->m:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object v6, v0, LgS0/a$d;->z:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v7, v0, LgS0/a$d;->h:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v8, v0, LgS0/a$d;->w0:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object v9, v0, LgS0/a$d;->K:Ldagger/internal/h;

    .line 134
    .line 135
    iget-object v10, v0, LgS0/a$d;->E0:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object v11, v0, LgS0/a$d;->Q:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object v12, v0, LgS0/a$d;->F0:Ldagger/internal/h;

    .line 140
    .line 141
    iget-object v13, v0, LgS0/a$d;->u0:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object v14, v0, LgS0/a$d;->J:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v15, v0, LgS0/a$d;->H0:Ldagger/internal/h;

    .line 146
    .line 147
    move-object/from16 v18, v1

    .line 148
    .line 149
    iget-object v1, v0, LgS0/a$d;->J0:Ldagger/internal/h;

    .line 150
    .line 151
    move-object/from16 v16, v1

    .line 152
    .line 153
    iget-object v1, v0, LgS0/a$d;->K0:Ldagger/internal/h;

    .line 154
    .line 155
    move-object/from16 v17, v1

    .line 156
    .line 157
    iget-object v1, v0, LgS0/a$d;->x:Ldagger/internal/h;

    .line 158
    .line 159
    move-object/from16 v19, v1

    .line 160
    .line 161
    iget-object v1, v0, LgS0/a$d;->m0:Ldagger/internal/h;

    .line 162
    .line 163
    move-object/from16 v20, v1

    .line 164
    .line 165
    invoke-static/range {v2 .. v20}, Lorg/xbet/swamp_land/presentation/game/h;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/swamp_land/presentation/game/h;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    iput-object v1, v0, LgS0/a$d;->M0:Ldagger/internal/h;

    .line 170
    .line 171
    new-instance v1, LgS0/a$d$j;

    .line 172
    .line 173
    move-object/from16 v2, p3

    .line 174
    .line 175
    invoke-direct {v1, v2}, LgS0/a$d$j;-><init>(LQv/v;)V

    .line 176
    .line 177
    .line 178
    iput-object v1, v0, LgS0/a$d;->N0:Ldagger/internal/h;

    .line 179
    .line 180
    return-void
.end method

.method public c(Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LgS0/a$d;->d0(Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;)Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c0(Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;)Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LgS0/a$d;->f0()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/swamp_land/presentation/game/f;->a(Lorg/xbet/swamp_land/presentation/game/SwampLandGameFragment;Landroidx/lifecycle/e0$c;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d0(Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;)Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LgS0/a$d;->a:LQv/v;

    .line 2
    .line 3
    invoke-interface {v0}, LQv/v;->e()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LTZ0/a;

    .line 12
    .line 13
    invoke-static {p1, v0}, Lorg/xbet/core/presentation/holder/n;->a(Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;LTZ0/a;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, LgS0/a$d;->s0:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    check-cast v0, LQv/a$s;

    .line 23
    .line 24
    invoke-static {p1, v0}, Lorg/xbet/swamp_land/presentation/holder/c;->a(Lorg/xbet/swamp_land/presentation/holder/SwampLandHolderFragment;LQv/a$s;)V

    .line 25
    .line 26
    .line 27
    return-object p1
.end method

.method public final e0()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/swamp_land/presentation/game/SwampLandViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LgS0/a$d;->M0:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final f0()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LgS0/a$d;->e0()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
