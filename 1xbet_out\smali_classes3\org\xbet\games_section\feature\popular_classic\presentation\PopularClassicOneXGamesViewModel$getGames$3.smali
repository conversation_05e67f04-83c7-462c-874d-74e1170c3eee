.class final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.presentation.PopularClassicOneXGamesViewModel$getGames$3"
    f = "PopularClassicOneXGamesViewModel.kt"
    l = {
        0x19e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$RequestType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->M3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->L$0:Ljava/lang/Object;

    .line 38
    .line 39
    iput v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->label:I

    .line 40
    .line 41
    invoke-virtual {v1, p0}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->d(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    if-ne v1, v0, :cond_2

    .line 46
    .line 47
    return-object v0

    .line 48
    :cond_2
    move-object v0, p1

    .line 49
    move-object p1, v1

    .line 50
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 51
    .line 52
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->y3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/util/List;)Ljava/util/List;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    new-instance v0, Ljava/util/ArrayList;

    .line 57
    .line 58
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 59
    .line 60
    .line 61
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    :cond_3
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    if-eqz v1, :cond_4

    .line 70
    .line 71
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    move-object v2, v1

    .line 76
    check-cast v2, Lf50/a;

    .line 77
    .line 78
    invoke-virtual {v2}, Lf50/a;->e()Ljava/util/List;

    .line 79
    .line 80
    .line 81
    move-result-object v2

    .line 82
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 83
    .line 84
    .line 85
    move-result v2

    .line 86
    if-nez v2, :cond_3

    .line 87
    .line 88
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 89
    .line 90
    .line 91
    goto :goto_1

    .line 92
    :cond_4
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 93
    .line 94
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->H3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Z

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    if-nez p1, :cond_5

    .line 99
    .line 100
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 101
    .line 102
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->a4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lcom/xbet/onexuser/domain/user/c;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-virtual {v1}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 107
    .line 108
    .line 109
    move-result v1

    .line 110
    invoke-static {p1, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->m4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Z)V

    .line 111
    .line 112
    .line 113
    :cond_5
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getGames$3;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 114
    .line 115
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->V3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 120
    .line 121
    .line 122
    move-result v1

    .line 123
    if-eqz v1, :cond_6

    .line 124
    .line 125
    sget-object v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;->a:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$b;

    .line 126
    .line 127
    goto :goto_2

    .line 128
    :cond_6
    new-instance v1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 129
    .line 130
    new-instance v2, Lk50/h$a;

    .line 131
    .line 132
    invoke-direct {v2, v0}, Lk50/h$a;-><init>(Ljava/util/List;)V

    .line 133
    .line 134
    .line 135
    invoke-direct {v1, v2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;-><init>(Ljava/lang/Object;)V

    .line 136
    .line 137
    .line 138
    move-object v0, v1

    .line 139
    :goto_2
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 140
    .line 141
    .line 142
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 143
    .line 144
    return-object p1
.end method
