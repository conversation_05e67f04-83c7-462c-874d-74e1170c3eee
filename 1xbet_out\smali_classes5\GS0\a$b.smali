.class public final LGS0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lr4/d;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LGS0/a;->b(JJ)Lq4/q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "GS0/a$b",
        "Lr4/d;",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:J

.field public final synthetic b:J


# direct methods
.method public constructor <init>(JJ)V
    .locals 0

    .line 1
    iput-wide p1, p0, LGS0/a$b;->a:J

    .line 2
    .line 3
    iput-wide p3, p0, LGS0/a$b;->b:J

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 4

    .line 1
    sget-object p1, Lorg/xbet/swipex/impl/presentation/filter/SwipexFilterFragment;->v1:Lorg/xbet/swipex/impl/presentation/filter/SwipexFilterFragment$a;

    .line 2
    .line 3
    iget-wide v0, p0, LGS0/a$b;->a:J

    .line 4
    .line 5
    iget-wide v2, p0, LGS0/a$b;->b:J

    .line 6
    .line 7
    invoke-virtual {p1, v0, v1, v2, v3}, Lorg/xbet/swipex/impl/presentation/filter/SwipexFilterFragment$a;->a(JJ)Lorg/xbet/swipex/impl/presentation/filter/SwipexFilterFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method

.method public getClearContainer()Z
    .locals 1

    .line 1
    invoke-static {p0}, Lr4/d$b;->a(Lr4/d;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public getScreenKey()Ljava/lang/String;
    .locals 1

    .line 1
    invoke-static {p0}, Lr4/d$b;->b(Lr4/d;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
