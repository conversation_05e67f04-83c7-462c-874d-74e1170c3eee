.class public final synthetic LmJ0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Landroid/graphics/drawable/Drawable;


# direct methods
.method public synthetic constructor <init>(LB4/a;Landroid/graphics/drawable/Drawable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LmJ0/d;->a:LB4/a;

    iput-object p2, p0, LmJ0/d;->b:Landroid/graphics/drawable/Drawable;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LmJ0/d;->a:LB4/a;

    iget-object v1, p0, LmJ0/d;->b:Landroid/graphics/drawable/Drawable;

    check-cast p1, <PERSON>ja<PERSON>/util/List;

    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/player/impl/player/player_transfers/presentation/adapter/PlayerTransferAdapterDelegateKt;->c(LB4/a;Landroid/graphics/drawable/Drawable;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
