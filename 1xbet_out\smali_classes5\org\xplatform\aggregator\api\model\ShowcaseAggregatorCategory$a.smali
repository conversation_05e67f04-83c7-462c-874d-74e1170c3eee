.class public final Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u000b\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\u00020\u0005*\u00020\u0004H\u0007\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\nR\u0014\u0010\u000b\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\nR\u0014\u0010\u000c\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\nR\u0014\u0010\r\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\nR\u0014\u0010\u000e\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\nR\u0014\u0010\u000f\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\nR\u0014\u0010\u0010\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\nR\u0014\u0010\u0011\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\nR\u0014\u0010\u0012\u001a\u00020\u00088\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\n\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;",
        "",
        "a",
        "(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)I",
        "",
        "ONE_X_LIVE_AGGREGATOR_CATEGORY_ID",
        "J",
        "LIVE_AGGREGATOR_CATEGORY_ID",
        "SLOTS_CATEGORY_ID",
        "NONE_CATEGORY_ID",
        "POPULAR_CATEGORY_ID",
        "TOP_CHOICE_CATEGORY_ID",
        "NEW_SLOTS_CATEGORY_ID",
        "EXCLUSIVE_CATEGORY_ID",
        "RECOMMENDATION_CATEGORY_ID",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)I
    .locals 1
    .param p1    # Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    packed-switch p1, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p1

    .line 18
    :pswitch_0
    sget p1, Lpb/k;->recommendation:I

    .line 19
    .line 20
    return p1

    .line 21
    :pswitch_1
    sget p1, Lpb/k;->exclusive_slots:I

    .line 22
    .line 23
    return p1

    .line 24
    :pswitch_2
    sget p1, Lpb/k;->new_slots:I

    .line 25
    .line 26
    return p1

    .line 27
    :pswitch_3
    sget p1, Lpb/k;->top_choice:I

    .line 28
    .line 29
    return p1

    .line 30
    :pswitch_4
    sget p1, Lpb/k;->live_casino_popular:I

    .line 31
    .line 32
    return p1

    .line 33
    :pswitch_5
    sget p1, Lpb/k;->slots_popular:I

    .line 34
    .line 35
    return p1

    .line 36
    :pswitch_6
    sget p1, Lpb/k;->popular:I

    .line 37
    .line 38
    return p1

    .line 39
    :pswitch_7
    sget p1, Lpb/k;->empty_str:I

    .line 40
    .line 41
    return p1

    .line 42
    :pswitch_8
    sget p1, Lpb/k;->array_slots:I

    .line 43
    .line 44
    return p1

    .line 45
    :pswitch_9
    sget p1, Lpb/k;->live_casino_title:I

    .line 46
    .line 47
    return p1

    .line 48
    :pswitch_a
    sget p1, Lpb/k;->one_x_live_text:I

    .line 49
    .line 50
    return p1

    .line 51
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
