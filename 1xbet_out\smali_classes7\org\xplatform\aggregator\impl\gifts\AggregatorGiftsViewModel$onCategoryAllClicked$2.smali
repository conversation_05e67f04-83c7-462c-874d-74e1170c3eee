.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$onCategoryAllClicked$2"
    f = "AggregatorGiftsViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->C5(JJLjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $id:J

.field final synthetic $partId:J

.field final synthetic $title:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JJLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "JJ",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$id:J

    iput-wide p4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$partId:J

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$title:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$id:J

    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$partId:J

    iget-object v6, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$title:Ljava/lang/String;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JJLjava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$id:J

    .line 18
    .line 19
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$partId:J

    .line 20
    .line 21
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 22
    .line 23
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 24
    .line 25
    .line 26
    move-result-wide v5

    .line 27
    cmp-long v0, v1, v5

    .line 28
    .line 29
    if-nez v0, :cond_0

    .line 30
    .line 31
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$id:J

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$partId:J

    .line 35
    .line 36
    :goto_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 37
    .line 38
    iget-wide v7, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$id:J

    .line 39
    .line 40
    iget-wide v9, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$partId:J

    .line 41
    .line 42
    invoke-static {v0, v7, v8, v9, v10}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->t4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JJ)Ljava/lang/Integer;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    if-eqz v0, :cond_1

    .line 47
    .line 48
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 49
    .line 50
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->M4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)LHX0/e;

    .line 55
    .line 56
    .line 57
    move-result-object v7

    .line 58
    const/4 v8, 0x0

    .line 59
    new-array v8, v8, [Ljava/lang/Object;

    .line 60
    .line 61
    invoke-interface {v7, v0, v8}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    if-eqz v0, :cond_1

    .line 66
    .line 67
    :goto_1
    move-object v7, v0

    .line 68
    goto :goto_2

    .line 69
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;->$title:Ljava/lang/String;

    .line 70
    .line 71
    goto :goto_1

    .line 72
    :goto_2
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;

    .line 73
    .line 74
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;-><init>(JJJLjava/lang/String;)V

    .line 75
    .line 76
    .line 77
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$a;

    .line 78
    .line 79
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d$a;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p1, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 86
    .line 87
    return-object p1

    .line 88
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 89
    .line 90
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 91
    .line 92
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    throw p1
.end method
