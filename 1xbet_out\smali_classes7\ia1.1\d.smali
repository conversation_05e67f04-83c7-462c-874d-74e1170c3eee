.class public final Lia1/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lia1/d$b;,
        Lia1/d$a;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()Lia1/a$a;
    .locals 2

    .line 1
    new-instance v0, Lia1/d$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lia1/d$b;-><init>(Lia1/e;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method
