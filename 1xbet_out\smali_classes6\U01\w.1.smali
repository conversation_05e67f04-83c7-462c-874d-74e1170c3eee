.class public final synthetic LU01/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/views/LoadableImageView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/views/LoadableImageView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/w;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LU01/w;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    invoke-static {v0}, Lorg/xbet/uikit/components/views/LoadableImageView;->w(Lorg/xbet/uikit/components/views/LoadableImageView;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
