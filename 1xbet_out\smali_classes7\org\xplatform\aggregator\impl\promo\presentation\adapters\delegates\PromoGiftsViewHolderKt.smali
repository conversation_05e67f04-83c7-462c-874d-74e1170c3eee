.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a)\u0010\u0006\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a9\u0010\u000e\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u0006\u0010\r\u001a\u00020\u000c2\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a9\u0010\u0011\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u0006\u0010\r\u001a\u00020\u00102\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001a9\u0010\u0015\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u0006\u0010\u0014\u001a\u00020\u00132\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016\u001a+\u0010\u0017\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u001a1\u0010\u0019\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a+\u0010\u001d\u001a\u00020\u0001*\u0012\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008j\u0002`\u000b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001e*$\u0008\u0000\u0010\u001f\"\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u00082\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\u0008\u00a8\u0006 "
    }
    d2 = {
        "Lkotlin/Function0;",
        "",
        "onClick",
        "LA4/c;",
        "",
        "LVX0/i;",
        "i",
        "(Lkotlin/jvm/functions/Function0;)LA4/c;",
        "LB4/a;",
        "Lza1/a$d;",
        "LS91/x0;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolder;",
        "Lza1/a$d$a;",
        "item",
        "f",
        "(LB4/a;Lza1/a$d$a;Lkotlin/jvm/functions/Function0;)V",
        "Lza1/a$d$b;",
        "g",
        "(LB4/a;Lza1/a$d$b;Lkotlin/jvm/functions/Function0;)V",
        "",
        "needAuth",
        "h",
        "(LB4/a;ZLkotlin/jvm/functions/Function0;)V",
        "p",
        "(LB4/a;Z)V",
        "m",
        "(LB4/a;Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;",
        "style",
        "q",
        "(LB4/a;Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V",
        "PromoGiftsViewHolder",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/x0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/x0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->k(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->o(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->n(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic e(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->l(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(LB4/a;Lza1/a$d$a;Lkotlin/jvm/functions/Function0;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;",
            "Lza1/a$d$a;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/x0;

    .line 6
    .line 7
    iget-object v1, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 8
    .line 9
    invoke-virtual {p1}, Lza1/a$d$a;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setStyle(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V

    .line 14
    .line 15
    .line 16
    invoke-static {p0, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->m(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 20
    .line 21
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    sget v2, Lpb/k;->gifts_title:I

    .line 30
    .line 31
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeader(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 39
    .line 40
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sget v2, Lpb/k;->active_bonus_title:I

    .line 49
    .line 50
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabel(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p1}, Lza1/a$d$a;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->CARD:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 62
    .line 63
    if-eq p2, v1, :cond_0

    .line 64
    .line 65
    invoke-virtual {p1}, Lza1/a$d$a;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 66
    .line 67
    .line 68
    move-result-object p2

    .line 69
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->HEADER:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 70
    .line 71
    if-eq p2, v1, :cond_0

    .line 72
    .line 73
    invoke-virtual {p1}, Lza1/a$d$a;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 74
    .line 75
    .line 76
    move-result-object p2

    .line 77
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->PICTURE_L:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 78
    .line 79
    if-eq p2, v1, :cond_0

    .line 80
    .line 81
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 82
    .line 83
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    sget v2, Lpb/k;->gifts_title:I

    .line 92
    .line 93
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setTitle(Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    :cond_0
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 101
    .line 102
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    sget v2, Lpb/k;->bonuses_and_free_spins_title:I

    .line 111
    .line 112
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeaderTitle(Ljava/lang/String;)V

    .line 117
    .line 118
    .line 119
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 120
    .line 121
    invoke-virtual {p1}, Lza1/a$d$a;->p()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    invoke-virtual {p1}, Lza1/a$d$a;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 126
    .line 127
    .line 128
    move-result-object v2

    .line 129
    invoke-static {v2}, Lza1/c;->b(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    new-instance v3, Ljava/lang/StringBuilder;

    .line 134
    .line 135
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 136
    .line 137
    .line 138
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 139
    .line 140
    .line 141
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 142
    .line 143
    .line 144
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setPicture(Ljava/lang/String;)V

    .line 149
    .line 150
    .line 151
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 152
    .line 153
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 154
    .line 155
    .line 156
    move-result-object v1

    .line 157
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 158
    .line 159
    .line 160
    move-result-object v1

    .line 161
    sget v2, Lpb/k;->go_to:I

    .line 162
    .line 163
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setButtonTitle(Ljava/lang/String;)V

    .line 168
    .line 169
    .line 170
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 171
    .line 172
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabelStyle()V

    .line 173
    .line 174
    .line 175
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 176
    .line 177
    sget-object v1, Ll8/j;->a:Ll8/j;

    .line 178
    .line 179
    invoke-virtual {p1}, Lza1/a$d$a;->d()D

    .line 180
    .line 181
    .line 182
    move-result-wide v2

    .line 183
    invoke-virtual {p1}, Lza1/a$d$a;->e()Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v4

    .line 187
    sget-object v5, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 188
    .line 189
    invoke-virtual {v1, v2, v3, v4, v5}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 190
    .line 191
    .line 192
    move-result-object v1

    .line 193
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setValue(Ljava/lang/String;)V

    .line 194
    .line 195
    .line 196
    invoke-virtual {p1}, Lza1/a$d$a;->f()Z

    .line 197
    .line 198
    .line 199
    move-result p1

    .line 200
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->p(LB4/a;Z)V

    .line 201
    .line 202
    .line 203
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 204
    .line 205
    const/16 p1, 0x8

    .line 206
    .line 207
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 208
    .line 209
    .line 210
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 211
    .line 212
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;->u()V

    .line 213
    .line 214
    .line 215
    iget-object p0, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 216
    .line 217
    const/4 p1, 0x0

    .line 218
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 219
    .line 220
    .line 221
    return-void
.end method

.method public static final g(LB4/a;Lza1/a$d$b;Lkotlin/jvm/functions/Function0;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;",
            "Lza1/a$d$b;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/x0;

    .line 6
    .line 7
    iget-object v1, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 8
    .line 9
    invoke-virtual {p1}, Lza1/a$d$b;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setStyle(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V

    .line 14
    .line 15
    .line 16
    invoke-static {p0, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->m(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 20
    .line 21
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    sget v2, Lpb/k;->gifts_title:I

    .line 30
    .line 31
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeader(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 39
    .line 40
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sget v2, Lpb/k;->available_bonuses_desc:I

    .line 49
    .line 50
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabel(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p1}, Lza1/a$d$b;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->CARD:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 62
    .line 63
    if-eq p2, v1, :cond_0

    .line 64
    .line 65
    invoke-virtual {p1}, Lza1/a$d$b;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 66
    .line 67
    .line 68
    move-result-object p2

    .line 69
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->HEADER:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 70
    .line 71
    if-eq p2, v1, :cond_0

    .line 72
    .line 73
    invoke-virtual {p1}, Lza1/a$d$b;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 74
    .line 75
    .line 76
    move-result-object p2

    .line 77
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->PICTURE_L:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 78
    .line 79
    if-eq p2, v1, :cond_0

    .line 80
    .line 81
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 82
    .line 83
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    sget v2, Lpb/k;->gifts_title:I

    .line 92
    .line 93
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setTitle(Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    :cond_0
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 101
    .line 102
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    sget v2, Lpb/k;->bonuses_and_free_spins_title:I

    .line 111
    .line 112
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeaderTitle(Ljava/lang/String;)V

    .line 117
    .line 118
    .line 119
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 120
    .line 121
    invoke-virtual {p1}, Lza1/a$d$b;->p()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    invoke-virtual {p1}, Lza1/a$d$b;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 126
    .line 127
    .line 128
    move-result-object v2

    .line 129
    invoke-static {v2}, Lza1/c;->b(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    new-instance v3, Ljava/lang/StringBuilder;

    .line 134
    .line 135
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 136
    .line 137
    .line 138
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 139
    .line 140
    .line 141
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 142
    .line 143
    .line 144
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setPicture(Ljava/lang/String;)V

    .line 149
    .line 150
    .line 151
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 152
    .line 153
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 154
    .line 155
    .line 156
    move-result-object v1

    .line 157
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 158
    .line 159
    .line 160
    move-result-object v1

    .line 161
    sget v2, Lpb/k;->go_to:I

    .line 162
    .line 163
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setButtonTitle(Ljava/lang/String;)V

    .line 168
    .line 169
    .line 170
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 171
    .line 172
    invoke-virtual {p1}, Lza1/a$d$b;->d()I

    .line 173
    .line 174
    .line 175
    move-result v1

    .line 176
    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setValue(Ljava/lang/String;)V

    .line 181
    .line 182
    .line 183
    invoke-virtual {p1}, Lza1/a$d$b;->e()Z

    .line 184
    .line 185
    .line 186
    move-result p1

    .line 187
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->p(LB4/a;Z)V

    .line 188
    .line 189
    .line 190
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 191
    .line 192
    const/16 p1, 0x8

    .line 193
    .line 194
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 195
    .line 196
    .line 197
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 198
    .line 199
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;->u()V

    .line 200
    .line 201
    .line 202
    iget-object p0, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 203
    .line 204
    const/4 p1, 0x0

    .line 205
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 206
    .line 207
    .line 208
    return-void
.end method

.method public static final h(LB4/a;ZLkotlin/jvm/functions/Function0;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;Z",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/x0;

    .line 6
    .line 7
    iget-object v1, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    check-cast v2, Lza1/a$d;

    .line 14
    .line 15
    invoke-interface {v2}, Lza1/a$d;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setStyle(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V

    .line 20
    .line 21
    .line 22
    invoke-static {p0, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->m(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    check-cast p2, Lza1/a$d;

    .line 30
    .line 31
    invoke-interface {p2}, Lza1/a$d;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->BUTTON:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 36
    .line 37
    if-ne p2, v1, :cond_0

    .line 38
    .line 39
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 40
    .line 41
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    sget v2, Lpb/k;->gifts_title:I

    .line 50
    .line 51
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setTitle(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 59
    .line 60
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    sget v2, Lpb/k;->bonuses_and_free_spins_title:I

    .line 69
    .line 70
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabelTitle(Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    goto :goto_0

    .line 78
    :cond_0
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 79
    .line 80
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 85
    .line 86
    .line 87
    move-result-object v1

    .line 88
    sget v2, Lpb/k;->gifts_title:I

    .line 89
    .line 90
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeader(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 98
    .line 99
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    sget v2, Lpb/k;->bonuses_for_games_title:I

    .line 108
    .line 109
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setTitle(Ljava/lang/String;)V

    .line 114
    .line 115
    .line 116
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 117
    .line 118
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    sget v2, Lpb/k;->bonuses_and_free_spins_title:I

    .line 127
    .line 128
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setHeaderTitle(Ljava/lang/String;)V

    .line 133
    .line 134
    .line 135
    :goto_0
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object p2

    .line 139
    check-cast p2, Lza1/a$d;

    .line 140
    .line 141
    invoke-interface {p2}, Lza1/a$d;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 142
    .line 143
    .line 144
    move-result-object p2

    .line 145
    invoke-static {p0, p2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->q(LB4/a;Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V

    .line 146
    .line 147
    .line 148
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 149
    .line 150
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v1

    .line 154
    check-cast v1, Lza1/a$d;

    .line 155
    .line 156
    invoke-interface {v1}, Lza1/a$d;->p()Ljava/lang/String;

    .line 157
    .line 158
    .line 159
    move-result-object v1

    .line 160
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    check-cast v2, Lza1/a$d;

    .line 165
    .line 166
    invoke-interface {v2}, Lza1/a$d;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 167
    .line 168
    .line 169
    move-result-object v2

    .line 170
    invoke-static {v2}, Lza1/c;->b(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)Ljava/lang/String;

    .line 171
    .line 172
    .line 173
    move-result-object v2

    .line 174
    new-instance v3, Ljava/lang/StringBuilder;

    .line 175
    .line 176
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 177
    .line 178
    .line 179
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 180
    .line 181
    .line 182
    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 183
    .line 184
    .line 185
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setPicture(Ljava/lang/String;)V

    .line 190
    .line 191
    .line 192
    iget-object p2, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 193
    .line 194
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 195
    .line 196
    .line 197
    move-result-object v1

    .line 198
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 199
    .line 200
    .line 201
    move-result-object v1

    .line 202
    sget v2, Lpb/k;->go_to:I

    .line 203
    .line 204
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 205
    .line 206
    .line 207
    move-result-object v1

    .line 208
    invoke-virtual {p2, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setButtonTitle(Ljava/lang/String;)V

    .line 209
    .line 210
    .line 211
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->p(LB4/a;Z)V

    .line 212
    .line 213
    .line 214
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 215
    .line 216
    const/16 p1, 0x8

    .line 217
    .line 218
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 219
    .line 220
    .line 221
    iget-object p0, v0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 222
    .line 223
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;->u()V

    .line 224
    .line 225
    .line 226
    iget-object p0, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 227
    .line 228
    const/4 p1, 0x0

    .line 229
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 230
    .line 231
    .line 232
    return-void
.end method

.method public static final i(Lkotlin/jvm/functions/Function0;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LAa1/g;

    .line 2
    .line 3
    invoke-direct {v0}, LAa1/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LAa1/h;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LAa1/h;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$promoGiftsViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$promoGiftsViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$promoGiftsViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$promoGiftsViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/x0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/x0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/x0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final k(Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LAa1/i;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0}, LAa1/i;-><init>(LB4/a;Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final l(LB4/a;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lza1/a$d;

    .line 6
    .line 7
    instance-of v0, p2, Lza1/a$d$d;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    check-cast p1, LS91/x0;

    .line 16
    .line 17
    iget-object p1, p1, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 18
    .line 19
    const/16 v0, 0x8

    .line 20
    .line 21
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    check-cast p1, LS91/x0;

    .line 29
    .line 30
    iget-object p1, p1, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 31
    .line 32
    check-cast p2, Lza1/a$d$d;

    .line 33
    .line 34
    invoke-virtual {p2}, Lza1/a$d$d;->getType()Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 35
    .line 36
    .line 37
    move-result-object p2

    .line 38
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;->s(Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    check-cast p0, LS91/x0;

    .line 46
    .line 47
    iget-object p0, p0, LS91/x0;->c:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesShimmer;

    .line 48
    .line 49
    const/4 p1, 0x0

    .line 50
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 51
    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_0
    instance-of v0, p2, Lza1/a$d$e;

    .line 55
    .line 56
    if-eqz v0, :cond_1

    .line 57
    .line 58
    check-cast p2, Lza1/a$d$e;

    .line 59
    .line 60
    invoke-virtual {p2}, Lza1/a$d$e;->d()Z

    .line 61
    .line 62
    .line 63
    move-result p2

    .line 64
    invoke-static {p0, p2, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->h(LB4/a;ZLkotlin/jvm/functions/Function0;)V

    .line 65
    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    instance-of v0, p2, Lza1/a$d$b;

    .line 69
    .line 70
    if-eqz v0, :cond_2

    .line 71
    .line 72
    check-cast p2, Lza1/a$d$b;

    .line 73
    .line 74
    invoke-static {p0, p2, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->g(LB4/a;Lza1/a$d$b;Lkotlin/jvm/functions/Function0;)V

    .line 75
    .line 76
    .line 77
    goto :goto_0

    .line 78
    :cond_2
    instance-of v0, p2, Lza1/a$d$a;

    .line 79
    .line 80
    if-eqz v0, :cond_3

    .line 81
    .line 82
    check-cast p2, Lza1/a$d$a;

    .line 83
    .line 84
    invoke-static {p0, p2, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt;->f(LB4/a;Lza1/a$d$a;Lkotlin/jvm/functions/Function0;)V

    .line 85
    .line 86
    .line 87
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 88
    .line 89
    return-object p0

    .line 90
    :cond_3
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 91
    .line 92
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 93
    .line 94
    .line 95
    throw p0
.end method

.method public static final m(LB4/a;Lkotlin/jvm/functions/Function0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/x0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 8
    .line 9
    new-instance v1, LAa1/j;

    .line 10
    .line 11
    invoke-direct {v1, p1}, LAa1/j;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, LS91/x0;

    .line 22
    .line 23
    iget-object p0, p0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 24
    .line 25
    new-instance v0, LAa1/k;

    .line 26
    .line 27
    invoke-direct {v0, p1}, LAa1/k;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setOnBonusButtonClickListener(Lkotlin/jvm/functions/Function0;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public static final n(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final o(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final p(LB4/a;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LS91/x0;

    .line 6
    .line 7
    iget-object p0, p0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 8
    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setDisabled()V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setEnabled()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public static final q(LB4/a;Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "Lza1/a$d;",
            "LS91/x0;",
            ">;",
            "Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;",
            ")V"
        }
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/PromoGiftsViewHolderKt$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    const-string v1, ""

    .line 11
    .line 12
    if-eq p1, v0, :cond_2

    .line 13
    .line 14
    const/4 v0, 0x2

    .line 15
    if-eq p1, v0, :cond_1

    .line 16
    .line 17
    const/4 v0, 0x3

    .line 18
    if-eq p1, v0, :cond_0

    .line 19
    .line 20
    return-void

    .line 21
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    check-cast p0, LS91/x0;

    .line 26
    .line 27
    iget-object p0, p0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 28
    .line 29
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabelTitle(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    check-cast p0, LS91/x0;

    .line 38
    .line 39
    iget-object p0, p0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 40
    .line 41
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabelTitle(Ljava/lang/String;)V

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_2
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 46
    .line 47
    .line 48
    move-result-object p0

    .line 49
    check-cast p0, LS91/x0;

    .line 50
    .line 51
    iget-object p0, p0, LS91/x0;->b:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;

    .line 52
    .line 53
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonuses;->setLabelTitle(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    return-void
.end method
