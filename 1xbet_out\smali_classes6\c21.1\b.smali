.class public final synthetic Lc21/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/FilterContainerView;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/FilterContainerView;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc21/b;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/FilterContainerView;

    iput-object p2, p0, Lc21/b;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lc21/b;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/FilterContainerView;

    iget-object v1, p0, Lc21/b;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;->b(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/FilterContainerView;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;Landroid/view/View;)V

    return-void
.end method
