.class public final synthetic LN1/w;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LN1/x;->a:LN1/x;

    return-void
.end method

.method public static a(LN1/x;Landroid/net/Uri;Ljava/util/Map;)[LN1/r;
    .locals 0

    .line 1
    invoke-interface {p0}, LN1/x;->e()[LN1/r;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static b(LN1/x;I)LN1/x;
    .locals 0
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    return-object p0
.end method

.method public static c(LN1/x;Z)LN1/x;
    .locals 0
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1
    return-object p0
.end method

.method public static d(LN1/x;Lk2/s$a;)LN1/x;
    .locals 0

    .line 1
    return-object p0
.end method

.method public static synthetic e()[LN1/r;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    new-array v0, v0, [LN1/r;

    .line 3
    .line 4
    return-object v0
.end method
