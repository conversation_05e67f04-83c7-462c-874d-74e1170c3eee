.class public final LhT0/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0018\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LhT0/g;",
        "",
        "LRf0/o;",
        "settingsPrefsRepository",
        "<init>",
        "(LRf0/o;)V",
        "",
        "turnOn",
        "LfT0/a;",
        "a",
        "(Z)LfT0/a;",
        "LRf0/o;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LRf0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LRf0/o;)V
    .locals 0
    .param p1    # LRf0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LhT0/g;->a:LRf0/o;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Z)LfT0/a;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    new-instance p1, LfT0/a;

    .line 4
    .line 5
    iget-object v0, p0, LhT0/g;->a:LRf0/o;

    .line 6
    .line 7
    invoke-interface {v0}, LRf0/o;->v()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    iget-object v1, p0, LhT0/g;->a:LRf0/o;

    .line 12
    .line 13
    invoke-interface {v1}, LRf0/o;->u()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sget-object v2, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->TWENTY_FOUR:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 18
    .line 19
    invoke-direct {p1, v0, v1, v2}, LfT0/a;-><init>(IILorg/xbet/themesettings/impl/domain/model/TimeFrame;)V

    .line 20
    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    new-instance p1, LfT0/a;

    .line 24
    .line 25
    iget-object v0, p0, LhT0/g;->a:LRf0/o;

    .line 26
    .line 27
    invoke-interface {v0}, LRf0/o;->d()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    iget-object v1, p0, LhT0/g;->a:LRf0/o;

    .line 32
    .line 33
    invoke-interface {v1}, LRf0/o;->s()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    sget-object v2, Lorg/xbet/themesettings/impl/domain/model/TimeFrame;->TWENTY_FOUR:Lorg/xbet/themesettings/impl/domain/model/TimeFrame;

    .line 38
    .line 39
    invoke-direct {p1, v0, v1, v2}, LfT0/a;-><init>(IILorg/xbet/themesettings/impl/domain/model/TimeFrame;)V

    .line 40
    .line 41
    .line 42
    return-object p1
.end method
