.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u0000 52\u00020\u0001:\u00016B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001d\u0010\u0008\u001a\u00020\u00072\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\rJ\u0017\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0012J\u000f\u0010\u0015\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0003J\u0019\u0010\u0018\u001a\u00020\u00072\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016H\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u0003J\u000f\u0010\u001b\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u0003R\u001b\u0010!\u001a\u00020\u001c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 R\"\u0010)\u001a\u00020\"8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008#\u0010$\u001a\u0004\u0008%\u0010&\"\u0004\u0008\'\u0010(R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.R\u001b\u00104\u001a\u0002008BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u0010,\u001a\u0004\u00082\u00103\u00a8\u00067"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "Lkb1/l;",
        "data",
        "",
        "K2",
        "(Ljava/util/List;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "L2",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "M2",
        "",
        "loading",
        "d",
        "(Z)V",
        "buttonVisible",
        "G2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "x2",
        "v2",
        "LS91/g0;",
        "i0",
        "LRc/c;",
        "I2",
        "()LS91/g0;",
        "viewBinding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "getViewModelFactory",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        "k0",
        "Lkotlin/j;",
        "J2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        "viewModel",
        "Lcb1/a;",
        "l0",
        "H2",
        "()Lcb1/a;",
        "adapter",
        "m0",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTournamentsConditionsBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->n0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->m0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lu91/c;->fragment_tournaments_conditions:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$viewModel$2;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$viewModel$2;-><init>(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 20
    .line 21
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$1;

    .line 22
    .line 23
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 24
    .line 25
    .line 26
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const-class v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 31
    .line 32
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$2;

    .line 37
    .line 38
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    .line 39
    .line 40
    .line 41
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    const/4 v5, 0x0

    .line 44
    invoke-direct {v4, v5, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 45
    .line 46
    .line 47
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$4;

    .line 48
    .line 49
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$special$$inlined$viewModels$default$4;-><init>(Landroidx/fragment/app/Fragment;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v5}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->k0:Lkotlin/j;

    .line 57
    .line 58
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/h;

    .line 59
    .line 60
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/h;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)V

    .line 61
    .line 62
    .line 63
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->l0:Lkotlin/j;

    .line 68
    .line 69
    return-void
.end method

.method public static final synthetic A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->G2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->K2(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->L2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->d(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)Lcb1/a;
    .locals 2

    .line 1
    new-instance v0, Lcb1/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/i;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/i;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, Lcb1/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;J)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->J2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;

    .line 6
    .line 7
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->H2()Lcb1/a;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    invoke-virtual {p0}, LA4/e;->getItems()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, v1, p1, p2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->r4(Ljava/lang/String;JLjava/util/List;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method private final G2(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/g0;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    sget p1, Lpb/f;->space_8:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    sget p1, Lpb/f;->space_48:I

    .line 17
    .line 18
    :goto_0
    invoke-virtual {v1, p1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    invoke-virtual {v0}, Landroid/view/View;->getPaddingLeft()I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    invoke-virtual {v0}, Landroid/view/View;->getPaddingRight()I

    .line 31
    .line 32
    .line 33
    move-result v3

    .line 34
    invoke-virtual {v0, v1, v2, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method private final J2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method private final K2(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/l;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->H2()Lcb1/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final L2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->d(Z)V

    .line 3
    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v1, v1, LS91/g0;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 10
    .line 11
    const/16 v2, 0x8

    .line 12
    .line 13
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    iget-object v1, v1, LS91/g0;->b:Lcom/facebook/shimmer/ShimmerFrameLayout;

    .line 21
    .line 22
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iget-object v1, v1, LS91/g0;->c:Landroidx/core/widget/NestedScrollView;

    .line 30
    .line 31
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->M2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method private final M2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/g0;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final d(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/g0;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LS91/g0;->b:Lcom/facebook/shimmer/ShimmerFrameLayout;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v3, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v0, v0, LS91/g0;->c:Landroidx/core/widget/NestedScrollView;

    .line 39
    .line 40
    if-eqz p1, :cond_2

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)Lcb1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)Lcb1/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final H2()Lcb1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcb1/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final I2()LS91/g0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/g0;

    .line 13
    .line 14
    return-object v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->I2()LS91/g0;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object v0, p1, LS91/g0;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->H2()Lcb1/a;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p1, LS91/g0;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 15
    .line 16
    const/4 v0, 0x1

    .line 17
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setHasFixedSize(Z)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-static {p0}, LVa1/w;->a(Landroidx/fragment/app/Fragment;)LVa1/r;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, LVa1/r;->g(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;->J2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->d4()Lkotlinx/coroutines/flow/f0;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsConditionFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
