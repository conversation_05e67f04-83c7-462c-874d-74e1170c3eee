.class public final Lm2/a$g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lm2/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "g"
.end annotation


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I


# direct methods
.method public constructor <init>(IIIIII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lm2/a$g;->a:I

    .line 5
    .line 6
    iput p2, p0, Lm2/a$g;->b:I

    .line 7
    .line 8
    iput p3, p0, Lm2/a$g;->c:I

    .line 9
    .line 10
    iput p4, p0, Lm2/a$g;->d:I

    .line 11
    .line 12
    iput p5, p0, Lm2/a$g;->e:I

    .line 13
    .line 14
    iput p6, p0, Lm2/a$g;->f:I

    .line 15
    .line 16
    return-void
.end method
