.class final Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.BaseAggregatorViewModel$subscribeToBalanceChange$1"
    f = "BaseAggregatorViewModel.kt"
    l = {
        0xa8,
        0xa7
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->h4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Lorg/xbet/balance/model/BalanceModel;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u0002*\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Lorg/xbet/balance/model/BalanceModel;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast v1, Lkotlinx/coroutines/flow/f;

    .line 30
    .line 31
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    move-object v7, p0

    .line 35
    goto :goto_0

    .line 36
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->L$0:Ljava/lang/Object;

    .line 40
    .line 41
    move-object v1, p1

    .line 42
    check-cast v1, Lkotlinx/coroutines/flow/f;

    .line 43
    .line 44
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 45
    .line 46
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->x3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lp9/c;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 51
    .line 52
    .line 53
    move-result p1

    .line 54
    if-eqz p1, :cond_4

    .line 55
    .line 56
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 57
    .line 58
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->A3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lfk/s;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 63
    .line 64
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->u3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/balance/model/BalanceScreenType;

    .line 65
    .line 66
    .line 67
    move-result-object v4

    .line 68
    invoke-interface {p1, v4}, Lfk/s;->a(Lorg/xbet/balance/model/BalanceScreenType;)Z

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    if-eqz p1, :cond_4

    .line 73
    .line 74
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 75
    .line 76
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->z3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/d;

    .line 77
    .line 78
    .line 79
    move-result-object v4

    .line 80
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 81
    .line 82
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->u3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/balance/model/BalanceScreenType;

    .line 83
    .line 84
    .line 85
    move-result-object v5

    .line 86
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->L$0:Ljava/lang/Object;

    .line 87
    .line 88
    iput v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->label:I

    .line 89
    .line 90
    const/4 v6, 0x0

    .line 91
    const/4 v8, 0x2

    .line 92
    const/4 v9, 0x0

    .line 93
    move-object v7, p0

    .line 94
    invoke-static/range {v4 .. v9}, Lek/d$a;->a(Lek/d;Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    if-ne p1, v0, :cond_3

    .line 99
    .line 100
    goto :goto_1

    .line 101
    :cond_3
    :goto_0
    iget-object v3, v7, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;

    .line 102
    .line 103
    move-object v4, p1

    .line 104
    check-cast v4, Lorg/xbet/balance/model/BalanceModel;

    .line 105
    .line 106
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->F3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lek/f;

    .line 107
    .line 108
    .line 109
    move-result-object v5

    .line 110
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->u3(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;)Lorg/xbet/balance/model/BalanceScreenType;

    .line 111
    .line 112
    .line 113
    move-result-object v3

    .line 114
    invoke-interface {v5, v3, v4}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 115
    .line 116
    .line 117
    const/4 v3, 0x0

    .line 118
    iput-object v3, v7, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->L$0:Ljava/lang/Object;

    .line 119
    .line 120
    iput v2, v7, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel$subscribeToBalanceChange$1;->label:I

    .line 121
    .line 122
    invoke-interface {v1, p1, p0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    if-ne p1, v0, :cond_5

    .line 127
    .line 128
    :goto_1
    return-object v0

    .line 129
    :cond_4
    :goto_2
    move-object v7, p0

    .line 130
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 131
    .line 132
    return-object p1
.end method
