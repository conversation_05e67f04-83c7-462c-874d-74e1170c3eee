.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;
.super Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0011\n\u0002\u0010\r\n\u0002\u0008\t\n\u0002\u0010\u000b\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 ]2\u00020\u0001:\u0001^B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u000cJ\u0017\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u000cJ\u000f\u0010\u0013\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u000eJ\u000f\u0010\u0014\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u000eJ\u000f\u0010\u0015\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u000eJ\u000f\u0010\u0016\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u000eJ\u000f\u0010\u0017\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u000eJ\u000f\u0010\u0018\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u000eJ\u000f\u0010\u0019\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u001b\u0010\u001e\u001a\u00020\n2\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001b\u0010 \u001a\u00020\n2\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u001b\u0010!\u001a\u00020\n2\n\u0008\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0002\u00a2\u0006\u0004\u0008!\u0010\u001fJ\u001f\u0010$\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\u00082\u0006\u0010#\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008$\u0010%J7\u0010,\u001a\u00020\n2\u0006\u0010\'\u001a\u00020&2\u0006\u0010(\u001a\u00020\u00082\u0006\u0010)\u001a\u00020\u00082\u0006\u0010*\u001a\u00020\u00082\u0006\u0010+\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008,\u0010-J\u0019\u0010.\u001a\u00020\n2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u0008.\u0010\u001fJ\u0019\u0010/\u001a\u00020\n2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u0008/\u0010\u001fJ\u0019\u00100\u001a\u00020\n2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u00080\u0010\u001fJ\u0017\u00101\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u00081\u0010\u000cJ\u0017\u00103\u001a\u00020\n2\u0006\u00102\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u00083\u0010\u000cJ\u0017\u00106\u001a\u00020\n2\u0006\u00105\u001a\u000204H\u0016\u00a2\u0006\u0004\u00086\u00107R\u0014\u00109\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u00108R\u0014\u0010;\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u00108R\u0014\u0010<\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u00108R\u0014\u0010>\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u00108R\u0014\u0010?\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u00108R\u0014\u0010@\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u00108R\u0014\u0010A\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u00108R\u0014\u0010B\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u00108R\u0014\u0010C\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u00108R\u0014\u0010D\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u00108R\u0014\u0010E\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u00108R\u0014\u0010F\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u00108R\u0014\u0010G\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u00108R\u0014\u0010H\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u00108R\u0016\u0010\t\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008I\u00108R\u0014\u0010M\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010Q\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0014\u0010S\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010LR\u0014\u0010U\u001a\u00020J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010LR\u0014\u0010Y\u001a\u00020V8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\u001a\u0010\\\u001a\u00020\u00088\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008Z\u00108\u001a\u0004\u0008[\u0010\u001a\u00a8\u0006_"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attributeSet",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "progress",
        "",
        "setProgressInternally",
        "(I)V",
        "r",
        "()V",
        "p",
        "parentWidth",
        "s",
        "q",
        "o",
        "m",
        "k",
        "n",
        "l",
        "j",
        "getIvStatusIconStart",
        "()I",
        "getCommonTextViewMeasuredWidth",
        "",
        "text",
        "h",
        "(Ljava/lang/CharSequence;)V",
        "d",
        "f",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "setStageNumberText",
        "setTitleText",
        "setCaptionText",
        "setProgress",
        "maxProgress",
        "setMaxProgress",
        "Lc31/a;",
        "state",
        "setState",
        "(Lc31/a;)V",
        "I",
        "size6",
        "g",
        "size24",
        "size28",
        "i",
        "size32",
        "size40",
        "size60",
        "space4",
        "space12",
        "space16",
        "space18",
        "colorSecondary",
        "colorPrimary",
        "colorGreen",
        "minDisplayableProgressWidth",
        "t",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "u",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStageNumber",
        "Landroid/widget/ProgressBar;",
        "v",
        "Landroid/widget/ProgressBar;",
        "progressBar",
        "w",
        "tvTitle",
        "x",
        "tvCaption",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "y",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "ivStatusIcon",
        "z",
        "getCardHeight",
        "cardHeight",
        "A",
        "a",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final A:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final B:I


# instance fields
.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:I

.field public t:I

.field public final u:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Landroid/widget/ProgressBar;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->A:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->B:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 13
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_6:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->f:I

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_24:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->g:I

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_28:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->h:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_32:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->i:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->size_40:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->size_60:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->k:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_4:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_12:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_16:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->n:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_18:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->o:I

    .line 13
    sget v3, LlZ0/d;->uikitSecondary:I

    const/4 v4, 0x0

    const/4 v5, 0x2

    invoke-static {p1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->p:I

    .line 14
    sget v3, LlZ0/d;->uikitPrimary:I

    invoke-static {p1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->q:I

    .line 15
    sget v3, LlZ0/d;->uikitStaticGreen:I

    invoke-static {p1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    iput v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->r:I

    .line 16
    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->s:I

    .line 17
    new-instance v3, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v3, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 18
    const-string v6, "DSAggregatorTournamentStagesCell.TAG_TV_STAGE_NUMBER"

    invoke-virtual {v3, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 19
    sget v6, LlZ0/n;->TextStyle_Title_Medium_L:I

    invoke-static {v3, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/16 v6, 0x11

    .line 20
    invoke-virtual {v3, v6}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v6, 0x1

    .line 21
    invoke-virtual {v3, v6}, Landroid/widget/TextView;->setMaxLines(I)V

    const/4 v7, 0x0

    .line 22
    invoke-virtual {v3, v7}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 23
    iput-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 24
    new-instance v8, Landroid/widget/ProgressBar;

    const v9, 0x103001f

    invoke-direct {v8, p1, v4, v9}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 25
    new-instance v9, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v9, v1, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v8, v9}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 26
    invoke-virtual {v8, v7, v7, v7, v7}, Landroid/view/View;->setPadding(IIII)V

    .line 27
    sget p2, LlZ0/h;->rounded_full:I

    invoke-virtual {v8, p2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 28
    invoke-virtual {v8, v6}, Landroid/view/View;->setClipToOutline(Z)V

    .line 29
    sget p2, LlZ0/h;->ds_tournament_stages_cell_number_progress_bar_bg:I

    .line 30
    invoke-static {p1, p2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {v8, p2}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 31
    invoke-virtual {v8, v7}, Landroid/widget/ProgressBar;->setIndeterminate(Z)V

    .line 32
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result p2

    if-eqz p2, :cond_0

    const/high16 p2, -0x40800000    # -1.0f

    goto :goto_0

    :cond_0
    const/high16 p2, 0x3f800000    # 1.0f

    :goto_0
    invoke-virtual {v8, p2}, Landroid/view/View;->setScaleX(F)V

    .line 33
    iput-object v8, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 34
    new-instance p2, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {p2, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 35
    const-string v1, "DSAggregatorTournamentStagesCell.TAG_TV_TITLE"

    invoke-virtual {p2, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 36
    sget v1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {p2, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/16 v1, 0x10

    .line 37
    invoke-virtual {p2, v1}, Landroid/widget/TextView;->setGravity(I)V

    .line 38
    invoke-virtual {p2, v5}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 39
    sget-object v1, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {p2, v1}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 40
    invoke-virtual {p2, v7}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 41
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v9

    const/4 v10, 0x3

    const/4 v11, 0x5

    if-eqz v9, :cond_1

    const/4 v9, 0x5

    goto :goto_1

    :cond_1
    const/4 v9, 0x3

    :goto_1
    invoke-virtual {p2, v9}, Landroid/widget/TextView;->setGravity(I)V

    .line 42
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    new-instance v9, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v9, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 44
    const-string v12, "DSAggregatorTournamentStagesCell.TAG_TV_CAPTION"

    invoke-virtual {v9, v12}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 45
    sget v12, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v9, v12}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 46
    invoke-virtual {v9, v6}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 47
    invoke-virtual {v9, v7}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 48
    invoke-virtual {v9, v1}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 49
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v1

    if-eqz v1, :cond_2

    const/4 v10, 0x5

    :cond_2
    invoke-virtual {v9, v10}, Landroid/widget/TextView;->setGravity(I)V

    .line 50
    iput-object v9, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    new-instance v1, Landroidx/appcompat/widget/AppCompatImageView;

    invoke-direct {v1, p1}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 52
    const-string v6, "DSAggregatorTournamentStagesCell.TAG_IV_STATUS_ICON"

    invoke-virtual {v1, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 53
    new-instance v6, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v6, v0, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v6}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 54
    sget v0, LlZ0/h;->ic_glyph_checkmark:I

    invoke-virtual {v1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 55
    sget v0, LlZ0/d;->uikitStaticGreen:I

    invoke-static {p1, v0, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p1

    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object p1

    invoke-virtual {v1, p1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 56
    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 57
    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->z:I

    .line 58
    sget p1, LlZ0/h;->rounded_background_16_content:I

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 59
    invoke-virtual {p0, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 60
    invoke-virtual {p0, v8}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 61
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 62
    invoke-virtual {p0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 63
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final d(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_10:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_12:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->d(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static synthetic g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->f(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final getCommonTextViewMeasuredWidth()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->i:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 9
    .line 10
    mul-int/lit8 v1, v1, 0x3

    .line 11
    .line 12
    sub-int/2addr v0, v1

    .line 13
    return v0
.end method

.method private final getIvStatusIconStart()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->n:I

    .line 14
    .line 15
    sub-int/2addr v0, v1

    .line 16
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 17
    .line 18
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    sub-int/2addr v0, v1

    .line 23
    return v0

    .line 24
    :cond_0
    const/4 v0, 0x0

    .line 25
    return v0
.end method

.method private final h(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getCommonTextViewMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sget v2, LlZ0/g;->text_12:I

    .line 8
    .line 9
    sget v3, LlZ0/g;->text_14:I

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    if-nez p1, :cond_1

    .line 20
    .line 21
    const-string p1, ""

    .line 22
    .line 23
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static synthetic i(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->h(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final j()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getIvStatusIconStart()I

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->o:I

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->n:I

    .line 14
    .line 15
    sub-int v4, v0, v4

    .line 16
    .line 17
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->o:I

    .line 18
    .line 19
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 20
    .line 21
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    add-int/2addr v5, v0

    .line 26
    move-object v0, p0

    .line 27
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final l()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l:I

    .line 25
    .line 26
    add-int v5, v0, v1

    .line 27
    .line 28
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 31
    .line 32
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 33
    .line 34
    mul-int/lit8 v1, v1, 0x2

    .line 35
    .line 36
    add-int v4, v0, v1

    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 43
    .line 44
    sub-int v6, v0, v1

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 47
    .line 48
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    add-int v7, v5, v0

    .line 53
    .line 54
    move-object v2, p0

    .line 55
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 56
    .line 57
    .line 58
    :cond_0
    return-void
.end method

.method private final m()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    div-int/lit8 v0, v0, 0x2

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 16
    .line 17
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 22
    .line 23
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    add-int/2addr v1, v2

    .line 28
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l:I

    .line 29
    .line 30
    add-int/2addr v1, v2

    .line 31
    div-int/lit8 v1, v1, 0x2

    .line 32
    .line 33
    :goto_0
    sub-int/2addr v0, v1

    .line 34
    move v4, v0

    .line 35
    goto :goto_1

    .line 36
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    div-int/lit8 v0, v0, 0x2

    .line 41
    .line 42
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    div-int/lit8 v1, v1, 0x2

    .line 49
    .line 50
    goto :goto_0

    .line 51
    :goto_1
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 52
    .line 53
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->o:I

    .line 54
    .line 55
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    add-int v5, v3, v0

    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 62
    .line 63
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    add-int v6, v4, v0

    .line 68
    .line 69
    move-object v1, p0

    .line 70
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 71
    .line 72
    .line 73
    return-void
.end method

.method private final n()V
    .locals 14

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lc31/a$a;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    div-int/lit8 v0, v0, 0x2

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 16
    .line 17
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    div-int/lit8 v1, v1, 0x2

    .line 22
    .line 23
    sub-int v5, v0, v1

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 26
    .line 27
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 28
    .line 29
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 30
    .line 31
    mul-int/lit8 v1, v1, 0x2

    .line 32
    .line 33
    add-int v4, v0, v1

    .line 34
    .line 35
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 40
    .line 41
    sub-int v6, v0, v1

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    add-int v7, v5, v0

    .line 50
    .line 51
    move-object v2, p0

    .line 52
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 53
    .line 54
    .line 55
    move-object v8, v2

    .line 56
    return-void

    .line 57
    :cond_0
    move-object v8, p0

    .line 58
    instance-of v1, v0, Lc31/a$b;

    .line 59
    .line 60
    if-eqz v1, :cond_1

    .line 61
    .line 62
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    div-int/lit8 v0, v0, 0x2

    .line 67
    .line 68
    iget-object v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 69
    .line 70
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 71
    .line 72
    .line 73
    move-result v1

    .line 74
    div-int/lit8 v1, v1, 0x2

    .line 75
    .line 76
    sub-int v11, v0, v1

    .line 77
    .line 78
    iget-object v9, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 79
    .line 80
    iget v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 81
    .line 82
    iget v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 83
    .line 84
    mul-int/lit8 v1, v1, 0x2

    .line 85
    .line 86
    add-int v10, v0, v1

    .line 87
    .line 88
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getIvStatusIconStart()I

    .line 89
    .line 90
    .line 91
    move-result v0

    .line 92
    iget v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 93
    .line 94
    sub-int v12, v0, v1

    .line 95
    .line 96
    iget-object v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 97
    .line 98
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 99
    .line 100
    .line 101
    move-result v0

    .line 102
    add-int v13, v11, v0

    .line 103
    .line 104
    invoke-static/range {v8 .. v13}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 105
    .line 106
    .line 107
    return-void

    .line 108
    :cond_1
    instance-of v0, v0, Lc31/a$c;

    .line 109
    .line 110
    if-eqz v0, :cond_3

    .line 111
    .line 112
    iget-object v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 113
    .line 114
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 115
    .line 116
    .line 117
    move-result v0

    .line 118
    const/16 v1, 0x8

    .line 119
    .line 120
    if-ne v0, v1, :cond_2

    .line 121
    .line 122
    const/4 v0, 0x0

    .line 123
    goto :goto_0

    .line 124
    :cond_2
    iget-object v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 125
    .line 126
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 127
    .line 128
    .line 129
    move-result v0

    .line 130
    iget v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l:I

    .line 131
    .line 132
    add-int/2addr v0, v1

    .line 133
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 134
    .line 135
    .line 136
    move-result v1

    .line 137
    div-int/lit8 v1, v1, 0x2

    .line 138
    .line 139
    iget-object v2, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 140
    .line 141
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 142
    .line 143
    .line 144
    move-result v2

    .line 145
    add-int/2addr v2, v0

    .line 146
    div-int/lit8 v2, v2, 0x2

    .line 147
    .line 148
    sub-int v11, v1, v2

    .line 149
    .line 150
    iget-object v9, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 151
    .line 152
    iget v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 153
    .line 154
    iget v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 155
    .line 156
    mul-int/lit8 v1, v1, 0x2

    .line 157
    .line 158
    add-int v10, v0, v1

    .line 159
    .line 160
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 161
    .line 162
    .line 163
    move-result v0

    .line 164
    iget v1, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 165
    .line 166
    sub-int v12, v0, v1

    .line 167
    .line 168
    iget-object v0, v8, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 169
    .line 170
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 171
    .line 172
    .line 173
    move-result v0

    .line 174
    add-int v13, v11, v0

    .line 175
    .line 176
    invoke-static/range {v8 .. v13}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 177
    .line 178
    .line 179
    :cond_3
    return-void
.end method

.method private final o()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final r()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->h:I

    .line 9
    .line 10
    const/high16 v2, 0x40000000    # 2.0f

    .line 11
    .line 12
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    const/4 v2, 0x0

    .line 17
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final setProgressInternally(I)V
    .locals 4

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->t:I

    .line 2
    .line 3
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->s:I

    .line 4
    .line 5
    int-to-float v0, v0

    .line 6
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 7
    .line 8
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 13
    .line 14
    int-to-float v1, v1

    .line 15
    div-float/2addr v0, v1

    .line 16
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 17
    .line 18
    invoke-virtual {v1}, Landroid/widget/ProgressBar;->getMax()I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    int-to-float v1, v1

    .line 23
    mul-float v0, v0, v1

    .line 24
    .line 25
    float-to-int v0, v0

    .line 26
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 27
    .line 28
    invoke-virtual {v1}, Landroid/widget/ProgressBar;->getMax()I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    sub-int/2addr v1, v0

    .line 33
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 34
    .line 35
    const/4 v3, 0x1

    .line 36
    if-gt v3, p1, :cond_0

    .line 37
    .line 38
    if-gt p1, v0, :cond_0

    .line 39
    .line 40
    move p1, v0

    .line 41
    goto :goto_0

    .line 42
    :cond_0
    invoke-virtual {v2}, Landroid/widget/ProgressBar;->getMax()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    if-ge p1, v0, :cond_1

    .line 47
    .line 48
    if-le p1, v1, :cond_1

    .line 49
    .line 50
    move p1, v1

    .line 51
    :cond_1
    :goto_0
    invoke-virtual {v2, p1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 52
    .line 53
    .line 54
    return-void
.end method


# virtual methods
.method public final f(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->h:I

    .line 4
    .line 5
    sget v2, LlZ0/g;->text_16:I

    .line 6
    .line 7
    sget v3, LlZ0/g;->text_24:I

    .line 8
    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 p1, 0x0

    .line 17
    :goto_0
    if-nez p1, :cond_1

    .line 18
    .line 19
    const-string p1, ""

    .line 20
    .line 21
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public getCardHeight()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->z:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l:I

    .line 25
    .line 26
    add-int v5, v0, v1

    .line 27
    .line 28
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 29
    .line 30
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->n:I

    .line 31
    .line 32
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    add-int v6, v4, v0

    .line 37
    .line 38
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 39
    .line 40
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    add-int v7, v5, v0

    .line 45
    .line 46
    move-object v2, p0

    .line 47
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 48
    .line 49
    .line 50
    :cond_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->k()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->n()V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->l()V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    const/high16 p2, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getCardHeight()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p2

    .line 22
    invoke-virtual {p0, v0, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 23
    .line 24
    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->r()V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->p()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->s(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->q(I)V

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->o()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final p()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 16
    .line 17
    const/high16 v2, 0x40000000    # 2.0f

    .line 18
    .line 19
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 24
    .line 25
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 30
    .line 31
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 32
    .line 33
    .line 34
    move-result v2

    .line 35
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 36
    .line 37
    .line 38
    :cond_0
    return-void
.end method

.method public final q(I)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 12
    .line 13
    mul-int/lit8 v1, v1, 0x3

    .line 14
    .line 15
    sub-int/2addr p1, v1

    .line 16
    const/high16 v1, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    const/4 v1, 0x0

    .line 23
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final s(I)V
    .locals 4

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->i(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    instance-of v0, v0, Lc31/a$b;

    .line 11
    .line 12
    const/high16 v1, 0x40000000    # 2.0f

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->getIvStatusIconStart()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 24
    .line 25
    sub-int/2addr v0, v3

    .line 26
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 27
    .line 28
    mul-int/lit8 v3, v3, 0x3

    .line 29
    .line 30
    sub-int/2addr v0, v3

    .line 31
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    invoke-virtual {p1, v0, v1}, Landroid/view/View;->measure(II)V

    .line 40
    .line 41
    .line 42
    return-void

    .line 43
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 44
    .line 45
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->j:I

    .line 46
    .line 47
    sub-int/2addr p1, v3

    .line 48
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->m:I

    .line 49
    .line 50
    mul-int/lit8 v3, v3, 0x3

    .line 51
    .line 52
    sub-int/2addr p1, v3

    .line 53
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public setCaptionText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->d(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMaxProgress(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 4
    .line 5
    .line 6
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->t:I

    .line 7
    .line 8
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setProgressInternally(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setProgress(I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setProgressInternally(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setStageNumberText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->f(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    const/4 v1, 0x3

    .line 9
    invoke-static {p1, v1}, Lkotlin/text/A;->X1(Ljava/lang/CharSequence;I)Ljava/lang/CharSequence;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 p1, 0x0

    .line 15
    :goto_0
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public setState(Lc31/a;)V
    .locals 6
    .param p1    # Lc31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setState(Lc31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lc31/a$a;

    .line 5
    .line 6
    const/4 v1, 0x2

    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 12
    .line 13
    sget v3, LlZ0/n;->TextStyle_Title_Medium_L:I

    .line 14
    .line 15
    invoke-static {v0, v3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->p:I

    .line 21
    .line 22
    invoke-virtual {v0, v3}, Landroid/widget/TextView;->setTextColor(I)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 26
    .line 27
    sget v3, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 28
    .line 29
    invoke-static {v0, v3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 38
    .line 39
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 43
    .line 44
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 48
    .line 49
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 50
    .line 51
    .line 52
    goto/16 :goto_2

    .line 53
    .line 54
    :cond_0
    instance-of v0, p1, Lc31/a$c;

    .line 55
    .line 56
    const/4 v3, 0x0

    .line 57
    if-eqz v0, :cond_4

    .line 58
    .line 59
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 60
    .line 61
    sget v1, LlZ0/n;->TextStyle_Title_Bold_L:I

    .line 62
    .line 63
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 67
    .line 68
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->q:I

    .line 69
    .line 70
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 71
    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 74
    .line 75
    sget v1, LlZ0/n;->TextStyle_Text_Bold_TextPrimary:I

    .line 76
    .line 77
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 78
    .line 79
    .line 80
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 81
    .line 82
    const/4 v1, 0x1

    .line 83
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 84
    .line 85
    .line 86
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 87
    .line 88
    move-object v4, p1

    .line 89
    check-cast v4, Lc31/a$c;

    .line 90
    .line 91
    invoke-virtual {v4}, Lc31/a$c;->c()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v5

    .line 95
    if-eqz v5, :cond_2

    .line 96
    .line 97
    invoke-interface {v5}, Ljava/lang/CharSequence;->length()I

    .line 98
    .line 99
    .line 100
    move-result v5

    .line 101
    if-nez v5, :cond_1

    .line 102
    .line 103
    goto :goto_0

    .line 104
    :cond_1
    const/4 v1, 0x0

    .line 105
    :cond_2
    :goto_0
    if-nez v1, :cond_3

    .line 106
    .line 107
    const/4 v1, 0x0

    .line 108
    goto :goto_1

    .line 109
    :cond_3
    const/16 v1, 0x8

    .line 110
    .line 111
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 112
    .line 113
    .line 114
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 115
    .line 116
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 117
    .line 118
    .line 119
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 120
    .line 121
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 122
    .line 123
    .line 124
    invoke-virtual {v4}, Lc31/a$c;->c()Ljava/lang/String;

    .line 125
    .line 126
    .line 127
    move-result-object v0

    .line 128
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setCaptionText(Ljava/lang/CharSequence;)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {v4}, Lc31/a$c;->e()I

    .line 132
    .line 133
    .line 134
    move-result v0

    .line 135
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setMaxProgress(I)V

    .line 136
    .line 137
    .line 138
    invoke-virtual {v4}, Lc31/a$c;->f()I

    .line 139
    .line 140
    .line 141
    move-result v0

    .line 142
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setProgress(I)V

    .line 143
    .line 144
    .line 145
    goto :goto_2

    .line 146
    :cond_4
    instance-of v0, p1, Lc31/a$b;

    .line 147
    .line 148
    if-eqz v0, :cond_5

    .line 149
    .line 150
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 151
    .line 152
    sget v4, LlZ0/n;->TextStyle_Title_Medium_L:I

    .line 153
    .line 154
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 155
    .line 156
    .line 157
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->u:Landroidx/appcompat/widget/AppCompatTextView;

    .line 158
    .line 159
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->r:I

    .line 160
    .line 161
    invoke-virtual {v0, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 162
    .line 163
    .line 164
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 165
    .line 166
    sget v4, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    .line 167
    .line 168
    invoke-static {v0, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 169
    .line 170
    .line 171
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 172
    .line 173
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 174
    .line 175
    .line 176
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 177
    .line 178
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 179
    .line 180
    .line 181
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->y:Landroidx/appcompat/widget/AppCompatImageView;

    .line 182
    .line 183
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 184
    .line 185
    .line 186
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->v:Landroid/widget/ProgressBar;

    .line 187
    .line 188
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 189
    .line 190
    .line 191
    :goto_2
    invoke-interface {p1}, Lc31/a;->b()Ljava/lang/String;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setStageNumberText(Ljava/lang/CharSequence;)V

    .line 196
    .line 197
    .line 198
    invoke-interface {p1}, Lc31/a;->a()Ljava/lang/String;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->setTitleText(Ljava/lang/CharSequence;)V

    .line 203
    .line 204
    .line 205
    return-void

    .line 206
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 207
    .line 208
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 209
    .line 210
    .line 211
    throw p1
.end method

.method public setTitleText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->h(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method
