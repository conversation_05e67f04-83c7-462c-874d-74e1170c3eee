.class public final Lu61/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0018\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0086\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lu61/a;",
        "",
        "Lak/a;",
        "balanceFeature",
        "Lw61/a;",
        "saveMenuChangedUseCase",
        "Lorg/xbet/wallet/impl/domain/wallets/usecase/e;",
        "clearQuickBetUseCase",
        "LmS0/a;",
        "swipexFeature",
        "<init>",
        "(Lak/a;Lw61/a;Lorg/xbet/wallet/impl/domain/wallets/usecase/e;LmS0/a;)V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "item",
        "",
        "a",
        "(Lorg/xbet/balance/model/BalanceModel;)V",
        "b",
        "Lak/a;",
        "Lw61/a;",
        "c",
        "Lorg/xbet/wallet/impl/domain/wallets/usecase/e;",
        "d",
        "LmS0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lw61/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/wallet/impl/domain/wallets/usecase/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LmS0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lak/a;Lw61/a;Lorg/xbet/wallet/impl/domain/wallets/usecase/e;LmS0/a;)V
    .locals 0
    .param p1    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lw61/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/wallet/impl/domain/wallets/usecase/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LmS0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lu61/a;->a:Lak/a;

    .line 5
    .line 6
    iput-object p2, p0, Lu61/a;->b:Lw61/a;

    .line 7
    .line 8
    iput-object p3, p0, Lu61/a;->c:Lorg/xbet/wallet/impl/domain/wallets/usecase/e;

    .line 9
    .line 10
    iput-object p4, p0, Lu61/a;->d:LmS0/a;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 7
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p1}, Lu61/a;->b(Lorg/xbet/balance/model/BalanceModel;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lu61/a;->a:Lak/a;

    .line 5
    .line 6
    invoke-interface {v0}, Lak/a;->J()Lfk/u;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 11
    .line 12
    .line 13
    move-result-wide v2

    .line 14
    const/4 v5, 0x2

    .line 15
    const/4 v6, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    invoke-static/range {v1 .. v6}, Lfk/u$a;->a(Lfk/u;JLorg/xbet/balance/model/BalanceScreenType;ILjava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lu61/a;->a:Lak/a;

    .line 21
    .line 22
    invoke-interface {v0}, Lak/a;->H()Lfk/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, p1}, Lfk/a;->a(Lorg/xbet/balance/model/BalanceModel;)V

    .line 27
    .line 28
    .line 29
    iget-object p1, p0, Lu61/a;->b:Lw61/a;

    .line 30
    .line 31
    invoke-virtual {p1}, Lw61/a;->a()V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lu61/a;->c:Lorg/xbet/wallet/impl/domain/wallets/usecase/e;

    .line 35
    .line 36
    invoke-virtual {p1}, Lorg/xbet/wallet/impl/domain/wallets/usecase/e;->a()V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lu61/a;->d:LmS0/a;

    .line 40
    .line 41
    invoke-interface {p1}, LmS0/a;->c()LpS0/a;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    invoke-interface {p1}, LpS0/a;->invoke()V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final b(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 3

    .line 1
    const/4 v0, 0x6

    .line 2
    new-array v0, v0, [Lorg/xbet/balance/model/BalanceScreenType;

    .line 3
    .line 4
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->SWIPEX:Lorg/xbet/balance/model/BalanceScreenType;

    .line 5
    .line 6
    const/4 v2, 0x0

    .line 7
    aput-object v1, v0, v2

    .line 8
    .line 9
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->MULTI:Lorg/xbet/balance/model/BalanceScreenType;

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    aput-object v1, v0, v2

    .line 13
    .line 14
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->WALLET:Lorg/xbet/balance/model/BalanceScreenType;

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    aput-object v1, v0, v2

    .line 18
    .line 19
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->HISTORY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 20
    .line 21
    const/4 v2, 0x3

    .line 22
    aput-object v1, v0, v2

    .line 23
    .line 24
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->MAIN_MENU:Lorg/xbet/balance/model/BalanceScreenType;

    .line 25
    .line 26
    const/4 v2, 0x4

    .line 27
    aput-object v1, v0, v2

    .line 28
    .line 29
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->COUPON:Lorg/xbet/balance/model/BalanceScreenType;

    .line 30
    .line 31
    const/4 v2, 0x5

    .line 32
    aput-object v1, v0, v2

    .line 33
    .line 34
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_0

    .line 47
    .line 48
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    check-cast v1, Lorg/xbet/balance/model/BalanceScreenType;

    .line 53
    .line 54
    iget-object v2, p0, Lu61/a;->a:Lak/a;

    .line 55
    .line 56
    invoke-interface {v2}, Lak/a;->p()Lfk/b;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    invoke-interface {v2, v1, p1}, Lfk/b;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_0
    return-void
.end method
