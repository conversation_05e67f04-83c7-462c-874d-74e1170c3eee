.class public final LtW0/e$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/q$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LtW0/f;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LtW0/e$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LwW0/k;LzX0/k;)LtW0/q;
    .locals 2

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(<PERSON>ja<PERSON>/lang/Object;)<PERSON>ja<PERSON>/lang/Object;

    .line 5
    .line 6
    .line 7
    new-instance v0, LtW0/e$b;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-direct {v0, p1, p2, v1}, LtW0/e$b;-><init>(LwW0/k;LzX0/k;LtW0/f;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method
