.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u0000 82\u00020\u0001:\u00019B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003R\"\u0010\u0012\u001a\u00020\u000b8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u000c\u0010\r\u001a\u0004\u0008\u000e\u0010\u000f\"\u0004\u0008\u0010\u0010\u0011R\"\u0010\u001a\u001a\u00020\u00138\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017\"\u0004\u0008\u0018\u0010\u0019R\u001b\u0010 \u001a\u00020\u001b8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u001fR\u001b\u0010&\u001a\u00020!8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%R\u001b\u0010,\u001a\u00020\'8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+R\u001a\u00102\u001a\u00020-8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101R\u001b\u00107\u001a\u0002038BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00084\u0010\u001d\u001a\u0004\u00085\u00106\u00a8\u0006:"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "LBy0/f;",
        "i0",
        "LBy0/f;",
        "E2",
        "()LBy0/f;",
        "setViewModelFactory",
        "(LBy0/f;)V",
        "viewModelFactory",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "j0",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "G2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;",
        "setWhoWinCardFragmentDelegate",
        "(Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V",
        "whoWinCardFragmentDelegate",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;",
        "k0",
        "Lkotlin/j;",
        "D2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;",
        "viewModel",
        "LGq0/w;",
        "l0",
        "LRc/c;",
        "C2",
        "()LGq0/w;",
        "viewBinding",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
        "m0",
        "LeX0/h;",
        "B2",
        "()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
        "screenParams",
        "",
        "n0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "LGy0/d;",
        "o0",
        "F2",
        "()LGy0/d;",
        "whoWinAdapter",
        "b1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic k1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final v1:I


# instance fields
.field public i0:LBy0/f;

.field public j0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Z

.field public final o0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xbet/special_event/impl/databinding/FragmentOpponentsBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "screenParams"

    .line 20
    .line 21
    const-string v5, "getScreenParams()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->k1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->b1:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$a;

    .line 47
    .line 48
    const/16 v0, 0x8

    .line 49
    .line 50
    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->v1:I

    .line 51
    .line 52
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LUo0/c;->fragment_opponents:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/a;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->k0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$viewBinding$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->l0:LRc/c;

    .line 57
    .line 58
    new-instance v0, LeX0/h;

    .line 59
    .line 60
    const-string v1, "PARAMS_KEY"

    .line 61
    .line 62
    const/4 v2, 0x2

    .line 63
    invoke-direct {v0, v1, v5, v2, v5}, LeX0/h;-><init>(Ljava/lang/String;Landroid/os/Parcelable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 64
    .line 65
    .line 66
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->m0:LeX0/h;

    .line 67
    .line 68
    const/4 v0, 0x1

    .line 69
    iput-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->n0:Z

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/b;

    .line 72
    .line 73
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/b;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)V

    .line 74
    .line 75
    .line 76
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->o0:Lkotlin/j;

    .line 81
    .line 82
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)LGy0/d;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->F2()LGy0/d;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final H2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)Landroidx/lifecycle/e0$c;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->E2()LBy0/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v4, 0x4

    .line 8
    const/4 v5, 0x0

    .line 9
    const/4 v3, 0x0

    .line 10
    move-object v2, p0

    .line 11
    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/viewmodel/core/f;-><init>(Lorg/xbet/ui_common/viewmodel/core/e;Landroidx/savedstate/f;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static final I2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)LGy0/d;
    .locals 1

    .line 1
    new-instance v0, LGy0/d;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->D2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-direct {v0, p0}, LGy0/d;-><init>(LHy0/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)LGy0/d;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->I2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)LGy0/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->H2(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final B2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->m0:LeX0/h;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/h;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Landroid/os/Parcelable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 13
    .line 14
    return-object v0
.end method

.method public final C2()LGq0/w;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LGq0/w;

    .line 13
    .line 14
    return-object v0
.end method

.method public final D2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final E2()LBy0/f;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->i0:LBy0/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final F2()LGy0/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->o0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LGy0/d;

    .line 8
    .line 9
    return-object v0
.end method

.method public final G2()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->j0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->n0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->G2()Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->D2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    new-instance v1, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$Unknown;

    .line 13
    .line 14
    invoke-direct {v1}, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$Unknown;-><init>()V

    .line 15
    .line 16
    .line 17
    invoke-interface {p1, p0, v0, v1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;->a(Landroidx/fragment/app/Fragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->C2()LGq0/w;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iget-object p1, p1, LGq0/w;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->F2()LGy0/d;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LBy0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LBy0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LBy0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    sget-object v0, Lyy0/c;->a:Lyy0/c;

    .line 56
    .line 57
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->B2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->G()I

    .line 62
    .line 63
    .line 64
    move-result v1

    .line 65
    invoke-static {p0}, LQW0/h;->a(Landroidx/fragment/app/Fragment;)Lorg/xbet/ui_common/router/NavBarScreenTypes;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    invoke-virtual {v3}, Lorg/xbet/ui_common/router/NavBarScreenTypes;->getTag()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v3

    .line 73
    invoke-virtual {v0, v1, v3}, Lyy0/c;->c(ILjava/lang/String;)Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    invoke-virtual {v3}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 82
    .line 83
    .line 84
    move-result-object v3

    .line 85
    invoke-virtual {v0, v1, v3}, Lyy0/c;->d(Ljava/lang/String;Landroid/app/Application;)Lyy0/d;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->B2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    invoke-virtual {v2, v0, v1}, LBy0/d;->a(Lyy0/d;Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;)LBy0/c;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    invoke-interface {v0, p0}, LBy0/c;->a(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;)V

    .line 98
    .line 99
    .line 100
    return-void

    .line 101
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 102
    .line 103
    new-instance v2, Ljava/lang/StringBuilder;

    .line 104
    .line 105
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 106
    .line 107
    .line 108
    const-string v3, "Cannot create dependency "

    .line 109
    .line 110
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 111
    .line 112
    .line 113
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 114
    .line 115
    .line 116
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 125
    .line 126
    .line 127
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->D2()Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$onObserveData$1;

    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    invoke-direct {v5, p0, v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$onObserveData$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 19
    .line 20
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 29
    .line 30
    const/4 v6, 0x0

    .line 31
    invoke-direct/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v10, 0x3

    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v7, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    move-object v6, v0

    .line 39
    move-object v9, v1

    .line 40
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    return-void
.end method
