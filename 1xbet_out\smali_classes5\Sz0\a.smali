.class public final LSz0/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LTz0/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0000\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LSz0/a;",
        "LA4/e;",
        "LTz0/a;",
        "<init>",
        "()V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    sget-object v0, LTz0/a;->d:LTz0/a$b;

    .line 2
    .line 3
    invoke-virtual {v0}, LTz0/a$b;->a()Landroidx/recyclerview/widget/i$f;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 11
    .line 12
    invoke-static {}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/compressedcard/period/viewholders/CompressedPeriodItemViewHolderKt;->e()LA4/c;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 17
    .line 18
    .line 19
    return-void
.end method
