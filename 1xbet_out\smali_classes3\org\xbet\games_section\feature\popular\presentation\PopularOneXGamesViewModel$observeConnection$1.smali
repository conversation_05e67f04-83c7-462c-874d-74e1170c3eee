.class final Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular.presentation.PopularOneXGamesViewModel$observeConnection$1"
    f = "PopularOneXGamesViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->O4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "connected",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 14
    .line 15
    invoke-static {v0, p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->n4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V

    .line 16
    .line 17
    .line 18
    const/4 v0, 0x0

    .line 19
    if-nez p1, :cond_1

    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 22
    .line 23
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    sget-object v1, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;

    .line 32
    .line 33
    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    if-eqz p1, :cond_0

    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 40
    .line 41
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->q4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;Z)V

    .line 42
    .line 43
    .line 44
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 45
    .line 46
    return-object p1

    .line 47
    :cond_1
    const/4 p1, 0x3

    .line 48
    new-array p1, p1, [Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 49
    .line 50
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 51
    .line 52
    invoke-static {v1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->H3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    aput-object v1, p1, v0

    .line 61
    .line 62
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 63
    .line 64
    invoke-static {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->U3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    const/4 v1, 0x1

    .line 73
    aput-object v0, p1, v1

    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 76
    .line 77
    invoke-static {v0}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->Y3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    const/4 v1, 0x2

    .line 86
    aput-object v0, p1, v1

    .line 87
    .line 88
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    move-result v0

    .line 96
    if-eqz v0, :cond_2

    .line 97
    .line 98
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 99
    .line 100
    .line 101
    move-result v0

    .line 102
    if-eqz v0, :cond_2

    .line 103
    .line 104
    goto :goto_0

    .line 105
    :cond_2
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 110
    .line 111
    .line 112
    move-result v0

    .line 113
    if-eqz v0, :cond_4

    .line 114
    .line 115
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    check-cast v0, Lorg/xbet/games_section/feature/popular/presentation/a;

    .line 120
    .line 121
    instance-of v0, v0, Lorg/xbet/games_section/feature/popular/presentation/a$d;

    .line 122
    .line 123
    if-nez v0, :cond_3

    .line 124
    .line 125
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 126
    .line 127
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->d4(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    sget-object v0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;->a:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$d$d;

    .line 132
    .line 133
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 134
    .line 135
    .line 136
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel$observeConnection$1;->this$0:Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;

    .line 137
    .line 138
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;->K3(Lorg/xbet/games_section/feature/popular/presentation/PopularOneXGamesViewModel;)V

    .line 139
    .line 140
    .line 141
    :cond_4
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 142
    .line 143
    return-object p1

    .line 144
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 145
    .line 146
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 147
    .line 148
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 149
    .line 150
    .line 151
    throw p1
.end method
