.class public final synthetic LCX0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Landroid/content/Context;

.field public final synthetic c:Ljava/lang/Integer;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Landroid/content/Context;Ljava/lang/Integer;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LCX0/i;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LCX0/i;->b:Landroid/content/Context;

    iput-object p3, p0, LCX0/i;->c:Ljava/lang/Integer;

    iput-object p4, p0, LCX0/i;->d:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LCX0/i;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LCX0/i;->b:Landroid/content/Context;

    iget-object v2, p0, LCX0/i;->c:Ljava/lang/Integer;

    iget-object v3, p0, LCX0/i;->d:Lkotlin/jvm/functions/Function1;

    check-cast p1, Ljava/lang/Throwable;

    invoke-static {v0, v1, v2, v3, p1}, LCX0/l;->b(Lkotlin/jvm/functions/Function1;Landroid/content/Context;Ljava/lang/Integer;Lkotlin/jvm/functions/Function1;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
