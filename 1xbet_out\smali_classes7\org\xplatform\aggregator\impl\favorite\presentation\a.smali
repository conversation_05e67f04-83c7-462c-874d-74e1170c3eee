.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/a;
.super LkY0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LkY0/a<",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B5\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\u000fH\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/presentation/a;",
        "LkY0/a;",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;",
        "Landroidx/fragment/app/FragmentManager;",
        "childFragmentManager",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "",
        "items",
        "LZ91/a;",
        "lockBalanceSelectorListener",
        "",
        "virtual",
        "<init>",
        "(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;LZ91/a;Z)V",
        "",
        "position",
        "Landroidx/fragment/app/Fragment;",
        "p",
        "(I)Landroidx/fragment/app/Fragment;",
        "o",
        "LZ91/a;",
        "Z",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final o:LZ91/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Z


# direct methods
.method public constructor <init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;LZ91/a;Z)V
    .locals 0
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/lifecycle/Lifecycle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LZ91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/fragment/app/FragmentManager;",
            "Landroidx/lifecycle/Lifecycle;",
            "Ljava/util/List<",
            "+",
            "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;",
            ">;",
            "LZ91/a;",
            "Z)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2, p3}, LkY0/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->o:LZ91/a;

    .line 5
    .line 6
    iput-boolean p5, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->p:Z

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public p(I)Landroidx/fragment/app/Fragment;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_1

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-ne p1, v0, :cond_0

    .line 5
    .line 6
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->H1:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;

    .line 7
    .line 8
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->VIEWED:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->o:LZ91/a;

    .line 11
    .line 12
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->p:Z

    .line 13
    .line 14
    invoke-virtual {p1, v0, v1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;->a(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;LZ91/a;Z)Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    return-object p1

    .line 19
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    new-instance v1, Ljava/lang/StringBuilder;

    .line 22
    .line 23
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 24
    .line 25
    .line 26
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 27
    .line 28
    .line 29
    const-string p1, " not supported"

    .line 30
    .line 31
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    throw v0

    .line 46
    :cond_1
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->H1:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;

    .line 47
    .line 48
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;->FAVORITES:Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;

    .line 49
    .line 50
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->o:LZ91/a;

    .line 51
    .line 52
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;->p:Z

    .line 53
    .line 54
    invoke-virtual {p1, v0, v1, v2}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment$a;->a(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteScreenType;LZ91/a;Z)Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    return-object p1
.end method
