.class final Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.analytics.data.repositories.SysLogRepositoryImpl$logRequest$1"
    f = "SysLogRepositoryImpl.kt"
    l = {
        0x139
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->d(Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0006\n\u0000\n\u0002\u0010\u0002\u0010\u0000\u001a\u00020\u0001H\n"
    }
    d2 = {
        "<anonymous>",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $requestError:Ljava/lang/String;

.field final synthetic $requestHeaders:Ljava/lang/String;

.field final synthetic $requestUrl:Ljava/lang/String;

.field final synthetic $responseCode:I

.field final synthetic $responseTime:J

.field label:I

.field final synthetic this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;",
            "Ljava/lang/String;",
            "IJ",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestUrl:Ljava/lang/String;

    iput p3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseCode:I

    iput-wide p4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseTime:J

    iput-object p6, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestError:Ljava/lang/String;

    iput-object p7, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestHeaders:Ljava/lang/String;

    const/4 p1, 0x1

    invoke-direct {p0, p1, p8}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;

    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestUrl:Ljava/lang/String;

    iget v3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseCode:I

    iget-wide v4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseTime:J

    iget-object v6, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestError:Ljava/lang/String;

    iget-object v7, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestHeaders:Ljava/lang/String;

    move-object v8, p1

    invoke-direct/range {v0 .. v8}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->create(Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 28
    .line 29
    const-string v1, "request"

    .line 30
    .line 31
    invoke-static {p1, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->t(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)Lcom/google/gson/JsonObject;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestUrl:Ljava/lang/String;

    .line 36
    .line 37
    iget v3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseCode:I

    .line 38
    .line 39
    iget-wide v4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$responseTime:J

    .line 40
    .line 41
    iget-object v6, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestError:Ljava/lang/String;

    .line 42
    .line 43
    iget-object v7, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->$requestHeaders:Ljava/lang/String;

    .line 44
    .line 45
    const-string v8, "requestUrl"

    .line 46
    .line 47
    invoke-virtual {p1, v8, v1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    const-string v1, "response"

    .line 51
    .line 52
    invoke-static {v3}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    invoke-virtual {p1, v1, v3}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 57
    .line 58
    .line 59
    const-string v1, "responseTime"

    .line 60
    .line 61
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 62
    .line 63
    .line 64
    move-result-object v3

    .line 65
    invoke-virtual {p1, v1, v3}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 66
    .line 67
    .line 68
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 69
    .line 70
    .line 71
    move-result v1

    .line 72
    if-lez v1, :cond_2

    .line 73
    .line 74
    new-instance v1, Ljava/lang/StringBuilder;

    .line 75
    .line 76
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    const-string v3, " | "

    .line 83
    .line 84
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 85
    .line 86
    .line 87
    invoke-virtual {v1, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 88
    .line 89
    .line 90
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    const-string v3, "requestError"

    .line 95
    .line 96
    invoke-virtual {p1, v3, v1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 97
    .line 98
    .line 99
    :cond_2
    invoke-virtual {p1}, Lcom/google/gson/JsonElement;->toString()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->this$0:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;

    .line 104
    .line 105
    sget-object v3, Lokhttp3/z;->Companion:Lokhttp3/z$a;

    .line 106
    .line 107
    sget-object v4, Lokhttp3/v;->e:Lokhttp3/v$a;

    .line 108
    .line 109
    const-string v5, "application/json; charset=utf-8"

    .line 110
    .line 111
    invoke-virtual {v4, v5}, Lokhttp3/v$a;->b(Ljava/lang/String;)Lokhttp3/v;

    .line 112
    .line 113
    .line 114
    move-result-object v4

    .line 115
    invoke-virtual {v3, p1, v4}, Lokhttp3/z$a;->b(Ljava/lang/String;Lokhttp3/v;)Lokhttp3/z;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iput v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;->label:I

    .line 120
    .line 121
    invoke-static {v1, p1, p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->w(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    if-ne p1, v0, :cond_3

    .line 126
    .line 127
    return-object v0

    .line 128
    :cond_3
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 129
    .line 130
    return-object p1
.end method
