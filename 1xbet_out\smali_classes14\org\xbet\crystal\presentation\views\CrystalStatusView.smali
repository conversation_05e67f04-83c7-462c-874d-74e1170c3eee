.class public final Lorg/xbet/crystal/presentation/views/CrystalStatusView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\r\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001b\u0010\u000e\u001a\u00020\u00082\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0015\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0013R\u001b\u0010\u001b\u001a\u00020\u00168BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0018\u001a\u0004\u0008\u0019\u0010\u001aR\u001b\u0010 \u001a\u00020\u001c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u0018\u001a\u0004\u0008\u001e\u0010\u001fR\"\u0010\'\u001a\u00020!8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u000e\u0010\"\u001a\u0004\u0008#\u0010$\"\u0004\u0008%\u0010&R\u0016\u0010*\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\t\u0010)\u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/crystal/presentation/views/CrystalStatusView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "d",
        "()V",
        "",
        "LZx/e;",
        "winCombos",
        "c",
        "(Ljava/util/List;)V",
        "",
        "winSum",
        "setFinalSum",
        "(D)V",
        "sum",
        "e",
        "LXx/b;",
        "a",
        "Lkotlin/j;",
        "getViewBinding",
        "()LXx/b;",
        "viewBinding",
        "Lcy/b;",
        "b",
        "getAdapter",
        "()Lcy/b;",
        "adapter",
        "",
        "Ljava/lang/String;",
        "getCurrencySymbol",
        "()Ljava/lang/String;",
        "setCurrencySymbol",
        "(Ljava/lang/String;)V",
        "currencySymbol",
        "Ljava/math/BigDecimal;",
        "Ljava/math/BigDecimal;",
        "currentSum",
        "crystal_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/math/BigDecimal;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/util/AttributeSet;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    sget-object p2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 5
    .line 6
    new-instance v0, Lorg/xbet/crystal/presentation/views/CrystalStatusView$a;

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    invoke-direct {v0, p0, p0, v1}, Lorg/xbet/crystal/presentation/views/CrystalStatusView$a;-><init>(Landroid/view/ViewGroup;Landroid/view/ViewGroup;Z)V

    .line 10
    .line 11
    .line 12
    invoke-static {p2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    iput-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->a:Lkotlin/j;

    .line 17
    .line 18
    new-instance p2, Ldy/i;

    .line 19
    .line 20
    invoke-direct {p2}, Ldy/i;-><init>()V

    .line 21
    .line 22
    .line 23
    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    iput-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->b:Lkotlin/j;

    .line 28
    .line 29
    const-string p2, ""

    .line 30
    .line 31
    iput-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c:Ljava/lang/String;

    .line 32
    .line 33
    new-instance p2, Ljava/math/BigDecimal;

    .line 34
    .line 35
    const-wide/16 v2, 0x0

    .line 36
    .line 37
    invoke-direct {p2, v2, v3}, Ljava/math/BigDecimal;-><init>(D)V

    .line 38
    .line 39
    .line 40
    iput-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 41
    .line 42
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    iget-object p2, p2, LXx/b;->d:Landroidx/recyclerview/widget/RecyclerView;

    .line 47
    .line 48
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getAdapter()Lcy/b;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    invoke-virtual {p2, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 53
    .line 54
    .line 55
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    iget-object p2, p2, LXx/b;->d:Landroidx/recyclerview/widget/RecyclerView;

    .line 60
    .line 61
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 62
    .line 63
    invoke-direct {v0, p1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;->setOrientation(I)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p2, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 70
    .line 71
    .line 72
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iget-object p1, p1, LXx/b;->d:Landroidx/recyclerview/widget/RecyclerView;

    .line 77
    .line 78
    new-instance p2, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 79
    .line 80
    const/4 v0, 0x2

    .line 81
    const/4 v2, 0x0

    .line 82
    invoke-direct {p2, v0, v2, v1}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(III)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p1, p2}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 86
    .line 87
    .line 88
    return-void
.end method

.method public static synthetic a()Lcy/b;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->b()Lcy/b;

    move-result-object v0

    return-object v0
.end method

.method public static final b()Lcy/b;
    .locals 1

    .line 1
    new-instance v0, Lcy/b;

    .line 2
    .line 3
    invoke-direct {v0}, Lcy/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final getAdapter()Lcy/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcy/b;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getViewBinding()LXx/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LXx/b;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final c(Ljava/util/List;)V
    .locals 6
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LZx/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    if-eqz v2, :cond_0

    .line 21
    .line 22
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    check-cast v2, LZx/e;

    .line 27
    .line 28
    new-instance v3, Ljava/math/BigDecimal;

    .line 29
    .line 30
    invoke-virtual {v2}, LZx/e;->d()D

    .line 31
    .line 32
    .line 33
    move-result-wide v4

    .line 34
    invoke-static {v4, v5}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-direct {v3, v2}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    invoke-interface {v0, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 50
    .line 51
    .line 52
    move-result v1

    .line 53
    if-eqz v1, :cond_1

    .line 54
    .line 55
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    check-cast v1, Ljava/math/BigDecimal;

    .line 60
    .line 61
    iget-object v2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 62
    .line 63
    invoke-virtual {v2, v1}, Ljava/math/BigDecimal;->add(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    iput-object v1, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 68
    .line 69
    goto :goto_1

    .line 70
    :cond_1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 71
    .line 72
    new-instance v1, Ljava/math/BigDecimal;

    .line 73
    .line 74
    const-wide/16 v2, 0x0

    .line 75
    .line 76
    invoke-direct {v1, v2, v3}, Ljava/math/BigDecimal;-><init>(D)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v0, v1}, Ljava/math/BigDecimal;->compareTo(Ljava/math/BigDecimal;)I

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    if-lez v0, :cond_2

    .line 84
    .line 85
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 86
    .line 87
    invoke-virtual {v0}, Ljava/math/BigDecimal;->doubleValue()D

    .line 88
    .line 89
    .line 90
    move-result-wide v0

    .line 91
    invoke-virtual {p0, v0, v1}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->e(D)V

    .line 92
    .line 93
    .line 94
    :cond_2
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    :goto_2
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 99
    .line 100
    .line 101
    move-result v0

    .line 102
    if-eqz v0, :cond_3

    .line 103
    .line 104
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    check-cast v0, LZx/e;

    .line 109
    .line 110
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getAdapter()Lcy/b;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    invoke-virtual {v1, v0}, LUX0/h;->q(Ljava/lang/Object;)V

    .line 115
    .line 116
    .line 117
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    iget-object v0, v0, LXx/b;->d:Landroidx/recyclerview/widget/RecyclerView;

    .line 122
    .line 123
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getAdapter()Lcy/b;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    invoke-virtual {v1}, LUX0/h;->getItemCount()I

    .line 128
    .line 129
    .line 130
    move-result v1

    .line 131
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->smoothScrollToPosition(I)V

    .line 132
    .line 133
    .line 134
    goto :goto_2

    .line 135
    :cond_3
    return-void
.end method

.method public final d()V
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getAdapter()Lcy/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LUX0/h;->u()V

    .line 6
    .line 7
    .line 8
    new-instance v0, Ljava/math/BigDecimal;

    .line 9
    .line 10
    const-wide/16 v1, 0x0

    .line 11
    .line 12
    invoke-direct {v0, v1, v2}, Ljava/math/BigDecimal;-><init>(D)V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->d:Ljava/math/BigDecimal;

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget-object v0, v0, LXx/b;->c:Landroid/widget/TextView;

    .line 22
    .line 23
    const-string v1, ""

    .line 24
    .line 25
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final e(D)V
    .locals 5

    .line 1
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LXx/b;->c:Landroid/widget/TextView;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    sget v2, Lpb/k;->current_win_two_lines:I

    .line 12
    .line 13
    sget-object v3, Ll8/j;->a:Ll8/j;

    .line 14
    .line 15
    sget-object v4, Lcom/xbet/onexcore/utils/ValueType;->LIMIT:Lcom/xbet/onexcore/utils/ValueType;

    .line 16
    .line 17
    invoke-virtual {v3, p1, p2, v4}, Ll8/j;->d(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    iget-object p2, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c:Ljava/lang/String;

    .line 22
    .line 23
    const/4 v3, 0x2

    .line 24
    new-array v3, v3, [Ljava/lang/Object;

    .line 25
    .line 26
    const/4 v4, 0x0

    .line 27
    aput-object p1, v3, v4

    .line 28
    .line 29
    const/4 p1, 0x1

    .line 30
    aput-object p2, v3, p1

    .line 31
    .line 32
    invoke-virtual {v1, v2, v3}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final getCurrencySymbol()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final setCurrencySymbol(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method public final setFinalSum(D)V
    .locals 3

    .line 1
    const-wide/16 v0, 0x0

    .line 2
    .line 3
    cmpg-double v2, p1, v0

    .line 4
    .line 5
    if-nez v2, :cond_0

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->getViewBinding()LXx/b;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iget-object p1, p1, LXx/b;->c:Landroid/widget/TextView;

    .line 12
    .line 13
    const-string p2, ""

    .line 14
    .line 15
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/views/CrystalStatusView;->e(D)V

    .line 20
    .line 21
    .line 22
    return-void
.end method
