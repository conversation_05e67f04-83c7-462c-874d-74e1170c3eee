.class public final Lorg/xbet/uikit_sport/eventcard/bottom/p;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u001e\n\u0002\u0010 \n\u0002\u0008\u0005\n\u0002\u0010%\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0010!\n\u0002\u0008\u001b\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001d\u0010\n\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001f\u0010\u000c\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u000c\u0010\u000bJ\u0015\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0015\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0019\u0010\u0018\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u00170\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u001d\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\u00172\u0006\u0010\u001a\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0019\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\u001d\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010 \u001a\u0004\u0018\u00010\r2\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008 \u0010\u000fJ\u0017\u0010!\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008!\u0010\u0012J\u0017\u0010\"\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0008\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\"\u0010\u0015J\r\u0010$\u001a\u00020#\u00a2\u0006\u0004\u0008$\u0010%JA\u0010*\u001a\u00020\'2\u0018\u0010(\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\'0&2\u0018\u0010)\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\'0&\u00a2\u0006\u0004\u0008*\u0010+J\u001b\u0010.\u001a\u00020\'2\u000c\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\'0,\u00a2\u0006\u0004\u0008.\u0010/J-\u00103\u001a\u00020\'2\u001e\u0010-\u001a\u001a\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u000202\u0012\u0004\u0012\u00020\'00\u00a2\u0006\u0004\u00083\u00104J\u001f\u00105\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00085\u0010\u000bJ\u0017\u00107\u001a\u00020\r2\u0006\u00106\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00087\u0010\u000fJ\u0017\u00108\u001a\u00020\u00102\u0006\u00106\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00088\u0010\u0012J-\u0010;\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u000c\u0010:\u001a\u0008\u0012\u0004\u0012\u00020\t09H\u0002\u00a2\u0006\u0004\u0008;\u0010<J\u0017\u0010=\u001a\u00020\u00132\u0006\u0010\u0008\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008=\u0010\u0015R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010AR \u0010D\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR \u0010F\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00100\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010CR&\u0010H\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t090\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010CR \u0010I\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00130\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u0010CR\u0018\u0010L\u001a\u0004\u0018\u00010#8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR*\u0010(\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\'\u0018\u00010&8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00085\u0010MR*\u0010)\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\'\u0018\u00010&8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008;\u0010MR\u001e\u0010P\u001a\n\u0012\u0004\u0012\u00020\'\u0018\u00010,8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR0\u0010S\u001a\u001c\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u000202\u0012\u0004\u0012\u00020\'\u0018\u0001008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Q\u0010R\u00a8\u0006T"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/p;",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;",
        "parent",
        "<init>",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;)V",
        "",
        "groupIndex",
        "viewIndex",
        "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
        "o",
        "(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
        "u",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;",
        "p",
        "(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;",
        "n",
        "(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;",
        "Lorg/xbet/uikit/components/market/view/MarketBlocked;",
        "m",
        "(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;",
        "",
        "",
        "t",
        "()Ljava/util/Collection;",
        "index",
        "s",
        "(I)Ljava/util/List;",
        "",
        "y",
        "()Ljava/util/Map;",
        "x",
        "r",
        "q",
        "Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;",
        "v",
        "()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;",
        "Lkotlin/Function2;",
        "",
        "clickListener",
        "longClickListener",
        "A",
        "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V",
        "Lkotlin/Function0;",
        "listener",
        "B",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lkotlin/Function3;",
        "Landroid/view/View;",
        "",
        "z",
        "(LOc/n;)V",
        "g",
        "viewGroupIndex",
        "l",
        "e",
        "",
        "cachedGroup",
        "h",
        "(IILjava/util/List;)Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
        "k",
        "a",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;",
        "getParent",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;",
        "b",
        "Ljava/util/Map;",
        "cachedTitleHeaders",
        "c",
        "cachedExpandableHeaders",
        "d",
        "cachedMarketMap",
        "cacheBlockedMarketMap",
        "f",
        "Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;",
        "cacheShowMore",
        "Lkotlin/jvm/functions/Function2;",
        "i",
        "Lkotlin/jvm/functions/Function0;",
        "showMoreListener",
        "j",
        "LOc/n;",
        "headerExpandableListener",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/util/List<",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/xbet/uikit/components/market/view/MarketBlocked;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

.field public g:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public h:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public i:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public j:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "-",
            "Landroid/view/View;",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 5
    .line 6
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 7
    .line 8
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 9
    .line 10
    .line 11
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->b:Ljava/util/Map;

    .line 12
    .line 13
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 14
    .line 15
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 16
    .line 17
    .line 18
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->c:Ljava/util/Map;

    .line 19
    .line 20
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 21
    .line 22
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 23
    .line 24
    .line 25
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 26
    .line 27
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 28
    .line 29
    invoke-direct {p1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 30
    .line 31
    .line 32
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->e:Ljava/util/Map;

    .line 33
    .line 34
    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->j(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Z

    move-result p0

    return p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->i(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;ILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->f(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;ILandroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/eventcard/bottom/p;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->w(Lorg/xbet/uikit_sport/eventcard/bottom/p;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;ILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->j:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->b()Z

    .line 10
    .line 11
    .line 12
    move-result p3

    .line 13
    invoke-static {p3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    invoke-interface {p0, p1, p2, p3}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final i(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->g:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-interface {p0, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final j(Lorg/xbet/uikit_sport/eventcard/bottom/p;IILandroid/view/View;)Z
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->h:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-interface {p0, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    :cond_0
    const/4 p0, 0x1

    .line 17
    return p0
.end method

.method public static final w(Lorg/xbet/uikit_sport/eventcard/bottom/p;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->i:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final A(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->g:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->h:Lkotlin/jvm/functions/Function2;

    .line 4
    .line 5
    return-void
.end method

.method public final B(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->i:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public final e(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->c:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 18
    .line 19
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    const/4 v2, 0x2

    .line 24
    const/4 v3, 0x0

    .line 25
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->c:Ljava/util/Map;

    .line 33
    .line 34
    invoke-interface {v2, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    sget-object v1, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 38
    .line 39
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/bottom/m;

    .line 40
    .line 41
    invoke-direct {v2, p0, v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/m;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;I)V

    .line 42
    .line 43
    .line 44
    invoke-static {v0, v1, v2}, LN11/f;->m(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 45
    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 48
    .line 49
    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 50
    .line 51
    .line 52
    :cond_0
    return-object v0
.end method

.method public final g(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Ljava/util/List;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Ljava/util/ArrayList;

    .line 16
    .line 17
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 18
    .line 19
    .line 20
    :cond_0
    invoke-virtual {p0, p1, p2, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->h(IILjava/util/List;)Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 29
    .line 30
    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    return-object p2
.end method

.method public final h(IILjava/util/List;)Lorg/xbet/uikit/components/market/view/MarketCoefficient;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
            ">;)",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;"
        }
    .end annotation

    .line 1
    invoke-static {p3, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 6
    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    new-instance v1, Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 12
    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    const/4 v5, 0x6

    .line 18
    const/4 v6, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 22
    .line 23
    .line 24
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    invoke-virtual {v1, v0}, Landroid/view/View;->setId(I)V

    .line 29
    .line 30
    .line 31
    const-string v0, "marketCoefficient"

    .line 32
    .line 33
    invoke-virtual {v1, v0}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    const/16 v0, 0x8

    .line 37
    .line 38
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    sget-object v0, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 42
    .line 43
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/bottom/n;

    .line 44
    .line 45
    invoke-direct {v2, p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/n;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;II)V

    .line 46
    .line 47
    .line 48
    invoke-static {v1, v0, v2}, LN11/f;->m(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 49
    .line 50
    .line 51
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/bottom/o;

    .line 52
    .line 53
    invoke-direct {v2, p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/o;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;II)V

    .line 54
    .line 55
    .line 56
    invoke-static {v1, v0, v2}, LN11/h;->a(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnLongClickListener;

    .line 57
    .line 58
    .line 59
    invoke-interface {p3, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 63
    .line 64
    invoke-virtual {p1, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 65
    .line 66
    .line 67
    return-object v1

    .line 68
    :cond_0
    return-object v0
.end method

.method public final k(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->e:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v1, Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 18
    .line 19
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    const/4 v5, 0x6

    .line 24
    const/4 v6, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/market/view/MarketBlocked;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->e:Ljava/util/Map;

    .line 35
    .line 36
    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 40
    .line 41
    invoke-virtual {p1, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 42
    .line 43
    .line 44
    return-object v1

    .line 45
    :cond_0
    return-object v0
.end method

.method public final l(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->b:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 18
    .line 19
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    const/4 v2, 0x2

    .line 24
    const/4 v3, 0x0

    .line 25
    invoke-direct {v0, v1, v3, v2, v3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->b:Ljava/util/Map;

    .line 33
    .line 34
    invoke-interface {v1, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 38
    .line 39
    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    return-object v0
.end method

.method public final m(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->k(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public final n(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->e(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public final o(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->g(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public final p(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->l(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public final q(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->e:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 12
    .line 13
    return-object p1
.end method

.method public final r(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->c:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 12
    .line 13
    return-object p1
.end method

.method public final s(I)Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Iterable;

    .line 8
    .line 9
    invoke-static {v0, p1}, Lkotlin/collections/CollectionsKt;->s0(Ljava/lang/Iterable;I)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Ljava/util/List;

    .line 14
    .line 15
    return-object p1
.end method

.method public final t()Ljava/util/Collection;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Collection<",
            "Ljava/util/List<",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final u(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->d:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Ljava/util/List;

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    invoke-static {p1, p2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    check-cast p1, Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 20
    .line 21
    return-object p1

    .line 22
    :cond_0
    const/4 p1, 0x0

    .line 23
    return-object p1
.end method

.method public final v()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->f:Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    new-instance v1, Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    const/4 v5, 0x6

    .line 14
    const/4 v6, 0x0

    .line 15
    const/4 v3, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    const/16 v0, 0x8

    .line 21
    .line 22
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    sget-object v0, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 26
    .line 27
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/bottom/l;

    .line 28
    .line 29
    invoke-direct {v2, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/l;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;)V

    .line 30
    .line 31
    .line 32
    invoke-static {v1, v0, v2}, LN11/f;->m(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;

    .line 36
    .line 37
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 38
    .line 39
    .line 40
    iput-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->f:Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 41
    .line 42
    return-object v1

    .line 43
    :cond_0
    return-object v0
.end method

.method public final x(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->b:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 12
    .line 13
    return-object p1
.end method

.method public final y()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->b:Ljava/util/Map;

    .line 2
    .line 3
    return-object v0
.end method

.method public final z(LOc/n;)V
    .locals 0
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Landroid/view/View;",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/p;->j:LOc/n;

    .line 2
    .line 3
    return-void
.end method
