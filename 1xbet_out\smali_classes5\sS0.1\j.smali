.class public final LsS0/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LsS0/i;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Landroid/content/Context;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;",
            "LBc/a<",
            "Landroid/content/Context;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LsS0/j;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LsS0/j;->b:LBc/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(LBc/a;LBc/a;)LsS0/j;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;",
            "LBc/a<",
            "Landroid/content/Context;",
            ">;)",
            "LsS0/j;"
        }
    .end annotation

    .line 1
    new-instance v0, LsS0/j;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LsS0/j;-><init>(LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lcom/google/gson/Gson;Landroid/content/Context;)LsS0/i;
    .locals 1

    .line 1
    new-instance v0, LsS0/i;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LsS0/i;-><init>(Lcom/google/gson/Gson;Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LsS0/i;
    .locals 2

    .line 1
    iget-object v0, p0, LsS0/j;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/gson/Gson;

    .line 8
    .line 9
    iget-object v1, p0, LsS0/j;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Landroid/content/Context;

    .line 16
    .line 17
    invoke-static {v0, v1}, LsS0/j;->c(Lcom/google/gson/Gson;Landroid/content/Context;)LsS0/i;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LsS0/j;->b()LsS0/i;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
