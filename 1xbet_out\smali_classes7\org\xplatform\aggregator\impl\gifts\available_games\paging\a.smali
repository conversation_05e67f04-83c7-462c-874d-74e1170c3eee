.class public final synthetic Lorg/xplatform/aggregator/impl/gifts/available_games/paging/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lf8/g;


# direct methods
.method public synthetic constructor <init>(Lf8/g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/a;->a:Lf8/g;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/a;->a:Lf8/g;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/paging/AggregatorPromoGamesPagingSource;->m(Lf8/g;)Lva1/a;

    move-result-object v0

    return-object v0
.end method
