.class public final Lorg/xbet/coupon/impl/notify/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;LNb0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;->h:LNb0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;LHX0/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;->j:LHX0/e;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;Ltw/n;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/coupon/impl/notify/CouponNotificationWorker;->i:Ltw/n;

    .line 2
    .line 3
    return-void
.end method
