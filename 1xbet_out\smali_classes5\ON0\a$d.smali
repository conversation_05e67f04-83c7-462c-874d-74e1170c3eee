.class public final LON0/a$d;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LON0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0008\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J%\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\r\u0010\u000e\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LON0/a$d;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LDN0/z;",
        "binding",
        "<init>",
        "(LDN0/z;)V",
        "LaZ0/f;",
        "item",
        "",
        "width",
        "textGravity",
        "",
        "d",
        "(LaZ0/f;II)V",
        "e",
        "()V",
        "LDN0/z;",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LDN0/z;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LDN0/z;)V
    .locals 1
    .param p1    # LDN0/z;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LDN0/z;->b()Landroid/widget/LinearLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LON0/a$d;->e:LDN0/z;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final d(LaZ0/f;II)V
    .locals 10
    .param p1    # LaZ0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LaZ0/f;->b()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 12
    .line 13
    iget-object v0, v0, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 14
    .line 15
    const/16 v1, 0x8

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-virtual {p1}, LaZ0/f;->b()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    const-string v1, "empty_country"

    .line 26
    .line 27
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    const/4 v1, 0x0

    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 35
    .line 36
    iget-object v0, v0, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 39
    .line 40
    .line 41
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 42
    .line 43
    iget-object v0, v0, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 44
    .line 45
    sget v1, Lpb/g;->ic_no_country:I

    .line 46
    .line 47
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 48
    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_1
    invoke-virtual {p1}, LaZ0/f;->b()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    if-lez v0, :cond_2

    .line 60
    .line 61
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 62
    .line 63
    iget-object v0, v0, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 64
    .line 65
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 66
    .line 67
    .line 68
    sget-object v2, LCX0/l;->a:LCX0/l;

    .line 69
    .line 70
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 71
    .line 72
    iget-object v3, v0, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 73
    .line 74
    invoke-virtual {p1}, LaZ0/f;->b()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v6

    .line 78
    const/16 v8, 0xb

    .line 79
    .line 80
    const/4 v9, 0x0

    .line 81
    const/4 v4, 0x0

    .line 82
    const/4 v5, 0x0

    .line 83
    const/4 v7, 0x0

    .line 84
    invoke-static/range {v2 .. v9}, LCX0/l;->F(LCX0/l;Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;IILjava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    :cond_2
    :goto_0
    iget-object v0, p0, LON0/a$d;->e:LDN0/z;

    .line 88
    .line 89
    iget-object v0, v0, LDN0/z;->c:Landroid/widget/TextView;

    .line 90
    .line 91
    invoke-virtual {p1}, LaZ0/f;->c()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 96
    .line 97
    .line 98
    iget-object p1, p0, LON0/a$d;->e:LDN0/z;

    .line 99
    .line 100
    iget-object p1, p1, LDN0/z;->c:Landroid/widget/TextView;

    .line 101
    .line 102
    invoke-virtual {p1, p3}, Landroid/widget/TextView;->setGravity(I)V

    .line 103
    .line 104
    .line 105
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 106
    .line 107
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    iget p1, p1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 112
    .line 113
    if-eq p1, p2, :cond_4

    .line 114
    .line 115
    iget-object p1, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 116
    .line 117
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 118
    .line 119
    .line 120
    move-result-object p3

    .line 121
    if-eqz p3, :cond_3

    .line 122
    .line 123
    iput p2, p3, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 124
    .line 125
    invoke-virtual {p1, p3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 126
    .line 127
    .line 128
    return-void

    .line 129
    :cond_3
    new-instance p1, Ljava/lang/NullPointerException;

    .line 130
    .line 131
    const-string p2, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 132
    .line 133
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 134
    .line 135
    .line 136
    throw p1

    .line 137
    :cond_4
    return-void
.end method

.method public final e()V
    .locals 2

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    iget-object v1, p0, LON0/a$d;->e:LDN0/z;

    .line 4
    .line 5
    iget-object v1, v1, LDN0/z;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, LCX0/l;->j(Landroid/widget/ImageView;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
