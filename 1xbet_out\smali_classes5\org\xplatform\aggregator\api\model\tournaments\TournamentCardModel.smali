.class public final Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$a;,
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$CounterType;,
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;,
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;,
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;,
        Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008(\u0008\u0086\u0008\u0018\u00002\u00020\u0001:\u000604,(\u001aFBg\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0010\u001a\u00020\u000e\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u0012\u0006\u0010\u0015\u001a\u00020\u0011\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0088\u0001\u0010\u001a\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\t\u001a\u00020\u00082\u0008\u0008\u0002\u0010\u000b\u001a\u00020\n2\u0008\u0008\u0002\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u0010\u001a\u00020\u000e2\u0008\u0008\u0002\u0010\u0012\u001a\u00020\u00112\u0008\u0008\u0002\u0010\u0014\u001a\u00020\u00132\u0008\u0008\u0002\u0010\u0015\u001a\u00020\u00112\u0008\u0008\u0002\u0010\u0017\u001a\u00020\u0016H\u00c6\u0001\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0010\u0010\u001d\u001a\u00020\u001cH\u00d6\u0001\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0010\u0010 \u001a\u00020\u001fH\u00d6\u0001\u00a2\u0006\u0004\u0008 \u0010!J\u001a\u0010#\u001a\u00020\u000e2\u0008\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008#\u0010$R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010%\u001a\u0004\u0008&\u0010\'R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R\u0017\u0010\t\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u00080\u00101\u001a\u0004\u00082\u00103R\u0017\u0010\u000b\u001a\u00020\n8\u0006\u00a2\u0006\u000c\n\u0004\u00084\u00105\u001a\u0004\u00080\u00106R\u0017\u0010\r\u001a\u00020\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u00087\u00108\u001a\u0004\u0008,\u00109R\u0017\u0010\u000f\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u00082\u0010:\u001a\u0004\u0008;\u0010<R\u0017\u0010\u0010\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008&\u0010:\u001a\u0004\u0008=\u0010<R\u0017\u0010\u0012\u001a\u00020\u00118\u0006\u00a2\u0006\u000c\n\u0004\u0008*\u0010>\u001a\u0004\u0008?\u0010@R\u0017\u0010\u0014\u001a\u00020\u00138\u0006\u00a2\u0006\u000c\n\u0004\u0008A\u0010B\u001a\u0004\u00087\u0010CR\u0017\u0010\u0015\u001a\u00020\u00118\u0006\u00a2\u0006\u000c\n\u0004\u0008=\u0010>\u001a\u0004\u0008A\u0010@R\u0017\u0010\u0017\u001a\u00020\u00168\u0006\u00a2\u0006\u000c\n\u0004\u0008.\u0010D\u001a\u0004\u00084\u0010E\u00a8\u0006G"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;",
        "",
        "",
        "id",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;",
        "type",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;",
        "chipStatus",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;",
        "blockImage",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "blockHeader",
        "",
        "meParticipating",
        "providerTournamentWithStages",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "userActionButton",
        "Lh81/a;",
        "buttons",
        "moreButton",
        "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "buttonStatus",
        "<init>",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)V",
        "a",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "J",
        "h",
        "()J",
        "b",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "i",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "c",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;",
        "l",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;",
        "d",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;",
        "g",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;",
        "e",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;",
        "f",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "Z",
        "getMeParticipating",
        "()Z",
        "k",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "m",
        "()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "j",
        "Lh81/a;",
        "()Lh81/a;",
        "Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;",
        "CounterType",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:J

.field public final b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Z

.field public final h:Z

.field public final i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lh81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)V
    .locals 0
    .param p3    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lh81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 7
    .line 8
    iput-object p4, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    .line 9
    .line 10
    iput-object p5, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 15
    .line 16
    iput-boolean p8, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    .line 17
    .line 18
    iput-boolean p9, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    .line 19
    .line 20
    iput-object p10, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 21
    .line 22
    iput-object p11, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 27
    .line 28
    return-void
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;ILjava/lang/Object;)Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
    .locals 13

    .line 1
    move/from16 v0, p14

    .line 2
    .line 3
    and-int/lit8 v1, v0, 0x1

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    iget-wide v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    move-wide v1, p1

    .line 11
    :goto_0
    and-int/lit8 v3, v0, 0x2

    .line 12
    .line 13
    if-eqz v3, :cond_1

    .line 14
    .line 15
    iget-object v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 16
    .line 17
    goto :goto_1

    .line 18
    :cond_1
    move-object/from16 v3, p3

    .line 19
    .line 20
    :goto_1
    and-int/lit8 v4, v0, 0x4

    .line 21
    .line 22
    if-eqz v4, :cond_2

    .line 23
    .line 24
    iget-object v4, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    .line 25
    .line 26
    goto :goto_2

    .line 27
    :cond_2
    move-object/from16 v4, p4

    .line 28
    .line 29
    :goto_2
    and-int/lit8 v5, v0, 0x8

    .line 30
    .line 31
    if-eqz v5, :cond_3

    .line 32
    .line 33
    iget-object v5, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    .line 34
    .line 35
    goto :goto_3

    .line 36
    :cond_3
    move-object/from16 v5, p5

    .line 37
    .line 38
    :goto_3
    and-int/lit8 v6, v0, 0x10

    .line 39
    .line 40
    if-eqz v6, :cond_4

    .line 41
    .line 42
    iget-object v6, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    .line 43
    .line 44
    goto :goto_4

    .line 45
    :cond_4
    move-object/from16 v6, p6

    .line 46
    .line 47
    :goto_4
    and-int/lit8 v7, v0, 0x20

    .line 48
    .line 49
    if-eqz v7, :cond_5

    .line 50
    .line 51
    iget-object v7, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 52
    .line 53
    goto :goto_5

    .line 54
    :cond_5
    move-object/from16 v7, p7

    .line 55
    .line 56
    :goto_5
    and-int/lit8 v8, v0, 0x40

    .line 57
    .line 58
    if-eqz v8, :cond_6

    .line 59
    .line 60
    iget-boolean v8, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    .line 61
    .line 62
    goto :goto_6

    .line 63
    :cond_6
    move/from16 v8, p8

    .line 64
    .line 65
    :goto_6
    and-int/lit16 v9, v0, 0x80

    .line 66
    .line 67
    if-eqz v9, :cond_7

    .line 68
    .line 69
    iget-boolean v9, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    .line 70
    .line 71
    goto :goto_7

    .line 72
    :cond_7
    move/from16 v9, p9

    .line 73
    .line 74
    :goto_7
    and-int/lit16 v10, v0, 0x100

    .line 75
    .line 76
    if-eqz v10, :cond_8

    .line 77
    .line 78
    iget-object v10, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 79
    .line 80
    goto :goto_8

    .line 81
    :cond_8
    move-object/from16 v10, p10

    .line 82
    .line 83
    :goto_8
    and-int/lit16 v11, v0, 0x200

    .line 84
    .line 85
    if-eqz v11, :cond_9

    .line 86
    .line 87
    iget-object v11, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    .line 88
    .line 89
    goto :goto_9

    .line 90
    :cond_9
    move-object/from16 v11, p11

    .line 91
    .line 92
    :goto_9
    and-int/lit16 v12, v0, 0x400

    .line 93
    .line 94
    if-eqz v12, :cond_a

    .line 95
    .line 96
    iget-object v12, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 97
    .line 98
    goto :goto_a

    .line 99
    :cond_a
    move-object/from16 v12, p12

    .line 100
    .line 101
    :goto_a
    and-int/lit16 v0, v0, 0x800

    .line 102
    .line 103
    if-eqz v0, :cond_b

    .line 104
    .line 105
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 106
    .line 107
    move-object/from16 p14, v0

    .line 108
    .line 109
    :goto_b
    move-object p1, p0

    .line 110
    move-wide p2, v1

    .line 111
    move-object/from16 p4, v3

    .line 112
    .line 113
    move-object/from16 p5, v4

    .line 114
    .line 115
    move-object/from16 p6, v5

    .line 116
    .line 117
    move-object/from16 p7, v6

    .line 118
    .line 119
    move-object/from16 p8, v7

    .line 120
    .line 121
    move/from16 p9, v8

    .line 122
    .line 123
    move/from16 p10, v9

    .line 124
    .line 125
    move-object/from16 p11, v10

    .line 126
    .line 127
    move-object/from16 p12, v11

    .line 128
    .line 129
    move-object/from16 p13, v12

    .line 130
    .line 131
    goto :goto_c

    .line 132
    :cond_b
    move-object/from16 p14, p13

    .line 133
    .line 134
    goto :goto_b

    .line 135
    :goto_c
    invoke-virtual/range {p1 .. p14}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 136
    .line 137
    .line 138
    move-result-object p0

    .line 139
    return-object p0
.end method


# virtual methods
.method public final a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
    .locals 14
    .param p3    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lh81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 2
    .line 3
    move-wide v1, p1

    .line 4
    move-object/from16 v3, p3

    .line 5
    .line 6
    move-object/from16 v4, p4

    .line 7
    .line 8
    move-object/from16 v5, p5

    .line 9
    .line 10
    move-object/from16 v6, p6

    .line 11
    .line 12
    move-object/from16 v7, p7

    .line 13
    .line 14
    move/from16 v8, p8

    .line 15
    .line 16
    move/from16 v9, p9

    .line 17
    .line 18
    move-object/from16 v10, p10

    .line 19
    .line 20
    move-object/from16 v11, p11

    .line 21
    .line 22
    move-object/from16 v12, p12

    .line 23
    .line 24
    move-object/from16 v13, p13

    .line 25
    .line 26
    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;-><init>(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;ZZLorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)V

    .line 27
    .line 28
    .line 29
    return-object v0
.end method

.method public final c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    iget-wide v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    iget-wide v5, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_6

    return v2

    :cond_6
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_7

    return v2

    :cond_7
    iget-boolean v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    iget-boolean v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    if-eq v1, v3, :cond_8

    return v2

    :cond_8
    iget-boolean v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    iget-boolean v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    if-eq v1, v3, :cond_9

    return v2

    :cond_9
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_a

    return v2

    :cond_a
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_b

    return v2

    :cond_b
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_c

    return v2

    :cond_c
    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    iget-object p1, p1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    if-eq v1, p1, :cond_d

    return v2

    :cond_d
    return v0
.end method

.method public final f()Lh81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public hashCode()I
    .locals 2

    iget-wide v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    invoke-static {v0, v1}, Lu/l;->a(J)I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    invoke-virtual {v1}, Lh81/a;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public final i()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    .line 2
    .line 3
    return v0
.end method

.method public final l()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 15
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-wide v0, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->a:J

    iget-object v2, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v3, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    iget-object v4, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    iget-object v5, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->e:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    iget-object v6, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->f:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    iget-boolean v7, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g:Z

    iget-boolean v8, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h:Z

    iget-object v9, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v10, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->j:Lh81/a;

    iget-object v11, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->k:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v12, p0, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l:Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "TournamentCardModel(id="

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", kind="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", type="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", chipStatus="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", blockImage="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", blockHeader="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", meParticipating="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", providerTournamentWithStages="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ", userActionButton="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", buttons="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", moreButton="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", buttonStatus="

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v13, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
