.class public Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/security/PrivateKey;


# instance fields
.field private final keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

.field private final treeDigest:LSe/m;


# direct methods
.method public constructor <init>(LSe/m;Lorg/spongycastle/pqc/crypto/xmss/r;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 3
    iput-object p2, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    return-void
.end method

.method public constructor <init>(Lcf/d;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5
    invoke-virtual {p1}, Lcf/d;->s()Lkf/a;

    move-result-object v0

    invoke-virtual {v0}, Lkf/a;->t()LSe/e;

    move-result-object v0

    invoke-static {v0}, LIf/j;->r(Ljava/lang/Object;)LIf/j;

    move-result-object v0

    .line 6
    invoke-virtual {v0}, LIf/j;->s()Lkf/a;

    move-result-object v1

    invoke-virtual {v1}, Lkf/a;->o()LSe/m;

    move-result-object v1

    iput-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 7
    invoke-virtual {p1}, Lcf/d;->t()LSe/e;

    move-result-object p1

    invoke-static {p1}, LIf/m;->s(Ljava/lang/Object;)LIf/m;

    move-result-object p1

    .line 8
    :try_start_0
    new-instance v2, Lorg/spongycastle/pqc/crypto/xmss/r$b;

    new-instance v3, Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 9
    invoke-virtual {v0}, LIf/j;->o()I

    move-result v0

    invoke-static {v1}, LUf/a;->a(LSe/m;)Lorg/spongycastle/crypto/e;

    move-result-object v1

    invoke-direct {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/q;-><init>(ILorg/spongycastle/crypto/e;)V

    invoke-direct {v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/r$b;-><init>(Lorg/spongycastle/pqc/crypto/xmss/q;)V

    .line 10
    invoke-virtual {p1}, LIf/m;->r()I

    move-result v0

    invoke-virtual {v2, v0}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->l(I)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    move-result-object v0

    .line 11
    invoke-virtual {p1}, LIf/m;->A()[B

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->p([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    move-result-object v0

    .line 12
    invoke-virtual {p1}, LIf/m;->z()[B

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->o([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    move-result-object v0

    .line 13
    invoke-virtual {p1}, LIf/m;->t()[B

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->m([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    move-result-object v0

    .line 14
    invoke-virtual {p1}, LIf/m;->w()[B

    move-result-object v1

    invoke-virtual {v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->n([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    move-result-object v0

    .line 15
    invoke-virtual {p1}, LIf/m;->o()[B

    move-result-object v1

    if-eqz v1, :cond_0

    .line 16
    invoke-virtual {p1}, LIf/m;->o()[B

    move-result-object p1

    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->f([B)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    invoke-virtual {v0, p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->k(Lorg/spongycastle/pqc/crypto/xmss/BDS;)Lorg/spongycastle/pqc/crypto/xmss/r$b;

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_1

    .line 17
    :cond_0
    :goto_0
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->j()Lorg/spongycastle/pqc/crypto/xmss/r;

    move-result-object p1

    iput-object p1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    .line 18
    :goto_1
    new-instance v0, Ljava/io/IOException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "ClassNotFoundException processing BDS state: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method


# virtual methods
.method public final a()LIf/m;
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/r;->c()[B

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/r;->b()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/q;->c()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    iget-object v2, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/r;->b()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    const/4 v3, 0x0

    .line 28
    const/4 v4, 0x4

    .line 29
    invoke-static {v0, v3, v4}, Lorg/spongycastle/pqc/crypto/xmss/t;->a([BII)J

    .line 30
    .line 31
    .line 32
    move-result-wide v5

    .line 33
    long-to-int v8, v5

    .line 34
    int-to-long v5, v8

    .line 35
    invoke-static {v2, v5, v6}, Lorg/spongycastle/pqc/crypto/xmss/t;->l(IJ)Z

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    if-eqz v2, :cond_0

    .line 40
    .line 41
    invoke-static {v0, v4, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    .line 42
    .line 43
    .line 44
    move-result-object v9

    .line 45
    add-int/2addr v4, v1

    .line 46
    invoke-static {v0, v4, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    .line 47
    .line 48
    .line 49
    move-result-object v10

    .line 50
    add-int/2addr v4, v1

    .line 51
    invoke-static {v0, v4, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    .line 52
    .line 53
    .line 54
    move-result-object v11

    .line 55
    add-int/2addr v4, v1

    .line 56
    invoke-static {v0, v4, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    .line 57
    .line 58
    .line 59
    move-result-object v12

    .line 60
    add-int/2addr v4, v1

    .line 61
    array-length v1, v0

    .line 62
    sub-int/2addr v1, v4

    .line 63
    invoke-static {v0, v4, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    .line 64
    .line 65
    .line 66
    move-result-object v13

    .line 67
    new-instance v7, LIf/m;

    .line 68
    .line 69
    invoke-direct/range {v7 .. v13}, LIf/m;-><init>(I[B[B[B[B[B)V

    .line 70
    .line 71
    .line 72
    return-object v7

    .line 73
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 74
    .line 75
    const-string v1, "index out of bounds"

    .line 76
    .line 77
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 78
    .line 79
    .line 80
    throw v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p1, p0, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    check-cast p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;

    .line 11
    .line 12
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 13
    .line 14
    iget-object v3, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 15
    .line 16
    invoke-virtual {v1, v3}, LSe/q;->equals(Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_1

    .line 21
    .line 22
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 23
    .line 24
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/r;->c()[B

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iget-object p1, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 29
    .line 30
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/r;->c()[B

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-static {v1, p1}, Lorg/spongycastle/util/a;->a([B[B)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    return v0

    .line 41
    :cond_1
    return v2
.end method

.method public getAlgorithm()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "XMSS"

    .line 2
    .line 3
    return-object v0
.end method

.method public getEncoded()[B
    .locals 6

    .line 1
    :try_start_0
    new-instance v0, Lkf/a;

    .line 2
    .line 3
    sget-object v1, LIf/e;->w:LSe/m;

    .line 4
    .line 5
    new-instance v2, LIf/j;

    .line 6
    .line 7
    iget-object v3, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 8
    .line 9
    invoke-virtual {v3}, Lorg/spongycastle/pqc/crypto/xmss/r;->b()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual {v3}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    new-instance v4, Lkf/a;

    .line 18
    .line 19
    iget-object v5, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 20
    .line 21
    invoke-direct {v4, v5}, Lkf/a;-><init>(LSe/m;)V

    .line 22
    .line 23
    .line 24
    invoke-direct {v2, v3, v4}, LIf/j;-><init>(ILkf/a;)V

    .line 25
    .line 26
    .line 27
    invoke-direct {v0, v1, v2}, Lkf/a;-><init>(LSe/m;LSe/e;)V

    .line 28
    .line 29
    .line 30
    new-instance v1, Lcf/d;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->a()LIf/m;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-direct {v1, v0, v2}, Lcf/d;-><init>(Lkf/a;LSe/e;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1}, LSe/l;->i()[B

    .line 40
    .line 41
    .line 42
    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 43
    return-object v0

    .line 44
    :catch_0
    const/4 v0, 0x0

    .line 45
    return-object v0
.end method

.method public getFormat()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "PKCS#8"

    .line 2
    .line 3
    return-object v0
.end method

.method public getHeight()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/r;->b()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public getKeyParams()Lorg/spongycastle/crypto/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 2
    .line 3
    return-object v0
.end method

.method public getTreeDigest()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    invoke-static {v0}, LUf/a;->b(LSe/m;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public getTreeDigestOID()LSe/m;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    invoke-virtual {v0}, LSe/m;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSPrivateKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/r;->c()[B

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-static {v1}, Lorg/spongycastle/util/a;->p([B)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    mul-int/lit8 v1, v1, 0x25

    .line 18
    .line 19
    add-int/2addr v0, v1

    .line 20
    return v0
.end method
