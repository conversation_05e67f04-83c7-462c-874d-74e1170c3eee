.class public final LNK0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LNK0/b;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lf8/g;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LNK0/c;->a:LBc/a;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LBc/a;)LNK0/c;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lf8/g;",
            ">;)",
            "LNK0/c;"
        }
    .end annotation

    .line 1
    new-instance v0, LNK0/c;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LNK0/c;-><init>(LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lf8/g;)LNK0/b;
    .locals 1

    .line 1
    new-instance v0, LNK0/b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LNK0/b;-><init>(Lf8/g;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LNK0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LNK0/c;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lf8/g;

    .line 8
    .line 9
    invoke-static {v0}, LNK0/c;->c(Lf8/g;)LNK0/b;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LNK0/c;->b()LNK0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
