.class public final Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;,
        Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 i2\u00020\u0001:\u0001?B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u000c2\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0014\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0014\u001a\u00020\u000c2\u0008\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0018J\u0017\u0010\u0019\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0019\u0010\u0015J\u0017\u0010\u0019\u001a\u00020\u000c2\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\u0017\u0010\u001d\u001a\u00020\u000c2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0015\u0010!\u001a\u00020\u000c2\u0006\u0010 \u001a\u00020\u001f\u00a2\u0006\u0004\u0008!\u0010\"J\u0019\u0010$\u001a\u00020\u000c2\n\u0008\u0001\u0010#\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u0008$\u0010%J\u0017\u0010&\u001a\u00020\u000c2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u001b\u00a2\u0006\u0004\u0008&\u0010\u001eJ\u0015\u0010(\u001a\u00020\u000c2\u0006\u0010\'\u001a\u00020\u001f\u00a2\u0006\u0004\u0008(\u0010\"J\u001d\u0010+\u001a\u00020\u000c2\u0006\u0010)\u001a\u00020\u001f2\u0006\u0010*\u001a\u00020\u001f\u00a2\u0006\u0004\u0008+\u0010,J\u0015\u0010+\u001a\u00020\u000c2\u0006\u0010.\u001a\u00020-\u00a2\u0006\u0004\u0008+\u0010/J\u0017\u0010+\u001a\u00020\u000c2\u0008\u0010.\u001a\u0004\u0018\u000100\u00a2\u0006\u0004\u0008+\u00101J\u0015\u00103\u001a\u00020\u000c2\u0006\u00102\u001a\u00020\u001f\u00a2\u0006\u0004\u00083\u0010\"J\u0015\u00105\u001a\u00020\u000c2\u0006\u00104\u001a\u00020\u0006\u00a2\u0006\u0004\u00085\u0010\u0015J\u0015\u00107\u001a\u00020\u000c2\u0006\u00106\u001a\u00020\u001f\u00a2\u0006\u0004\u00087\u0010\"J\u0017\u00109\u001a\u00020\u000c2\u0008\u00108\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u00089\u0010%J\u0017\u0010;\u001a\u00020\u000c2\u0008\u0010:\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008;\u0010\u0018J\r\u0010=\u001a\u00020<\u00a2\u0006\u0004\u0008=\u0010>R\u0016\u0010\u000b\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R$\u0010D\u001a\u00020\u001f2\u0006\u0010\u001a\u001a\u00020\u001f8\u0002@BX\u0082\u000e\u00a2\u0006\u000c\n\u0004\u0008A\u0010B\"\u0004\u0008C\u0010\"R\u0014\u0010G\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010I\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010FR\u0014\u0010K\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010FR\u0014\u0010M\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010FR\u001b\u0010S\u001a\u00020N8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008O\u0010P\u001a\u0004\u0008Q\u0010RR\u001b\u0010V\u001a\u00020N8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008T\u0010P\u001a\u0004\u0008U\u0010RR\u001b\u0010[\u001a\u00020W8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008X\u0010P\u001a\u0004\u0008Y\u0010ZR\u001b\u0010`\u001a\u00020\\8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008]\u0010P\u001a\u0004\u0008^\u0010_R\u001b\u0010c\u001a\u00020\\8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008a\u0010P\u001a\u0004\u0008b\u0010_R \u0010h\u001a\u000e\u0012\u0004\u0012\u00020e\u0012\u0004\u0012\u00020\u000c0d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010g\u00a8\u0006j"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;",
        "type",
        "",
        "setStyle",
        "(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)V",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setActionIconClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "resId",
        "setIcon",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "setIconBackground",
        "value",
        "Landroid/content/res/ColorStateList;",
        "colorStateList",
        "setIconTint",
        "(Landroid/content/res/ColorStateList;)V",
        "",
        "showIconTint",
        "w",
        "(Z)V",
        "iconTintColorAttr",
        "setIconTintByColorAttr",
        "(Ljava/lang/Integer;)V",
        "setBackgroundTint",
        "favorite",
        "setFavoriteIcon",
        "top",
        "new",
        "setBadge",
        "(ZZ)V",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;",
        "badgeType",
        "(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V",
        "Lorg/xbet/uikit/components/badges/BadgeType;",
        "(Lorg/xbet/uikit/components/badges/BadgeType;)V",
        "newChamp",
        "setBadgeNew",
        "tint",
        "setBadgeBackgroundTint",
        "enabled",
        "setBadgeVisible",
        "count",
        "setCounterNumber",
        "icon",
        "setBottomIcon",
        "Landroid/widget/ImageView;",
        "q",
        "()Landroid/widget/ImageView;",
        "a",
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;",
        "b",
        "Z",
        "setImageBackgroundAvailable",
        "imageBackgroundAvailable",
        "c",
        "I",
        "size24",
        "d",
        "size40",
        "e",
        "size64",
        "f",
        "size66",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "g",
        "Lkotlin/j;",
        "getIconBackgroundView",
        "()Landroidx/appcompat/widget/AppCompatImageView;",
        "iconBackgroundView",
        "h",
        "getIconView",
        "iconView",
        "Lorg/xbet/uikit/components/counter/a;",
        "i",
        "getCounterHelper",
        "()Lorg/xbet/uikit/components/counter/a;",
        "counterHelper",
        "Lorg/xbet/uikit/components/badges/a;",
        "j",
        "getBadgeHelper",
        "()Lorg/xbet/uikit/components/badges/a;",
        "badgeHelper",
        "k",
        "getIconHelper",
        "iconHelper",
        "Lkotlin/Function1;",
        "Landroid/content/res/TypedArray;",
        "l",
        "Lkotlin/jvm/functions/Function1;",
        "applyAttrs",
        "m",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final n:I

.field public static final o:I

.field public static final p:I


# instance fields
.field public a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Z

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/content/res/TypedArray;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->m:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->n:I

    .line 12
    .line 13
    sget v0, LlZ0/h;->ic_glyph_category_new:I

    .line 14
    .line 15
    sput v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->o:I

    .line 16
    .line 17
    sget v0, LlZ0/h;->ic_glyph_language:I

    .line 18
    .line 19
    sput v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->p:I

    .line 20
    .line 21
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_24:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->c:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_40:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->d:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_64:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->e:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_66:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->f:I

    .line 10
    new-instance v0, LK31/b;

    invoke-direct {v0, p1, p0}, LK31/b;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 11
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->g:Lkotlin/j;

    .line 13
    new-instance v0, LK31/c;

    invoke-direct {v0, p1, p0}, LK31/c;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 14
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->h:Lkotlin/j;

    .line 16
    new-instance v0, LK31/d;

    invoke-direct {v0, p0}, LK31/d;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 17
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 18
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->i:Lkotlin/j;

    .line 19
    new-instance v0, LK31/e;

    invoke-direct {v0, p0}, LK31/e;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 20
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 21
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->j:Lkotlin/j;

    .line 22
    new-instance v0, LK31/f;

    invoke-direct {v0, p0}, LK31/f;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 23
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 24
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->k:Lkotlin/j;

    .line 25
    new-instance v0, LK31/g;

    invoke-direct {v0, p0}, LK31/g;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->l:Lkotlin/jvm/functions/Function1;

    .line 26
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 27
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 28
    sget-object v1, Lm31/g;->SportCellLeftView:[I

    const/4 v2, 0x0

    .line 29
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getCounterHelper()Lorg/xbet/uikit/components/counter/a;

    move-result-object p1

    const/4 p3, 0x2

    const/4 v0, 0x0

    invoke-static {p1, p2, v2, p3, v0}, Lorg/xbet/uikit/components/counter/a;->c(Lorg/xbet/uikit/components/counter/a;Landroid/util/AttributeSet;IILjava/lang/Object;)V

    .line 31
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object p1

    invoke-static {p1, p2, v2, p3, v0}, Lorg/xbet/uikit/components/badges/a;->j(Lorg/xbet/uikit/components/badges/a;Landroid/util/AttributeSet;IILjava/lang/Object;)V

    .line 32
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object p1

    invoke-static {p1, p2, v2, p3, v0}, Lorg/xbet/uikit/components/badges/a;->m(Lorg/xbet/uikit/components/badges/a;Landroid/util/AttributeSet;IILjava/lang/Object;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->s(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->u(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->r(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->n(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/counter/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->o(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/counter/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->p(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->v(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/view/View;)V

    return-void
.end method

.method private final getBadgeHelper()Lorg/xbet/uikit/components/badges/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->j:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/badges/a;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getCounterHelper()Lorg/xbet/uikit/components/counter/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->i:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/counter/a;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->g:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getIconHelper()Lorg/xbet/uikit/components/badges/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->k:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/badges/a;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getIconView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->h:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic h(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->t(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->m(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->l(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic k()I
    .locals 1

    .line 1
    sget v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->o:I

    .line 2
    .line 3
    return v0
.end method

.method public static final l(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 3

    .line 1
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftStyle:I

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-static {}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->getEntries()Lkotlin/enums/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 22
    .line 23
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 24
    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 30
    .line 31
    sget-object v2, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ACTION_ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 32
    .line 33
    if-ne v1, v2, :cond_0

    .line 34
    .line 35
    const/4 v1, 0x1

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    const/4 v1, 0x0

    .line 38
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setClickable(Z)V

    .line 39
    .line 40
    .line 41
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftIconSize:I

    .line 42
    .line 43
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->c:I

    .line 44
    .line 45
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    if-eqz v2, :cond_5

    .line 58
    .line 59
    check-cast v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 60
    .line 61
    iput v0, v2, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 62
    .line 63
    iput v0, v2, Landroid/widget/FrameLayout$LayoutParams;->width:I

    .line 64
    .line 65
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 66
    .line 67
    .line 68
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftImageBackgroundAvailable:I

    .line 69
    .line 70
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->b:Z

    .line 71
    .line 72
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 73
    .line 74
    .line 75
    move-result v0

    .line 76
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setImageBackgroundAvailable(Z)V

    .line 77
    .line 78
    .line 79
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftImageBackground:I

    .line 80
    .line 81
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    if-eqz v0, :cond_1

    .line 86
    .line 87
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconBackground(Landroid/graphics/drawable/Drawable;)V

    .line 88
    .line 89
    .line 90
    :cond_1
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftIcon:I

    .line 91
    .line 92
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    if-eqz v0, :cond_2

    .line 97
    .line 98
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIcon(Landroid/graphics/drawable/Drawable;)V

    .line 99
    .line 100
    .line 101
    :cond_2
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftIconTint:I

    .line 102
    .line 103
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getColorStateList(I)Landroid/content/res/ColorStateList;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    if-eqz v0, :cond_3

    .line 108
    .line 109
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTint(Landroid/content/res/ColorStateList;)V

    .line 110
    .line 111
    .line 112
    :cond_3
    sget v0, Lm31/g;->SportCellLeftView_sportCellLeftImageBackgroundTint:I

    .line 113
    .line 114
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getColorStateList(I)Landroid/content/res/ColorStateList;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    if-eqz p1, :cond_4

    .line 119
    .line 120
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setBackgroundTint(Landroid/content/res/ColorStateList;)V

    .line 121
    .line 122
    .line 123
    :cond_4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 124
    .line 125
    return-object p0

    .line 126
    :cond_5
    new-instance p0, Ljava/lang/NullPointerException;

    .line 127
    .line 128
    const-string p1, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 129
    .line 130
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 131
    .line 132
    .line 133
    throw p0
.end method

.method public static final m(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/badges/a;

    .line 2
    .line 3
    new-instance v1, LK31/i;

    .line 4
    .line 5
    invoke-direct {v1, p0}, LK31/i;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, p0, v1}, Lorg/xbet/uikit/components/badges/a;-><init>(Landroid/view/View;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final n(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Landroid/view/View;

    .line 6
    .line 7
    return-object p0
.end method

.method public static final o(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/counter/a;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/counter/a;

    .line 2
    .line 3
    new-instance v1, LK31/j;

    .line 4
    .line 5
    invoke-direct {v1, p0}, LK31/j;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, p0, v1}, Lorg/xbet/uikit/components/counter/a;-><init>(Landroid/view/View;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final p(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Landroid/view/View;

    .line 6
    .line 7
    return-object p0
.end method

.method public static final r(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 7
    .line 8
    iget p1, p1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->d:I

    .line 9
    .line 10
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 11
    .line 12
    .line 13
    const/16 p1, 0x11

    .line 14
    .line 15
    iput p1, p0, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 16
    .line 17
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 18
    .line 19
    .line 20
    sget p0, LlZ0/h;->ic_sport_cell_left_icon_background_circle:I

    .line 21
    .line 22
    invoke-virtual {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method

.method public static final s(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/badges/a;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/badges/a;

    .line 2
    .line 3
    new-instance v1, LK31/h;

    .line 4
    .line 5
    invoke-direct {v1, p0}, LK31/h;-><init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, p0, v1}, Lorg/xbet/uikit/components/badges/a;-><init>(Landroid/view/View;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method private final setImageBackgroundAvailable(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->b:Z

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/16 p1, 0x8

    .line 12
    .line 13
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public static final t(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Landroid/view/View;

    .line 6
    .line 7
    return-object p0
.end method

.method public static final u(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 7
    .line 8
    .line 9
    move-result p0

    .line 10
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 11
    .line 12
    .line 13
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 14
    .line 15
    iget p1, p1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->c:I

    .line 16
    .line 17
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 18
    .line 19
    .line 20
    const/16 p1, 0x11

    .line 21
    .line 22
    iput p1, p0, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 23
    .line 24
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 25
    .line 26
    .line 27
    return-object v0
.end method

.method public static final v(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/view/View;)V
    .locals 0

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-direct {p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {p0, p1}, Landroid/view/View$OnClickListener;->onClick(Landroid/view/View;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method


# virtual methods
.method public final q()Landroid/widget/ImageView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final setActionIconClickListener(Landroid/view/View$OnClickListener;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, LK31/a;

    .line 6
    .line 7
    invoke-direct {v1, p1, p0}, LK31/a;-><init>(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 18
    .line 19
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ACTION_ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 20
    .line 21
    if-ne v0, v1, :cond_0

    .line 22
    .line 23
    const/4 v0, 0x1

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    const/4 v0, 0x0

    .line 26
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setClickable(Z)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final setBackgroundTint(Landroid/content/res/ColorStateList;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setBadge(Lorg/xbet/uikit/components/badges/BadgeType;)V
    .locals 1

    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object v0

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    return-void
.end method

.method public final setBadge(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$b;->b:[I

    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    move-result p1

    aget p1, v0, p1

    const/4 v0, 0x1

    if-eq p1, v0, :cond_2

    const/4 v0, 0x2

    if-eq p1, v0, :cond_1

    const/4 v0, 0x3

    if-ne p1, v0, :cond_0

    .line 6
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    return-void

    .line 7
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw p1

    .line 8
    :cond_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object p1

    sget-object v0, Lorg/xbet/uikit/components/badges/BadgeType;->WIDGET_BADGE_CHAMPIONSHIP_NEW:Lorg/xbet/uikit/components/badges/BadgeType;

    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    return-void

    .line 9
    :cond_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    move-result-object p1

    sget-object v0, Lorg/xbet/uikit/components/badges/BadgeType;->WIDGET_BADGE_CHAMPIONSHIP_POPULAR:Lorg/xbet/uikit/components/badges/BadgeType;

    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    return-void
.end method

.method public final setBadge(ZZ)V
    .locals 0

    if-eqz p1, :cond_0

    .line 1
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->POPULAR:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    goto :goto_0

    :cond_0
    if-eqz p2, :cond_1

    .line 2
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->NEW:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    goto :goto_0

    .line 3
    :cond_1
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->NONE:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 4
    :goto_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setBadge(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    return-void
.end method

.method public final setBadgeBackgroundTint(I)V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x1

    .line 6
    invoke-virtual {v0, p1, v1}, Lorg/xbet/uikit/components/badges/a;->u(IZ)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setBadgeNew(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    sget-object v0, Lorg/xbet/uikit/components/badges/BadgeType;->WIDGET_BADGE_CHAMPIONSHIP_NEW:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 8
    .line 9
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x0

    .line 18
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public final setBadgeVisible(Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/badges/a;->r()Lorg/xbet/uikit/components/badges/Badge;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    const/4 p1, 0x0

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/16 p1, 0x8

    .line 16
    .line 17
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    :cond_1
    return-void
.end method

.method public final setBottomIcon(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p1, :cond_1

    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 5
    .line 6
    .line 7
    move-result-object v1

    .line 8
    invoke-virtual {v1}, Lorg/xbet/uikit/components/badges/a;->r()Lorg/xbet/uikit/components/badges/Badge;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    if-nez v1, :cond_0

    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    sget-object v2, Lorg/xbet/uikit/components/badges/BadgeType;->WIDGET_BADGE_CUSTOM:Lorg/xbet/uikit/components/badges/BadgeType;

    .line 19
    .line 20
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, Lorg/xbet/uikit/components/badges/a;->r()Lorg/xbet/uikit/components/badges/Badge;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    if-eqz v1, :cond_2

    .line 32
    .line 33
    invoke-virtual {v1, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    .line 42
    .line 43
    .line 44
    :cond_2
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getCounterHelper()Lorg/xbet/uikit/components/counter/a;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/counter/a;->e(Ljava/lang/Integer;)V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public final setCounterNumber(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getCounterHelper()Lorg/xbet/uikit/components/counter/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/counter/a;->e(Ljava/lang/Integer;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    const/4 v0, 0x0

    .line 13
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->c(Lorg/xbet/uikit/components/badges/BadgeType;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setFavoriteIcon(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget p1, LlZ0/h;->ic_glyph_favourite_active:I

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget p1, LlZ0/h;->ic_glyph_favourite_inactive:I

    .line 7
    .line 8
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final setIcon(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    return-void
.end method

.method public final setIcon(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setIconBackground(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    return-void
.end method

.method public final setIconBackground(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconBackgroundView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setIconTint(Landroid/content/res/ColorStateList;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setIconTintByColorAttr(Ljava/lang/Integer;)V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p1, :cond_0

    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 5
    .line 6
    .line 7
    move-result-object v1

    .line 8
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    const/4 v2, 0x2

    .line 13
    invoke-static {v1, p1, v0, v2, v0}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTint(Landroid/content/res/ColorStateList;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final setStyle(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)V
    .locals 4
    .param p1    # Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->m:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;

    .line 4
    .line 5
    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;->a(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$a;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView$b;->a:[I

    .line 10
    .line 11
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    aget v1, v1, v2

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    if-eq v1, v2, :cond_3

    .line 19
    .line 20
    const/4 v2, 0x2

    .line 21
    if-eq v1, v2, :cond_2

    .line 22
    .line 23
    const/4 v2, 0x3

    .line 24
    if-eq v1, v2, :cond_1

    .line 25
    .line 26
    const/4 v2, 0x4

    .line 27
    if-ne v1, v2, :cond_0

    .line 28
    .line 29
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->d:I

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 33
    .line 34
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 35
    .line 36
    .line 37
    throw p1

    .line 38
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->f:I

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_2
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->e:I

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->d:I

    .line 45
    .line 46
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    if-eqz v2, :cond_5

    .line 51
    .line 52
    check-cast v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 53
    .line 54
    iput v1, v2, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 55
    .line 56
    invoke-virtual {p0, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 57
    .line 58
    .line 59
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->BACKGROUND_ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 60
    .line 61
    if-ne p1, v1, :cond_4

    .line 62
    .line 63
    const/4 p1, 0x0

    .line 64
    goto :goto_1

    .line 65
    :cond_4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    sget v1, LlZ0/g;->space_2:I

    .line 70
    .line 71
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 72
    .line 73
    .line 74
    move-result p1

    .line 75
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getPaddingStart()I

    .line 76
    .line 77
    .line 78
    move-result v1

    .line 79
    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    .line 80
    .line 81
    .line 82
    move-result v2

    .line 83
    invoke-virtual {p0}, Landroid/view/View;->getPaddingBottom()I

    .line 84
    .line 85
    .line 86
    move-result v3

    .line 87
    invoke-virtual {p0, v1, v2, p1, v3}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    sget-object v1, Lm31/g;->SportCellLeftView:[I

    .line 95
    .line 96
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->l:Lkotlin/jvm/functions/Function1;

    .line 97
    .line 98
    invoke-virtual {p1, v0, v1}, Landroid/content/Context;->obtainStyledAttributes(I[I)Landroid/content/res/TypedArray;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    invoke-interface {v2, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 106
    .line 107
    .line 108
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getCounterHelper()Lorg/xbet/uikit/components/counter/a;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/counter/a;->a(I)V

    .line 113
    .line 114
    .line 115
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getBadgeHelper()Lorg/xbet/uikit/components/badges/a;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->h(I)V

    .line 120
    .line 121
    .line 122
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconHelper()Lorg/xbet/uikit/components/badges/a;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/badges/a;->k(I)V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :cond_5
    new-instance p1, Ljava/lang/NullPointerException;

    .line 131
    .line 132
    const-string v0, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 133
    .line 134
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 135
    .line 136
    .line 137
    throw p1
.end method

.method public final w(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget p1, LlZ0/d;->uikitSecondary:I

    .line 4
    .line 5
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->getIconView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x0

    .line 18
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method
