.class public final LsQ0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001b\u0010\u0008\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0007\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LnQ0/b;",
        "",
        "playerId",
        "LtQ0/c;",
        "b",
        "(LnQ0/b;Ljava/lang/String;)LtQ0/c;",
        "",
        "tabPosition",
        "a",
        "(LnQ0/b;I)LtQ0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LnQ0/b;I)LtQ0/c;
    .locals 15
    .param p0    # LnQ0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LnQ0/b;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ljava/util/ArrayList;

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 10
    .line 11
    .line 12
    move-result v3

    .line 13
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 14
    .line 15
    .line 16
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    if-eqz v3, :cond_f

    .line 25
    .line 26
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    check-cast v3, LND0/k;

    .line 31
    .line 32
    invoke-virtual {p0}, LnQ0/b;->b()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 37
    .line 38
    .line 39
    move-result-object v4

    .line 40
    :cond_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 41
    .line 42
    .line 43
    move-result v5

    .line 44
    const/4 v6, 0x0

    .line 45
    if-eqz v5, :cond_1

    .line 46
    .line 47
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object v5

    .line 51
    move-object v7, v5

    .line 52
    check-cast v7, LnQ0/a;

    .line 53
    .line 54
    invoke-virtual {v7}, LnQ0/a;->c()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v7

    .line 58
    invoke-virtual {v3}, LND0/k;->c()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v8

    .line 62
    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 63
    .line 64
    .line 65
    move-result v7

    .line 66
    if-eqz v7, :cond_0

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_1
    move-object v5, v6

    .line 70
    :goto_1
    check-cast v5, LnQ0/a;

    .line 71
    .line 72
    invoke-virtual {v3}, LND0/k;->f()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v8

    .line 76
    sget-object v4, LDX0/e;->a:LDX0/e;

    .line 77
    .line 78
    invoke-virtual {v3}, LND0/k;->d()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v3

    .line 82
    invoke-virtual {v4, v3}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v9

    .line 86
    if-eqz v5, :cond_2

    .line 87
    .line 88
    invoke-virtual {v5}, LnQ0/a;->b()J

    .line 89
    .line 90
    .line 91
    move-result-wide v3

    .line 92
    goto :goto_2

    .line 93
    :cond_2
    const-wide/16 v3, -0x1

    .line 94
    .line 95
    :goto_2
    invoke-static {v3, v4}, Ll8/b$a$c;->f(J)J

    .line 96
    .line 97
    .line 98
    move-result-wide v3

    .line 99
    invoke-static {v3, v4}, Ll8/b$a$c;->d(J)Ll8/b$a$c;

    .line 100
    .line 101
    .line 102
    move-result-object v10

    .line 103
    if-eqz v5, :cond_3

    .line 104
    .line 105
    invoke-virtual {v5}, LnQ0/a;->a()I

    .line 106
    .line 107
    .line 108
    move-result v3

    .line 109
    move v11, v3

    .line 110
    goto :goto_3

    .line 111
    :cond_3
    const/4 v3, 0x0

    .line 112
    const/4 v11, 0x0

    .line 113
    :goto_3
    if-eqz v5, :cond_4

    .line 114
    .line 115
    invoke-virtual {v5}, LnQ0/a;->e()Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object v3

    .line 119
    goto :goto_4

    .line 120
    :cond_4
    move-object v3, v6

    .line 121
    :goto_4
    if-nez v3, :cond_5

    .line 122
    .line 123
    const-string v3, ""

    .line 124
    .line 125
    :cond_5
    move-object v12, v3

    .line 126
    invoke-virtual {p0}, LnQ0/b;->a()Ljava/util/List;

    .line 127
    .line 128
    .line 129
    move-result-object v3

    .line 130
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 131
    .line 132
    .line 133
    move-result-object v3

    .line 134
    :cond_6
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 135
    .line 136
    .line 137
    move-result v4

    .line 138
    if-eqz v4, :cond_8

    .line 139
    .line 140
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v4

    .line 144
    move-object v7, v4

    .line 145
    check-cast v7, LND0/h;

    .line 146
    .line 147
    invoke-virtual {v7}, LND0/h;->c()Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v7

    .line 151
    if-eqz v5, :cond_7

    .line 152
    .line 153
    invoke-virtual {v5}, LnQ0/a;->c()Ljava/lang/String;

    .line 154
    .line 155
    .line 156
    move-result-object v13

    .line 157
    goto :goto_5

    .line 158
    :cond_7
    move-object v13, v6

    .line 159
    :goto_5
    invoke-static {v7, v13}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 160
    .line 161
    .line 162
    move-result v7

    .line 163
    if-eqz v7, :cond_6

    .line 164
    .line 165
    goto :goto_6

    .line 166
    :cond_8
    move-object v4, v6

    .line 167
    :goto_6
    check-cast v4, LND0/h;

    .line 168
    .line 169
    if-eqz v4, :cond_a

    .line 170
    .line 171
    invoke-virtual {v4}, LND0/h;->b()LND0/a;

    .line 172
    .line 173
    .line 174
    move-result-object v3

    .line 175
    if-nez v3, :cond_9

    .line 176
    .line 177
    goto :goto_8

    .line 178
    :cond_9
    :goto_7
    move-object v13, v3

    .line 179
    goto :goto_9

    .line 180
    :cond_a
    :goto_8
    sget-object v3, LND0/a;->c:LND0/a$a;

    .line 181
    .line 182
    invoke-virtual {v3}, LND0/a$a;->a()LND0/a;

    .line 183
    .line 184
    .line 185
    move-result-object v3

    .line 186
    goto :goto_7

    .line 187
    :goto_9
    if-eqz v5, :cond_d

    .line 188
    .line 189
    invoke-virtual {v5}, LnQ0/a;->d()Ljava/util/List;

    .line 190
    .line 191
    .line 192
    move-result-object v3

    .line 193
    if-eqz v3, :cond_d

    .line 194
    .line 195
    new-instance v4, Ljava/util/ArrayList;

    .line 196
    .line 197
    invoke-static {v3, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 198
    .line 199
    .line 200
    move-result v5

    .line 201
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 202
    .line 203
    .line 204
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 205
    .line 206
    .line 207
    move-result-object v3

    .line 208
    :goto_a
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 209
    .line 210
    .line 211
    move-result v5

    .line 212
    if-eqz v5, :cond_b

    .line 213
    .line 214
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 215
    .line 216
    .line 217
    move-result-object v5

    .line 218
    check-cast v5, LGN0/a;

    .line 219
    .line 220
    invoke-static {v5}, LsQ0/a;->a(LGN0/a;)LtQ0/a;

    .line 221
    .line 222
    .line 223
    move-result-object v5

    .line 224
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 225
    .line 226
    .line 227
    goto :goto_a

    .line 228
    :cond_b
    new-instance v6, Ljava/util/ArrayList;

    .line 229
    .line 230
    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 231
    .line 232
    .line 233
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 234
    .line 235
    .line 236
    move-result-object v3

    .line 237
    :cond_c
    :goto_b
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 238
    .line 239
    .line 240
    move-result v4

    .line 241
    if-eqz v4, :cond_d

    .line 242
    .line 243
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 244
    .line 245
    .line 246
    move-result-object v4

    .line 247
    move-object v5, v4

    .line 248
    check-cast v5, LtQ0/a;

    .line 249
    .line 250
    invoke-virtual {v5}, LtQ0/a;->a()LtQ0/f;

    .line 251
    .line 252
    .line 253
    move-result-object v5

    .line 254
    invoke-virtual {v5}, LtQ0/f;->a()Z

    .line 255
    .line 256
    .line 257
    move-result v5

    .line 258
    if-eqz v5, :cond_c

    .line 259
    .line 260
    invoke-interface {v6, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 261
    .line 262
    .line 263
    goto :goto_b

    .line 264
    :cond_d
    if-nez v6, :cond_e

    .line 265
    .line 266
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 267
    .line 268
    .line 269
    move-result-object v6

    .line 270
    :cond_e
    move-object v14, v6

    .line 271
    new-instance v7, LtQ0/c;

    .line 272
    .line 273
    invoke-direct/range {v7 .. v14}, LtQ0/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ll8/b$a;ILjava/lang/String;LND0/a;Ljava/util/List;)V

    .line 274
    .line 275
    .line 276
    invoke-interface {v1, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 277
    .line 278
    .line 279
    goto/16 :goto_0

    .line 280
    .line 281
    :cond_f
    move/from16 v3, p1

    .line 282
    .line 283
    invoke-static {v1, v3}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 284
    .line 285
    .line 286
    move-result-object p0

    .line 287
    check-cast p0, LtQ0/c;

    .line 288
    .line 289
    if-eqz p0, :cond_10

    .line 290
    .line 291
    return-object p0

    .line 292
    :cond_10
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 293
    .line 294
    invoke-direct {p0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 295
    .line 296
    .line 297
    throw p0
.end method

.method public static final b(LnQ0/b;Ljava/lang/String;)LtQ0/c;
    .locals 13
    .param p0    # LnQ0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LnQ0/b;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/4 v2, 0x0

    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    move-object v3, v1

    .line 21
    check-cast v3, LND0/k;

    .line 22
    .line 23
    invoke-virtual {v3}, LND0/k;->c()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-static {v3, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    if-eqz v3, :cond_0

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_1
    move-object v1, v2

    .line 35
    :goto_0
    check-cast v1, LND0/k;

    .line 36
    .line 37
    invoke-virtual {p0}, LnQ0/b;->b()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    if-eqz v3, :cond_3

    .line 50
    .line 51
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v3

    .line 55
    move-object v4, v3

    .line 56
    check-cast v4, LnQ0/a;

    .line 57
    .line 58
    invoke-virtual {v4}, LnQ0/a;->c()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-static {v4, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 63
    .line 64
    .line 65
    move-result v4

    .line 66
    if-eqz v4, :cond_2

    .line 67
    .line 68
    goto :goto_1

    .line 69
    :cond_3
    move-object v3, v2

    .line 70
    :goto_1
    check-cast v3, LnQ0/a;

    .line 71
    .line 72
    if-eqz v1, :cond_4

    .line 73
    .line 74
    invoke-virtual {v1}, LND0/k;->f()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    goto :goto_2

    .line 79
    :cond_4
    move-object v0, v2

    .line 80
    :goto_2
    const-string v4, ""

    .line 81
    .line 82
    if-nez v0, :cond_5

    .line 83
    .line 84
    move-object v6, v4

    .line 85
    goto :goto_3

    .line 86
    :cond_5
    move-object v6, v0

    .line 87
    :goto_3
    sget-object v0, LDX0/e;->a:LDX0/e;

    .line 88
    .line 89
    if-eqz v1, :cond_6

    .line 90
    .line 91
    invoke-virtual {v1}, LND0/k;->d()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    goto :goto_4

    .line 96
    :cond_6
    move-object v1, v2

    .line 97
    :goto_4
    if-nez v1, :cond_7

    .line 98
    .line 99
    move-object v1, v4

    .line 100
    :cond_7
    invoke-virtual {v0, v1}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v7

    .line 104
    if-eqz v3, :cond_8

    .line 105
    .line 106
    invoke-virtual {v3}, LnQ0/a;->b()J

    .line 107
    .line 108
    .line 109
    move-result-wide v0

    .line 110
    goto :goto_5

    .line 111
    :cond_8
    const-wide/16 v0, -0x1

    .line 112
    .line 113
    :goto_5
    invoke-static {v0, v1}, Ll8/b$a$c;->f(J)J

    .line 114
    .line 115
    .line 116
    move-result-wide v0

    .line 117
    invoke-static {v0, v1}, Ll8/b$a$c;->d(J)Ll8/b$a$c;

    .line 118
    .line 119
    .line 120
    move-result-object v8

    .line 121
    if-eqz v3, :cond_9

    .line 122
    .line 123
    invoke-virtual {v3}, LnQ0/a;->a()I

    .line 124
    .line 125
    .line 126
    move-result v0

    .line 127
    move v9, v0

    .line 128
    goto :goto_6

    .line 129
    :cond_9
    const/4 v0, 0x0

    .line 130
    const/4 v9, 0x0

    .line 131
    :goto_6
    if-eqz v3, :cond_a

    .line 132
    .line 133
    invoke-virtual {v3}, LnQ0/a;->e()Ljava/lang/String;

    .line 134
    .line 135
    .line 136
    move-result-object v0

    .line 137
    goto :goto_7

    .line 138
    :cond_a
    move-object v0, v2

    .line 139
    :goto_7
    if-nez v0, :cond_b

    .line 140
    .line 141
    move-object v10, v4

    .line 142
    goto :goto_8

    .line 143
    :cond_b
    move-object v10, v0

    .line 144
    :goto_8
    invoke-virtual {p0}, LnQ0/b;->a()Ljava/util/List;

    .line 145
    .line 146
    .line 147
    move-result-object p0

    .line 148
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 149
    .line 150
    .line 151
    move-result-object p0

    .line 152
    :cond_c
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 153
    .line 154
    .line 155
    move-result v0

    .line 156
    if-eqz v0, :cond_d

    .line 157
    .line 158
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    move-object v1, v0

    .line 163
    check-cast v1, LND0/h;

    .line 164
    .line 165
    invoke-virtual {v1}, LND0/h;->c()Ljava/lang/String;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    move-result v1

    .line 173
    if-eqz v1, :cond_c

    .line 174
    .line 175
    goto :goto_9

    .line 176
    :cond_d
    move-object v0, v2

    .line 177
    :goto_9
    check-cast v0, LND0/h;

    .line 178
    .line 179
    if-eqz v0, :cond_f

    .line 180
    .line 181
    invoke-virtual {v0}, LND0/h;->b()LND0/a;

    .line 182
    .line 183
    .line 184
    move-result-object p0

    .line 185
    if-nez p0, :cond_e

    .line 186
    .line 187
    goto :goto_b

    .line 188
    :cond_e
    :goto_a
    move-object v11, p0

    .line 189
    goto :goto_c

    .line 190
    :cond_f
    :goto_b
    sget-object p0, LND0/a;->c:LND0/a$a;

    .line 191
    .line 192
    invoke-virtual {p0}, LND0/a$a;->a()LND0/a;

    .line 193
    .line 194
    .line 195
    move-result-object p0

    .line 196
    goto :goto_a

    .line 197
    :goto_c
    if-eqz v3, :cond_12

    .line 198
    .line 199
    invoke-virtual {v3}, LnQ0/a;->d()Ljava/util/List;

    .line 200
    .line 201
    .line 202
    move-result-object p0

    .line 203
    if-eqz p0, :cond_12

    .line 204
    .line 205
    new-instance p1, Ljava/util/ArrayList;

    .line 206
    .line 207
    const/16 v0, 0xa

    .line 208
    .line 209
    invoke-static {p0, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 210
    .line 211
    .line 212
    move-result v0

    .line 213
    invoke-direct {p1, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 214
    .line 215
    .line 216
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 217
    .line 218
    .line 219
    move-result-object p0

    .line 220
    :goto_d
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 221
    .line 222
    .line 223
    move-result v0

    .line 224
    if-eqz v0, :cond_10

    .line 225
    .line 226
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 227
    .line 228
    .line 229
    move-result-object v0

    .line 230
    check-cast v0, LGN0/a;

    .line 231
    .line 232
    invoke-static {v0}, LsQ0/a;->a(LGN0/a;)LtQ0/a;

    .line 233
    .line 234
    .line 235
    move-result-object v0

    .line 236
    invoke-interface {p1, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 237
    .line 238
    .line 239
    goto :goto_d

    .line 240
    :cond_10
    new-instance v2, Ljava/util/ArrayList;

    .line 241
    .line 242
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 243
    .line 244
    .line 245
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 246
    .line 247
    .line 248
    move-result-object p0

    .line 249
    :cond_11
    :goto_e
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 250
    .line 251
    .line 252
    move-result p1

    .line 253
    if-eqz p1, :cond_12

    .line 254
    .line 255
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 256
    .line 257
    .line 258
    move-result-object p1

    .line 259
    move-object v0, p1

    .line 260
    check-cast v0, LtQ0/a;

    .line 261
    .line 262
    invoke-virtual {v0}, LtQ0/a;->a()LtQ0/f;

    .line 263
    .line 264
    .line 265
    move-result-object v0

    .line 266
    invoke-virtual {v0}, LtQ0/f;->a()Z

    .line 267
    .line 268
    .line 269
    move-result v0

    .line 270
    if-eqz v0, :cond_11

    .line 271
    .line 272
    invoke-interface {v2, p1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 273
    .line 274
    .line 275
    goto :goto_e

    .line 276
    :cond_12
    if-nez v2, :cond_13

    .line 277
    .line 278
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 279
    .line 280
    .line 281
    move-result-object v2

    .line 282
    :cond_13
    move-object v12, v2

    .line 283
    new-instance v5, LtQ0/c;

    .line 284
    .line 285
    invoke-direct/range {v5 .. v12}, LtQ0/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ll8/b$a;ILjava/lang/String;LND0/a;Ljava/util/List;)V

    .line 286
    .line 287
    .line 288
    return-object v5
.end method
