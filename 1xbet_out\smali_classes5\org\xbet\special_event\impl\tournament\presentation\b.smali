.class public final synthetic Lorg/xbet/special_event/impl/tournament/presentation/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/b;->a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/b;->a:Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;

    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;->y2(Lorg/xbet/special_event/impl/tournament/presentation/TournamentFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
