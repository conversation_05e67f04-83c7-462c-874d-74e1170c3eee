.class public final synthetic Lorg/xbet/themesettings/impl/presentation/theme/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/theme/m;

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/l;->a:Lorg/xbet/themesettings/impl/presentation/theme/m;

    iput-boolean p2, p0, Lorg/xbet/themesettings/impl/presentation/theme/l;->b:Z

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/l;->a:Lorg/xbet/themesettings/impl/presentation/theme/m;

    iget-boolean v1, p0, Lorg/xbet/themesettings/impl/presentation/theme/l;->b:Z

    invoke-static {v0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->o3(Lorg/xbet/themesettings/impl/presentation/theme/m;Z)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
