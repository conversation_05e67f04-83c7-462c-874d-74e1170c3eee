.class public final Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\rH\u0000\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u0015\u0010\u0013J\u001f\u0010\u001d\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u0019H\u0000\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001f\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u001e\u0010\u0013J\u0017\u0010!\u001a\u00020\n2\u0006\u0010 \u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008!\u0010\u0013J\u0017\u0010$\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008#\u0010\u0013R\u0014\u0010(\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'\u00a8\u0006)"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "u",
        "()V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "t",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V",
        "count",
        "s",
        "(I)V",
        "resId",
        "setCoeffImage$tile_matching_release",
        "setCoeffImage",
        "",
        "coeff",
        "",
        "empty",
        "setCoeffValue$tile_matching_release",
        "(DZ)V",
        "setCoeffValue",
        "setProgressDrawableTint$tile_matching_release",
        "setProgressDrawableTint",
        "progress",
        "v",
        "max",
        "setMaxProgress$tile_matching_release",
        "setMaxProgress",
        "LxT0/c;",
        "a",
        "LxT0/c;",
        "binding",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LxT0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    const/4 p2, 0x1

    .line 6
    invoke-static {p1, p0, p2}, LxT0/c;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LxT0/c;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final u()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->s(I)V

    .line 3
    .line 4
    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->v(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final s(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 2
    .line 3
    iget-object v0, v0, LxT0/c;->f:Landroid/widget/TextView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-gtz p1, :cond_0

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v2, 0x0

    .line 11
    :goto_0
    if-eqz v2, :cond_1

    .line 12
    .line 13
    const/4 v1, 0x4

    .line 14
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 18
    .line 19
    iget-object v0, v0, LxT0/c;->f:Landroid/widget/TextView;

    .line 20
    .line 21
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final setCoeffImage$tile_matching_release(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 2
    .line 3
    iget-object v0, v0, LxT0/c;->b:Landroid/widget/ImageView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setCoeffValue$tile_matching_release(DZ)V
    .locals 6

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 6
    .line 7
    iget-object p1, p1, LxT0/c;->g:Landroid/widget/TextView;

    .line 8
    .line 9
    const-string p2, ""

    .line 10
    .line 11
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    int-to-double v2, v1

    .line 16
    rem-double v2, p1, v2

    .line 17
    .line 18
    const-wide/16 v4, 0x0

    .line 19
    .line 20
    cmpg-double p3, v2, v4

    .line 21
    .line 22
    if-nez p3, :cond_1

    .line 23
    .line 24
    invoke-static {p1, p2}, LQc/c;->c(D)I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    goto :goto_0

    .line 33
    :cond_1
    invoke-static {p1, p2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    :goto_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object p2

    .line 41
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    move-result p2

    .line 45
    iget-object p3, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 46
    .line 47
    iget-object p3, p3, LxT0/c;->g:Landroid/widget/TextView;

    .line 48
    .line 49
    if-eqz p2, :cond_2

    .line 50
    .line 51
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    sget p2, Lpb/k;->bonus:I

    .line 56
    .line 57
    invoke-virtual {p1, p2}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    goto :goto_1

    .line 62
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 63
    .line 64
    .line 65
    move-result-object p2

    .line 66
    sget v2, Lpb/k;->factor:I

    .line 67
    .line 68
    new-array v1, v1, [Ljava/lang/Object;

    .line 69
    .line 70
    aput-object p1, v1, v0

    .line 71
    .line 72
    invoke-virtual {p2, v2, v1}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    :goto_1
    invoke-virtual {p3, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 77
    .line 78
    .line 79
    return-void
.end method

.method public final setMaxProgress$tile_matching_release(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 2
    .line 3
    iget-object v0, v0, LxT0/c;->e:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setProgressDrawableTint$tile_matching_release(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 2
    .line 3
    iget-object v0, v0, LxT0/c;->e:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/widget/ProgressBar;->getProgressDrawable()Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-static {v1, p1}, LNW0/a;->a(Landroid/content/Context;I)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-virtual {v0, p1}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final t(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V
    .locals 3
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1}, LBT0/a;->f(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 14
    .line 15
    iget-object v1, v1, LxT0/c;->f:Landroid/widget/TextView;

    .line 16
    .line 17
    invoke-static {p1}, LBT0/a;->e(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {v1, v2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 22
    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 25
    .line 26
    iget-object v1, v1, LxT0/c;->b:Landroid/widget/ImageView;

    .line 27
    .line 28
    invoke-static {p1}, LBT0/a;->d(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    invoke-virtual {v1, v2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 33
    .line 34
    .line 35
    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 36
    .line 37
    iget-object v1, v1, LxT0/c;->b:Landroid/widget/ImageView;

    .line 38
    .line 39
    invoke-virtual {v1, v0, v0, v0, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 43
    .line 44
    iget-object v0, v0, LxT0/c;->c:Landroid/widget/ImageView;

    .line 45
    .line 46
    invoke-static {p1}, LBT0/a;->h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 54
    .line 55
    iget-object v0, v0, LxT0/c;->d:Landroid/widget/ImageView;

    .line 56
    .line 57
    invoke-static {p1}, LBT0/a;->s(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    invoke-virtual {v0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 62
    .line 63
    .line 64
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->u()V

    .line 65
    .line 66
    .line 67
    return-void
.end method

.method public final v(I)V
    .locals 2

    .line 1
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 2
    .line 3
    const/16 v1, 0x18

    .line 4
    .line 5
    if-lt v0, v1, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 8
    .line 9
    iget-object v0, v0, LxT0/c;->e:Landroid/widget/ProgressBar;

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    invoke-static {v0, p1, v1}, LCT0/l;->a(Landroid/widget/ProgressBar;IZ)V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;->a:LxT0/c;

    .line 17
    .line 18
    iget-object v0, v0, LxT0/c;->e:Landroid/widget/ProgressBar;

    .line 19
    .line 20
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
