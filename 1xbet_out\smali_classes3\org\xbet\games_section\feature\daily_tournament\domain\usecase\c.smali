.class public final Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\u0018\u0000 \n2\u00020\u0001:\u0001\u0007B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;",
        "",
        "Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;",
        "repository",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;)V",
        "",
        "a",
        "()Ljava/lang/String;",
        "Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;",
        "b",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;->b:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c$a;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 2
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;->a:Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;

    .line 2
    .line 3
    const-string v1, "banner_1xGames_day_"

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/data/repository/DailyRepository;->k(Ljava/lang/String;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method
