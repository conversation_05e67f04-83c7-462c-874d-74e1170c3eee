.class public final LEa1/j;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LEa1/j$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/remoteconfig/domain/models/AggregatorPromoGiftsStyleType;",
        "Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;",
        "a",
        "(Lorg/xbet/remoteconfig/domain/models/AggregatorPromoGiftsStyleType;)Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/remoteconfig/domain/models/AggregatorPromoGiftsStyleType;)Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;
    .locals 1
    .param p0    # Lorg/xbet/remoteconfig/domain/models/AggregatorPromoGiftsStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LEa1/j$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_4

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_3

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_2

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-eq p0, v0, :cond_1

    .line 20
    .line 21
    const/4 v0, 0x5

    .line 22
    if-ne p0, v0, :cond_0

    .line 23
    .line 24
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->BUTTON:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 25
    .line 26
    return-object p0

    .line 27
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 28
    .line 29
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 30
    .line 31
    .line 32
    throw p0

    .line 33
    :cond_1
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->PICTURE_L:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 34
    .line 35
    return-object p0

    .line 36
    :cond_2
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->CARD:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 37
    .line 38
    return-object p0

    .line 39
    :cond_3
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->PICTURE_S:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 40
    .line 41
    return-object p0

    .line 42
    :cond_4
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;->HEADER:Lorg/xbet/uikit_aggregator/aggregatorBonuses/AggregatorBonusesStyle;

    .line 43
    .line 44
    return-object p0
.end method
