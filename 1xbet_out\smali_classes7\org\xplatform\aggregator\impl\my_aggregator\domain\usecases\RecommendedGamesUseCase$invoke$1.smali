.class final Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;
.source "SourceFile"


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.domain.usecases.RecommendedGamesUseCase"
    f = "RecommendedGamesUseCase.kt"
    l = {
        0x11,
        0x31
    }
    m = "invoke"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;->a(JLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field I$0:I

.field I$1:I

.field J$0:J

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->result:Ljava/lang/Object;

    iget p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->label:I

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const-wide/16 v1, 0x0

    move-object v5, p0

    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/my_aggregator/domain/usecases/RecommendedGamesUseCase;->a(JLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
