.class public final synthetic Leb1/t;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroidx/lifecycle/LifecycleCoroutineScope;


# direct methods
.method public synthetic constructor <init>(Landroidx/lifecycle/LifecycleCoroutineScope;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/t;->a:Landroidx/lifecycle/LifecycleCoroutineScope;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Leb1/t;->a:Landroidx/lifecycle/LifecycleCoroutineScope;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentProvidersDelegateKt;->c(Landroidx/lifecycle/LifecycleCoroutineScope;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
