.class public final Lorg/xbet/spin_and_win/data/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0006\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010!\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0019\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0015\u0010\r\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\r\u0010\u000e\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000e\u0010\u0003J\r\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0015\u0010\u0012\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\r\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\r\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0015\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\r\u0010\u001d\u001a\u00020\n\u00a2\u0006\u0004\u0008\u001d\u0010\u0003J\u0015\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u001eH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 R \u0010#\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\"R\u0016\u0010%\u001a\u00020\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010$R\u0016\u0010\'\u001a\u00020\u00178\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010&\u00a8\u0006("
    }
    d2 = {
        "Lorg/xbet/spin_and_win/data/a;",
        "",
        "<init>",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "Ldz0/a;",
        "c",
        "()Lkotlinx/coroutines/flow/e;",
        "bet",
        "",
        "a",
        "(Ldz0/a;)V",
        "i",
        "h",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "g",
        "()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "k",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V",
        "",
        "f",
        "()D",
        "Ldz0/b;",
        "e",
        "()Ldz0/b;",
        "gameResult",
        "j",
        "(Ldz0/b;)V",
        "b",
        "",
        "d",
        "()Ljava/util/List;",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlinx/coroutines/flow/V;",
        "betsList",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "selectedBet",
        "Ldz0/b;",
        "currentGameResult",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ldz0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 15

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-static {v0}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    sget-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 15
    .line 16
    iput-object v0, p0, Lorg/xbet/spin_and_win/data/a;->b:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 17
    .line 18
    new-instance v1, Ldz0/b;

    .line 19
    .line 20
    const/16 v13, 0x7f

    .line 21
    .line 22
    const/4 v14, 0x0

    .line 23
    const-wide/16 v2, 0x0

    .line 24
    .line 25
    const-wide/16 v4, 0x0

    .line 26
    .line 27
    const-wide/16 v6, 0x0

    .line 28
    .line 29
    const/4 v8, 0x0

    .line 30
    const/4 v9, 0x0

    .line 31
    const-wide/16 v10, 0x0

    .line 32
    .line 33
    const/4 v12, 0x0

    .line 34
    invoke-direct/range {v1 .. v14}, Ldz0/b;-><init>(JDDLjava/util/List;Lorg/xbet/spin_and_win/domain/model/SpinAndWinGameStateEnum;DLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    iput-object v1, p0, Lorg/xbet/spin_and_win/data/a;->c:Ldz0/b;

    .line 38
    .line 39
    return-void
.end method


# virtual methods
.method public final a(Ldz0/a;)V
    .locals 5
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/data/a;->d()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    if-eqz v2, :cond_1

    .line 14
    .line 15
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    move-object v3, v2

    .line 20
    check-cast v3, Ldz0/a;

    .line 21
    .line 22
    invoke-virtual {v3}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 23
    .line 24
    .line 25
    move-result-object v3

    .line 26
    invoke-virtual {p1}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    if-ne v3, v4, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v2, 0x0

    .line 34
    :goto_0
    check-cast v2, Ldz0/a;

    .line 35
    .line 36
    invoke-static {v0, v2}, Lkotlin/collections/CollectionsKt;->B0(Ljava/util/List;Ljava/lang/Object;)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    const/4 v2, -0x1

    .line 41
    if-le v1, v2, :cond_2

    .line 42
    .line 43
    invoke-interface {v0, v1, p1}, Ljava/util/List;->set(ILjava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    goto :goto_1

    .line 47
    :cond_2
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    :goto_1
    iget-object p1, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 51
    .line 52
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final b()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/data/a;->h()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->EMPTY:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Lorg/xbet/spin_and_win/data/a;->k(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 7
    .line 8
    .line 9
    new-instance v1, Ldz0/b;

    .line 10
    .line 11
    const/16 v13, 0x7f

    .line 12
    .line 13
    const/4 v14, 0x0

    .line 14
    const-wide/16 v2, 0x0

    .line 15
    .line 16
    const-wide/16 v4, 0x0

    .line 17
    .line 18
    const-wide/16 v6, 0x0

    .line 19
    .line 20
    const/4 v8, 0x0

    .line 21
    const/4 v9, 0x0

    .line 22
    const-wide/16 v10, 0x0

    .line 23
    .line 24
    const/4 v12, 0x0

    .line 25
    invoke-direct/range {v1 .. v14}, Ldz0/b;-><init>(JDDLjava/util/List;Lorg/xbet/spin_and_win/domain/model/SpinAndWinGameStateEnum;DLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, v1}, Lorg/xbet/spin_and_win/data/a;->j(Ldz0/b;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final c()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/Collection;

    .line 8
    .line 9
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->C1(Ljava/util/Collection;)Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public final e()Ldz0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/a;->c:Ldz0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()D
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/data/a;->d()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-nez v1, :cond_0

    .line 10
    .line 11
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Ldz0/a;

    .line 16
    .line 17
    invoke-virtual {v0}, Ldz0/a;->c()D

    .line 18
    .line 19
    .line 20
    move-result-wide v0

    .line 21
    return-wide v0

    .line 22
    :cond_0
    const-wide/16 v0, 0x0

    .line 23
    .line 24
    return-wide v0
.end method

.method public final g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/a;->b:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final i(Ldz0/a;)V
    .locals 5
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/data/a;->d()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-interface {v0, v1}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    :cond_0
    invoke-interface {v1}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    if-eqz v2, :cond_1

    .line 18
    .line 19
    invoke-interface {v1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    move-object v3, v2

    .line 24
    check-cast v3, Ldz0/a;

    .line 25
    .line 26
    invoke-virtual {p1}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    .line 31
    .line 32
    .line 33
    move-result v4

    .line 34
    invoke-virtual {v3}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    if-ne v4, v3, :cond_0

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    const/4 v2, 0x0

    .line 46
    :goto_0
    check-cast v2, Ldz0/a;

    .line 47
    .line 48
    if-eqz v2, :cond_2

    .line 49
    .line 50
    invoke-interface {v0, v2}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 51
    .line 52
    .line 53
    move-result p1

    .line 54
    invoke-interface {v0, p1}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    check-cast p1, Ldz0/a;

    .line 59
    .line 60
    :cond_2
    iget-object p1, p0, Lorg/xbet/spin_and_win/data/a;->a:Lkotlinx/coroutines/flow/V;

    .line 61
    .line 62
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public final j(Ldz0/b;)V
    .locals 0
    .param p1    # Ldz0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/spin_and_win/data/a;->c:Ldz0/b;

    .line 2
    .line 3
    return-void
.end method

.method public final k(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V
    .locals 0
    .param p1    # Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/spin_and_win/data/a;->b:Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 2
    .line 3
    return-void
.end method
