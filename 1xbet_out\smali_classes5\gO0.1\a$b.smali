.class public final LgO0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LgO0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgO0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LgO0/a$b$a;,
        LgO0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LgO0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LdO0/b;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_characterstic_statistic/data/repository/TeamsCharacteristicsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjO0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LlO0/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/viewmodels/TeamCharacteristicsStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LgO0/a$b;->a:LgO0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p14}, LgO0/a$b;->b(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;LgO0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p14}, LgO0/a$b;-><init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LgO0/a$b;->c(Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;)Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;LSX0/a;Li8/l;Ljava/lang/Long;Lc8/h;)V
    .locals 10

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iput-object v0, p0, LgO0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {v0}, LdO0/c;->a(LBc/a;)LdO0/c;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, LgO0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance v0, LgO0/a$b$a;

    .line 14
    .line 15
    invoke-direct {v0, p1}, LgO0/a$b$a;-><init>(LQW0/c;)V

    .line 16
    .line 17
    .line 18
    iput-object v0, p0, LgO0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    iput-object v0, p0, LgO0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    iget-object v1, p0, LgO0/a$b;->c:Ldagger/internal/h;

    .line 27
    .line 28
    iget-object v2, p0, LgO0/a$b;->d:Ldagger/internal/h;

    .line 29
    .line 30
    invoke-static {v1, v2, v0}, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_characterstic_statistic/data/repository/a;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    iput-object v0, p0, LgO0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {v0}, LjO0/b;->a(LBc/a;)LjO0/b;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iput-object v0, p0, LgO0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {}, LlO0/b;->a()LlO0/b;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-static {v0}, LlO0/d;->a(LBc/a;)LlO0/d;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    iput-object v0, p0, LgO0/a$b;->h:Ldagger/internal/h;

    .line 51
    .line 52
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, LgO0/a$b;->i:Ldagger/internal/h;

    .line 57
    .line 58
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iput-object v0, p0, LgO0/a$b;->j:Ldagger/internal/h;

    .line 63
    .line 64
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, LgO0/a$b;->k:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iput-object v0, p0, LgO0/a$b;->l:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, LgO0/a$b;->m:Ldagger/internal/h;

    .line 81
    .line 82
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    iput-object v0, p0, LgO0/a$b;->n:Ldagger/internal/h;

    .line 87
    .line 88
    new-instance v0, LgO0/a$b$b;

    .line 89
    .line 90
    invoke-direct {v0, p2}, LgO0/a$b$b;-><init>(LEN0/f;)V

    .line 91
    .line 92
    .line 93
    iput-object v0, p0, LgO0/a$b;->o:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static {v0}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    iput-object v0, p0, LgO0/a$b;->p:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    iput-object v0, p0, LgO0/a$b;->q:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v1, p0, LgO0/a$b;->d:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {v1, v0}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    iput-object v0, p0, LgO0/a$b;->r:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v0, p0, LgO0/a$b;->o:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static {v0}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    iput-object v0, p0, LgO0/a$b;->s:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object v0, p0, LgO0/a$b;->o:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static {v0}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    iput-object v0, p0, LgO0/a$b;->t:Ldagger/internal/h;

    .line 130
    .line 131
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    iput-object v0, p0, LgO0/a$b;->u:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object v1, p0, LgO0/a$b;->p:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object v2, p0, LgO0/a$b;->r:Ldagger/internal/h;

    .line 140
    .line 141
    iget-object v3, p0, LgO0/a$b;->s:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object v4, p0, LgO0/a$b;->m:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v5, p0, LgO0/a$b;->t:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object v6, p0, LgO0/a$b;->k:Ldagger/internal/h;

    .line 148
    .line 149
    move-object/from16 p6, v0

    .line 150
    .line 151
    move-object p1, v1

    .line 152
    move-object p2, v2

    .line 153
    move-object p3, v3

    .line 154
    move-object p4, v4

    .line 155
    move-object p5, v5

    .line 156
    move-object/from16 p7, v6

    .line 157
    .line 158
    invoke-static/range {p1 .. p7}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    iput-object v0, p0, LgO0/a$b;->v:Ldagger/internal/h;

    .line 163
    .line 164
    iget-object v1, p0, LgO0/a$b;->g:Ldagger/internal/h;

    .line 165
    .line 166
    iget-object v2, p0, LgO0/a$b;->h:Ldagger/internal/h;

    .line 167
    .line 168
    iget-object v3, p0, LgO0/a$b;->i:Ldagger/internal/h;

    .line 169
    .line 170
    iget-object v4, p0, LgO0/a$b;->j:Ldagger/internal/h;

    .line 171
    .line 172
    iget-object v5, p0, LgO0/a$b;->k:Ldagger/internal/h;

    .line 173
    .line 174
    iget-object v6, p0, LgO0/a$b;->l:Ldagger/internal/h;

    .line 175
    .line 176
    iget-object v7, p0, LgO0/a$b;->m:Ldagger/internal/h;

    .line 177
    .line 178
    iget-object v8, p0, LgO0/a$b;->n:Ldagger/internal/h;

    .line 179
    .line 180
    iget-object v9, p0, LgO0/a$b;->d:Ldagger/internal/h;

    .line 181
    .line 182
    move-object/from16 p9, v0

    .line 183
    .line 184
    move-object p1, v1

    .line 185
    move-object p2, v2

    .line 186
    move-object p3, v3

    .line 187
    move-object p4, v4

    .line 188
    move-object p5, v5

    .line 189
    move-object/from16 p6, v6

    .line 190
    .line 191
    move-object/from16 p7, v7

    .line 192
    .line 193
    move-object/from16 p8, v8

    .line 194
    .line 195
    move-object/from16 p10, v9

    .line 196
    .line 197
    invoke-static/range {p1 .. p10}, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/viewmodels/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/viewmodels/a;

    .line 198
    .line 199
    .line 200
    move-result-object v0

    .line 201
    iput-object v0, p0, LgO0/a$b;->w:Ldagger/internal/h;

    .line 202
    .line 203
    return-void
.end method

.method public final c(Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;)Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LgO0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/c;->a(Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/viewmodels/TeamCharacteristicsStatisticViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LgO0/a$b;->w:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LgO0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
