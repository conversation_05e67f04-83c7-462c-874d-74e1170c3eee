.class public final synthetic LkZ0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

.field public final synthetic b:Landroid/content/Context;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LkZ0/d;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    iput-object p2, p0, LkZ0/d;->b:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LkZ0/d;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    iget-object v1, p0, LkZ0/d;->b:Landroid/content/Context;

    invoke-static {v0, v1}, Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;->b(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;Landroid/content/Context;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
