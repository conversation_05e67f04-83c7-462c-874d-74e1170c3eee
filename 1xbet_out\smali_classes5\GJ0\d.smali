.class public final LGJ0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u000eH\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001b\u00a8\u0006\u001c"
    }
    d2 = {
        "LGJ0/d;",
        "LQW0/a;",
        "Lf8/g;",
        "serviceGenerator",
        "Lc8/h;",
        "requestParamsDataSource",
        "LCJ0/a;",
        "playersStatisticCricketLocalDataSource",
        "LSX0/a;",
        "lottieConfigurator",
        "LHX0/e;",
        "resourceManager",
        "<init>",
        "(Lf8/g;Lc8/h;LCJ0/a;LSX0/a;LHX0/e;)V",
        "LwX0/c;",
        "router",
        "LGJ0/c;",
        "a",
        "(LwX0/c;)LGJ0/c;",
        "Lf8/g;",
        "b",
        "Lc8/h;",
        "c",
        "LCJ0/a;",
        "d",
        "LSX0/a;",
        "e",
        "LHX0/e;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LCJ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;Lc8/h;LCJ0/a;LSX0/a;LHX0/e;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LCJ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LGJ0/d;->a:Lf8/g;

    .line 5
    .line 6
    iput-object p2, p0, LGJ0/d;->b:Lc8/h;

    .line 7
    .line 8
    iput-object p3, p0, LGJ0/d;->c:LCJ0/a;

    .line 9
    .line 10
    iput-object p4, p0, LGJ0/d;->d:LSX0/a;

    .line 11
    .line 12
    iput-object p5, p0, LGJ0/d;->e:LHX0/e;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a(LwX0/c;)LGJ0/c;
    .locals 7
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LGJ0/a;->a()LGJ0/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LGJ0/d;->a:Lf8/g;

    .line 6
    .line 7
    iget-object v2, p0, LGJ0/d;->b:Lc8/h;

    .line 8
    .line 9
    iget-object v4, p0, LGJ0/d;->c:LCJ0/a;

    .line 10
    .line 11
    iget-object v5, p0, LGJ0/d;->d:LSX0/a;

    .line 12
    .line 13
    iget-object v6, p0, LGJ0/d;->e:LHX0/e;

    .line 14
    .line 15
    move-object v3, p1

    .line 16
    invoke-interface/range {v0 .. v6}, LGJ0/c$a;->a(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)LGJ0/c;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    return-object p1
.end method
