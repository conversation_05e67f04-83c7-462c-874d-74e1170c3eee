.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$loadLolPopularChampions$1"
    f = "TournamentViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "LKo0/a<",
        "Ljava/util/List<",
        "+",
        "Lrq0/a;",
        ">;>;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0012\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "LKo0/a;",
        "",
        "Lrq0/a;",
        "result",
        "",
        "<anonymous>",
        "(LKo0/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;

    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "Ljava/util/List<",
            "Lrq0/a;",
            ">;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LKo0/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->invoke(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LKo0/a;

    .line 14
    .line 15
    instance-of v0, p1, LKo0/a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 20
    .line 21
    new-instance v0, LZx0/a$g;

    .line 22
    .line 23
    sget-object v1, LZx0/f;->a:LZx0/f;

    .line 24
    .line 25
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-direct {v0, v1, v2}, LZx0/a$g;-><init>(LZx0/g;Ljava/util/List;)V

    .line 30
    .line 31
    .line 32
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 33
    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    instance-of v0, p1, LKo0/a$b;

    .line 37
    .line 38
    if-eqz v0, :cond_2

    .line 39
    .line 40
    check-cast p1, LKo0/a$b;

    .line 41
    .line 42
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    check-cast p1, Ljava/util/List;

    .line 47
    .line 48
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    if-eqz v0, :cond_1

    .line 53
    .line 54
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 55
    .line 56
    new-instance v0, LZx0/a$g;

    .line 57
    .line 58
    sget-object v1, LZx0/e;->a:LZx0/e;

    .line 59
    .line 60
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    invoke-direct {v0, v1, v2}, LZx0/a$g;-><init>(LZx0/g;Ljava/util/List;)V

    .line 65
    .line 66
    .line 67
    invoke-static {p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 72
    .line 73
    new-instance v1, LZx0/a$g;

    .line 74
    .line 75
    sget-object v2, LZx0/g$a$b;->a:LZx0/g$a$b;

    .line 76
    .line 77
    invoke-direct {v1, v2, p1}, LZx0/a$g;-><init>(LZx0/g;Ljava/util/List;)V

    .line 78
    .line 79
    .line 80
    invoke-static {v0, v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->n4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;LZx0/a;)V

    .line 81
    .line 82
    .line 83
    :goto_0
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$loadLolPopularChampions$1;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 84
    .line 85
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->S3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lkotlinx/coroutines/x0;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-static {p1}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 90
    .line 91
    .line 92
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 93
    .line 94
    return-object p1

    .line 95
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 96
    .line 97
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 98
    .line 99
    .line 100
    throw p1

    .line 101
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 102
    .line 103
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 104
    .line 105
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    throw p1
.end method
