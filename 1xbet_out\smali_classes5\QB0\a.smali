.class public final LQB0/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LTB0/c;",
        ">;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001BC\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0012\u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\u0003\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LQB0/a;",
        "LA4/e;",
        "LTB0/c;",
        "Lkotlin/Function1;",
        "LRB0/a;",
        "",
        "insightMarketHeaderClickListener",
        "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
        "betEventClickListener",
        "betEventLongClickListener",
        "<init>",
        "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LRB0/a;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/sportgame/markets/impl/presentation/base/i;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LTB0/c;->a:LTB0/c$a;

    .line 2
    .line 3
    invoke-virtual {v0}, LTB0/c$a;->a()Landroidx/recyclerview/widget/i$f;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 11
    .line 12
    invoke-static {p1}, Lorg/xbet/sportgame/markets/impl/presentation/insights/adapter/viewholders/InsightMarketsHeaderAdapterDelegateKt;->n(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    const/4 v0, 0x0

    .line 21
    invoke-static {p2, p3, v0}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/EventBetAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Z)LA4/c;

    .line 22
    .line 23
    .line 24
    move-result-object p2

    .line 25
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-static {}, Lorg/xbet/sportgame/markets/impl/presentation/markets/adapter/viewholders/HiddenMarketsCounterAdapterDelegateKt;->d()LA4/c;

    .line 30
    .line 31
    .line 32
    move-result-object p2

    .line 33
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method
