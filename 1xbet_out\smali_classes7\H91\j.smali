.class public final LH91/j;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;",
        "LD91/e;",
        "a",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;)LD91/e;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;)LD91/e;
    .locals 3
    .param p0    # Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LD91/e;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getId()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->getName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/category/presentation/models/PromotedCategoryUiModel;->P()Z

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    invoke-direct {v0, v1, v2, p0}, LD91/e;-><init>(Ljava/lang/String;Ljava/lang/String;Z)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method
