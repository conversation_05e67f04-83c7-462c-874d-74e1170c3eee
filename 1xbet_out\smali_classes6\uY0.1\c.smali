.class public final LuY0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u00020\u0001*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a\u0013\u0010\u0008\u001a\u00020\u0001*\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LIY0/d;",
        "",
        "chartWidth",
        "LtY0/a;",
        "horizontalDimensions",
        "a",
        "(LIY0/d;FLtY0/a;)F",
        "LuY0/a;",
        "b",
        "(LuY0/a;)F",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LIY0/d;FLtY0/a;)F
    .locals 1
    .param p0    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LIY0/d;->P()F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-interface {p2, v0}, LtY0/a;->c(F)LtY0/a;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-interface {p0}, LIY0/d;->U()LyY0/c;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, LyY0/c;->a()LyY0/b;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-interface {v0}, LyY0/b;->c()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    invoke-interface {p2, v0}, LtY0/a;->d(I)F

    .line 22
    .line 23
    .line 24
    move-result p2

    .line 25
    invoke-interface {p0}, LIY0/d;->O()F

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    sub-float/2addr p2, p1

    .line 30
    mul-float v0, v0, p2

    .line 31
    .line 32
    invoke-interface {p0}, LIY0/d;->S()Z

    .line 33
    .line 34
    .line 35
    move-result p0

    .line 36
    const/4 p1, 0x0

    .line 37
    if-eqz p0, :cond_0

    .line 38
    .line 39
    invoke-static {v0, p1}, Lkotlin/ranges/f;->f(FF)F

    .line 40
    .line 41
    .line 42
    move-result p0

    .line 43
    return p0

    .line 44
    :cond_0
    invoke-static {v0, p1}, Lkotlin/ranges/f;->k(FF)F

    .line 45
    .line 46
    .line 47
    move-result p0

    .line 48
    return p0
.end method

.method public static final b(LuY0/a;)F
    .locals 2
    .param p0    # LuY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LuY0/a;->b()Landroid/graphics/RectF;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/graphics/RectF;->width()F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-interface {p0}, LuY0/a;->j()LtY0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-static {p0, v0, v1}, LuY0/c;->a(LIY0/d;FLtY0/a;)F

    .line 14
    .line 15
    .line 16
    move-result p0

    .line 17
    return p0
.end method
