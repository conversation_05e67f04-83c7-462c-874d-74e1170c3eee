.class final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.presentation.PopularClassicOneXGamesViewModel$onActionBannerClick$2"
    f = "PopularClassicOneXGamesViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

.field final synthetic $position:I

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iput p3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$position:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    iget v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$position:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;-><init>(Lorg/xplatform/banners/api/domain/models/BannerModel;Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 12
    .line 13
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-lez p1, :cond_0

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->z3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a$a;

    .line 38
    .line 39
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 40
    .line 41
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getDeeplink()Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$a$a;-><init>(Ljava/lang/String;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    goto :goto_3

    .line 52
    :cond_0
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 53
    .line 54
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getAction()Z

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    if-eqz p1, :cond_1

    .line 59
    .line 60
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 61
    .line 62
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 67
    .line 68
    .line 69
    move-result p1

    .line 70
    if-lez p1, :cond_1

    .line 71
    .line 72
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->Z3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LwX0/c;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 79
    .line 80
    invoke-static {v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->B3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LwX0/a;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 85
    .line 86
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getSiteLink()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-interface {v0, v1}, LwX0/a;->J(Ljava/lang/String;)Lq4/q;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 95
    .line 96
    .line 97
    goto :goto_3

    .line 98
    :cond_1
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 99
    .line 100
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->C3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Ljava/util/List;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    const/4 v0, 0x0

    .line 109
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 110
    .line 111
    .line 112
    move-result v1

    .line 113
    const/4 v2, -0x1

    .line 114
    if-eqz v1, :cond_3

    .line 115
    .line 116
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 121
    .line 122
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 123
    .line 124
    .line 125
    move-result v1

    .line 126
    const/16 v3, 0x1081

    .line 127
    .line 128
    if-ne v1, v3, :cond_2

    .line 129
    .line 130
    goto :goto_1

    .line 131
    :cond_2
    add-int/lit8 v0, v0, 0x1

    .line 132
    .line 133
    goto :goto_0

    .line 134
    :cond_3
    const/4 v0, -0x1

    .line 135
    :goto_1
    if-eq v0, v2, :cond_4

    .line 136
    .line 137
    iget p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$position:I

    .line 138
    .line 139
    add-int/lit8 p1, p1, -0x1

    .line 140
    .line 141
    goto :goto_2

    .line 142
    :cond_4
    iget p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$position:I

    .line 143
    .line 144
    :goto_2
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 145
    .line 146
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$onActionBannerClick$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 147
    .line 148
    invoke-static {v0, v1, p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->h4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lorg/xplatform/banners/api/domain/models/BannerModel;I)V

    .line 149
    .line 150
    .line 151
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 152
    .line 153
    return-object p1

    .line 154
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 155
    .line 156
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 157
    .line 158
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 159
    .line 160
    .line 161
    throw p1
.end method
