.class public final synthetic LU01/G;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/views/LoadableShapeableImageView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/G;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LU01/G;->b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LU01/G;->a:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    iget-object v1, p0, LU01/G;->b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    check-cast p1, Lcom/bumptech/glide/load/engine/GlideException;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->C(Lkotlin/jvm/functions/Function1;Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Lcom/bumptech/glide/load/engine/GlideException;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
