.class public final Lgc1/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a5\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LTb1/a;",
        "",
        "isVirtual",
        "",
        "style",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;",
        "gameCardCollectionStyle",
        "Lhc1/d;",
        "a",
        "(LTb1/a;ZILHX0/e;Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)Lhc1/d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LTb1/a;ZILHX0/e;Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)Lhc1/d;
    .locals 17
    .param p0    # LTb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/16 v0, 0xa

    .line 2
    .line 3
    if-eqz p1, :cond_2

    .line 4
    .line 5
    invoke-virtual/range {p0 .. p0}, LTb1/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v10, Ljava/util/ArrayList;

    .line 10
    .line 11
    invoke-static {v1, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    invoke-direct {v10, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_0

    .line 27
    .line 28
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    check-cast v2, Lorg/xplatform/aggregator/api/model/Game;

    .line 33
    .line 34
    new-instance v3, LHZ0/a;

    .line 35
    .line 36
    invoke-virtual/range {p0 .. p0}, LTb1/a;->d()J

    .line 37
    .line 38
    .line 39
    move-result-wide v4

    .line 40
    long-to-int v5, v4

    .line 41
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/Game;->getLogoUrl()Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    invoke-static {v2}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-static {v2}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-virtual/range {p0 .. p0}, LTb1/a;->f()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    invoke-direct {v3, v5, v2, v4}, LHZ0/a;-><init>(ILL11/c;Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    invoke-interface {v10, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_0
    invoke-virtual/range {p0 .. p0}, LTb1/a;->f()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v8

    .line 68
    invoke-virtual/range {p0 .. p0}, LTb1/a;->d()J

    .line 69
    .line 70
    .line 71
    move-result-wide v6

    .line 72
    invoke-virtual/range {p0 .. p0}, LTb1/a;->c()Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    new-instance v5, Ljava/util/ArrayList;

    .line 77
    .line 78
    invoke-static {v1, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    invoke-direct {v5, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 83
    .line 84
    .line 85
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 90
    .line 91
    .line 92
    move-result v1

    .line 93
    if-eqz v1, :cond_1

    .line 94
    .line 95
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v11, v1

    .line 100
    check-cast v11, Lorg/xplatform/aggregator/api/model/Game;

    .line 101
    .line 102
    invoke-virtual/range {p0 .. p0}, LTb1/a;->a()Z

    .line 103
    .line 104
    .line 105
    move-result v13

    .line 106
    invoke-virtual/range {p0 .. p0}, LTb1/a;->b()Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    invoke-interface {v1, v11}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 111
    .line 112
    .line 113
    move-result v12

    .line 114
    const/4 v15, 0x1

    .line 115
    move-object/from16 v14, p3

    .line 116
    .line 117
    move-object/from16 v16, p4

    .line 118
    .line 119
    invoke-static/range {v11 .. v16}, Lgc1/b;->a(Lorg/xplatform/aggregator/api/model/Game;ZZLHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)LN21/k;

    .line 120
    .line 121
    .line 122
    move-result-object v1

    .line 123
    invoke-interface {v5, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 124
    .line 125
    .line 126
    goto :goto_1

    .line 127
    :cond_1
    sget-object v9, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 128
    .line 129
    new-instance v2, Lhc1/d;

    .line 130
    .line 131
    const/16 v13, 0x100

    .line 132
    .line 133
    const/4 v14, 0x0

    .line 134
    const/4 v3, 0x1

    .line 135
    const/4 v11, 0x0

    .line 136
    const/4 v12, 0x0

    .line 137
    move/from16 v4, p2

    .line 138
    .line 139
    invoke-direct/range {v2 .. v14}, Lhc1/d;-><init>(ZILjava/util/List;JLjava/lang/String;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 140
    .line 141
    .line 142
    return-object v2

    .line 143
    :cond_2
    invoke-virtual/range {p0 .. p0}, LTb1/a;->c()Ljava/util/List;

    .line 144
    .line 145
    .line 146
    move-result-object v1

    .line 147
    new-instance v6, Ljava/util/ArrayList;

    .line 148
    .line 149
    invoke-static {v1, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 150
    .line 151
    .line 152
    move-result v0

    .line 153
    invoke-direct {v6, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 154
    .line 155
    .line 156
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 161
    .line 162
    .line 163
    move-result v1

    .line 164
    if-eqz v1, :cond_6

    .line 165
    .line 166
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    move-object v11, v1

    .line 171
    check-cast v11, Lorg/xplatform/aggregator/api/model/Game;

    .line 172
    .line 173
    invoke-virtual/range {p0 .. p0}, LTb1/a;->a()Z

    .line 174
    .line 175
    .line 176
    move-result v13

    .line 177
    invoke-virtual/range {p0 .. p0}, LTb1/a;->b()Ljava/util/List;

    .line 178
    .line 179
    .line 180
    move-result-object v1

    .line 181
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 182
    .line 183
    .line 184
    move-result-object v1

    .line 185
    :cond_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 186
    .line 187
    .line 188
    move-result v2

    .line 189
    if-eqz v2, :cond_4

    .line 190
    .line 191
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 192
    .line 193
    .line 194
    move-result-object v2

    .line 195
    move-object v3, v2

    .line 196
    check-cast v3, Lorg/xplatform/aggregator/api/model/Game;

    .line 197
    .line 198
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 199
    .line 200
    .line 201
    move-result-wide v3

    .line 202
    invoke-virtual {v11}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 203
    .line 204
    .line 205
    move-result-wide v7

    .line 206
    cmp-long v5, v3, v7

    .line 207
    .line 208
    if-nez v5, :cond_3

    .line 209
    .line 210
    goto :goto_3

    .line 211
    :cond_4
    const/4 v2, 0x0

    .line 212
    :goto_3
    if-eqz v2, :cond_5

    .line 213
    .line 214
    const/4 v1, 0x1

    .line 215
    const/4 v12, 0x1

    .line 216
    goto :goto_4

    .line 217
    :cond_5
    const/4 v1, 0x0

    .line 218
    const/4 v12, 0x0

    .line 219
    :goto_4
    const/4 v15, 0x0

    .line 220
    move-object/from16 v14, p3

    .line 221
    .line 222
    move-object/from16 v16, p4

    .line 223
    .line 224
    invoke-static/range {v11 .. v16}, Lgc1/b;->a(Lorg/xplatform/aggregator/api/model/Game;ZZLHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)LN21/k;

    .line 225
    .line 226
    .line 227
    move-result-object v1

    .line 228
    invoke-interface {v6, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 229
    .line 230
    .line 231
    goto :goto_2

    .line 232
    :cond_6
    invoke-virtual/range {p0 .. p0}, LTb1/a;->d()J

    .line 233
    .line 234
    .line 235
    move-result-wide v7

    .line 236
    sget-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->Companion:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;

    .line 237
    .line 238
    invoke-virtual/range {p0 .. p0}, LTb1/a;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 239
    .line 240
    .line 241
    move-result-object v1

    .line 242
    invoke-virtual {v0, v1}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;->a(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)I

    .line 243
    .line 244
    .line 245
    move-result v0

    .line 246
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/D;->b(I)I

    .line 247
    .line 248
    .line 249
    move-result v12

    .line 250
    invoke-virtual/range {p0 .. p0}, LTb1/a;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 251
    .line 252
    .line 253
    move-result-object v10

    .line 254
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 255
    .line 256
    .line 257
    move-result-object v11

    .line 258
    new-instance v3, Lhc1/d;

    .line 259
    .line 260
    const-string v9, ""

    .line 261
    .line 262
    const/4 v13, 0x0

    .line 263
    const/4 v4, 0x0

    .line 264
    move/from16 v5, p2

    .line 265
    .line 266
    invoke-direct/range {v3 .. v13}, Lhc1/d;-><init>(ZILjava/util/List;JLjava/lang/String;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;II)V

    .line 267
    .line 268
    .line 269
    return-object v3
.end method
