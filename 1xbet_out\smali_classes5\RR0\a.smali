.class public final LRR0/a;
.super Ljava/lang/Object;


# static fields
.field public static btnApply:I = 0x7f0a0298

.field public static btnCancel:I = 0x7f0a02a1

.field public static btnClose:I = 0x7f0a02a6

.field public static card:I = 0x7f0a0366

.field public static cell:I = 0x7f0a03a1

.field public static counter:I = 0x7f0a05a0

.field public static ffProgress:I = 0x7f0a07c6

.field public static icon:I = 0x7f0a0ac8

.field public static image:I = 0x7f0a0ae2

.field public static lLoader:I = 0x7f0a0dc3

.field public static left_guideline:I = 0x7f0a0df0

.field public static navigationBar:I = 0x7f0a0faf

.field public static nsvAuthDialog:I = 0x7f0a0fea

.field public static recycler_view:I = 0x7f0a11b9

.field public static right_guideline:I = 0x7f0a1210

.field public static title:I = 0x7f0a1808

.field public static tvDescription:I = 0x7f0a1a20

.field public static tvTitle:I = 0x7f0a1cac


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
