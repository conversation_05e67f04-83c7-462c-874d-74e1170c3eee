.class final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.data.repositories.AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1"
    f = "AggregatorFavoritesRepositoryImpl.kt"
    l = {
        0x102,
        0x108
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->i(IJLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\u0010\u0000\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "token",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $endPoint:Ljava/lang/String;

.field final synthetic $hasAggregatorBrands:Z

.field final synthetic $limit:I

.field final synthetic $partitionId:J

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILjava/lang/String;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
            "I",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$partitionId:J

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iput p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$limit:I

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$endPoint:Ljava/lang/String;

    iput-boolean p6, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$hasAggregatorBrands:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$partitionId:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iget v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$limit:I

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$endPoint:Ljava/lang/String;

    iget-boolean v6, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$hasAggregatorBrands:Z

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;-><init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILjava/lang/String;ZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    move-object v9, p0

    .line 19
    goto :goto_2

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-object v9, p0

    .line 32
    goto :goto_0

    .line 33
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->L$0:Ljava/lang/Object;

    .line 37
    .line 38
    move-object v6, p1

    .line 39
    check-cast v6, Ljava/lang/String;

    .line 40
    .line 41
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$partitionId:J

    .line 42
    .line 43
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 44
    .line 45
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 46
    .line 47
    .line 48
    move-result-wide v7

    .line 49
    cmp-long p1, v4, v7

    .line 50
    .line 51
    if-nez p1, :cond_4

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->t(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 56
    .line 57
    .line 58
    move-result-object v4

    .line 59
    iget v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$limit:I

    .line 60
    .line 61
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 62
    .line 63
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->u(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lc8/h;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-interface {p1}, Lc8/h;->a()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v8

    .line 71
    iput v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->label:I

    .line 72
    .line 73
    const/4 v7, 0x0

    .line 74
    const/4 v10, 0x4

    .line 75
    const/4 v11, 0x0

    .line 76
    move-object v9, p0

    .line 77
    invoke-static/range {v4 .. v11}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->x(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;ILjava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    if-ne p1, v0, :cond_3

    .line 82
    .line 83
    goto :goto_1

    .line 84
    :cond_3
    :goto_0
    check-cast p1, Le8/b;

    .line 85
    .line 86
    goto :goto_3

    .line 87
    :cond_4
    move-object v9, p0

    .line 88
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 89
    .line 90
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->t(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 91
    .line 92
    .line 93
    move-result-object v4

    .line 94
    iget v5, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$limit:I

    .line 95
    .line 96
    move-object v11, v9

    .line 97
    iget-wide v8, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$partitionId:J

    .line 98
    .line 99
    iget-object p1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 100
    .line 101
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->u(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lc8/h;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    invoke-interface {p1}, Lc8/h;->a()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v10

    .line 109
    iput v2, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->label:I

    .line 110
    .line 111
    const/4 v7, 0x0

    .line 112
    const/4 v12, 0x4

    .line 113
    const/4 v13, 0x0

    .line 114
    invoke-static/range {v4 .. v13}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->z(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;ILjava/lang/String;IJLjava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    move-object v9, v11

    .line 119
    if-ne p1, v0, :cond_5

    .line 120
    .line 121
    :goto_1
    return-object v0

    .line 122
    :cond_5
    :goto_2
    check-cast p1, Le8/b;

    .line 123
    .line 124
    :goto_3
    invoke-virtual {p1}, Le8/b;->a()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    check-cast p1, LL91/e;

    .line 129
    .line 130
    invoke-virtual {p1}, LL91/e;->b()Ljava/util/List;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    if-eqz p1, :cond_7

    .line 135
    .line 136
    iget-object v0, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$endPoint:Ljava/lang/String;

    .line 137
    .line 138
    iget-boolean v1, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGamesForPopularSearch$games$1;->$hasAggregatorBrands:Z

    .line 139
    .line 140
    new-instance v2, Ljava/util/ArrayList;

    .line 141
    .line 142
    const/16 v3, 0xa

    .line 143
    .line 144
    invoke-static {p1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 145
    .line 146
    .line 147
    move-result v3

    .line 148
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 149
    .line 150
    .line 151
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    :goto_4
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 156
    .line 157
    .line 158
    move-result v3

    .line 159
    if-eqz v3, :cond_6

    .line 160
    .line 161
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v3

    .line 165
    check-cast v3, LL91/d;

    .line 166
    .line 167
    invoke-static {v3, v0, v1}, LA91/d;->c(LL91/d;Ljava/lang/String;Z)Lorg/xplatform/aggregator/api/model/Game;

    .line 168
    .line 169
    .line 170
    move-result-object v3

    .line 171
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 172
    .line 173
    .line 174
    goto :goto_4

    .line 175
    :cond_6
    return-object v2

    .line 176
    :cond_7
    const/4 p1, 0x0

    .line 177
    return-object p1
.end method
