.class public final LoM0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoM0/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LoM0/e$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ#\u0010\u0011\u001a\u00020\u0010*\u00020\r2\u0006\u0010\u000e\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u0013R \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R\u0014\u0010\u0019\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0018\u00a8\u0006\u001a"
    }
    d2 = {
        "LoM0/e;",
        "LoM0/a;",
        "Landroid/view/View;",
        "view",
        "Lkotlin/Function1;",
        "LzM0/a;",
        "",
        "listener",
        "<init>",
        "(Landroid/view/View;Lkotlin/jvm/functions/Function1;)V",
        "netCell",
        "a",
        "(LzM0/a;)V",
        "LuM0/b;",
        "netCellModel",
        "game",
        "",
        "d",
        "(LuM0/b;LzM0/a;LuM0/b;)Ljava/lang/String;",
        "Landroid/view/View;",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "LpM0/o;",
        "c",
        "LpM0/o;",
        "binding",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "LzM0/a;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LpM0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LzM0/a;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LoM0/e;->a:Landroid/view/View;

    .line 5
    .line 6
    iput-object p2, p0, LoM0/e;->b:Lkotlin/jvm/functions/Function1;

    .line 7
    .line 8
    invoke-static {p1}, LpM0/o;->a(Landroid/view/View;)LpM0/o;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    iput-object p1, p0, LoM0/e;->c:LpM0/o;

    .line 13
    .line 14
    return-void
.end method

.method public static synthetic b(LoM0/e;LzM0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LoM0/e;->c(LoM0/e;LzM0/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LoM0/e;LzM0/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, LoM0/e;->b:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public a(LzM0/a;)V
    .locals 9
    .param p1    # LzM0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LoM0/e;->c:LpM0/o;

    .line 2
    .line 3
    iget-object v0, v0, LpM0/o;->c:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;

    .line 4
    .line 5
    invoke-virtual {p1}, LzM0/a;->h()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setTopFirstTeamLogo(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p1}, LzM0/a;->f()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setBotFirstTeamLogo(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1}, LzM0/a;->m()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setTopSecondTeamLogo(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1}, LzM0/a;->k()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setBotSecondTeamLogo(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, LzM0/a;->j()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setFirstTeamsName(Ljava/lang/CharSequence;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p1}, LzM0/a;->o()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setSecondTeamsName(Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p1}, LzM0/a;->a()Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, LuM0/b;

    .line 56
    .line 57
    if-eqz v0, :cond_0

    .line 58
    .line 59
    iget-object v1, p0, LoM0/e;->a:Landroid/view/View;

    .line 60
    .line 61
    new-instance v2, LoM0/d;

    .line 62
    .line 63
    invoke-direct {v2, p0, p1}, LoM0/d;-><init>(LoM0/e;LzM0/a;)V

    .line 64
    .line 65
    .line 66
    const/4 v3, 0x1

    .line 67
    const/4 v4, 0x0

    .line 68
    invoke-static {v1, v4, v2, v3, v4}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 69
    .line 70
    .line 71
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 72
    .line 73
    iget-object v1, v1, LpM0/o;->c:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;

    .line 74
    .line 75
    invoke-virtual {p0, v0, p1, v0}, LoM0/e;->d(LuM0/b;LzM0/a;LuM0/b;)Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v2

    .line 79
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setScore(Ljava/lang/CharSequence;)V

    .line 80
    .line 81
    .line 82
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 83
    .line 84
    iget-object v1, v1, LpM0/o;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;

    .line 85
    .line 86
    sget-object v2, Ll8/b;->a:Ll8/b;

    .line 87
    .line 88
    iget-object v3, p0, LoM0/e;->a:Landroid/view/View;

    .line 89
    .line 90
    invoke-virtual {v3}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 91
    .line 92
    .line 93
    move-result-object v3

    .line 94
    invoke-static {v3}, Landroid/text/format/DateFormat;->is24HourFormat(Landroid/content/Context;)Z

    .line 95
    .line 96
    .line 97
    move-result v3

    .line 98
    invoke-virtual {v0}, LuM0/b;->a()J

    .line 99
    .line 100
    .line 101
    move-result-wide v4

    .line 102
    const/4 v7, 0x4

    .line 103
    const/4 v8, 0x0

    .line 104
    const/4 v6, 0x0

    .line 105
    invoke-static/range {v2 .. v8}, Ll8/b;->E(Ll8/b;ZJLjava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;->setInfoText(Ljava/lang/CharSequence;)V

    .line 110
    .line 111
    .line 112
    iget-object v0, p0, LoM0/e;->c:LpM0/o;

    .line 113
    .line 114
    iget-object v0, v0, LpM0/o;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;

    .line 115
    .line 116
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    invoke-virtual {v1}, LuM0/f;->a()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    invoke-virtual {p1}, LuM0/f;->c()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;->setSeeding(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 133
    .line 134
    .line 135
    return-void

    .line 136
    :cond_0
    iget-object v0, p0, LoM0/e;->c:LpM0/o;

    .line 137
    .line 138
    iget-object v0, v0, LpM0/o;->c:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;

    .line 139
    .line 140
    iget-object v1, p0, LoM0/e;->a:Landroid/view/View;

    .line 141
    .line 142
    invoke-virtual {v1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    sget v2, Lpb/k;->vs:I

    .line 147
    .line 148
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setScore(Ljava/lang/CharSequence;)V

    .line 153
    .line 154
    .line 155
    iget-object v0, p0, LoM0/e;->c:LpM0/o;

    .line 156
    .line 157
    iget-object v0, v0, LpM0/o;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;

    .line 158
    .line 159
    const-string v1, ""

    .line 160
    .line 161
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;->setInfoText(Ljava/lang/CharSequence;)V

    .line 162
    .line 163
    .line 164
    iget-object v0, p0, LoM0/e;->c:LpM0/o;

    .line 165
    .line 166
    iget-object v0, v0, LpM0/o;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;

    .line 167
    .line 168
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 169
    .line 170
    .line 171
    move-result-object v1

    .line 172
    invoke-virtual {v1}, LuM0/f;->a()Ljava/lang/String;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    invoke-virtual {p1}, LzM0/a;->c()LuM0/f;

    .line 177
    .line 178
    .line 179
    move-result-object p1

    .line 180
    invoke-virtual {p1}, LuM0/f;->c()Ljava/lang/String;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoSeeding;->setSeeding(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 185
    .line 186
    .line 187
    return-void
.end method

.method public final d(LuM0/b;LzM0/a;LuM0/b;)Ljava/lang/String;
    .locals 6

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {p1}, LxM0/a;->a(LuM0/b;)Z

    .line 3
    .line 4
    .line 5
    move-result v1

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x1

    .line 8
    if-nez v1, :cond_2

    .line 9
    .line 10
    invoke-virtual {p2}, LzM0/a;->i()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-nez v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    invoke-virtual {p2}, LzM0/a;->n()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p2

    .line 25
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 26
    .line 27
    .line 28
    move-result p2

    .line 29
    if-nez p2, :cond_1

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_1
    const/4 p2, 0x0

    .line 33
    goto :goto_1

    .line 34
    :cond_2
    :goto_0
    const/4 p2, 0x1

    .line 35
    :goto_1
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 36
    .line 37
    iget-object v1, v1, LpM0/o;->d:Lorg/xbet/uikit_sport/eventcard/top/StatisticsHeader;

    .line 38
    .line 39
    invoke-virtual {p3}, LuM0/b;->h()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 40
    .line 41
    .line 42
    move-result-object v4

    .line 43
    sget-object v5, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->GAME_STATUS_LIVE:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 44
    .line 45
    if-ne v4, v5, :cond_3

    .line 46
    .line 47
    const/4 v4, 0x1

    .line 48
    goto :goto_2

    .line 49
    :cond_3
    const/4 v4, 0x0

    .line 50
    :goto_2
    if-eqz v4, :cond_4

    .line 51
    .line 52
    const/4 v4, 0x0

    .line 53
    goto :goto_3

    .line 54
    :cond_4
    const/16 v4, 0x8

    .line 55
    .line 56
    :goto_3
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p3}, LuM0/b;->h()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    sget-object v4, LoM0/e$a;->a:[I

    .line 64
    .line 65
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    aget v1, v4, v1

    .line 70
    .line 71
    if-ne v1, v3, :cond_8

    .line 72
    .line 73
    invoke-virtual {p3}, LuM0/b;->i()I

    .line 74
    .line 75
    .line 76
    move-result v1

    .line 77
    if-eqz v1, :cond_7

    .line 78
    .line 79
    if-eq v1, v3, :cond_6

    .line 80
    .line 81
    if-eq v1, v0, :cond_5

    .line 82
    .line 83
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 84
    .line 85
    invoke-virtual {v1}, LpM0/o;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    invoke-static {v1}, LNN0/k;->g(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 90
    .line 91
    .line 92
    goto :goto_4

    .line 93
    :cond_5
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 94
    .line 95
    invoke-virtual {v1}, LpM0/o;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    invoke-static {v1}, LNN0/k;->e(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 100
    .line 101
    .line 102
    goto :goto_4

    .line 103
    :cond_6
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 104
    .line 105
    invoke-virtual {v1}, LpM0/o;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    invoke-static {v1}, LNN0/k;->c(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 110
    .line 111
    .line 112
    goto :goto_4

    .line 113
    :cond_7
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 114
    .line 115
    invoke-virtual {v1}, LpM0/o;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    invoke-static {v1}, LNN0/k;->a(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 120
    .line 121
    .line 122
    goto :goto_4

    .line 123
    :cond_8
    iget-object v1, p0, LoM0/e;->c:LpM0/o;

    .line 124
    .line 125
    invoke-virtual {v1}, LpM0/o;->b()Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    invoke-static {v1}, LNN0/k;->g(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;)V

    .line 130
    .line 131
    .line 132
    :goto_4
    if-eqz p2, :cond_a

    .line 133
    .line 134
    invoke-virtual {p3}, LuM0/b;->h()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 135
    .line 136
    .line 137
    move-result-object p2

    .line 138
    if-ne p2, v5, :cond_9

    .line 139
    .line 140
    goto :goto_5

    .line 141
    :cond_9
    iget-object p1, p0, LoM0/e;->a:Landroid/view/View;

    .line 142
    .line 143
    invoke-virtual {p1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    sget p2, Lpb/k;->vs:I

    .line 148
    .line 149
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    return-object p1

    .line 154
    :cond_a
    :goto_5
    iget-object p2, p0, LoM0/e;->a:Landroid/view/View;

    .line 155
    .line 156
    invoke-virtual {p2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 157
    .line 158
    .line 159
    move-result-object p2

    .line 160
    sget p3, Lpb/k;->placeholder_score_two_teams:I

    .line 161
    .line 162
    invoke-virtual {p1}, LuM0/b;->d()I

    .line 163
    .line 164
    .line 165
    move-result v1

    .line 166
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    invoke-virtual {p1}, LuM0/b;->g()I

    .line 171
    .line 172
    .line 173
    move-result p1

    .line 174
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    new-array v0, v0, [Ljava/lang/Object;

    .line 179
    .line 180
    aput-object v1, v0, v2

    .line 181
    .line 182
    aput-object p1, v0, v3

    .line 183
    .line 184
    invoke-virtual {p2, p3, v0}, Landroid/content/res/Resources;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 185
    .line 186
    .line 187
    move-result-object p1

    .line 188
    return-object p1
.end method
