.class final Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.popular_classic.presentation.PopularClassicOneXGamesViewModel$getJackpot$3$1"
    f = "PopularClassicOneXGamesViewModel.kt"
    l = {
        0x170,
        0x171
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
        "+",
        "Lk50/c$a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "it",
        "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;",
        "Lk50/c$a;",
        "<anonymous>",
        "(J)Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;

    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public final invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c<",
            "Lk50/c$a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 2
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, v0, v1, p2}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->L$0:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, LP40/c;

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto :goto_2

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->a4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lcom/xbet/onexuser/domain/user/c;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/user/c;->j()Z

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    if-eqz p1, :cond_5

    .line 49
    .line 50
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 51
    .line 52
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->Q3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)LR40/c;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput v3, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->label:I

    .line 57
    .line 58
    invoke-virtual {p1, p0}, LR40/c;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    if-ne p1, v0, :cond_3

    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_3
    :goto_0
    check-cast p1, Lkotlin/Pair;

    .line 66
    .line 67
    invoke-virtual {p1}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    check-cast v1, LP40/c;

    .line 72
    .line 73
    invoke-virtual {p1}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    check-cast p1, Ljava/lang/Number;

    .line 78
    .line 79
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 80
    .line 81
    .line 82
    move-result-wide v3

    .line 83
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 84
    .line 85
    invoke-static {p1}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->K3(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;)Lgk/b;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    iput-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->L$0:Ljava/lang/Object;

    .line 90
    .line 91
    iput v2, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->label:I

    .line 92
    .line 93
    invoke-interface {p1, v3, v4, p0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    if-ne p1, v0, :cond_4

    .line 98
    .line 99
    :goto_1
    return-object v0

    .line 100
    :cond_4
    move-object v0, v1

    .line 101
    :goto_2
    check-cast p1, Lbk/a;

    .line 102
    .line 103
    new-instance v1, LP40/b;

    .line 104
    .line 105
    invoke-virtual {p1}, Lbk/a;->o()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    invoke-direct {v1, v0, p1}, LP40/b;-><init>(LP40/c;Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$getJackpot$3$1;->this$0:Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;

    .line 113
    .line 114
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 115
    .line 116
    .line 117
    move-result-wide v2

    .line 118
    invoke-static {v2, v3}, LHc/a;->f(J)Ljava/lang/Long;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    invoke-static {p1, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;->n4(Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel;Ljava/lang/Long;)V

    .line 123
    .line 124
    .line 125
    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;

    .line 126
    .line 127
    new-instance v0, Lk50/c$a;

    .line 128
    .line 129
    invoke-direct {v0, v1}, Lk50/c$a;-><init>(LP40/b;)V

    .line 130
    .line 131
    .line 132
    invoke-direct {p1, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$d;-><init>(Ljava/lang/Object;)V

    .line 133
    .line 134
    .line 135
    return-object p1

    .line 136
    :cond_5
    new-instance p1, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;

    .line 137
    .line 138
    new-instance v0, Lcom/xbet/onexuser/domain/exceptions/UnauthorizedException;

    .line 139
    .line 140
    invoke-direct {v0}, Lcom/xbet/onexuser/domain/exceptions/UnauthorizedException;-><init>()V

    .line 141
    .line 142
    .line 143
    invoke-direct {p1, v0}, Lorg/xbet/games_section/feature/popular_classic/presentation/PopularClassicOneXGamesViewModel$c$c;-><init>(Ljava/lang/Throwable;)V

    .line 144
    .line 145
    .line 146
    return-object p1
.end method
