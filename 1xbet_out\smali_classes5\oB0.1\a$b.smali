.class public final LoB0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LoB0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LoB0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LoB0/a$b$a;,
        LoB0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LoB0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LhB0/p;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpB0/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LKA0/c;LQW0/c;LSX0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LoB0/a$b;->a:LoB0/a$b;

    .line 4
    invoke-virtual {p0, p1, p2, p3}, LoB0/a$b;->b(LKA0/c;LQW0/c;LSX0/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LKA0/c;LQW0/c;LSX0/a;LoB0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, LoB0/a$b;-><init>(LKA0/c;LQW0/c;LSX0/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LoB0/a$b;->c(Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;)Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LKA0/c;LQW0/c;LSX0/a;)V
    .locals 1

    .line 1
    new-instance v0, LoB0/a$b$a;

    .line 2
    .line 3
    invoke-direct {v0, p2}, LoB0/a$b$a;-><init>(LQW0/c;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, LoB0/a$b;->b:Ldagger/internal/h;

    .line 7
    .line 8
    new-instance p2, LoB0/a$b$b;

    .line 9
    .line 10
    invoke-direct {p2, p1}, LoB0/a$b$b;-><init>(LKA0/c;)V

    .line 11
    .line 12
    .line 13
    iput-object p2, p0, LoB0/a$b;->c:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {p2}, LpB0/b;->a(LBc/a;)LpB0/b;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    iput-object p1, p0, LoB0/a$b;->d:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iput-object p1, p0, LoB0/a$b;->e:Ldagger/internal/h;

    .line 26
    .line 27
    iget-object p2, p0, LoB0/a$b;->b:Ldagger/internal/h;

    .line 28
    .line 29
    iget-object p3, p0, LoB0/a$b;->d:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p2, p3, p1}, Lorg/xbet/sportgame/filters/impl/presentation/e;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/sportgame/filters/impl/presentation/e;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, LoB0/a$b;->f:Ldagger/internal/h;

    .line 36
    .line 37
    return-void
.end method

.method public final c(Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;)Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;
    .locals 1

    .line 1
    invoke-virtual {p0}, LoB0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/sportgame/filters/impl/presentation/d;->a(Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersDialog;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/sportgame/filters/impl/presentation/SubGameFiltersViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LoB0/a$b;->f:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LoB0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
