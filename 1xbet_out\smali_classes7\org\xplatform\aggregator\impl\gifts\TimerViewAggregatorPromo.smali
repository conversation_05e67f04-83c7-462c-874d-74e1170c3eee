.class public final Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u0000 \u001d2\u00020\u0001:\u0001\u0010B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0012\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u001b\u0010\u0018\u001a\u00020\u00138BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\u001c\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001b\u00a8\u0006\u001e"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "millisUntilFinished",
        "",
        "v",
        "(J)V",
        "Lkotlinx/coroutines/N;",
        "a",
        "Lkotlinx/coroutines/N;",
        "scope",
        "LS91/u1;",
        "b",
        "Lkotlin/j;",
        "getViewBinding",
        "()LS91/u1;",
        "viewBinding",
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
        "c",
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
        "timer",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->d:Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {}, Lkotlinx/coroutines/O;->b()Lkotlinx/coroutines/N;

    move-result-object p1

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->a:Lkotlinx/coroutines/N;

    .line 6
    sget-object p1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance p2, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;

    const/4 p3, 0x1

    invoke-direct {p2, p0, p0, p3}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$b;-><init>(Landroid/view/ViewGroup;Landroid/view/ViewGroup;Z)V

    invoke-static {p1, p2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    .line 7
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->b:Lkotlin/j;

    .line 8
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 9
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/y;

    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/gifts/y;-><init>(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;)V

    const/4 v5, 0x1

    const/4 v6, 0x0

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    .line 10
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->c:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getViewBinding()LS91/u1;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LS91/u1;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic s(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->w(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic t(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;)Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->c:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->v(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final w(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;J)Lkotlin/Unit;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->a:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$timer$1$1;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-direct {v3, p0, p1, p2, v1}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo$timer$1$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;JLkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    const/4 v4, 0x3

    .line 10
    const/4 v5, 0x0

    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method


# virtual methods
.method public final v(J)V
    .locals 9

    .line 1
    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toDays(J)J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    sget-object v3, Ljava/util/concurrent/TimeUnit;->DAYS:Ljava/util/concurrent/TimeUnit;

    .line 8
    .line 9
    invoke-virtual {v3, v1, v2}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 10
    .line 11
    .line 12
    move-result-wide v3

    .line 13
    sub-long/2addr p1, v3

    .line 14
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toHours(J)J

    .line 15
    .line 16
    .line 17
    move-result-wide v3

    .line 18
    sget-object v5, Ljava/util/concurrent/TimeUnit;->HOURS:Ljava/util/concurrent/TimeUnit;

    .line 19
    .line 20
    invoke-virtual {v5, v3, v4}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 21
    .line 22
    .line 23
    move-result-wide v5

    .line 24
    sub-long/2addr p1, v5

    .line 25
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toMinutes(J)J

    .line 26
    .line 27
    .line 28
    move-result-wide v5

    .line 29
    sget-object v7, Ljava/util/concurrent/TimeUnit;->MINUTES:Ljava/util/concurrent/TimeUnit;

    .line 30
    .line 31
    invoke-virtual {v7, v5, v6}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 32
    .line 33
    .line 34
    move-result-wide v7

    .line 35
    sub-long/2addr p1, v7

    .line 36
    invoke-virtual {v0, p1, p2}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 37
    .line 38
    .line 39
    move-result-wide p1

    .line 40
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->getViewBinding()LS91/u1;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    iget-object v0, v0, LS91/u1;->b:Landroid/widget/TextView;

    .line 45
    .line 46
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    const/4 v2, 0x2

    .line 51
    const/16 v7, 0x30

    .line 52
    .line 53
    invoke-static {v1, v2, v7}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 58
    .line 59
    .line 60
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->getViewBinding()LS91/u1;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget-object v0, v0, LS91/u1;->c:Landroid/widget/TextView;

    .line 65
    .line 66
    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-static {v1, v2, v7}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 75
    .line 76
    .line 77
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->getViewBinding()LS91/u1;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iget-object v0, v0, LS91/u1;->d:Landroid/widget/TextView;

    .line 82
    .line 83
    invoke-static {v5, v6}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-static {v1, v2, v7}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 92
    .line 93
    .line 94
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/TimerViewAggregatorPromo;->getViewBinding()LS91/u1;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    iget-object v0, v0, LS91/u1;->e:Landroid/widget/TextView;

    .line 99
    .line 100
    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    invoke-static {p1, v2, v7}, Lkotlin/text/StringsKt;->O0(Ljava/lang/String;IC)Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 109
    .line 110
    .line 111
    return-void
.end method
