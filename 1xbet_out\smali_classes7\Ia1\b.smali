.class public final LIa1/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00f2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008H\u0018\u00002\u00020\u0001B\u00a1\u0002\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u0012\u0006\u0010A\u001a\u00020@\u0012\u0006\u0010C\u001a\u00020B\u0012\u0006\u0010E\u001a\u00020D\u0012\u0006\u0010G\u001a\u00020F\u00a2\u0006\u0004\u0008H\u0010IJ\'\u0010P\u001a\u00020O2\u0006\u0010K\u001a\u00020J2\u0006\u0010L\u001a\u00020J2\u0006\u0010N\u001a\u00020MH\u0000\u00a2\u0006\u0004\u0008P\u0010QR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010RR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010VR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008W\u0010XR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010lR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010nR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u0014\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u00109\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010;\u001a\u00020:8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010=\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010?\u001a\u00020>8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010A\u001a\u00020@8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010C\u001a\u00020B8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0016\u0010E\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010G\u001a\u00020F8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001\u00a8\u0006\u0097\u0001"
    }
    d2 = {
        "LIa1/b;",
        "LQW0/a;",
        "LN91/e;",
        "aggregatorCoreLib",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "LJT/a;",
        "addAggregatorLastActionUseCase",
        "Lf8/g;",
        "serviceGenerator",
        "LfX/b;",
        "testRepository",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lkc1/c;",
        "getAggregatorBannerListByTypeScenario",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "profileInteractor",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "LP91/b;",
        "aggregatorNavigator",
        "Lc81/c;",
        "aggregatorScreenProvider",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lak/a;",
        "balanceFeature",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LQW0/c;",
        "coroutinesLib",
        "LwX0/C;",
        "routerHolder",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LwX0/a;",
        "appScreensProvider",
        "LHX0/e;",
        "resourceManager",
        "Lau/a;",
        "countryInfoRepository",
        "LTZ0/a;",
        "actionDialogManager",
        "Lak/b;",
        "changeBalanceFeature",
        "Lej0/d;",
        "getRegistrationTypesUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "LAR/a;",
        "depositFatmanLogger",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LZR/a;",
        "searchFatmanLogger",
        "Lo9/a;",
        "userRepository",
        "LzX0/k;",
        "snackbarManager",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "Lz81/a;",
        "dailyTasksFeature",
        "LWa0/a;",
        "messagesFeature",
        "<init>",
        "(LN91/e;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;Lak/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lorg/xbet/ui_common/utils/M;LQW0/c;LwX0/C;LSX0/c;LwX0/a;LHX0/e;Lau/a;LTZ0/a;Lak/b;Lej0/d;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;Lz81/a;LWa0/a;)V",
        "",
        "partitionId",
        "productId",
        "Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
        "openedFromType",
        "LIa1/a;",
        "a",
        "(JJLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)LIa1/a;",
        "LN91/e;",
        "b",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "c",
        "LJT/a;",
        "d",
        "Lf8/g;",
        "e",
        "LfX/b;",
        "f",
        "Lcom/xbet/onexuser/domain/user/c;",
        "g",
        "Lkc1/c;",
        "h",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "i",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "j",
        "LP91/b;",
        "k",
        "Lc81/c;",
        "l",
        "LxX0/a;",
        "m",
        "Lak/a;",
        "n",
        "LGg/a;",
        "o",
        "Lorg/xbet/analytics/domain/scope/I;",
        "p",
        "Lorg/xbet/ui_common/utils/M;",
        "q",
        "LQW0/c;",
        "r",
        "LwX0/C;",
        "s",
        "LSX0/c;",
        "t",
        "LwX0/a;",
        "u",
        "LHX0/e;",
        "v",
        "Lau/a;",
        "w",
        "LTZ0/a;",
        "x",
        "Lak/b;",
        "y",
        "Lej0/d;",
        "z",
        "Li8/j;",
        "A",
        "LAR/a;",
        "B",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "C",
        "LZR/a;",
        "D",
        "Lo9/a;",
        "E",
        "LzX0/k;",
        "F",
        "Lgk0/a;",
        "G",
        "LnR/a;",
        "H",
        "Lz81/a;",
        "I",
        "LWa0/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final A:LAR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C:LZR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D:Lo9/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F:Lgk0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H:Lz81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I:LWa0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a:LN91/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LJT/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lkc1/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lc81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LGg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lorg/xbet/analytics/domain/scope/I;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Lau/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Lak/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:Lej0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LN91/e;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;Lak/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lorg/xbet/ui_common/utils/M;LQW0/c;LwX0/C;LSX0/c;LwX0/a;LHX0/e;Lau/a;LTZ0/a;Lak/b;Lej0/d;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;Lz81/a;LWa0/a;)V
    .locals 0
    .param p1    # LN91/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LJT/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkc1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lc81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lau/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lak/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # Lej0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lo9/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p34    # Lz81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p35    # LWa0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LIa1/b;->a:LN91/e;

    .line 5
    .line 6
    iput-object p2, p0, LIa1/b;->b:Lorg/xbet/analytics/domain/scope/g0;

    .line 7
    .line 8
    iput-object p3, p0, LIa1/b;->c:LJT/a;

    .line 9
    .line 10
    iput-object p4, p0, LIa1/b;->d:Lf8/g;

    .line 11
    .line 12
    iput-object p5, p0, LIa1/b;->e:LfX/b;

    .line 13
    .line 14
    iput-object p6, p0, LIa1/b;->f:Lcom/xbet/onexuser/domain/user/c;

    .line 15
    .line 16
    iput-object p7, p0, LIa1/b;->g:Lkc1/c;

    .line 17
    .line 18
    iput-object p8, p0, LIa1/b;->h:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 19
    .line 20
    iput-object p9, p0, LIa1/b;->i:Lorg/xbet/ui_common/utils/internet/a;

    .line 21
    .line 22
    iput-object p10, p0, LIa1/b;->j:LP91/b;

    .line 23
    .line 24
    iput-object p11, p0, LIa1/b;->k:Lc81/c;

    .line 25
    .line 26
    iput-object p12, p0, LIa1/b;->l:LxX0/a;

    .line 27
    .line 28
    iput-object p13, p0, LIa1/b;->m:Lak/a;

    .line 29
    .line 30
    iput-object p14, p0, LIa1/b;->n:LGg/a;

    .line 31
    .line 32
    iput-object p15, p0, LIa1/b;->o:Lorg/xbet/analytics/domain/scope/I;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LIa1/b;->p:Lorg/xbet/ui_common/utils/M;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LIa1/b;->q:LQW0/c;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LIa1/b;->r:LwX0/C;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LIa1/b;->s:LSX0/c;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LIa1/b;->t:LwX0/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LIa1/b;->u:LHX0/e;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LIa1/b;->v:Lau/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, LIa1/b;->w:LTZ0/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, LIa1/b;->x:Lak/b;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, LIa1/b;->y:Lej0/d;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, LIa1/b;->z:Li8/j;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, LIa1/b;->A:LAR/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, LIa1/b;->B:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, LIa1/b;->C:LZR/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, LIa1/b;->D:Lo9/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, LIa1/b;->E:LzX0/k;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, LIa1/b;->F:Lgk0/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, LIa1/b;->G:LnR/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, LIa1/b;->H:Lz81/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, LIa1/b;->I:LWa0/a;

    .line 113
    .line 114
    return-void
.end method


# virtual methods
.method public final a(JJLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)LIa1/a;
    .locals 42
    .param p5    # Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, LIa1/g;->a()LIa1/a$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v10, v0, LIa1/b;->b:Lorg/xbet/analytics/domain/scope/g0;

    .line 8
    .line 9
    iget-object v2, v0, LIa1/b;->a:LN91/e;

    .line 10
    .line 11
    iget-object v3, v0, LIa1/b;->q:LQW0/c;

    .line 12
    .line 13
    iget-object v9, v0, LIa1/b;->r:LwX0/C;

    .line 14
    .line 15
    iget-object v4, v0, LIa1/b;->y:Lej0/d;

    .line 16
    .line 17
    iget-object v11, v0, LIa1/b;->c:LJT/a;

    .line 18
    .line 19
    iget-object v12, v0, LIa1/b;->d:Lf8/g;

    .line 20
    .line 21
    iget-object v13, v0, LIa1/b;->e:LfX/b;

    .line 22
    .line 23
    iget-object v14, v0, LIa1/b;->f:Lcom/xbet/onexuser/domain/user/c;

    .line 24
    .line 25
    iget-object v8, v0, LIa1/b;->w:LTZ0/a;

    .line 26
    .line 27
    iget-object v15, v0, LIa1/b;->g:Lkc1/c;

    .line 28
    .line 29
    iget-object v5, v0, LIa1/b;->h:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 30
    .line 31
    iget-object v6, v0, LIa1/b;->i:Lorg/xbet/ui_common/utils/internet/a;

    .line 32
    .line 33
    move-object/from16 v17, v4

    .line 34
    .line 35
    iget-object v4, v0, LIa1/b;->m:Lak/a;

    .line 36
    .line 37
    iget-object v7, v0, LIa1/b;->j:LP91/b;

    .line 38
    .line 39
    move-object/from16 v16, v1

    .line 40
    .line 41
    iget-object v1, v0, LIa1/b;->k:Lc81/c;

    .line 42
    .line 43
    move-object/from16 v20, v1

    .line 44
    .line 45
    iget-object v1, v0, LIa1/b;->l:LxX0/a;

    .line 46
    .line 47
    move-object/from16 v21, v1

    .line 48
    .line 49
    iget-object v1, v0, LIa1/b;->n:LGg/a;

    .line 50
    .line 51
    move-object/from16 v22, v1

    .line 52
    .line 53
    iget-object v1, v0, LIa1/b;->o:Lorg/xbet/analytics/domain/scope/I;

    .line 54
    .line 55
    move-object/from16 v23, v1

    .line 56
    .line 57
    move-object/from16 v1, v16

    .line 58
    .line 59
    move-object/from16 v16, v5

    .line 60
    .line 61
    iget-object v5, v0, LIa1/b;->x:Lak/b;

    .line 62
    .line 63
    move-object/from16 v18, v1

    .line 64
    .line 65
    iget-object v1, v0, LIa1/b;->p:Lorg/xbet/ui_common/utils/M;

    .line 66
    .line 67
    move-object/from16 v29, v1

    .line 68
    .line 69
    iget-object v1, v0, LIa1/b;->s:LSX0/c;

    .line 70
    .line 71
    move-object/from16 v30, v1

    .line 72
    .line 73
    iget-object v1, v0, LIa1/b;->t:LwX0/a;

    .line 74
    .line 75
    move-object/from16 v31, v1

    .line 76
    .line 77
    iget-object v1, v0, LIa1/b;->u:LHX0/e;

    .line 78
    .line 79
    move-object/from16 v32, v1

    .line 80
    .line 81
    iget-object v1, v0, LIa1/b;->v:Lau/a;

    .line 82
    .line 83
    move-object/from16 v33, v1

    .line 84
    .line 85
    iget-object v1, v0, LIa1/b;->z:Li8/j;

    .line 86
    .line 87
    move-object/from16 v34, v1

    .line 88
    .line 89
    iget-object v1, v0, LIa1/b;->A:LAR/a;

    .line 90
    .line 91
    move-object/from16 v35, v1

    .line 92
    .line 93
    iget-object v1, v0, LIa1/b;->B:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 94
    .line 95
    move-object/from16 v36, v1

    .line 96
    .line 97
    iget-object v1, v0, LIa1/b;->C:LZR/a;

    .line 98
    .line 99
    move-object/from16 v37, v1

    .line 100
    .line 101
    iget-object v1, v0, LIa1/b;->D:Lo9/a;

    .line 102
    .line 103
    move-object/from16 v38, v1

    .line 104
    .line 105
    iget-object v1, v0, LIa1/b;->E:LzX0/k;

    .line 106
    .line 107
    move-object/from16 v39, v1

    .line 108
    .line 109
    iget-object v1, v0, LIa1/b;->F:Lgk0/a;

    .line 110
    .line 111
    move-object/from16 v40, v1

    .line 112
    .line 113
    iget-object v1, v0, LIa1/b;->G:LnR/a;

    .line 114
    .line 115
    move-object/from16 v41, v1

    .line 116
    .line 117
    move-object/from16 v1, v18

    .line 118
    .line 119
    move-object/from16 v18, v6

    .line 120
    .line 121
    iget-object v6, v0, LIa1/b;->H:Lz81/a;

    .line 122
    .line 123
    move-object/from16 v19, v7

    .line 124
    .line 125
    iget-object v7, v0, LIa1/b;->I:LWa0/a;

    .line 126
    .line 127
    move-wide/from16 v24, p1

    .line 128
    .line 129
    move-wide/from16 v26, p3

    .line 130
    .line 131
    move-object/from16 v28, p5

    .line 132
    .line 133
    invoke-interface/range {v1 .. v41}, LIa1/a$a;->a(LN91/e;LQW0/c;Lak/a;Lak/b;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;JJLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)LIa1/a;

    .line 134
    .line 135
    .line 136
    move-result-object v1

    .line 137
    return-object v1
.end method
