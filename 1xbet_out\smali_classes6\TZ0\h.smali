.class public abstract LTZ0/h;
.super Landroidx/fragment/app/l;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LTZ0/h$a;,
        LTZ0/h$b;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\'\u0018\u0000 F2\u00020\u0001:\u0001GB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0015\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0003J\u000f\u0010\u0016\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0003J\u000f\u0010\u0017\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0003J\u0017\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u0019\u001a\u00020\u0018H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001c\u001a\u00020\u00042\u0006\u0010\u0019\u001a\u00020\u0018H\u0014\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u0011\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0004\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0011\u0010 \u001a\u0004\u0018\u00010\u001dH\u0004\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u0011\u0010\"\u001a\u0004\u0018\u00010!H\u0004\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008$\u0010\u0003J\u0019\u0010\'\u001a\u00020\u00042\u0008\u0010&\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010)\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008)\u0010\u0003J\u0019\u0010+\u001a\u00020*2\u0008\u0010&\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008+\u0010,J\u000f\u0010-\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008-\u0010\u0003J\u000f\u0010.\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008.\u0010\u0003J\u0017\u0010/\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008/\u0010\u0010R+\u00108\u001a\u0002002\u0006\u00101\u001a\u0002008D@DX\u0084\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105\"\u0004\u00086\u00107R+\u0010>\u001a\u00020\u00182\u0006\u00101\u001a\u00020\u00188D@DX\u0084\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00089\u0010:\u001a\u0004\u0008;\u0010<\"\u0004\u0008=\u0010\u001bR\u0018\u0010B\u001a\u0004\u0018\u00010?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0016\u0010E\u001a\u00020\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008C\u0010D\u00a8\u0006H"
    }
    d2 = {
        "LTZ0/h;",
        "Landroidx/fragment/app/l;",
        "<init>",
        "()V",
        "",
        "x2",
        "",
        "K2",
        "()I",
        "Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;",
        "type",
        "F2",
        "(Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;)V",
        "",
        "checked",
        "G2",
        "(Z)V",
        "Lorg/xbet/uikit/components/dialog/AlertType;",
        "alertType",
        "t2",
        "(Lorg/xbet/uikit/components/dialog/AlertType;)I",
        "q2",
        "E2",
        "M2",
        "",
        "message",
        "L2",
        "(Ljava/lang/String;)V",
        "I2",
        "Landroid/view/View;",
        "v2",
        "()Landroid/view/View;",
        "w2",
        "Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;",
        "s2",
        "()Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;",
        "C2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "onStart",
        "Landroid/app/Dialog;",
        "onCreateDialog",
        "(Landroid/os/Bundle;)Landroid/app/Dialog;",
        "onResume",
        "onDestroyView",
        "D2",
        "Lorg/xbet/uikit/components/dialog/DialogFields;",
        "<set-?>",
        "e0",
        "LO11/b;",
        "u2",
        "()Lorg/xbet/uikit/components/dialog/DialogFields;",
        "J2",
        "(Lorg/xbet/uikit/components/dialog/DialogFields;)V",
        "dialogFields",
        "f0",
        "LO11/c;",
        "r2",
        "()Ljava/lang/String;",
        "H2",
        "alertStyle",
        "LUZ0/a;",
        "g0",
        "LUZ0/a;",
        "dialogView",
        "h0",
        "Z",
        "showEnterAnimation",
        "i0",
        "a",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final i0:LTZ0/h$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic j0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final k0:I

.field public static final l0:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final e0:LO11/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f0:LO11/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g0:LUZ0/a;

.field public h0:Z


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, LTZ0/h;

    .line 4
    .line 5
    const-string v2, "dialogFields"

    .line 6
    .line 7
    const-string v3, "getDialogFields()Lorg/xbet/uikit/components/dialog/DialogFields;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "alertStyle"

    .line 20
    .line 21
    const-string v5, "getAlertStyle()Ljava/lang/String;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v3, 0x2

    .line 31
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v3, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v2, v3, v0

    .line 37
    .line 38
    sput-object v3, LTZ0/h;->j0:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, LTZ0/h$a;

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    invoke-direct {v0, v2}, LTZ0/h$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, LTZ0/h;->i0:LTZ0/h$a;

    .line 47
    .line 48
    const/16 v0, 0x8

    .line 49
    .line 50
    sput v0, LTZ0/h;->k0:I

    .line 51
    .line 52
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    sput-object v0, LTZ0/h;->l0:Ljava/lang/String;

    .line 57
    .line 58
    return-void
.end method

.method public constructor <init>()V
    .locals 4

    .line 1
    invoke-direct {p0}, Landroidx/fragment/app/l;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LO11/b;

    .line 5
    .line 6
    const-string v1, "DIALOG_FIELDS"

    .line 7
    .line 8
    invoke-direct {v0, v1}, LO11/b;-><init>(Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iput-object v0, p0, LTZ0/h;->e0:LO11/b;

    .line 12
    .line 13
    new-instance v0, LO11/c;

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    const/4 v2, 0x2

    .line 17
    const-string v3, "TMP_ALERT_STYLE"

    .line 18
    .line 19
    invoke-direct {v0, v3, v1, v2, v1}, LO11/c;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 20
    .line 21
    .line 22
    iput-object v0, p0, LTZ0/h;->f0:LO11/c;

    .line 23
    .line 24
    const/4 v0, 0x1

    .line 25
    iput-boolean v0, p0, LTZ0/h;->h0:Z

    .line 26
    .line 27
    return-void
.end method

.method public static final A2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LTZ0/h;->M2()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final B2(LTZ0/h;Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p2}, LTZ0/h;->D2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic N(LTZ0/h;Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;Z)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LTZ0/h;->B2(LTZ0/h;Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;Z)V

    return-void
.end method

.method public static synthetic O(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LTZ0/h;->y2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic P(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LTZ0/h;->A2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic Q(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LTZ0/h;->z2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, LTZ0/h;->l0:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final y2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LTZ0/h;->q2()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final z2(LTZ0/h;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LTZ0/h;->E2()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public C2()V
    .locals 0

    .line 1
    return-void
.end method

.method public D2(Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LTZ0/h;->G2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public E2()V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;->SECOND:Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, LTZ0/h;->F2(Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final F2(Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/dialog/DialogFields;->B()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-virtual {p1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 23
    .line 24
    invoke-static {v0, v1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    const/4 v1, 0x1

    .line 29
    new-array v1, v1, [Lkotlin/Pair;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    aput-object v0, v1, v2

    .line 33
    .line 34
    invoke-static {v1}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1}, Lorg/xbet/uikit/components/dialog/DialogFields;->B()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {p1}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    new-instance v2, Ljava/lang/StringBuilder;

    .line 51
    .line 52
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 53
    .line 54
    .line 55
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 56
    .line 57
    .line 58
    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 59
    .line 60
    .line 61
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-static {p0, p1, v0}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 66
    .line 67
    .line 68
    :cond_1
    :goto_0
    invoke-virtual {p0}, Landroidx/fragment/app/l;->dismissAllowingStateLoss()V

    .line 69
    .line 70
    .line 71
    return-void
.end method

.method public final G2(Z)V
    .locals 3

    .line 1
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/dialog/DialogFields;->B()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    const-string v0, "CHECK_BOX"

    .line 23
    .line 24
    invoke-static {v0, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    const/4 v0, 0x1

    .line 29
    new-array v0, v0, [Lkotlin/Pair;

    .line 30
    .line 31
    const/4 v1, 0x0

    .line 32
    aput-object p1, v0, v1

    .line 33
    .line 34
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    invoke-virtual {v0}, Lorg/xbet/uikit/components/dialog/DialogFields;->B()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    sget-object v1, Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;->CHECK_BOX:Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;

    .line 47
    .line 48
    new-instance v2, Ljava/lang/StringBuilder;

    .line 49
    .line 50
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 51
    .line 52
    .line 53
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 54
    .line 55
    .line 56
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-static {p0, v0, p1}, Landroidx/fragment/app/x;->d(Landroidx/fragment/app/Fragment;Ljava/lang/String;Landroid/os/Bundle;)V

    .line 64
    .line 65
    .line 66
    :cond_1
    :goto_0
    return-void
.end method

.method public final H2(Ljava/lang/String;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LTZ0/h;->f0:LO11/c;

    .line 2
    .line 3
    sget-object v1, LTZ0/h;->j0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LO11/c;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public I2(Ljava/lang/String;)V
    .locals 17
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/uikit/components/dialog/DialogFields;->e()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    move-object/from16 v9, p1

    .line 12
    .line 13
    invoke-static {v1, v9}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    const/16 v15, 0xfbf

    .line 24
    .line 25
    const/16 v16, 0x0

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    const/4 v4, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x0

    .line 32
    const/4 v8, 0x0

    .line 33
    const/4 v10, 0x0

    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v12, 0x0

    .line 36
    const/4 v13, 0x0

    .line 37
    const/4 v14, 0x0

    .line 38
    invoke-static/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;->b(Lorg/xbet/uikit/components/dialog/DialogFields;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILjava/lang/Object;)Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v0, v1}, LTZ0/h;->J2(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 43
    .line 44
    .line 45
    iget-object v1, v0, LTZ0/h;->g0:LUZ0/a;

    .line 46
    .line 47
    if-eqz v1, :cond_0

    .line 48
    .line 49
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-interface {v1, v2}, LUZ0/a;->setModel(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 54
    .line 55
    .line 56
    :cond_0
    return-void
.end method

.method public final J2(Lorg/xbet/uikit/components/dialog/DialogFields;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit/components/dialog/DialogFields;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LTZ0/h;->e0:LO11/b;

    .line 2
    .line 3
    sget-object v1, LTZ0/h;->j0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LO11/b;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Landroid/os/Parcelable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final K2()I
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget v1, LlZ0/e;->isTablet:I

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getBoolean(I)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    sget v1, LlZ0/g;->size_400:I

    .line 22
    .line 23
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    sget v1, LlZ0/g;->size_288:I

    .line 33
    .line 34
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    :goto_0
    float-to-int v0, v0

    .line 39
    return v0
.end method

.method public L2(Ljava/lang/String;)V
    .locals 17
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/uikit/components/dialog/DialogFields;->h()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    move-object/from16 v4, p1

    .line 12
    .line 13
    invoke-static {v1, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    const/16 v15, 0xffd

    .line 24
    .line 25
    const/16 v16, 0x0

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    const/4 v5, 0x0

    .line 29
    const/4 v6, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    const/4 v9, 0x0

    .line 33
    const/4 v10, 0x0

    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v12, 0x0

    .line 36
    const/4 v13, 0x0

    .line 37
    const/4 v14, 0x0

    .line 38
    invoke-static/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;->b(Lorg/xbet/uikit/components/dialog/DialogFields;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILjava/lang/Object;)Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v0, v1}, LTZ0/h;->J2(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 43
    .line 44
    .line 45
    iget-object v1, v0, LTZ0/h;->g0:LUZ0/a;

    .line 46
    .line 47
    if-eqz v1, :cond_0

    .line 48
    .line 49
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-interface {v1, v2}, LUZ0/a;->setModel(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 54
    .line 55
    .line 56
    :cond_0
    return-void
.end method

.method public M2()V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;->THIRD:Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, LTZ0/h;->F2(Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, Landroidx/fragment/app/l;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    new-instance v0, Ljava/lang/StringBuilder;

    .line 13
    .line 14
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 15
    .line 16
    .line 17
    const-string v1, "Current screen: "

    .line 18
    .line 19
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 20
    .line 21
    .line 22
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    const-string v0, "onCreate"

    .line 30
    .line 31
    invoke-static {v0, p1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public onCreateDialog(Landroid/os/Bundle;)Landroid/app/Dialog;
    .locals 18
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super/range {p0 .. p1}, Landroidx/fragment/app/l;->onCreateDialog(Landroid/os/Bundle;)Landroid/app/Dialog;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {v2}, Lorg/xbet/uikit/components/dialog/DialogFields;->g()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    if-nez v2, :cond_0

    .line 16
    .line 17
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-virtual {v0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-virtual {v2}, Lorg/xbet/uikit/components/dialog/DialogFields;->c()Lorg/xbet/uikit/components/dialog/AlertType;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v0, v2}, LTZ0/h;->t2(Lorg/xbet/uikit/components/dialog/AlertType;)I

    .line 30
    .line 31
    .line 32
    move-result v13

    .line 33
    const/16 v16, 0xdff

    .line 34
    .line 35
    const/16 v17, 0x0

    .line 36
    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v5, 0x0

    .line 39
    const/4 v6, 0x0

    .line 40
    const/4 v7, 0x0

    .line 41
    const/4 v8, 0x0

    .line 42
    const/4 v9, 0x0

    .line 43
    const/4 v10, 0x0

    .line 44
    const/4 v11, 0x0

    .line 45
    const/4 v12, 0x0

    .line 46
    const/4 v14, 0x0

    .line 47
    const/4 v15, 0x0

    .line 48
    invoke-static/range {v3 .. v17}, Lorg/xbet/uikit/components/dialog/DialogFields;->b(Lorg/xbet/uikit/components/dialog/DialogFields;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILjava/lang/Object;)Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    invoke-virtual {v0, v2}, LTZ0/h;->J2(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 53
    .line 54
    .line 55
    :cond_0
    iget-object v2, v0, LTZ0/h;->g0:LUZ0/a;

    .line 56
    .line 57
    instance-of v3, v2, Landroid/view/View;

    .line 58
    .line 59
    const/4 v4, 0x0

    .line 60
    if-eqz v3, :cond_1

    .line 61
    .line 62
    check-cast v2, Landroid/view/View;

    .line 63
    .line 64
    goto :goto_0

    .line 65
    :cond_1
    move-object v2, v4

    .line 66
    :goto_0
    if-eqz v2, :cond_2

    .line 67
    .line 68
    invoke-static {v2}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 69
    .line 70
    .line 71
    :cond_2
    invoke-virtual {v0}, LTZ0/h;->r2()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    invoke-virtual {v2}, Ljava/lang/String;->hashCode()I

    .line 76
    .line 77
    .line 78
    move-result v3

    .line 79
    const v5, -0x5069748f

    .line 80
    .line 81
    .line 82
    if-eq v3, v5, :cond_7

    .line 83
    .line 84
    const v5, -0x3ebdafe9

    .line 85
    .line 86
    .line 87
    if-eq v3, v5, :cond_5

    .line 88
    .line 89
    const v5, 0x7eb20e56

    .line 90
    .line 91
    .line 92
    if-eq v3, v5, :cond_3

    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_3
    const-string v3, "customWithLottie"

    .line 96
    .line 97
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 98
    .line 99
    .line 100
    move-result v2

    .line 101
    if-nez v2, :cond_4

    .line 102
    .line 103
    goto :goto_1

    .line 104
    :cond_4
    new-instance v5, Lorg/xbet/uikit/components/dialog/stylyableviews/CustomLottieAlertDialogView;

    .line 105
    .line 106
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 107
    .line 108
    .line 109
    move-result-object v6

    .line 110
    const/4 v9, 0x6

    .line 111
    const/4 v10, 0x0

    .line 112
    const/4 v7, 0x0

    .line 113
    const/4 v8, 0x0

    .line 114
    invoke-direct/range {v5 .. v10}, Lorg/xbet/uikit/components/dialog/stylyableviews/CustomLottieAlertDialogView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 115
    .line 116
    .line 117
    goto :goto_2

    .line 118
    :cond_5
    const-string v3, "native"

    .line 119
    .line 120
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 121
    .line 122
    .line 123
    move-result v2

    .line 124
    if-nez v2, :cond_6

    .line 125
    .line 126
    goto :goto_1

    .line 127
    :cond_6
    new-instance v5, Lorg/xbet/uikit/components/dialog/stylyableviews/NativeAlertDialogView;

    .line 128
    .line 129
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 130
    .line 131
    .line 132
    move-result-object v6

    .line 133
    const/4 v9, 0x6

    .line 134
    const/4 v10, 0x0

    .line 135
    const/4 v7, 0x0

    .line 136
    const/4 v8, 0x0

    .line 137
    invoke-direct/range {v5 .. v10}, Lorg/xbet/uikit/components/dialog/stylyableviews/NativeAlertDialogView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 138
    .line 139
    .line 140
    goto :goto_2

    .line 141
    :cond_7
    const-string v3, "custom"

    .line 142
    .line 143
    invoke-virtual {v2, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 144
    .line 145
    .line 146
    move-result v2

    .line 147
    if-nez v2, :cond_8

    .line 148
    .line 149
    :goto_1
    new-instance v5, Lorg/xbet/uikit/components/dialog/stylyableviews/NativeAlertDialogView;

    .line 150
    .line 151
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 152
    .line 153
    .line 154
    move-result-object v6

    .line 155
    const/4 v9, 0x6

    .line 156
    const/4 v10, 0x0

    .line 157
    const/4 v7, 0x0

    .line 158
    const/4 v8, 0x0

    .line 159
    invoke-direct/range {v5 .. v10}, Lorg/xbet/uikit/components/dialog/stylyableviews/NativeAlertDialogView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 160
    .line 161
    .line 162
    goto :goto_2

    .line 163
    :cond_8
    new-instance v6, Lorg/xbet/uikit/components/dialog/stylyableviews/CustomAlertDialogView;

    .line 164
    .line 165
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 166
    .line 167
    .line 168
    move-result-object v7

    .line 169
    const/4 v10, 0x6

    .line 170
    const/4 v11, 0x0

    .line 171
    const/4 v8, 0x0

    .line 172
    const/4 v9, 0x0

    .line 173
    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit/components/dialog/stylyableviews/CustomAlertDialogView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 174
    .line 175
    .line 176
    move-object v5, v6

    .line 177
    :goto_2
    iput-object v5, v0, LTZ0/h;->g0:LUZ0/a;

    .line 178
    .line 179
    invoke-virtual {v0}, LTZ0/h;->x2()V

    .line 180
    .line 181
    .line 182
    iget-object v2, v0, LTZ0/h;->g0:LUZ0/a;

    .line 183
    .line 184
    instance-of v3, v2, Landroid/view/View;

    .line 185
    .line 186
    if-eqz v3, :cond_9

    .line 187
    .line 188
    move-object v4, v2

    .line 189
    check-cast v4, Landroid/view/View;

    .line 190
    .line 191
    :cond_9
    if-eqz v4, :cond_a

    .line 192
    .line 193
    sget v2, LlZ0/j;->alertDialogRoot:I

    .line 194
    .line 195
    invoke-virtual {v4, v2}, Landroid/view/View;->setId(I)V

    .line 196
    .line 197
    .line 198
    sget-object v2, LTZ0/h;->l0:Ljava/lang/String;

    .line 199
    .line 200
    invoke-virtual {v4, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 201
    .line 202
    .line 203
    invoke-virtual {v1, v4}, Landroid/app/Dialog;->setContentView(Landroid/view/View;)V

    .line 204
    .line 205
    .line 206
    :cond_a
    return-object v1
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    iget-object v0, p0, LTZ0/h;->g0:LUZ0/a;

    .line 2
    .line 3
    instance-of v1, v0, Landroid/view/View;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, Landroid/view/View;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->l(Landroid/view/View;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    invoke-super {p0}, Landroidx/fragment/app/l;->onDestroyView()V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, LTZ0/h;->C2()V

    .line 5
    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    iput-boolean v0, p0, LTZ0/h;->h0:Z

    .line 9
    .line 10
    return-void
.end method

.method public onStart()V
    .locals 3

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/l;->onStart()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/l;->getDialog()Landroid/app/Dialog;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    if-eqz v0, :cond_5

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    const/16 v2, 0x11

    .line 17
    .line 18
    invoke-virtual {v1, v2}, Landroid/view/Window;->setGravity(I)V

    .line 19
    .line 20
    .line 21
    :cond_0
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    sget v2, LlZ0/h;->rounded_background_20_content:I

    .line 28
    .line 29
    invoke-virtual {v1, v2}, Landroid/view/Window;->setBackgroundDrawableResource(I)V

    .line 30
    .line 31
    .line 32
    :cond_1
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    if-eqz v1, :cond_3

    .line 37
    .line 38
    iget-boolean v2, p0, LTZ0/h;->h0:Z

    .line 39
    .line 40
    if-eqz v2, :cond_2

    .line 41
    .line 42
    sget v2, LlZ0/n;->Widget_Dialog_AlertDialogAnimation:I

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_2
    sget v2, LlZ0/n;->Widget_Dialog_AlertDialogExitAnimation:I

    .line 46
    .line 47
    :goto_0
    invoke-virtual {v1, v2}, Landroid/view/Window;->setWindowAnimations(I)V

    .line 48
    .line 49
    .line 50
    :cond_3
    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 51
    .line 52
    const/16 v2, 0x16

    .line 53
    .line 54
    if-lt v1, v2, :cond_4

    .line 55
    .line 56
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    if-eqz v1, :cond_4

    .line 61
    .line 62
    const/4 v2, 0x0

    .line 63
    invoke-static {v1, v2}, LTZ0/c;->a(Landroid/view/Window;F)V

    .line 64
    .line 65
    .line 66
    :cond_4
    const/4 v1, 0x0

    .line 67
    invoke-virtual {v0, v1}, Landroid/app/Dialog;->setCanceledOnTouchOutside(Z)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {v1}, Lorg/xbet/uikit/components/dialog/DialogFields;->m()Z

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    invoke-virtual {p0, v1}, Landroidx/fragment/app/l;->setCancelable(Z)V

    .line 79
    .line 80
    .line 81
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    if-eqz v0, :cond_5

    .line 86
    .line 87
    invoke-virtual {p0}, LTZ0/h;->K2()I

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    const/4 v2, -0x2

    .line 92
    invoke-virtual {v0, v1, v2}, Landroid/view/Window;->setLayout(II)V

    .line 93
    .line 94
    .line 95
    :cond_5
    return-void
.end method

.method public q2()V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;->FIRST:Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, LTZ0/h;->F2(Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonResultType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final r2()Ljava/lang/String;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LTZ0/h;->f0:LO11/c;

    .line 2
    .line 3
    sget-object v1, LTZ0/h;->j0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LO11/c;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final s2()Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/h;->g0:LUZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, LUZ0/a;->getChecker()Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    return-object v0
.end method

.method public final t2(Lorg/xbet/uikit/components/dialog/AlertType;)I
    .locals 1

    .line 1
    sget-object v0, LTZ0/h$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_2

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_1

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-ne p1, v0, :cond_0

    .line 17
    .line 18
    sget p1, LlZ0/m;->info_lottie_accept_opt:I

    .line 19
    .line 20
    return p1

    .line 21
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 22
    .line 23
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    sget p1, LlZ0/m;->info_lottie_warning_opt:I

    .line 28
    .line 29
    return p1

    .line 30
    :cond_2
    sget p1, LlZ0/m;->info_lottie_info_opt:I

    .line 31
    .line 32
    return p1
.end method

.method public final u2()Lorg/xbet/uikit/components/dialog/DialogFields;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LTZ0/h;->e0:LO11/b;

    .line 2
    .line 3
    sget-object v1, LTZ0/h;->j0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LO11/b;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Landroid/os/Parcelable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 13
    .line 14
    return-object v0
.end method

.method public final v2()Landroid/view/View;
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/h;->g0:LUZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, LUZ0/a;->getFirstButton()Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    return-object v0
.end method

.method public final w2()Landroid/view/View;
    .locals 1

    .line 1
    iget-object v0, p0, LTZ0/h;->g0:LUZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, LUZ0/a;->getSecondButton()Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    return-object v0
.end method

.method public final x2()V
    .locals 5

    .line 1
    iget-object v0, p0, LTZ0/h;->g0:LUZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, LTZ0/h;->u2()Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {v0, v1}, LUZ0/a;->setModel(Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 10
    .line 11
    .line 12
    invoke-interface {v0}, LUZ0/a;->getFirstButton()Landroid/view/View;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    const-string v2, "topButton"

    .line 17
    .line 18
    invoke-virtual {v1, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    invoke-interface {v0}, LUZ0/a;->getFirstButton()Landroid/view/View;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    new-instance v2, LTZ0/d;

    .line 26
    .line 27
    invoke-direct {v2, p0}, LTZ0/d;-><init>(LTZ0/h;)V

    .line 28
    .line 29
    .line 30
    const/4 v3, 0x0

    .line 31
    const/4 v4, 0x1

    .line 32
    invoke-static {v1, v3, v2, v4, v3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 33
    .line 34
    .line 35
    invoke-interface {v0}, LUZ0/a;->getSecondButton()Landroid/view/View;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    const-string v2, "middleButton"

    .line 40
    .line 41
    invoke-virtual {v1, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    invoke-interface {v0}, LUZ0/a;->getSecondButton()Landroid/view/View;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    new-instance v2, LTZ0/e;

    .line 49
    .line 50
    invoke-direct {v2, p0}, LTZ0/e;-><init>(LTZ0/h;)V

    .line 51
    .line 52
    .line 53
    invoke-static {v1, v3, v2, v4, v3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 54
    .line 55
    .line 56
    invoke-interface {v0}, LUZ0/a;->getThirdButton()Landroid/view/View;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    new-instance v2, LTZ0/f;

    .line 61
    .line 62
    invoke-direct {v2, p0}, LTZ0/f;-><init>(LTZ0/h;)V

    .line 63
    .line 64
    .line 65
    invoke-static {v1, v3, v2, v4, v3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 66
    .line 67
    .line 68
    invoke-interface {v0}, LUZ0/a;->getChecker()Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    new-instance v1, LTZ0/g;

    .line 73
    .line 74
    invoke-direct {v1, p0}, LTZ0/g;-><init>(LTZ0/h;)V

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/dscheckbox/DSCheckBox;->setCheckedChangeListener(La01/p;)V

    .line 78
    .line 79
    .line 80
    :cond_0
    return-void
.end method
