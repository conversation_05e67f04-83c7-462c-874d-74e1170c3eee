.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;-><init>(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/e<",
        "Ljava/lang/Boolean;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004*\u0001\u0000\u0008\n\u0018\u00002\u0008\u0012\u0004\u0012\u00028\u00000\u0001J\u001e\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0002H\u0096@\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0008\u00b8\u0006\u0007"
    }
    d2 = {
        "kotlinx/coroutines/flow/internal/SafeCollector_commonKt$unsafeFlow$1",
        "Lkotlinx/coroutines/flow/e;",
        "Lkotlinx/coroutines/flow/f;",
        "collector",
        "",
        "collect",
        "(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "com/xbet/onexcore/utils/flows/b",
        "kotlinx-coroutines-core"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:[Lkotlinx/coroutines/flow/e;


# direct methods
.method public constructor <init>([Lkotlinx/coroutines/flow/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1;->a:[Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1;->a:[Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1$a;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1$a;-><init>([Lkotlinx/coroutines/flow/e;)V

    .line 6
    .line 7
    .line 8
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1$3;

    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel$special$$inlined$combine$1$3;-><init>(Lkotlin/coroutines/e;)V

    .line 12
    .line 13
    .line 14
    invoke-static {p1, v0, v1, v2, p2}, Lkotlinx/coroutines/flow/internal/CombineKt;->a(Lkotlinx/coroutines/flow/f;[Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;LOc/n;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p2

    .line 22
    if-ne p1, p2, :cond_0

    .line 23
    .line 24
    return-object p1

    .line 25
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p1
.end method
