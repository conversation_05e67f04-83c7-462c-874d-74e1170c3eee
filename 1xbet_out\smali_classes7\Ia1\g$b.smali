.class public final LIa1/g$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIa1/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIa1/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LIa1/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LIa1/g$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LN91/e;LQW0/c;Lak/a;Lak/b;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;JJLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)LIa1/a;
    .locals 41

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    invoke-static/range {p20 .. p20}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    invoke-static/range {p21 .. p21}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    invoke-static/range {p22 .. p22}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    invoke-static/range {p23 .. p24}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    invoke-static/range {p25 .. p26}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    invoke-static/range {p27 .. p27}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    invoke-static/range {p28 .. p28}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    invoke-static/range {p29 .. p29}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    invoke-static/range {p30 .. p30}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    invoke-static/range {p31 .. p31}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    invoke-static/range {p32 .. p32}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    invoke-static/range {p33 .. p33}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 100
    .line 101
    .line 102
    invoke-static/range {p34 .. p34}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    invoke-static/range {p35 .. p35}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    invoke-static/range {p36 .. p36}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    invoke-static/range {p37 .. p37}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 112
    .line 113
    .line 114
    invoke-static/range {p38 .. p38}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    invoke-static/range {p39 .. p39}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 118
    .line 119
    .line 120
    invoke-static/range {p40 .. p40}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    new-instance v1, LIa1/g$a;

    .line 124
    .line 125
    invoke-static/range {p23 .. p24}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 126
    .line 127
    .line 128
    move-result-object v24

    .line 129
    invoke-static/range {p25 .. p26}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 130
    .line 131
    .line 132
    move-result-object v25

    .line 133
    const/16 v40, 0x0

    .line 134
    .line 135
    move-object/from16 v3, p1

    .line 136
    .line 137
    move-object/from16 v2, p2

    .line 138
    .line 139
    move-object/from16 v5, p3

    .line 140
    .line 141
    move-object/from16 v4, p4

    .line 142
    .line 143
    move-object/from16 v6, p5

    .line 144
    .line 145
    move-object/from16 v7, p6

    .line 146
    .line 147
    move-object/from16 v8, p7

    .line 148
    .line 149
    move-object/from16 v9, p8

    .line 150
    .line 151
    move-object/from16 v10, p9

    .line 152
    .line 153
    move-object/from16 v11, p10

    .line 154
    .line 155
    move-object/from16 v12, p11

    .line 156
    .line 157
    move-object/from16 v13, p12

    .line 158
    .line 159
    move-object/from16 v14, p13

    .line 160
    .line 161
    move-object/from16 v15, p14

    .line 162
    .line 163
    move-object/from16 v16, p15

    .line 164
    .line 165
    move-object/from16 v17, p16

    .line 166
    .line 167
    move-object/from16 v18, p17

    .line 168
    .line 169
    move-object/from16 v19, p18

    .line 170
    .line 171
    move-object/from16 v20, p19

    .line 172
    .line 173
    move-object/from16 v21, p20

    .line 174
    .line 175
    move-object/from16 v22, p21

    .line 176
    .line 177
    move-object/from16 v23, p22

    .line 178
    .line 179
    move-object/from16 v26, p27

    .line 180
    .line 181
    move-object/from16 v27, p28

    .line 182
    .line 183
    move-object/from16 v28, p29

    .line 184
    .line 185
    move-object/from16 v29, p30

    .line 186
    .line 187
    move-object/from16 v30, p31

    .line 188
    .line 189
    move-object/from16 v31, p32

    .line 190
    .line 191
    move-object/from16 v32, p33

    .line 192
    .line 193
    move-object/from16 v33, p34

    .line 194
    .line 195
    move-object/from16 v34, p35

    .line 196
    .line 197
    move-object/from16 v35, p36

    .line 198
    .line 199
    move-object/from16 v36, p37

    .line 200
    .line 201
    move-object/from16 v37, p38

    .line 202
    .line 203
    move-object/from16 v38, p39

    .line 204
    .line 205
    move-object/from16 v39, p40

    .line 206
    .line 207
    invoke-direct/range {v1 .. v40}, LIa1/g$a;-><init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;LIa1/h;)V

    .line 208
    .line 209
    .line 210
    return-object v1
.end method
