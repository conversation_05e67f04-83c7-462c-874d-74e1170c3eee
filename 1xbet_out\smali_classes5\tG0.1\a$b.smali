.class public final LtG0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtG0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtG0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtG0/a$b$a;,
        LtG0/a$b$b;,
        LtG0/a$b$c;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lastgames/presentation/viewmodel/LastGameSharedViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LtG0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpG0/c;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LpG0/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/lastgames/data/repository/LastGameRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/i;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/c;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxG0/k;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQD0/d;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/i;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtG0/a$b;->a:LtG0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p17}, LtG0/a$b;->c(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V

    .line 5
    invoke-virtual/range {p0 .. p17}, LtG0/a$b;->d(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;LtG0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p17}, LtG0/a$b;-><init>(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtG0/a$b;->e(Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;)Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/lastgames/presentation/fragments/ViewPagerFragment;)V
    .locals 0

    .line 1
    return-void
.end method

.method public final c(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LtG0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    new-instance p3, LtG0/a$b$a;

    .line 8
    .line 9
    invoke-direct {p3, p1}, LtG0/a$b$a;-><init>(LQW0/c;)V

    .line 10
    .line 11
    .line 12
    iput-object p3, p0, LtG0/a$b;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, LtG0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p1}, LpG0/d;->a(LBc/a;)LpG0/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LtG0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {}, LtG0/g;->a()LtG0/g;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-static {p1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, LtG0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LtG0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    iget-object p3, p0, LtG0/a$b;->c:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object p4, p0, LtG0/a$b;->e:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object p6, p0, LtG0/a$b;->f:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p3, p4, p6, p1}, Lorg/xbet/statistic/lastgames/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/lastgames/data/repository/a;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LtG0/a$b;->h:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p1}, LxG0/j;->a(LBc/a;)LxG0/j;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LtG0/a$b;->i:Ldagger/internal/h;

    .line 59
    .line 60
    iget-object p1, p0, LtG0/a$b;->h:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {p1}, LxG0/b;->a(LBc/a;)LxG0/b;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, LtG0/a$b;->j:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object p1, p0, LtG0/a$b;->h:Ldagger/internal/h;

    .line 69
    .line 70
    invoke-static {p1}, LxG0/d;->a(LBc/a;)LxG0/d;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iput-object p1, p0, LtG0/a$b;->k:Ldagger/internal/h;

    .line 75
    .line 76
    iget-object p1, p0, LtG0/a$b;->h:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p1}, LxG0/l;->a(LBc/a;)LxG0/l;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    iput-object p1, p0, LtG0/a$b;->l:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LtG0/a$b;->m:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LtG0/a$b;->n:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LtG0/a$b;->o:Ldagger/internal/h;

    .line 101
    .line 102
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    iput-object p1, p0, LtG0/a$b;->p:Ldagger/internal/h;

    .line 107
    .line 108
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    iput-object p1, p0, LtG0/a$b;->q:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    iput-object p1, p0, LtG0/a$b;->r:Ldagger/internal/h;

    .line 119
    .line 120
    new-instance p1, LtG0/a$b$b;

    .line 121
    .line 122
    invoke-direct {p1, p2}, LtG0/a$b$b;-><init>(LEN0/f;)V

    .line 123
    .line 124
    .line 125
    iput-object p1, p0, LtG0/a$b;->s:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object p3, p0, LtG0/a$b;->o:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object p4, p0, LtG0/a$b;->p:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object p6, p0, LtG0/a$b;->q:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object p7, p0, LtG0/a$b;->r:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static {p3, p4, p6, p7, p1}, Lorg/xbet/statistic/statistic_core/presentation/delegates/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/j;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    iput-object p1, p0, LtG0/a$b;->t:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object p1, p0, LtG0/a$b;->u:Ldagger/internal/h;

    .line 146
    .line 147
    new-instance p1, LtG0/a$b$c;

    .line 148
    .line 149
    invoke-direct {p1, p2}, LtG0/a$b$c;-><init>(LEN0/f;)V

    .line 150
    .line 151
    .line 152
    iput-object p1, p0, LtG0/a$b;->v:Ldagger/internal/h;

    .line 153
    .line 154
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iput-object p1, p0, LtG0/a$b;->w:Ldagger/internal/h;

    .line 159
    .line 160
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    iput-object p1, p0, LtG0/a$b;->x:Ldagger/internal/h;

    .line 165
    .line 166
    iget-object p2, p0, LtG0/a$b;->c:Ldagger/internal/h;

    .line 167
    .line 168
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    iput-object p1, p0, LtG0/a$b;->y:Ldagger/internal/h;

    .line 173
    .line 174
    iget-object p1, p0, LtG0/a$b;->v:Ldagger/internal/h;

    .line 175
    .line 176
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 177
    .line 178
    .line 179
    move-result-object p1

    .line 180
    iput-object p1, p0, LtG0/a$b;->z:Ldagger/internal/h;

    .line 181
    .line 182
    return-void
.end method

.method public final d(LQW0/c;LEN0/f;Lorg/xbet/ui_common/utils/M;Lf8/g;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;Ljava/lang/String;Lorg/xbet/statistic/statistic_core/domain/models/TeamPagerModel;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LwX0/c;LSX0/a;LHX0/e;LkC0/a;LDH0/a;Lc8/h;)V
    .locals 13

    .line 1
    iget-object p1, p0, LtG0/a$b;->v:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iput-object p1, p0, LtG0/a$b;->A:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object v0, p0, LtG0/a$b;->w:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object v1, p0, LtG0/a$b;->y:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v2, p0, LtG0/a$b;->z:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object v3, p0, LtG0/a$b;->b:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v4, p0, LtG0/a$b;->u:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v5, p0, LtG0/a$b;->m:Ldagger/internal/h;

    .line 20
    .line 21
    move-object/from16 p6, p1

    .line 22
    .line 23
    move-object p2, v0

    .line 24
    move-object/from16 p3, v1

    .line 25
    .line 26
    move-object/from16 p4, v2

    .line 27
    .line 28
    move-object/from16 p5, v3

    .line 29
    .line 30
    move-object/from16 p7, v4

    .line 31
    .line 32
    move-object/from16 p8, v5

    .line 33
    .line 34
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iput-object p1, p0, LtG0/a$b;->B:Ldagger/internal/h;

    .line 39
    .line 40
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iput-object p1, p0, LtG0/a$b;->C:Ldagger/internal/h;

    .line 45
    .line 46
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, LtG0/a$b;->D:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object v0, p0, LtG0/a$b;->b:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object v1, p0, LtG0/a$b;->i:Ldagger/internal/h;

    .line 55
    .line 56
    iget-object v2, p0, LtG0/a$b;->j:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object v3, p0, LtG0/a$b;->k:Ldagger/internal/h;

    .line 59
    .line 60
    iget-object v4, p0, LtG0/a$b;->l:Ldagger/internal/h;

    .line 61
    .line 62
    iget-object v5, p0, LtG0/a$b;->m:Ldagger/internal/h;

    .line 63
    .line 64
    iget-object v6, p0, LtG0/a$b;->n:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object v7, p0, LtG0/a$b;->t:Ldagger/internal/h;

    .line 67
    .line 68
    iget-object v8, p0, LtG0/a$b;->u:Ldagger/internal/h;

    .line 69
    .line 70
    iget-object v9, p0, LtG0/a$b;->B:Ldagger/internal/h;

    .line 71
    .line 72
    iget-object v10, p0, LtG0/a$b;->C:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object v11, p0, LtG0/a$b;->q:Ldagger/internal/h;

    .line 75
    .line 76
    iget-object v12, p0, LtG0/a$b;->c:Ldagger/internal/h;

    .line 77
    .line 78
    move-object/from16 p14, p1

    .line 79
    .line 80
    move-object p2, v0

    .line 81
    move-object/from16 p3, v1

    .line 82
    .line 83
    move-object/from16 p4, v2

    .line 84
    .line 85
    move-object/from16 p5, v3

    .line 86
    .line 87
    move-object/from16 p6, v4

    .line 88
    .line 89
    move-object/from16 p7, v5

    .line 90
    .line 91
    move-object/from16 p8, v6

    .line 92
    .line 93
    move-object/from16 p9, v7

    .line 94
    .line 95
    move-object/from16 p10, v8

    .line 96
    .line 97
    move-object/from16 p11, v9

    .line 98
    .line 99
    move-object/from16 p12, v10

    .line 100
    .line 101
    move-object/from16 p13, v11

    .line 102
    .line 103
    move-object/from16 p15, v12

    .line 104
    .line 105
    invoke-static/range {p2 .. p15}, Lorg/xbet/statistic/lastgames/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/lastgames/presentation/viewmodel/a;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    iput-object p1, p0, LtG0/a$b;->E:Ldagger/internal/h;

    .line 110
    .line 111
    return-void
.end method

.method public final e(Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;)Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtG0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/lastgames/presentation/fragments/f;->a(Lorg/xbet/statistic/lastgames/presentation/fragments/LastGameFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/lastgames/presentation/viewmodel/LastGameSharedViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LtG0/a$b;->E:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LtG0/a$b;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
