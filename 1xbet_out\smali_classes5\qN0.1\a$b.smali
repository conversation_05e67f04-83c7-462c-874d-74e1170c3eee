.class public final LqN0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LqN0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LqN0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LqN0/a$b$a;,
        LqN0/a$b$c;,
        LqN0/a$b$b;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LkC0/a;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQD0/d;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/i;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtN0/c;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/a;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LSX0/a;

.field public final b:LqN0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LnN0/c;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LnN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results_grid/data/repository/ResultsGridRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtN0/e;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/ResultsGridViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LqN0/a$b;->b:LqN0/a$b;

    .line 4
    iput-object p7, p0, LqN0/a$b;->a:LSX0/a;

    .line 5
    invoke-virtual/range {p0 .. p17}, LqN0/a$b;->d(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V

    .line 6
    invoke-virtual/range {p0 .. p17}, LqN0/a$b;->e(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;LqN0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p17}, LqN0/a$b;-><init>(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LqN0/a$b;->g(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LqN0/a$b;->f(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LqN0/a$b;->h(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final d(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V
    .locals 7

    .line 1
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LqN0/a$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, LnN0/d;->a(LBc/a;)LnN0/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LqN0/a$b;->d:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LqN0/a$b;->e:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    iput-object p3, p0, LqN0/a$b;->f:Ldagger/internal/h;

    .line 24
    .line 25
    new-instance p3, LqN0/a$b$a;

    .line 26
    .line 27
    invoke-direct {p3, p1}, LqN0/a$b$a;-><init>(LQW0/c;)V

    .line 28
    .line 29
    .line 30
    iput-object p3, p0, LqN0/a$b;->g:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p1, p0, LqN0/a$b;->d:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object v0, p0, LqN0/a$b;->e:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v1, p0, LqN0/a$b;->f:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {p1, v0, v1, p3}, Lorg/xbet/statistic/stat_results/impl/results_grid/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results_grid/data/repository/a;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LqN0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, LtN0/f;->a(LBc/a;)LtN0/f;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LqN0/a$b;->i:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, LqN0/a$b;->j:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LqN0/a$b;->k:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    iput-object p1, p0, LqN0/a$b;->l:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, LqN0/a$b;->m:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iput-object p1, p0, LqN0/a$b;->n:Ldagger/internal/h;

    .line 79
    .line 80
    new-instance p1, LqN0/a$b$c;

    .line 81
    .line 82
    invoke-direct {p1, p2}, LqN0/a$b$c;-><init>(LEN0/f;)V

    .line 83
    .line 84
    .line 85
    iput-object p1, p0, LqN0/a$b;->o:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LqN0/a$b;->p:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LqN0/a$b;->q:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p2, p0, LqN0/a$b;->g:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LqN0/a$b;->r:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object p1, p0, LqN0/a$b;->o:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LqN0/a$b;->s:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p1, p0, LqN0/a$b;->o:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    iput-object p1, p0, LqN0/a$b;->t:Ldagger/internal/h;

    .line 122
    .line 123
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 124
    .line 125
    .line 126
    move-result-object v5

    .line 127
    iput-object v5, p0, LqN0/a$b;->u:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object v0, p0, LqN0/a$b;->p:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v1, p0, LqN0/a$b;->r:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object v2, p0, LqN0/a$b;->s:Ldagger/internal/h;

    .line 134
    .line 135
    iget-object v3, p0, LqN0/a$b;->k:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object v4, p0, LqN0/a$b;->t:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object v6, p0, LqN0/a$b;->l:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static/range {v0 .. v6}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object p1, p0, LqN0/a$b;->v:Ldagger/internal/h;

    .line 146
    .line 147
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    iput-object p1, p0, LqN0/a$b;->w:Ldagger/internal/h;

    .line 152
    .line 153
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    iput-object p1, p0, LqN0/a$b;->x:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p2, p0, LqN0/a$b;->i:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object p3, p0, LqN0/a$b;->j:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object p4, p0, LqN0/a$b;->k:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object p5, p0, LqN0/a$b;->l:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v0, p0, LqN0/a$b;->m:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v1, p0, LqN0/a$b;->n:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v2, p0, LqN0/a$b;->g:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v3, p0, LqN0/a$b;->v:Ldagger/internal/h;

    .line 174
    .line 175
    iget-object v4, p0, LqN0/a$b;->w:Ldagger/internal/h;

    .line 176
    .line 177
    move-object/from16 p11, p1

    .line 178
    .line 179
    move-object p6, v0

    .line 180
    move-object p7, v1

    .line 181
    move-object p8, v2

    .line 182
    move-object/from16 p9, v3

    .line 183
    .line 184
    move-object/from16 p10, v4

    .line 185
    .line 186
    invoke-static/range {p2 .. p11}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/f;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/f;

    .line 187
    .line 188
    .line 189
    move-result-object p1

    .line 190
    iput-object p1, p0, LqN0/a$b;->y:Ldagger/internal/h;

    .line 191
    .line 192
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 193
    .line 194
    .line 195
    move-result-object p1

    .line 196
    iput-object p1, p0, LqN0/a$b;->z:Ldagger/internal/h;

    .line 197
    .line 198
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    iput-object p1, p0, LqN0/a$b;->A:Ldagger/internal/h;

    .line 203
    .line 204
    return-void
.end method

.method public final e(LQW0/c;LEN0/f;Lf8/g;Ljava/lang/String;Ljava/lang/Long;LHX0/e;LSX0/a;Li8/l;Lorg/xbet/ui_common/utils/M;Lorg/xbet/ui_common/utils/internet/a;LnN0/a;LTn/a;Lorg/xbet/onexdatabase/OnexDatabase;LDH0/a;LwX0/c;LkC0/a;Lc8/h;)V
    .locals 0

    .line 1
    new-instance p1, LqN0/a$b$b;

    .line 2
    .line 3
    invoke-direct {p1, p2}, LqN0/a$b$b;-><init>(LEN0/f;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, LqN0/a$b;->B:Ldagger/internal/h;

    .line 7
    .line 8
    iget-object p2, p0, LqN0/a$b;->z:Ldagger/internal/h;

    .line 9
    .line 10
    iget-object p3, p0, LqN0/a$b;->m:Ldagger/internal/h;

    .line 11
    .line 12
    iget-object p4, p0, LqN0/a$b;->w:Ldagger/internal/h;

    .line 13
    .line 14
    iget-object p5, p0, LqN0/a$b;->A:Ldagger/internal/h;

    .line 15
    .line 16
    invoke-static {p2, p3, p4, p5, p1}, Lorg/xbet/statistic/statistic_core/presentation/delegates/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/j;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    iput-object p1, p0, LqN0/a$b;->C:Ldagger/internal/h;

    .line 21
    .line 22
    iget-object p1, p0, LqN0/a$b;->h:Ldagger/internal/h;

    .line 23
    .line 24
    invoke-static {p1}, LtN0/d;->a(LBc/a;)LtN0/d;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iput-object p1, p0, LqN0/a$b;->D:Ldagger/internal/h;

    .line 29
    .line 30
    iget-object p2, p0, LqN0/a$b;->C:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p3, p0, LqN0/a$b;->l:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object p4, p0, LqN0/a$b;->u:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p2, p3, p1, p4}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/d;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/d;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LqN0/a$b;->E:Ldagger/internal/h;

    .line 41
    .line 42
    iget-object p1, p0, LqN0/a$b;->h:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, LtN0/b;->a(LBc/a;)LtN0/b;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LqN0/a$b;->F:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object p2, p0, LqN0/a$b;->l:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object p3, p0, LqN0/a$b;->m:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p1, p2, p3}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/b;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/b;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LqN0/a$b;->G:Ldagger/internal/h;

    .line 59
    .line 60
    return-void
.end method

.method public final f(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LqN0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/e;->b(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LqN0/a$b;->a:LSX0/a;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/e;->a(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;LSX0/a;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LqN0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/i;->a(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;)Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LqN0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/l;->a(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final i()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x3

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/ResultsGridViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LqN0/a$b;->y:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/c;

    .line 15
    .line 16
    iget-object v2, p0, LqN0/a$b;->E:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/viewmodel/a;

    .line 23
    .line 24
    iget-object v2, p0, LqN0/a$b;->G:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    return-object v0
.end method

.method public final j()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LqN0/a$b;->i()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
