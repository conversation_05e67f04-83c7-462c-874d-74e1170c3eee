.class public final Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\"\u0010\u000c\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b0\t0\u0008H\u0086\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;",
        "",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
        "getTournamentPrizesFlowUseCase",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;",
        "getUserPlaceModelFlowUseCase",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lkotlin/Pair;",
        "Lp40/b;",
        "Lp40/c;",
        "a",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;",
        "b",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;->a:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;->b:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Lkotlinx/coroutines/flow/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lkotlin/Pair<",
            "Lp40/b;",
            "Lp40/c;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;->a:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/f;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario;->b:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;->a()Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    new-instance v2, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;

    .line 14
    .line 15
    const/4 v3, 0x0

    .line 16
    invoke-direct {v2, v3}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentItemFlowScenario$invoke$1;-><init>(Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    return-object v0
.end method
