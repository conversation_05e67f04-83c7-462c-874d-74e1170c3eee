.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->g(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;LUX0/k;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;Lkotlin/jvm/functions/Function0;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->b:Lkotlin/jvm/functions/Function0;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->c:LB4/a;

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->a:LB4/a;

    .line 8
    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LS91/A0;

    .line 14
    .line 15
    iget-object p1, p1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->a:LB4/a;

    .line 18
    .line 19
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    check-cast v0, Lza1/a$i;

    .line 24
    .line 25
    invoke-virtual {v0}, Lza1/a$i;->d()Lk21/k;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setModel(Lk21/k;)V

    .line 30
    .line 31
    .line 32
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->a:LB4/a;

    .line 33
    .line 34
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    check-cast p1, Lza1/a$i;

    .line 39
    .line 40
    invoke-virtual {p1}, Lza1/a$i;->d()Lk21/k;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    instance-of p1, p1, Lk21/l;

    .line 45
    .line 46
    if-eqz p1, :cond_6

    .line 47
    .line 48
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->b:Lkotlin/jvm/functions/Function0;

    .line 49
    .line 50
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 55
    .line 56
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 57
    .line 58
    .line 59
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    if-eqz v1, :cond_1

    .line 68
    .line 69
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    check-cast v1, Ljava/util/Collection;

    .line 74
    .line 75
    check-cast v1, Ljava/lang/Iterable;

    .line 76
    .line 77
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 78
    .line 79
    .line 80
    goto :goto_0

    .line 81
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 86
    .line 87
    .line 88
    move-result v0

    .line 89
    if-eqz v0, :cond_6

    .line 90
    .line 91
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v0

    .line 95
    check-cast v0, Lk21/j;

    .line 96
    .line 97
    instance-of v1, v0, Lk21/j$d;

    .line 98
    .line 99
    if-eqz v1, :cond_2

    .line 100
    .line 101
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->c:LB4/a;

    .line 102
    .line 103
    invoke-virtual {v1}, LB4/a;->e()LL2/a;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    check-cast v1, LS91/A0;

    .line 108
    .line 109
    iget-object v1, v1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 110
    .line 111
    check-cast v0, Lk21/j$d;

    .line 112
    .line 113
    invoke-virtual {v0}, Lk21/j$d;->f()Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setTitle(Ljava/lang/String;)V

    .line 118
    .line 119
    .line 120
    goto :goto_1

    .line 121
    :cond_2
    instance-of v1, v0, Lk21/j$a;

    .line 122
    .line 123
    if-eqz v1, :cond_3

    .line 124
    .line 125
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->c:LB4/a;

    .line 126
    .line 127
    invoke-virtual {v1}, LB4/a;->e()LL2/a;

    .line 128
    .line 129
    .line 130
    move-result-object v1

    .line 131
    check-cast v1, LS91/A0;

    .line 132
    .line 133
    iget-object v1, v1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 134
    .line 135
    check-cast v0, Lk21/j$a;

    .line 136
    .line 137
    invoke-virtual {v0}, Lk21/j$a;->f()Ljava/lang/String;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setButtonText(Ljava/lang/String;)V

    .line 142
    .line 143
    .line 144
    goto :goto_1

    .line 145
    :cond_3
    instance-of v1, v0, Lk21/j$c;

    .line 146
    .line 147
    if-eqz v1, :cond_4

    .line 148
    .line 149
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->c:LB4/a;

    .line 150
    .line 151
    invoke-virtual {v1}, LB4/a;->e()LL2/a;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    check-cast v1, LS91/A0;

    .line 156
    .line 157
    iget-object v1, v1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 158
    .line 159
    check-cast v0, Lk21/j$c;

    .line 160
    .line 161
    invoke-virtual {v0}, Lk21/j$c;->a()Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->y(Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;)V

    .line 166
    .line 167
    .line 168
    goto :goto_1

    .line 169
    :cond_4
    instance-of v1, v0, Lk21/j$b;

    .line 170
    .line 171
    if-eqz v1, :cond_5

    .line 172
    .line 173
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->c:LB4/a;

    .line 174
    .line 175
    invoke-virtual {v1}, LB4/a;->e()LL2/a;

    .line 176
    .line 177
    .line 178
    move-result-object v1

    .line 179
    check-cast v1, LS91/A0;

    .line 180
    .line 181
    iget-object v1, v1, LS91/A0;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;

    .line 182
    .line 183
    check-cast v0, Lk21/j$b;

    .line 184
    .line 185
    invoke-virtual {v0}, Lk21/j$b;->a()Ljava/util/List;

    .line 186
    .line 187
    .line 188
    move-result-object v0

    .line 189
    const/4 v2, 0x2

    .line 190
    const/4 v3, 0x0

    .line 191
    invoke-static {v1, v0, v3, v2, v3}, Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;->setItems$default(Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/DSAggregatorTournamentCardsCollection;Ljava/util/List;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 192
    .line 193
    .line 194
    goto :goto_1

    .line 195
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 196
    .line 197
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 198
    .line 199
    .line 200
    throw p1

    .line 201
    :cond_6
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
