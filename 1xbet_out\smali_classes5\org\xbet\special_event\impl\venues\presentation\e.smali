.class public final synthetic Lorg/xbet/special_event/impl/venues/presentation/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/venues/presentation/e;->a:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/venues/presentation/e;->a:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;

    invoke-static {v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->A2(Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;)Lsy0/f;

    move-result-object v0

    return-object v0
.end method
