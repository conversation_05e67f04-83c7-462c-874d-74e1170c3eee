.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000]\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u000b*\u0001\u000b\u0008\u0000\u0018\u0000 >2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001?B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0004J\u000f\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0004J\u0019\u0010\u0011\u001a\u00020\u00072\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0004J\u000f\u0010\u0014\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0004J\u000f\u0010\u0015\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0004J\u000f\u0010\u0017\u001a\u00020\u0016H\u0010\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u001a\u001a\u00020\u0019H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0019\u0010\u001c\u001a\u00020\u00072\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0014\u00a2\u0006\u0004\u0008\u001c\u0010\u0012J\u000f\u0010\u001d\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u001d\u0010\u0004R\u001b\u0010#\u001a\u00020\u001e8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R\"\u0010+\u001a\u00020$8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(\"\u0004\u0008)\u0010*R\u001b\u00100\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R\u0014\u00104\u001a\u0002018\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R+\u0010=\u001a\u0002052\u0006\u00106\u001a\u0002058B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieEmptyConfig",
        "",
        "L3",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "D3",
        "org/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b",
        "I3",
        "()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;",
        "J3",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "onResume",
        "onPause",
        "u2",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "t2",
        "v2",
        "LS91/X;",
        "o0",
        "LRc/c;",
        "z3",
        "()LS91/X;",
        "binding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "b1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "C3",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "k1",
        "Lkotlin/j;",
        "B3",
        "()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;",
        "viewModel",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "v1",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "pagingAdapterObserver",
        "",
        "<set-?>",
        "x1",
        "LeX0/f;",
        "A3",
        "()J",
        "K3",
        "(J)V",
        "partitionId",
        "y1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic F1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final y1:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public b1:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Landroidx/recyclerview/widget/RecyclerView$i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    .line 4
    .line 5
    const-string v2, "binding"

    .line 6
    .line 7
    const-string v3, "getBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentRecomendedGamesBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "partitionId"

    .line 20
    .line 21
    const-string v5, "getPartitionId()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->F1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->y1:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$a;

    .line 47
    .line 48
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lu91/c;->fragment_recomended_games:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$binding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->o0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/n;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/n;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->k1:Lkotlin/j;

    .line 57
    .line 58
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->I3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->v1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 63
    .line 64
    new-instance v1, LeX0/f;

    .line 65
    .line 66
    const/4 v5, 0x2

    .line 67
    const/4 v6, 0x0

    .line 68
    const-string v2, "partitionId"

    .line 69
    .line 70
    const-wide/16 v3, 0x0

    .line 71
    .line 72
    invoke-direct/range {v1 .. v6}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 73
    .line 74
    .line 75
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->x1:LeX0/f;

    .line 76
    .line 77
    return-void
.end method

.method private final A3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->x1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->F1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method private final D3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/X;->e:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public static final E3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->K4(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final F3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LN21/k;->e()J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    invoke-virtual {p0, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->J4(J)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final G3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LN21/k;->e()J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    invoke-virtual {p1}, LN21/k;->c()LN21/m;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p1}, LN21/m;->b()Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-virtual {p0, v0, v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->N4(JZ)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final H3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->F4()V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Landroidx/paging/u;->d()Landroidx/paging/s;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 23
    .line 24
    const/4 v2, 0x0

    .line 25
    if-eqz v1, :cond_0

    .line 26
    .line 27
    check-cast v0, Landroidx/paging/s$a;

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    move-object v0, v2

    .line 31
    :goto_0
    if-nez v0, :cond_5

    .line 32
    .line 33
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {v0}, Landroidx/paging/u;->e()Landroidx/paging/s;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 42
    .line 43
    if-eqz v1, :cond_1

    .line 44
    .line 45
    check-cast v0, Landroidx/paging/s$a;

    .line 46
    .line 47
    goto :goto_1

    .line 48
    :cond_1
    move-object v0, v2

    .line 49
    :goto_1
    if-nez v0, :cond_5

    .line 50
    .line 51
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0}, Landroidx/paging/u;->f()Landroidx/paging/s;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 60
    .line 61
    if-eqz v1, :cond_2

    .line 62
    .line 63
    check-cast v0, Landroidx/paging/s$a;

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_2
    move-object v0, v2

    .line 67
    :goto_2
    if-nez v0, :cond_5

    .line 68
    .line 69
    invoke-virtual {p2}, Landroidx/paging/f;->a()Landroidx/paging/s;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 74
    .line 75
    if-eqz v1, :cond_3

    .line 76
    .line 77
    check-cast v0, Landroidx/paging/s$a;

    .line 78
    .line 79
    goto :goto_3

    .line 80
    :cond_3
    move-object v0, v2

    .line 81
    :goto_3
    if-nez v0, :cond_5

    .line 82
    .line 83
    invoke-virtual {p2}, Landroidx/paging/f;->c()Landroidx/paging/s;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 88
    .line 89
    if-eqz v1, :cond_4

    .line 90
    .line 91
    check-cast v0, Landroidx/paging/s$a;

    .line 92
    .line 93
    goto :goto_4

    .line 94
    :cond_4
    move-object v0, v2

    .line 95
    :goto_4
    if-nez v0, :cond_5

    .line 96
    .line 97
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 102
    .line 103
    if-eqz v1, :cond_6

    .line 104
    .line 105
    move-object v2, v0

    .line 106
    check-cast v2, Landroidx/paging/s$a;

    .line 107
    .line 108
    goto :goto_5

    .line 109
    :cond_5
    move-object v2, v0

    .line 110
    :cond_6
    :goto_5
    if-eqz v2, :cond_7

    .line 111
    .line 112
    invoke-virtual {v2}, Landroidx/paging/s$a;->b()Ljava/lang/Throwable;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    invoke-virtual {p1, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->G4(Ljava/lang/Throwable;)V

    .line 121
    .line 122
    .line 123
    :cond_7
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    instance-of p1, p1, Landroidx/paging/s$b;

    .line 128
    .line 129
    if-nez p1, :cond_8

    .line 130
    .line 131
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 132
    .line 133
    .line 134
    move-result p1

    .line 135
    :cond_8
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    instance-of p1, p1, Landroidx/paging/s$b;

    .line 140
    .line 141
    if-nez p1, :cond_9

    .line 142
    .line 143
    if-nez v2, :cond_9

    .line 144
    .line 145
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 146
    .line 147
    .line 148
    :cond_9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 149
    .line 150
    return-object p0
.end method

.method private final K3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->x1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->F1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final L3(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/X;->e:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static final M3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->C3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->E3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->M3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->H3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->G3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->F3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)LS91/X;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->D3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->J3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->K3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->d3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->e3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->L3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final C3()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->b1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final I3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final J3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/X;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/X;->f:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/m;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/m;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->v1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->v1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->D4()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 26
    .line 27
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget v1, LlZ0/g;->space_12:I

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    const/4 v1, 0x0

    .line 38
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->q(II)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iget-object p1, p1, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 46
    .line 47
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/o;

    .line 48
    .line 49
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/o;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 53
    .line 54
    .line 55
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object p1, p1, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/p;

    .line 62
    .line 63
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/p;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->z3()LS91/X;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iget-object p1, p1, LS91/X;->h:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 74
    .line 75
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    if-eqz p1, :cond_0

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;

    .line 82
    .line 83
    invoke-direct {v0, p1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/q;-><init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p1, v0}, Landroidx/paging/PagingDataAdapter;->p(Lkotlin/jvm/functions/Function1;)V

    .line 87
    .line 88
    .line 89
    :cond_0
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lna1/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lna1/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lna1/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    const/4 v0, 0x0

    .line 53
    invoke-virtual {v2, v0}, Lna1/d;->a(Z)Lna1/c;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-interface {v0, p0}, Lna1/c;->b(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 58
    .line 59
    .line 60
    return-void

    .line 61
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 62
    .line 63
    new-instance v2, Ljava/lang/StringBuilder;

    .line 64
    .line 65
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 66
    .line 67
    .line 68
    const-string v3, "Cannot create dependency "

    .line 69
    .line 70
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 85
    .line 86
    .line 87
    throw v0
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->M4()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->A3()J

    .line 15
    .line 16
    .line 17
    move-result-wide v2

    .line 18
    invoke-virtual {v1, v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->C4(J)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v5

    .line 22
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v6

    .line 26
    sget-object v7, Landroidx/lifecycle/Lifecycle$State;->CREATED:Landroidx/lifecycle/Lifecycle$State;

    .line 27
    .line 28
    new-instance v8, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$1;

    .line 29
    .line 30
    const/4 v1, 0x0

    .line 31
    invoke-direct {v8, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v6}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 39
    .line 40
    const/4 v9, 0x0

    .line 41
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 42
    .line 43
    .line 44
    const/4 v13, 0x3

    .line 45
    const/4 v14, 0x0

    .line 46
    const/4 v10, 0x0

    .line 47
    const/4 v11, 0x0

    .line 48
    move-object v9, v2

    .line 49
    move-object v12, v4

    .line 50
    invoke-static/range {v9 .. v14}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->z4()Lkotlinx/coroutines/flow/Z;

    .line 58
    .line 59
    .line 60
    move-result-object v4

    .line 61
    new-instance v7, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$2;

    .line 62
    .line 63
    invoke-direct {v7, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    sget-object v11, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 67
    .line 68
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 69
    .line 70
    .line 71
    move-result-object v5

    .line 72
    invoke-static {v5}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 73
    .line 74
    .line 75
    move-result-object v12

    .line 76
    new-instance v15, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 77
    .line 78
    const/4 v8, 0x0

    .line 79
    move-object v6, v11

    .line 80
    move-object v3, v15

    .line 81
    invoke-direct/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 82
    .line 83
    .line 84
    const/16 v16, 0x3

    .line 85
    .line 86
    const/16 v17, 0x0

    .line 87
    .line 88
    const/4 v13, 0x0

    .line 89
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 93
    .line 94
    .line 95
    move-result-object v2

    .line 96
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->A4()Lkotlinx/coroutines/flow/e;

    .line 97
    .line 98
    .line 99
    move-result-object v9

    .line 100
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 101
    .line 102
    .line 103
    move-result-object v10

    .line 104
    new-instance v12, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$3;

    .line 105
    .line 106
    invoke-direct {v12, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lkotlin/coroutines/e;)V

    .line 107
    .line 108
    .line 109
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 110
    .line 111
    .line 112
    move-result-object v2

    .line 113
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 114
    .line 115
    move-object v8, v5

    .line 116
    invoke-direct/range {v8 .. v13}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 117
    .line 118
    .line 119
    const/4 v6, 0x3

    .line 120
    const/4 v7, 0x0

    .line 121
    const/4 v3, 0x0

    .line 122
    const/4 v4, 0x0

    .line 123
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 124
    .line 125
    .line 126
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->B3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;

    .line 127
    .line 128
    .line 129
    move-result-object v2

    .line 130
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/RecommendedGamesViewModel;->E4()Lkotlinx/coroutines/flow/e;

    .line 131
    .line 132
    .line 133
    move-result-object v9

    .line 134
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 135
    .line 136
    .line 137
    move-result-object v10

    .line 138
    new-instance v12, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$4;

    .line 139
    .line 140
    invoke-direct {v12, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;Lkotlin/coroutines/e;)V

    .line 141
    .line 142
    .line 143
    invoke-static {v10}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 144
    .line 145
    .line 146
    move-result-object v2

    .line 147
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 148
    .line 149
    move-object v8, v5

    .line 150
    invoke-direct/range {v8 .. v13}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 151
    .line 152
    .line 153
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 154
    .line 155
    .line 156
    return-void
.end method

.method public final z3()LS91/X;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->F1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/X;

    .line 13
    .line 14
    return-object v0
.end method
