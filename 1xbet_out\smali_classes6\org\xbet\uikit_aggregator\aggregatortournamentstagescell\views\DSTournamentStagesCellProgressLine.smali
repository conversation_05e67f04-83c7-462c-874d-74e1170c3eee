.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;
.super Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\t\n\u0002\u0010\r\n\u0002\u0008\t\n\u0002\u0010\u000b\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 S2\u00020\u0001:\u0001TB\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u000f\u0010\u0010\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\nJ\u000f\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\nJ\u000f\u0010\u0012\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\nJ\u000f\u0010\u0013\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\nJ\u000f\u0010\u0014\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\nJ\u001b\u0010\u0017\u001a\u00020\u00082\n\u0008\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u001b\u0010\u0019\u001a\u00020\u00082\n\u0008\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\u001b\u0010\u001a\u001a\u00020\u00082\n\u0008\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0018J\u001f\u0010\u001d\u001a\u00020\u00082\u0006\u0010\u001b\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ7\u0010%\u001a\u00020\u00082\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u000b2\u0006\u0010\"\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008%\u0010&J\u0019\u0010\'\u001a\u00020\u00082\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0016\u00a2\u0006\u0004\u0008\'\u0010\u0018J\u0019\u0010(\u001a\u00020\u00082\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0016\u00a2\u0006\u0004\u0008(\u0010\u0018J\u0019\u0010)\u001a\u00020\u00082\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0016\u00a2\u0006\u0004\u0008)\u0010\u0018J\u0017\u0010+\u001a\u00020\u00082\u0006\u0010*\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008+\u0010\u000eJ\u0017\u0010-\u001a\u00020\u00082\u0006\u0010,\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008-\u0010\u000eJ\u0017\u00100\u001a\u00020\u00082\u0006\u0010/\u001a\u00020.H\u0016\u00a2\u0006\u0004\u00080\u00101J\u001f\u00102\u001a\u00020\u00082\u0006\u0010*\u001a\u00020\u000b2\u0006\u0010,\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u00082\u0010\u001eJ\u0017\u00103\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u00083\u00104J\u0017\u00105\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u00085\u0010\u000eJ\u0017\u00106\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u00086\u0010\u000eR\u0014\u00108\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u00107R\u0014\u0010:\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u00107R\u0014\u0010;\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u00107R\u0014\u0010=\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u00107R\u0014\u0010>\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00107R\u0014\u0010?\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u00107R\u0014\u0010@\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u00107R\u0014\u0010A\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u00107R\u0014\u0010B\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u00107R\u0014\u0010C\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u00107R\u0016\u0010*\u001a\u00020\u000b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\t\u00107R\u0014\u0010F\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u0010ER\u0014\u0010G\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010ER\u0014\u0010H\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u0010ER\u0014\u0010K\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010JR\u0014\u0010N\u001a\u00020L8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u0010MR\u001a\u0010R\u001a\u00020\u000b8\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008O\u00107\u001a\u0004\u0008P\u0010Q\u00a8\u0006U"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "p",
        "()V",
        "",
        "parentWidth",
        "t",
        "(I)V",
        "r",
        "k",
        "n",
        "o",
        "m",
        "l",
        "",
        "text",
        "f",
        "(Ljava/lang/CharSequence;)V",
        "h",
        "d",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "setStageNumberText",
        "setTitleText",
        "setCaptionText",
        "progress",
        "setProgress",
        "maxProgress",
        "setMaxProgress",
        "Lc31/a;",
        "state",
        "setState",
        "(Lc31/a;)V",
        "u",
        "j",
        "(I)I",
        "s",
        "q",
        "I",
        "space4",
        "g",
        "space6",
        "space8",
        "i",
        "space12",
        "space16",
        "size10",
        "size24",
        "size64",
        "size128",
        "minDisplayableProgressWidth",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStageNumber",
        "tvTitle",
        "tvCaption",
        "Landroid/widget/ProgressBar;",
        "Landroid/widget/ProgressBar;",
        "progressBar",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "ivStatusIcon",
        "v",
        "getCardHeight",
        "()I",
        "cardHeight",
        "w",
        "a",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final w:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final x:I


# instance fields
.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public p:I

.field public final q:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:Landroid/widget/ProgressBar;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->w:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->x:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 11
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_4:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->f:I

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_6:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->g:I

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_8:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->h:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_12:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_16:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_10:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->k:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_24:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->l:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v2, LlZ0/g;->size_64:I

    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->m:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->size_128:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->n:I

    .line 12
    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->o:I

    .line 13
    new-instance v2, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v2, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 14
    const-string v3, "DSAggregatorTournamentStagesCell.TAG_TV_STAGE_NUMBER"

    invoke-virtual {v2, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    const/4 v3, 0x0

    .line 15
    invoke-virtual {v2, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    const/4 v4, 0x1

    .line 16
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 17
    sget-object v5, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v2, v5}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v6

    const/4 v7, 0x3

    const/4 v8, 0x5

    if-eqz v6, :cond_0

    const/4 v6, 0x5

    goto :goto_0

    :cond_0
    const/4 v6, 0x3

    :goto_0
    invoke-virtual {v2, v6}, Landroid/widget/TextView;->setGravity(I)V

    .line 19
    iput-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    new-instance v6, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v6, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 21
    const-string v9, "DSAggregatorTournamentStagesCell.TAG_TV_TITLE"

    invoke-virtual {v6, v9}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 22
    sget v9, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v6, v9}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 23
    invoke-virtual {v6, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 24
    invoke-virtual {v6, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 25
    invoke-virtual {v6, v5}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 26
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v9

    if-eqz v9, :cond_1

    const/4 v9, 0x5

    goto :goto_1

    :cond_1
    const/4 v9, 0x3

    :goto_1
    invoke-virtual {v6, v9}, Landroid/widget/TextView;->setGravity(I)V

    .line 27
    iput-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 28
    new-instance v9, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v9, p1}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 29
    const-string v10, "DSAggregatorTournamentStagesCell.TAG_TV_CAPTION"

    invoke-virtual {v9, v10}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 30
    sget v10, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v9, v10}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 31
    invoke-virtual {v9, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 32
    invoke-virtual {v9, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 33
    invoke-virtual {v9, v5}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 34
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result v5

    if-eqz v5, :cond_2

    const/4 v7, 0x5

    :cond_2
    invoke-virtual {v9, v7}, Landroid/widget/TextView;->setGravity(I)V

    .line 35
    iput-object v9, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 36
    new-instance v5, Landroid/widget/ProgressBar;

    const v7, 0x103001f

    const/4 v8, 0x0

    invoke-direct {v5, p1, v8, v7}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 37
    new-instance v7, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v10, -0x1

    invoke-direct {v7, v10, p2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v7}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 38
    invoke-virtual {v5, v3, v3, v3, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 39
    sget p2, LlZ0/h;->rounded_background_4:I

    invoke-virtual {v5, p2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 40
    invoke-virtual {v5, v4}, Landroid/view/View;->setClipToOutline(Z)V

    .line 41
    sget p2, LlZ0/h;->ds_tournament_stages_cell_progress_line_progress_bar_bg:I

    .line 42
    invoke-static {p1, p2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {v5, p2}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 43
    invoke-virtual {v5, v3}, Landroid/widget/ProgressBar;->setIndeterminate(Z)V

    .line 44
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->c()Z

    move-result p2

    if-eqz p2, :cond_3

    const/high16 p2, -0x40800000    # -1.0f

    goto :goto_2

    :cond_3
    const/high16 p2, 0x3f800000    # 1.0f

    :goto_2
    invoke-virtual {v5, p2}, Landroid/view/View;->setScaleX(F)V

    .line 45
    iput-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 46
    new-instance p2, Landroidx/appcompat/widget/AppCompatImageView;

    invoke-direct {p2, p1}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 47
    const-string v3, "DSAggregatorTournamentStagesCell.TAG_IV_STATUS_ICON"

    invoke-virtual {p2, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 48
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    invoke-direct {v3, v0, v0}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {p2, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 49
    sget v0, LlZ0/h;->ic_glyph_circle_check:I

    invoke-virtual {p2, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 50
    sget v0, LlZ0/d;->uikitStaticGreen:I

    const/4 v3, 0x2

    invoke-static {p1, v0, v8, v3, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p1

    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object p1

    invoke-virtual {p2, p1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 51
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 52
    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->v:I

    .line 53
    sget p1, LlZ0/h;->rounded_background_16_content:I

    invoke-virtual {p0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 54
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 55
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 56
    invoke-virtual {p0, v6}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 57
    invoke-virtual {p0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 58
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final d(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    sget v2, LlZ0/g;->text_10:I

    .line 12
    .line 13
    sget v3, LlZ0/g;->text_12:I

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/4 p1, 0x0

    .line 23
    :goto_0
    if-nez p1, :cond_1

    .line 24
    .line 25
    const-string p1, ""

    .line 26
    .line 27
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->d(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final f(Ljava/lang/CharSequence;)V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    const-string v1, ""

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v0, :cond_2

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 13
    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 15
    .line 16
    .line 17
    move-result v3

    .line 18
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 19
    .line 20
    .line 21
    move-result v3

    .line 22
    sget v4, LlZ0/g;->text_20:I

    .line 23
    .line 24
    sget v5, LlZ0/g;->text_24:I

    .line 25
    .line 26
    if-eqz p1, :cond_0

    .line 27
    .line 28
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    :cond_0
    if-nez v2, :cond_1

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    move-object v1, v2

    .line 36
    :goto_0
    invoke-static {v0, v3, v4, v5, v1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 37
    .line 38
    .line 39
    return-void

    .line 40
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    sget v4, LlZ0/g;->text_16:I

    .line 51
    .line 52
    sget v5, LlZ0/g;->text_18:I

    .line 53
    .line 54
    if-eqz p1, :cond_3

    .line 55
    .line 56
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    :cond_3
    if-nez v2, :cond_4

    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_4
    move-object v1, v2

    .line 64
    :goto_1
    invoke-static {v0, v3, v4, v5, v1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 65
    .line 66
    .line 67
    return-void
.end method

.method public static synthetic g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->f(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final h(Ljava/lang/CharSequence;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    sget v2, LlZ0/g;->text_10:I

    .line 12
    .line 13
    sget v3, LlZ0/g;->text_12:I

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/4 p1, 0x0

    .line 23
    :goto_0
    if-nez p1, :cond_1

    .line 24
    .line 25
    const-string p1, ""

    .line 26
    .line 27
    :cond_1
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/uikit/utils/M;->d(Landroid/widget/TextView;IIILjava/lang/String;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static synthetic i(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    if-eqz p2, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->h(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method private final k()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 8
    .line 9
    sub-int/2addr v0, v2

    .line 10
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 11
    .line 12
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    sub-int v2, v0, v2

    .line 17
    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    div-int/lit8 v0, v0, 0x2

    .line 23
    .line 24
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 25
    .line 26
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 27
    .line 28
    .line 29
    move-result v3

    .line 30
    div-int/lit8 v3, v3, 0x2

    .line 31
    .line 32
    sub-int v3, v0, v3

    .line 33
    .line 34
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 39
    .line 40
    sub-int v4, v0, v4

    .line 41
    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    div-int/lit8 v0, v0, 0x2

    .line 47
    .line 48
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 49
    .line 50
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 51
    .line 52
    .line 53
    move-result v5

    .line 54
    div-int/lit8 v5, v5, 0x2

    .line 55
    .line 56
    add-int/2addr v5, v0

    .line 57
    move-object v0, p0

    .line 58
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method private final l()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 10
    .line 11
    sub-int/2addr v0, v3

    .line 12
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 13
    .line 14
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 15
    .line 16
    .line 17
    move-result v3

    .line 18
    sub-int v3, v0, v3

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 25
    .line 26
    sub-int v4, v0, v4

    .line 27
    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 33
    .line 34
    sub-int v5, v0, v5

    .line 35
    .line 36
    move-object v0, p0

    .line 37
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method private final m()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->g:I

    .line 25
    .line 26
    add-int v5, v0, v1

    .line 27
    .line 28
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 31
    .line 32
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 37
    .line 38
    sub-int v6, v0, v1

    .line 39
    .line 40
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 41
    .line 42
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    add-int v7, v5, v0

    .line 47
    .line 48
    move-object v2, p0

    .line 49
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 50
    .line 51
    .line 52
    :cond_0
    return-void
.end method

.method private final n()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Lc31/a$c;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 10
    .line 11
    :goto_0
    move v4, v0

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    div-int/lit8 v0, v0, 0x2

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 26
    .line 27
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    add-int/2addr v1, v2

    .line 32
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->f:I

    .line 33
    .line 34
    add-int/2addr v1, v2

    .line 35
    div-int/lit8 v1, v1, 0x2

    .line 36
    .line 37
    sub-int/2addr v0, v1

    .line 38
    goto :goto_0

    .line 39
    :goto_1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    instance-of v0, v0, Lc31/a$b;

    .line 44
    .line 45
    if-eqz v0, :cond_1

    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 52
    .line 53
    sub-int/2addr v0, v1

    .line 54
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 55
    .line 56
    sub-int/2addr v0, v1

    .line 57
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 58
    .line 59
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 60
    .line 61
    .line 62
    move-result v1

    .line 63
    :goto_2
    sub-int/2addr v0, v1

    .line 64
    move v5, v0

    .line 65
    goto :goto_3

    .line 66
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 67
    .line 68
    .line 69
    move-result v0

    .line 70
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 71
    .line 72
    goto :goto_2

    .line 73
    :goto_3
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 74
    .line 75
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 76
    .line 77
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    add-int v6, v4, v0

    .line 82
    .line 83
    move-object v1, p0

    .line 84
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 85
    .line 86
    .line 87
    return-void
.end method

.method private final o()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    instance-of v0, v0, Lc31/a$c;

    .line 15
    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->h:I

    .line 25
    .line 26
    :goto_0
    add-int/2addr v0, v1

    .line 27
    move v4, v0

    .line 28
    goto :goto_1

    .line 29
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getHelperRect()Landroid/graphics/Rect;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 34
    .line 35
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->f:I

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :goto_1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    instance-of v0, v0, Lc31/a$b;

    .line 43
    .line 44
    if-eqz v0, :cond_1

    .line 45
    .line 46
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 51
    .line 52
    sub-int/2addr v0, v1

    .line 53
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 54
    .line 55
    sub-int/2addr v0, v1

    .line 56
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 57
    .line 58
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    :goto_2
    sub-int/2addr v0, v1

    .line 63
    move v5, v0

    .line 64
    goto :goto_3

    .line 65
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :goto_3
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 73
    .line 74
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 75
    .line 76
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 77
    .line 78
    .line 79
    move-result v0

    .line 80
    add-int v6, v4, v0

    .line 81
    .line 82
    move-object v1, p0

    .line 83
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 84
    .line 85
    .line 86
    return-void
.end method

.method private final p()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 8
    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 16
    .line 17
    invoke-virtual {v3}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 22
    .line 23
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final r(I)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->e(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 7
    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    const/high16 v1, 0x40000000    # 2.0f

    .line 13
    .line 14
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private final t(I)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    instance-of v0, v0, Lc31/a$b;

    .line 11
    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    sub-int/2addr p1, v0

    .line 25
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 26
    .line 27
    sub-int/2addr p1, v0

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    const/high16 v1, 0x40000000    # 2.0f

    .line 36
    .line 37
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    const/4 v1, 0x0

    .line 42
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 47
    .line 48
    .line 49
    return-void
.end method


# virtual methods
.method public getCardHeight()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->v:I

    .line 2
    .line 3
    return v0
.end method

.method public final j(I)I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 2
    .line 3
    mul-int/lit8 v0, v0, 0x2

    .line 4
    .line 5
    sub-int/2addr p1, v0

    .line 6
    return p1
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    move-object p1, p0

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->k()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->n()V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->o()V

    .line 12
    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->m()V

    .line 15
    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->l()V

    .line 18
    .line 19
    .line 20
    iget p2, p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->p:I

    .line 21
    .line 22
    iget-object p3, p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 23
    .line 24
    invoke-virtual {p3}, Landroid/widget/ProgressBar;->getMax()I

    .line 25
    .line 26
    .line 27
    move-result p3

    .line 28
    invoke-virtual {p0, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u(II)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 9
    .line 10
    .line 11
    move-result-object p2

    .line 12
    instance-of p2, p2, Lc31/a$c;

    .line 13
    .line 14
    if-eqz p2, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getShowShimmer()Z

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    if-nez p2, :cond_0

    .line 21
    .line 22
    iget p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->n:I

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    iget p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->m:I

    .line 26
    .line 27
    :goto_0
    const/high16 v0, 0x40000000    # 2.0f

    .line 28
    .line 29
    invoke-static {p1, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 34
    .line 35
    .line 36
    move-result p2

    .line 37
    invoke-virtual {p0, v1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 38
    .line 39
    .line 40
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->p()V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s(I)V

    .line 44
    .line 45
    .line 46
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t(I)V

    .line 47
    .line 48
    .line 49
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r(I)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q(I)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final q(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->i:I

    .line 4
    .line 5
    mul-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    sub-int/2addr p1, v1

    .line 8
    const/high16 v1, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 15
    .line 16
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    iget v2, v2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 21
    .line 22
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final s(I)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->g(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;Ljava/lang/CharSequence;ILjava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getUiState()Lc31/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    instance-of v0, v0, Lc31/a$b;

    .line 11
    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    sub-int/2addr p1, v0

    .line 25
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j:I

    .line 26
    .line 27
    sub-int/2addr p1, v0

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->j(I)I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    :goto_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    const/high16 v1, 0x40000000    # 2.0f

    .line 36
    .line 37
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    const/4 v1, 0x0

    .line 42
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public setCaptionText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->d(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMaxProgress(I)V
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->p:I

    .line 2
    .line 3
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u(II)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setProgress(I)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->p:I

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/widget/ProgressBar;->getMax()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u(II)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public setStageNumberText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->f(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setState(Lc31/a;)V
    .locals 5
    .param p1    # Lc31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setState(Lc31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lc31/a$c;

    .line 5
    .line 6
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    const/16 v3, 0x8

    .line 10
    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    const/4 v4, 0x0

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/16 v4, 0x8

    .line 16
    .line 17
    :goto_0
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->s:Landroidx/appcompat/widget/AppCompatTextView;

    .line 21
    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    const/4 v4, 0x0

    .line 25
    goto :goto_1

    .line 26
    :cond_1
    const/16 v4, 0x8

    .line 27
    .line 28
    :goto_1
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    instance-of v1, p1, Lc31/a$a;

    .line 32
    .line 33
    if-eqz v1, :cond_2

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 36
    .line 37
    sget v1, LlZ0/n;->TextStyle_Title_Medium_S_TextPrimary:I

    .line 38
    .line 39
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 43
    .line 44
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 45
    .line 46
    .line 47
    goto :goto_2

    .line 48
    :cond_2
    if-eqz v0, :cond_3

    .line 49
    .line 50
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    .line 52
    sget v1, LlZ0/n;->TextStyle_Title_Bold_L_TextPrimary:I

    .line 53
    .line 54
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 55
    .line 56
    .line 57
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 58
    .line 59
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 60
    .line 61
    .line 62
    move-object v0, p1

    .line 63
    check-cast v0, Lc31/a$c;

    .line 64
    .line 65
    invoke-virtual {v0}, Lc31/a$c;->c()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->setCaptionText(Ljava/lang/CharSequence;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v0}, Lc31/a$c;->f()I

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->setProgress(I)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v0}, Lc31/a$c;->e()I

    .line 80
    .line 81
    .line 82
    move-result v0

    .line 83
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->setMaxProgress(I)V

    .line 84
    .line 85
    .line 86
    goto :goto_2

    .line 87
    :cond_3
    instance-of v0, p1, Lc31/a$b;

    .line 88
    .line 89
    if-eqz v0, :cond_4

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->q:Landroidx/appcompat/widget/AppCompatTextView;

    .line 92
    .line 93
    sget v1, LlZ0/n;->TextStyle_Title_Medium_S_TextPrimary:I

    .line 94
    .line 95
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 96
    .line 97
    .line 98
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->u:Landroidx/appcompat/widget/AppCompatImageView;

    .line 99
    .line 100
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 101
    .line 102
    .line 103
    :goto_2
    invoke-interface {p1}, Lc31/a;->b()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->setStageNumberText(Ljava/lang/CharSequence;)V

    .line 108
    .line 109
    .line 110
    invoke-interface {p1}, Lc31/a;->a()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->setTitleText(Ljava/lang/CharSequence;)V

    .line 115
    .line 116
    .line 117
    return-void

    .line 118
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 119
    .line 120
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 121
    .line 122
    .line 123
    throw p1
.end method

.method public setTitleText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->h(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->r:Landroidx/appcompat/widget/AppCompatTextView;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final u(II)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0, p2}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 4
    .line 5
    .line 6
    iget p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->o:I

    .line 7
    .line 8
    int-to-float p2, p2

    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    int-to-float v0, v0

    .line 16
    div-float/2addr p2, v0

    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 18
    .line 19
    invoke-virtual {v0}, Landroid/widget/ProgressBar;->getMax()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    int-to-float v0, v0

    .line 24
    mul-float p2, p2, v0

    .line 25
    .line 26
    float-to-int p2, p2

    .line 27
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 28
    .line 29
    invoke-virtual {v0}, Landroid/widget/ProgressBar;->getMax()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    sub-int/2addr v0, p2

    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;->t:Landroid/widget/ProgressBar;

    .line 35
    .line 36
    const/4 v2, 0x1

    .line 37
    if-gt v2, p1, :cond_0

    .line 38
    .line 39
    if-gt p1, p2, :cond_0

    .line 40
    .line 41
    move p1, p2

    .line 42
    goto :goto_0

    .line 43
    :cond_0
    invoke-virtual {v1}, Landroid/widget/ProgressBar;->getMax()I

    .line 44
    .line 45
    .line 46
    move-result p2

    .line 47
    if-ge p1, p2, :cond_1

    .line 48
    .line 49
    if-le p1, v0, :cond_1

    .line 50
    .line 51
    move p1, v0

    .line 52
    :cond_1
    :goto_0
    invoke-virtual {v1, p1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 53
    .line 54
    .line 55
    return-void
.end method
