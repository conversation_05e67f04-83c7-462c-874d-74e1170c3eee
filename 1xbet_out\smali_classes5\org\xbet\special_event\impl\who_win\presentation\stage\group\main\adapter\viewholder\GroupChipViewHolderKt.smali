.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a#\u0010\u000c\u001a\u00020\u0002*\u0012\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\n0\tj\u0002`\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r*$\u0008\u0000\u0010\u000e\"\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\n0\t2\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\n0\t\u00a8\u0006\u000f"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LOy0/a;",
        "",
        "onClick",
        "LA4/c;",
        "",
        "LVX0/i;",
        "f",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "LB4/a;",
        "LGq0/I;",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolder;",
        "e",
        "(LB4/a;)V",
        "GroupChipViewHolder",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt;->i(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/I;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/I;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic d(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt;->e(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LOy0/a;",
            "LGq0/I;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/I;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/I;->b:Lorg/xbet/uikit/components/chips/Chip;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LOy0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, LOy0/a;->d()Z

    .line 16
    .line 17
    .line 18
    move-result p0

    .line 19
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/chips/Chip;->setSelected(Z)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final f(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LOy0/a;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LPy0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LPy0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LPy0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LPy0/b;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$groupChipAdapterAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$groupChipAdapterAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$groupChipAdapterAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$groupChipAdapterAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/I;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/I;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/I;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/I;

    .line 6
    .line 7
    invoke-virtual {v0}, LGq0/I;->b()Lorg/xbet/uikit/components/chips/Chip;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LPy0/c;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LPy0/c;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$a;

    .line 22
    .line 23
    invoke-direct {p0, p1, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/adapter/viewholder/GroupChipViewHolderKt$a;-><init>(LB4/a;LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method
