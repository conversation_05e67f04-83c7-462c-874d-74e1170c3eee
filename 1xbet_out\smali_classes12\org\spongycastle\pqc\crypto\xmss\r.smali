.class public final Lorg/spongycastle/pqc/crypto/xmss/r;
.super Lsf/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/spongycastle/pqc/crypto/xmss/r$b;
    }
.end annotation


# instance fields
.field public final b:Lorg/spongycastle/pqc/crypto/xmss/q;

.field public final c:[B

.field public final d:[B

.field public final e:[B

.field public final f:[B

.field public final g:Lorg/spongycastle/pqc/crypto/xmss/BDS;


# direct methods
.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/r$b;)V
    .locals 7

    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, v0}, Lsf/a;-><init>(Z)V

    .line 3
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->a(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/q;

    move-result-object v2

    iput-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    if-eqz v2, :cond_e

    .line 4
    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/q;->c()I

    move-result v1

    .line 5
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->b(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B

    move-result-object v3

    if-eqz v3, :cond_3

    .line 6
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->c(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/q;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 7
    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    move-result v0

    const/4 v2, 0x0

    .line 8
    invoke-static {v3, v2}, Lorg/spongycastle/util/f;->a([BI)I

    move-result v2

    int-to-long v4, v2

    .line 9
    invoke-static {v0, v4, v5}, Lorg/spongycastle/pqc/crypto/xmss/t;->l(IJ)Z

    move-result v0

    if-eqz v0, :cond_1

    const/4 v0, 0x4

    .line 10
    invoke-static {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    move-result-object v4

    iput-object v4, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->c:[B

    add-int/2addr v0, v1

    .line 11
    invoke-static {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    move-result-object v4

    iput-object v4, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->d:[B

    add-int/2addr v0, v1

    .line 12
    invoke-static {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    move-result-object v4

    iput-object v4, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->e:[B

    add-int/2addr v0, v1

    .line 13
    invoke-static {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    move-result-object v4

    iput-object v4, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->f:[B

    add-int/2addr v0, v1

    .line 14
    array-length v1, v3

    sub-int/2addr v1, v0

    invoke-static {v3, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->g([BII)[B

    move-result-object v0

    .line 15
    :try_start_0
    invoke-static {v0}, Lorg/spongycastle/pqc/crypto/xmss/t;->f([B)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/spongycastle/pqc/crypto/xmss/BDS;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    :catch_0
    move-exception v0

    goto :goto_0

    :catch_1
    move-exception v0

    goto :goto_1

    .line 16
    :goto_0
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    goto :goto_2

    .line 17
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_2
    const/4 v0, 0x0

    .line 18
    :goto_3
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->c(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/q;

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->setXMSS(Lorg/spongycastle/pqc/crypto/xmss/q;)V

    .line 19
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->validate()V

    .line 20
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->getIndex()I

    move-result p1

    if-ne p1, v2, :cond_0

    .line 21
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    return-void

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "serialized BDS has wrong index"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 23
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "index out of bounds"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 24
    :cond_2
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "xmss == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 25
    :cond_3
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->d(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B

    move-result-object v4

    if-eqz v4, :cond_5

    .line 26
    array-length v3, v4

    if-ne v3, v1, :cond_4

    .line 27
    iput-object v4, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->c:[B

    goto :goto_4

    .line 28
    :cond_4
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "size of secretKeySeed needs to be equal size of digest"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 29
    :cond_5
    new-array v3, v1, [B

    iput-object v3, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->c:[B

    .line 30
    :goto_4
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->e(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B

    move-result-object v3

    if-eqz v3, :cond_7

    .line 31
    array-length v5, v3

    if-ne v5, v1, :cond_6

    .line 32
    iput-object v3, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->d:[B

    goto :goto_5

    .line 33
    :cond_6
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "size of secretKeyPRF needs to be equal size of digest"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 34
    :cond_7
    new-array v3, v1, [B

    iput-object v3, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->d:[B

    .line 35
    :goto_5
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->f(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B

    move-result-object v3

    if-eqz v3, :cond_9

    .line 36
    array-length v5, v3

    if-ne v5, v1, :cond_8

    .line 37
    iput-object v3, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->e:[B

    goto :goto_6

    .line 38
    :cond_8
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "size of publicSeed needs to be equal size of digest"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 39
    :cond_9
    new-array v5, v1, [B

    iput-object v5, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->e:[B

    .line 40
    :goto_6
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->g(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B

    move-result-object v5

    if-eqz v5, :cond_b

    .line 41
    array-length v6, v5

    if-ne v6, v1, :cond_a

    .line 42
    iput-object v5, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->f:[B

    goto :goto_7

    .line 43
    :cond_a
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "size of root needs to be equal size of digest"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 44
    :cond_b
    new-array v1, v1, [B

    iput-object v1, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->f:[B

    .line 45
    :goto_7
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->h(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    move-result-object v1

    if-eqz v1, :cond_c

    .line 46
    iput-object v1, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    return-void

    .line 47
    :cond_c
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->i(Lorg/spongycastle/pqc/crypto/xmss/r$b;)I

    move-result v1

    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    move-result v5

    shl-int/2addr v0, v5

    add-int/lit8 v0, v0, -0x2

    if-ge v1, v0, :cond_d

    if-eqz v3, :cond_d

    if-eqz v4, :cond_d

    .line 48
    new-instance v1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    invoke-direct {v0}, Lorg/spongycastle/pqc/crypto/xmss/f$b;-><init>()V

    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->l()Lorg/spongycastle/pqc/crypto/xmss/k;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lorg/spongycastle/pqc/crypto/xmss/f;

    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->i(Lorg/spongycastle/pqc/crypto/xmss/r$b;)I

    move-result v6

    invoke-direct/range {v1 .. v6}, Lorg/spongycastle/pqc/crypto/xmss/BDS;-><init>(Lorg/spongycastle/pqc/crypto/xmss/q;[B[BLorg/spongycastle/pqc/crypto/xmss/f;I)V

    iput-object v1, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    return-void

    .line 49
    :cond_d
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/r$b;->i(Lorg/spongycastle/pqc/crypto/xmss/r$b;)I

    move-result p1

    invoke-direct {v0, v2, p1}, Lorg/spongycastle/pqc/crypto/xmss/BDS;-><init>(Lorg/spongycastle/pqc/crypto/xmss/q;I)V

    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    return-void

    .line 50
    :cond_e
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "params == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public synthetic constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/r$b;Lorg/spongycastle/pqc/crypto/xmss/r$a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/spongycastle/pqc/crypto/xmss/r;-><init>(Lorg/spongycastle/pqc/crypto/xmss/r$b;)V

    return-void
.end method


# virtual methods
.method public b()Lorg/spongycastle/pqc/crypto/xmss/q;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    return-object v0
.end method

.method public c()[B
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->b:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->c()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v1, v0, 0x4

    .line 8
    .line 9
    add-int/2addr v1, v0

    .line 10
    add-int/2addr v1, v0

    .line 11
    add-int/2addr v1, v0

    .line 12
    new-array v1, v1, [B

    .line 13
    .line 14
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 15
    .line 16
    invoke-virtual {v2}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->getIndex()I

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    const/4 v3, 0x0

    .line 21
    invoke-static {v2, v1, v3}, Lorg/spongycastle/util/f;->c(I[BI)V

    .line 22
    .line 23
    .line 24
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->c:[B

    .line 25
    .line 26
    const/4 v3, 0x4

    .line 27
    invoke-static {v1, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/t;->e([B[BI)V

    .line 28
    .line 29
    .line 30
    add-int/2addr v3, v0

    .line 31
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->d:[B

    .line 32
    .line 33
    invoke-static {v1, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/t;->e([B[BI)V

    .line 34
    .line 35
    .line 36
    add-int/2addr v3, v0

    .line 37
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->e:[B

    .line 38
    .line 39
    invoke-static {v1, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/t;->e([B[BI)V

    .line 40
    .line 41
    .line 42
    add-int/2addr v3, v0

    .line 43
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->f:[B

    .line 44
    .line 45
    invoke-static {v1, v0, v3}, Lorg/spongycastle/pqc/crypto/xmss/t;->e([B[BI)V

    .line 46
    .line 47
    .line 48
    :try_start_0
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 49
    .line 50
    invoke-static {v0}, Lorg/spongycastle/pqc/crypto/xmss/t;->o(Ljava/lang/Object;)[B

    .line 51
    .line 52
    .line 53
    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 54
    invoke-static {v1, v0}, Lorg/spongycastle/util/a;->j([B[B)[B

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    return-object v0

    .line 59
    :catch_0
    move-exception v0

    .line 60
    new-instance v1, Ljava/lang/RuntimeException;

    .line 61
    .line 62
    new-instance v2, Ljava/lang/StringBuilder;

    .line 63
    .line 64
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 65
    .line 66
    .line 67
    const-string v3, "error serializing bds state: "

    .line 68
    .line 69
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 70
    .line 71
    .line 72
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-direct {v1, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw v1
.end method
