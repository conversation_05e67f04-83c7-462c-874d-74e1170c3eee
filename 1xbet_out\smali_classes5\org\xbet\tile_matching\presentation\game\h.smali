.class public final synthetic Lorg/xbet/tile_matching/presentation/game/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/h;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/h;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->z2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)Lkot<PERSON>/Unit;

    move-result-object v0

    return-object v0
.end method
