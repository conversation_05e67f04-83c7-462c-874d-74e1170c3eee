.class public final LiD0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0013\u0010\u0005\u001a\u00020\u0004*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LND0/e;",
        "LjD0/e;",
        "a",
        "(LND0/e;)LjD0/e;",
        "LNN0/d;",
        "b",
        "(LND0/e;)LNN0/d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LND0/e;)LjD0/e;
    .locals 3
    .param p0    # LND0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LjD0/e;

    .line 2
    .line 3
    invoke-virtual {p0}, LND0/e;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, LjD0/g;->d:LjD0/g$a;

    .line 8
    .line 9
    invoke-virtual {p0}, LND0/e;->b()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-virtual {v2, p0}, LjD0/g$a;->a(I)LjD0/g;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-direct {v0, v1, p0}, LjD0/e;-><init>(Ljava/lang/String;LjD0/g;)V

    .line 18
    .line 19
    .line 20
    return-object v0
.end method

.method public static final b(LND0/e;)LNN0/d;
    .locals 2
    .param p0    # LND0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LND0/e;->a()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, LjD0/g;->d:LjD0/g$a;

    .line 6
    .line 7
    invoke-virtual {p0}, LND0/e;->b()I

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    invoke-virtual {v1, p0}, LjD0/g$a;->a(I)LjD0/g;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    new-instance v1, LNN0/d;

    .line 16
    .line 17
    invoke-direct {v1, p0, v0}, LNN0/d;-><init>(LNN0/h;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    return-object v1
.end method
