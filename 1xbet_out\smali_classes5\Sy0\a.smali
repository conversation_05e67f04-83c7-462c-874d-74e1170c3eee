.class public final LSy0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "LTy0/a;",
        "LHX0/e;",
        "resourceManager",
        "LTy0/b;",
        "a",
        "(LTy0/a;LHX0/e;)LTy0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LTy0/a;LHX0/e;)LTy0/b;
    .locals 11
    .param p0    # LTy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, LTy0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Ljava/util/ArrayList;

    .line 10
    .line 11
    const/16 v3, 0xa

    .line 12
    .line 13
    invoke-static {v1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 18
    .line 19
    .line 20
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    const/4 v3, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v5

    .line 30
    if-eqz v5, :cond_4

    .line 31
    .line 32
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    add-int/lit8 v6, v4, 0x1

    .line 37
    .line 38
    if-gez v4, :cond_0

    .line 39
    .line 40
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 41
    .line 42
    .line 43
    :cond_0
    check-cast v5, LDy0/d;

    .line 44
    .line 45
    invoke-virtual {p0}, LTy0/a;->c()Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v7

    .line 49
    invoke-interface {v7}, Ljava/util/List;->size()I

    .line 50
    .line 51
    .line 52
    move-result v7

    .line 53
    const/4 v8, 0x1

    .line 54
    sub-int/2addr v7, v8

    .line 55
    if-ne v4, v7, :cond_1

    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_1
    const/4 v8, 0x0

    .line 59
    :goto_1
    invoke-virtual {p0}, LTy0/a;->b()Ljava/util/List;

    .line 60
    .line 61
    .line 62
    move-result-object v4

    .line 63
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 64
    .line 65
    .line 66
    move-result-object v4

    .line 67
    :cond_2
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 68
    .line 69
    .line 70
    move-result v7

    .line 71
    if-eqz v7, :cond_3

    .line 72
    .line 73
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v7

    .line 77
    move-object v9, v7

    .line 78
    check-cast v9, LDy0/c;

    .line 79
    .line 80
    invoke-virtual {v9}, LDy0/c;->a()I

    .line 81
    .line 82
    .line 83
    move-result v9

    .line 84
    invoke-virtual {v5}, LDy0/d;->c()LDy0/b;

    .line 85
    .line 86
    .line 87
    move-result-object v10

    .line 88
    invoke-virtual {v10}, LDy0/b;->f()I

    .line 89
    .line 90
    .line 91
    move-result v10

    .line 92
    if-ne v9, v10, :cond_2

    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_3
    const/4 v7, 0x0

    .line 96
    :goto_2
    check-cast v7, LDy0/c;

    .line 97
    .line 98
    invoke-static {v5, v8, p1, v7}, LKy0/b;->a(LDy0/d;ZLHX0/e;LDy0/c;)LIy0/f;

    .line 99
    .line 100
    .line 101
    move-result-object v4

    .line 102
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 103
    .line 104
    .line 105
    move v4, v6

    .line 106
    goto :goto_0

    .line 107
    :cond_4
    new-instance p0, LIy0/g$a$a;

    .line 108
    .line 109
    invoke-direct {p0, v2}, LIy0/g$a$a;-><init>(Ljava/util/List;)V

    .line 110
    .line 111
    .line 112
    new-instance p1, LIy0/g;

    .line 113
    .line 114
    invoke-direct {p1, p0}, LIy0/g;-><init>(LIy0/g$a$a;)V

    .line 115
    .line 116
    .line 117
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 118
    .line 119
    .line 120
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 121
    .line 122
    .line 123
    move-result-object p0

    .line 124
    new-instance p1, LTy0/b;

    .line 125
    .line 126
    invoke-direct {p1, p0}, LTy0/b;-><init>(Ljava/util/List;)V

    .line 127
    .line 128
    .line 129
    return-object p1
.end method
