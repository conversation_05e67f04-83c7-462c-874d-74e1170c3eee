.class public final LOK0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LPK0/b;",
        "LRK0/b;",
        "a",
        "(LPK0/b;)LRK0/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LPK0/b;)LRK0/b;
    .locals 17
    .param p0    # LPK0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LPK0/b;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, ""

    .line 6
    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    move-object v3, v1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object v3, v0

    .line 12
    :goto_0
    invoke-virtual/range {p0 .. p0}, LPK0/b;->h()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-nez v0, :cond_1

    .line 17
    .line 18
    move-object v4, v1

    .line 19
    goto :goto_1

    .line 20
    :cond_1
    move-object v4, v0

    .line 21
    :goto_1
    invoke-virtual/range {p0 .. p0}, LPK0/b;->j()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-nez v0, :cond_2

    .line 26
    .line 27
    move-object v5, v1

    .line 28
    goto :goto_2

    .line 29
    :cond_2
    move-object v5, v0

    .line 30
    :goto_2
    invoke-virtual/range {p0 .. p0}, LPK0/b;->i()Ljava/lang/Long;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    const-wide/16 v6, 0x0

    .line 35
    .line 36
    if-eqz v0, :cond_3

    .line 37
    .line 38
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 39
    .line 40
    .line 41
    move-result-wide v8

    .line 42
    goto :goto_3

    .line 43
    :cond_3
    move-wide v8, v6

    .line 44
    :goto_3
    invoke-virtual/range {p0 .. p0}, LPK0/b;->g()Ljava/lang/Integer;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    if-eqz v0, :cond_4

    .line 49
    .line 50
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    sget-object v2, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 55
    .line 56
    invoke-virtual {v2, v0}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    if-nez v0, :cond_5

    .line 61
    .line 62
    :cond_4
    sget-object v0, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->UNKNOWN:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 63
    .line 64
    :cond_5
    invoke-virtual/range {p0 .. p0}, LPK0/b;->b()Ljava/lang/Long;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    if-eqz v2, :cond_6

    .line 69
    .line 70
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 71
    .line 72
    .line 73
    move-result-wide v10

    .line 74
    goto :goto_4

    .line 75
    :cond_6
    move-wide v10, v6

    .line 76
    :goto_4
    invoke-virtual/range {p0 .. p0}, LPK0/b;->a()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v2

    .line 80
    if-nez v2, :cond_7

    .line 81
    .line 82
    goto :goto_5

    .line 83
    :cond_7
    move-object v1, v2

    .line 84
    :goto_5
    invoke-virtual/range {p0 .. p0}, LPK0/b;->d()Ljava/lang/Long;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    if-eqz v2, :cond_8

    .line 89
    .line 90
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 91
    .line 92
    .line 93
    move-result-wide v12

    .line 94
    goto :goto_6

    .line 95
    :cond_8
    move-wide v12, v6

    .line 96
    :goto_6
    invoke-virtual/range {p0 .. p0}, LPK0/b;->f()Ljava/lang/Long;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    if-eqz v2, :cond_9

    .line 101
    .line 102
    invoke-virtual {v2}, Ljava/lang/Long;->longValue()J

    .line 103
    .line 104
    .line 105
    move-result-wide v6

    .line 106
    :cond_9
    move-wide v14, v6

    .line 107
    invoke-virtual/range {p0 .. p0}, LPK0/b;->e()Ljava/util/List;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    if-eqz v2, :cond_a

    .line 112
    .line 113
    new-instance v6, Ljava/util/ArrayList;

    .line 114
    .line 115
    const/16 v7, 0xa

    .line 116
    .line 117
    invoke-static {v2, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 118
    .line 119
    .line 120
    move-result v7

    .line 121
    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 122
    .line 123
    .line 124
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 125
    .line 126
    .line 127
    move-result-object v2

    .line 128
    :goto_7
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 129
    .line 130
    .line 131
    move-result v7

    .line 132
    if-eqz v7, :cond_b

    .line 133
    .line 134
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v7

    .line 138
    check-cast v7, LPK0/a;

    .line 139
    .line 140
    invoke-static {v7}, LOK0/a;->a(LPK0/a;)LRK0/a;

    .line 141
    .line 142
    .line 143
    move-result-object v7

    .line 144
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 145
    .line 146
    .line 147
    goto :goto_7

    .line 148
    :cond_a
    const/4 v6, 0x0

    .line 149
    :cond_b
    if-nez v6, :cond_c

    .line 150
    .line 151
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 152
    .line 153
    .line 154
    move-result-object v6

    .line 155
    :cond_c
    move-object/from16 v16, v6

    .line 156
    .line 157
    new-instance v2, LRK0/b;

    .line 158
    .line 159
    move-wide v6, v8

    .line 160
    move-wide v9, v10

    .line 161
    move-object v8, v0

    .line 162
    move-object v11, v1

    .line 163
    invoke-direct/range {v2 .. v16}, LRK0/b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JLorg/xbet/statistic/domain/model/shortgame/EventStatusType;JLjava/lang/String;JJLjava/util/List;)V

    .line 164
    .line 165
    .line 166
    return-object v2
.end method
