.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.CouponMakeBetViewModel$loadCoefState$1"
    f = "CouponMakeBetViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->k4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "newCoef",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 24

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_8

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    move-object v7, v1

    .line 16
    check-cast v7, Ljava/lang/String;

    .line 17
    .line 18
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 19
    .line 20
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->q3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Z

    .line 21
    .line 22
    .line 23
    move-result v12

    .line 24
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 25
    .line 26
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->s3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    check-cast v1, LAx/c;

    .line 35
    .line 36
    invoke-virtual {v1}, LAx/c;->g()D

    .line 37
    .line 38
    .line 39
    move-result-wide v1

    .line 40
    iget-object v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 41
    .line 42
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->s3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    invoke-interface {v3}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    check-cast v3, LAx/c;

    .line 51
    .line 52
    invoke-virtual {v3}, LAx/c;->f()Lorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    iget-object v4, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 57
    .line 58
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->B3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    invoke-virtual {v4}, Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;->a()D

    .line 63
    .line 64
    .line 65
    move-result-wide v5

    .line 66
    iget-object v4, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 67
    .line 68
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->A3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;

    .line 69
    .line 70
    .line 71
    move-result-object v4

    .line 72
    invoke-virtual {v4}, Lorg/xbet/coupon/impl/coupon/domain/usecases/l0;->a()Lorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;

    .line 73
    .line 74
    .line 75
    move-result-object v13

    .line 76
    const/16 v17, 0x1

    .line 77
    .line 78
    cmpg-double v4, v1, v5

    .line 79
    .line 80
    if-nez v4, :cond_4

    .line 81
    .line 82
    if-eq v3, v13, :cond_0

    .line 83
    .line 84
    goto/16 :goto_6

    .line 85
    .line 86
    :cond_0
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 87
    .line 88
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->s3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    iget-object v2, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 93
    .line 94
    :goto_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object v3

    .line 98
    move-object v4, v2

    .line 99
    move-object v2, v3

    .line 100
    check-cast v2, LAx/c;

    .line 101
    .line 102
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;

    .line 103
    .line 104
    .line 105
    move-result-object v5

    .line 106
    invoke-virtual {v5}, Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;->a()Z

    .line 107
    .line 108
    .line 109
    move-result v5

    .line 110
    if-eqz v5, :cond_1

    .line 111
    .line 112
    sget-object v5, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;->BLOCKED:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;

    .line 113
    .line 114
    :goto_1
    move-object v9, v5

    .line 115
    goto :goto_2

    .line 116
    :cond_1
    sget-object v5, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;->NONE:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;

    .line 117
    .line 118
    goto :goto_1

    .line 119
    :goto_2
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->H3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;

    .line 120
    .line 121
    .line 122
    move-result-object v5

    .line 123
    invoke-virtual {v5}, Lorg/xbet/coupon/impl/coupon/domain/usecases/d2;->a()Z

    .line 124
    .line 125
    .line 126
    move-result v5

    .line 127
    if-eqz v5, :cond_2

    .line 128
    .line 129
    sget-object v5, Lorg/xbet/ui_common/CoefficientState;->BLOCKED:Lorg/xbet/ui_common/CoefficientState;

    .line 130
    .line 131
    :goto_3
    move-object v10, v5

    .line 132
    goto :goto_4

    .line 133
    :cond_2
    sget-object v5, Lorg/xbet/ui_common/CoefficientState;->SAME:Lorg/xbet/ui_common/CoefficientState;

    .line 134
    .line 135
    goto :goto_3

    .line 136
    :goto_4
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->u3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 137
    .line 138
    .line 139
    move-result-object v5

    .line 140
    invoke-interface {v5}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v5

    .line 144
    check-cast v5, Ljava/lang/Boolean;

    .line 145
    .line 146
    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    .line 147
    .line 148
    .line 149
    move-result v11

    .line 150
    const/16 v15, 0xb

    .line 151
    .line 152
    const/16 v16, 0x0

    .line 153
    .line 154
    move-object v6, v3

    .line 155
    move-object v5, v4

    .line 156
    const-wide/16 v3, 0x0

    .line 157
    .line 158
    move-object v8, v5

    .line 159
    move-object v14, v6

    .line 160
    const-wide/16 v5, 0x0

    .line 161
    .line 162
    move-object/from16 v18, v8

    .line 163
    .line 164
    const/4 v8, 0x0

    .line 165
    move-object/from16 v19, v14

    .line 166
    .line 167
    const/4 v14, 0x0

    .line 168
    move-object/from16 v0, v19

    .line 169
    .line 170
    invoke-static/range {v2 .. v16}, LAx/c;->b(LAx/c;JDLjava/lang/String;Ljava/lang/String;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;Lorg/xbet/ui_common/CoefficientState;ZZLorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;ZILjava/lang/Object;)LAx/c;

    .line 171
    .line 172
    .line 173
    move-result-object v2

    .line 174
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 175
    .line 176
    .line 177
    move-result v0

    .line 178
    if-eqz v0, :cond_3

    .line 179
    .line 180
    :goto_5
    move-object/from16 v0, p0

    .line 181
    .line 182
    goto/16 :goto_a

    .line 183
    .line 184
    :cond_3
    move-object/from16 v0, p0

    .line 185
    .line 186
    move-object/from16 v2, v18

    .line 187
    .line 188
    goto :goto_0

    .line 189
    :cond_4
    move-object/from16 v0, p0

    .line 190
    .line 191
    :goto_6
    iget-object v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 192
    .line 193
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->z3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;

    .line 194
    .line 195
    .line 196
    move-result-object v3

    .line 197
    invoke-virtual {v3, v1, v2}, Lorg/xbet/coupon/impl/coupon/domain/usecases/j0;->a(D)Lorg/xbet/ui_common/CoefficientState;

    .line 198
    .line 199
    .line 200
    move-result-object v10

    .line 201
    sget-object v3, Lorg/xbet/ui_common/CoefficientState;->BLOCKED:Lorg/xbet/ui_common/CoefficientState;

    .line 202
    .line 203
    if-ne v10, v3, :cond_5

    .line 204
    .line 205
    const/16 v18, 0x1

    .line 206
    .line 207
    goto :goto_7

    .line 208
    :cond_5
    const/4 v3, 0x0

    .line 209
    const/16 v18, 0x0

    .line 210
    .line 211
    :goto_7
    iget-object v3, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 212
    .line 213
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->s3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 214
    .line 215
    .line 216
    move-result-object v3

    .line 217
    iget-object v4, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 218
    .line 219
    :goto_8
    invoke-interface {v3}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object v8

    .line 223
    move-object v9, v8

    .line 224
    check-cast v9, LAx/c;

    .line 225
    .line 226
    if-nez v18, :cond_6

    .line 227
    .line 228
    invoke-virtual {v9}, LAx/c;->h()Ljava/lang/String;

    .line 229
    .line 230
    .line 231
    move-result-object v11

    .line 232
    goto :goto_9

    .line 233
    :cond_6
    const-string v11, ""

    .line 234
    .line 235
    :goto_9
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->y3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;

    .line 236
    .line 237
    .line 238
    move-result-object v14

    .line 239
    invoke-virtual {v14, v1, v2}, Lorg/xbet/coupon/impl/coupon/domain/usecases/d0;->a(D)Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;

    .line 240
    .line 241
    .line 242
    move-result-object v14

    .line 243
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->u3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 244
    .line 245
    .line 246
    move-result-object v15

    .line 247
    invoke-interface {v15}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 248
    .line 249
    .line 250
    move-result-object v15

    .line 251
    check-cast v15, Ljava/lang/Boolean;

    .line 252
    .line 253
    invoke-virtual {v15}, Ljava/lang/Boolean;->booleanValue()Z

    .line 254
    .line 255
    .line 256
    move-result v15

    .line 257
    move-object/from16 v16, v8

    .line 258
    .line 259
    move-object v8, v11

    .line 260
    move v11, v15

    .line 261
    const/4 v15, 0x1

    .line 262
    move-object/from16 v19, v16

    .line 263
    .line 264
    const/16 v16, 0x0

    .line 265
    .line 266
    move-object/from16 v20, v3

    .line 267
    .line 268
    move-object/from16 v21, v4

    .line 269
    .line 270
    const-wide/16 v3, 0x0

    .line 271
    .line 272
    move-wide/from16 v22, v1

    .line 273
    .line 274
    move-object v2, v9

    .line 275
    move-object v9, v14

    .line 276
    const/4 v14, 0x0

    .line 277
    move-object/from16 v0, v19

    .line 278
    .line 279
    move-object/from16 v1, v20

    .line 280
    .line 281
    invoke-static/range {v2 .. v16}, LAx/c;->b(LAx/c;JDLjava/lang/String;Ljava/lang/String;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefChangeTypeModel;Lorg/xbet/ui_common/CoefficientState;ZZLorg/xbet/coupon/impl/coupon/domain/models/CoefViewTypeModel;ZILjava/lang/Object;)LAx/c;

    .line 282
    .line 283
    .line 284
    move-result-object v2

    .line 285
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 286
    .line 287
    .line 288
    move-result v0

    .line 289
    if-eqz v0, :cond_7

    .line 290
    .line 291
    goto :goto_5

    .line 292
    :goto_a
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 293
    .line 294
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->v3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/x0;

    .line 295
    .line 296
    .line 297
    move-result-object v1

    .line 298
    invoke-static {v1}, Lorg/xbet/uikit/utils/CoroutineExtensionKt;->a(Lkotlinx/coroutines/x0;)V

    .line 299
    .line 300
    .line 301
    iget-object v1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$loadCoefState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 302
    .line 303
    invoke-static {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->u3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 304
    .line 305
    .line 306
    move-result-object v1

    .line 307
    invoke-static/range {v17 .. v17}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 308
    .line 309
    .line 310
    move-result-object v2

    .line 311
    invoke-interface {v1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 312
    .line 313
    .line 314
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 315
    .line 316
    return-object v1

    .line 317
    :cond_7
    move-object/from16 v0, p0

    .line 318
    .line 319
    move-object v3, v1

    .line 320
    move-object/from16 v4, v21

    .line 321
    .line 322
    move-wide/from16 v1, v22

    .line 323
    .line 324
    goto :goto_8

    .line 325
    :cond_8
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 326
    .line 327
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 328
    .line 329
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 330
    .line 331
    .line 332
    throw v1
.end method
