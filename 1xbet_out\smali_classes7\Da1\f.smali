.class public final synthetic LDa1/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:Lkotlin/jvm/functions/Function2;

.field public final synthetic c:LUX0/k;

.field public final synthetic d:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LDa1/f;->a:Lkotlin/jvm/functions/Function0;

    iput-object p2, p0, LDa1/f;->b:Lkotlin/jvm/functions/Function2;

    iput-object p3, p0, LDa1/f;->c:LUX0/k;

    iput-object p4, p0, LDa1/f;->d:<PERSON><PERSON><PERSON>/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LDa1/f;->a:Lkotlin/jvm/functions/Function0;

    iget-object v1, p0, LDa1/f;->b:Lkotlin/jvm/functions/Function2;

    iget-object v2, p0, LDa1/f;->c:LUX0/k;

    iget-object v3, p0, LDa1/f;->d:Lkotlin/jvm/functions/Function0;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/tournaments/PromoTournamentCarouselViewHolderKt;->e(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;LUX0/k;Lkotlin/jvm/functions/Function0;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
