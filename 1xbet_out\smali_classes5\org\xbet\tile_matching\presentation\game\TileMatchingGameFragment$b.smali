.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0011\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "org/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b",
        "Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;",
        "",
        "onGlobalLayout",
        "()V",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onGlobalLayout()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, LxT0/a;->e:Lorg/xbet/tile_matching/presentation/views/TileMatchingCoeffView;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 17
    .line 18
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->F2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)V

    .line 19
    .line 20
    .line 21
    return-void
.end method
