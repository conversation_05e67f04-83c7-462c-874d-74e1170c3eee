.class public interface abstract LqN0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LqN0/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008a\u0018\u00002\u00020\u0001:\u0001\rJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\t\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LqN0/c;",
        "",
        "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;",
        "fragment",
        "",
        "b",
        "(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;)V",
        "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;",
        "bottomSheet",
        "c",
        "(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;)V",
        "Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;",
        "infoFragment",
        "a",
        "(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;)V
    .param p1    # Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridInfoFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract b(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;)V
    .param p1    # Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;)V
    .param p1    # Lorg/xbet/statistic/stat_results/impl/results_grid/presentation/fragment/ResultsGridNavigationBottomSheet;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
