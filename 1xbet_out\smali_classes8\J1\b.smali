.class public interface abstract LJ1/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LJ1/b$a;
    }
.end annotation


# virtual methods
.method public abstract a(LJ1/a;)V
.end method

.method public abstract b()V
.end method

.method public abstract c()LJ1/a;
.end method

.method public abstract d(LJ1/b$a;)V
.end method

.method public abstract e()I
.end method
