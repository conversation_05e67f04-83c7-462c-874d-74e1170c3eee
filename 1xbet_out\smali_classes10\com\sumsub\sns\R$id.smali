.class public final Lcom/sumsub/sns/R$id;
.super Ljava/lang/Object;


# static fields
.field public static ANIMATION_TAG:I = 0x7f0a0001

.field public static BOTTOM_END:I = 0x7f0a0003

.field public static BOTTOM_START:I = 0x7f0a0004

.field public static NO_DEBUG:I = 0x7f0a000e

.field public static SHOW_ALL:I = 0x7f0a0011

.field public static SHOW_PATH:I = 0x7f0a0012

.field public static SHOW_PROGRESS:I = 0x7f0a0013

.field public static TOP_END:I = 0x7f0a0015

.field public static TOP_START:I = 0x7f0a0016

.field public static above_bottom_sheet_container:I = 0x7f0a0019

.field public static accelerate:I = 0x7f0a001a

.field public static accessibility_action_clickable_span:I = 0x7f0a0025

.field public static accessibility_custom_action_0:I = 0x7f0a0026

.field public static accessibility_custom_action_1:I = 0x7f0a0027

.field public static accessibility_custom_action_10:I = 0x7f0a0028

.field public static accessibility_custom_action_11:I = 0x7f0a0029

.field public static accessibility_custom_action_12:I = 0x7f0a002a

.field public static accessibility_custom_action_13:I = 0x7f0a002b

.field public static accessibility_custom_action_14:I = 0x7f0a002c

.field public static accessibility_custom_action_15:I = 0x7f0a002d

.field public static accessibility_custom_action_16:I = 0x7f0a002e

.field public static accessibility_custom_action_17:I = 0x7f0a002f

.field public static accessibility_custom_action_18:I = 0x7f0a0030

.field public static accessibility_custom_action_19:I = 0x7f0a0031

.field public static accessibility_custom_action_2:I = 0x7f0a0032

.field public static accessibility_custom_action_20:I = 0x7f0a0033

.field public static accessibility_custom_action_21:I = 0x7f0a0034

.field public static accessibility_custom_action_22:I = 0x7f0a0035

.field public static accessibility_custom_action_23:I = 0x7f0a0036

.field public static accessibility_custom_action_24:I = 0x7f0a0037

.field public static accessibility_custom_action_25:I = 0x7f0a0038

.field public static accessibility_custom_action_26:I = 0x7f0a0039

.field public static accessibility_custom_action_27:I = 0x7f0a003a

.field public static accessibility_custom_action_28:I = 0x7f0a003b

.field public static accessibility_custom_action_29:I = 0x7f0a003c

.field public static accessibility_custom_action_3:I = 0x7f0a003d

.field public static accessibility_custom_action_30:I = 0x7f0a003e

.field public static accessibility_custom_action_31:I = 0x7f0a003f

.field public static accessibility_custom_action_4:I = 0x7f0a0040

.field public static accessibility_custom_action_5:I = 0x7f0a0041

.field public static accessibility_custom_action_6:I = 0x7f0a0042

.field public static accessibility_custom_action_7:I = 0x7f0a0043

.field public static accessibility_custom_action_8:I = 0x7f0a0044

.field public static accessibility_custom_action_9:I = 0x7f0a0045

.field public static actionDown:I = 0x7f0a0062

.field public static actionDownUp:I = 0x7f0a0063

.field public static actionUp:I = 0x7f0a006d

.field public static action_bar:I = 0x7f0a006e

.field public static action_bar_activity_content:I = 0x7f0a006f

.field public static action_bar_container:I = 0x7f0a0070

.field public static action_bar_root:I = 0x7f0a0071

.field public static action_bar_spinner:I = 0x7f0a0072

.field public static action_bar_subtitle:I = 0x7f0a0073

.field public static action_bar_title:I = 0x7f0a0074

.field public static action_container:I = 0x7f0a0078

.field public static action_context_bar:I = 0x7f0a0079

.field public static action_divider:I = 0x7f0a007c

.field public static action_image:I = 0x7f0a007f

.field public static action_menu_divider:I = 0x7f0a0080

.field public static action_menu_presenter:I = 0x7f0a0081

.field public static action_mode_bar:I = 0x7f0a0082

.field public static action_mode_bar_stub:I = 0x7f0a0083

.field public static action_mode_close_button:I = 0x7f0a0084

.field public static action_text:I = 0x7f0a008b

.field public static actions:I = 0x7f0a008e

.field public static activity_chooser_view_content:I = 0x7f0a009a

.field public static add:I = 0x7f0a009c

.field public static adjust_height:I = 0x7f0a00af

.field public static adjust_width:I = 0x7f0a00b0

.field public static alertTitle:I = 0x7f0a00e2

.field public static aligned:I = 0x7f0a00e3

.field public static allStates:I = 0x7f0a00e9

.field public static animateToEnd:I = 0x7f0a00fa

.field public static animateToStart:I = 0x7f0a00fb

.field public static antiClockwise:I = 0x7f0a00fc

.field public static anticipate:I = 0x7f0a00fd

.field public static arc:I = 0x7f0a0111

.field public static asConfigured:I = 0x7f0a0117

.field public static async:I = 0x7f0a011a

.field public static auto:I = 0x7f0a012a

.field public static autoComplete:I = 0x7f0a012c

.field public static autoCompleteToEnd:I = 0x7f0a012d

.field public static autoCompleteToStart:I = 0x7f0a012e

.field public static baseline:I = 0x7f0a01a9

.field public static bestChoice:I = 0x7f0a01b6

.field public static blocking:I = 0x7f0a01fd

.field public static bottom:I = 0x7f0a0245

.field public static bottom_container:I = 0x7f0a0277

.field public static bottom_sheet_scroll:I = 0x7f0a027b

.field public static bounce:I = 0x7f0a027f

.field public static buttonPanel:I = 0x7f0a0338

.field public static callMeasure:I = 0x7f0a0358

.field public static cancel_button:I = 0x7f0a0360

.field public static carryVelocity:I = 0x7f0a0388

.field public static center:I = 0x7f0a03f4

.field public static centerCrop:I = 0x7f0a03f5

.field public static centerInside:I = 0x7f0a03f7

.field public static chain:I = 0x7f0a0401

.field public static chain2:I = 0x7f0a0402

.field public static checkbox:I = 0x7f0a0433

.field public static checked:I = 0x7f0a0437

.field public static chronometer:I = 0x7f0a0464

.field public static circle_center:I = 0x7f0a0473

.field public static clear_text:I = 0x7f0a04f1

.field public static clockwise:I = 0x7f0a04f9

.field public static closest:I = 0x7f0a04fe

.field public static compatible:I = 0x7f0a054e

.field public static compress:I = 0x7f0a0551

.field public static confirm_button:I = 0x7f0a0556

.field public static constraint:I = 0x7f0a0558

.field public static container:I = 0x7f0a0561

.field public static content:I = 0x7f0a057b

.field public static contentPanel:I = 0x7f0a0587

.field public static contiguous:I = 0x7f0a058b

.field public static continuousVelocity:I = 0x7f0a058d

.field public static coordinator:I = 0x7f0a0590

.field public static cos:I = 0x7f0a0597

.field public static counterclockwise:I = 0x7f0a05a8

.field public static cradle:I = 0x7f0a05c0

.field public static currentState:I = 0x7f0a05e0

.field public static custom:I = 0x7f0a05e2

.field public static customPanel:I = 0x7f0a05e5

.field public static cut:I = 0x7f0a05e7

.field public static dark:I = 0x7f0a05f6

.field public static date_picker_actions:I = 0x7f0a060b

.field public static decelerate:I = 0x7f0a062b

.field public static decelerateAndComplete:I = 0x7f0a062c

.field public static decor_content_parent:I = 0x7f0a0630

.field public static default_activity_button:I = 0x7f0a0634

.field public static deltaRelative:I = 0x7f0a0639

.field public static design_bottom_sheet:I = 0x7f0a0646

.field public static design_menu_item_action_area:I = 0x7f0a0647

.field public static design_menu_item_action_area_stub:I = 0x7f0a0648

.field public static design_menu_item_text:I = 0x7f0a0649

.field public static design_navigation_view:I = 0x7f0a064a

.field public static dialog_button:I = 0x7f0a0654

.field public static disjoint:I = 0x7f0a0670

.field public static documents:I = 0x7f0a0683

.field public static dragAnticlockwise:I = 0x7f0a0696

.field public static dragClockwise:I = 0x7f0a0697

.field public static dragDown:I = 0x7f0a0698

.field public static dragEnd:I = 0x7f0a0699

.field public static dragLeft:I = 0x7f0a069a

.field public static dragRight:I = 0x7f0a069b

.field public static dragStart:I = 0x7f0a069c

.field public static dragUp:I = 0x7f0a069d

.field public static dropdown_menu:I = 0x7f0a06a4

.field public static easeIn:I = 0x7f0a06b3

.field public static easeInOut:I = 0x7f0a06b4

.field public static easeOut:I = 0x7f0a06b5

.field public static east:I = 0x7f0a06b6

.field public static edge:I = 0x7f0a06b8

.field public static edit_query:I = 0x7f0a06bd

.field public static edit_text_id:I = 0x7f0a06be

.field public static elastic:I = 0x7f0a06c8

.field public static embed:I = 0x7f0a06ce

.field public static end:I = 0x7f0a0717

.field public static endToStart:I = 0x7f0a0727

.field public static expand_activities_button:I = 0x7f0a0796

.field public static expanded_menu:I = 0x7f0a079a

.field public static fade:I = 0x7f0a07b2

.field public static fill:I = 0x7f0a07d1

.field public static fillCenter:I = 0x7f0a07d2

.field public static fillEnd:I = 0x7f0a07d3

.field public static fillStart:I = 0x7f0a07d4

.field public static filled:I = 0x7f0a07d7

.field public static fit:I = 0x7f0a0855

.field public static fitCenter:I = 0x7f0a0856

.field public static fitEnd:I = 0x7f0a0857

.field public static fitStart:I = 0x7f0a0858

.field public static fitXY:I = 0x7f0a085a

.field public static fixed:I = 0x7f0a0863

.field public static flip:I = 0x7f0a08be

.field public static floating:I = 0x7f0a08bf

.field public static forever:I = 0x7f0a08c9

.field public static fragment_container_view_tag:I = 0x7f0a08de

.field public static frost:I = 0x7f0a08eb

.field public static fullscreen_header:I = 0x7f0a08f7

.field public static ghost_view:I = 0x7f0a095f

.field public static ghost_view_holder:I = 0x7f0a0960

.field public static gone:I = 0x7f0a0972

.field public static group_divider:I = 0x7f0a09ac

.field public static header_title:I = 0x7f0a0a80

.field public static helper_root:I = 0x7f0a0a89

.field public static hide_ime_id:I = 0x7f0a0a96

.field public static home:I = 0x7f0a0aa3

.field public static honorRequest:I = 0x7f0a0aa5

.field public static horizontal_only:I = 0x7f0a0aa9

.field public static icon:I = 0x7f0a0ac8

.field public static icon_group:I = 0x7f0a0adb

.field public static icon_only:I = 0x7f0a0adc

.field public static ignore:I = 0x7f0a0adf

.field public static ignoreRequest:I = 0x7f0a0ae0

.field public static image:I = 0x7f0a0ae2

.field public static image_view:I = 0x7f0a0b3b

.field public static immediateStop:I = 0x7f0a0b82

.field public static included:I = 0x7f0a0b8b

.field public static indeterminate:I = 0x7f0a0b8c

.field public static info:I = 0x7f0a0b92

.field public static invisible:I = 0x7f0a0bb9

.field public static inward:I = 0x7f0a0bba

.field public static italic:I = 0x7f0a0bc6

.field public static item_touch_helper_previous_elevation:I = 0x7f0a0bd3

.field public static jumpToEnd:I = 0x7f0a0dba

.field public static jumpToStart:I = 0x7f0a0dbb

.field public static labeled:I = 0x7f0a0dc9

.field public static language_section:I = 0x7f0a0dca

.field public static layout:I = 0x7f0a0dd1

.field public static left:I = 0x7f0a0de4

.field public static leftToRight:I = 0x7f0a0def

.field public static legacy:I = 0x7f0a0df3

.field public static light:I = 0x7f0a0e00

.field public static line1:I = 0x7f0a0e13

.field public static line3:I = 0x7f0a0e14

.field public static linear:I = 0x7f0a0e3f

.field public static listMode:I = 0x7f0a0e4c

.field public static list_item:I = 0x7f0a0e4e

.field public static local_video_section:I = 0x7f0a0ed2

.field public static local_video_view:I = 0x7f0a0ed3

.field public static m3_side_sheet:I = 0x7f0a0efc

.field public static markwon_drawables_scheduler:I = 0x7f0a0f1f

.field public static markwon_drawables_scheduler_last_text_hashcode:I = 0x7f0a0f20

.field public static marquee:I = 0x7f0a0f21

.field public static masked:I = 0x7f0a0f24

.field public static match_constraint:I = 0x7f0a0f29

.field public static match_parent:I = 0x7f0a0f2a

.field public static material_clock_display:I = 0x7f0a0f2e

.field public static material_clock_display_and_toggle:I = 0x7f0a0f2f

.field public static material_clock_face:I = 0x7f0a0f30

.field public static material_clock_hand:I = 0x7f0a0f31

.field public static material_clock_level:I = 0x7f0a0f32

.field public static material_clock_period_am_button:I = 0x7f0a0f33

.field public static material_clock_period_pm_button:I = 0x7f0a0f34

.field public static material_clock_period_toggle:I = 0x7f0a0f35

.field public static material_hour_text_input:I = 0x7f0a0f36

.field public static material_hour_tv:I = 0x7f0a0f37

.field public static material_label:I = 0x7f0a0f38

.field public static material_minute_text_input:I = 0x7f0a0f39

.field public static material_minute_tv:I = 0x7f0a0f3a

.field public static material_textinput_timepicker:I = 0x7f0a0f3b

.field public static material_timepicker_cancel_button:I = 0x7f0a0f3c

.field public static material_timepicker_container:I = 0x7f0a0f3d

.field public static material_timepicker_mode_button:I = 0x7f0a0f3e

.field public static material_timepicker_ok_button:I = 0x7f0a0f3f

.field public static material_timepicker_view:I = 0x7f0a0f40

.field public static material_value_index:I = 0x7f0a0f41

.field public static matrix:I = 0x7f0a0f43

.field public static message:I = 0x7f0a0f56

.field public static middle:I = 0x7f0a0f5e

.field public static mini:I = 0x7f0a0f69

.field public static month_grid:I = 0x7f0a0f79

.field public static month_navigation_bar:I = 0x7f0a0f7a

.field public static month_navigation_fragment_toggle:I = 0x7f0a0f7b

.field public static month_navigation_next:I = 0x7f0a0f7c

.field public static month_navigation_previous:I = 0x7f0a0f7d

.field public static month_title:I = 0x7f0a0f7e

.field public static motion_base:I = 0x7f0a0f83

.field public static mtrl_anchor_parent:I = 0x7f0a0f8a

.field public static mtrl_calendar_day_selector_frame:I = 0x7f0a0f8b

.field public static mtrl_calendar_days_of_week:I = 0x7f0a0f8c

.field public static mtrl_calendar_frame:I = 0x7f0a0f8d

.field public static mtrl_calendar_main_pane:I = 0x7f0a0f8e

.field public static mtrl_calendar_months:I = 0x7f0a0f8f

.field public static mtrl_calendar_selection_frame:I = 0x7f0a0f90

.field public static mtrl_calendar_text_input_frame:I = 0x7f0a0f91

.field public static mtrl_calendar_year_selector_frame:I = 0x7f0a0f92

.field public static mtrl_card_checked_layer_id:I = 0x7f0a0f93

.field public static mtrl_child_content_container:I = 0x7f0a0f94

.field public static mtrl_internal_children_alpha_tag:I = 0x7f0a0f95

.field public static mtrl_motion_snapshot_view:I = 0x7f0a0f96

.field public static mtrl_picker_fullscreen:I = 0x7f0a0f97

.field public static mtrl_picker_header:I = 0x7f0a0f98

.field public static mtrl_picker_header_selection_text:I = 0x7f0a0f99

.field public static mtrl_picker_header_title_and_selection:I = 0x7f0a0f9a

.field public static mtrl_picker_header_toggle:I = 0x7f0a0f9b

.field public static mtrl_picker_text_input_date:I = 0x7f0a0f9c

.field public static mtrl_picker_text_input_range_end:I = 0x7f0a0f9d

.field public static mtrl_picker_text_input_range_start:I = 0x7f0a0f9e

.field public static mtrl_picker_title_text:I = 0x7f0a0f9f

.field public static mtrl_view_tag_bottom_padding:I = 0x7f0a0fa0

.field public static multiply:I = 0x7f0a0fa4

.field public static navigation_bar_item_active_indicator_view:I = 0x7f0a0fb3

.field public static navigation_bar_item_icon_container:I = 0x7f0a0fb4

.field public static navigation_bar_item_icon_view:I = 0x7f0a0fb5

.field public static navigation_bar_item_labels_group:I = 0x7f0a0fb6

.field public static navigation_bar_item_large_label_view:I = 0x7f0a0fb7

.field public static navigation_bar_item_small_label_view:I = 0x7f0a0fb8

.field public static navigation_header_container:I = 0x7f0a0fb9

.field public static neverCompleteToEnd:I = 0x7f0a0fc4

.field public static neverCompleteToStart:I = 0x7f0a0fc5

.field public static noState:I = 0x7f0a0fd7

.field public static none:I = 0x7f0a0fd9

.field public static normal:I = 0x7f0a0fdb

.field public static north:I = 0x7f0a0fdc

.field public static notification_background:I = 0x7f0a0fe2

.field public static notification_main_column:I = 0x7f0a0fe4

.field public static notification_main_column_container:I = 0x7f0a0fe5

.field public static off:I = 0x7f0a0ffb

.field public static on:I = 0x7f0a1001

.field public static open_search_bar_text_view:I = 0x7f0a101d

.field public static open_search_view_background:I = 0x7f0a101e

.field public static open_search_view_clear_button:I = 0x7f0a101f

.field public static open_search_view_content_container:I = 0x7f0a1020

.field public static open_search_view_divider:I = 0x7f0a1021

.field public static open_search_view_dummy_toolbar:I = 0x7f0a1022

.field public static open_search_view_edit_text:I = 0x7f0a1023

.field public static open_search_view_header_container:I = 0x7f0a1024

.field public static open_search_view_root:I = 0x7f0a1025

.field public static open_search_view_scrim:I = 0x7f0a1026

.field public static open_search_view_search_prefix:I = 0x7f0a1027

.field public static open_search_view_status_bar_spacer:I = 0x7f0a1028

.field public static open_search_view_toolbar:I = 0x7f0a1029

.field public static open_search_view_toolbar_container:I = 0x7f0a102a

.field public static outline:I = 0x7f0a103d

.field public static outward:I = 0x7f0a103f

.field public static overshoot:I = 0x7f0a1046

.field public static packed:I = 0x7f0a1047

.field public static parallax:I = 0x7f0a104a

.field public static parent:I = 0x7f0a104c

.field public static parentPanel:I = 0x7f0a1051

.field public static parentRelative:I = 0x7f0a1052

.field public static parent_matrix:I = 0x7f0a1056

.field public static password_toggle:I = 0x7f0a1065

.field public static path:I = 0x7f0a1066

.field public static pathRelative:I = 0x7f0a1067

.field public static percent:I = 0x7f0a107c

.field public static performance:I = 0x7f0a107d

.field public static phone_verification_bottom_sheet:I = 0x7f0a1087

.field public static phone_verification_fragment:I = 0x7f0a1088

.field public static photo_frame:I = 0x7f0a108a

.field public static photo_made_indicator:I = 0x7f0a108b

.field public static pin:I = 0x7f0a1094

.field public static position:I = 0x7f0a10e4

.field public static postLayout:I = 0x7f0a10f1

.field public static pressed:I = 0x7f0a10fd

.field public static progress_circular:I = 0x7f0a112d

.field public static progress_horizontal:I = 0x7f0a112e

.field public static radio:I = 0x7f0a1170

.field public static rectangles:I = 0x7f0a1197

.field public static remote_video:I = 0x7f0a11df

.field public static remote_video_view:I = 0x7f0a11e0

.field public static report_drawn:I = 0x7f0a11eb

.field public static reverseSawtooth:I = 0x7f0a11ff

.field public static right:I = 0x7f0a1202

.field public static rightToLeft:I = 0x7f0a120f

.field public static right_icon:I = 0x7f0a1211

.field public static right_side:I = 0x7f0a1213

.field public static rounded:I = 0x7f0a123a

.field public static row_index_key:I = 0x7f0a1245

.field public static save_non_transition_alpha:I = 0x7f0a12d9

.field public static save_overlay_view:I = 0x7f0a12da

.field public static sawtooth:I = 0x7f0a12dc

.field public static scale:I = 0x7f0a12f2

.field public static screen:I = 0x7f0a12fd

.field public static scrollIndicatorDown:I = 0x7f0a1302

.field public static scrollIndicatorUp:I = 0x7f0a1303

.field public static scrollView:I = 0x7f0a1304

.field public static scrollable:I = 0x7f0a130a

.field public static search_badge:I = 0x7f0a1317

.field public static search_bar:I = 0x7f0a1318

.field public static search_button:I = 0x7f0a1319

.field public static search_close_btn:I = 0x7f0a131a

.field public static search_edit_frame:I = 0x7f0a131b

.field public static search_go_btn:I = 0x7f0a131d

.field public static search_mag_icon:I = 0x7f0a131e

.field public static search_plate:I = 0x7f0a131f

.field public static search_src_text:I = 0x7f0a1320

.field public static search_voice_btn:I = 0x7f0a1322

.field public static select_dialog_listview:I = 0x7f0a13b0

.field public static selected:I = 0x7f0a13b1

.field public static selection_type:I = 0x7f0a13ba

.field public static sharedValueSet:I = 0x7f0a13fe

.field public static sharedValueUnset:I = 0x7f0a13ff

.field public static shortcut:I = 0x7f0a1495

.field public static sin:I = 0x7f0a14a4

.field public static skipped:I = 0x7f0a14b9

.field public static slide:I = 0x7f0a14bb

.field public static snackbar_action:I = 0x7f0a14d8

.field public static snackbar_text:I = 0x7f0a14d9

.field public static sns_auto_manual:I = 0x7f0a14dc

.field public static sns_auto_manual_switch:I = 0x7f0a14dd

.field public static sns_autocapture_hint:I = 0x7f0a14de

.field public static sns_barrier_end:I = 0x7f0a14df

.field public static sns_barrier_start:I = 0x7f0a14e0

.field public static sns_below_image_content:I = 0x7f0a14e1

.field public static sns_bg_file_item:I = 0x7f0a14e2

.field public static sns_bottom_sheet:I = 0x7f0a14e3

.field public static sns_bottomsheet_toolbar:I = 0x7f0a14e4

.field public static sns_brief_details:I = 0x7f0a14e5

.field public static sns_button:I = 0x7f0a14e6

.field public static sns_button1:I = 0x7f0a14e7

.field public static sns_button2:I = 0x7f0a14e8

.field public static sns_button3:I = 0x7f0a14e9

.field public static sns_button_close:I = 0x7f0a14ea

.field public static sns_button_option:I = 0x7f0a14eb

.field public static sns_camera:I = 0x7f0a14ec

.field public static sns_camera_preview:I = 0x7f0a14ed

.field public static sns_camera_preview_container:I = 0x7f0a14ee

.field public static sns_center_screen:I = 0x7f0a14ef

.field public static sns_checkgroup:I = 0x7f0a14f0

.field public static sns_child_content:I = 0x7f0a14f1

.field public static sns_complete_icon:I = 0x7f0a14f2

.field public static sns_container:I = 0x7f0a14f3

.field public static sns_content:I = 0x7f0a14f4

.field public static sns_content_icon:I = 0x7f0a14f5

.field public static sns_continue:I = 0x7f0a14f6

.field public static sns_counter:I = 0x7f0a14f7

.field public static sns_country_field:I = 0x7f0a14f8

.field public static sns_country_selector:I = 0x7f0a14f9

.field public static sns_country_title:I = 0x7f0a14fa

.field public static sns_custom_view:I = 0x7f0a14fb

.field public static sns_dark_overlay:I = 0x7f0a14fc

.field public static sns_data_bool:I = 0x7f0a14fd

.field public static sns_data_date:I = 0x7f0a14fe

.field public static sns_data_datetime:I = 0x7f0a14ff

.field public static sns_data_file:I = 0x7f0a1500

.field public static sns_debug:I = 0x7f0a1501

.field public static sns_debug_info:I = 0x7f0a1502

.field public static sns_debug_info_right:I = 0x7f0a1503

.field public static sns_description:I = 0x7f0a1504

.field public static sns_description_1:I = 0x7f0a1505

.field public static sns_description_2:I = 0x7f0a1506

.field public static sns_divider:I = 0x7f0a1507

.field public static sns_doc_bounds_confidence:I = 0x7f0a1508

.field public static sns_doc_detection_result:I = 0x7f0a1509

.field public static sns_document:I = 0x7f0a150a

.field public static sns_documents_empty:I = 0x7f0a150b

.field public static sns_documents_title:I = 0x7f0a150c

.field public static sns_done:I = 0x7f0a150d

.field public static sns_editor:I = 0x7f0a150e

.field public static sns_editor_layout:I = 0x7f0a150f

.field public static sns_eid_bottom_sheet:I = 0x7f0a1510

.field public static sns_email:I = 0x7f0a1511

.field public static sns_email_id:I = 0x7f0a1512

.field public static sns_end_container:I = 0x7f0a1513

.field public static sns_end_icon:I = 0x7f0a1514

.field public static sns_error:I = 0x7f0a1515

.field public static sns_error_bottom_sheet:I = 0x7f0a1516

.field public static sns_error_icon:I = 0x7f0a1517

.field public static sns_error_subtitle:I = 0x7f0a1518

.field public static sns_error_title:I = 0x7f0a1519

.field public static sns_esign_bottom_progress_bar:I = 0x7f0a151a

.field public static sns_esign_bottom_sheet:I = 0x7f0a151b

.field public static sns_example:I = 0x7f0a151c

.field public static sns_face_view:I = 0x7f0a151d

.field public static sns_footer:I = 0x7f0a151e

.field public static sns_form_container:I = 0x7f0a151f

.field public static sns_form_placeholder:I = 0x7f0a1520

.field public static sns_fragment_content:I = 0x7f0a1521

.field public static sns_frame:I = 0x7f0a1522

.field public static sns_frame_border:I = 0x7f0a1523

.field public static sns_frame_container:I = 0x7f0a1524

.field public static sns_frame_mask:I = 0x7f0a1525

.field public static sns_frame_popup_hint_container:I = 0x7f0a1526

.field public static sns_frame_with_background:I = 0x7f0a1527

.field public static sns_gallery:I = 0x7f0a1528

.field public static sns_good_photo_confidence:I = 0x7f0a1529

.field public static sns_guideline_center:I = 0x7f0a152a

.field public static sns_guideline_end:I = 0x7f0a152b

.field public static sns_guideline_start:I = 0x7f0a152c

.field public static sns_helper:I = 0x7f0a152d

.field public static sns_helper_brief:I = 0x7f0a152e

.field public static sns_helper_details:I = 0x7f0a152f

.field public static sns_helper_details_frame:I = 0x7f0a1530

.field public static sns_helper_title:I = 0x7f0a1531

.field public static sns_hint:I = 0x7f0a1532

.field public static sns_icon:I = 0x7f0a1533

.field public static sns_iddoc:I = 0x7f0a1534

.field public static sns_image:I = 0x7f0a1535

.field public static sns_instructions:I = 0x7f0a1536

.field public static sns_intro_content:I = 0x7f0a1537

.field public static sns_item_end_icon:I = 0x7f0a1538

.field public static sns_item_start_icon:I = 0x7f0a1539

.field public static sns_item_subtitle:I = 0x7f0a153a

.field public static sns_item_title:I = 0x7f0a153b

.field public static sns_item_title_placeholder:I = 0x7f0a153c

.field public static sns_label:I = 0x7f0a153d

.field public static sns_label_container:I = 0x7f0a153e

.field public static sns_label_icon:I = 0x7f0a153f

.field public static sns_language_section_title:I = 0x7f0a1540

.field public static sns_list:I = 0x7f0a1541

.field public static sns_nfc_icon:I = 0x7f0a1542

.field public static sns_otp_error:I = 0x7f0a1543

.field public static sns_overlay:I = 0x7f0a1544

.field public static sns_phone:I = 0x7f0a1545

.field public static sns_phone_id:I = 0x7f0a1546

.field public static sns_photo:I = 0x7f0a1547

.field public static sns_photo_preview:I = 0x7f0a1548

.field public static sns_photos:I = 0x7f0a1549

.field public static sns_picker_progress:I = 0x7f0a154a

.field public static sns_pin_code:I = 0x7f0a154b

.field public static sns_pin_error:I = 0x7f0a154c

.field public static sns_placeholder:I = 0x7f0a154d

.field public static sns_player:I = 0x7f0a154e

.field public static sns_player_container:I = 0x7f0a154f

.field public static sns_popup_hint_container_background:I = 0x7f0a1550

.field public static sns_powered:I = 0x7f0a1551

.field public static sns_preview_content:I = 0x7f0a1552

.field public static sns_primary_button:I = 0x7f0a1553

.field public static sns_primary_button_progress:I = 0x7f0a1554

.field public static sns_progress:I = 0x7f0a1555

.field public static sns_progress_bar:I = 0x7f0a1556

.field public static sns_progress_bg:I = 0x7f0a1557

.field public static sns_progress_text:I = 0x7f0a1558

.field public static sns_radiogroup:I = 0x7f0a1559

.field public static sns_reading_progress:I = 0x7f0a155a

.field public static sns_resend_verification_code:I = 0x7f0a155b

.field public static sns_rotate_ccw:I = 0x7f0a155c

.field public static sns_rotate_cw:I = 0x7f0a155d

.field public static sns_rotation_buttons:I = 0x7f0a155e

.field public static sns_save_frame:I = 0x7f0a155f

.field public static sns_scroller:I = 0x7f0a1560

.field public static sns_secondary_button:I = 0x7f0a1561

.field public static sns_skip:I = 0x7f0a1562

.field public static sns_start_icon:I = 0x7f0a1563

.field public static sns_status:I = 0x7f0a1564

.field public static sns_status_comment:I = 0x7f0a1565

.field public static sns_status_icon:I = 0x7f0a1566

.field public static sns_status_title:I = 0x7f0a1567

.field public static sns_stop:I = 0x7f0a1568

.field public static sns_subtitle:I = 0x7f0a1569

.field public static sns_success_icon:I = 0x7f0a156a

.field public static sns_success_title:I = 0x7f0a156b

.field public static sns_text:I = 0x7f0a156c

.field public static sns_time:I = 0x7f0a156d

.field public static sns_title:I = 0x7f0a156e

.field public static sns_toolbar:I = 0x7f0a156f

.field public static sns_upload_progress:I = 0x7f0a1570

.field public static sns_vi_bottom_progress_bar:I = 0x7f0a1571

.field public static sns_video_circle_progress:I = 0x7f0a1572

.field public static sns_view_stub:I = 0x7f0a1573

.field public static sns_warning:I = 0x7f0a1574

.field public static sns_warning_icon:I = 0x7f0a1575

.field public static sns_warning_message:I = 0x7f0a1576

.field public static sns_warning_primary_button:I = 0x7f0a1577

.field public static sns_warning_secondary_button:I = 0x7f0a1578

.field public static sns_warning_title:I = 0x7f0a1579

.field public static sns_web_content:I = 0x7f0a157a

.field public static sns_web_view_bottom_sheet:I = 0x7f0a157b

.field public static sns_web_view_bottom_sheet_container:I = 0x7f0a157c

.field public static sns_webview:I = 0x7f0a157d

.field public static south:I = 0x7f0a158c

.field public static spacer:I = 0x7f0a1598

.field public static special_effects_controller_view_tag:I = 0x7f0a159c

.field public static spline:I = 0x7f0a15a8

.field public static split_action_bar:I = 0x7f0a15a9

.field public static spread:I = 0x7f0a15bc

.field public static spread_inside:I = 0x7f0a15bd

.field public static spring:I = 0x7f0a15be

.field public static square:I = 0x7f0a15bf

.field public static src_atop:I = 0x7f0a15c5

.field public static src_in:I = 0x7f0a15c6

.field public static src_over:I = 0x7f0a15c7

.field public static standard:I = 0x7f0a15d6

.field public static start:I = 0x7f0a15df

.field public static startHorizontal:I = 0x7f0a15e8

.field public static startToEnd:I = 0x7f0a15ee

.field public static startVertical:I = 0x7f0a15f0

.field public static staticLayout:I = 0x7f0a15f4

.field public static staticPostLayout:I = 0x7f0a15f6

.field public static stop:I = 0x7f0a161b

.field public static stretch:I = 0x7f0a1620

.field public static submenuarrow:I = 0x7f0a1639

.field public static submit_area:I = 0x7f0a163a

.field public static switchCamera:I = 0x7f0a165f

.field public static tabMode:I = 0x7f0a167a

.field public static tag_accessibility_actions:I = 0x7f0a169a

.field public static tag_accessibility_clickable_spans:I = 0x7f0a169b

.field public static tag_accessibility_heading:I = 0x7f0a169c

.field public static tag_accessibility_pane_title:I = 0x7f0a169d

.field public static tag_on_apply_window_listener:I = 0x7f0a16a2

.field public static tag_on_receive_content_listener:I = 0x7f0a16a3

.field public static tag_on_receive_content_mime_types:I = 0x7f0a16a4

.field public static tag_screen_reader_focusable:I = 0x7f0a16a7

.field public static tag_state_description:I = 0x7f0a16a8

.field public static tag_transition_group:I = 0x7f0a16ab

.field public static tag_unhandled_key_event_manager:I = 0x7f0a16ac

.field public static tag_unhandled_key_listeners:I = 0x7f0a16ad

.field public static tag_window_insets_animation_callback:I = 0x7f0a16ae

.field public static text:I = 0x7f0a170c

.field public static text1:I = 0x7f0a170d

.field public static text2:I = 0x7f0a170e

.field public static text3:I = 0x7f0a170f

.field public static textSpacerNoButtons:I = 0x7f0a172c

.field public static textSpacerNoTitle:I = 0x7f0a172d

.field public static text_input_end_icon:I = 0x7f0a1759

.field public static text_input_error_icon:I = 0x7f0a175a

.field public static text_input_start_icon:I = 0x7f0a175b

.field public static textinput_counter:I = 0x7f0a175e

.field public static textinput_error:I = 0x7f0a175f

.field public static textinput_helper_text:I = 0x7f0a1760

.field public static textinput_placeholder:I = 0x7f0a1761

.field public static textinput_prefix_text:I = 0x7f0a1762

.field public static textinput_suffix_text:I = 0x7f0a1763

.field public static time:I = 0x7f0a17e3

.field public static timer:I = 0x7f0a17f3

.field public static title:I = 0x7f0a1808

.field public static titleDividerNoCustom:I = 0x7f0a1811

.field public static title_template:I = 0x7f0a1825

.field public static top:I = 0x7f0a1852

.field public static topPanel:I = 0x7f0a1873

.field public static touch_outside:I = 0x7f0a18e1

.field public static transition_current_scene:I = 0x7f0a1903

.field public static transition_layout_save:I = 0x7f0a1905

.field public static transition_position:I = 0x7f0a1907

.field public static transition_scene_layoutid_cache:I = 0x7f0a1908

.field public static transition_transform:I = 0x7f0a1909

.field public static triangle:I = 0x7f0a1910

.field public static unchecked:I = 0x7f0a1ddf

.field public static uniform:I = 0x7f0a1de0

.field public static unlabeled:I = 0x7f0a1de5

.field public static up:I = 0x7f0a1de7

.field public static vertical_only:I = 0x7f0a1f02

.field public static view_offset_helper:I = 0x7f0a1fa4

.field public static view_transition:I = 0x7f0a1fac

.field public static view_tree_lifecycle_owner:I = 0x7f0a1fae

.field public static view_tree_on_back_pressed_dispatcher_owner:I = 0x7f0a1faf

.field public static view_tree_saved_state_registry_owner:I = 0x7f0a1fb0

.field public static view_tree_view_model_store_owner:I = 0x7f0a1fb1

.field public static visible:I = 0x7f0a1fb2

.field public static visible_removing_fragment_view_tag:I = 0x7f0a1fb4

.field public static west:I = 0x7f0a1fe0

.field public static wide:I = 0x7f0a1feb

.field public static with_icon:I = 0x7f0a2000

.field public static withinBounds:I = 0x7f0a2003

.field public static wrap:I = 0x7f0a2005

.field public static wrap_content:I = 0x7f0a2006

.field public static wrap_content_constrained:I = 0x7f0a2007

.field public static x_left:I = 0x7f0a2013

.field public static x_right:I = 0x7f0a2014


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
