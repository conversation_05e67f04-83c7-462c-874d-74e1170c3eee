.class public final LtW0/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/u$b;


# instance fields
.field public final a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LtW0/x;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;

    .line 5
    .line 6
    return-void
.end method

.method public static c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;)Ldagger/internal/h;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;",
            ")",
            "Ldagger/internal/h<",
            "LtW0/u$b;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, LtW0/x;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LtW0/x;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;)V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;)Landroidx/lifecycle/b0;
    .locals 0

    .line 1
    check-cast p1, LwX0/c;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LtW0/x;->b(LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModelOld;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public b(LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModelOld;
    .locals 1

    .line 1
    iget-object v0, p0, LtW0/x;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/I;->b(LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModelOld;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
