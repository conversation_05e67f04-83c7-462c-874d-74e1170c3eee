.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$a;,
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;,
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u001f\u0008\u0001\u0018\u0000 I2\u00020\u0001:\u0002J!B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ/\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u0013H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001f\u0010\u0019\u001a\u00020\u00102\u0006\u0010\u0017\u001a\u00020\u000b2\u0006\u0010\u0018\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u001f\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010!\u001a\u00020\u00102\u0006\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008!\u0010\"R\u0014\u0010$\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010#R\u0014\u0010\'\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010&R\u0014\u0010)\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010&R\u0014\u0010+\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010&R\u0014\u0010/\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0016\u00101\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u0010#R\u0016\u00103\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u0010#R\u0016\u00104\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\r\u0010#R*\u0010<\u001a\u00020\u000b2\u0006\u00105\u001a\u00020\u000b8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109\"\u0004\u0008:\u0010;R*\u0010@\u001a\u00020\u000b2\u0006\u00105\u001a\u00020\u000b8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008=\u00107\u001a\u0004\u0008>\u00109\"\u0004\u0008?\u0010;R*\u0010F\u001a\u00020\u001f2\u0006\u00105\u001a\u00020\u001f8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008A\u0010B\u001a\u0004\u0008C\u0010D\"\u0004\u0008E\u0010\"R\u0014\u0010H\u001a\u00020\u00088BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008G\u0010\n\u00a8\u0006K"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attributeSet",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "getMaxRotationOffsetInDegrees",
        "()F",
        "",
        "w",
        "h",
        "oldw",
        "oldh",
        "",
        "onSizeChanged",
        "(IIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "size",
        "measureSpec",
        "b",
        "(II)I",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;",
        "direction",
        "a",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;)V",
        "F",
        "trackWidth",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "trackPaint",
        "c",
        "progressPaint",
        "d",
        "shortProgressIndicatorPaint",
        "Landroid/graphics/RectF;",
        "e",
        "Landroid/graphics/RectF;",
        "circularProgressRect",
        "f",
        "centerX",
        "g",
        "centerY",
        "radius",
        "value",
        "i",
        "I",
        "getProgress",
        "()I",
        "setProgress",
        "(I)V",
        "progress",
        "j",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "k",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;",
        "getProgressDirection",
        "()Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;",
        "setProgressDirection",
        "progressDirection",
        "getCircleLength",
        "circleLength",
        "l",
        "Direction",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final l:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final m:I


# instance fields
.field public final a:F

.field public final b:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:F

.field public g:F

.field public h:F

.field public i:I

.field public j:I

.field public k:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->l:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->m:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_5:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimension(I)F

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 4
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    const/4 v1, 0x1

    .line 5
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 6
    sget-object v2, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 7
    sget v3, LlZ0/d;->uikitSecondary10:I

    const/4 v4, 0x0

    const/4 v5, 0x2

    invoke-static {p1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    invoke-virtual {v0, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 8
    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->b:Landroid/graphics/Paint;

    .line 10
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 11
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 12
    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 13
    sget-object v2, Landroid/graphics/Paint$Cap;->ROUND:Landroid/graphics/Paint$Cap;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStrokeCap(Landroid/graphics/Paint$Cap;)V

    .line 14
    sget v2, LlZ0/d;->uikitPrimary:I

    invoke-static {p1, v2, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 15
    invoke-virtual {v0, p2}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 16
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->c:Landroid/graphics/Paint;

    .line 17
    new-instance p2, Landroid/graphics/Paint;

    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 18
    invoke-virtual {p2, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 19
    sget-object v0, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 20
    sget v0, LlZ0/d;->uikitPrimary:I

    invoke-static {p1, v0, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p1

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 21
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->d:Landroid/graphics/Paint;

    .line 22
    new-instance p1, Landroid/graphics/RectF;

    invoke-direct {p1}, Landroid/graphics/RectF;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->e:Landroid/graphics/RectF;

    const/16 p1, 0x64

    .line 23
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->j:I

    .line 24
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;->CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->k:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final getCircleLength()F
    .locals 2

    .line 1
    const v0, 0x40c90fdb

    .line 2
    .line 3
    .line 4
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->h:F

    .line 5
    .line 6
    mul-float v1, v1, v0

    .line 7
    .line 8
    return v1
.end method

.method private final getMaxRotationOffsetInDegrees()F
    .locals 2

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->getCircleLength()F

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    div-float/2addr v0, v1

    .line 8
    const/high16 v1, 0x43b40000    # 360.0f

    .line 9
    .line 10
    mul-float v0, v0, v1

    .line 11
    .line 12
    const/high16 v1, 0x3fe00000    # 1.75f

    .line 13
    .line 14
    mul-float v0, v0, v1

    .line 15
    .line 16
    return v0
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_1

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-ne p1, v0, :cond_0

    .line 14
    .line 15
    const/high16 p1, -0x40800000    # -1.0f

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 19
    .line 20
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    const/high16 p1, 0x3f800000    # 1.0f

    .line 25
    .line 26
    :goto_0
    invoke-virtual {p0, p1}, Landroid/view/View;->setScaleX(F)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final b(II)I
    .locals 2

    .line 1
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getMode(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    const/high16 v1, -0x80000000

    .line 10
    .line 11
    if-eq v0, v1, :cond_1

    .line 12
    .line 13
    const/high16 v1, 0x40000000    # 2.0f

    .line 14
    .line 15
    if-eq v0, v1, :cond_0

    .line 16
    .line 17
    return p1

    .line 18
    :cond_0
    return p2

    .line 19
    :cond_1
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    return p1
.end method

.method public final getMaxProgress()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public final getProgress()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->i:I

    .line 2
    .line 3
    return v0
.end method

.method public final getProgressDirection()Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->k:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 10
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->e:Landroid/graphics/RectF;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->b:Landroid/graphics/Paint;

    .line 4
    .line 5
    invoke-virtual {p1, v0, v1}, Landroid/graphics/Canvas;->drawOval(Landroid/graphics/RectF;Landroid/graphics/Paint;)V

    .line 6
    .line 7
    .line 8
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 9
    .line 10
    const/high16 v1, 0x43b40000    # 360.0f

    .line 11
    .line 12
    mul-float v0, v0, v1

    .line 13
    .line 14
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->getCircleLength()F

    .line 15
    .line 16
    .line 17
    move-result v2

    .line 18
    div-float/2addr v0, v2

    .line 19
    const/high16 v2, 0x40000000    # 2.0f

    .line 20
    .line 21
    div-float/2addr v0, v2

    .line 22
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->i:I

    .line 23
    .line 24
    if-eqz v3, :cond_3

    .line 25
    .line 26
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->j:I

    .line 27
    .line 28
    if-ne v3, v4, :cond_0

    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->e:Landroid/graphics/RectF;

    .line 31
    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->c:Landroid/graphics/Paint;

    .line 33
    .line 34
    invoke-virtual {p1, v0, v1}, Landroid/graphics/Canvas;->drawOval(Landroid/graphics/RectF;Landroid/graphics/Paint;)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_0
    int-to-float v3, v3

    .line 39
    int-to-float v4, v4

    .line 40
    div-float/2addr v3, v4

    .line 41
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->getCircleLength()F

    .line 42
    .line 43
    .line 44
    move-result v4

    .line 45
    mul-float v3, v3, v4

    .line 46
    .line 47
    iget v8, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 48
    .line 49
    const/4 v4, 0x2

    .line 50
    int-to-float v4, v4

    .line 51
    div-float v4, v8, v4

    .line 52
    .line 53
    cmpl-float v3, v3, v4

    .line 54
    .line 55
    if-lez v3, :cond_2

    .line 56
    .line 57
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->getMaxRotationOffsetInDegrees()F

    .line 58
    .line 59
    .line 60
    move-result v2

    .line 61
    sub-float v2, v1, v2

    .line 62
    .line 63
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->i:I

    .line 64
    .line 65
    int-to-float v3, v3

    .line 66
    mul-float v3, v3, v1

    .line 67
    .line 68
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->j:I

    .line 69
    .line 70
    int-to-float v1, v1

    .line 71
    div-float/2addr v3, v1

    .line 72
    sub-float/2addr v3, v0

    .line 73
    invoke-static {v2, v3}, Ljava/lang/Math;->min(FF)F

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->e:Landroid/graphics/RectF;

    .line 78
    .line 79
    const/4 v1, 0x0

    .line 80
    cmpg-float v3, v0, v1

    .line 81
    .line 82
    if-gez v3, :cond_1

    .line 83
    .line 84
    const/4 v4, 0x0

    .line 85
    goto :goto_0

    .line 86
    :cond_1
    move v4, v0

    .line 87
    :goto_0
    const/4 v5, 0x0

    .line 88
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->c:Landroid/graphics/Paint;

    .line 89
    .line 90
    const/high16 v3, 0x43870000    # 270.0f

    .line 91
    .line 92
    move-object v1, p1

    .line 93
    invoke-virtual/range {v1 .. v6}, Landroid/graphics/Canvas;->drawArc(Landroid/graphics/RectF;FFZLandroid/graphics/Paint;)V

    .line 94
    .line 95
    .line 96
    return-void

    .line 97
    :cond_2
    move-object v1, p1

    .line 98
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->f:F

    .line 99
    .line 100
    div-float v0, v8, v2

    .line 101
    .line 102
    sub-float v5, p1, v0

    .line 103
    .line 104
    div-float v0, v8, v2

    .line 105
    .line 106
    add-float v7, p1, v0

    .line 107
    .line 108
    iget-object v9, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->d:Landroid/graphics/Paint;

    .line 109
    .line 110
    const/4 v6, 0x0

    .line 111
    move-object v4, v1

    .line 112
    invoke-virtual/range {v4 .. v9}, Landroid/graphics/Canvas;->drawOval(FFFFLandroid/graphics/Paint;)V

    .line 113
    .line 114
    .line 115
    :cond_3
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/g;->size_40:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->b(II)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p0, v0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->b(II)I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 27
    .line 28
    .line 29
    move-result p2

    .line 30
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    int-to-float p1, p1

    .line 35
    const/4 p2, 0x2

    .line 36
    int-to-float p2, p2

    .line 37
    div-float/2addr p1, p2

    .line 38
    iget p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 39
    .line 40
    const/high16 v0, 0x40000000    # 2.0f

    .line 41
    .line 42
    div-float/2addr p2, v0

    .line 43
    sub-float/2addr p1, p2

    .line 44
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->h:F

    .line 45
    .line 46
    return-void
.end method

.method public onSizeChanged(IIII)V
    .locals 6

    .line 1
    int-to-float v0, p1

    .line 2
    const/high16 v1, 0x40000000    # 2.0f

    .line 3
    .line 4
    div-float/2addr v0, v1

    .line 5
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->f:F

    .line 6
    .line 7
    int-to-float v0, p2

    .line 8
    div-float/2addr v0, v1

    .line 9
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->g:F

    .line 10
    .line 11
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    int-to-float v0, v0

    .line 16
    div-float/2addr v0, v1

    .line 17
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a:F

    .line 18
    .line 19
    div-float/2addr v2, v1

    .line 20
    sub-float/2addr v0, v2

    .line 21
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->h:F

    .line 22
    .line 23
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->e:Landroid/graphics/RectF;

    .line 24
    .line 25
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->f:F

    .line 26
    .line 27
    sub-float v3, v2, v0

    .line 28
    .line 29
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->g:F

    .line 30
    .line 31
    sub-float v5, v4, v0

    .line 32
    .line 33
    add-float/2addr v2, v0

    .line 34
    add-float/2addr v4, v0

    .line 35
    invoke-virtual {v1, v3, v5, v2, v4}, Landroid/graphics/RectF;->set(FFFF)V

    .line 36
    .line 37
    .line 38
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/View;->onSizeChanged(IIII)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final setMaxProgress(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->j:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setProgress(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->i:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setProgressDirection(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->k:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar;->a(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/TournamentStagesCellRadialProgressBar$Direction;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
