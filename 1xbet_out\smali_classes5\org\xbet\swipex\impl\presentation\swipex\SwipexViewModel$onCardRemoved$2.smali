.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$onCardRemoved$2"
    f = "SwipexViewModel.kt"
    l = {
        0xb9,
        0xba,
        0xc1
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->E4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;

    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    goto/16 :goto_3

    .line 22
    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_1

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 43
    .line 44
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->label:I

    .line 49
    .line 50
    invoke-virtual {p1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->d(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    if-ne p1, v0, :cond_4

    .line 55
    .line 56
    goto :goto_2

    .line 57
    :cond_4
    :goto_0
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 58
    .line 59
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iput v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->label:I

    .line 64
    .line 65
    invoke-virtual {p1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->i(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v0, :cond_5

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_5
    :goto_1
    check-cast p1, Ljava/lang/Number;

    .line 73
    .line 74
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 75
    .line 76
    .line 77
    move-result p1

    .line 78
    if-eqz p1, :cond_7

    .line 79
    .line 80
    const/4 v1, 0x5

    .line 81
    if-eq p1, v1, :cond_6

    .line 82
    .line 83
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 84
    .line 85
    iput v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->label:I

    .line 86
    .line 87
    invoke-static {p1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->e4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    if-ne p1, v0, :cond_9

    .line 92
    .line 93
    :goto_2
    return-object v0

    .line 94
    :cond_6
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 95
    .line 96
    const/4 v0, 0x0

    .line 97
    const/4 v1, 0x0

    .line 98
    invoke-static {p1, v0, v4, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->r4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;ZILjava/lang/Object;)V

    .line 99
    .line 100
    .line 101
    goto :goto_3

    .line 102
    :cond_7
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 103
    .line 104
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->x3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Z

    .line 105
    .line 106
    .line 107
    move-result p1

    .line 108
    if-eqz p1, :cond_8

    .line 109
    .line 110
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 111
    .line 112
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;

    .line 117
    .line 118
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 119
    .line 120
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->I3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-direct {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$d;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 125
    .line 126
    .line 127
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 128
    .line 129
    .line 130
    goto :goto_3

    .line 131
    :cond_8
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lkotlinx/coroutines/flow/V;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;->a:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c$a;

    .line 138
    .line 139
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 140
    .line 141
    .line 142
    :cond_9
    :goto_3
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$onCardRemoved$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 143
    .line 144
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->a4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)V

    .line 145
    .line 146
    .line 147
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 148
    .line 149
    return-object p1
.end method
