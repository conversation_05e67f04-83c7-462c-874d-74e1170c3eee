.class public final Lgc1/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a#\u0010\n\u001a\u00020\t*\u00020\u00062\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LTb1/d;",
        "",
        "isNight",
        "Lhc1/e;",
        "b",
        "(LTb1/d;Z)Lhc1/e;",
        "LTb1/c;",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "partitionType",
        "LVb1/b;",
        "a",
        "(LTb1/c;ZLorg/xplatform/aggregator/api/model/PartitionType;)LVb1/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LTb1/c;ZLorg/xplatform/aggregator/api/model/PartitionType;)LVb1/b;
    .locals 11
    .param p0    # LTb1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/PartitionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, LTb1/c;->e()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {p0}, LTb1/c;->g()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    :goto_0
    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    invoke-virtual {p0}, LTb1/c;->f()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :cond_1
    move-object v1, p1

    .line 23
    invoke-virtual {p0}, LTb1/c;->j()I

    .line 24
    .line 25
    .line 26
    move-result v6

    .line 27
    invoke-virtual {p0}, LTb1/c;->c()J

    .line 28
    .line 29
    .line 30
    move-result-wide v3

    .line 31
    invoke-virtual {p0}, LTb1/c;->d()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    invoke-virtual {p0}, LTb1/c;->h()Z

    .line 36
    .line 37
    .line 38
    move-result v9

    .line 39
    invoke-virtual {p0}, LTb1/c;->a()Z

    .line 40
    .line 41
    .line 42
    move-result v10

    .line 43
    invoke-virtual {p0}, LTb1/c;->i()J

    .line 44
    .line 45
    .line 46
    move-result-wide v7

    .line 47
    new-instance v0, LVb1/b;

    .line 48
    .line 49
    move-object v2, p2

    .line 50
    invoke-direct/range {v0 .. v10}, LVb1/b;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/PartitionType;JLjava/lang/String;IJZZ)V

    .line 51
    .line 52
    .line 53
    return-object v0
.end method

.method public static final b(LTb1/d;Z)Lhc1/e;
    .locals 8
    .param p0    # LTb1/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, LTb1/d;->c()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {p0}, LTb1/d;->e()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    :goto_0
    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_1

    .line 17
    .line 18
    invoke-virtual {p0}, LTb1/d;->d()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    :cond_1
    move-object v1, p1

    .line 23
    invoke-virtual {p0}, LTb1/d;->i()I

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    invoke-virtual {p0}, LTb1/d;->g()I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    invoke-virtual {p0}, LTb1/d;->h()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    invoke-virtual {p0}, LTb1/d;->b()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    invoke-virtual {p0}, LTb1/d;->f()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v7

    .line 43
    invoke-virtual {p0}, LTb1/d;->j()Lorg/xplatform/aggregator/api/model/BrandType;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    new-instance v0, Lhc1/e;

    .line 48
    .line 49
    invoke-direct/range {v0 .. v7}, Lhc1/e;-><init>(Ljava/lang/String;ILjava/lang/String;ILjava/lang/String;Lorg/xplatform/aggregator/api/model/BrandType;Ljava/util/List;)V

    .line 50
    .line 51
    .line 52
    return-object v0
.end method
