.class public final Ljb1/C;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a\u001f\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lm81/b;",
        "LHX0/e;",
        "resourceManager",
        "Lmb1/b;",
        "b",
        "(Lm81/b;LHX0/e;)Lmb1/b;",
        "",
        "place",
        "",
        "active",
        "Lmb1/a;",
        "a",
        "(IZ)Lmb1/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(IZ)Lmb1/a;
    .locals 1

    .line 1
    packed-switch p0, :pswitch_data_0

    .line 2
    .line 3
    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->ACTIVE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;->PREPARE:Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;

    .line 10
    .line 11
    :goto_0
    new-instance v0, Lmb1/a$d;

    .line 12
    .line 13
    invoke-static {p0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-direct {v0, p0, p1}, Lmb1/a$d;-><init>(Ljava/lang/String;Lorg/xplatform/aggregator/impl/tournaments/presentation/pretendents/tournamentCard/models/TournamentCardCellTextType;)V

    .line 18
    .line 19
    .line 20
    return-object v0

    .line 21
    :pswitch_0
    new-instance p0, Lmb1/a$b;

    .line 22
    .line 23
    sget p1, Lu91/a;->ic_tournament_place_ten:I

    .line 24
    .line 25
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 34
    .line 35
    .line 36
    return-object p0

    .line 37
    :pswitch_1
    new-instance p0, Lmb1/a$b;

    .line 38
    .line 39
    sget p1, Lu91/a;->ic_tournament_place_nine:I

    .line 40
    .line 41
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 50
    .line 51
    .line 52
    return-object p0

    .line 53
    :pswitch_2
    new-instance p0, Lmb1/a$b;

    .line 54
    .line 55
    sget p1, Lu91/a;->ic_tournament_place_eight:I

    .line 56
    .line 57
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 66
    .line 67
    .line 68
    return-object p0

    .line 69
    :pswitch_3
    new-instance p0, Lmb1/a$b;

    .line 70
    .line 71
    sget p1, Lu91/a;->ic_tournament_place_seven:I

    .line 72
    .line 73
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 74
    .line 75
    .line 76
    move-result p1

    .line 77
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 82
    .line 83
    .line 84
    return-object p0

    .line 85
    :pswitch_4
    new-instance p0, Lmb1/a$b;

    .line 86
    .line 87
    sget p1, Lu91/a;->ic_tournament_place_six:I

    .line 88
    .line 89
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 90
    .line 91
    .line 92
    move-result p1

    .line 93
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 98
    .line 99
    .line 100
    return-object p0

    .line 101
    :pswitch_5
    new-instance p0, Lmb1/a$b;

    .line 102
    .line 103
    sget p1, Lu91/a;->ic_tournament_place_five:I

    .line 104
    .line 105
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 114
    .line 115
    .line 116
    return-object p0

    .line 117
    :pswitch_6
    new-instance p0, Lmb1/a$b;

    .line 118
    .line 119
    sget p1, Lu91/a;->ic_tournament_place_four:I

    .line 120
    .line 121
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 122
    .line 123
    .line 124
    move-result p1

    .line 125
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 130
    .line 131
    .line 132
    return-object p0

    .line 133
    :pswitch_7
    new-instance p0, Lmb1/a$b;

    .line 134
    .line 135
    sget p1, Lu91/a;->ic_tournament_place_three:I

    .line 136
    .line 137
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 138
    .line 139
    .line 140
    move-result p1

    .line 141
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 146
    .line 147
    .line 148
    return-object p0

    .line 149
    :pswitch_8
    new-instance p0, Lmb1/a$b;

    .line 150
    .line 151
    sget p1, Lu91/a;->ic_tournament_place_two:I

    .line 152
    .line 153
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 162
    .line 163
    .line 164
    return-object p0

    .line 165
    :pswitch_9
    new-instance p0, Lmb1/a$b;

    .line 166
    .line 167
    sget p1, Lu91/a;->ic_tournament_place_one:I

    .line 168
    .line 169
    invoke-static {p1}, LL11/c$c;->d(I)I

    .line 170
    .line 171
    .line 172
    move-result p1

    .line 173
    invoke-static {p1}, LL11/c$c;->c(I)LL11/c$c;

    .line 174
    .line 175
    .line 176
    move-result-object p1

    .line 177
    invoke-direct {p0, p1}, Lmb1/a$b;-><init>(LL11/c;)V

    .line 178
    .line 179
    .line 180
    return-object p0

    .line 181
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final b(Lm81/b;LHX0/e;)Lmb1/b;
    .locals 8
    .param p0    # Lm81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lmb1/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lm81/b;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-static {v1}, Lkotlin/text/StringsKt;->y(Ljava/lang/String;)Ljava/lang/Long;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 14
    .line 15
    .line 16
    move-result-wide v1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const-wide/16 v1, 0x0

    .line 19
    .line 20
    :goto_0
    sget v3, Lpb/k;->tournament_points:I

    .line 21
    .line 22
    invoke-virtual {p0}, Lm81/b;->d()I

    .line 23
    .line 24
    .line 25
    move-result v4

    .line 26
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    const/4 v5, 0x1

    .line 31
    new-array v5, v5, [Ljava/lang/Object;

    .line 32
    .line 33
    const/4 v6, 0x0

    .line 34
    aput-object v4, v5, v6

    .line 35
    .line 36
    invoke-interface {p1, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    invoke-virtual {p0}, Lm81/b;->a()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v4

    .line 44
    invoke-virtual {p0}, Lm81/b;->b()Z

    .line 45
    .line 46
    .line 47
    move-result v5

    .line 48
    invoke-virtual {p0}, Lm81/b;->c()I

    .line 49
    .line 50
    .line 51
    move-result p1

    .line 52
    invoke-virtual {p0}, Lm81/b;->b()Z

    .line 53
    .line 54
    .line 55
    move-result p0

    .line 56
    invoke-static {p1, p0}, Ljb1/C;->a(IZ)Lmb1/a;

    .line 57
    .line 58
    .line 59
    move-result-object v7

    .line 60
    invoke-direct/range {v0 .. v7}, Lmb1/b;-><init>(JLjava/lang/String;Ljava/lang/String;ZZLmb1/a;)V

    .line 61
    .line 62
    .line 63
    return-object v0
.end method
