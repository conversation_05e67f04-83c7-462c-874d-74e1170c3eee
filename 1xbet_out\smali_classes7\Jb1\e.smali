.class public final LJb1/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u00bf\u0001\u00101\u001a\u0002002\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010!\u001a\u00020 2\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$2\u0006\u0010\'\u001a\u00020&2\u0006\u0010)\u001a\u00020(2\u0006\u0010+\u001a\u00020*2\u0006\u0010-\u001a\u00020,2\u0006\u0010/\u001a\u00020.H\u0000\u00a2\u0006\u0004\u00081\u00102\u00a8\u00063"
    }
    d2 = {
        "LJb1/e;",
        "",
        "<init>",
        "()V",
        "LwX0/C;",
        "rootRouterHolder",
        "LfX/b;",
        "testRepository",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "popularClassicAggregatorDelegate",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lf8/g;",
        "serviceGenerator",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lcom/xbet/onexcore/utils/ext/c;",
        "iNetworkConnectionUtil",
        "Lak/a;",
        "balanceFeature",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "Li8/j;",
        "getServiceUseCase",
        "LHX0/e;",
        "resourceManager",
        "LTZ0/a;",
        "actionDialogManager",
        "LS8/a;",
        "profileLocalDataSource",
        "Lc81/a;",
        "aggregatorCoreFeature",
        "Lak/b;",
        "changeBalanceFeature",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "LzX0/k;",
        "snackbarManager",
        "Lau/a;",
        "countryInfoRepository",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LJb1/a;",
        "a",
        "(LwX0/C;LfX/b;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lm8/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexcore/utils/ext/c;Lak/a;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LTZ0/a;LS8/a;Lc81/a;Lak/b;LVg0/a;LzX0/k;Lau/a;Lp9/c;)LJb1/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(LwX0/C;LfX/b;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lm8/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexcore/utils/ext/c;Lak/a;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LTZ0/a;LS8/a;Lc81/a;Lak/b;LVg0/a;LzX0/k;Lau/a;Lp9/c;)LJb1/a;
    .locals 24
    .param p1    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lcom/xbet/onexcore/utils/ext/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LS8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lc81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lak/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lau/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LJb1/g;->a()LJb1/a$a;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    move-object/from16 v8, p1

    .line 6
    .line 7
    move-object/from16 v7, p2

    .line 8
    .line 9
    move-object/from16 v9, p3

    .line 10
    .line 11
    move-object/from16 v6, p4

    .line 12
    .line 13
    move-object/from16 v10, p5

    .line 14
    .line 15
    move-object/from16 v11, p6

    .line 16
    .line 17
    move-object/from16 v13, p7

    .line 18
    .line 19
    move-object/from16 v12, p8

    .line 20
    .line 21
    move-object/from16 v3, p9

    .line 22
    .line 23
    move-object/from16 v14, p10

    .line 24
    .line 25
    move-object/from16 v15, p11

    .line 26
    .line 27
    move-object/from16 v16, p12

    .line 28
    .line 29
    move-object/from16 v17, p13

    .line 30
    .line 31
    move-object/from16 v18, p14

    .line 32
    .line 33
    move-object/from16 v5, p15

    .line 34
    .line 35
    move-object/from16 v19, p16

    .line 36
    .line 37
    move-object/from16 v2, p17

    .line 38
    .line 39
    move-object/from16 v4, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    invoke-interface/range {v1 .. v23}, LJb1/a$a;->a(Lc81/a;Lak/a;Lak/b;LTZ0/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LfX/b;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;Lf8/g;Lcom/xbet/onexcore/utils/ext/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LS8/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;)LJb1/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    return-object v0
.end method
