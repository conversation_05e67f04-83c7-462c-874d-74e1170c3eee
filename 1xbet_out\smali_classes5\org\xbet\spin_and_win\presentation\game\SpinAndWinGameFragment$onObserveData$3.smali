.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameFragment$onObserveData$3"
    f = "SpinAndWinGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/spin_and_win/presentation/game/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/a;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/spin_and_win/presentation/game/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;

    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->invoke(Lorg/xbet/spin_and_win/presentation/game/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/spin_and_win/presentation/game/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/a$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/a$a;->b()Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/a$a;->c()D

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/a$a;->a()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->K2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;ZDLjava/lang/String;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/a$c;

    .line 40
    .line 41
    if-eqz v0, :cond_1

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 44
    .line 45
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/a$c;

    .line 46
    .line 47
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/a$c;->a()Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->P2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ljava/util/List;)V

    .line 52
    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_1
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 56
    .line 57
    if-eqz v0, :cond_2

    .line 58
    .line 59
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 60
    .line 61
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/a$b;

    .line 62
    .line 63
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/a$b;->a()Z

    .line 64
    .line 65
    .line 66
    move-result p1

    .line 67
    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->D2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V

    .line 68
    .line 69
    .line 70
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object p1

    .line 73
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 74
    .line 75
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 76
    .line 77
    .line 78
    throw p1

    .line 79
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 80
    .line 81
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 82
    .line 83
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw p1
.end method
