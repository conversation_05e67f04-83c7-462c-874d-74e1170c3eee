.class public final Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\n\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0016\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\u000cH\u0082@\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0014R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;",
        "",
        "Lw30/r;",
        "getGpResultUseCase",
        "Lm8/a;",
        "dispatchers",
        "Li8/j;",
        "getServiceUseCase",
        "Lak/a;",
        "balanceFeature",
        "<init>",
        "(Lw30/r;Lm8/a;Li8/j;Lak/a;)V",
        "",
        "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
        "c",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "Lw30/r;",
        "b",
        "Lm8/a;",
        "Li8/j;",
        "d",
        "Lak/a;",
        "popular_classic_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lw30/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lw30/r;Lm8/a;Li8/j;Lak/a;)V
    .locals 0
    .param p1    # Lw30/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->a:Lw30/r;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->b:Lm8/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->c:Li8/j;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->d:Lak/a;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;)Lak/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->d:Lak/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final c(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->c:Li8/j;

    .line 2
    .line 3
    invoke-interface {v0}, Li8/j;->invoke()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/b;->a:Lw30/r;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-interface {v1, v2, v2, v0, p1}, Lw30/r;->a(ZILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    return-object p1
.end method
