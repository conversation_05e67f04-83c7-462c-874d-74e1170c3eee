.class public final LSF0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LSF0/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LSF0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LSF0/a$b$a;,
        LSF0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LSF0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LPF0/b;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/horses_race_menu/data/repository/HorsesMenuRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVF0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/HorsesRaceMenuViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/HorseRaceMenuViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LSF0/a$b;->a:LSF0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p14}, LSF0/a$b;->c(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LSF0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p14}, LSF0/a$b;-><init>(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LSF0/a$b;->e(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;)Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LSF0/a$b;->d(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;)Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)V
    .locals 9

    .line 1
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iput-object v0, p0, LSF0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, LSF0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance v0, LSF0/a$b$a;

    .line 14
    .line 15
    invoke-direct {v0, p1}, LSF0/a$b$a;-><init>(LQW0/c;)V

    .line 16
    .line 17
    .line 18
    iput-object v0, p0, LSF0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LSF0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p1}, LPF0/c;->a(LBc/a;)LPF0/c;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LSF0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LSF0/a$b;->g:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object v0, p0, LSF0/a$b;->d:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object v1, p0, LSF0/a$b;->f:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/horses_race_menu/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/horses_race_menu/data/repository/a;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LSF0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p1}, LVF0/b;->a(LBc/a;)LVF0/b;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LSF0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LSF0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LSF0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, LSF0/a$b;->l:Ldagger/internal/h;

    .line 71
    .line 72
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LSF0/a$b;->m:Ldagger/internal/h;

    .line 77
    .line 78
    new-instance p1, LSF0/a$b$b;

    .line 79
    .line 80
    invoke-direct {p1, p2}, LSF0/a$b$b;-><init>(LEN0/f;)V

    .line 81
    .line 82
    .line 83
    iput-object p1, p0, LSF0/a$b;->n:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    iput-object p1, p0, LSF0/a$b;->o:Ldagger/internal/h;

    .line 90
    .line 91
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 92
    .line 93
    .line 94
    move-result-object v8

    .line 95
    iput-object v8, p0, LSF0/a$b;->p:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object v0, p0, LSF0/a$b;->b:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object v1, p0, LSF0/a$b;->c:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object v2, p0, LSF0/a$b;->i:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v3, p0, LSF0/a$b;->j:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v4, p0, LSF0/a$b;->k:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v5, p0, LSF0/a$b;->l:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v6, p0, LSF0/a$b;->m:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v7, p0, LSF0/a$b;->o:Ldagger/internal/h;

    .line 112
    .line 113
    invoke-static/range {v0 .. v8}, Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/k;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/k;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    iput-object p1, p0, LSF0/a$b;->q:Ldagger/internal/h;

    .line 118
    .line 119
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    iput-object p1, p0, LSF0/a$b;->r:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LSF0/a$b;->s:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object p1, p0, LSF0/a$b;->n:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/b;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/b;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, LSF0/a$b;->t:Ldagger/internal/h;

    .line 138
    .line 139
    iget-object p2, p0, LSF0/a$b;->b:Ldagger/internal/h;

    .line 140
    .line 141
    iget-object p3, p0, LSF0/a$b;->c:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object v0, p0, LSF0/a$b;->i:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object v1, p0, LSF0/a$b;->j:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object v2, p0, LSF0/a$b;->k:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object v3, p0, LSF0/a$b;->r:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object v4, p0, LSF0/a$b;->s:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v5, p0, LSF0/a$b;->m:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object v6, p0, LSF0/a$b;->o:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object v7, p0, LSF0/a$b;->d:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v8, p0, LSF0/a$b;->p:Ldagger/internal/h;

    .line 160
    .line 161
    move-object/from16 p11, p1

    .line 162
    .line 163
    move-object p4, v0

    .line 164
    move-object p5, v1

    .line 165
    move-object p6, v2

    .line 166
    move-object/from16 p7, v3

    .line 167
    .line 168
    move-object/from16 p8, v4

    .line 169
    .line 170
    move-object/from16 p9, v5

    .line 171
    .line 172
    move-object/from16 p10, v6

    .line 173
    .line 174
    move-object/from16 p12, v7

    .line 175
    .line 176
    move-object/from16 p13, v8

    .line 177
    .line 178
    invoke-static/range {p2 .. p13}, Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/j;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/j;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    iput-object p1, p0, LSF0/a$b;->u:Ldagger/internal/h;

    .line 183
    .line 184
    return-void
.end method

.method public final d(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;)Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LSF0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/horses_race_menu/presentation/fragment/c;->a(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorseRaceMenuFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;)Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LSF0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/horses_race_menu/presentation/fragment/g;->a(Lorg/xbet/statistic/horses_race_menu/presentation/fragment/HorsesRaceMenuFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/HorsesRaceMenuViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LSF0/a$b;->q:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/horses_race_menu/presentation/viewmodel/HorseRaceMenuViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LSF0/a$b;->u:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LSF0/a$b;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
