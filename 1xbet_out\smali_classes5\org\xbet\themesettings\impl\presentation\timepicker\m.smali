.class public final synthetic Lorg/xbet/themesettings/impl/presentation/timepicker/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/m;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/m;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;

    invoke-static {v0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;->R2(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomSheet;Landroid/view/View;)V

    return-void
.end method
