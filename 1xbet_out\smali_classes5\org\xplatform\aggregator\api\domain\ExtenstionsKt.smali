.class public final Lorg/xplatform/aggregator/api/domain/ExtenstionsKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001aD\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00028\u00010\u0006\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0001*\u00028\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00010\u0004H\u0086H\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a,\u0010\r\u001a\u00028\u0000\"\u0004\u0008\u0000\u0010\u00002\u0006\u0010\n\u001a\u00020\t2\u000c\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u000bH\u0086H\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "T",
        "R",
        "",
        "isThrowClientErrorUp",
        "Lkotlin/Function1;",
        "block",
        "Lkotlin/Result;",
        "b",
        "(Ljava/lang/Object;ZLkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "delay",
        "Lkotlin/Function0;",
        "request",
        "a",
        "(JLkotlin/jvm/functions/Function0;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "api_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(JLkotlin/jvm/functions/Function0;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(J",
            "Lkotlin/jvm/functions/Function0<",
            "+TT;>;",
            "Lkotlin/coroutines/e<",
            "-TT;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;

    .line 21
    .line 22
    invoke-direct {v0, p3}, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;-><init>(Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-eq v2, v3, :cond_1

    .line 40
    .line 41
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 42
    .line 43
    const-string p1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 44
    .line 45
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 46
    .line 47
    .line 48
    throw p0

    .line 49
    :cond_1
    iget-object p0, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->L$0:Ljava/lang/Object;

    .line 50
    .line 51
    check-cast p0, Ljava/lang/Throwable;

    .line 52
    .line 53
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    goto :goto_3

    .line 57
    :cond_2
    iget-object p0, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_3
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 67
    .line 68
    .line 69
    move-result-wide v5

    .line 70
    :try_start_0
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 74
    invoke-static {v4}, Lkotlin/jvm/internal/r;->b(I)V

    .line 75
    .line 76
    .line 77
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 78
    .line 79
    .line 80
    move-result-wide v2

    .line 81
    sub-long/2addr v2, v5

    .line 82
    cmp-long p3, v2, p0

    .line 83
    .line 84
    if-gez p3, :cond_5

    .line 85
    .line 86
    sub-long/2addr p0, v2

    .line 87
    iput-object p2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->L$0:Ljava/lang/Object;

    .line 88
    .line 89
    iput v4, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->label:I

    .line 90
    .line 91
    invoke-static {p0, p1, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object p0

    .line 95
    if-ne p0, v1, :cond_4

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :cond_4
    move-object p0, p2

    .line 99
    :goto_1
    move-object p2, p0

    .line 100
    :cond_5
    invoke-static {v4}, Lkotlin/jvm/internal/r;->a(I)V

    .line 101
    .line 102
    .line 103
    return-object p2

    .line 104
    :catchall_0
    move-exception p2

    .line 105
    invoke-static {v4}, Lkotlin/jvm/internal/r;->b(I)V

    .line 106
    .line 107
    .line 108
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 109
    .line 110
    .line 111
    move-result-wide v7

    .line 112
    sub-long/2addr v7, v5

    .line 113
    cmp-long p3, v7, p0

    .line 114
    .line 115
    if-gez p3, :cond_7

    .line 116
    .line 117
    sub-long/2addr p0, v7

    .line 118
    iput-object p2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->L$0:Ljava/lang/Object;

    .line 119
    .line 120
    iput v3, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$requestWithMinDelay$1;->label:I

    .line 121
    .line 122
    invoke-static {p0, p1, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object p0

    .line 126
    if-ne p0, v1, :cond_6

    .line 127
    .line 128
    :goto_2
    return-object v1

    .line 129
    :cond_6
    move-object p0, p2

    .line 130
    :goto_3
    move-object p2, p0

    .line 131
    :cond_7
    invoke-static {v4}, Lkotlin/jvm/internal/r;->a(I)V

    .line 132
    .line 133
    .line 134
    throw p2
.end method

.method public static final b(Ljava/lang/Object;ZLkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            "R:",
            "Ljava/lang/Object;",
            ">(TT;Z",
            "Lkotlin/jvm/functions/Function1<",
            "-TT;+TR;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+TR;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;

    .line 21
    .line 22
    invoke-direct {v0, p3}, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;-><init>(Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_2

    .line 36
    .line 37
    if-ne v2, v4, :cond_1

    .line 38
    .line 39
    iget p0, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->I$0:I

    .line 40
    .line 41
    iget-boolean p1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->Z$0:Z

    .line 42
    .line 43
    iget-object p2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->L$1:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast p2, Lkotlin/jvm/functions/Function1;

    .line 46
    .line 47
    iget-object v2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    move p3, p0

    .line 53
    move-object p0, v2

    .line 54
    goto :goto_1

    .line 55
    :cond_1
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 56
    .line 57
    const-string p1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 58
    .line 59
    invoke-direct {p0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    throw p0

    .line 63
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    const/4 p3, 0x0

    .line 67
    :cond_3
    :goto_1
    :try_start_0
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 68
    .line 69
    invoke-interface {p2, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-static {v2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 77
    return-object p0

    .line 78
    :catchall_0
    move-exception v2

    .line 79
    if-eqz p1, :cond_4

    .line 80
    .line 81
    instance-of v5, v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 82
    .line 83
    if-eqz v5, :cond_4

    .line 84
    .line 85
    move-object v5, v2

    .line 86
    check-cast v5, Lcom/xbet/onexcore/data/model/ServerException;

    .line 87
    .line 88
    invoke-virtual {v5}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 89
    .line 90
    .line 91
    move-result v5

    .line 92
    if-eqz v5, :cond_4

    .line 93
    .line 94
    const/4 v5, 0x1

    .line 95
    goto :goto_2

    .line 96
    :cond_4
    const/4 v5, 0x0

    .line 97
    :goto_2
    instance-of v6, v2, Ljava/util/concurrent/CancellationException;

    .line 98
    .line 99
    if-nez v6, :cond_a

    .line 100
    .line 101
    instance-of v6, v2, Ljava/net/ConnectException;

    .line 102
    .line 103
    if-nez v6, :cond_a

    .line 104
    .line 105
    if-nez v5, :cond_a

    .line 106
    .line 107
    instance-of v5, v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 108
    .line 109
    if-eqz v5, :cond_7

    .line 110
    .line 111
    move-object v5, v2

    .line 112
    check-cast v5, Lcom/xbet/onexcore/data/model/ServerException;

    .line 113
    .line 114
    invoke-virtual {v5}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 115
    .line 116
    .line 117
    move-result v6

    .line 118
    if-nez v6, :cond_6

    .line 119
    .line 120
    invoke-virtual {v5}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 121
    .line 122
    .line 123
    move-result v5

    .line 124
    if-eqz v5, :cond_5

    .line 125
    .line 126
    goto :goto_3

    .line 127
    :cond_5
    const/4 v5, 0x0

    .line 128
    goto :goto_4

    .line 129
    :cond_6
    :goto_3
    const/4 v5, 0x1

    .line 130
    goto :goto_4

    .line 131
    :cond_7
    invoke-static {v2}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 132
    .line 133
    .line 134
    move-result v5

    .line 135
    if-nez v5, :cond_5

    .line 136
    .line 137
    goto :goto_3

    .line 138
    :goto_4
    add-int/2addr p3, v4

    .line 139
    const/4 v6, 0x3

    .line 140
    if-gt p3, v6, :cond_9

    .line 141
    .line 142
    if-eqz v5, :cond_8

    .line 143
    .line 144
    goto :goto_5

    .line 145
    :cond_8
    new-instance v5, Ljava/lang/StringBuilder;

    .line 146
    .line 147
    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    .line 148
    .line 149
    .line 150
    const-string v6, "error ("

    .line 151
    .line 152
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 153
    .line 154
    .line 155
    invoke-virtual {v5, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 156
    .line 157
    .line 158
    const-string v6, "): "

    .line 159
    .line 160
    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 161
    .line 162
    .line 163
    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 164
    .line 165
    .line 166
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object v2

    .line 170
    sget-object v5, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 171
    .line 172
    invoke-virtual {v5, v2}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 173
    .line 174
    .line 175
    iput-object p0, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->L$0:Ljava/lang/Object;

    .line 176
    .line 177
    iput-object p2, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->L$1:Ljava/lang/Object;

    .line 178
    .line 179
    iput-boolean p1, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->Z$0:Z

    .line 180
    .line 181
    iput p3, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->I$0:I

    .line 182
    .line 183
    iput v4, v0, Lorg/xplatform/aggregator/api/domain/ExtenstionsKt$runWithRetry$1;->label:I

    .line 184
    .line 185
    const-wide/16 v5, 0xbb8

    .line 186
    .line 187
    invoke-static {v5, v6, v0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 188
    .line 189
    .line 190
    move-result-object v2

    .line 191
    if-ne v2, v1, :cond_3

    .line 192
    .line 193
    return-object v1

    .line 194
    :cond_9
    :goto_5
    sget-object p0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 195
    .line 196
    invoke-static {v2}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 197
    .line 198
    .line 199
    move-result-object p0

    .line 200
    invoke-static {p0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object p0

    .line 204
    return-object p0

    .line 205
    :cond_a
    throw v2
.end method
