.class public final LRx0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010 \n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0000\n\u0002\u0008\u0003\u001a#\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00002\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "",
        "",
        "contentIds",
        "",
        "a",
        "(Ljava/util/List;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/List;)Ljava/util/List;
    .locals 3
    .param p0    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/16 v1, 0xa

    .line 4
    .line 5
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 10
    .line 11
    .line 12
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_0

    .line 21
    .line 22
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    check-cast v1, Ljava/lang/Number;

    .line 27
    .line 28
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    packed-switch v1, :pswitch_data_0

    .line 33
    .line 34
    .line 35
    sget-object v1, LZx0/j;->a:LZx0/j;

    .line 36
    .line 37
    goto/16 :goto_1

    .line 38
    .line 39
    :pswitch_0
    new-instance v1, LZx0/i$a$l;

    .line 40
    .line 41
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 42
    .line 43
    invoke-direct {v1, v2}, LZx0/i$a$l;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 44
    .line 45
    .line 46
    goto/16 :goto_1

    .line 47
    .line 48
    :pswitch_1
    new-instance v1, LZx0/i$a$j;

    .line 49
    .line 50
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 51
    .line 52
    invoke-direct {v1, v2}, LZx0/i$a$j;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 53
    .line 54
    .line 55
    goto/16 :goto_1

    .line 56
    .line 57
    :pswitch_2
    new-instance v1, LZx0/i$a$g;

    .line 58
    .line 59
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 60
    .line 61
    invoke-direct {v1, v2}, LZx0/i$a$g;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 62
    .line 63
    .line 64
    goto/16 :goto_1

    .line 65
    .line 66
    :pswitch_3
    new-instance v1, LZx0/i$a$e;

    .line 67
    .line 68
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 69
    .line 70
    invoke-direct {v1, v2}, LZx0/i$a$e;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 71
    .line 72
    .line 73
    goto/16 :goto_1

    .line 74
    .line 75
    :pswitch_4
    new-instance v1, LZx0/i$a$h;

    .line 76
    .line 77
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 78
    .line 79
    invoke-direct {v1, v2}, LZx0/i$a$h;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 80
    .line 81
    .line 82
    goto/16 :goto_1

    .line 83
    .line 84
    :pswitch_5
    new-instance v1, LZx0/i$a$d;

    .line 85
    .line 86
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 87
    .line 88
    invoke-direct {v1, v2}, LZx0/i$a$d;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 89
    .line 90
    .line 91
    goto/16 :goto_1

    .line 92
    .line 93
    :pswitch_6
    new-instance v1, LZx0/i$a$b;

    .line 94
    .line 95
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 96
    .line 97
    invoke-direct {v1, v2}, LZx0/i$a$b;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 98
    .line 99
    .line 100
    goto :goto_1

    .line 101
    :pswitch_7
    new-instance v1, LZx0/i$a$a;

    .line 102
    .line 103
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 104
    .line 105
    invoke-direct {v1, v2}, LZx0/i$a$a;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 106
    .line 107
    .line 108
    goto :goto_1

    .line 109
    :pswitch_8
    new-instance v1, LZx0/i$a$c;

    .line 110
    .line 111
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 112
    .line 113
    invoke-direct {v1, v2}, LZx0/i$a$c;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 114
    .line 115
    .line 116
    goto :goto_1

    .line 117
    :pswitch_9
    new-instance v1, LZx0/i$a$p;

    .line 118
    .line 119
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 120
    .line 121
    invoke-direct {v1, v2}, LZx0/i$a$p;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 122
    .line 123
    .line 124
    goto :goto_1

    .line 125
    :pswitch_a
    new-instance v1, LZx0/i$a$k;

    .line 126
    .line 127
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 128
    .line 129
    invoke-direct {v1, v2}, LZx0/i$a$k;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 130
    .line 131
    .line 132
    goto :goto_1

    .line 133
    :pswitch_b
    new-instance v1, LZx0/i$a$n;

    .line 134
    .line 135
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 136
    .line 137
    invoke-direct {v1, v2}, LZx0/i$a$n;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 138
    .line 139
    .line 140
    goto :goto_1

    .line 141
    :pswitch_c
    new-instance v1, LZx0/i$b$a;

    .line 142
    .line 143
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 144
    .line 145
    invoke-direct {v1, v2}, LZx0/i$b$a;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 146
    .line 147
    .line 148
    goto :goto_1

    .line 149
    :pswitch_d
    new-instance v1, LZx0/i$a$f;

    .line 150
    .line 151
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 152
    .line 153
    invoke-direct {v1, v2}, LZx0/i$a$f;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 154
    .line 155
    .line 156
    goto :goto_1

    .line 157
    :pswitch_e
    new-instance v1, LZx0/i$a$i;

    .line 158
    .line 159
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 160
    .line 161
    invoke-direct {v1, v2}, LZx0/i$a$i;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 162
    .line 163
    .line 164
    goto :goto_1

    .line 165
    :pswitch_f
    new-instance v1, LZx0/i$a$m;

    .line 166
    .line 167
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 168
    .line 169
    invoke-direct {v1, v2}, LZx0/i$a$m;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 170
    .line 171
    .line 172
    goto :goto_1

    .line 173
    :pswitch_10
    new-instance v1, LZx0/i$a$o;

    .line 174
    .line 175
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 176
    .line 177
    invoke-direct {v1, v2}, LZx0/i$a$o;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 178
    .line 179
    .line 180
    goto :goto_1

    .line 181
    :pswitch_11
    new-instance v1, LZx0/i$b$c;

    .line 182
    .line 183
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 184
    .line 185
    invoke-direct {v1, v2}, LZx0/i$b$c;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 186
    .line 187
    .line 188
    goto :goto_1

    .line 189
    :pswitch_12
    new-instance v1, LZx0/i$b$b;

    .line 190
    .line 191
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;->ACTIVE:Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;

    .line 192
    .line 193
    invoke-direct {v1, v2}, LZx0/i$b$b;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/model/TournamentSectionStateModel$Status;)V

    .line 194
    .line 195
    .line 196
    :goto_1
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 197
    .line 198
    .line 199
    goto/16 :goto_0

    .line 200
    .line 201
    :cond_0
    return-object v0

    .line 202
    nop

    .line 203
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
