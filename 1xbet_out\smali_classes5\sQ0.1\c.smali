.class public final LsQ0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LtQ0/d;",
        "Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b;",
        "a",
        "(LtQ0/d;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LtQ0/d;)Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b;
    .locals 4
    .param p0    # LtQ0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtQ0/d;->h()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    sget-object p0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$c;->a:Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$c;

    .line 14
    .line 15
    return-object p0

    .line 16
    :cond_0
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, LnQ0/b;->c()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-eqz v0, :cond_1

    .line 35
    .line 36
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$a;

    .line 37
    .line 38
    invoke-virtual {p0}, LtQ0/d;->d()Lorg/xbet/uikit/components/lottie/a;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-direct {v0, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 43
    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_1
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    if-eqz v0, :cond_2

    .line 51
    .line 52
    invoke-virtual {p0}, LtQ0/d;->i()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    if-nez v0, :cond_2

    .line 57
    .line 58
    invoke-virtual {p0}, LtQ0/d;->g()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    if-lez v0, :cond_2

    .line 67
    .line 68
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$d;

    .line 69
    .line 70
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {p0}, LtQ0/d;->g()Ljava/lang/String;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    invoke-static {v1, v2}, LsQ0/e;->b(LnQ0/b;Ljava/lang/String;)LtQ0/c;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    invoke-virtual {p0}, LtQ0/d;->d()Lorg/xbet/uikit/components/lottie/a;

    .line 83
    .line 84
    .line 85
    move-result-object p0

    .line 86
    invoke-direct {v0, v1, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$d;-><init>(LtQ0/c;Lorg/xbet/uikit/components/lottie/a;)V

    .line 87
    .line 88
    .line 89
    return-object v0

    .line 90
    :cond_2
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    if-eqz v0, :cond_4

    .line 95
    .line 96
    invoke-virtual {p0}, LtQ0/d;->i()Z

    .line 97
    .line 98
    .line 99
    move-result v0

    .line 100
    if-eqz v0, :cond_4

    .line 101
    .line 102
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    invoke-virtual {p0}, LtQ0/d;->c()I

    .line 107
    .line 108
    .line 109
    move-result v1

    .line 110
    invoke-static {v0, v1}, LsQ0/e;->a(LnQ0/b;I)LtQ0/c;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-virtual {p0}, LtQ0/d;->f()LnQ0/b;

    .line 115
    .line 116
    .line 117
    move-result-object v1

    .line 118
    invoke-virtual {v1}, LnQ0/b;->c()Ljava/util/List;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    new-instance v2, Ljava/util/ArrayList;

    .line 123
    .line 124
    const/16 v3, 0xa

    .line 125
    .line 126
    invoke-static {v1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 127
    .line 128
    .line 129
    move-result v3

    .line 130
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 131
    .line 132
    .line 133
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 134
    .line 135
    .line 136
    move-result-object v1

    .line 137
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 138
    .line 139
    .line 140
    move-result v3

    .line 141
    if-eqz v3, :cond_3

    .line 142
    .line 143
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 144
    .line 145
    .line 146
    move-result-object v3

    .line 147
    check-cast v3, LND0/k;

    .line 148
    .line 149
    invoke-virtual {v3}, LND0/k;->f()Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object v3

    .line 153
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 154
    .line 155
    .line 156
    goto :goto_0

    .line 157
    :cond_3
    invoke-virtual {p0}, LtQ0/d;->c()I

    .line 158
    .line 159
    .line 160
    move-result v1

    .line 161
    invoke-virtual {p0}, LtQ0/d;->d()Lorg/xbet/uikit/components/lottie/a;

    .line 162
    .line 163
    .line 164
    move-result-object p0

    .line 165
    new-instance v3, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$e;

    .line 166
    .line 167
    invoke-direct {v3, v0, v2, v1, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$e;-><init>(LtQ0/c;Ljava/util/List;ILorg/xbet/uikit/components/lottie/a;)V

    .line 168
    .line 169
    .line 170
    return-object v3

    .line 171
    :cond_4
    new-instance v0, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$b;

    .line 172
    .line 173
    invoke-virtual {p0}, LtQ0/d;->e()Lorg/xbet/uikit/components/lottie/a;

    .line 174
    .line 175
    .line 176
    move-result-object p0

    .line 177
    invoke-direct {v0, p0}, Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel$b$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 178
    .line 179
    .line 180
    return-object v0
.end method
