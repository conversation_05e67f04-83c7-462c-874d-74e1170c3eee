.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderViewModel$handleGamesStreamStates$1$1"
    f = "NewGamesFolderViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "Lkotlin/coroutines/e<",
        "-",
        "LN21/d;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "LN21/d;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/api/model/Game;)LN21/d;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $favoriteIds:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/util/Set;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->$favoriteIds:Ljava/util/Set;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->$favoriteIds:Ljava/util/Set;

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/util/Set;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->invoke(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Lkotlin/coroutines/e<",
            "-",
            "LN21/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v0, p1

    .line 14
    check-cast v0, Lorg/xplatform/aggregator/api/model/Game;

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 17
    .line 18
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->y4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Ljava/util/Map;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-interface {p1, v1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 34
    .line 35
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->I4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)LHX0/e;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 40
    .line 41
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->C4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lp9/c;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    invoke-virtual {p1}, Lp9/c;->a()Z

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 50
    .line 51
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->J4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Z

    .line 52
    .line 53
    .line 54
    move-result v3

    .line 55
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->$favoriteIds:Ljava/util/Set;

    .line 56
    .line 57
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 58
    .line 59
    .line 60
    move-result-wide v4

    .line 61
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    invoke-interface {p1, v4}, Ljava/util/Set;->contains(Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v4

    .line 69
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 70
    .line 71
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->G4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;)Lek0/o;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    invoke-virtual {p1}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    const/16 v7, 0x20

    .line 80
    .line 81
    const/4 v8, 0x0

    .line 82
    const/4 v6, 0x0

    .line 83
    invoke-static/range {v0 .. v8}, LQ91/c;->b(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;ILjava/lang/Object;)LN21/k;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    return-object p1

    .line 88
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 89
    .line 90
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 91
    .line 92
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    throw p1
.end method
