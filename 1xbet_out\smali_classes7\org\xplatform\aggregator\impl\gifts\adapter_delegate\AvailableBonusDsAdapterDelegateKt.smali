.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001aW\u0010\u000e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\r0\u000c0\u000b2\"\u0010\u0006\u001a\u001e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0000j\u0002`\u00052\u0016\u0010\n\u001a\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00040\u0007j\u0002`\tH\u0000\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a/\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00102\u0016\u0010\n\u001a\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00040\u0007j\u0002`\tH\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013\u001a;\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0015\u001a\u00020\u00142\"\u0010\u0006\u001a\u001e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0000j\u0002`\u0005H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lkotlin/Function3;",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
        "Lha1/b;",
        "",
        "Lorg/xplatform/aggregator/impl/gifts/adapter/StateCallback;",
        "stateCallback",
        "Lkotlin/Function1;",
        "",
        "Lorg/xplatform/aggregator/impl/gifts/adapter/RemoveCallback;",
        "removeCallback",
        "LA4/c;",
        "",
        "LVX0/i;",
        "j",
        "(LOc/n;Lkotlin/jvm/functions/Function1;)LA4/c;",
        "Lma1/d;",
        "item",
        "u",
        "(Lma1/d;Lkotlin/jvm/functions/Function1;)V",
        "Lma1/a;",
        "aggregatorClickUiModel",
        "t",
        "(Lma1/a;LOc/n;)V",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Lkotlin/jvm/functions/Function1;LOc/n;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->m(LB4/a;Lkotlin/jvm/functions/Function1;LOc/n;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->r(LB4/a;LOc/n;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->l(Lkotlin/jvm/functions/Function1;LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->n(LB4/a;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->o(LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->p(LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->s(LB4/a;LOc/n;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->q(LB4/a;LOc/n;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final j(LOc/n;Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 3
    .param p0    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Lorg/xplatform/aggregator/api/model/PartitionType;",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
            "-",
            "Lha1/b;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/d;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/d;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/e;

    .line 7
    .line 8
    invoke-direct {v1, p1, p0}, Lba1/e;-><init>(Lkotlin/jvm/functions/Function1;LOc/n;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt$getAvailableBonusDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt$getAvailableBonusDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt$getAvailableBonusDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt$getAvailableBonusDsAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final k(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/n1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/n1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/n1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final l(Lkotlin/jvm/functions/Function1;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lba1/f;

    .line 2
    .line 3
    invoke-direct {v0, p2, p0, p1}, Lba1/f;-><init>(LB4/a;Lkotlin/jvm/functions/Function1;LOc/n;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p2, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final m(LB4/a;Lkotlin/jvm/functions/Function1;LOc/n;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, LS91/n1;

    .line 6
    .line 7
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lma1/d;

    .line 16
    .line 17
    invoke-virtual {v0}, Lma1/d;->j()Lf21/b;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Lf21/b;->g()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p3, v0}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setStyle(Lorg/xbet/uikit_aggregator/aggregatorGiftCard/model/AggregatorGiftCardStyleEnum;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 29
    .line 30
    .line 31
    move-result-object p3

    .line 32
    check-cast p3, LS91/n1;

    .line 33
    .line 34
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 35
    .line 36
    .line 37
    move-result-object p3

    .line 38
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    check-cast v0, Lma1/d;

    .line 43
    .line 44
    invoke-virtual {v0}, Lma1/d;->j()Lf21/b;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    check-cast v1, Lma1/d;

    .line 53
    .line 54
    invoke-virtual {v1}, Lma1/d;->f()Lf21/a;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {p3, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setModel(Lf21/b;Lf21/a;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 62
    .line 63
    .line 64
    move-result-object p3

    .line 65
    check-cast p3, LS91/n1;

    .line 66
    .line 67
    invoke-virtual {p3}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 68
    .line 69
    .line 70
    move-result-object p3

    .line 71
    new-instance v0, Lba1/g;

    .line 72
    .line 73
    invoke-direct {v0, p0, p1}, Lba1/g;-><init>(LB4/a;Lkotlin/jvm/functions/Function1;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p3, v0}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setStopTimerSubject(Lkotlin/jvm/functions/Function0;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    check-cast p1, LS91/n1;

    .line 84
    .line 85
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    new-instance p3, Lba1/h;

    .line 90
    .line 91
    invoke-direct {p3, p2, p0}, Lba1/h;-><init>(LOc/n;LB4/a;)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setTopTagListener(Lkotlin/jvm/functions/Function0;)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    check-cast p1, LS91/n1;

    .line 102
    .line 103
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    new-instance p3, Lba1/i;

    .line 108
    .line 109
    invoke-direct {p3, p2, p0}, Lba1/i;-><init>(LOc/n;LB4/a;)V

    .line 110
    .line 111
    .line 112
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setBottomTagListener(Lkotlin/jvm/functions/Function0;)V

    .line 113
    .line 114
    .line 115
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    check-cast p1, LS91/n1;

    .line 120
    .line 121
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    new-instance p3, Lba1/j;

    .line 126
    .line 127
    invoke-direct {p3, p0, p2}, Lba1/j;-><init>(LB4/a;LOc/n;)V

    .line 128
    .line 129
    .line 130
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setCloseButtonListener(Lkotlin/jvm/functions/Function0;)V

    .line 131
    .line 132
    .line 133
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    check-cast p1, LS91/n1;

    .line 138
    .line 139
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    new-instance p3, Lba1/k;

    .line 144
    .line 145
    invoke-direct {p3, p0, p2}, Lba1/k;-><init>(LB4/a;LOc/n;)V

    .line 146
    .line 147
    .line 148
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setTopButtonListener(Lkotlin/jvm/functions/Function0;)V

    .line 149
    .line 150
    .line 151
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    check-cast p1, LS91/n1;

    .line 156
    .line 157
    invoke-virtual {p1}, LS91/n1;->b()Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    new-instance p3, Lba1/l;

    .line 162
    .line 163
    invoke-direct {p3, p0, p2}, Lba1/l;-><init>(LB4/a;LOc/n;)V

    .line 164
    .line 165
    .line 166
    invoke-virtual {p1, p3}, Lorg/xbet/uikit_aggregator/aggregatorGiftCard/DsAggregatorGiftCard;->setBottomButtonListener(Lkotlin/jvm/functions/Function0;)V

    .line 167
    .line 168
    .line 169
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 170
    .line 171
    return-object p0
.end method

.method public static final n(LB4/a;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lma1/d;

    .line 6
    .line 7
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->u(Lma1/d;Lkotlin/jvm/functions/Function1;)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final o(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lma1/d;

    .line 6
    .line 7
    invoke-virtual {v0}, Lma1/d;->A()Lma1/g;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lma1/g;->b()Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    check-cast v1, Lma1/d;

    .line 20
    .line 21
    invoke-virtual {v1}, Lma1/d;->A()Lma1/g;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Lma1/g;->c()Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    check-cast p1, Lma1/d;

    .line 34
    .line 35
    invoke-virtual {p1}, Lma1/d;->A()Lma1/g;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-virtual {p1}, Lma1/g;->a()Lha1/b;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-interface {p0, v0, v1, p1}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p0
.end method

.method public static final p(LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lma1/d;

    .line 6
    .line 7
    invoke-virtual {v0}, Lma1/d;->e()Lma1/g;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lma1/g;->b()Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    check-cast v1, Lma1/d;

    .line 20
    .line 21
    invoke-virtual {v1}, Lma1/d;->e()Lma1/g;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1}, Lma1/g;->c()Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    check-cast p1, Lma1/d;

    .line 34
    .line 35
    invoke-virtual {p1}, Lma1/d;->e()Lma1/g;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-virtual {p1}, Lma1/g;->a()Lha1/b;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-interface {p0, v0, v1, p1}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p0
.end method

.method public static final q(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 7

    .line 1
    new-instance v0, Lma1/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    .line 4
    .line 5
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    check-cast p0, Lma1/d;

    .line 10
    .line 11
    invoke-virtual {p0}, Lma1/d;->getId()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    const/16 v5, 0x8

    .line 16
    .line 17
    const/4 v6, 0x0

    .line 18
    const-string v3, ""

    .line 19
    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-direct/range {v0 .. v6}, Lma1/a;-><init>(Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;ILjava/lang/String;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 22
    .line 23
    .line 24
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->t(Lma1/a;LOc/n;)V

    .line 25
    .line 26
    .line 27
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 28
    .line 29
    return-object p0
.end method

.method public static final r(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lma1/d;

    .line 6
    .line 7
    invoke-virtual {p0}, Lma1/d;->s()Lma1/a;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->t(Lma1/a;LOc/n;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final s(LB4/a;LOc/n;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lma1/d;

    .line 6
    .line 7
    invoke-virtual {p0}, Lma1/d;->d()Lma1/a;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/AvailableBonusDsAdapterDelegateKt;->t(Lma1/a;LOc/n;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final t(Lma1/a;LOc/n;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lma1/a;",
            "LOc/n<",
            "-",
            "Lorg/xplatform/aggregator/api/model/PartitionType;",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
            "-",
            "Lha1/b;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    invoke-virtual {p0}, Lma1/a;->d()Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lha1/b;

    .line 8
    .line 9
    invoke-virtual {p0}, Lma1/a;->b()I

    .line 10
    .line 11
    .line 12
    move-result v3

    .line 13
    invoke-virtual {p0}, Lma1/a;->c()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {p0}, Lma1/a;->a()Ljava/lang/Integer;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    invoke-direct {v2, v3, v4, p0}, Lha1/b;-><init>(ILjava/lang/String;Ljava/lang/Integer;)V

    .line 22
    .line 23
    .line 24
    invoke-interface {p1, v0, v1, v2}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public static final u(Lma1/d;Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lma1/d;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lma1/d;->o()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->AWAITING_BY_OPERATOR:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 6
    .line 7
    if-eq v0, v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lma1/d;->getId()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    :cond_0
    return-void
.end method
