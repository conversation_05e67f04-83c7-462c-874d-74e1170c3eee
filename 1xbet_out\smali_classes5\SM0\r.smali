.class public final LSM0/r;
.super LeZ0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LSM0/r$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 \u00152\u00020\u0001:\u0001\u0016B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001f\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0015\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0017"
    }
    d2 = {
        "LSM0/r;",
        "LeZ0/a;",
        "LpM0/q;",
        "binding",
        "<init>",
        "(LpM0/q;)V",
        "Landroid/widget/TextView;",
        "textView",
        "Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;",
        "gameStatusUiModel",
        "",
        "j",
        "(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V",
        "LRM0/e;",
        "item",
        "i",
        "(LRM0/e;)V",
        "h",
        "()V",
        "e",
        "LpM0/q;",
        "f",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LSM0/r$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final e:LpM0/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LSM0/r$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LSM0/r$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LSM0/r;->f:LSM0/r$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(LpM0/q;)V
    .locals 1
    .param p1    # LpM0/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LpM0/q;->b()Landroid/widget/FrameLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, LeZ0/a;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LSM0/r;->e:LpM0/q;

    .line 9
    .line 10
    return-void
.end method

.method private final j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V
    .locals 3

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p2}, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;->getTextId()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getText(I)Ljava/lang/CharSequence;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {p2}, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;->getColorRes()I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    const/4 v1, 0x0

    .line 29
    const/4 v2, 0x2

    .line 30
    invoke-static {v0, p2, v1, v2, v1}, Lorg/xbet/uikit/utils/i;->f(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 31
    .line 32
    .line 33
    move-result p2

    .line 34
    invoke-static {p2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 35
    .line 36
    .line 37
    move-result-object p2

    .line 38
    invoke-virtual {p1, p2}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 39
    .line 40
    .line 41
    return-void
.end method


# virtual methods
.method public final h()V
    .locals 8

    .line 1
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 2
    .line 3
    iget-object v1, v0, LpM0/q;->g:Landroid/widget/TextView;

    .line 4
    .line 5
    iget-object v2, v0, LpM0/q;->i:Landroid/widget/TextView;

    .line 6
    .line 7
    iget-object v3, v0, LpM0/q;->k:Landroid/widget/TextView;

    .line 8
    .line 9
    iget-object v4, v0, LpM0/q;->h:Landroid/widget/TextView;

    .line 10
    .line 11
    iget-object v5, v0, LpM0/q;->f:Landroid/widget/TextView;

    .line 12
    .line 13
    iget-object v0, v0, LpM0/q;->j:Landroid/widget/TextView;

    .line 14
    .line 15
    const/4 v6, 0x6

    .line 16
    new-array v6, v6, [Landroid/widget/TextView;

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    aput-object v1, v6, v7

    .line 20
    .line 21
    const/4 v1, 0x1

    .line 22
    aput-object v2, v6, v1

    .line 23
    .line 24
    const/4 v1, 0x2

    .line 25
    aput-object v3, v6, v1

    .line 26
    .line 27
    const/4 v1, 0x3

    .line 28
    aput-object v4, v6, v1

    .line 29
    .line 30
    const/4 v1, 0x4

    .line 31
    aput-object v5, v6, v1

    .line 32
    .line 33
    const/4 v1, 0x5

    .line 34
    aput-object v0, v6, v1

    .line 35
    .line 36
    invoke-static {v6}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    if-eqz v1, :cond_0

    .line 49
    .line 50
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    check-cast v1, Landroid/widget/TextView;

    .line 55
    .line 56
    invoke-static {v1}, Lorg/xbet/ui_common/utils/w0;->c(Landroid/widget/TextView;)V

    .line 57
    .line 58
    .line 59
    const/4 v2, 0x0

    .line 60
    invoke-virtual {v1, v2}, Landroid/view/View;->setBackgroundTintList(Landroid/content/res/ColorStateList;)V

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_0
    return-void
.end method

.method public final i(LRM0/e;)V
    .locals 5
    .param p1    # LRM0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LRM0/e$d;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-eqz v0, :cond_7

    .line 7
    .line 8
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 9
    .line 10
    iget-object v3, v0, LpM0/q;->d:Landroid/widget/LinearLayout;

    .line 11
    .line 12
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    iget-object v3, v0, LpM0/q;->b:Landroid/widget/TextView;

    .line 16
    .line 17
    invoke-virtual {v3, v1}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, LSM0/r;->h()V

    .line 21
    .line 22
    .line 23
    check-cast p1, LRM0/e$d;

    .line 24
    .line 25
    invoke-virtual {p1}, LRM0/e$d;->e()Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_8

    .line 38
    .line 39
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    add-int/lit8 v3, v2, 0x1

    .line 44
    .line 45
    if-gez v2, :cond_0

    .line 46
    .line 47
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 48
    .line 49
    .line 50
    :cond_0
    check-cast v1, Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;

    .line 51
    .line 52
    if-eqz v2, :cond_6

    .line 53
    .line 54
    const/4 v4, 0x1

    .line 55
    if-eq v2, v4, :cond_5

    .line 56
    .line 57
    const/4 v4, 0x2

    .line 58
    if-eq v2, v4, :cond_4

    .line 59
    .line 60
    const/4 v4, 0x3

    .line 61
    if-eq v2, v4, :cond_3

    .line 62
    .line 63
    const/4 v4, 0x4

    .line 64
    if-eq v2, v4, :cond_2

    .line 65
    .line 66
    const/4 v4, 0x5

    .line 67
    if-eq v2, v4, :cond_1

    .line 68
    .line 69
    goto :goto_1

    .line 70
    :cond_1
    iget-object v2, v0, LpM0/q;->j:Landroid/widget/TextView;

    .line 71
    .line 72
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 73
    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_2
    iget-object v2, v0, LpM0/q;->f:Landroid/widget/TextView;

    .line 77
    .line 78
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 79
    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_3
    iget-object v2, v0, LpM0/q;->h:Landroid/widget/TextView;

    .line 83
    .line 84
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 85
    .line 86
    .line 87
    goto :goto_1

    .line 88
    :cond_4
    iget-object v2, v0, LpM0/q;->k:Landroid/widget/TextView;

    .line 89
    .line 90
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 91
    .line 92
    .line 93
    goto :goto_1

    .line 94
    :cond_5
    iget-object v2, v0, LpM0/q;->i:Landroid/widget/TextView;

    .line 95
    .line 96
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 97
    .line 98
    .line 99
    goto :goto_1

    .line 100
    :cond_6
    iget-object v2, v0, LpM0/q;->g:Landroid/widget/TextView;

    .line 101
    .line 102
    invoke-direct {p0, v2, v1}, LSM0/r;->j(Landroid/widget/TextView;Lorg/xbet/statistic/statistic_core/presentation/models/GameStatusUiModel;)V

    .line 103
    .line 104
    .line 105
    :goto_1
    move v2, v3

    .line 106
    goto :goto_0

    .line 107
    :cond_7
    instance-of v0, p1, LRM0/e$f;

    .line 108
    .line 109
    if-eqz v0, :cond_9

    .line 110
    .line 111
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 112
    .line 113
    iget-object v0, v0, LpM0/q;->d:Landroid/widget/LinearLayout;

    .line 114
    .line 115
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 116
    .line 117
    .line 118
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 119
    .line 120
    iget-object v0, v0, LpM0/q;->b:Landroid/widget/TextView;

    .line 121
    .line 122
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 123
    .line 124
    .line 125
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 126
    .line 127
    iget-object v0, v0, LpM0/q;->b:Landroid/widget/TextView;

    .line 128
    .line 129
    check-cast p1, LRM0/e$f;

    .line 130
    .line 131
    invoke-virtual {p1}, LRM0/e$f;->e()Ljava/lang/String;

    .line 132
    .line 133
    .line 134
    move-result-object v1

    .line 135
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 136
    .line 137
    .line 138
    invoke-virtual {p1}, LRM0/e$f;->f()I

    .line 139
    .line 140
    .line 141
    move-result v0

    .line 142
    if-eqz v0, :cond_8

    .line 143
    .line 144
    iget-object v0, p0, LSM0/r;->e:LpM0/q;

    .line 145
    .line 146
    iget-object v0, v0, LpM0/q;->b:Landroid/widget/TextView;

    .line 147
    .line 148
    invoke-virtual {v0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    invoke-virtual {p1}, LRM0/e$f;->f()I

    .line 153
    .line 154
    .line 155
    move-result p1

    .line 156
    invoke-static {v1, p1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 157
    .line 158
    .line 159
    move-result p1

    .line 160
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 161
    .line 162
    .line 163
    :cond_8
    return-void

    .line 164
    :cond_9
    invoke-virtual {p0}, LSM0/r;->h()V

    .line 165
    .line 166
    .line 167
    iget-object p1, p0, LSM0/r;->e:LpM0/q;

    .line 168
    .line 169
    iget-object p1, p1, LpM0/q;->b:Landroid/widget/TextView;

    .line 170
    .line 171
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 172
    .line 173
    .line 174
    return-void
.end method
