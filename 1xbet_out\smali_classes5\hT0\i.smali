.class public final LhT0/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0007\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LhT0/i;",
        "",
        "LRf0/o;",
        "settingsPrefsRepository",
        "<init>",
        "(LRf0/o;)V",
        "LfT0/b;",
        "a",
        "()LfT0/b;",
        "LRf0/o;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LRf0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LRf0/o;)V
    .locals 0
    .param p1    # LRf0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LhT0/i;->a:LRf0/o;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()LfT0/b;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LfT0/b;

    .line 2
    .line 3
    iget-object v1, p0, LhT0/i;->a:LRf0/o;

    .line 4
    .line 5
    invoke-interface {v1}, LRf0/o;->u()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    iget-object v2, p0, LhT0/i;->a:LRf0/o;

    .line 10
    .line 11
    invoke-interface {v2}, LRf0/o;->v()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    iget-object v3, p0, LhT0/i;->a:LRf0/o;

    .line 16
    .line 17
    invoke-interface {v3}, LRf0/o;->s()I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    iget-object v4, p0, LhT0/i;->a:LRf0/o;

    .line 22
    .line 23
    invoke-interface {v4}, LRf0/o;->d()I

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    invoke-direct {v0, v1, v2, v3, v4}, LfT0/b;-><init>(IIII)V

    .line 28
    .line 29
    .line 30
    return-object v0
.end method
