.class public final Ljb1/v;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/v$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u001ac\u0010\u0013\u001a\u00020\u0012*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0018\u0010\u000b\u001a\u0014\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u0008\u0012\u0004\u0012\u00020\n0\u00072\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u00082\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0000\u00a2\u0006\u0004\u0008\u0013\u0010\u0014\u001a-\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u001a\u001d\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a+\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u00002\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001c\u001a%\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001e\u001a3\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u00082\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008 \u0010!\u001a3\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u000c\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00082\u0006\u0010\"\u001a\u00020\u00102\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008#\u0010$\u001a\u001d\u0010%\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008%\u0010\u001a\u001a5\u0010&\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00082\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008&\u0010\'\u001a\'\u0010)\u001a\u00020(2\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008)\u0010*\u001a\u0017\u0010,\u001a\u00020+2\u0006\u0010\u0015\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008,\u0010-\u001a\u001d\u00100\u001a\u00020/2\u000c\u0010.\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0008H\u0002\u00a2\u0006\u0004\u00080\u00101\u001a1\u00108\u001a\u0002072\u0006\u00102\u001a\u00020\u00012\u0006\u00103\u001a\u00020\u00102\u0006\u00105\u001a\u0002042\u0008\u0008\u0001\u00106\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u00088\u00109\u001a\u001f\u0010:\u001a\u00020\u00162\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008:\u0010;\u001a\u001f\u0010<\u001a\u00020\u00162\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008<\u0010;\u001a%\u0010>\u001a\u00020=2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u00082\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008>\u0010?\u001a\u001d\u0010A\u001a\u00020@2\u000c\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008H\u0002\u00a2\u0006\u0004\u0008A\u0010B\u001a\u0017\u0010D\u001a\u00020C2\u0006\u0010\u0015\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008D\u0010E\u001a/\u0010G\u001a\u00020F2\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008G\u0010H\u001a\u001f\u0010J\u001a\u00020\u00012\u0006\u0010I\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008J\u0010K\u00a8\u0006L"
    }
    d2 = {
        "Li81/a;",
        "",
        "currencySymbol",
        "LHX0/e;",
        "resourceManager",
        "Lkb1/m;",
        "topGames",
        "Lkotlin/Pair;",
        "",
        "Lkb1/D;",
        "",
        "stages",
        "Lkb1/z;",
        "prizeUIModels",
        "Lek0/o;",
        "remoteConfigModel",
        "",
        "aggregatorAltDesignEnable",
        "Lkb1/n;",
        "t",
        "(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;Lkotlin/Pair;Ljava/util/List;Lek0/o;Z)Lkb1/n;",
        "tournament",
        "LVX0/i;",
        "b",
        "(Li81/a;LHX0/e;Ljava/lang/String;)Ljava/util/List;",
        "n",
        "(Li81/a;)Ljava/util/List;",
        "d",
        "(Li81/a;Ljava/util/List;)Ljava/util/List;",
        "e",
        "(Li81/a;LHX0/e;)Ljava/util/List;",
        "LN21/k;",
        "q",
        "(Ljava/util/List;LHX0/e;Lek0/o;)Ljava/util/List;",
        "showAllVisible",
        "l",
        "(Ljava/util/List;ZLHX0/e;)Ljava/util/List;",
        "j",
        "g",
        "(Li81/a;LHX0/e;Lek0/o;Z)Ljava/util/List;",
        "Llb1/a;",
        "a",
        "(Li81/a;LHX0/e;Ljava/lang/String;)Llb1/a;",
        "Llb1/n;",
        "m",
        "(Li81/a;)Llb1/n;",
        "prizes",
        "Llb1/d;",
        "c",
        "(Ljava/util/List;)Llb1/d;",
        "title",
        "showAll",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;",
        "type",
        "bottomPadding",
        "Llb1/q;",
        "o",
        "(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;",
        "h",
        "(Li81/a;LHX0/e;)LVX0/i;",
        "r",
        "Llb1/s;",
        "p",
        "(Ljava/util/List;Lek0/o;)Llb1/s;",
        "Llb1/l;",
        "k",
        "(Ljava/util/List;)Llb1/l;",
        "Llb1/j;",
        "i",
        "(Li81/a;)Llb1/j;",
        "Llb1/h;",
        "f",
        "(Li81/a;Lek0/o;LHX0/e;Z)Llb1/h;",
        "gamesSize",
        "s",
        "(ILHX0/e;)Ljava/lang/String;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Li81/a;LHX0/e;Ljava/lang/String;)Llb1/a;
    .locals 11

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lj81/a;->h()J

    .line 8
    .line 9
    .line 10
    move-result-wide v1

    .line 11
    const/4 v4, 0x2

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    invoke-static/range {v0 .. v5}, Ll8/b;->p0(Ll8/b;JZILjava/lang/Object;)Ljava/util/Date;

    .line 15
    .line 16
    .line 17
    move-result-object v6

    .line 18
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v1}, Lj81/a;->c()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static/range {v0 .. v5}, Ll8/b;->p0(Ll8/b;JZILjava/lang/Object;)Ljava/util/Date;

    .line 27
    .line 28
    .line 29
    move-result-object v7

    .line 30
    new-instance v1, Ljava/util/Date;

    .line 31
    .line 32
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v0, v1, v6, v7}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    const-string v8, " - "

    .line 40
    .line 41
    const/4 v9, 0x0

    .line 42
    if-nez v1, :cond_1

    .line 43
    .line 44
    new-instance v1, Ljava/util/Date;

    .line 45
    .line 46
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v1, v6}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 50
    .line 51
    .line 52
    move-result v1

    .line 53
    if-eqz v1, :cond_0

    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_0
    sget v1, Lpb/k;->end_of_tournament:I

    .line 57
    .line 58
    new-array v2, v9, [Ljava/lang/Object;

    .line 59
    .line 60
    invoke-interface {p1, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    move-object v10, v6

    .line 65
    :goto_0
    move-object v6, v1

    .line 66
    goto :goto_2

    .line 67
    :cond_1
    :goto_1
    const/4 v4, 0x4

    .line 68
    const/4 v5, 0x0

    .line 69
    const-string v2, "dd MMMM yyyy HH:mm"

    .line 70
    .line 71
    const/4 v3, 0x0

    .line 72
    move-object v1, v6

    .line 73
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v6

    .line 77
    move-object v10, v1

    .line 78
    const-string v2, "dd MMMM yyyy HH:mm"

    .line 79
    .line 80
    move-object v1, v7

    .line 81
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    new-instance v1, Ljava/lang/StringBuilder;

    .line 86
    .line 87
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 97
    .line 98
    .line 99
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    goto :goto_0

    .line 104
    :goto_2
    new-instance v1, Ljava/util/Date;

    .line 105
    .line 106
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 107
    .line 108
    .line 109
    invoke-virtual {v0, v1, v10, v7}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 110
    .line 111
    .line 112
    move-result v1

    .line 113
    if-nez v1, :cond_3

    .line 114
    .line 115
    new-instance v1, Ljava/util/Date;

    .line 116
    .line 117
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 118
    .line 119
    .line 120
    invoke-virtual {v1, v10}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 121
    .line 122
    .line 123
    move-result v1

    .line 124
    if-eqz v1, :cond_2

    .line 125
    .line 126
    goto :goto_3

    .line 127
    :cond_2
    sget v0, Lpb/k;->end_of_tournament:I

    .line 128
    .line 129
    new-array v1, v9, [Ljava/lang/Object;

    .line 130
    .line 131
    invoke-interface {p1, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    goto :goto_4

    .line 136
    :cond_3
    :goto_3
    const/4 v4, 0x4

    .line 137
    const/4 v5, 0x0

    .line 138
    const-string v2, "dd MMMM yyyy (hh:mm a)"

    .line 139
    .line 140
    const/4 v3, 0x0

    .line 141
    move-object v1, v10

    .line 142
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    const-string v2, "dd MMMM yyyy (hh:mm a)"

    .line 147
    .line 148
    move-object v1, v7

    .line 149
    invoke-static/range {v0 .. v5}, Ll8/b;->h(Ll8/b;Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object v0

    .line 153
    new-instance v1, Ljava/lang/StringBuilder;

    .line 154
    .line 155
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 156
    .line 157
    .line 158
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 159
    .line 160
    .line 161
    invoke-virtual {v1, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 162
    .line 163
    .line 164
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 165
    .line 166
    .line 167
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    :goto_4
    sget-object v0, Ll8/j;->a:Ll8/j;

    .line 172
    .line 173
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 174
    .line 175
    .line 176
    move-result-object v1

    .line 177
    invoke-virtual {v1}, Lj81/a;->j()J

    .line 178
    .line 179
    .line 180
    move-result-wide v1

    .line 181
    long-to-double v1, v1

    .line 182
    sget-object v3, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 183
    .line 184
    invoke-virtual {v0, v1, v2, v3}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 185
    .line 186
    .line 187
    move-result-object v0

    .line 188
    new-instance v1, Ljava/lang/StringBuilder;

    .line 189
    .line 190
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 191
    .line 192
    .line 193
    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 194
    .line 195
    .line 196
    const-string p2, " "

    .line 197
    .line 198
    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 199
    .line 200
    .line 201
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 202
    .line 203
    .line 204
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 205
    .line 206
    .line 207
    move-result-object p2

    .line 208
    new-instance v0, Llb1/a;

    .line 209
    .line 210
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 211
    .line 212
    .line 213
    move-result-object p0

    .line 214
    invoke-virtual {p0}, Lj81/a;->g()Ljava/lang/String;

    .line 215
    .line 216
    .line 217
    move-result-object p0

    .line 218
    invoke-direct {v0, p0, p2, v6, p1}, Llb1/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 219
    .line 220
    .line 221
    return-object v0
.end method

.method public static final b(Li81/a;LHX0/e;Ljava/lang/String;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {p0, p1, p2}, Ljb1/v;->a(Li81/a;LHX0/e;Ljava/lang/String;)Llb1/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-static {p0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static final c(Ljava/util/List;)Llb1/d;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/z;",
            ">;)",
            "Llb1/d;"
        }
    .end annotation

    .line 1
    new-instance v0, Llb1/d;

    .line 2
    .line 3
    const/4 v1, 0x5

    .line 4
    invoke-static {p0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 5
    .line 6
    .line 7
    move-result-object p0

    .line 8
    invoke-direct {v0, p0}, Llb1/d;-><init>(Ljava/util/List;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final d(Li81/a;Ljava/util/List;)Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Ljava/util/List<",
            "+",
            "Lkb1/z;",
            ">;)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-virtual {v2}, Lk81/a;->c()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-nez v2, :cond_2

    .line 12
    .line 13
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-virtual {v2}, Lk81/a;->d()Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    if-nez v2, :cond_2

    .line 26
    .line 27
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    if-nez v2, :cond_1

    .line 32
    .line 33
    invoke-virtual {p0}, Li81/a;->e()Lk81/a;

    .line 34
    .line 35
    .line 36
    move-result-object p0

    .line 37
    invoke-virtual {p0}, Lk81/a;->e()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object p0

    .line 41
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 42
    .line 43
    .line 44
    move-result v2

    .line 45
    const/4 v3, 0x5

    .line 46
    if-le v2, v3, :cond_0

    .line 47
    .line 48
    const/4 v2, 0x1

    .line 49
    goto :goto_0

    .line 50
    :cond_0
    const/4 v2, 0x0

    .line 51
    :goto_0
    sget-object v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PRIZE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 52
    .line 53
    const/16 v4, 0x8

    .line 54
    .line 55
    invoke-static {p0, v2, v3, v4}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    invoke-static {p1}, Ljb1/v;->c(Ljava/util/List;)Llb1/d;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    const/4 v2, 0x2

    .line 64
    new-array v2, v2, [LVX0/i;

    .line 65
    .line 66
    aput-object p0, v2, v0

    .line 67
    .line 68
    aput-object p1, v2, v1

    .line 69
    .line 70
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    return-object p0

    .line 75
    :cond_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    return-object p0

    .line 80
    :cond_2
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object p0

    .line 84
    return-object p0
.end method

.method public static final e(Li81/a;LHX0/e;)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x0

    .line 3
    const/4 v2, 0x1

    .line 4
    invoke-virtual {p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 5
    .line 6
    .line 7
    move-result-object v3

    .line 8
    sget-object v4, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 9
    .line 10
    if-ne v3, v4, :cond_5

    .line 11
    .line 12
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    invoke-virtual {v3}, Lj81/a;->i()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    sget-object v4, Ljb1/v$a;->a:[I

    .line 21
    .line 22
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 23
    .line 24
    .line 25
    move-result v3

    .line 26
    aget v3, v4, v3

    .line 27
    .line 28
    const/16 v4, 0x8

    .line 29
    .line 30
    if-eq v3, v2, :cond_3

    .line 31
    .line 32
    if-eq v3, v0, :cond_1

    .line 33
    .line 34
    const/4 v5, 0x3

    .line 35
    if-ne v3, v5, :cond_0

    .line 36
    .line 37
    sget v3, Lpb/k;->tournament_your_progress:I

    .line 38
    .line 39
    new-array v5, v1, [Ljava/lang/Object;

    .line 40
    .line 41
    invoke-interface {p1, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    sget-object v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 46
    .line 47
    invoke-static {v3, v1, v5, v4}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    invoke-static {p0, p1}, Ljb1/v;->h(Li81/a;LHX0/e;)LVX0/i;

    .line 52
    .line 53
    .line 54
    move-result-object p0

    .line 55
    new-array p1, v0, [LVX0/i;

    .line 56
    .line 57
    aput-object v3, p1, v1

    .line 58
    .line 59
    aput-object p0, p1, v2

    .line 60
    .line 61
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    return-object p0

    .line 66
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 67
    .line 68
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 69
    .line 70
    .line 71
    throw p0

    .line 72
    :cond_1
    sget v3, Lpb/k;->tournament_your_progress:I

    .line 73
    .line 74
    new-array v5, v1, [Ljava/lang/Object;

    .line 75
    .line 76
    invoke-interface {p1, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    sget-object v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 81
    .line 82
    invoke-static {v3, v1, v5, v4}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 83
    .line 84
    .line 85
    move-result-object v3

    .line 86
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 87
    .line 88
    .line 89
    move-result p0

    .line 90
    if-eqz p0, :cond_2

    .line 91
    .line 92
    new-instance p0, Lkb1/p;

    .line 93
    .line 94
    sget v4, Lpb/k;->you_participating_tournament:I

    .line 95
    .line 96
    new-array v5, v1, [Ljava/lang/Object;

    .line 97
    .line 98
    invoke-interface {p1, v4, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v4

    .line 102
    sget v5, Lpb/k;->tournament_participating_waiting_start:I

    .line 103
    .line 104
    new-array v6, v1, [Ljava/lang/Object;

    .line 105
    .line 106
    invoke-interface {p1, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    sget v5, Lpb/g;->ic_tournament_cup_gold_waiting:I

    .line 111
    .line 112
    invoke-direct {p0, v4, p1, v5}, Lkb1/p;-><init>(Ljava/lang/String;Ljava/lang/String;I)V

    .line 113
    .line 114
    .line 115
    goto :goto_0

    .line 116
    :cond_2
    new-instance p0, Lkb1/p;

    .line 117
    .line 118
    sget v4, Lpb/k;->tournament_participate:I

    .line 119
    .line 120
    new-array v5, v1, [Ljava/lang/Object;

    .line 121
    .line 122
    invoke-interface {p1, v4, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v4

    .line 126
    sget v5, Lpb/k;->tournament_compete_win:I

    .line 127
    .line 128
    new-array v6, v1, [Ljava/lang/Object;

    .line 129
    .line 130
    invoke-interface {p1, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    sget v5, Lpb/g;->ic_tournament_cup_gold:I

    .line 135
    .line 136
    invoke-direct {p0, v4, p1, v5}, Lkb1/p;-><init>(Ljava/lang/String;Ljava/lang/String;I)V

    .line 137
    .line 138
    .line 139
    :goto_0
    new-array p1, v0, [LVX0/i;

    .line 140
    .line 141
    aput-object v3, p1, v1

    .line 142
    .line 143
    aput-object p0, p1, v2

    .line 144
    .line 145
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 146
    .line 147
    .line 148
    move-result-object p0

    .line 149
    return-object p0

    .line 150
    :cond_3
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 151
    .line 152
    .line 153
    move-result v3

    .line 154
    if-eqz v3, :cond_4

    .line 155
    .line 156
    sget v3, Lpb/k;->tournament_your_progress:I

    .line 157
    .line 158
    new-array v5, v1, [Ljava/lang/Object;

    .line 159
    .line 160
    invoke-interface {p1, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 161
    .line 162
    .line 163
    move-result-object v3

    .line 164
    sget-object v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 165
    .line 166
    invoke-static {v3, v1, v5, v4}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 167
    .line 168
    .line 169
    move-result-object v3

    .line 170
    invoke-static {p0, p1}, Ljb1/v;->h(Li81/a;LHX0/e;)LVX0/i;

    .line 171
    .line 172
    .line 173
    move-result-object p0

    .line 174
    new-array p1, v0, [LVX0/i;

    .line 175
    .line 176
    aput-object v3, p1, v1

    .line 177
    .line 178
    aput-object p0, p1, v2

    .line 179
    .line 180
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 181
    .line 182
    .line 183
    move-result-object p0

    .line 184
    return-object p0

    .line 185
    :cond_4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 186
    .line 187
    .line 188
    move-result-object p0

    .line 189
    return-object p0

    .line 190
    :cond_5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 191
    .line 192
    .line 193
    move-result-object p0

    .line 194
    return-object p0
.end method

.method public static final f(Li81/a;Lek0/o;LHX0/e;Z)Llb1/h;
    .locals 16

    .line 1
    if-eqz p3, :cond_0

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->Companion:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;

    .line 4
    .line 5
    invoke-virtual/range {p1 .. p1}, Lek0/o;->w()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandS:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 15
    .line 16
    :goto_0
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;->Horizontal:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 17
    .line 18
    new-instance v2, LO21/b;

    .line 19
    .line 20
    invoke-direct {v2, v1, v0}, LO21/b;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual/range {p0 .. p0}, Li81/a;->q()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    const/16 v1, 0x8

    .line 28
    .line 29
    invoke-static {v0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    new-instance v1, Ljava/util/ArrayList;

    .line 34
    .line 35
    const/16 v3, 0xa

    .line 36
    .line 37
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    invoke-direct {v1, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 42
    .line 43
    .line 44
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 49
    .line 50
    .line 51
    move-result v3

    .line 52
    if-eqz v3, :cond_1

    .line 53
    .line 54
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    check-cast v3, Ll81/a;

    .line 59
    .line 60
    invoke-virtual {v3}, Ll81/a;->c()I

    .line 61
    .line 62
    .line 63
    move-result v4

    .line 64
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    invoke-virtual {v3}, Ll81/a;->a()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v4

    .line 72
    invoke-static {v4}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v4

    .line 76
    invoke-virtual {v3}, Ll81/a;->b()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v9

    .line 80
    sget v3, Lpb/k;->casino_providers_games:I

    .line 81
    .line 82
    const/4 v5, 0x0

    .line 83
    new-array v5, v5, [Ljava/lang/Object;

    .line 84
    .line 85
    move-object/from16 v7, p2

    .line 86
    .line 87
    invoke-interface {v7, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v11

    .line 91
    new-instance v5, LP21/c;

    .line 92
    .line 93
    invoke-static {v4}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    const/16 v14, 0xc0

    .line 98
    .line 99
    const/4 v15, 0x0

    .line 100
    const/4 v8, 0x0

    .line 101
    const-string v10, "-"

    .line 102
    .line 103
    const/4 v12, 0x0

    .line 104
    const/4 v13, 0x0

    .line 105
    move-object v7, v3

    .line 106
    invoke-direct/range {v5 .. v15}, LP21/c;-><init>(Ljava/lang/String;LL11/c;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LL11/c;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 107
    .line 108
    .line 109
    invoke-interface {v1, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    goto :goto_1

    .line 113
    :cond_1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$a;

    .line 114
    .line 115
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$a;-><init>(Ljava/util/List;LO21/b;)V

    .line 116
    .line 117
    .line 118
    new-instance v1, Llb1/h;

    .line 119
    .line 120
    invoke-direct {v1, v0}, Llb1/h;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 121
    .line 122
    .line 123
    return-object v1
.end method

.method public static final g(Li81/a;LHX0/e;Lek0/o;Z)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "LHX0/e;",
            "Lek0/o;",
            "Z)",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    invoke-virtual {p0}, Li81/a;->q()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-nez v2, :cond_1

    .line 12
    .line 13
    sget v2, Lpb/k;->providers:I

    .line 14
    .line 15
    new-array v3, v1, [Ljava/lang/Object;

    .line 16
    .line 17
    invoke-interface {p1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-virtual {p0}, Li81/a;->q()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 26
    .line 27
    .line 28
    move-result v3

    .line 29
    const/16 v4, 0x8

    .line 30
    .line 31
    if-le v3, v4, :cond_0

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    goto :goto_0

    .line 35
    :cond_0
    const/4 v3, 0x0

    .line 36
    :goto_0
    sget-object v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->PROVIDER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 37
    .line 38
    const/4 v5, 0x4

    .line 39
    invoke-static {v2, v3, v4, v5}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-static {p0, p2, p1, p3}, Ljb1/v;->f(Li81/a;Lek0/o;LHX0/e;Z)Llb1/h;

    .line 44
    .line 45
    .line 46
    move-result-object p0

    .line 47
    const/4 p1, 0x2

    .line 48
    new-array p1, p1, [LVX0/i;

    .line 49
    .line 50
    aput-object v2, p1, v1

    .line 51
    .line 52
    aput-object p0, p1, v0

    .line 53
    .line 54
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object p0

    .line 58
    return-object p0

    .line 59
    :cond_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 60
    .line 61
    .line 62
    move-result-object p0

    .line 63
    return-object p0
.end method

.method public static final h(Li81/a;LHX0/e;)LVX0/i;
    .locals 3

    .line 1
    invoke-virtual {p0}, Li81/a;->p()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {p0, p1}, Ljb1/v;->r(Li81/a;LHX0/e;)LVX0/i;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0

    .line 12
    :cond_0
    new-instance p0, Lkb1/p;

    .line 13
    .line 14
    sget v0, Lpb/k;->tournament_participate:I

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    new-array v2, v1, [Ljava/lang/Object;

    .line 18
    .line 19
    invoke-interface {p1, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 24
    .line 25
    new-array v1, v1, [Ljava/lang/Object;

    .line 26
    .line 27
    invoke-interface {p1, v2, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    sget v1, Lpb/g;->ic_tournament_cup_gold:I

    .line 32
    .line 33
    invoke-direct {p0, v0, p1, v1}, Lkb1/p;-><init>(Ljava/lang/String;Ljava/lang/String;I)V

    .line 34
    .line 35
    .line 36
    return-object p0
.end method

.method public static final i(Li81/a;)Llb1/j;
    .locals 5

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Li81/a;->g()Ln81/c;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    invoke-virtual {p0}, Ln81/c;->a()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    const/4 v2, 0x0

    .line 19
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-eqz v3, :cond_2

    .line 24
    .line 25
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    add-int/lit8 v4, v2, 0x1

    .line 30
    .line 31
    if-gez v2, :cond_0

    .line 32
    .line 33
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 34
    .line 35
    .line 36
    :cond_0
    check-cast v3, Ljava/lang/String;

    .line 37
    .line 38
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 39
    .line 40
    .line 41
    move-result v2

    .line 42
    if-eq v4, v2, :cond_1

    .line 43
    .line 44
    new-instance v2, Ljava/lang/StringBuilder;

    .line 45
    .line 46
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 50
    .line 51
    .line 52
    const-string v3, "\n"

    .line 53
    .line 54
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 55
    .line 56
    .line 57
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v2

    .line 61
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_1
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    :goto_1
    move v2, v4

    .line 69
    goto :goto_0

    .line 70
    :cond_2
    new-instance p0, Llb1/j;

    .line 71
    .line 72
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    invoke-direct {p0, v0}, Llb1/j;-><init>(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    return-object p0
.end method

.method public static final j(Li81/a;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0}, Li81/a;->g()Ln81/c;

    .line 3
    .line 4
    .line 5
    move-result-object v1

    .line 6
    invoke-virtual {v1}, Ln81/c;->a()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-nez v1, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, Li81/a;->g()Ln81/c;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, Ln81/c;->b()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    sget-object v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 25
    .line 26
    const/16 v3, 0x8

    .line 27
    .line 28
    invoke-static {v1, v0, v2, v3}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    invoke-static {p0}, Ljb1/v;->i(Li81/a;)Llb1/j;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    const/4 v2, 0x2

    .line 37
    new-array v2, v2, [LVX0/i;

    .line 38
    .line 39
    aput-object v1, v2, v0

    .line 40
    .line 41
    const/4 v0, 0x1

    .line 42
    aput-object p0, v2, v0

    .line 43
    .line 44
    invoke-static {v2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0

    .line 49
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object p0

    .line 53
    return-object p0
.end method

.method public static final k(Ljava/util/List;)Llb1/l;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lkb1/D;",
            ">;)",
            "Llb1/l;"
        }
    .end annotation

    .line 1
    new-instance v0, Llb1/l;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Llb1/l;-><init>(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final l(Ljava/util/List;ZLHX0/e;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lkb1/D;",
            ">;Z",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 3
    .line 4
    .line 5
    move-result v1

    .line 6
    if-nez v1, :cond_0

    .line 7
    .line 8
    sget v1, Lpb/k;->tournament_stages:I

    .line 9
    .line 10
    new-array v2, v0, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {p2, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 17
    .line 18
    const/16 v2, 0x8

    .line 19
    .line 20
    invoke-static {p2, p1, v1, v2}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-static {p0}, Ljb1/v;->k(Ljava/util/List;)Llb1/l;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    const/4 p2, 0x2

    .line 29
    new-array p2, p2, [LVX0/i;

    .line 30
    .line 31
    aput-object p1, p2, v0

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    aput-object p0, p2, p1

    .line 35
    .line 36
    invoke-static {p2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    return-object p0

    .line 41
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    return-object p0
.end method

.method public static final m(Li81/a;)Llb1/n;
    .locals 8

    .line 1
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lj81/a;->a()Lj81/b;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Lj81/b;->b()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Llb1/n;

    .line 14
    .line 15
    sget-object v2, Ll8/b;->a:Ll8/b;

    .line 16
    .line 17
    new-instance v3, Ljava/util/Date;

    .line 18
    .line 19
    invoke-direct {v3}, Ljava/util/Date;-><init>()V

    .line 20
    .line 21
    .line 22
    invoke-virtual {v3}, Ljava/util/Date;->getTime()J

    .line 23
    .line 24
    .line 25
    move-result-wide v3

    .line 26
    sget-object v5, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 27
    .line 28
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 29
    .line 30
    .line 31
    move-result-object p0

    .line 32
    invoke-virtual {p0}, Lj81/a;->a()Lj81/b;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    invoke-virtual {p0}, Lj81/b;->a()J

    .line 37
    .line 38
    .line 39
    move-result-wide v6

    .line 40
    invoke-virtual {v5, v6, v7}, Ljava/util/concurrent/TimeUnit;->toMillis(J)J

    .line 41
    .line 42
    .line 43
    move-result-wide v5

    .line 44
    add-long/2addr v3, v5

    .line 45
    const/4 p0, 0x0

    .line 46
    invoke-virtual {v2, v3, v4, p0}, Ll8/b;->o0(JZ)Ljava/util/Date;

    .line 47
    .line 48
    .line 49
    move-result-object p0

    .line 50
    invoke-direct {v1, v0, p0}, Llb1/n;-><init>(Ljava/lang/String;Ljava/util/Date;)V

    .line 51
    .line 52
    .line 53
    return-object v1
.end method

.method public static final n(Li81/a;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lj81/a;->a()Lj81/b;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Lj81/b;->c()Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;->STOPPED:Lorg/xplatform/aggregator/api/model/tournaments/header/CounterType;

    .line 14
    .line 15
    if-eq v0, v1, :cond_0

    .line 16
    .line 17
    invoke-static {p0}, Ljb1/v;->m(Li81/a;)Llb1/n;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    invoke-static {p0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    return-object p0

    .line 26
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p0

    .line 30
    return-object p0
.end method

.method public static final o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;
    .locals 1

    .line 1
    new-instance v0, Llb1/q;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2, p3}, Llb1/q;-><init>(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final p(Ljava/util/List;Lek0/o;)Llb1/s;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LN21/k;",
            ">;",
            "Lek0/o;",
            ")",
            "Llb1/s;"
        }
    .end annotation

    .line 1
    new-instance v0, Llb1/s;

    .line 2
    .line 3
    invoke-virtual {p1}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const/4 v1, 0x1

    .line 8
    invoke-static {p1, v1}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    const/16 v1, 0x8

    .line 13
    .line 14
    invoke-static {p0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    invoke-direct {v0, p1, p0}, Llb1/s;-><init>(ILjava/util/List;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method

.method public static final q(Ljava/util/List;LHX0/e;Lek0/o;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LN21/k;",
            ">;",
            "LHX0/e;",
            "Lek0/o;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-interface {p0}, Ljava/util/Collection;->isEmpty()Z

    .line 3
    .line 4
    .line 5
    move-result v1

    .line 6
    if-nez v1, :cond_0

    .line 7
    .line 8
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    invoke-static {v1, p1}, Ljb1/v;->s(ILHX0/e;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;->OTHER:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    .line 17
    .line 18
    const/16 v2, 0x8

    .line 19
    .line 20
    invoke-static {p1, v0, v1, v2}, Ljb1/v;->o(Ljava/lang/String;ZLorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;I)Llb1/q;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-static {p0, p2}, Ljb1/v;->p(Ljava/util/List;Lek0/o;)Llb1/s;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    const/4 p2, 0x2

    .line 29
    new-array p2, p2, [LVX0/i;

    .line 30
    .line 31
    aput-object p1, p2, v0

    .line 32
    .line 33
    const/4 p1, 0x1

    .line 34
    aput-object p0, p2, p1

    .line 35
    .line 36
    invoke-static {p2}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    return-object p0

    .line 41
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    return-object p0
.end method

.method public static final r(Li81/a;LHX0/e;)LVX0/i;
    .locals 3

    .line 1
    invoke-virtual {p0}, Li81/a;->f()Lm81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lm81/a;->a()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-eqz v1, :cond_1

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    move-object v2, v1

    .line 24
    check-cast v2, Lm81/b;

    .line 25
    .line 26
    invoke-virtual {v2}, Lm81/b;->b()Z

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    if-eqz v2, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v1, 0x0

    .line 34
    :goto_0
    check-cast v1, Lm81/b;

    .line 35
    .line 36
    if-nez v1, :cond_2

    .line 37
    .line 38
    new-instance p0, Lkb1/p;

    .line 39
    .line 40
    sget v0, Lpb/k;->you_participating_tournament:I

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    new-array v2, v1, [Ljava/lang/Object;

    .line 44
    .line 45
    invoke-interface {p1, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 50
    .line 51
    new-array v1, v1, [Ljava/lang/Object;

    .line 52
    .line 53
    invoke-interface {p1, v2, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    sget v1, Lpb/g;->ic_tournament_cup_gold:I

    .line 58
    .line 59
    invoke-direct {p0, v0, p1, v1}, Lkb1/p;-><init>(Ljava/lang/String;Ljava/lang/String;I)V

    .line 60
    .line 61
    .line 62
    return-object p0

    .line 63
    :cond_2
    invoke-static {p0}, Ljb1/y;->b(Li81/a;)Lkb1/s;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    return-object p0
.end method

.method public static final s(ILHX0/e;)Ljava/lang/String;
    .locals 2

    .line 1
    const/4 v0, 0x1

    .line 2
    const/4 v1, 0x0

    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->tournament_top_game:I

    .line 6
    .line 7
    new-array v0, v1, [Ljava/lang/Object;

    .line 8
    .line 9
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    return-object p0

    .line 14
    :cond_0
    sget p0, Lpb/k;->tournament_top_games:I

    .line 15
    .line 16
    new-array v0, v1, [Ljava/lang/Object;

    .line 17
    .line 18
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    return-object p0
.end method

.method public static final t(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;Lkotlin/Pair;Ljava/util/List;Lek0/o;Z)Lkb1/n;
    .locals 1
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkb1/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/Pair;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lek0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Ljava/lang/String;",
            "LHX0/e;",
            "Lkb1/m;",
            "Lkotlin/Pair<",
            "+",
            "Ljava/util/List<",
            "Lkb1/D;",
            ">;",
            "Ljava/lang/Integer;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lkb1/z;",
            ">;",
            "Lek0/o;",
            "Z)",
            "Lkb1/n;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0, p2, p1}, Ljb1/v;->b(Li81/a;LHX0/e;Ljava/lang/String;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 10
    .line 11
    .line 12
    invoke-static {p0}, Ljb1/v;->n(Li81/a;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 17
    .line 18
    .line 19
    invoke-static {p0, p2}, Ljb1/v;->e(Li81/a;LHX0/e;)Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    invoke-static {p0, p5}, Ljb1/v;->d(Li81/a;Ljava/util/List;)Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 31
    .line 32
    .line 33
    invoke-virtual {p3}, Lkb1/m;->b()Ljava/util/List;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-static {p1, p2, p6}, Ljb1/v;->q(Ljava/util/List;LHX0/e;Lek0/o;)Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 42
    .line 43
    .line 44
    invoke-virtual {p4}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    check-cast p1, Ljava/util/List;

    .line 49
    .line 50
    invoke-virtual {p4}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p3

    .line 54
    check-cast p3, Ljava/lang/Number;

    .line 55
    .line 56
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 57
    .line 58
    .line 59
    move-result p3

    .line 60
    const/4 p4, 0x3

    .line 61
    if-le p3, p4, :cond_0

    .line 62
    .line 63
    const/4 p3, 0x1

    .line 64
    goto :goto_0

    .line 65
    :cond_0
    const/4 p3, 0x0

    .line 66
    :goto_0
    invoke-static {p1, p3, p2}, Ljb1/v;->l(Ljava/util/List;ZLHX0/e;)Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 71
    .line 72
    .line 73
    invoke-static {p0}, Ljb1/v;->j(Li81/a;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 78
    .line 79
    .line 80
    invoke-static {p0, p2, p6, p7}, Ljb1/v;->g(Li81/a;LHX0/e;Lek0/o;Z)Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 85
    .line 86
    .line 87
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    invoke-virtual {p0}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 92
    .line 93
    .line 94
    move-result-object p2

    .line 95
    sget-object p3, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 96
    .line 97
    invoke-virtual {p0}, Li81/a;->j()Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;

    .line 98
    .line 99
    .line 100
    move-result-object p0

    .line 101
    invoke-static {p2, p3, p0}, Lh81/c;->a(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/api/model/tournaments/ButtonStatus;)Z

    .line 102
    .line 103
    .line 104
    move-result p0

    .line 105
    new-instance p2, Lkb1/n;

    .line 106
    .line 107
    invoke-direct {p2, p1, p0}, Lkb1/n;-><init>(Ljava/util/List;Z)V

    .line 108
    .line 109
    .line 110
    return-object p2
.end method
