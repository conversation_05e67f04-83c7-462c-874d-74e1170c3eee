.class public final Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 *2\u00020\u0001:\u0001\'B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000b\u001a\u00020\n2\u0008\u0008\u0001\u0010\t\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000b\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000f\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\n2\u0008\u0008\u0001\u0010\t\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u0017\u0010\u0010\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u0010\u0010\u000cJ\u0017\u0010\u0010\u001a\u00020\n2\u0008\u0008\u0001\u0010\t\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0010\u0010\u000eJ\u0015\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0012\u0010\u000eJ\u0015\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0013\u0010\u000eJ\u0015\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0015\u0010\u000eJ\u0017\u0010\u0017\u001a\u00020\n2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0017\u0010\u000eJ\u0017\u0010\u0018\u001a\u00020\n2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\r\u00a2\u0006\u0004\u0008\u0018\u0010\u000eJ\u001f\u0010\u001b\u001a\u00020\n2\u0006\u0010\u0019\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u000f\u0010\u001d\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010\u001f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u001eJ\'\u0010$\u001a\u00020\n2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\r2\u0006\u0010#\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010)\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(\u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "text",
        "",
        "setLeftTitle",
        "(Ljava/lang/CharSequence;)V",
        "",
        "(I)V",
        "setTitle",
        "setRightTitle",
        "progress",
        "setLeftIndicator",
        "setRightIndicator",
        "top",
        "u",
        "colorRes",
        "setLeftIndicatorColor",
        "setRightIndicatorColor",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "v",
        "()V",
        "s",
        "Landroid/widget/ProgressBar;",
        "indicator",
        "min",
        "max",
        "t",
        "(Landroid/widget/ProgressBar;II)V",
        "LC31/T;",
        "a",
        "LC31/T;",
        "binding",
        "b",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final b:Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final c:I


# instance fields
.field public final a:LC31/T;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->b:Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->c:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, LC31/T;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/T;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 5
    sget-object v0, Lm31/g;->StatisticsIndicator:[I

    const/4 v1, 0x0

    .line 6
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 7
    sget v0, Lm31/g;->StatisticsIndicator_leftTitle:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v0

    const/4 v2, 0x0

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_0

    :cond_0
    move-object v0, v2

    :goto_0
    const-string v3, ""

    if-nez v0, :cond_1

    move-object v0, v3

    .line 8
    :cond_1
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setLeftTitle(Ljava/lang/CharSequence;)V

    .line 9
    sget v0, Lm31/g;->StatisticsIndicator_title:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v0

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    goto :goto_1

    :cond_2
    move-object v0, v2

    :goto_1
    if-nez v0, :cond_3

    move-object v0, v3

    .line 10
    :cond_3
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setTitle(Ljava/lang/CharSequence;)V

    .line 11
    sget v0, Lm31/g;->StatisticsIndicator_rightTitle:I

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    :cond_4
    if-nez v2, :cond_5

    goto :goto_2

    :cond_5
    move-object v3, v2

    .line 12
    :goto_2
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setRightTitle(Ljava/lang/CharSequence;)V

    .line 13
    sget p1, Lm31/g;->StatisticsIndicator_leftProgress:I

    invoke-virtual {p2, p1, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p1

    .line 14
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setLeftIndicator(I)V

    .line 15
    sget p1, Lm31/g;->StatisticsIndicator_rightProgress:I

    invoke-virtual {p2, p1, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p1

    .line 16
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setRightIndicator(I)V

    .line 17
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method


# virtual methods
.method public onMeasure(II)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->v()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->s()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final s()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/g;->size_8:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 12
    .line 13
    iget-object v1, v1, LC31/T;->d:Landroid/widget/ProgressBar;

    .line 14
    .line 15
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    div-int/lit8 v1, v1, 0x64

    .line 20
    .line 21
    div-int/2addr v0, v1

    .line 22
    rsub-int/lit8 v1, v0, 0x64

    .line 23
    .line 24
    iget-object v2, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 25
    .line 26
    iget-object v2, v2, LC31/T;->d:Landroid/widget/ProgressBar;

    .line 27
    .line 28
    invoke-virtual {p0, v2, v0, v1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->t(Landroid/widget/ProgressBar;II)V

    .line 29
    .line 30
    .line 31
    iget-object v2, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 32
    .line 33
    iget-object v2, v2, LC31/T;->f:Landroid/widget/ProgressBar;

    .line 34
    .line 35
    invoke-virtual {p0, v2, v0, v1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->t(Landroid/widget/ProgressBar;II)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final setLeftIndicator(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 2
    .line 3
    iget-object v0, v0, LC31/T;->d:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setLeftIndicatorColor(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 2
    .line 3
    iget-object v0, v0, LC31/T;->d:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1, p1}, LF0/b;->getColorStateList(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setLeftTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setLeftTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setLeftTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    iget-object v0, v0, LC31/T;->e:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setRightIndicator(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 2
    .line 3
    iget-object v0, v0, LC31/T;->f:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setRightIndicatorColor(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 2
    .line 3
    iget-object v0, v0, LC31/T;->f:Landroid/widget/ProgressBar;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1, p1}, LF0/b;->getColorStateList(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {v0, p1}, Landroid/widget/ProgressBar;->setProgressTintList(Landroid/content/res/ColorStateList;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setRightTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setRightTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setRightTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    iget-object v0, v0, LC31/T;->g:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    iget-object v0, v0, LC31/T;->h:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final t(Landroid/widget/ProgressBar;II)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/widget/ProgressBar;->getProgress()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-gt v1, v0, :cond_0

    .line 7
    .line 8
    if-ge v0, p2, :cond_0

    .line 9
    .line 10
    invoke-virtual {p1, p2}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 11
    .line 12
    .line 13
    return-void

    .line 14
    :cond_0
    if-gt p3, v0, :cond_1

    .line 15
    .line 16
    const/16 p2, 0x64

    .line 17
    .line 18
    if-ge v0, p2, :cond_1

    .line 19
    .line 20
    invoke-virtual {p1, p3}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 21
    .line 22
    .line 23
    :cond_1
    return-void
.end method

.method public final u(I)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 4
    .line 5
    iget-object v2, v1, LC31/T;->d:Landroid/widget/ProgressBar;

    .line 6
    .line 7
    const/16 v7, 0xd

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    move/from16 v4, p1

    .line 14
    .line 15
    invoke-static/range {v2 .. v8}, Lorg/xbet/uikit/utils/S;->p(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    iget-object v1, v0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 19
    .line 20
    iget-object v9, v1, LC31/T;->f:Landroid/widget/ProgressBar;

    .line 21
    .line 22
    const/16 v14, 0xd

    .line 23
    .line 24
    const/4 v15, 0x0

    .line 25
    const/4 v10, 0x0

    .line 26
    const/4 v12, 0x0

    .line 27
    const/4 v13, 0x0

    .line 28
    move/from16 v11, p1

    .line 29
    .line 30
    invoke-static/range {v9 .. v15}, Lorg/xbet/uikit/utils/S;->p(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final v()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 2
    .line 3
    iget-object v0, v0, LC31/T;->e:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 10
    .line 11
    iget-object v1, v1, LC31/T;->g:Landroid/widget/TextView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    sget v2, LlZ0/g;->space_8:I

    .line 26
    .line 27
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    add-int/2addr v0, v1

    .line 32
    mul-int/lit8 v0, v0, 0x2

    .line 33
    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_sport/statisticsindicator/StatisticsIndicator;->a:LC31/T;

    .line 35
    .line 36
    iget-object v2, v1, LC31/T;->h:Landroid/widget/TextView;

    .line 37
    .line 38
    invoke-virtual {v1}, LC31/T;->getRoot()Landroid/view/View;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    sub-int/2addr v1, v0

    .line 47
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setWidth(I)V

    .line 48
    .line 49
    .line 50
    return-void
.end method
