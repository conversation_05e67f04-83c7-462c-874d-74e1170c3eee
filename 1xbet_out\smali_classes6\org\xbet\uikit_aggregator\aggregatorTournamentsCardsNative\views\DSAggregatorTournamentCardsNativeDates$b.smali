.class public final Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;->setTimerValue(Ljava/lang/Long;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function2<",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Ljava/lang/Long;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;


# direct methods
.method public constructor <init>(Ljava/lang/Long;Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->a:Ljava/lang/Long;

    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->c(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;->e(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final b(Landroidx/compose/runtime/j;I)V
    .locals 12

    .line 1
    and-int/lit8 v0, p2, 0x3

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    if-ne v0, v1, :cond_1

    .line 5
    .line 6
    invoke-interface {p1}, Landroidx/compose/runtime/j;->c()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-interface {p1}, Landroidx/compose/runtime/j;->n()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_2

    .line 22
    .line 23
    const/4 v0, -0x1

    .line 24
    const-string v1, "org.xbet.uikit_aggregator.aggregatorTournamentsCardsNative.views.DSAggregatorTournamentCardsNativeDates.setTimerValue.<anonymous> (DSAggregatorTournamentCardsNativeDates.kt:442)"

    .line 25
    .line 26
    const v2, 0x4d3fcc68    # 2.01115264E8f

    .line 27
    .line 28
    .line 29
    invoke-static {v2, p2, v0, v1}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 30
    .line 31
    .line 32
    :cond_2
    new-instance v3, Ljava/util/Date;

    .line 33
    .line 34
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->a:Ljava/lang/Long;

    .line 35
    .line 36
    invoke-virtual {p2}, Ljava/lang/Long;->longValue()J

    .line 37
    .line 38
    .line 39
    move-result-wide v0

    .line 40
    invoke-direct {v3, v0, v1}, Ljava/util/Date;-><init>(J)V

    .line 41
    .line 42
    .line 43
    new-instance v4, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/i;

    .line 44
    .line 45
    const/16 v9, 0xf

    .line 46
    .line 47
    const/4 v10, 0x0

    .line 48
    const/4 v5, 0x0

    .line 49
    const/4 v6, 0x0

    .line 50
    const/4 v7, 0x0

    .line 51
    const/4 v8, 0x0

    .line 52
    invoke-direct/range {v4 .. v10}, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/i;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 53
    .line 54
    .line 55
    sget-object v6, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/DsAggregatorSmallTimerStyle;->MEDIUM:Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/DsAggregatorSmallTimerStyle;

    .line 56
    .line 57
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/a;->e:Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/a$a;

    .line 58
    .line 59
    sget-object v0, Lw21/a;->a:Lw21/a;

    .line 60
    .line 61
    invoke-virtual {v0}, Lw21/a;->a()LOc/n;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    const/16 v1, 0x36

    .line 66
    .line 67
    invoke-virtual {p2, v0, p1, v1}, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/a$a;->a(LOc/n;Landroidx/compose/runtime/j;I)Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/a;

    .line 68
    .line 69
    .line 70
    move-result-object v7

    .line 71
    const p2, 0x4c5de2

    .line 72
    .line 73
    .line 74
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->t(I)V

    .line 75
    .line 76
    .line 77
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;

    .line 78
    .line 79
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 80
    .line 81
    .line 82
    move-result p2

    .line 83
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->b:Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;

    .line 84
    .line 85
    invoke-interface {p1}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    if-nez p2, :cond_3

    .line 90
    .line 91
    sget-object p2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 92
    .line 93
    invoke-virtual {p2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object p2

    .line 97
    if-ne v1, p2, :cond_4

    .line 98
    .line 99
    :cond_3
    new-instance v1, Lw21/i;

    .line 100
    .line 101
    invoke-direct {v1, v0}, Lw21/i;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates;)V

    .line 102
    .line 103
    .line 104
    invoke-interface {p1, v1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 105
    .line 106
    .line 107
    :cond_4
    move-object v8, v1

    .line 108
    check-cast v8, Lkotlin/jvm/functions/Function0;

    .line 109
    .line 110
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 111
    .line 112
    .line 113
    const/16 v10, 0xc00

    .line 114
    .line 115
    const/4 v11, 0x4

    .line 116
    const/4 v5, 0x0

    .line 117
    move-object v9, p1

    .line 118
    invoke-static/range {v3 .. v11}, Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/DsAggregatorSmallTimerKt;->n(Ljava/util/Date;Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/i;Landroidx/compose/ui/l;Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/DsAggregatorSmallTimerStyle;Lorg/xbet/uikit_aggregator/aggregatorsmalltimer/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 119
    .line 120
    .line 121
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 122
    .line 123
    .line 124
    move-result p1

    .line 125
    if-eqz p1, :cond_5

    .line 126
    .line 127
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 128
    .line 129
    .line 130
    :cond_5
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/runtime/j;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/views/DSAggregatorTournamentCardsNativeDates$b;->b(Landroidx/compose/runtime/j;I)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1
.end method
