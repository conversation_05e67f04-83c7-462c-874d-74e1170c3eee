.class final Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.delegate.WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2"
    f = "WhoWinCardViewModelDelegateImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->c3(ILjava/lang/Integer;JI)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betInfo:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

.field final synthetic $game:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;",
            "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
            "Lorg/xbet/betting/core/zip/model/bet/BetInfo;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$game:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$betInfo:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$game:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$betInfo:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$game:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 20
    .line 21
    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->$betInfo:Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 22
    .line 23
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onMarketClicked$1$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    .line 24
    .line 25
    invoke-static {v3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->n(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Le90/a;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    invoke-interface {v3}, Le90/a;->invoke()Z

    .line 30
    .line 31
    .line 32
    move-result v3

    .line 33
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$c;-><init>(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/zip/model/bet/BetInfo;Z)V

    .line 34
    .line 35
    .line 36
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 40
    .line 41
    return-object p1

    .line 42
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1
.end method
