.class public final Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\t\u0018\u0000 \u000c2\u00020\u0001:\u0001\u0018B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0016\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\nH\u0086B\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001e\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\n2\u0006\u0010\u000f\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0013\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0017\u001a\u00020\u00142\u0006\u0010\u0013\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0016R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u001c\u00a8\u0006\u001d"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;",
        "",
        "Li8/j;",
        "getServiceUseCase",
        "Lw30/q;",
        "getGpResultScenario",
        "Lw30/m;",
        "getGamesCategoriesScenario",
        "<init>",
        "(Li8/j;Lw30/q;Lw30/m;)V",
        "",
        "Lf50/a;",
        "d",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "categoryId",
        "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
        "c",
        "(ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "gpResult",
        "",
        "e",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z",
        "f",
        "a",
        "Li8/j;",
        "b",
        "Lw30/q;",
        "Lw30/m;",
        "popular_classic_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lw30/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lw30/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->d:Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$a;

    return-void
.end method

.method public constructor <init>(Li8/j;Lw30/q;Lw30/m;)V
    .locals 0
    .param p1    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lw30/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lw30/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->a:Li8/j;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->b:Lw30/q;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->c:Lw30/m;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->c(ILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;)Li8/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->a:Li8/j;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final c(ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v4, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v4, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v4, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 34
    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v2, 0x3

    .line 37
    const/4 v8, 0x2

    .line 38
    const/4 v3, 0x1

    .line 39
    if-eqz v1, :cond_4

    .line 40
    .line 41
    if-eq v1, v3, :cond_3

    .line 42
    .line 43
    if-eq v1, v8, :cond_2

    .line 44
    .line 45
    if-ne v1, v2, :cond_1

    .line 46
    .line 47
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    return-object p2

    .line 51
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 54
    .line 55
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 56
    .line 57
    .line 58
    throw p1

    .line 59
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    goto :goto_4

    .line 63
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    goto :goto_2

    .line 67
    :cond_4
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    sget-object p2, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->CENTER_OF_ATTENTION:Lorg/xbet/core/domain/GamesCategoryTypeEnum;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/core/domain/GamesCategoryTypeEnum;->getId()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    invoke-static {p2}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 77
    .line 78
    .line 79
    move-result p2

    .line 80
    if-ne p1, p2, :cond_e

    .line 81
    .line 82
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->b:Lw30/q;

    .line 83
    .line 84
    iput v3, v4, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 85
    .line 86
    const/4 v2, 0x0

    .line 87
    const/4 v5, 0x1

    .line 88
    const/4 v6, 0x0

    .line 89
    move v3, p1

    .line 90
    invoke-static/range {v1 .. v6}, Lw30/q$a;->a(Lw30/q;ZILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 91
    .line 92
    .line 93
    move-result-object p2

    .line 94
    if-ne p2, v0, :cond_5

    .line 95
    .line 96
    goto/16 :goto_6

    .line 97
    .line 98
    :cond_5
    :goto_2
    check-cast p2, Ljava/lang/Iterable;

    .line 99
    .line 100
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    :cond_6
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 105
    .line 106
    .line 107
    move-result p2

    .line 108
    if-eqz p2, :cond_7

    .line 109
    .line 110
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object p2

    .line 114
    move-object v1, p2

    .line 115
    check-cast v1, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 116
    .line 117
    invoke-virtual {p0, v1}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->f(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z

    .line 118
    .line 119
    .line 120
    move-result v1

    .line 121
    if-eqz v1, :cond_6

    .line 122
    .line 123
    goto :goto_3

    .line 124
    :cond_7
    move-object p2, v7

    .line 125
    :goto_3
    check-cast p2, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 126
    .line 127
    if-nez p2, :cond_b

    .line 128
    .line 129
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->b:Lw30/q;

    .line 130
    .line 131
    iput v8, v4, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 132
    .line 133
    const/4 v2, 0x0

    .line 134
    const/4 v3, 0x0

    .line 135
    const/4 v5, 0x1

    .line 136
    const/4 v6, 0x0

    .line 137
    invoke-static/range {v1 .. v6}, Lw30/q$a;->a(Lw30/q;ZILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 138
    .line 139
    .line 140
    move-result-object p2

    .line 141
    if-ne p2, v0, :cond_8

    .line 142
    .line 143
    goto :goto_6

    .line 144
    :cond_8
    :goto_4
    check-cast p2, Ljava/lang/Iterable;

    .line 145
    .line 146
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    :cond_9
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 151
    .line 152
    .line 153
    move-result p2

    .line 154
    if-eqz p2, :cond_a

    .line 155
    .line 156
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object p2

    .line 160
    move-object v0, p2

    .line 161
    check-cast v0, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 162
    .line 163
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->e(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z

    .line 164
    .line 165
    .line 166
    move-result v0

    .line 167
    if-eqz v0, :cond_9

    .line 168
    .line 169
    move-object v7, p2

    .line 170
    :cond_a
    move-object p2, v7

    .line 171
    check-cast p2, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;

    .line 172
    .line 173
    :cond_b
    if-eqz p2, :cond_d

    .line 174
    .line 175
    invoke-static {p2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 176
    .line 177
    .line 178
    move-result-object p1

    .line 179
    if-nez p1, :cond_c

    .line 180
    .line 181
    goto :goto_5

    .line 182
    :cond_c
    return-object p1

    .line 183
    :cond_d
    :goto_5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 184
    .line 185
    .line 186
    move-result-object p1

    .line 187
    return-object p1

    .line 188
    :cond_e
    move v3, p1

    .line 189
    iget-object v1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->b:Lw30/q;

    .line 190
    .line 191
    iput v2, v4, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$getGames$1;->label:I

    .line 192
    .line 193
    const/4 v2, 0x0

    .line 194
    const/4 v5, 0x1

    .line 195
    const/4 v6, 0x0

    .line 196
    invoke-static/range {v1 .. v6}, Lw30/q$a;->a(Lw30/q;ZILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 197
    .line 198
    .line 199
    move-result-object p1

    .line 200
    if-ne p1, v0, :cond_f

    .line 201
    .line 202
    :goto_6
    return-object v0

    .line 203
    :cond_f
    return-object p1
.end method

.method public final d(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lf50/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;-><init>(Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    return-object p1

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    iget-object p1, p0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->c:Lw30/m;

    .line 61
    .line 62
    iput v4, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->label:I

    .line 63
    .line 64
    invoke-interface {p1, v0}, Lw30/m;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    if-ne p1, v1, :cond_4

    .line 69
    .line 70
    goto :goto_2

    .line 71
    :cond_4
    :goto_1
    check-cast p1, Ljava/lang/Iterable;

    .line 72
    .line 73
    invoke-static {p1}, Lkotlinx/coroutines/flow/g;->c(Ljava/lang/Iterable;)Lkotlinx/coroutines/flow/e;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    new-instance v2, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1;

    .line 78
    .line 79
    invoke-direct {v2, p1, p0}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;)V

    .line 80
    .line 81
    .line 82
    iput v3, v0, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario$invoke$1;->label:I

    .line 83
    .line 84
    const/4 p1, 0x0

    .line 85
    invoke-static {v2, p1, v0, v4, p1}, Lkotlinx/coroutines/flow/g;->B0(Lkotlinx/coroutines/flow/e;Ljava/util/List;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    if-ne p1, v1, :cond_5

    .line 90
    .line 91
    :goto_2
    return-object v1

    .line 92
    :cond_5
    return-object p1
.end method

.method public final e(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z
    .locals 5

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    sget-object v2, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->LUCKY_WHEEL:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 10
    .line 11
    invoke-virtual {v2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->getGameId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    cmp-long v4, v0, v2

    .line 16
    .line 17
    if-eqz v4, :cond_0

    .line 18
    .line 19
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/popular_classic/domain/scenarios/GetGameItemsByCategoryScenario;->f(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    if-eqz p1, :cond_0

    .line 24
    .line 25
    const/4 p1, 0x1

    .line 26
    return p1

    .line 27
    :cond_0
    const/4 p1, 0x0

    .line 28
    return p1
.end method

.method public final f(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getUnderMaintenance()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getEnable()Z

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    const/4 p1, 0x1

    .line 14
    return p1

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    return p1
.end method
