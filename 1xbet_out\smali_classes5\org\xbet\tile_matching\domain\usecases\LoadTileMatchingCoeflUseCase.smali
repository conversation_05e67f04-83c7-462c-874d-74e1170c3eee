.class public final Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u001e\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0080B\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;",
        "",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(LAT0/a;)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "",
        "LzT0/c;",
        "a",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LAT0/a;)V
    .locals 0
    .param p1    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;->a:LAT0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LzT0/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;-><init>(Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;->a:LAT0/a;

    .line 54
    .line 55
    iput v3, v0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase$invoke$1;->label:I

    .line 56
    .line 57
    invoke-interface {p2, p1, v0}, LAT0/a;->h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    if-ne p2, v1, :cond_3

    .line 62
    .line 63
    return-object v1

    .line 64
    :cond_3
    :goto_1
    check-cast p2, Ljava/util/List;

    .line 65
    .line 66
    iget-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/LoadTileMatchingCoeflUseCase;->a:LAT0/a;

    .line 67
    .line 68
    invoke-interface {p1, p2}, LAT0/a;->e(Ljava/util/List;)V

    .line 69
    .line 70
    .line 71
    return-object p2
.end method
