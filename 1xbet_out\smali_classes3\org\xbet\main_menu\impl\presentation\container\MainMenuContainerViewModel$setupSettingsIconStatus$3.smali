.class final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.container.MainMenuContainerViewModel$setupSettingsIconStatus$3"
    f = "MainMenuContainerViewModel.kt"
    l = {
        0x14e,
        0x152
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->N4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;

    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->label:I

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_2

    .line 13
    .line 14
    if-eq v2, v5, :cond_1

    .line 15
    .line 16
    if-ne v2, v4, :cond_0

    .line 17
    .line 18
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->L$0:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v1, Lcom/xbet/onexuser/domain/models/SecurityLevel;

    .line 21
    .line 22
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    move-object/from16 v4, p1

    .line 26
    .line 27
    goto :goto_2

    .line 28
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 29
    .line 30
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 31
    .line 32
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    throw v1

    .line 36
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    move-object/from16 v2, p1

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    iget-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 46
    .line 47
    invoke-static {v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->D3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lmn0/i;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    iput v5, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->label:I

    .line 52
    .line 53
    invoke-interface {v2, v3, v0}, Lmn0/i;->a(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v2

    .line 57
    if-ne v2, v1, :cond_3

    .line 58
    .line 59
    goto :goto_1

    .line 60
    :cond_3
    :goto_0
    check-cast v2, Lcom/xbet/onexuser/domain/models/SecurityLevel;

    .line 61
    .line 62
    iget-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 63
    .line 64
    invoke-static {v6}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lek0/o;

    .line 65
    .line 66
    .line 67
    move-result-object v6

    .line 68
    invoke-virtual {v6}, Lek0/o;->L2()Z

    .line 69
    .line 70
    .line 71
    move-result v6

    .line 72
    if-eqz v6, :cond_6

    .line 73
    .line 74
    iget-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 75
    .line 76
    invoke-static {v6}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->O3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LfX/b;

    .line 77
    .line 78
    .line 79
    move-result-object v6

    .line 80
    invoke-interface {v6}, LfX/b;->o0()Z

    .line 81
    .line 82
    .line 83
    move-result v6

    .line 84
    if-eqz v6, :cond_6

    .line 85
    .line 86
    iget-object v6, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 87
    .line 88
    invoke-static {v6}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->B3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 89
    .line 90
    .line 91
    move-result-object v6

    .line 92
    iput-object v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->L$0:Ljava/lang/Object;

    .line 93
    .line 94
    iput v4, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->label:I

    .line 95
    .line 96
    invoke-virtual {v6, v5, v0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v4

    .line 100
    if-ne v4, v1, :cond_4

    .line 101
    .line 102
    :goto_1
    return-object v1

    .line 103
    :cond_4
    move-object v1, v2

    .line 104
    :goto_2
    check-cast v4, Le9/a;

    .line 105
    .line 106
    invoke-virtual {v4}, Le9/a;->a0()Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    sget-object v4, Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;->VERIFICATION_SUCCESSFUL:Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;

    .line 111
    .line 112
    if-ne v2, v4, :cond_5

    .line 113
    .line 114
    const/4 v3, 0x1

    .line 115
    :cond_5
    move-object v2, v1

    .line 116
    move v5, v3

    .line 117
    :cond_6
    sget-object v1, Lcom/xbet/onexuser/domain/models/SecurityLevel;->HIGH:Lcom/xbet/onexuser/domain/models/SecurityLevel;

    .line 118
    .line 119
    if-ne v2, v1, :cond_8

    .line 120
    .line 121
    if-nez v5, :cond_7

    .line 122
    .line 123
    goto :goto_4

    .line 124
    :cond_7
    const/4 v1, 0x0

    .line 125
    :goto_3
    move-object v11, v1

    .line 126
    goto :goto_5

    .line 127
    :cond_8
    :goto_4
    sget-object v1, Lorg/xbet/uikit/models/StateStatus;->WARNING_RED:Lorg/xbet/uikit/models/StateStatus;

    .line 128
    .line 129
    goto :goto_3

    .line 130
    :goto_5
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;->this$0:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 131
    .line 132
    invoke-static {v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    :goto_6
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 137
    .line 138
    .line 139
    move-result-object v2

    .line 140
    move-object v3, v2

    .line 141
    move-object v2, v3

    .line 142
    check-cast v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 143
    .line 144
    const/16 v16, 0x1eff

    .line 145
    .line 146
    const/16 v17, 0x0

    .line 147
    .line 148
    move-object v4, v3

    .line 149
    const/4 v3, 0x0

    .line 150
    move-object v5, v4

    .line 151
    const/4 v4, 0x0

    .line 152
    move-object v6, v5

    .line 153
    const/4 v5, 0x0

    .line 154
    move-object v7, v6

    .line 155
    const/4 v6, 0x0

    .line 156
    move-object v8, v7

    .line 157
    const/4 v7, 0x0

    .line 158
    move-object v9, v8

    .line 159
    const/4 v8, 0x0

    .line 160
    move-object v10, v9

    .line 161
    const/4 v9, 0x0

    .line 162
    move-object v12, v10

    .line 163
    const/4 v10, 0x0

    .line 164
    move-object v13, v12

    .line 165
    const/4 v12, 0x0

    .line 166
    move-object v14, v13

    .line 167
    const/4 v13, 0x0

    .line 168
    move-object v15, v14

    .line 169
    const/4 v14, 0x0

    .line 170
    move-object/from16 v18, v15

    .line 171
    .line 172
    const/4 v15, 0x0

    .line 173
    move-object/from16 v0, v18

    .line 174
    .line 175
    invoke-static/range {v2 .. v17}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 176
    .line 177
    .line 178
    move-result-object v2

    .line 179
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 180
    .line 181
    .line 182
    move-result v0

    .line 183
    if-eqz v0, :cond_9

    .line 184
    .line 185
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 186
    .line 187
    return-object v0

    .line 188
    :cond_9
    move-object/from16 v0, p0

    .line 189
    .line 190
    goto :goto_6
.end method
