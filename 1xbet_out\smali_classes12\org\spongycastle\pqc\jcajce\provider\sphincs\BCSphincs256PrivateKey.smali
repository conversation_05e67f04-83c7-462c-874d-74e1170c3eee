.class public Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/security/PrivateKey;
.implements Lorg/spongycastle/pqc/jcajce/interfaces/SPHINCSKey;


# static fields
.field private static final serialVersionUID:J = 0x1L


# instance fields
.field private final params:LOf/a;

.field private final treeDigest:LSe/m;


# direct methods
.method public constructor <init>(LSe/m;LOf/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 3
    iput-object p2, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    return-void
.end method

.method public constructor <init>(Lcf/d;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5
    invoke-virtual {p1}, Lcf/d;->s()Lkf/a;

    move-result-object v0

    invoke-virtual {v0}, Lkf/a;->t()LSe/e;

    move-result-object v0

    invoke-static {v0}, LIf/i;->o(Ljava/lang/Object;)LIf/i;

    move-result-object v0

    invoke-virtual {v0}, LIf/i;->r()Lkf/a;

    move-result-object v0

    invoke-virtual {v0}, Lkf/a;->o()LSe/m;

    move-result-object v0

    iput-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 6
    new-instance v0, LOf/a;

    invoke-virtual {p1}, Lcf/d;->t()LSe/e;

    move-result-object p1

    invoke-static {p1}, LSe/n;->C(Ljava/lang/Object;)LSe/n;

    move-result-object p1

    invoke-virtual {p1}, LSe/n;->D()[B

    move-result-object p1

    invoke-direct {v0, p1}, LOf/a;-><init>([B)V

    iput-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p1, p0, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    check-cast p1, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;

    .line 11
    .line 12
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 13
    .line 14
    iget-object v3, p1, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 15
    .line 16
    invoke-virtual {v1, v3}, LSe/q;->equals(Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_1

    .line 21
    .line 22
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 23
    .line 24
    invoke-virtual {v1}, LOf/a;->b()[B

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iget-object p1, p1, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 29
    .line 30
    invoke-virtual {p1}, LOf/a;->b()[B

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-static {v1, p1}, Lorg/spongycastle/util/a;->a([B[B)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    return v0

    .line 41
    :cond_1
    return v2
.end method

.method public final getAlgorithm()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "SPHINCS-256"

    .line 2
    .line 3
    return-object v0
.end method

.method public getEncoded()[B
    .locals 5

    .line 1
    :try_start_0
    new-instance v0, Lkf/a;

    .line 2
    .line 3
    sget-object v1, LIf/e;->r:LSe/m;

    .line 4
    .line 5
    new-instance v2, LIf/i;

    .line 6
    .line 7
    new-instance v3, Lkf/a;

    .line 8
    .line 9
    iget-object v4, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 10
    .line 11
    invoke-direct {v3, v4}, Lkf/a;-><init>(LSe/m;)V

    .line 12
    .line 13
    .line 14
    invoke-direct {v2, v3}, LIf/i;-><init>(Lkf/a;)V

    .line 15
    .line 16
    .line 17
    invoke-direct {v0, v1, v2}, Lkf/a;-><init>(LSe/m;LSe/e;)V

    .line 18
    .line 19
    .line 20
    new-instance v1, Lcf/d;

    .line 21
    .line 22
    new-instance v2, LSe/X;

    .line 23
    .line 24
    iget-object v3, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 25
    .line 26
    invoke-virtual {v3}, LOf/a;->b()[B

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    invoke-direct {v2, v3}, LSe/X;-><init>([B)V

    .line 31
    .line 32
    .line 33
    invoke-direct {v1, v0, v2}, Lcf/d;-><init>(Lkf/a;LSe/e;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v1}, LSe/l;->i()[B

    .line 37
    .line 38
    .line 39
    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 40
    return-object v0

    .line 41
    :catch_0
    const/4 v0, 0x0

    .line 42
    return-object v0
.end method

.method public getFormat()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "PKCS#8"

    .line 2
    .line 3
    return-object v0
.end method

.method public getKeyData()[B
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LOf/a;->b()[B

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public getKeyParams()Lorg/spongycastle/crypto/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    invoke-virtual {v0}, LSe/m;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/sphincs/BCSphincs256PrivateKey;->params:LOf/a;

    .line 8
    .line 9
    invoke-virtual {v1}, LOf/a;->b()[B

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-static {v1}, Lorg/spongycastle/util/a;->p([B)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    mul-int/lit8 v1, v1, 0x25

    .line 18
    .line 19
    add-int/2addr v0, v1

    .line 20
    return v0
.end method
