.class public final Lorg/xbet/ui_common/moxy/activities/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/ui_common/moxy/activities/h$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/ui_common/moxy/activities/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/ui_common/moxy/activities/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/moxy/activities/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/onexlocalization/k;)Lorg/xbet/ui_common/moxy/activities/h;
    .locals 2

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/ui_common/moxy/activities/a$b;

    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    invoke-direct {v0, p1, v1}, Lorg/xbet/ui_common/moxy/activities/a$b;-><init>(Lorg/xbet/onexlocalization/k;Lorg/xbet/ui_common/moxy/activities/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
