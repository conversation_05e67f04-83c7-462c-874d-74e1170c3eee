.class public final LQQ0/u;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0018\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0086\u0002\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LQQ0/u;",
        "",
        "LOQ0/a;",
        "tennisWinLossRepository",
        "<init>",
        "(LOQ0/a;)V",
        "LPQ0/d;",
        "info",
        "",
        "a",
        "(LPQ0/d;)V",
        "LOQ0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LOQ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LOQ0/a;)V
    .locals 0
    .param p1    # LOQ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQQ0/u;->a:LOQ0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(LPQ0/d;)V
    .locals 2
    .param p1    # LPQ0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, LPQ0/b;

    .line 2
    .line 3
    invoke-virtual {p1}, LPQ0/d;->c()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {p1}, LPQ0/d;->b()Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-direct {v0, v1, p1}, LPQ0/b;-><init>(ILorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, LQQ0/u;->a:LOQ0/a;

    .line 15
    .line 16
    invoke-interface {p1, v0}, LOQ0/a;->i(LPQ0/b;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method
