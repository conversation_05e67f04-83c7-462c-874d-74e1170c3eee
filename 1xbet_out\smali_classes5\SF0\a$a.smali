.class public final LSF0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LSF0/g$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LSF0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LSF0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LSF0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;JLi8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;)LSF0/g;
    .locals 17

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p8}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    new-instance v1, LSF0/a$b;

    .line 48
    .line 49
    invoke-static/range {p7 .. p8}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 50
    .line 51
    .line 52
    move-result-object v8

    .line 53
    const/16 v16, 0x0

    .line 54
    .line 55
    move-object/from16 v2, p1

    .line 56
    .line 57
    move-object/from16 v3, p2

    .line 58
    .line 59
    move-object/from16 v4, p3

    .line 60
    .line 61
    move-object/from16 v5, p4

    .line 62
    .line 63
    move-object/from16 v6, p5

    .line 64
    .line 65
    move-object/from16 v7, p6

    .line 66
    .line 67
    move-object/from16 v9, p9

    .line 68
    .line 69
    move-object/from16 v10, p10

    .line 70
    .line 71
    move-object/from16 v11, p11

    .line 72
    .line 73
    move-object/from16 v12, p12

    .line 74
    .line 75
    move-object/from16 v13, p13

    .line 76
    .line 77
    move-object/from16 v14, p14

    .line 78
    .line 79
    move-object/from16 v15, p15

    .line 80
    .line 81
    invoke-direct/range {v1 .. v16}, LSF0/a$b;-><init>(LQW0/c;LEN0/f;LSX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;Ljava/lang/String;Ljava/lang/Long;Li8/m;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LSF0/b;)V

    .line 82
    .line 83
    .line 84
    return-object v1
.end method
