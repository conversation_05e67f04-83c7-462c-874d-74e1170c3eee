.class final Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.domain.interactors.AfricanRouletteInteractor$play$2$1"
    f = "AfricanRouletteInteractor.kt"
    l = {
        0x1e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lig/b;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "",
        "token",
        "Lig/b;",
        "<anonymous>",
        "(Ljava/lang/String;)Lig/b;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $rouletteBets:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lig/a;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;Ljava/util/List;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;",
            "Ljava/util/List<",
            "Lig/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    iput-object p2, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->$rouletteBets:Ljava/util/List;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;

    iget-object v1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    iget-object v2, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->$rouletteBets:Ljava/util/List;

    invoke-direct {v0, v1, v2, p2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;-><init>(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;Ljava/util/List;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lig/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-object p1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    move-object v4, p1

    .line 30
    check-cast v4, Ljava/lang/String;

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 33
    .line 34
    invoke-static {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->a(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    iget-object v5, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->$rouletteBets:Ljava/util/List;

    .line 39
    .line 40
    iget-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 41
    .line 42
    invoke-static {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->b(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)J

    .line 43
    .line 44
    .line 45
    move-result-wide v6

    .line 46
    iget-object p1, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->this$0:Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->c(Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;)Lorg/xbet/core/domain/usecases/bonus/e;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-virtual {p1}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 53
    .line 54
    .line 55
    move-result-object v8

    .line 56
    iput v2, p0, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor$play$2$1;->label:I

    .line 57
    .line 58
    move-object v9, p0

    .line 59
    invoke-virtual/range {v3 .. v9}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->o(Ljava/lang/String;Ljava/util/List;JLorg/xbet/games_section/api/models/GameBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    if-ne p1, v0, :cond_2

    .line 64
    .line 65
    return-object v0

    .line 66
    :cond_2
    return-object p1
.end method
