.class public final Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0081\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0006*\u0001\u0017\u0018\u0000 e2\u00020\u0001:\u0001fB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u000f\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u000f\u0010\u000b\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J\u000f\u0010\u000c\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\u0003J\u001d\u0010\u000f\u001a\u00020\u00062\u000c\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\u00060\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0003J\u0017\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0003J\u000f\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0003J\u000f\u0010\u001b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u0003J\u0019\u0010\u001e\u001a\u00020\u00062\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0019\u0010 \u001a\u00020\u00062\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001cH\u0014\u00a2\u0006\u0004\u0008 \u0010\u001fJ\u000f\u0010!\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008!\u0010\u0003J\u000f\u0010\"\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\"\u0010\u0003J\u000f\u0010#\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008#\u0010\u0003R+\u0010,\u001a\u00020$2\u0006\u0010%\u001a\u00020$8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008&\u0010\'\u001a\u0004\u0008(\u0010)\"\u0004\u0008*\u0010+R+\u00104\u001a\u00020-2\u0006\u0010%\u001a\u00020-8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\"\u0004\u00082\u00103R\"\u0010<\u001a\u0002058\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109\"\u0004\u0008:\u0010;R\"\u0010D\u001a\u00020=8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010CR\"\u0010L\u001a\u00020E8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008F\u0010G\u001a\u0004\u0008H\u0010I\"\u0004\u0008J\u0010KR\"\u0010T\u001a\u00020M8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008N\u0010O\u001a\u0004\u0008P\u0010Q\"\u0004\u0008R\u0010SR\u001b\u0010Z\u001a\u00020U8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008V\u0010W\u001a\u0004\u0008X\u0010YR\u001b\u0010`\u001a\u00020[8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\\\u0010]\u001a\u0004\u0008^\u0010_R\u0014\u0010d\u001a\u00020a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010c\u00a8\u0006g"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "",
        "m3",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "n3",
        "l3",
        "o3",
        "s3",
        "Lkotlin/Function0;",
        "runFunction",
        "q3",
        "(Lkotlin/jvm/functions/Function0;)V",
        "p3",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;",
        "errorState",
        "k3",
        "(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;)V",
        "Z2",
        "org/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b",
        "g3",
        "()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b;",
        "h3",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "t2",
        "onResume",
        "onPause",
        "v2",
        "",
        "<set-?>",
        "i0",
        "LeX0/d;",
        "T2",
        "()I",
        "j3",
        "(I)V",
        "bundleBonusId",
        "",
        "j0",
        "LeX0/f;",
        "S2",
        "()J",
        "i3",
        "(J)V",
        "bundleAccountId",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "k0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "Y2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LTZ0/a;",
        "l0",
        "LTZ0/a;",
        "R2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "m0",
        "LzX0/k;",
        "V2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lck/a;",
        "n0",
        "Lck/a;",
        "U2",
        "()Lck/a;",
        "setChangeBalanceDialogProvider",
        "(Lck/a;)V",
        "changeBalanceDialogProvider",
        "Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
        "o0",
        "Lkotlin/j;",
        "X2",
        "()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;",
        "viewModel",
        "LS91/N;",
        "b1",
        "LRc/c;",
        "W2",
        "()LS91/N;",
        "viewBinding",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "k1",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "pagingAdapterObserver",
        "v1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final v1:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic x1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i0:LeX0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k1:Landroidx/recyclerview/widget/RecyclerView$i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l0:LTZ0/a;

.field public m0:LzX0/k;

.field public n0:Lck/a;

.field public final o0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;

    .line 4
    .line 5
    const-string v2, "bundleBonusId"

    .line 6
    .line 7
    const-string v3, "getBundleBonusId()I"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bundleAccountId"

    .line 20
    .line 21
    const-string v5, "getBundleAccountId()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "viewBinding"

    .line 33
    .line 34
    const-string v6, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAvailableGamesPublisherBinding;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->v1:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 11

    .line 1
    sget v0, Lu91/c;->fragment_available_games_publisher:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LeX0/d;

    .line 7
    .line 8
    const-string v1, "PARTITION_ID"

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x2

    .line 12
    const/4 v4, 0x0

    .line 13
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/d;-><init>(Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->i0:LeX0/d;

    .line 17
    .line 18
    new-instance v5, LeX0/f;

    .line 19
    .line 20
    const/4 v9, 0x2

    .line 21
    const/4 v10, 0x0

    .line 22
    const-string v6, "ACCOUNT_ID"

    .line 23
    .line 24
    const-wide/16 v7, 0x0

    .line 25
    .line 26
    invoke-direct/range {v5 .. v10}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    .line 28
    .line 29
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->j0:LeX0/f;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/f;

    .line 32
    .line 33
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/f;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 34
    .line 35
    .line 36
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$1;

    .line 37
    .line 38
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 39
    .line 40
    .line 41
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 42
    .line 43
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$2;

    .line 44
    .line 45
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 46
    .line 47
    .line 48
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    const-class v2, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 53
    .line 54
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$3;

    .line 59
    .line 60
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 61
    .line 62
    .line 63
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$4;

    .line 64
    .line 65
    invoke-direct {v5, v4, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 66
    .line 67
    .line 68
    invoke-static {p0, v2, v3, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->o0:Lkotlin/j;

    .line 73
    .line 74
    sget-object v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$viewBinding$2;

    .line 75
    .line 76
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->b1:LRc/c;

    .line 81
    .line 82
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->g3()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->k1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 87
    .line 88
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->e3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->t3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->r3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->b3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->c3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->a3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic G2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)LS91/N;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->h3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->i3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->j3(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->k3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->l3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->m3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->p3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->q3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->s3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final Z2()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/N;->c:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/available_games/h;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/h;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 10
    .line 11
    .line 12
    const/4 v2, 0x1

    .line 13
    const/4 v3, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    invoke-static {v0, v4, v1, v2, v3}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public static final a3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->W3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final b3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->b4(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final c3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->n3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final d3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v0, v0, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 12
    .line 13
    const/16 v1, 0x8

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Landroidx/paging/u;->d()Landroidx/paging/s;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 27
    .line 28
    const/4 v2, 0x0

    .line 29
    if-eqz v1, :cond_0

    .line 30
    .line 31
    check-cast v0, Landroidx/paging/s$a;

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    move-object v0, v2

    .line 35
    :goto_0
    if-nez v0, :cond_5

    .line 36
    .line 37
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {v0}, Landroidx/paging/u;->e()Landroidx/paging/s;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 46
    .line 47
    if-eqz v1, :cond_1

    .line 48
    .line 49
    check-cast v0, Landroidx/paging/s$a;

    .line 50
    .line 51
    goto :goto_1

    .line 52
    :cond_1
    move-object v0, v2

    .line 53
    :goto_1
    if-nez v0, :cond_5

    .line 54
    .line 55
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v0}, Landroidx/paging/u;->f()Landroidx/paging/s;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 64
    .line 65
    if-eqz v1, :cond_2

    .line 66
    .line 67
    check-cast v0, Landroidx/paging/s$a;

    .line 68
    .line 69
    goto :goto_2

    .line 70
    :cond_2
    move-object v0, v2

    .line 71
    :goto_2
    if-nez v0, :cond_5

    .line 72
    .line 73
    invoke-virtual {p2}, Landroidx/paging/f;->a()Landroidx/paging/s;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 78
    .line 79
    if-eqz v1, :cond_3

    .line 80
    .line 81
    check-cast v0, Landroidx/paging/s$a;

    .line 82
    .line 83
    goto :goto_3

    .line 84
    :cond_3
    move-object v0, v2

    .line 85
    :goto_3
    if-nez v0, :cond_5

    .line 86
    .line 87
    invoke-virtual {p2}, Landroidx/paging/f;->c()Landroidx/paging/s;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 92
    .line 93
    if-eqz v1, :cond_4

    .line 94
    .line 95
    check-cast v0, Landroidx/paging/s$a;

    .line 96
    .line 97
    goto :goto_4

    .line 98
    :cond_4
    move-object v0, v2

    .line 99
    :goto_4
    if-nez v0, :cond_5

    .line 100
    .line 101
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 106
    .line 107
    if-eqz v1, :cond_6

    .line 108
    .line 109
    move-object v2, v0

    .line 110
    check-cast v2, Landroidx/paging/s$a;

    .line 111
    .line 112
    goto :goto_5

    .line 113
    :cond_5
    move-object v2, v0

    .line 114
    :cond_6
    :goto_5
    if-eqz v2, :cond_7

    .line 115
    .line 116
    invoke-virtual {v2}, Landroidx/paging/s$a;->b()Ljava/lang/Throwable;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    invoke-virtual {v1, v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->U3(Ljava/lang/Throwable;)V

    .line 125
    .line 126
    .line 127
    :cond_7
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 128
    .line 129
    .line 130
    move-result-object v0

    .line 131
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 132
    .line 133
    if-nez v0, :cond_8

    .line 134
    .line 135
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 136
    .line 137
    .line 138
    move-result v0

    .line 139
    if-nez v0, :cond_8

    .line 140
    .line 141
    if-nez v2, :cond_8

    .line 142
    .line 143
    const/4 v0, 0x1

    .line 144
    goto :goto_6

    .line 145
    :cond_8
    const/4 v0, 0x0

    .line 146
    :goto_6
    if-eqz v0, :cond_9

    .line 147
    .line 148
    invoke-direct {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->o3()V

    .line 149
    .line 150
    .line 151
    :cond_9
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    instance-of p1, p1, Landroidx/paging/s$b;

    .line 156
    .line 157
    if-nez p1, :cond_a

    .line 158
    .line 159
    if-nez v2, :cond_a

    .line 160
    .line 161
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 162
    .line 163
    .line 164
    :cond_a
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 165
    .line 166
    return-object p0
.end method

.method public static final e3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LN21/k;->e()J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    invoke-virtual {p0, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->a4(J)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final f3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LN21/k;->e()J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    invoke-virtual {p1}, LN21/k;->c()LN21/m;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p1}, LN21/m;->b()Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-virtual {p0, v0, v1, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->Z3(JZ)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method private final h3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final l3()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->V2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final m3(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, "OPEN_GAME_ITEM"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0, v1, p1}, Landroid/os/Bundle;->putSerializable(Ljava/lang/String;Ljava/io/Serializable;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const/4 v0, 0x1

    .line 18
    new-array v0, v0, [Lkotlin/Pair;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    aput-object p1, v0, v1

    .line 22
    .line 23
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 28
    .line 29
    .line 30
    :goto_0
    sget-object p1, LKW0/b;->a:LKW0/b;

    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->R2()LTZ0/a;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p1, p0, v0}, LKW0/b;->c(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method private final n3()V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->U2()Lck/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    sget v3, Lpb/k;->gift_balance_dialog_description:I

    .line 12
    .line 13
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    const/16 v11, 0x2e6

    .line 22
    .line 23
    const/4 v12, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const-string v9, "REQUEST_CHANGE_BALANCE_KEY"

    .line 30
    .line 31
    const/4 v10, 0x0

    .line 32
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private final o3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->O3()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v1, v2}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, v0, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final q3(Lkotlin/jvm/functions/Function0;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->R2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/g;

    .line 8
    .line 9
    invoke-direct {v2, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/g;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, p0, v2, v1}, LKW0/b;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function0;LTZ0/a;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final r3(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final s3()V
    .locals 2

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->R2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, p0, v1}, LKW0/b;->f(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final t3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->Y2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->d3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->f3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final R2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->l0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final S2()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->j0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final T2()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->i0:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/d;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Integer;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final U2()Lck/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->n0:Lck/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final V2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->m0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final W2()LS91/N;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->b1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/N;

    .line 13
    .line 14
    return-object v0
.end method

.method public final X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->o0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final Y2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->k0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final g3()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$b;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final i3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->j0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final j3(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->i0:LeX0/d;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/d;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final k3(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b;)V
    .locals 2

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iget-object v0, v0, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 11
    .line 12
    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;

    .line 13
    .line 14
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 26
    .line 27
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_0
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel$b$b;

    .line 32
    .line 33
    if-eqz p1, :cond_1

    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 40
    .line 41
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iget-object p1, p1, LS91/N;->b:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 49
    .line 50
    const/16 v0, 0x8

    .line 51
    .line 52
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 53
    .line 54
    .line 55
    return-void

    .line 56
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 57
    .line 58
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 59
    .line 60
    .line 61
    throw p1
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 1

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/available_games/a;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/a;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    new-instance p1, Lorg/xplatform/aggregator/impl/gifts/available_games/b;

    .line 13
    .line 14
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/b;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 15
    .line 16
    .line 17
    const-string v0, "REQUEST_BONUS_BALANCE_WARNING_DIALOG_KEY"

    .line 18
    .line 19
    invoke-static {p0, v0, p1}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->k1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->k1:Landroidx/recyclerview/widget/RecyclerView$i;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public final p3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->R2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->error:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->casino_favorites_limit_error:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    sget v5, Lpb/k;->ok_new:I

    .line 22
    .line 23
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 28
    .line 29
    const/16 v15, 0xbf8

    .line 30
    .line 31
    const/16 v16, 0x0

    .line 32
    .line 33
    const/4 v6, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    const/4 v8, 0x0

    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v10, 0x0

    .line 38
    const/4 v11, 0x0

    .line 39
    const/4 v12, 0x0

    .line 40
    const/4 v14, 0x0

    .line 41
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->c4()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->Z2()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->M3()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 33
    .line 34
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    sget v1, LlZ0/g;->space_8:I

    .line 39
    .line 40
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    const/4 v1, 0x0

    .line 45
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->q(II)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 53
    .line 54
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    if-eqz p1, :cond_0

    .line 59
    .line 60
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/c;

    .line 61
    .line 62
    invoke-direct {v0, p1, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p1, v0}, Landroidx/paging/PagingDataAdapter;->p(Lkotlin/jvm/functions/Function1;)V

    .line 66
    .line 67
    .line 68
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 73
    .line 74
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/d;

    .line 75
    .line 76
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/d;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->W2()LS91/N;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iget-object p1, p1, LS91/N;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 87
    .line 88
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/available_games/e;

    .line 89
    .line 90
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/e;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method public u2()V
    .locals 13

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lca1/b;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lca1/b;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lca1/b;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    new-instance v3, Lea1/a;

    .line 53
    .line 54
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->T2()I

    .line 55
    .line 56
    .line 57
    move-result v4

    .line 58
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->S2()J

    .line 59
    .line 60
    .line 61
    move-result-wide v5

    .line 62
    sget-object v10, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->UNKNOWN:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 63
    .line 64
    const/16 v11, 0xc

    .line 65
    .line 66
    const/4 v12, 0x0

    .line 67
    const-wide/16 v7, 0x0

    .line 68
    .line 69
    const/4 v9, 0x0

    .line 70
    invoke-direct/range {v3 .. v12}, Lea1/a;-><init>(IJJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v2, v3}, Lca1/b;->a(Lea1/a;)Lca1/a;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-interface {v0, p0}, Lca1/a;->a(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;)V

    .line 78
    .line 79
    .line 80
    return-void

    .line 81
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 82
    .line 83
    new-instance v2, Ljava/lang/StringBuilder;

    .line 84
    .line 85
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 86
    .line 87
    .line 88
    const-string v3, "Cannot create dependency "

    .line 89
    .line 90
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->R3()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v11

    .line 27
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    move-object v5, v10

    .line 31
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v15, 0x3

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v12, 0x0

    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v14, v2

    .line 40
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->K3()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    new-instance v11, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$2;

    .line 52
    .line 53
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 65
    .line 66
    move-object v7, v5

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/4 v6, 0x3

    .line 71
    const/4 v7, 0x0

    .line 72
    const/4 v3, 0x0

    .line 73
    const/4 v4, 0x0

    .line 74
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->Q3()Lkotlinx/coroutines/flow/e;

    .line 82
    .line 83
    .line 84
    move-result-object v4

    .line 85
    sget-object v6, Landroidx/lifecycle/Lifecycle$State;->RESUMED:Landroidx/lifecycle/Lifecycle$State;

    .line 86
    .line 87
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$3;

    .line 88
    .line 89
    invoke-direct {v7, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/coroutines/e;)V

    .line 90
    .line 91
    .line 92
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 93
    .line 94
    .line 95
    move-result-object v5

    .line 96
    invoke-static {v5}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 97
    .line 98
    .line 99
    move-result-object v11

    .line 100
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;

    .line 101
    .line 102
    const/4 v8, 0x0

    .line 103
    invoke-direct/range {v3 .. v8}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 104
    .line 105
    .line 106
    move-object v14, v3

    .line 107
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 108
    .line 109
    .line 110
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->L3()Lkotlinx/coroutines/flow/e;

    .line 115
    .line 116
    .line 117
    move-result-object v8

    .line 118
    new-instance v11, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$4;

    .line 119
    .line 120
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/coroutines/e;)V

    .line 121
    .line 122
    .line 123
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 124
    .line 125
    .line 126
    move-result-object v9

    .line 127
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 128
    .line 129
    .line 130
    move-result-object v2

    .line 131
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 132
    .line 133
    move-object v7, v5

    .line 134
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 135
    .line 136
    .line 137
    const/4 v6, 0x3

    .line 138
    const/4 v7, 0x0

    .line 139
    const/4 v3, 0x0

    .line 140
    const/4 v4, 0x0

    .line 141
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 142
    .line 143
    .line 144
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;->X2()Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;

    .line 145
    .line 146
    .line 147
    move-result-object v2

    .line 148
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesViewModel;->N3()Lkotlinx/coroutines/flow/e;

    .line 149
    .line 150
    .line 151
    move-result-object v8

    .line 152
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 153
    .line 154
    .line 155
    move-result-object v9

    .line 156
    new-instance v11, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$5;

    .line 157
    .line 158
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment;Lkotlin/coroutines/e;)V

    .line 159
    .line 160
    .line 161
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 162
    .line 163
    .line 164
    move-result-object v2

    .line 165
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 166
    .line 167
    move-object v7, v5

    .line 168
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/gifts/available_games/AvailableGamesFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 169
    .line 170
    .line 171
    const/4 v7, 0x0

    .line 172
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 173
    .line 174
    .line 175
    return-void
.end method
