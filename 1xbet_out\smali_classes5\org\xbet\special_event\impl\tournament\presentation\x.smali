.class public final Lorg/xbet/special_event/impl/tournament/presentation/x;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LIu0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lry0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LPx0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LKs0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final J:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final K:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final L:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
            ">;"
        }
    .end annotation
.end field

.field public final M:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LFy0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final N:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Llp0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final O:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final P:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final Q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Llp0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final R:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final S:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LuX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final T:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Llp0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final U:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LFI/c;",
            ">;"
        }
    .end annotation
.end field

.field public final V:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Llp0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final W:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LNo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final X:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Llp0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVo/e;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lkc1/p;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltn/a;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lvw0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LWo0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LRl0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVg0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LIP/a;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LJZ/a;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lbl0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LNo0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lss0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lqs0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Los0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LCw0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LDH0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LVo/e;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
            ">;",
            "LBc/a<",
            "Lkc1/p;",
            ">;",
            "LBc/a<",
            "Ltn/a;",
            ">;",
            "LBc/a<",
            "Lvw0/a;",
            ">;",
            "LBc/a<",
            "LWo0/a;",
            ">;",
            "LBc/a<",
            "LRl0/b;",
            ">;",
            "LBc/a<",
            "LVg0/a;",
            ">;",
            "LBc/a<",
            "LIP/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;",
            "LBc/a<",
            "LJZ/a;",
            ">;",
            "LBc/a<",
            "Lbl0/g;",
            ">;",
            "LBc/a<",
            "LNo0/b;",
            ">;",
            "LBc/a<",
            "Lss0/a;",
            ">;",
            "LBc/a<",
            "Lqs0/a;",
            ">;",
            "LBc/a<",
            "Los0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LCw0/a;",
            ">;",
            "LBc/a<",
            "LDH0/a;",
            ">;",
            "LBc/a<",
            "LIu0/b;",
            ">;",
            "LBc/a<",
            "Lry0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LPx0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
            ">;",
            "LBc/a<",
            "LKs0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
            ">;",
            "LBc/a<",
            "LFy0/a;",
            ">;",
            "LBc/a<",
            "Llp0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
            ">;",
            "LBc/a<",
            "Llp0/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
            ">;",
            "LBc/a<",
            "LuX0/a;",
            ">;",
            "LBc/a<",
            "Llp0/g;",
            ">;",
            "LBc/a<",
            "LFI/c;",
            ">;",
            "LBc/a<",
            "Llp0/e;",
            ">;",
            "LBc/a<",
            "LNo0/a;",
            ">;",
            "LBc/a<",
            "Llp0/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->a:LBc/a;

    .line 3
    iput-object p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->b:LBc/a;

    .line 4
    iput-object p3, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->c:LBc/a;

    .line 5
    iput-object p4, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->d:LBc/a;

    .line 6
    iput-object p5, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->e:LBc/a;

    .line 7
    iput-object p6, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->f:LBc/a;

    .line 8
    iput-object p7, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->g:LBc/a;

    .line 9
    iput-object p8, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->h:LBc/a;

    .line 10
    iput-object p9, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->i:LBc/a;

    .line 11
    iput-object p10, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->j:LBc/a;

    .line 12
    iput-object p11, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->k:LBc/a;

    .line 13
    iput-object p12, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->l:LBc/a;

    .line 14
    iput-object p13, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->m:LBc/a;

    .line 15
    iput-object p14, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->n:LBc/a;

    .line 16
    iput-object p15, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->o:LBc/a;

    move-object/from16 p1, p16

    .line 17
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->p:LBc/a;

    move-object/from16 p1, p17

    .line 18
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->q:LBc/a;

    move-object/from16 p1, p18

    .line 19
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->r:LBc/a;

    move-object/from16 p1, p19

    .line 20
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->s:LBc/a;

    move-object/from16 p1, p20

    .line 21
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->t:LBc/a;

    move-object/from16 p1, p21

    .line 22
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->u:LBc/a;

    move-object/from16 p1, p22

    .line 23
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->v:LBc/a;

    move-object/from16 p1, p23

    .line 24
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->w:LBc/a;

    move-object/from16 p1, p24

    .line 25
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->x:LBc/a;

    move-object/from16 p1, p25

    .line 26
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->y:LBc/a;

    move-object/from16 p1, p26

    .line 27
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->z:LBc/a;

    move-object/from16 p1, p27

    .line 28
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->A:LBc/a;

    move-object/from16 p1, p28

    .line 29
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->B:LBc/a;

    move-object/from16 p1, p29

    .line 30
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->C:LBc/a;

    move-object/from16 p1, p30

    .line 31
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->D:LBc/a;

    move-object/from16 p1, p31

    .line 32
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->E:LBc/a;

    move-object/from16 p1, p32

    .line 33
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->F:LBc/a;

    move-object/from16 p1, p33

    .line 34
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->G:LBc/a;

    move-object/from16 p1, p34

    .line 35
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->H:LBc/a;

    move-object/from16 p1, p35

    .line 36
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->I:LBc/a;

    move-object/from16 p1, p36

    .line 37
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->J:LBc/a;

    move-object/from16 p1, p37

    .line 38
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->K:LBc/a;

    move-object/from16 p1, p38

    .line 39
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->L:LBc/a;

    move-object/from16 p1, p39

    .line 40
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->M:LBc/a;

    move-object/from16 p1, p40

    .line 41
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->N:LBc/a;

    move-object/from16 p1, p41

    .line 42
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->O:LBc/a;

    move-object/from16 p1, p42

    .line 43
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->P:LBc/a;

    move-object/from16 p1, p43

    .line 44
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->Q:LBc/a;

    move-object/from16 p1, p44

    .line 45
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->R:LBc/a;

    move-object/from16 p1, p45

    .line 46
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->S:LBc/a;

    move-object/from16 p1, p46

    .line 47
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->T:LBc/a;

    move-object/from16 p1, p47

    .line 48
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->U:LBc/a;

    move-object/from16 p1, p48

    .line 49
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->V:LBc/a;

    move-object/from16 p1, p49

    .line 50
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->W:LBc/a;

    move-object/from16 p1, p50

    .line 51
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/x;->X:LBc/a;

    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/tournament/presentation/x;
    .locals 51
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "LVo/e;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;",
            ">;",
            "LBc/a<",
            "Lkc1/p;",
            ">;",
            "LBc/a<",
            "Ltn/a;",
            ">;",
            "LBc/a<",
            "Lvw0/a;",
            ">;",
            "LBc/a<",
            "LWo0/a;",
            ">;",
            "LBc/a<",
            "LRl0/b;",
            ">;",
            "LBc/a<",
            "LVg0/a;",
            ">;",
            "LBc/a<",
            "LIP/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/k;",
            ">;",
            "LBc/a<",
            "LJZ/a;",
            ">;",
            "LBc/a<",
            "Lbl0/g;",
            ">;",
            "LBc/a<",
            "LNo0/b;",
            ">;",
            "LBc/a<",
            "Lss0/a;",
            ">;",
            "LBc/a<",
            "Lqs0/a;",
            ">;",
            "LBc/a<",
            "Los0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LCw0/a;",
            ">;",
            "LBc/a<",
            "LDH0/a;",
            ">;",
            "LBc/a<",
            "LIu0/b;",
            ">;",
            "LBc/a<",
            "Lry0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "LPx0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;",
            ">;",
            "LBc/a<",
            "LKs0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/teams/domain/usecase/d;",
            ">;",
            "LBc/a<",
            "LFy0/a;",
            ">;",
            "LBc/a<",
            "Llp0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;",
            ">;",
            "LBc/a<",
            "Llp0/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;",
            ">;",
            "LBc/a<",
            "LuX0/a;",
            ">;",
            "LBc/a<",
            "Llp0/g;",
            ">;",
            "LBc/a<",
            "LFI/c;",
            ">;",
            "LBc/a<",
            "Llp0/e;",
            ">;",
            "LBc/a<",
            "LNo0/a;",
            ">;",
            "LBc/a<",
            "Llp0/c;",
            ">;)",
            "Lorg/xbet/special_event/impl/tournament/presentation/x;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/x;

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object/from16 v3, p2

    move-object/from16 v4, p3

    move-object/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    move-object/from16 v8, p7

    move-object/from16 v9, p8

    move-object/from16 v10, p9

    move-object/from16 v11, p10

    move-object/from16 v12, p11

    move-object/from16 v13, p12

    move-object/from16 v14, p13

    move-object/from16 v15, p14

    move-object/from16 v16, p15

    move-object/from16 v17, p16

    move-object/from16 v18, p17

    move-object/from16 v19, p18

    move-object/from16 v20, p19

    move-object/from16 v21, p20

    move-object/from16 v22, p21

    move-object/from16 v23, p22

    move-object/from16 v24, p23

    move-object/from16 v25, p24

    move-object/from16 v26, p25

    move-object/from16 v27, p26

    move-object/from16 v28, p27

    move-object/from16 v29, p28

    move-object/from16 v30, p29

    move-object/from16 v31, p30

    move-object/from16 v32, p31

    move-object/from16 v33, p32

    move-object/from16 v34, p33

    move-object/from16 v35, p34

    move-object/from16 v36, p35

    move-object/from16 v37, p36

    move-object/from16 v38, p37

    move-object/from16 v39, p38

    move-object/from16 v40, p39

    move-object/from16 v41, p40

    move-object/from16 v42, p41

    move-object/from16 v43, p42

    move-object/from16 v44, p43

    move-object/from16 v45, p44

    move-object/from16 v46, p45

    move-object/from16 v47, p46

    move-object/from16 v48, p47

    move-object/from16 v49, p48

    move-object/from16 v50, p49

    invoke-direct/range {v0 .. v50}, Lorg/xbet/special_event/impl/tournament/presentation/x;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;ILwX0/c;Ljava/lang/String;Lorg/xbet/remoteconfig/domain/usecases/i;LHX0/e;LSX0/a;LVo/e;Lm8/a;Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;Lkc1/p;Ltn/a;Lvw0/a;LWo0/a;LRl0/b;LVg0/a;LIP/a;Lorg/xbet/remoteconfig/domain/usecases/k;LJZ/a;Lbl0/g;LNo0/b;Lss0/a;Lqs0/a;Los0/a;Lorg/xbet/ui_common/utils/M;LCw0/a;LDH0/a;LIu0/b;Lry0/a;Lorg/xbet/ui_common/utils/internet/a;LPx0/a;Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;LKs0/a;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/special_event/impl/teams/domain/usecase/d;LFy0/a;Llp0/a;Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;Llp0/i;Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;LuX0/a;Llp0/g;LFI/c;Llp0/e;LNo0/a;Llp0/c;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;
    .locals 52

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    move-object/from16 v40, p39

    .line 82
    .line 83
    move-object/from16 v41, p40

    .line 84
    .line 85
    move-object/from16 v42, p41

    .line 86
    .line 87
    move-object/from16 v43, p42

    .line 88
    .line 89
    move-object/from16 v44, p43

    .line 90
    .line 91
    move-object/from16 v45, p44

    .line 92
    .line 93
    move-object/from16 v46, p45

    .line 94
    .line 95
    move-object/from16 v47, p46

    .line 96
    .line 97
    move-object/from16 v48, p47

    .line 98
    .line 99
    move-object/from16 v49, p48

    .line 100
    .line 101
    move-object/from16 v50, p49

    .line 102
    .line 103
    move-object/from16 v51, p50

    .line 104
    .line 105
    invoke-direct/range {v0 .. v51}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;-><init>(Landroidx/lifecycle/Q;ILwX0/c;Ljava/lang/String;Lorg/xbet/remoteconfig/domain/usecases/i;LHX0/e;LSX0/a;LVo/e;Lm8/a;Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;Lkc1/p;Ltn/a;Lvw0/a;LWo0/a;LRl0/b;LVg0/a;LIP/a;Lorg/xbet/remoteconfig/domain/usecases/k;LJZ/a;Lbl0/g;LNo0/b;Lss0/a;Lqs0/a;Los0/a;Lorg/xbet/ui_common/utils/M;LCw0/a;LDH0/a;LIu0/b;Lry0/a;Lorg/xbet/ui_common/utils/internet/a;LPx0/a;Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;LKs0/a;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/special_event/impl/teams/domain/usecase/d;LFy0/a;Llp0/a;Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;Llp0/i;Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;LuX0/a;Llp0/g;LFI/c;Llp0/e;LNo0/a;Llp0/c;)V

    .line 106
    .line 107
    .line 108
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;
    .locals 53

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    check-cast v1, Ljava/lang/Integer;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->b:LBc/a;

    .line 16
    .line 17
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    move-object v4, v1

    .line 22
    check-cast v4, LwX0/c;

    .line 23
    .line 24
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->c:LBc/a;

    .line 25
    .line 26
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    move-object v5, v1

    .line 31
    check-cast v5, Ljava/lang/String;

    .line 32
    .line 33
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->d:LBc/a;

    .line 34
    .line 35
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    move-object v6, v1

    .line 40
    check-cast v6, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 41
    .line 42
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->e:LBc/a;

    .line 43
    .line 44
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    move-object v7, v1

    .line 49
    check-cast v7, LHX0/e;

    .line 50
    .line 51
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->f:LBc/a;

    .line 52
    .line 53
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    move-object v8, v1

    .line 58
    check-cast v8, LSX0/a;

    .line 59
    .line 60
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->g:LBc/a;

    .line 61
    .line 62
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    move-object v9, v1

    .line 67
    check-cast v9, LVo/e;

    .line 68
    .line 69
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->h:LBc/a;

    .line 70
    .line 71
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    move-object v10, v1

    .line 76
    check-cast v10, Lm8/a;

    .line 77
    .line 78
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->i:LBc/a;

    .line 79
    .line 80
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    move-object v11, v1

    .line 85
    check-cast v11, Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;

    .line 86
    .line 87
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->j:LBc/a;

    .line 88
    .line 89
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    move-object v12, v1

    .line 94
    check-cast v12, Lkc1/p;

    .line 95
    .line 96
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->k:LBc/a;

    .line 97
    .line 98
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    move-object v13, v1

    .line 103
    check-cast v13, Ltn/a;

    .line 104
    .line 105
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->l:LBc/a;

    .line 106
    .line 107
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    move-object v14, v1

    .line 112
    check-cast v14, Lvw0/a;

    .line 113
    .line 114
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->m:LBc/a;

    .line 115
    .line 116
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    move-object v15, v1

    .line 121
    check-cast v15, LWo0/a;

    .line 122
    .line 123
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->n:LBc/a;

    .line 124
    .line 125
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    move-object/from16 v16, v1

    .line 130
    .line 131
    check-cast v16, LRl0/b;

    .line 132
    .line 133
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->o:LBc/a;

    .line 134
    .line 135
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    move-object/from16 v17, v1

    .line 140
    .line 141
    check-cast v17, LVg0/a;

    .line 142
    .line 143
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->p:LBc/a;

    .line 144
    .line 145
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    move-object/from16 v18, v1

    .line 150
    .line 151
    check-cast v18, LIP/a;

    .line 152
    .line 153
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->q:LBc/a;

    .line 154
    .line 155
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 156
    .line 157
    .line 158
    move-result-object v1

    .line 159
    move-object/from16 v19, v1

    .line 160
    .line 161
    check-cast v19, Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 162
    .line 163
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->r:LBc/a;

    .line 164
    .line 165
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    move-object/from16 v20, v1

    .line 170
    .line 171
    check-cast v20, LJZ/a;

    .line 172
    .line 173
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->s:LBc/a;

    .line 174
    .line 175
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object v1

    .line 179
    move-object/from16 v21, v1

    .line 180
    .line 181
    check-cast v21, Lbl0/g;

    .line 182
    .line 183
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->t:LBc/a;

    .line 184
    .line 185
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    move-object/from16 v22, v1

    .line 190
    .line 191
    check-cast v22, LNo0/b;

    .line 192
    .line 193
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->u:LBc/a;

    .line 194
    .line 195
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v1

    .line 199
    move-object/from16 v23, v1

    .line 200
    .line 201
    check-cast v23, Lss0/a;

    .line 202
    .line 203
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->v:LBc/a;

    .line 204
    .line 205
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v1

    .line 209
    move-object/from16 v24, v1

    .line 210
    .line 211
    check-cast v24, Lqs0/a;

    .line 212
    .line 213
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->w:LBc/a;

    .line 214
    .line 215
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 216
    .line 217
    .line 218
    move-result-object v1

    .line 219
    move-object/from16 v25, v1

    .line 220
    .line 221
    check-cast v25, Los0/a;

    .line 222
    .line 223
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->x:LBc/a;

    .line 224
    .line 225
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    move-object/from16 v26, v1

    .line 230
    .line 231
    check-cast v26, Lorg/xbet/ui_common/utils/M;

    .line 232
    .line 233
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->y:LBc/a;

    .line 234
    .line 235
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 236
    .line 237
    .line 238
    move-result-object v1

    .line 239
    move-object/from16 v27, v1

    .line 240
    .line 241
    check-cast v27, LCw0/a;

    .line 242
    .line 243
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->z:LBc/a;

    .line 244
    .line 245
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 246
    .line 247
    .line 248
    move-result-object v1

    .line 249
    move-object/from16 v28, v1

    .line 250
    .line 251
    check-cast v28, LDH0/a;

    .line 252
    .line 253
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->A:LBc/a;

    .line 254
    .line 255
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 256
    .line 257
    .line 258
    move-result-object v1

    .line 259
    move-object/from16 v29, v1

    .line 260
    .line 261
    check-cast v29, LIu0/b;

    .line 262
    .line 263
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->B:LBc/a;

    .line 264
    .line 265
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object v1

    .line 269
    move-object/from16 v30, v1

    .line 270
    .line 271
    check-cast v30, Lry0/a;

    .line 272
    .line 273
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->C:LBc/a;

    .line 274
    .line 275
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 276
    .line 277
    .line 278
    move-result-object v1

    .line 279
    move-object/from16 v31, v1

    .line 280
    .line 281
    check-cast v31, Lorg/xbet/ui_common/utils/internet/a;

    .line 282
    .line 283
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->D:LBc/a;

    .line 284
    .line 285
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object v1

    .line 289
    move-object/from16 v32, v1

    .line 290
    .line 291
    check-cast v32, LPx0/a;

    .line 292
    .line 293
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->E:LBc/a;

    .line 294
    .line 295
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 296
    .line 297
    .line 298
    move-result-object v1

    .line 299
    move-object/from16 v33, v1

    .line 300
    .line 301
    check-cast v33, Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;

    .line 302
    .line 303
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->F:LBc/a;

    .line 304
    .line 305
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 306
    .line 307
    .line 308
    move-result-object v1

    .line 309
    move-object/from16 v34, v1

    .line 310
    .line 311
    check-cast v34, LKs0/a;

    .line 312
    .line 313
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->G:LBc/a;

    .line 314
    .line 315
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 316
    .line 317
    .line 318
    move-result-object v1

    .line 319
    move-object/from16 v35, v1

    .line 320
    .line 321
    check-cast v35, Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    .line 322
    .line 323
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->H:LBc/a;

    .line 324
    .line 325
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 326
    .line 327
    .line 328
    move-result-object v1

    .line 329
    move-object/from16 v36, v1

    .line 330
    .line 331
    check-cast v36, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 332
    .line 333
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->I:LBc/a;

    .line 334
    .line 335
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 336
    .line 337
    .line 338
    move-result-object v1

    .line 339
    move-object/from16 v37, v1

    .line 340
    .line 341
    check-cast v37, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 342
    .line 343
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->J:LBc/a;

    .line 344
    .line 345
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 346
    .line 347
    .line 348
    move-result-object v1

    .line 349
    move-object/from16 v38, v1

    .line 350
    .line 351
    check-cast v38, Lp9/c;

    .line 352
    .line 353
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->K:LBc/a;

    .line 354
    .line 355
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 356
    .line 357
    .line 358
    move-result-object v1

    .line 359
    move-object/from16 v39, v1

    .line 360
    .line 361
    check-cast v39, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 362
    .line 363
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->L:LBc/a;

    .line 364
    .line 365
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 366
    .line 367
    .line 368
    move-result-object v1

    .line 369
    move-object/from16 v40, v1

    .line 370
    .line 371
    check-cast v40, Lorg/xbet/special_event/impl/teams/domain/usecase/d;

    .line 372
    .line 373
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->M:LBc/a;

    .line 374
    .line 375
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 376
    .line 377
    .line 378
    move-result-object v1

    .line 379
    move-object/from16 v41, v1

    .line 380
    .line 381
    check-cast v41, LFy0/a;

    .line 382
    .line 383
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->N:LBc/a;

    .line 384
    .line 385
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 386
    .line 387
    .line 388
    move-result-object v1

    .line 389
    move-object/from16 v42, v1

    .line 390
    .line 391
    check-cast v42, Llp0/a;

    .line 392
    .line 393
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->O:LBc/a;

    .line 394
    .line 395
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 396
    .line 397
    .line 398
    move-result-object v1

    .line 399
    move-object/from16 v43, v1

    .line 400
    .line 401
    check-cast v43, Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;

    .line 402
    .line 403
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->P:LBc/a;

    .line 404
    .line 405
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 406
    .line 407
    .line 408
    move-result-object v1

    .line 409
    move-object/from16 v44, v1

    .line 410
    .line 411
    check-cast v44, Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;

    .line 412
    .line 413
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->Q:LBc/a;

    .line 414
    .line 415
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 416
    .line 417
    .line 418
    move-result-object v1

    .line 419
    move-object/from16 v45, v1

    .line 420
    .line 421
    check-cast v45, Llp0/i;

    .line 422
    .line 423
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->R:LBc/a;

    .line 424
    .line 425
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 426
    .line 427
    .line 428
    move-result-object v1

    .line 429
    move-object/from16 v46, v1

    .line 430
    .line 431
    check-cast v46, Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;

    .line 432
    .line 433
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->S:LBc/a;

    .line 434
    .line 435
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 436
    .line 437
    .line 438
    move-result-object v1

    .line 439
    move-object/from16 v47, v1

    .line 440
    .line 441
    check-cast v47, LuX0/a;

    .line 442
    .line 443
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->T:LBc/a;

    .line 444
    .line 445
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 446
    .line 447
    .line 448
    move-result-object v1

    .line 449
    move-object/from16 v48, v1

    .line 450
    .line 451
    check-cast v48, Llp0/g;

    .line 452
    .line 453
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->U:LBc/a;

    .line 454
    .line 455
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 456
    .line 457
    .line 458
    move-result-object v1

    .line 459
    move-object/from16 v49, v1

    .line 460
    .line 461
    check-cast v49, LFI/c;

    .line 462
    .line 463
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->V:LBc/a;

    .line 464
    .line 465
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 466
    .line 467
    .line 468
    move-result-object v1

    .line 469
    move-object/from16 v50, v1

    .line 470
    .line 471
    check-cast v50, Llp0/e;

    .line 472
    .line 473
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->W:LBc/a;

    .line 474
    .line 475
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 476
    .line 477
    .line 478
    move-result-object v1

    .line 479
    move-object/from16 v51, v1

    .line 480
    .line 481
    check-cast v51, LNo0/a;

    .line 482
    .line 483
    iget-object v1, v0, Lorg/xbet/special_event/impl/tournament/presentation/x;->X:LBc/a;

    .line 484
    .line 485
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 486
    .line 487
    .line 488
    move-result-object v1

    .line 489
    move-object/from16 v52, v1

    .line 490
    .line 491
    check-cast v52, Llp0/c;

    .line 492
    .line 493
    move-object/from16 v2, p1

    .line 494
    .line 495
    invoke-static/range {v2 .. v52}, Lorg/xbet/special_event/impl/tournament/presentation/x;->c(Landroidx/lifecycle/Q;ILwX0/c;Ljava/lang/String;Lorg/xbet/remoteconfig/domain/usecases/i;LHX0/e;LSX0/a;LVo/e;Lm8/a;Lorg/xbet/special_event/impl/teams/domain/usecase/GetTeamsStreamUseCase;Lkc1/p;Ltn/a;Lvw0/a;LWo0/a;LRl0/b;LVg0/a;LIP/a;Lorg/xbet/remoteconfig/domain/usecases/k;LJZ/a;Lbl0/g;LNo0/b;Lss0/a;Lqs0/a;Los0/a;Lorg/xbet/ui_common/utils/M;LCw0/a;LDH0/a;LIu0/b;Lry0/a;Lorg/xbet/ui_common/utils/internet/a;LPx0/a;Lorg/xbet/special_event/impl/medal_statistic/domain/uesecases/GetChampTop10MedalsUseCase;LKs0/a;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/special_event/impl/teams/domain/usecase/d;LFy0/a;Llp0/a;Lorg/xbet/special_event/impl/cyber_info/dota/domain/popular_heroes/usecase/GetDotaPopularHeroesStreamUseCase;Lorg/xbet/special_event/impl/cyber_info/lol/domain/popular_champions/usecase/GetLolPopularChampionsStreamUseCase;Llp0/i;Lorg/xbet/special_event/impl/cyber_info/cs/domain/usecase/GetCSMapStatisticStreamUseCase;LuX0/a;Llp0/g;LFI/c;Llp0/e;LNo0/a;Llp0/c;)Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 496
    .line 497
    .line 498
    move-result-object v1

    .line 499
    return-object v1
.end method
