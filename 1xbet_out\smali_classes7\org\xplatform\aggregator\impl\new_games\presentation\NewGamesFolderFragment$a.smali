.class public final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0006\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001d\u0010\t\u001a\u00020\u00082\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\rR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0011\u001a\u00020\u000e8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0010R\u0014\u0010\u0012\u001a\u00020\u000e8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0010R\u0014\u0010\u0013\u001a\u00020\u000e8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0010\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "aggregatorScreenModel",
        "",
        "fromSearch",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;",
        "a",
        "(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;",
        "",
        "LOTTIE_RETRY_COUNT_DOWN_TIME_MILLIS",
        "J",
        "",
        "AGGREGATOR_SCREEN_ITEM",
        "Ljava/lang/String;",
        "FROM_AGGREGATOR_SEARCH_ITEM",
        "VIRTUAL_CATEGORY",
        "BANNER_STATE_KEY",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Z)Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;
    .locals 1
    .param p1    # Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->x3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->y3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Z)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method
