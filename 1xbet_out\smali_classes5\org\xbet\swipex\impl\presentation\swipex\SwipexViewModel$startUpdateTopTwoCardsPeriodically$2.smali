.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$startUpdateTopTwoCardsPeriodically$2"
    f = "SwipexViewModel.kt"
    l = {
        0x1d7,
        0x1e7
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->L4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;

    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast v1, Lkotlinx/coroutines/N;

    .line 30
    .line 31
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    move-object v1, p1

    .line 41
    check-cast v1, Lkotlinx/coroutines/N;

    .line 42
    .line 43
    iput-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    iput v3, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->label:I

    .line 46
    .line 47
    const-wide/16 v3, 0x1f40

    .line 48
    .line 49
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    if-ne p1, v0, :cond_3

    .line 54
    .line 55
    goto :goto_1

    .line 56
    :cond_3
    :goto_0
    sget-object p1, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    .line 57
    .line 58
    new-instance v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;

    .line 59
    .line 60
    iget-object v4, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 61
    .line 62
    const/4 v5, 0x0

    .line 63
    invoke-direct {v3, v1, v4, v5}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;-><init>(Lkotlinx/coroutines/N;Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V

    .line 64
    .line 65
    .line 66
    const-wide/16 v6, 0x8

    .line 67
    .line 68
    invoke-static {v6, v7, p1, v3}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->b(JLjava/util/concurrent/TimeUnit;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object v5, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->L$0:Ljava/lang/Object;

    .line 73
    .line 74
    iput v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->label:I

    .line 75
    .line 76
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->m(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    if-ne p1, v0, :cond_4

    .line 81
    .line 82
    :goto_1
    return-object v0

    .line 83
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 84
    .line 85
    return-object p1
.end method
