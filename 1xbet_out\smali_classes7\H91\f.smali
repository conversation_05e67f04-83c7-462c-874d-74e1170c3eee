.class public final LH91/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a;\u0010\u000b\u001a\u00020\n*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u001d\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\u00012\u0006\u0010\r\u001a\u00020\u0002H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LD91/a;",
        "",
        "LD91/b;",
        "savedFilters",
        "",
        "virtual",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "providerStyle",
        "Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;",
        "filterScreenStyleType",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;",
        "b",
        "(LD91/a;Ljava/util/List;ZLorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;)Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;",
        "category",
        "Lg81/f;",
        "a",
        "(LD91/b;)Ljava/util/List;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LD91/b;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD91/b;",
            ")",
            "Ljava/util/List<",
            "Lg81/f;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LD91/b;->d()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    new-instance v0, LD91/c;

    .line 6
    .line 7
    const-string v1, "0"

    .line 8
    .line 9
    const-string v2, ""

    .line 10
    .line 11
    invoke-direct {v0, v1, v2}, LD91/c;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {p0, v0}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    return-object p0
.end method

.method public static final b(LD91/a;Ljava/util/List;ZLorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;)Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;
    .locals 31
    .param p0    # LD91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LD91/a;",
            "Ljava/util/List<",
            "LD91/b;",
            ">;Z",
            "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
            "Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;",
            ")",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-virtual/range {p0 .. p0}, LD91/a;->d()J

    .line 3
    .line 4
    .line 5
    move-result-wide v1

    .line 6
    invoke-virtual/range {p0 .. p0}, LD91/a;->c()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v3

    .line 10
    new-instance v4, Ljava/util/ArrayList;

    .line 11
    .line 12
    const/16 v5, 0xa

    .line 13
    .line 14
    invoke-static {v3, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 15
    .line 16
    .line 17
    move-result v6

    .line 18
    invoke-direct {v4, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 19
    .line 20
    .line 21
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    const/4 v7, 0x0

    .line 26
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v8

    .line 30
    if-eqz v8, :cond_10

    .line 31
    .line 32
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v8

    .line 36
    add-int/lit8 v9, v7, 0x1

    .line 37
    .line 38
    if-gez v7, :cond_0

    .line 39
    .line 40
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 41
    .line 42
    .line 43
    :cond_0
    check-cast v8, LD91/b;

    .line 44
    .line 45
    invoke-interface/range {p1 .. p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 46
    .line 47
    .line 48
    move-result-object v10

    .line 49
    :cond_1
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 50
    .line 51
    .line 52
    move-result v11

    .line 53
    if-eqz v11, :cond_2

    .line 54
    .line 55
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v11

    .line 59
    move-object v13, v11

    .line 60
    check-cast v13, LD91/b;

    .line 61
    .line 62
    invoke-virtual {v13}, LD91/b;->e()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v13

    .line 66
    invoke-virtual {v8}, LD91/b;->e()Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v14

    .line 70
    invoke-static {v13, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 71
    .line 72
    .line 73
    move-result v13

    .line 74
    if-eqz v13, :cond_1

    .line 75
    .line 76
    goto :goto_1

    .line 77
    :cond_2
    const/4 v11, 0x0

    .line 78
    :goto_1
    check-cast v11, LD91/b;

    .line 79
    .line 80
    invoke-virtual/range {p0 .. p0}, LD91/a;->c()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v10

    .line 84
    invoke-interface {v10}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 85
    .line 86
    .line 87
    move-result-object v10

    .line 88
    const/4 v13, 0x0

    .line 89
    :goto_2
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 90
    .line 91
    .line 92
    move-result v14

    .line 93
    if-eqz v14, :cond_4

    .line 94
    .line 95
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v14

    .line 99
    check-cast v14, LD91/b;

    .line 100
    .line 101
    invoke-virtual {v14}, LD91/b;->f()Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 102
    .line 103
    .line 104
    move-result-object v14

    .line 105
    sget-object v15, Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;->FILTERS:Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 106
    .line 107
    if-ne v14, v15, :cond_3

    .line 108
    .line 109
    goto :goto_3

    .line 110
    :cond_3
    add-int/2addr v13, v0

    .line 111
    goto :goto_2

    .line 112
    :cond_4
    const/4 v13, -0x1

    .line 113
    :goto_3
    if-ne v7, v13, :cond_5

    .line 114
    .line 115
    invoke-static {v8}, LH91/f;->a(LD91/b;)Ljava/util/List;

    .line 116
    .line 117
    .line 118
    move-result-object v10

    .line 119
    goto :goto_4

    .line 120
    :cond_5
    invoke-virtual {v8}, LD91/b;->d()Ljava/util/List;

    .line 121
    .line 122
    .line 123
    move-result-object v10

    .line 124
    :goto_4
    if-ne v7, v13, :cond_6

    .line 125
    .line 126
    sget-object v7, Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;->SMALL_HEADER:Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;

    .line 127
    .line 128
    move-object/from16 v13, p4

    .line 129
    .line 130
    if-eq v13, v7, :cond_7

    .line 131
    .line 132
    const/4 v7, 0x1

    .line 133
    goto :goto_5

    .line 134
    :cond_6
    move-object/from16 v13, p4

    .line 135
    .line 136
    :cond_7
    const/4 v7, 0x0

    .line 137
    :goto_5
    invoke-virtual {v8}, LD91/b;->e()Ljava/lang/String;

    .line 138
    .line 139
    .line 140
    move-result-object v14

    .line 141
    invoke-virtual {v8}, LD91/b;->c()Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v15

    .line 145
    const/16 v16, 0x0

    .line 146
    .line 147
    invoke-virtual {v8}, LD91/b;->f()Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 148
    .line 149
    .line 150
    move-result-object v6

    .line 151
    new-instance v12, Ljava/util/ArrayList;

    .line 152
    .line 153
    invoke-static {v10, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 154
    .line 155
    .line 156
    move-result v0

    .line 157
    invoke-direct {v12, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 158
    .line 159
    .line 160
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    :goto_6
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 165
    .line 166
    .line 167
    move-result v10

    .line 168
    if-eqz v10, :cond_f

    .line 169
    .line 170
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object v10

    .line 174
    check-cast v10, Lg81/f;

    .line 175
    .line 176
    invoke-virtual {v8}, LD91/b;->f()Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 177
    .line 178
    .line 179
    move-result-object v5

    .line 180
    move-object/from16 v18, v0

    .line 181
    .line 182
    sget-object v0, Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;->PROVIDERS:Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 183
    .line 184
    if-ne v5, v0, :cond_c

    .line 185
    .line 186
    instance-of v0, v10, Lg81/j;

    .line 187
    .line 188
    if-eqz v0, :cond_c

    .line 189
    .line 190
    if-eqz v11, :cond_a

    .line 191
    .line 192
    invoke-virtual {v11}, LD91/b;->d()Ljava/util/List;

    .line 193
    .line 194
    .line 195
    move-result-object v0

    .line 196
    if-eqz v0, :cond_a

    .line 197
    .line 198
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 199
    .line 200
    .line 201
    move-result-object v0

    .line 202
    :goto_7
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 203
    .line 204
    .line 205
    move-result v5

    .line 206
    if-eqz v5, :cond_9

    .line 207
    .line 208
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 209
    .line 210
    .line 211
    move-result-object v5

    .line 212
    move-object/from16 v19, v5

    .line 213
    .line 214
    check-cast v19, Lg81/f;

    .line 215
    .line 216
    move-object/from16 v20, v0

    .line 217
    .line 218
    invoke-interface/range {v19 .. v19}, Lg81/f;->getId()Ljava/lang/String;

    .line 219
    .line 220
    .line 221
    move-result-object v0

    .line 222
    move-object/from16 v19, v10

    .line 223
    .line 224
    check-cast v19, Lg81/j;

    .line 225
    .line 226
    move-object/from16 v21, v3

    .line 227
    .line 228
    invoke-virtual/range {v19 .. v19}, Lg81/j;->getId()Ljava/lang/String;

    .line 229
    .line 230
    .line 231
    move-result-object v3

    .line 232
    invoke-static {v0, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 233
    .line 234
    .line 235
    move-result v0

    .line 236
    if-eqz v0, :cond_8

    .line 237
    .line 238
    goto :goto_8

    .line 239
    :cond_8
    move-object/from16 v0, v20

    .line 240
    .line 241
    move-object/from16 v3, v21

    .line 242
    .line 243
    goto :goto_7

    .line 244
    :cond_9
    move-object/from16 v21, v3

    .line 245
    .line 246
    const/4 v5, 0x0

    .line 247
    :goto_8
    check-cast v5, Lg81/f;

    .line 248
    .line 249
    goto :goto_9

    .line 250
    :cond_a
    move-object/from16 v21, v3

    .line 251
    .line 252
    const/4 v5, 0x0

    .line 253
    :goto_9
    if-eqz v5, :cond_b

    .line 254
    .line 255
    const/16 v25, 0x1

    .line 256
    .line 257
    goto :goto_a

    .line 258
    :cond_b
    const/16 v25, 0x0

    .line 259
    .line 260
    :goto_a
    check-cast v10, Lg81/j;

    .line 261
    .line 262
    invoke-virtual {v10}, Lg81/j;->getId()Ljava/lang/String;

    .line 263
    .line 264
    .line 265
    move-result-object v23

    .line 266
    invoke-virtual {v10}, Lg81/j;->getName()Ljava/lang/String;

    .line 267
    .line 268
    .line 269
    move-result-object v24

    .line 270
    invoke-virtual {v10}, Lg81/j;->d()Ljava/lang/String;

    .line 271
    .line 272
    .line 273
    move-result-object v27

    .line 274
    invoke-virtual {v10}, Lg81/j;->g()Ljava/lang/String;

    .line 275
    .line 276
    .line 277
    move-result-object v28

    .line 278
    new-instance v22, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 279
    .line 280
    const/16 v26, 0x0

    .line 281
    .line 282
    move/from16 v29, p2

    .line 283
    .line 284
    move-object/from16 v30, p3

    .line 285
    .line 286
    invoke-direct/range {v22 .. v30}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;)V

    .line 287
    .line 288
    .line 289
    move/from16 v17, v7

    .line 290
    .line 291
    move-object/from16 v20, v8

    .line 292
    .line 293
    move-object/from16 v3, v22

    .line 294
    .line 295
    goto :goto_d

    .line 296
    :cond_c
    move-object/from16 v21, v3

    .line 297
    .line 298
    if-eqz v7, :cond_d

    .line 299
    .line 300
    new-instance v0, Ln8/a;

    .line 301
    .line 302
    invoke-direct {v0}, Ln8/a;-><init>()V

    .line 303
    .line 304
    .line 305
    sget-object v3, Lkotlin/jvm/internal/D;->a:Lkotlin/jvm/internal/D;

    .line 306
    .line 307
    sget-object v3, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    .line 308
    .line 309
    invoke-interface {v10}, Lg81/f;->getId()Ljava/lang/String;

    .line 310
    .line 311
    .line 312
    move-result-object v5

    .line 313
    move-object/from16 v19, v5

    .line 314
    .line 315
    move/from16 v17, v7

    .line 316
    .line 317
    const/4 v5, 0x1

    .line 318
    new-array v7, v5, [Ljava/lang/Object;

    .line 319
    .line 320
    aput-object v19, v7, v16

    .line 321
    .line 322
    invoke-static {v7, v5}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 323
    .line 324
    .line 325
    move-result-object v7

    .line 326
    const-string v5, "/static/img/android/casino/alt_design/filter_screen_style/%s.svg"

    .line 327
    .line 328
    invoke-static {v3, v5, v7}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 329
    .line 330
    .line 331
    move-result-object v3

    .line 332
    invoke-virtual {v0, v3}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 333
    .line 334
    .line 335
    move-result-object v0

    .line 336
    invoke-virtual {v0}, Ln8/a;->a()Ljava/lang/String;

    .line 337
    .line 338
    .line 339
    move-result-object v0

    .line 340
    goto :goto_b

    .line 341
    :cond_d
    move/from16 v17, v7

    .line 342
    .line 343
    const/4 v0, 0x0

    .line 344
    :goto_b
    new-instance v3, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterUiModel;

    .line 345
    .line 346
    invoke-interface {v10}, Lg81/f;->getId()Ljava/lang/String;

    .line 347
    .line 348
    .line 349
    move-result-object v5

    .line 350
    invoke-interface {v10}, Lg81/f;->getName()Ljava/lang/String;

    .line 351
    .line 352
    .line 353
    move-result-object v7

    .line 354
    move-object/from16 v20, v8

    .line 355
    .line 356
    if-eqz v11, :cond_e

    .line 357
    .line 358
    invoke-virtual {v11}, LD91/b;->d()Ljava/util/List;

    .line 359
    .line 360
    .line 361
    move-result-object v8

    .line 362
    if-eqz v8, :cond_e

    .line 363
    .line 364
    invoke-interface {v8, v10}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 365
    .line 366
    .line 367
    move-result v8

    .line 368
    goto :goto_c

    .line 369
    :cond_e
    const/4 v8, 0x0

    .line 370
    :goto_c
    invoke-direct {v3, v5, v7, v8, v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;)V

    .line 371
    .line 372
    .line 373
    :goto_d
    invoke-interface {v12, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 374
    .line 375
    .line 376
    move/from16 v7, v17

    .line 377
    .line 378
    move-object/from16 v0, v18

    .line 379
    .line 380
    move-object/from16 v8, v20

    .line 381
    .line 382
    move-object/from16 v3, v21

    .line 383
    .line 384
    const/16 v5, 0xa

    .line 385
    .line 386
    goto/16 :goto_6

    .line 387
    .line 388
    :cond_f
    move-object/from16 v21, v3

    .line 389
    .line 390
    new-instance v0, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;

    .line 391
    .line 392
    invoke-direct {v0, v14, v15, v6, v12}, Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;Ljava/util/List;)V

    .line 393
    .line 394
    .line 395
    invoke-interface {v4, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 396
    .line 397
    .line 398
    move v7, v9

    .line 399
    const/4 v0, 0x1

    .line 400
    const/16 v5, 0xa

    .line 401
    .line 402
    goto/16 :goto_0

    .line 403
    .line 404
    :cond_10
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 405
    .line 406
    .line 407
    move-result-object v0

    .line 408
    new-instance v3, Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;

    .line 409
    .line 410
    invoke-direct {v3, v1, v2, v4, v0}, Lorg/xplatform/aggregator/impl/category/presentation/models/AggregatorProvidersFiltersUiModel;-><init>(JLjava/util/List;Ljava/util/List;)V

    .line 411
    .line 412
    .line 413
    return-object v3
.end method
