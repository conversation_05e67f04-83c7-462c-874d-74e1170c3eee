.class public final LtW0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/i;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtW0/a$b$b;,
        LtW0/a$b$a;
    }
.end annotation


# instance fields
.field public A:Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtW0/i$b;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public final b:LtW0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/i;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/e;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/scenario/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/o;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/m;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtW0/a$b;->b:LtW0/a$b;

    .line 4
    iput-object p3, p0, LtW0/a$b;->a:LTZ0/a;

    .line 5
    invoke-virtual/range {p0 .. p25}, LtW0/a$b;->b(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V

    .line 6
    invoke-virtual/range {p0 .. p25}, LtW0/a$b;->c(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;LtW0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p25}, LtW0/a$b;-><init>(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtW0/a$b;->d(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iput-object v1, v0, LtW0/a$b;->c:Ldagger/internal/h;

    .line 8
    .line 9
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iput-object v1, v0, LtW0/a$b;->d:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iput-object v1, v0, LtW0/a$b;->e:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    iput-object v1, v0, LtW0/a$b;->f:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iput-object v1, v0, LtW0/a$b;->g:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iput-object v1, v0, LtW0/a$b;->h:Ldagger/internal/h;

    .line 38
    .line 39
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    iput-object v1, v0, LtW0/a$b;->i:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    iput-object v1, v0, LtW0/a$b;->j:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iput-object v1, v0, LtW0/a$b;->k:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    iput-object v1, v0, LtW0/a$b;->l:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    iput-object v1, v0, LtW0/a$b;->m:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, v0, LtW0/a$b;->n:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    iput-object v1, v0, LtW0/a$b;->o:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    iput-object v1, v0, LtW0/a$b;->p:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    iput-object v1, v0, LtW0/a$b;->q:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    iput-object v1, v0, LtW0/a$b;->r:Ldagger/internal/h;

    .line 98
    .line 99
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    iput-object v1, v0, LtW0/a$b;->s:Ldagger/internal/h;

    .line 104
    .line 105
    invoke-static/range {p4 .. p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 106
    .line 107
    .line 108
    move-result-object v1

    .line 109
    iput-object v1, v0, LtW0/a$b;->t:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static/range {p5 .. p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    iput-object v1, v0, LtW0/a$b;->u:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    iput-object v1, v0, LtW0/a$b;->v:Ldagger/internal/h;

    .line 122
    .line 123
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    iput-object v1, v0, LtW0/a$b;->w:Ldagger/internal/h;

    .line 128
    .line 129
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 130
    .line 131
    .line 132
    move-result-object v1

    .line 133
    iput-object v1, v0, LtW0/a$b;->x:Ldagger/internal/h;

    .line 134
    .line 135
    new-instance v1, LtW0/a$b$b;

    .line 136
    .line 137
    move-object/from16 v2, p1

    .line 138
    .line 139
    invoke-direct {v1, v2}, LtW0/a$b$b;-><init>(LQW0/c;)V

    .line 140
    .line 141
    .line 142
    iput-object v1, v0, LtW0/a$b;->y:Ldagger/internal/h;

    .line 143
    .line 144
    new-instance v1, LtW0/a$b$a;

    .line 145
    .line 146
    move-object/from16 v2, p2

    .line 147
    .line 148
    invoke-direct {v1, v2}, LtW0/a$b$a;-><init>(Lak/a;)V

    .line 149
    .line 150
    .line 151
    iput-object v1, v0, LtW0/a$b;->z:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v2, v0, LtW0/a$b;->c:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object v3, v0, LtW0/a$b;->d:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object v4, v0, LtW0/a$b;->e:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v5, v0, LtW0/a$b;->f:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object v6, v0, LtW0/a$b;->g:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object v7, v0, LtW0/a$b;->h:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object v8, v0, LtW0/a$b;->i:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v9, v0, LtW0/a$b;->j:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v10, v0, LtW0/a$b;->k:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v11, v0, LtW0/a$b;->l:Ldagger/internal/h;

    .line 172
    .line 173
    iget-object v12, v0, LtW0/a$b;->m:Ldagger/internal/h;

    .line 174
    .line 175
    iget-object v13, v0, LtW0/a$b;->n:Ldagger/internal/h;

    .line 176
    .line 177
    iget-object v14, v0, LtW0/a$b;->o:Ldagger/internal/h;

    .line 178
    .line 179
    iget-object v15, v0, LtW0/a$b;->p:Ldagger/internal/h;

    .line 180
    .line 181
    move-object/from16 p24, v1

    .line 182
    .line 183
    iget-object v1, v0, LtW0/a$b;->q:Ldagger/internal/h;

    .line 184
    .line 185
    move-object/from16 p15, v1

    .line 186
    .line 187
    iget-object v1, v0, LtW0/a$b;->r:Ldagger/internal/h;

    .line 188
    .line 189
    move-object/from16 p16, v1

    .line 190
    .line 191
    iget-object v1, v0, LtW0/a$b;->s:Ldagger/internal/h;

    .line 192
    .line 193
    move-object/from16 p17, v1

    .line 194
    .line 195
    iget-object v1, v0, LtW0/a$b;->t:Ldagger/internal/h;

    .line 196
    .line 197
    move-object/from16 p18, v1

    .line 198
    .line 199
    iget-object v1, v0, LtW0/a$b;->u:Ldagger/internal/h;

    .line 200
    .line 201
    move-object/from16 p19, v1

    .line 202
    .line 203
    iget-object v1, v0, LtW0/a$b;->v:Ldagger/internal/h;

    .line 204
    .line 205
    move-object/from16 p20, v1

    .line 206
    .line 207
    iget-object v1, v0, LtW0/a$b;->w:Ldagger/internal/h;

    .line 208
    .line 209
    move-object/from16 p21, v1

    .line 210
    .line 211
    iget-object v1, v0, LtW0/a$b;->x:Ldagger/internal/h;

    .line 212
    .line 213
    move-object/from16 p22, v1

    .line 214
    .line 215
    iget-object v1, v0, LtW0/a$b;->y:Ldagger/internal/h;

    .line 216
    .line 217
    move-object/from16 p23, v1

    .line 218
    .line 219
    move-object/from16 p1, v2

    .line 220
    .line 221
    move-object/from16 p2, v3

    .line 222
    .line 223
    move-object/from16 p3, v4

    .line 224
    .line 225
    move-object/from16 p4, v5

    .line 226
    .line 227
    move-object/from16 p5, v6

    .line 228
    .line 229
    move-object/from16 p6, v7

    .line 230
    .line 231
    move-object/from16 p7, v8

    .line 232
    .line 233
    move-object/from16 p8, v9

    .line 234
    .line 235
    move-object/from16 p9, v10

    .line 236
    .line 237
    move-object/from16 p10, v11

    .line 238
    .line 239
    move-object/from16 p11, v12

    .line 240
    .line 241
    move-object/from16 p12, v13

    .line 242
    .line 243
    move-object/from16 p13, v14

    .line 244
    .line 245
    move-object/from16 p14, v15

    .line 246
    .line 247
    invoke-static/range {p1 .. p24}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;

    .line 248
    .line 249
    .line 250
    move-result-object v1

    .line 251
    iput-object v1, v0, LtW0/a$b;->A:Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;

    .line 252
    .line 253
    return-void
.end method

.method public final c(LQW0/c;Lak/a;LTZ0/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/j;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;LwW0/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/f;Lorg/xbet/toto_jackpot/impl/domain/scenario/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/h;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/n;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/GetJackpotTiragUseCase;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/c;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/l;LwW0/i;LwW0/o;LwW0/m;LHX0/e;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;)V
    .locals 0

    .line 1
    iget-object p1, p0, LtW0/a$b;->A:Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;

    .line 2
    .line 3
    invoke-static {p1}, LtW0/l;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/u;)Ldagger/internal/h;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iput-object p1, p0, LtW0/a$b;->B:Ldagger/internal/h;

    .line 8
    .line 9
    return-void
.end method

.method public final d(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LtW0/a$b;->B:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LtW0/i$b;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/n;->b(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;LtW0/i$b;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LtW0/a$b;->a:LTZ0/a;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/n;->a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/jackpot/TotoJackpotFragment;LTZ0/a;)V

    .line 15
    .line 16
    .line 17
    return-object p1
.end method
