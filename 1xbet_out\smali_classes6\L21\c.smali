.class public final synthetic LL21/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL21/c;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LL21/c;->a:Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;->g(Lorg/xbet/uikit_aggregator/aggregatordailymissionswidget/stateView/DSAggregatorDailyMissionsWidgetCurrentActive;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
