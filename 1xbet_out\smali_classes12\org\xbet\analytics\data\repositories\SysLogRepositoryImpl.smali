.class public final Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lxg/g;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$a;,
        Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00d4\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\t\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0010\t\n\u0002\u0008\u0011\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u00084\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u0000 _2\u00020\u0001:\u0002wrBy\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u000e\u0008\u0001\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u001f\u0010#\u001a\u00020\"2\u0006\u0010\u001f\u001a\u00020\u001b2\u0006\u0010!\u001a\u00020 H\u0002\u00a2\u0006\u0004\u0008#\u0010$J$\u0010%\u001a\u00020\"*\u00020 2\u0006\u0010\u001f\u001a\u00020\u001b2\u0006\u0010!\u001a\u00020 H\u0082@\u00a2\u0006\u0004\u0008%\u0010&J\u0018\u0010)\u001a\u00020\"2\u0006\u0010(\u001a\u00020\'H\u0082@\u00a2\u0006\u0004\u0008)\u0010*J\u0017\u0010-\u001a\u00060,R\u00020\u0000*\u00020+H\u0002\u00a2\u0006\u0004\u0008-\u0010.J%\u00101\u001a\u00020\u001b*\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u001b000/H\u0002\u00a2\u0006\u0004\u00081\u00102J\u0013\u00105\u001a\u000204*\u000203H\u0002\u00a2\u0006\u0004\u00085\u00106J\u0017\u00108\u001a\u00020 2\u0006\u00107\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010;\u001a\u00020\u001b2\u0006\u0010:\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008;\u0010<J-\u0010A\u001a\u00020\"2\u001c\u0010@\u001a\u0018\u0008\u0001\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\"0>\u0012\u0006\u0012\u0004\u0018\u00010?0=H\u0002\u00a2\u0006\u0004\u0008A\u0010BJ\u000f\u0010C\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008C\u0010DJ\u000f\u0010E\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008E\u0010DJ\u000f\u0010F\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008F\u0010DJ\u000f\u0010G\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008G\u0010DJ\u000f\u0010H\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008H\u0010DJ\u000f\u0010J\u001a\u00020IH\u0002\u00a2\u0006\u0004\u0008J\u0010KJ\u000f\u0010L\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008L\u0010MJ\u0017\u0010O\u001a\u00020\u001b2\u0006\u0010N\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008O\u0010<J/\u0010U\u001a\u00020\"2\u0006\u0010Q\u001a\u00020P2\u0006\u0010R\u001a\u00020\u001b2\u0006\u0010S\u001a\u00020\u001b2\u0006\u0010T\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008U\u0010VJ7\u0010X\u001a\u00020\"2\u0006\u0010Q\u001a\u00020P2\u0006\u0010S\u001a\u00020\u001b2\u0006\u0010T\u001a\u00020\u001b2\u0006\u0010R\u001a\u00020\u001b2\u0006\u0010W\u001a\u00020IH\u0016\u00a2\u0006\u0004\u0008X\u0010YJ\u001d\u0010[\u001a\u00020\"2\u000c\u0010Z\u001a\u0008\u0012\u0004\u0012\u00020\u001b0/H\u0016\u00a2\u0006\u0004\u0008[\u0010\\J\'\u0010_\u001a\u00020\"2\u0006\u0010]\u001a\u00020\u001b2\u0006\u0010^\u001a\u00020\u001b2\u0006\u0010\u001f\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008_\u0010`J5\u0010g\u001a\u00020\"2\u0006\u0010a\u001a\u00020\u001b2\u0008\u0010c\u001a\u0004\u0018\u00010b2\u0008\u0010e\u001a\u0004\u0018\u00010d2\u0008\u0010f\u001a\u0004\u0018\u00010\u001bH\u0016\u00a2\u0006\u0004\u0008g\u0010hJ/\u0010m\u001a\u00020\"2\u0006\u0010i\u001a\u00020\u001b2\u0006\u0010j\u001a\u00020I2\u0006\u0010k\u001a\u00020\u001b2\u0006\u0010l\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008m\u0010nJ7\u0010p\u001a\u00020\"2\u0006\u0010i\u001a\u00020\u001b2\u0006\u0010j\u001a\u00020I2\u0006\u0010k\u001a\u00020\u001b2\u0006\u0010o\u001a\u00020\u001b2\u0006\u0010l\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008p\u0010qJ\u0017\u0010r\u001a\u00020\"2\u0006\u0010\u001f\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008r\u0010sJ\u000f\u0010t\u001a\u00020\"H\u0016\u00a2\u0006\u0004\u0008t\u0010MJ\u001f\u0010w\u001a\u00020\"2\u0006\u0010u\u001a\u00020\u001b2\u0006\u0010v\u001a\u00020PH\u0016\u00a2\u0006\u0004\u0008w\u0010xJ\u000f\u0010y\u001a\u00020\"H\u0016\u00a2\u0006\u0004\u0008y\u0010MJ\u0017\u0010{\u001a\u00020\"2\u0006\u0010z\u001a\u00020+H\u0016\u00a2\u0006\u0004\u0008{\u0010|J\u0017\u0010}\u001a\u00020\"2\u0006\u0010z\u001a\u00020+H\u0016\u00a2\u0006\u0004\u0008}\u0010|J\u0017\u0010~\u001a\u00020\"2\u0006\u0010z\u001a\u00020+H\u0016\u00a2\u0006\u0004\u0008~\u0010|JG\u0010\u0085\u0001\u001a\u00020\"2\u0006\u0010\u007f\u001a\u00020\u001b2\u0007\u0010\u0080\u0001\u001a\u00020b2\u0007\u0010\u0081\u0001\u001a\u00020P2\u0007\u0010\u0082\u0001\u001a\u00020\u001b2\u0007\u0010\u0083\u0001\u001a\u00020\u001b2\u0007\u0010\u0084\u0001\u001a\u00020\u001bH\u0016\u00a2\u0006\u0006\u0008\u0085\u0001\u0010\u0086\u0001J\u0011\u0010\u0087\u0001\u001a\u00020IH\u0016\u00a2\u0006\u0005\u0008\u0087\u0001\u0010KJ\u0011\u0010\u0088\u0001\u001a\u00020IH\u0016\u00a2\u0006\u0005\u0008\u0088\u0001\u0010KR\u0015\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008r\u0010\u0089\u0001R\u0015\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008w\u0010\u008a\u0001R\u0015\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008{\u0010\u008b\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u008c\u0001R\u0015\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008y\u0010\u008d\u0001R\u0015\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008g\u0010\u008e\u0001R\u0015\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008}\u0010\u008f\u0001R\u0015\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008U\u0010\u0090\u0001R\u0015\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008X\u0010\u0091\u0001R\u0015\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008t\u0010\u0092\u0001R\u0015\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008m\u0010\u0093\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0094\u0001R\u001b\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008[\u0010\u0095\u0001R\u001f\u0010\u0098\u0001\u001a\u000b \u0096\u0001*\u0004\u0018\u000104048\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008p\u0010\u0097\u0001R\u0018\u0010\u009b\u0001\u001a\u00030\u0099\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u009a\u0001R \u0010\u00a0\u0001\u001a\u00030\u009c\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008~\u0010\u009d\u0001\u001a\u0006\u0008\u009e\u0001\u0010\u009f\u0001\u00a8\u0006\u00a1\u0001"
    }
    d2 = {
        "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;",
        "Lxg/g;",
        "Landroid/content/Context;",
        "context",
        "Lcom/google/gson/Gson;",
        "gson",
        "Lc8/b;",
        "deviceDataSource",
        "Lug/u;",
        "sysLogRemoteDataSource",
        "Lug/q;",
        "sysLogLocalDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lug/n;",
        "referralAssetsLocalDataSource",
        "LRf0/f;",
        "privatePreferencesWrapper",
        "LRf0/l;",
        "publicPreferencesWrapper",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lo9/a;",
        "userRepository",
        "Lc8/a;",
        "applicationSettingsDataSource",
        "Lyb/a;",
        "",
        "kibanaAppNameProvider",
        "<init>",
        "(Landroid/content/Context;Lcom/google/gson/Gson;Lc8/b;Lug/u;Lug/q;Lc8/h;Lug/n;LRf0/f;LRf0/l;Lm8/a;Lo9/a;Lc8/a;Lyb/a;)V",
        "eventName",
        "Lcom/google/gson/JsonObject;",
        "eventParameters",
        "",
        "L",
        "(Ljava/lang/String;Lcom/google/gson/JsonObject;)V",
        "M",
        "(Lcom/google/gson/JsonObject;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lokhttp3/z;",
        "request",
        "N",
        "(Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lokhttp3/A;",
        "Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;",
        "C",
        "(Lokhttp3/A;)Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;",
        "",
        "Lkotlin/Pair;",
        "R",
        "(Ljava/util/List;)Ljava/lang/String;",
        "Lokhttp3/B;",
        "Ljava/nio/charset/Charset;",
        "z",
        "(Lokhttp3/B;)Ljava/nio/charset/Charset;",
        "logType",
        "B",
        "(Ljava/lang/String;)Lcom/google/gson/JsonObject;",
        "carrier",
        "O",
        "(Ljava/lang/String;)Ljava/lang/String;",
        "Lkotlin/Function1;",
        "Lkotlin/coroutines/e;",
        "",
        "action",
        "K",
        "(Lkotlin/jvm/functions/Function1;)V",
        "I",
        "()Ljava/lang/String;",
        "G",
        "E",
        "J",
        "H",
        "",
        "P",
        "()Z",
        "Q",
        "()V",
        "postBack",
        "D",
        "",
        "userId",
        "promocode",
        "devNumber",
        "devNumberType",
        "h",
        "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "isRegPromoCodePriorityReduced",
        "i",
        "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V",
        "stepNames",
        "m",
        "(Ljava/util/List;)V",
        "key",
        "message",
        "q",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "template",
        "",
        "type",
        "Ljava/math/BigDecimal;",
        "param",
        "player",
        "f",
        "(Ljava/lang/String;Ljava/lang/Integer;Ljava/math/BigDecimal;Ljava/lang/String;)V",
        "generated",
        "isQuickBet",
        "betGuid",
        "vid",
        "k",
        "(Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;)V",
        "couponId",
        "n",
        "(Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "a",
        "(Ljava/lang/String;)V",
        "j",
        "methodName",
        "time",
        "b",
        "(Ljava/lang/String;J)V",
        "e",
        "response",
        "c",
        "(Lokhttp3/A;)V",
        "g",
        "p",
        "requestUrl",
        "responseCode",
        "responseTime",
        "requestError",
        "requestHeaders",
        "requestBody",
        "d",
        "(Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "l",
        "o",
        "Landroid/content/Context;",
        "Lcom/google/gson/Gson;",
        "Lc8/b;",
        "Lug/u;",
        "Lug/q;",
        "Lc8/h;",
        "Lug/n;",
        "LRf0/f;",
        "LRf0/l;",
        "Lm8/a;",
        "Lo9/a;",
        "Lc8/a;",
        "Lyb/a;",
        "kotlin.jvm.PlatformType",
        "Ljava/nio/charset/Charset;",
        "utf8",
        "Lkotlinx/coroutines/N;",
        "Lkotlinx/coroutines/N;",
        "sysLogRepositoryScope",
        "Lvg/d;",
        "Lkotlin/j;",
        "F",
        "()Lvg/d;",
        "commonDeviceParamsModel",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final q:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static r:J


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/google/gson/Gson;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lc8/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lug/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lug/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lug/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LRf0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LRf0/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lo9/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lc8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lyb/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lyb/a<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Ljava/nio/charset/Charset;

.field public final o:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->q:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$a;

    .line 8
    .line 9
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    sput-wide v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->r:J

    .line 14
    .line 15
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lcom/google/gson/Gson;Lc8/b;Lug/u;Lug/q;Lc8/h;Lug/n;LRf0/f;LRf0/l;Lm8/a;Lo9/a;Lc8/a;Lyb/a;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/google/gson/Gson;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lc8/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lug/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lug/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lug/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LRf0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LRf0/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lo9/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lc8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lyb/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Lcom/google/gson/Gson;",
            "Lc8/b;",
            "Lug/u;",
            "Lug/q;",
            "Lc8/h;",
            "Lug/n;",
            "LRf0/f;",
            "LRf0/l;",
            "Lm8/a;",
            "Lo9/a;",
            "Lc8/a;",
            "Lyb/a<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->a:Landroid/content/Context;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->b:Lcom/google/gson/Gson;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->d:Lug/u;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->e:Lug/q;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->f:Lc8/h;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->g:Lug/n;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->h:LRf0/f;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->i:LRf0/l;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->j:Lm8/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->k:Lo9/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->l:Lc8/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->m:Lyb/a;

    .line 29
    .line 30
    const-string p1, "UTF-8"

    .line 31
    .line 32
    invoke-static {p1}, Ljava/nio/charset/Charset;->forName(Ljava/lang/String;)Ljava/nio/charset/Charset;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->n:Ljava/nio/charset/Charset;

    .line 37
    .line 38
    const/4 p1, 0x0

    .line 39
    const/4 p2, 0x1

    .line 40
    invoke-static {p1, p2, p1}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-interface {p10}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    invoke-interface {p1, p2}, Lkotlin/coroutines/CoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-static {p1}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->o:Lkotlinx/coroutines/N;

    .line 57
    .line 58
    new-instance p1, Lorg/xbet/analytics/data/repositories/k;

    .line 59
    .line 60
    invoke-direct {p1, p0}, Lorg/xbet/analytics/data/repositories/k;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)V

    .line 61
    .line 62
    .line 63
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->p:Lkotlin/j;

    .line 68
    .line 69
    return-void
.end method

.method public static final A(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Lvg/d;
    .locals 11

    .line 1
    new-instance v0, Lvg/d;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->f:Lc8/h;

    .line 4
    .line 5
    invoke-interface {v1}, Lc8/h;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 10
    .line 11
    invoke-interface {v2}, Lc8/b;->d()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iget-object v3, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 16
    .line 17
    invoke-interface {v3}, Lc8/b;->g()I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    iget-object v4, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->m:Lyb/a;

    .line 22
    .line 23
    invoke-interface {v4}, Lyb/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    check-cast v4, Ljava/lang/String;

    .line 28
    .line 29
    iget-object v5, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->l:Lc8/a;

    .line 30
    .line 31
    invoke-interface {v5}, Lc8/a;->c()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    iget-object v6, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 36
    .line 37
    invoke-interface {v6}, Lc8/b;->b()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v6

    .line 41
    iget-object v7, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 42
    .line 43
    invoke-interface {v7}, Lc8/b;->a()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v7

    .line 47
    iget-object v8, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 48
    .line 49
    invoke-interface {v8}, Lc8/b;->p()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v8

    .line 53
    iget-object v9, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 54
    .line 55
    invoke-interface {v9}, Lc8/b;->n()Lkotlin/Pair;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-virtual {v9}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    check-cast v9, Ljava/lang/String;

    .line 64
    .line 65
    iget-object p0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->c:Lc8/b;

    .line 66
    .line 67
    invoke-interface {p0}, Lc8/b;->n()Lkotlin/Pair;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    invoke-virtual {p0}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    move-object v10, p0

    .line 76
    check-cast v10, Ljava/lang/String;

    .line 77
    .line 78
    invoke-direct/range {v0 .. v10}, Lvg/d;-><init>(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 79
    .line 80
    .line 81
    return-object v0
.end method

.method public static final S(Lkotlin/Pair;)Ljava/lang/CharSequence;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ljava/lang/String;

    .line 6
    .line 7
    invoke-virtual {p0}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, Ljava/lang/String;

    .line 12
    .line 13
    new-instance v1, Ljava/lang/StringBuilder;

    .line 14
    .line 15
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 16
    .line 17
    .line 18
    const-string v2, "\""

    .line 19
    .line 20
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    const-string v0, "\":\""

    .line 27
    .line 28
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 35
    .line 36
    .line 37
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object p0

    .line 41
    return-object p0
.end method

.method public static synthetic r(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Lvg/d;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->A(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Lvg/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s(Lkotlin/Pair;)Ljava/lang/CharSequence;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->S(Lkotlin/Pair;)Ljava/lang/CharSequence;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic t(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)Lcom/google/gson/JsonObject;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->B(Ljava/lang/String;)Lcom/google/gson/JsonObject;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic u(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Lug/u;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->d:Lug/u;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lcom/google/gson/JsonObject;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->M(Lcom/google/gson/JsonObject;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic w(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->N(Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->P()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic y(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->Q()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final B(Ljava/lang/String;)Lcom/google/gson/JsonObject;
    .locals 3

    .line 1
    sget-object v0, Lsg/c;->a:Lsg/c;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->F()Lvg/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, v1}, Lsg/c;->a(Lvg/d;)Lcom/google/gson/JsonObject;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    sget-object v1, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 12
    .line 13
    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->a:Landroid/content/Context;

    .line 14
    .line 15
    invoke-virtual {v1, v2}, Lorg/xbet/ui_common/utils/g;->i(Landroid/content/Context;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const-string v2, "wifi"

    .line 20
    .line 21
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    const-string v2, "deviceWiFiOn"

    .line 30
    .line 31
    invoke-virtual {v0, v2, v1}, Lcom/google/gson/JsonObject;->C(Ljava/lang/String;Ljava/lang/Boolean;)V

    .line 32
    .line 33
    .line 34
    const-string v1, "logType"

    .line 35
    .line 36
    invoke-virtual {v0, v1, p1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->f:Lc8/h;

    .line 40
    .line 41
    invoke-interface {p1}, Lc8/h;->c()Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    const-string v1, "deviseLanguage"

    .line 46
    .line 47
    invoke-virtual {v0, v1, p1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->k:Lo9/a;

    .line 51
    .line 52
    invoke-interface {p1}, Lo9/a;->j()Z

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    if-eqz p1, :cond_0

    .line 57
    .line 58
    :try_start_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->k:Lo9/a;

    .line 59
    .line 60
    invoke-interface {p1}, Lo9/a;->E()Lcom/xbet/onexuser/domain/user/model/UserInfo;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/user/model/UserInfo;->getUserId()J

    .line 65
    .line 66
    .line 67
    move-result-wide v1

    .line 68
    const-string p1, "userId"

    .line 69
    .line 70
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-virtual {v0, p1, v1}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 75
    .line 76
    .line 77
    goto :goto_0

    .line 78
    :catch_0
    move-exception p1

    .line 79
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 80
    .line 81
    .line 82
    :cond_0
    :goto_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->e:Lug/q;

    .line 83
    .line 84
    invoke-virtual {p1}, Lug/q;->b()Landroid/net/wifi/WifiManager;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    if-eqz p1, :cond_2

    .line 89
    .line 90
    invoke-virtual {p1}, Landroid/net/wifi/WifiManager;->getConnectionInfo()Landroid/net/wifi/WifiInfo;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    if-eqz p1, :cond_2

    .line 95
    .line 96
    invoke-virtual {p1}, Landroid/net/wifi/WifiInfo;->getSupplicantState()Landroid/net/wifi/SupplicantState;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-static {v1}, Landroid/net/wifi/WifiInfo;->getDetailedStateOf(Landroid/net/wifi/SupplicantState;)Landroid/net/NetworkInfo$DetailedState;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    sget-object v2, Landroid/net/NetworkInfo$DetailedState;->CONNECTED:Landroid/net/NetworkInfo$DetailedState;

    .line 105
    .line 106
    if-eq v1, v2, :cond_1

    .line 107
    .line 108
    sget-object v2, Landroid/net/NetworkInfo$DetailedState;->OBTAINING_IPADDR:Landroid/net/NetworkInfo$DetailedState;

    .line 109
    .line 110
    if-ne v1, v2, :cond_2

    .line 111
    .line 112
    :cond_1
    const-string v1, "wifiName"

    .line 113
    .line 114
    invoke-virtual {p1}, Landroid/net/wifi/WifiInfo;->getSSID()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    invoke-virtual {v0, v1, p1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 119
    .line 120
    .line 121
    :cond_2
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->e:Lug/q;

    .line 122
    .line 123
    invoke-virtual {p1}, Lug/q;->a()Landroid/telephony/TelephonyManager;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    if-nez p1, :cond_3

    .line 128
    .line 129
    goto :goto_1

    .line 130
    :cond_3
    invoke-virtual {p1}, Landroid/telephony/TelephonyManager;->getNetworkOperatorName()Ljava/lang/String;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 135
    .line 136
    .line 137
    move-result v2

    .line 138
    if-lez v2, :cond_4

    .line 139
    .line 140
    const-string v2, "carrierName"

    .line 141
    .line 142
    invoke-virtual {p0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->O(Ljava/lang/String;)Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    invoke-virtual {v0, v2, v1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 147
    .line 148
    .line 149
    :cond_4
    invoke-virtual {p1}, Landroid/telephony/TelephonyManager;->getSimCountryIso()Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 154
    .line 155
    .line 156
    move-result v1

    .line 157
    if-lez v1, :cond_5

    .line 158
    .line 159
    const-string v1, "carrierCC"

    .line 160
    .line 161
    invoke-virtual {v0, v1, p1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 162
    .line 163
    .line 164
    :cond_5
    :goto_1
    return-object v0
.end method

.method public final C(Lokhttp3/A;)Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;
    .locals 5

    .line 1
    :try_start_0
    invoke-virtual {p1}, Lokhttp3/A;->a()Lokhttp3/B;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    if-eqz p1, :cond_2

    .line 6
    .line 7
    invoke-virtual {p1}, Lokhttp3/B;->h()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    const-wide/16 v2, 0x0

    .line 12
    .line 13
    cmp-long v4, v0, v2

    .line 14
    .line 15
    if-lez v4, :cond_0

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->b:Lcom/google/gson/Gson;

    .line 18
    .line 19
    invoke-virtual {p1}, Lokhttp3/B;->a1()Lee/f;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-interface {v1}, Lee/f;->getBuffer()Lee/d;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, Lee/d;->b()Lee/d;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->z(Lokhttp3/B;)Ljava/nio/charset/Charset;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {v1, p1}, Lee/d;->Q0(Ljava/nio/charset/Charset;)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    const-class v1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 40
    .line 41
    invoke-virtual {v0, p1, v1}, Lcom/google/gson/Gson;->n(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    check-cast p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :catch_0
    move-exception p1

    .line 49
    goto :goto_2

    .line 50
    :cond_0
    new-instance p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 51
    .line 52
    const-string v0, "Empty content"

    .line 53
    .line 54
    invoke-direct {p1, p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    :goto_0
    if-nez p1, :cond_1

    .line 58
    .line 59
    goto :goto_1

    .line 60
    :cond_1
    return-object p1

    .line 61
    :cond_2
    :goto_1
    new-instance p1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 62
    .line 63
    const-string v0, "Response body == null"

    .line 64
    .line 65
    invoke-direct {p1, p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 66
    .line 67
    .line 68
    return-object p1

    .line 69
    :goto_2
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 70
    .line 71
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    new-instance v1, Ljava/lang/StringBuilder;

    .line 76
    .line 77
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 78
    .line 79
    .line 80
    const-string v2, "Unknown error format ("

    .line 81
    .line 82
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    .line 84
    .line 85
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 86
    .line 87
    .line 88
    const-string p1, ")"

    .line 89
    .line 90
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-direct {v0, p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    return-object v0
.end method

.method public final D(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 1
    :try_start_0
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 2
    .line 3
    new-instance v0, Lorg/json/JSONObject;

    .line 4
    .line 5
    invoke-direct {v0, p1}, Lorg/json/JSONObject;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    const-string v1, "pb"

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 18
    goto :goto_0

    .line 19
    :catchall_0
    move-exception v0

    .line 20
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 21
    .line 22
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    :goto_0
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    if-eqz v1, :cond_0

    .line 35
    .line 36
    goto :goto_1

    .line 37
    :cond_0
    move-object p1, v0

    .line 38
    :goto_1
    check-cast p1, Ljava/lang/String;

    .line 39
    .line 40
    return-object p1
.end method

.method public final E()Ljava/lang/String;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->h:LRf0/f;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x2

    .line 5
    const-string v3, "APPS_FLYER_ID"

    .line 6
    .line 7
    invoke-static {v0, v3, v1, v2, v1}, Lk8/g$a;->c(Lk8/g;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final F()Lvg/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->p:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lvg/d;

    .line 8
    .line 9
    return-object v0
.end method

.method public final G()Ljava/lang/String;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->g:Lug/n;

    .line 2
    .line 3
    invoke-interface {v0}, Lug/n;->d()Lj9/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lj9/i;->c()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    const-string v0, ""

    .line 14
    .line 15
    :cond_0
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-nez v1, :cond_1

    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->H()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    :cond_1
    return-object v0
.end method

.method public final H()Ljava/lang/String;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->h:LRf0/f;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x2

    .line 5
    const-string v3, "post_back"

    .line 6
    .line 7
    invoke-static {v0, v3, v1, v2, v1}, Lk8/g$a;->c(Lk8/g;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final I()Ljava/lang/String;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->g:Lug/n;

    .line 2
    .line 3
    invoke-interface {v0}, Lug/n;->d()Lj9/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lj9/i;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    const-string v0, ""

    .line 14
    .line 15
    :cond_0
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-nez v1, :cond_1

    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->J()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    :cond_1
    return-object v0
.end method

.method public final J()Ljava/lang/String;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->h:LRf0/f;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x2

    .line 5
    const-string v3, "referral_dl"

    .line 6
    .line 7
    invoke-static {v0, v3, v1, v2, v1}, Lk8/g$a;->c(Lk8/g;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final K(Lkotlin/jvm/functions/Function1;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->o:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$launchInRepositoryScope$1;->INSTANCE:Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$launchInRepositoryScope$1;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->j:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v5, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$launchInRepositoryScope$2;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-direct {v5, p1, v2}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$launchInRepositoryScope$2;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    const/16 v6, 0xa

    .line 18
    .line 19
    const/4 v7, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logEvent$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, p1, p2, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logEvent$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final M(Lcom/google/gson/JsonObject;Ljava/lang/String;Lcom/google/gson/JsonObject;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/google/gson/JsonObject;",
            "Ljava/lang/String;",
            "Lcom/google/gson/JsonObject;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const-string v0, "eventName"

    .line 2
    .line 3
    invoke-virtual {p1, v0, p2}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p3}, Lcom/google/gson/JsonObject;->isEmpty()Z

    .line 7
    .line 8
    .line 9
    move-result p2

    .line 10
    if-nez p2, :cond_0

    .line 11
    .line 12
    const-string p2, "eventParameters"

    .line 13
    .line 14
    invoke-virtual {p1, p2, p3}, Lcom/google/gson/JsonObject;->B(Ljava/lang/String;Lcom/google/gson/JsonElement;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    sget-object p2, Lokhttp3/z;->Companion:Lokhttp3/z$a;

    .line 18
    .line 19
    invoke-virtual {p1}, Lcom/google/gson/JsonElement;->toString()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    sget-object p3, Lokhttp3/v;->e:Lokhttp3/v$a;

    .line 24
    .line 25
    const-string v0, "application/json; charset=utf-8"

    .line 26
    .line 27
    invoke-virtual {p3, v0}, Lokhttp3/v$a;->b(Ljava/lang/String;)Lokhttp3/v;

    .line 28
    .line 29
    .line 30
    move-result-object p3

    .line 31
    invoke-virtual {p2, p1, p3}, Lokhttp3/z$a;->b(Ljava/lang/String;Lokhttp3/v;)Lokhttp3/z;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {p0, p1, p4}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->N(Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p2

    .line 43
    if-ne p1, p2, :cond_1

    .line 44
    .line 45
    return-object p1

    .line 46
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p1
.end method

.method public final N(Lokhttp3/z;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lokhttp3/z;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    sget-object v0, Lokhttp3/t;->k:Lokhttp3/t$b;

    .line 2
    .line 3
    sget-object v1, LY7/a;->a:LY7/a;

    .line 4
    .line 5
    invoke-virtual {v1}, LY7/a;->b()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v0, v2}, Lokhttp3/t$b;->f(Ljava/lang/String;)Lokhttp3/t;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p1

    .line 18
    :cond_0
    invoke-virtual {v1}, LY7/a;->b()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-string v1, "https://mob-experience.space"

    .line 23
    .line 24
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-eqz v0, :cond_1

    .line 29
    .line 30
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 31
    .line 32
    return-object p1

    .line 33
    :cond_1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->d:Lug/u;

    .line 34
    .line 35
    const-string v1, "Basic YW5kcm9pZF91c2VyOmVpR2hvb0I0YWwteWllM1RoYWV0aC1lZVBodWRpdWI5"

    .line 36
    .line 37
    invoke-virtual {v0, p1, v1, p2}, Lug/u;->d(Lokhttp3/z;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p2

    .line 45
    if-ne p1, p2, :cond_2

    .line 46
    .line 47
    return-object p1

    .line 48
    :cond_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 49
    .line 50
    return-object p1
.end method

.method public final O(Ljava/lang/String;)Ljava/lang/String;
    .locals 6

    .line 1
    sget-object v0, Ljava/util/Locale;->ROOT:Ljava/util/Locale;

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Ljava/lang/String;->toUpperCase(Ljava/util/Locale;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const-string v1, "MTS"

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x2

    .line 11
    const/4 v4, 0x0

    .line 12
    invoke-static {v0, v1, v2, v3, v4}, Lkotlin/text/StringsKt;->i0(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    move-result v5

    .line 16
    if-eqz v5, :cond_0

    .line 17
    .line 18
    return-object v1

    .line 19
    :cond_0
    const-string v1, "MEGAFON"

    .line 20
    .line 21
    invoke-static {v0, v1, v2, v3, v4}, Lkotlin/text/StringsKt;->i0(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    const-string p1, "MegaFon"

    .line 28
    .line 29
    return-object p1

    .line 30
    :cond_1
    const-string v1, "TELE2"

    .line 31
    .line 32
    invoke-static {v0, v1, v2, v3, v4}, Lkotlin/text/StringsKt;->i0(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    if-eqz v0, :cond_2

    .line 37
    .line 38
    const-string p1, "Tele2"

    .line 39
    .line 40
    :cond_2
    return-object p1
.end method

.method public final P()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->i:LRf0/l;

    .line 2
    .line 3
    const-string v1, "R_IS_SENT_STATUS"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-virtual {v0, v1, v2}, LRf0/l;->c(Ljava/lang/String;Z)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    return v0
.end method

.method public final Q()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->i:LRf0/l;

    .line 2
    .line 3
    const-string v1, "R_IS_SENT_STATUS"

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    invoke-virtual {v0, v1, v2}, LRf0/l;->n(Ljava/lang/String;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final R(Ljava/util/List;)Ljava/lang/String;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lkotlin/Pair<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;>;)",
            "Ljava/lang/String;"
        }
    .end annotation

    .line 1
    new-instance v6, Lorg/xbet/analytics/data/repositories/l;

    .line 2
    .line 3
    invoke-direct {v6}, Lorg/xbet/analytics/data/repositories/l;-><init>()V

    .line 4
    .line 5
    .line 6
    const/16 v7, 0x1e

    .line 7
    .line 8
    const/4 v8, 0x0

    .line 9
    const-string v1, ","

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    const/4 v3, 0x0

    .line 13
    const/4 v4, 0x0

    .line 14
    const/4 v5, 0x0

    .line 15
    move-object v0, p1

    .line 16
    invoke-static/range {v0 .. v8}, Lkotlin/collections/CollectionsKt;->G0(Ljava/lang/Iterable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    new-instance v0, Ljava/lang/StringBuilder;

    .line 21
    .line 22
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 23
    .line 24
    .line 25
    const-string v1, "headers:\'"

    .line 26
    .line 27
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 31
    .line 32
    .line 33
    const-string p1, "\'"

    .line 34
    .line 35
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 36
    .line 37
    .line 38
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    return-object p1
.end method

.method public a(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lcom/google/gson/JsonObject;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/google/gson/JsonObject;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public b(Ljava/lang/String;J)V
    .locals 6
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logCaptchaTime$1;

    .line 2
    .line 3
    const/4 v5, 0x0

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move-wide v3, p2

    .line 7
    invoke-direct/range {v0 .. v5}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logCaptchaTime$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;JLkotlin/coroutines/e;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public c(Lokhttp3/A;)V
    .locals 12
    .param p1    # Lokhttp3/A;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lokhttp3/A;->isSuccessful()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_5

    .line 6
    .line 7
    const/16 v0, 0x191

    .line 8
    .line 9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const/16 v1, 0x1a6

    .line 14
    .line 15
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/16 v2, 0x190

    .line 20
    .line 21
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    const/4 v3, 0x3

    .line 26
    new-array v3, v3, [Ljava/lang/Integer;

    .line 27
    .line 28
    const/4 v4, 0x0

    .line 29
    aput-object v0, v3, v4

    .line 30
    .line 31
    const/4 v0, 0x1

    .line 32
    aput-object v1, v3, v0

    .line 33
    .line 34
    const/4 v0, 0x2

    .line 35
    aput-object v2, v3, v0

    .line 36
    .line 37
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {p1}, Lokhttp3/A;->h()I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_0

    .line 54
    .line 55
    goto/16 :goto_1

    .line 56
    .line 57
    :cond_0
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {v0}, Lokhttp3/y;->f()Lokhttp3/s;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    check-cast v0, Ljava/lang/Iterable;

    .line 70
    .line 71
    new-instance v1, Ljava/util/ArrayList;

    .line 72
    .line 73
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 74
    .line 75
    .line 76
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 81
    .line 82
    .line 83
    move-result v2

    .line 84
    if-eqz v2, :cond_3

    .line 85
    .line 86
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v2

    .line 90
    move-object v3, v2

    .line 91
    check-cast v3, Lkotlin/Pair;

    .line 92
    .line 93
    invoke-virtual {v3}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    check-cast v3, Ljava/lang/String;

    .line 98
    .line 99
    const-string v4, "X-Auth"

    .line 100
    .line 101
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    move-result v4

    .line 105
    if-nez v4, :cond_1

    .line 106
    .line 107
    const-string v4, "X-Sign"

    .line 108
    .line 109
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    move-result v3

    .line 113
    if-eqz v3, :cond_2

    .line 114
    .line 115
    goto :goto_0

    .line 116
    :cond_2
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 117
    .line 118
    .line 119
    goto :goto_0

    .line 120
    :cond_3
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    invoke-virtual {v0}, Lokhttp3/y;->k()Lokhttp3/t;

    .line 125
    .line 126
    .line 127
    move-result-object v0

    .line 128
    invoke-virtual {v0}, Lokhttp3/t;->toString()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v3

    .line 132
    invoke-virtual {p1}, Lokhttp3/A;->h()I

    .line 133
    .line 134
    .line 135
    move-result v4

    .line 136
    invoke-virtual {p1}, Lokhttp3/A;->x()J

    .line 137
    .line 138
    .line 139
    move-result-wide v5

    .line 140
    invoke-virtual {p1}, Lokhttp3/A;->B()J

    .line 141
    .line 142
    .line 143
    move-result-wide v7

    .line 144
    sub-long/2addr v5, v7

    .line 145
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->C(Lokhttp3/A;)Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    invoke-virtual {p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;->a()Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    if-nez p1, :cond_4

    .line 154
    .line 155
    const-string p1, ""

    .line 156
    .line 157
    :cond_4
    move-object v7, p1

    .line 158
    invoke-virtual {p0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->R(Ljava/util/List;)Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object v8

    .line 162
    const/16 v10, 0x20

    .line 163
    .line 164
    const/4 v11, 0x0

    .line 165
    const/4 v9, 0x0

    .line 166
    move-object v2, p0

    .line 167
    invoke-static/range {v2 .. v11}, Lxg/g$a;->a(Lxg/g;Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)V

    .line 168
    .line 169
    .line 170
    :cond_5
    :goto_1
    return-void
.end method

.method public d(Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x0

    .line 3
    const-string v2, "/log/Android"

    .line 4
    .line 5
    const/4 v3, 0x0

    .line 6
    invoke-static {p1, v2, v3, v0, v1}, Lkotlin/text/StringsKt;->i0(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    new-instance v1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;

    .line 14
    .line 15
    const/4 v9, 0x0

    .line 16
    move-object v2, p0

    .line 17
    move-object v3, p1

    .line 18
    move v4, p2

    .line 19
    move-wide v5, p3

    .line 20
    move-object v7, p5

    .line 21
    move-object/from16 v8, p6

    .line 22
    .line 23
    invoke-direct/range {v1 .. v9}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logRequest$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public e()V
    .locals 3

    .line 1
    sget-object v0, LJg/f;->a:LJg/f;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->a:Landroid/content/Context;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, LJg/f;->l(Landroid/content/Context;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-nez v1, :cond_0

    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    new-instance v1, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logProxies$1;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v1, p0, v0, v2}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logProxies$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public f(Ljava/lang/String;Ljava/lang/Integer;Ljava/math/BigDecimal;Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lcom/google/gson/JsonObject;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/google/gson/JsonObject;-><init>()V

    .line 4
    .line 5
    .line 6
    const-string v1, "template"

    .line 7
    .line 8
    invoke-virtual {v0, v1, p1}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    const-string p1, "type"

    .line 12
    .line 13
    invoke-virtual {v0, p1, p2}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 14
    .line 15
    .line 16
    const-string p1, "param"

    .line 17
    .line 18
    invoke-virtual {v0, p1, p3}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 19
    .line 20
    .line 21
    const-string p1, "player"

    .line 22
    .line 23
    invoke-virtual {v0, p1, p4}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    const-string p1, "GameBetObjectInfo"

    .line 27
    .line 28
    invoke-virtual {p0, p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public g(Lokhttp3/A;)V
    .locals 10
    .param p1    # Lokhttp3/A;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lokhttp3/y;->f()Lokhttp3/s;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->E1(Ljava/lang/Iterable;)Ljava/util/Set;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Ljava/lang/Iterable;

    .line 14
    .line 15
    new-instance v1, Ljava/util/ArrayList;

    .line 16
    .line 17
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 18
    .line 19
    .line 20
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_2

    .line 29
    .line 30
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    move-object v3, v2

    .line 35
    check-cast v3, Lkotlin/Pair;

    .line 36
    .line 37
    invoke-virtual {v3}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v3

    .line 41
    check-cast v3, Ljava/lang/String;

    .line 42
    .line 43
    const-string v4, "X-Auth"

    .line 44
    .line 45
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    if-nez v4, :cond_0

    .line 50
    .line 51
    const-string v4, "X-Sign"

    .line 52
    .line 53
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    move-result v3

    .line 57
    if-eqz v3, :cond_1

    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_1
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_2
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    invoke-virtual {v0}, Lokhttp3/y;->k()Lokhttp3/t;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    invoke-virtual {v0}, Lokhttp3/t;->toString()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v3

    .line 76
    invoke-virtual {p1}, Lokhttp3/A;->h()I

    .line 77
    .line 78
    .line 79
    move-result v4

    .line 80
    invoke-virtual {p1}, Lokhttp3/A;->x()J

    .line 81
    .line 82
    .line 83
    move-result-wide v5

    .line 84
    invoke-virtual {p1}, Lokhttp3/A;->B()J

    .line 85
    .line 86
    .line 87
    move-result-wide v7

    .line 88
    sub-long/2addr v5, v7

    .line 89
    invoke-virtual {p0, p1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->C(Lokhttp3/A;)Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    invoke-virtual {v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$b;->a()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    const-string v2, ""

    .line 98
    .line 99
    if-nez v0, :cond_3

    .line 100
    .line 101
    move-object v7, v2

    .line 102
    goto :goto_1

    .line 103
    :cond_3
    move-object v7, v0

    .line 104
    :goto_1
    invoke-virtual {p0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->R(Ljava/util/List;)Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v8

    .line 108
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    invoke-virtual {p1}, Lokhttp3/y;->a()Lokhttp3/z;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    if-eqz p1, :cond_5

    .line 117
    .line 118
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-nez p1, :cond_4

    .line 123
    .line 124
    goto :goto_3

    .line 125
    :cond_4
    move-object v9, p1

    .line 126
    :goto_2
    move-object v2, p0

    .line 127
    goto :goto_4

    .line 128
    :cond_5
    :goto_3
    move-object v9, v2

    .line 129
    goto :goto_2

    .line 130
    :goto_4
    invoke-virtual/range {v2 .. v9}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->d(Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 131
    .line 132
    .line 133
    return-void
.end method

.method public h(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->I()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    invoke-virtual {p0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->G()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p4

    .line 9
    new-instance p5, Lcom/google/gson/JsonObject;

    .line 10
    .line 11
    invoke-direct {p5}, Lcom/google/gson/JsonObject;-><init>()V

    .line 12
    .line 13
    .line 14
    const-string v0, "tag"

    .line 15
    .line 16
    invoke-virtual {p5, v0, p3}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    const-string p3, "pb"

    .line 20
    .line 21
    invoke-virtual {p5, p3, p4}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    const-string p2, "userId"

    .line 29
    .line 30
    invoke-virtual {p5, p2, p1}, Lcom/google/gson/JsonObject;->D(Ljava/lang/String;Ljava/lang/Number;)V

    .line 31
    .line 32
    .line 33
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 34
    .line 35
    const-string p1, "InstallFromLoader"

    .line 36
    .line 37
    invoke-virtual {p0, p1, p5}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public i(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 16
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->I()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v6

    .line 7
    invoke-virtual {v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->G()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    const/4 v15, 0x0

    .line 16
    if-lez v2, :cond_0

    .line 17
    .line 18
    if-eqz p6, :cond_0

    .line 19
    .line 20
    move-object v14, v15

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    move-object/from16 v14, p5

    .line 23
    .line 24
    :goto_0
    iget-object v2, v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->l:Lc8/a;

    .line 25
    .line 26
    invoke-interface {v2}, Lc8/a;->d()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    iget-object v2, v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->l:Lc8/a;

    .line 31
    .line 32
    invoke-interface {v2}, Lc8/a;->r()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 37
    .line 38
    .line 39
    move-result v4

    .line 40
    if-nez v4, :cond_1

    .line 41
    .line 42
    move-object v4, v15

    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v4, v2

    .line 45
    :goto_1
    invoke-virtual {v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->E()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v5

    .line 49
    iget-object v2, v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->f:Lc8/h;

    .line 50
    .line 51
    invoke-interface {v2}, Lc8/h;->b()I

    .line 52
    .line 53
    .line 54
    move-result v7

    .line 55
    iget-object v2, v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->f:Lc8/h;

    .line 56
    .line 57
    invoke-interface {v2}, Lc8/h;->d()I

    .line 58
    .line 59
    .line 60
    move-result v8

    .line 61
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->D(Ljava/lang/String;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v13

    .line 65
    new-instance v1, Lvg/c;

    .line 66
    .line 67
    const/4 v9, 0x3

    .line 68
    move-wide/from16 v11, p1

    .line 69
    .line 70
    move-object/from16 v10, p3

    .line 71
    .line 72
    move-object/from16 v2, p4

    .line 73
    .line 74
    invoke-direct/range {v1 .. v14}, Lvg/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIILjava/lang/String;JLjava/lang/String;Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    new-instance v2, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;

    .line 78
    .line 79
    invoke-direct {v2, v0, v1, v15}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logAppsFlyer$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Lvg/c;Lkotlin/coroutines/e;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v0, v2}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public j()V
    .locals 6

    .line 1
    sget-wide v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->r:J

    .line 2
    .line 3
    const-wide/16 v2, 0x0

    .line 4
    .line 5
    cmp-long v4, v0, v2

    .line 6
    .line 7
    if-lez v4, :cond_0

    .line 8
    .line 9
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 10
    .line 11
    .line 12
    move-result-wide v0

    .line 13
    sget-wide v4, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->r:J

    .line 14
    .line 15
    sub-long/2addr v0, v4

    .line 16
    new-instance v4, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logLoadingTime$1;

    .line 17
    .line 18
    const/4 v5, 0x0

    .line 19
    invoke-direct {v4, p0, v0, v1, v5}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logLoadingTime$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;JLkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0, v4}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 23
    .line 24
    .line 25
    sput-wide v2, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->r:J

    .line 26
    .line 27
    :cond_0
    return-void
.end method

.method public k(Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;

    .line 2
    .line 3
    const/4 v6, 0x0

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move v3, p2

    .line 7
    move-object v4, p3

    .line 8
    move-object v5, p4

    .line 9
    invoke-direct/range {v0 .. v6}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetRequest$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public l()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->h:LRf0/f;

    .line 2
    .line 3
    const-string v1, "IS_ORGANIC"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-virtual {v0, v1, v2}, LRf0/f;->getBoolean(Ljava/lang/String;Z)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    return v0
.end method

.method public m(Ljava/util/List;)V
    .locals 3
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/google/gson/JsonObject;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/google/gson/JsonObject;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    check-cast v1, Ljava/lang/String;

    .line 21
    .line 22
    const-string v2, "Completed"

    .line 23
    .line 24
    invoke-virtual {v0, v1, v2}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 29
    .line 30
    const-string p1, "LoadingScreenLoadStates"

    .line 31
    .line 32
    invoke-virtual {p0, p1, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public n(Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetResponse$1;

    .line 2
    .line 3
    const/4 v7, 0x0

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move v3, p2

    .line 7
    move-object v4, p3

    .line 8
    move-object v5, p4

    .line 9
    move-object v6, p5

    .line 10
    invoke-direct/range {v0 .. v7}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl$logBetResponse$1;-><init>(Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->K(Lkotlin/jvm/functions/Function1;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public o()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->g:Lug/n;

    .line 2
    .line 3
    invoke-interface {v0}, Lug/n;->d()Lj9/i;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lj9/i;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    const-string v0, ""

    .line 14
    .line 15
    :cond_0
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-lez v0, :cond_1

    .line 20
    .line 21
    const/4 v0, 0x1

    .line 22
    return v0

    .line 23
    :cond_1
    const/4 v0, 0x0

    .line 24
    return v0
.end method

.method public p(Lokhttp3/A;)V
    .locals 11
    .param p1    # Lokhttp3/A;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lokhttp3/A;->isSuccessful()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    const/16 v0, 0x191

    .line 8
    .line 9
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const/16 v1, 0x1a6

    .line 14
    .line 15
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/16 v2, 0x190

    .line 20
    .line 21
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    const/4 v3, 0x3

    .line 26
    new-array v3, v3, [Ljava/lang/Integer;

    .line 27
    .line 28
    const/4 v4, 0x0

    .line 29
    aput-object v0, v3, v4

    .line 30
    .line 31
    const/4 v0, 0x1

    .line 32
    aput-object v1, v3, v0

    .line 33
    .line 34
    const/4 v0, 0x2

    .line 35
    aput-object v2, v3, v0

    .line 36
    .line 37
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {p1}, Lokhttp3/A;->h()I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_0

    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_0
    invoke-virtual {p1}, Lokhttp3/A;->y()Lokhttp3/y;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-virtual {v0}, Lokhttp3/y;->k()Lokhttp3/t;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    invoke-virtual {v0}, Lokhttp3/t;->toString()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    invoke-virtual {p1}, Lokhttp3/A;->h()I

    .line 69
    .line 70
    .line 71
    move-result v3

    .line 72
    invoke-virtual {p1}, Lokhttp3/A;->x()J

    .line 73
    .line 74
    .line 75
    move-result-wide v0

    .line 76
    invoke-virtual {p1}, Lokhttp3/A;->B()J

    .line 77
    .line 78
    .line 79
    move-result-wide v4

    .line 80
    sub-long v4, v0, v4

    .line 81
    .line 82
    const/16 v9, 0x38

    .line 83
    .line 84
    const/4 v10, 0x0

    .line 85
    const/4 v6, 0x0

    .line 86
    const/4 v7, 0x0

    .line 87
    const/4 v8, 0x0

    .line 88
    move-object v1, p0

    .line 89
    invoke-static/range {v1 .. v10}, Lxg/g$a;->a(Lxg/g;Ljava/lang/String;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)V

    .line 90
    .line 91
    .line 92
    :cond_1
    :goto_0
    return-void
.end method

.method public q(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lcom/google/gson/JsonObject;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/google/gson/JsonObject;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p1, p2}, Lcom/google/gson/JsonObject;->E(Ljava/lang/String;Ljava/lang/String;)V

    .line 7
    .line 8
    .line 9
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    invoke-virtual {p0, p3, v0}, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->L(Ljava/lang/String;Lcom/google/gson/JsonObject;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final z(Lokhttp3/B;)Ljava/nio/charset/Charset;
    .locals 1

    .line 1
    invoke-virtual {p1}, Lokhttp3/B;->i()Lokhttp3/v;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    if-eqz p1, :cond_1

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->n:Ljava/nio/charset/Charset;

    .line 8
    .line 9
    invoke-virtual {p1, v0}, Lokhttp3/v;->c(Ljava/nio/charset/Charset;)Ljava/nio/charset/Charset;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    return-object p1

    .line 17
    :cond_1
    :goto_0
    iget-object p1, p0, Lorg/xbet/analytics/data/repositories/SysLogRepositoryImpl;->n:Ljava/nio/charset/Charset;

    .line 18
    .line 19
    return-object p1
.end method
