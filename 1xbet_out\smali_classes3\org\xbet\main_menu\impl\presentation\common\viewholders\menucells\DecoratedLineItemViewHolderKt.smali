.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/N;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/N;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LT80/V;

    .line 2
    .line 3
    invoke-direct {v0}, LT80/V;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LT80/W;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LT80/W;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt$getDecoratedLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt$getDecoratedLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt$getDecoratedLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/menucells/DecoratedLineItemViewHolderKt$getDecoratedLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/N;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/N;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/N;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/N;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/N;->b()Lorg/xbet/uikit/components/cells/MenuCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LT80/X;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LT80/X;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LT80/Y;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LT80/Y;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 5

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LN80/c$e;

    .line 6
    .line 7
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lv80/N;

    .line 12
    .line 13
    iget-object v0, v0, Lv80/N;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 14
    .line 15
    invoke-virtual {p1}, LN80/c$e;->getIcon()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconResource(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Lv80/N;

    .line 27
    .line 28
    iget-object v0, v0, Lv80/N;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {p1}, LN80/c$e;->A()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    const/4 v3, 0x0

    .line 39
    const/4 v4, 0x2

    .line 40
    invoke-static {v1, v2, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconTint(I)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    check-cast v0, Lv80/N;

    .line 52
    .line 53
    iget-object v0, v0, Lv80/N;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 54
    .line 55
    invoke-virtual {p1}, LN80/c$e;->u()Z

    .line 56
    .line 57
    .line 58
    move-result v1

    .line 59
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setBadgeVisible(Z)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    check-cast v0, Lv80/N;

    .line 67
    .line 68
    iget-object v0, v0, Lv80/N;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 69
    .line 70
    invoke-virtual {p1}, LN80/c$e;->j()I

    .line 71
    .line 72
    .line 73
    move-result v1

    .line 74
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setBadgeBackgroundTint(I)V

    .line 75
    .line 76
    .line 77
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    check-cast v0, Lv80/N;

    .line 82
    .line 83
    iget-object v0, v0, Lv80/N;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 84
    .line 85
    invoke-virtual {p1}, LN80/c$e;->s()I

    .line 86
    .line 87
    .line 88
    move-result v1

    .line 89
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconBackgroundTint(I)V

    .line 90
    .line 91
    .line 92
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    check-cast v0, Lv80/N;

    .line 97
    .line 98
    iget-object v0, v0, Lv80/N;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 99
    .line 100
    invoke-virtual {p1}, LN80/c$e;->getTitle()Ljava/lang/String;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setTitle(Ljava/lang/CharSequence;)V

    .line 105
    .line 106
    .line 107
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    check-cast v0, Lv80/N;

    .line 112
    .line 113
    iget-object v0, v0, Lv80/N;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 114
    .line 115
    invoke-virtual {p1}, LN80/c$e;->B()Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 120
    .line 121
    .line 122
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    check-cast v0, Lv80/N;

    .line 127
    .line 128
    iget-object v0, v0, Lv80/N;->c:Lorg/xbet/uikit/components/cells/right/CellRightBanner;

    .line 129
    .line 130
    invoke-virtual {p1}, LN80/c$e;->o()I

    .line 131
    .line 132
    .line 133
    move-result v1

    .line 134
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageResource(I)V

    .line 135
    .line 136
    .line 137
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 138
    .line 139
    .line 140
    move-result-object p0

    .line 141
    check-cast p0, Lv80/N;

    .line 142
    .line 143
    invoke-virtual {p0}, Lv80/N;->b()Lorg/xbet/uikit/components/cells/MenuCell;

    .line 144
    .line 145
    .line 146
    move-result-object p0

    .line 147
    invoke-virtual {p1}, LN80/c$e;->f()I

    .line 148
    .line 149
    .line 150
    move-result p1

    .line 151
    invoke-virtual {p0, p1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 152
    .line 153
    .line 154
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 155
    .line 156
    return-object p0
.end method
