.class public final synthetic Lorg/xplatform/aggregator/impl/favorite/presentation/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/recyclerview/widget/RecyclerView;

.field public final synthetic b:Landroid/widget/ProgressBar;


# direct methods
.method public synthetic constructor <init>(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/r;->a:Landroidx/recyclerview/widget/RecyclerView;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/r;->b:Landroid/widget/ProgressBar;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/r;->a:Landroidx/recyclerview/widget/RecyclerView;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/r;->b:Landroid/widget/ProgressBar;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->H2(Landroidx/recyclerview/widget/RecyclerView;Landroid/widget/ProgressBar;)V

    return-void
.end method
