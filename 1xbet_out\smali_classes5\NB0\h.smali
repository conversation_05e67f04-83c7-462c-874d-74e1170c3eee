.class public final synthetic LNB0/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ljava/util/List;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(L<PERSON><PERSON>/util/List;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNB0/h;->a:Ljava/util/List;

    iput p2, p0, LNB0/h;->b:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LNB0/h;->a:Ljava/util/List;

    iget v1, p0, LNB0/h;->b:I

    check-cast p1, Landroidx/compose/ui/layout/g0$a;

    invoke-static {v0, v1, p1}, LNB0/g$b;->e(<PERSON><PERSON><PERSON>/util/List;ILandroidx/compose/ui/layout/g0$a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
