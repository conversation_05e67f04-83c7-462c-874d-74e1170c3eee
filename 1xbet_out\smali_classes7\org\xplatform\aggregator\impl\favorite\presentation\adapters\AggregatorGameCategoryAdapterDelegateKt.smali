.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001aU\u0010\n\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\t0\u00080\u00072\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u00002\u001e\u0010\u0006\u001a\u001a\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0005H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a+\u0010\u0010\u001a\u00020\u0003*\u0008\u0012\u0004\u0012\u00020\u000c0\u00082\u0006\u0010\u000e\u001a\u00020\r2\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "Lkotlin/Function2;",
        "",
        "",
        "",
        "onGameClick",
        "Lkotlin/Function3;",
        "onFavoriteClick",
        "LA4/c;",
        "",
        "",
        "f",
        "(Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;",
        "LN21/k;",
        "Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;",
        "recycler",
        "resetScrollState",
        "l",
        "(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;Z)V",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->k(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->i(LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->j(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->h(Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m0;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;
    .locals 3
    .param p0    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;",
            "LOc/n<",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/Boolean;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LY91/a;

    .line 2
    .line 3
    invoke-direct {v0}, LY91/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LY91/b;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, LY91/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$aggregatorGameHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$aggregatorGameHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$aggregatorGameHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$aggregatorGameHeaderAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/m0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/m0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/m0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function2;LOc/n;LB4/a;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LS91/m0;

    .line 6
    .line 7
    iget-object v0, v0, LS91/m0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    new-instance v1, LY91/c;

    .line 10
    .line 11
    invoke-direct {v1, p2}, LY91/c;-><init>(LB4/a;)V

    .line 12
    .line 13
    .line 14
    const/4 v2, 0x1

    .line 15
    const/4 v3, 0x0

    .line 16
    invoke-static {v3, v1, v2, v3}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    check-cast v0, LS91/m0;

    .line 28
    .line 29
    iget-object v0, v0, LS91/m0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 30
    .line 31
    new-instance v1, LY91/d;

    .line 32
    .line 33
    invoke-direct {v1, p0, p2}, LY91/d;-><init>(Lkotlin/jvm/functions/Function2;LB4/a;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 40
    .line 41
    .line 42
    move-result-object p0

    .line 43
    check-cast p0, LS91/m0;

    .line 44
    .line 45
    iget-object p0, p0, LS91/m0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 46
    .line 47
    new-instance v0, LY91/e;

    .line 48
    .line 49
    invoke-direct {v0, p1, p2}, LY91/e;-><init>(LOc/n;LB4/a;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 53
    .line 54
    .line 55
    new-instance p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;

    .line 56
    .line 57
    invoke-direct {p0, p2, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;-><init>(LB4/a;LB4/a;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 61
    .line 62
    .line 63
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 64
    .line 65
    return-object p0
.end method

.method public static final i(LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lt81/b;

    .line 6
    .line 7
    invoke-virtual {p0}, Lt81/b;->o()Lkotlin/jvm/functions/Function0;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function2;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p2}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, Lt81/b;

    .line 14
    .line 15
    invoke-virtual {p1}, Lt81/b;->s()Z

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p2, p1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 27
    .line 28
    return-object p0
.end method

.method public static final k(LOc/n;LB4/a;LN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p2}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p2}, LN21/k;->c()LN21/m;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-virtual {p2}, LN21/m;->b()Z

    .line 14
    .line 15
    .line 16
    move-result p2

    .line 17
    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    check-cast p1, Lt81/b;

    .line 26
    .line 27
    invoke-virtual {p1}, Lt81/b;->s()Z

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-interface {p0, v0, p2, p1}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 39
    .line 40
    return-object p0
.end method

.method public static final l(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;Z)V
    .locals 0
    .param p0    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LN21/k;",
            ">;",
            "Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;",
            "Z)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1, p0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    if-eqz p2, :cond_0

    .line 5
    .line 6
    const/4 p0, 0x0

    .line 7
    invoke-virtual {p1, p0}, Landroidx/recyclerview/widget/RecyclerView;->scrollToPosition(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public static synthetic m(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;ZILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    const/4 p2, 0x0

    .line 6
    :cond_0
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->l(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method
