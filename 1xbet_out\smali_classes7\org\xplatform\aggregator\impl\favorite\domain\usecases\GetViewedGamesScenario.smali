.class public final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u00002\u00020\u0001B!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ$\u0010\u000f\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000e0\r0\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0086\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0013R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
        "",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;",
        "getFavoriteUpdateFlowUseCase",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;",
        "getViewedGamesUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;Li8/j;)V",
        "",
        "forCarousel",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "b",
        "(Z)Lkotlinx/coroutines/flow/e;",
        "a",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;",
        "c",
        "Li8/j;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;Li8/j;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->c:Li8/j;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->a:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final b(Z)Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->b:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->c:Li8/j;

    .line 4
    .line 5
    invoke-interface {v1}, Li8/j;->invoke()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, p1, v1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesUseCase;->d(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-direct {v0, p0, v1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1
.end method
