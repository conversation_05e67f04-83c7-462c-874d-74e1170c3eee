.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteViewModel$gameFinished$2"
    f = "AfricanRouletteViewModel.kt"
    l = {
        0x144
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->S3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;

    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;-><init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v1

    .line 26
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 30
    .line 31
    new-instance v4, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;

    .line 32
    .line 33
    const/4 v8, 0x7

    .line 34
    const/4 v9, 0x0

    .line 35
    const/4 v5, 0x0

    .line 36
    const/4 v6, 0x0

    .line 37
    const/4 v7, 0x0

    .line 38
    invoke-direct/range {v4 .. v9}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$b;-><init>(ZFFILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    .line 40
    .line 41
    invoke-static {v2, v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->O3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 42
    .line 43
    .line 44
    iget-object v2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 45
    .line 46
    new-instance v4, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;

    .line 47
    .line 48
    invoke-direct {v4, v3}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d$a;-><init>(Z)V

    .line 49
    .line 50
    .line 51
    invoke-static {v2, v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->O3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$d;)V

    .line 52
    .line 53
    .line 54
    iget-object v2, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 55
    .line 56
    invoke-static {v2}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->x3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    invoke-virtual {v2}, Lorg/xbet/african_roulette/domain/interactors/AfricanRouletteInteractor;->l()Lig/b;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    iget-object v4, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 65
    .line 66
    invoke-static {v4}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->w3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    new-instance v5, LTv/a$j;

    .line 71
    .line 72
    invoke-virtual {v2}, Lig/b;->e()D

    .line 73
    .line 74
    .line 75
    move-result-wide v6

    .line 76
    sget-object v8, Lorg/xbet/core/domain/StatusBetEnum;->UNDEFINED:Lorg/xbet/core/domain/StatusBetEnum;

    .line 77
    .line 78
    invoke-virtual {v2}, Lig/b;->b()D

    .line 79
    .line 80
    .line 81
    move-result-wide v10

    .line 82
    invoke-virtual {v2}, Lig/b;->c()D

    .line 83
    .line 84
    .line 85
    move-result-wide v12

    .line 86
    iget-object v9, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    .line 87
    .line 88
    invoke-static {v9}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->B3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;

    .line 89
    .line 90
    .line 91
    move-result-object v9

    .line 92
    invoke-virtual {v9}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 93
    .line 94
    .line 95
    move-result-object v9

    .line 96
    invoke-virtual {v9}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 97
    .line 98
    .line 99
    move-result-object v14

    .line 100
    invoke-virtual {v2}, Lig/b;->a()J

    .line 101
    .line 102
    .line 103
    move-result-wide v15

    .line 104
    const/4 v9, 0x0

    .line 105
    invoke-direct/range {v5 .. v16}, LTv/a$j;-><init>(DLorg/xbet/core/domain/StatusBetEnum;ZDDLorg/xbet/games_section/api/models/GameBonusType;J)V

    .line 106
    .line 107
    .line 108
    iput v3, v0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$gameFinished$2;->label:I

    .line 109
    .line 110
    invoke-virtual {v4, v5, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    if-ne v2, v1, :cond_2

    .line 115
    .line 116
    return-object v1

    .line 117
    :cond_2
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 118
    .line 119
    return-object v1
.end method
