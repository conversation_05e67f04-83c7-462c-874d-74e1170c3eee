.class final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.data.repositories.AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1"
    f = "AggregatorFavoritesRepositoryImpl.kt"
    l = {
        0xdf,
        0xe5
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->c(IJLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Le8/b<",
        "+",
        "LL91/e;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "",
        "token",
        "Le8/b;",
        "LL91/e;",
        "<anonymous>",
        "(Ljava/lang/String;)Le8/b;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $limit:I

.field final synthetic $partitionId:J

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;


# direct methods
.method public constructor <init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$partitionId:J

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iput p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$limit:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$partitionId:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iget v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$limit:I

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;-><init>(JLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    move-object v9, p0

    .line 31
    goto :goto_0

    .line 32
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->L$0:Ljava/lang/Object;

    .line 36
    .line 37
    move-object v6, p1

    .line 38
    check-cast v6, Ljava/lang/String;

    .line 39
    .line 40
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$partitionId:J

    .line 41
    .line 42
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 43
    .line 44
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 45
    .line 46
    .line 47
    move-result-wide v7

    .line 48
    cmp-long p1, v4, v7

    .line 49
    .line 50
    if-nez p1, :cond_4

    .line 51
    .line 52
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 53
    .line 54
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->t(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    iget v5, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$limit:I

    .line 59
    .line 60
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 61
    .line 62
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->u(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lc8/h;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    invoke-interface {p1}, Lc8/h;->a()Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v8

    .line 70
    iput v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->label:I

    .line 71
    .line 72
    const/4 v7, 0x0

    .line 73
    const/4 v10, 0x4

    .line 74
    const/4 v11, 0x0

    .line 75
    move-object v9, p0

    .line 76
    invoke-static/range {v4 .. v11}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->x(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;ILjava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    if-ne p1, v0, :cond_3

    .line 81
    .line 82
    goto :goto_1

    .line 83
    :cond_3
    :goto_0
    check-cast p1, Le8/b;

    .line 84
    .line 85
    return-object p1

    .line 86
    :cond_4
    move-object v9, p0

    .line 87
    iget-object p1, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 88
    .line 89
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->t(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;

    .line 90
    .line 91
    .line 92
    move-result-object v4

    .line 93
    iget v5, v9, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$limit:I

    .line 94
    .line 95
    move-object v11, v9

    .line 96
    iget-wide v8, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->$partitionId:J

    .line 97
    .line 98
    iget-object p1, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 99
    .line 100
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->u(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lc8/h;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    invoke-interface {p1}, Lc8/h;->a()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v10

    .line 108
    iput v2, v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$getRecommendedGames$games$1;->label:I

    .line 109
    .line 110
    const/4 v7, 0x0

    .line 111
    const/4 v12, 0x4

    .line 112
    const/4 v13, 0x0

    .line 113
    invoke-static/range {v4 .. v13}, Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;->z(Lorg/xplatform/aggregator/impl/core/data/datasources/AggregatorRemoteDataSource;ILjava/lang/String;IJLjava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    if-ne p1, v0, :cond_5

    .line 118
    .line 119
    :goto_1
    return-object v0

    .line 120
    :cond_5
    :goto_2
    check-cast p1, Le8/b;

    .line 121
    .line 122
    return-object p1
.end method
