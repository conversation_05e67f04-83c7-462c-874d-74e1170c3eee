.class public final synthetic LoM0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LoM0/c;

.field public final synthetic b:LzM0/a;


# direct methods
.method public synthetic constructor <init>(LoM0/c;LzM0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LoM0/b;->a:LoM0/c;

    iput-object p2, p0, LoM0/b;->b:LzM0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LoM0/b;->a:LoM0/c;

    iget-object v1, p0, LoM0/b;->b:LzM0/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LoM0/c;->b(LoM0/c;LzM0/a;Landroid/view/View;)L<PERSON><PERSON>/Unit;

    move-result-object p1

    return-object p1
.end method
