.class public final synthetic Lix0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lgx0/a;


# direct methods
.method public synthetic constructor <init>(Lgx0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lix0/c;->a:Lgx0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lix0/c;->a:Lgx0/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/location/alllocationsbutton/viewholder/TournamentAllLocationsViewHolderKt;->c(Lgx0/a;Landroid/view/View;)L<PERSON>lin/Unit;

    move-result-object p1

    return-object p1
.end method
