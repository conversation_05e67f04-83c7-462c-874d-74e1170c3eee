.class public final synthetic Lorg/xbet/special_event/impl/utils/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Ljava/lang/Throwable;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/special_event/impl/utils/a;->a:Ljava/lang/Throwable;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/a;->a:Ljava/lang/Throwable;

    invoke-static {v0}, Lorg/xbet/special_event/impl/utils/ErrorUtilKt;->a(Ljava/lang/Throwable;)Z

    move-result v0

    invoke-static {v0}, <PERSON><PERSON><PERSON>/lang/<PERSON>;->valueOf(Z)Ljava/lang/<PERSON>an;

    move-result-object v0

    return-object v0
.end method
