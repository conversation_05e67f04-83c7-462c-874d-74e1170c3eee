.class public final Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;
.super Lcom/xbet/onexcore/data/model/ServerException;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0006\u001a\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;",
        "Lcom/xbet/onexcore/data/model/ServerException;",
        "Lwa1/a;",
        "errorModel",
        "<init>",
        "(Lwa1/a;)V",
        "Lwa1/a;",
        "getErrorModel",
        "()Lwa1/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final errorModel:Lwa1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lwa1/a;)V
    .locals 7
    .param p1    # Lwa1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lwa1/a;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual {p1}, Lwa1/a;->b()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    const/16 v5, 0xc

    .line 10
    .line 11
    const/4 v6, 0x0

    .line 12
    const/4 v3, 0x0

    .line 13
    const/4 v4, 0x0

    .line 14
    move-object v0, p0

    .line 15
    invoke-direct/range {v0 .. v6}, Lcom/xbet/onexcore/data/model/ServerException;-><init>(Ljava/lang/String;ILcom/xbet/onexcore/data/errors/ServerError;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 16
    .line 17
    .line 18
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;->errorModel:Lwa1/a;

    .line 19
    .line 20
    return-void
.end method


# virtual methods
.method public final getErrorModel()Lwa1/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;->errorModel:Lwa1/a;

    .line 2
    .line 3
    return-object v0
.end method
