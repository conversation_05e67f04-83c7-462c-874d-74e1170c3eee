.class public final LtY0/b$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtY0/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LtY0/b;->a(FFFFF)LtY0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0011\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u000f*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001R\u001a\u0010\u0006\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0004\u001a\u0004\u0008\u0003\u0010\u0005R\u001a\u0010\t\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0007\u0010\u0004\u001a\u0004\u0008\u0008\u0010\u0005R\u001a\u0010\u000c\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u0004\u001a\u0004\u0008\u000b\u0010\u0005R\u001a\u0010\u000f\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u0004\u001a\u0004\u0008\u000e\u0010\u0005R\u001a\u0010\u0010\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0008\u0010\u0004\u001a\u0004\u0008\u0007\u0010\u0005\u00a8\u0006\u0011"
    }
    d2 = {
        "tY0/b$a",
        "LtY0/a;",
        "",
        "a",
        "F",
        "()F",
        "xSpacing",
        "b",
        "e",
        "scalableStartPadding",
        "c",
        "g",
        "scalableEndPadding",
        "d",
        "i",
        "unscalableStartPadding",
        "unscalableEndPadding",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:F

.field public final b:F

.field public final c:F

.field public final d:F

.field public final e:F


# direct methods
.method public constructor <init>(FFFFF)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, LtY0/b$a;->a:F

    .line 5
    .line 6
    iput p2, p0, LtY0/b$a;->b:F

    .line 7
    .line 8
    iput p3, p0, LtY0/b$a;->c:F

    .line 9
    .line 10
    iput p4, p0, LtY0/b$a;->d:F

    .line 11
    .line 12
    iput p5, p0, LtY0/b$a;->e:F

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public a()F
    .locals 1

    .line 1
    iget v0, p0, LtY0/b$a;->a:F

    .line 2
    .line 3
    return v0
.end method

.method public b()F
    .locals 1

    .line 1
    iget v0, p0, LtY0/b$a;->e:F

    .line 2
    .line 3
    return v0
.end method

.method public c(F)LtY0/a;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LtY0/a$a;->e(LtY0/a;F)LtY0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public d(I)F
    .locals 0

    .line 1
    invoke-static {p0, p1}, LtY0/a$a;->a(LtY0/a;I)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e()F
    .locals 1

    .line 1
    iget v0, p0, LtY0/b$a;->b:F

    .line 2
    .line 3
    return v0
.end method

.method public f()F
    .locals 1

    .line 1
    invoke-static {p0}, LtY0/a$a;->d(LtY0/a;)F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public g()F
    .locals 1

    .line 1
    iget v0, p0, LtY0/b$a;->c:F

    .line 2
    .line 3
    return v0
.end method

.method public h()F
    .locals 1

    .line 1
    invoke-static {p0}, LtY0/a$a;->b(LtY0/a;)F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public i()F
    .locals 1

    .line 1
    iget v0, p0, LtY0/b$a;->d:F

    .line 2
    .line 3
    return v0
.end method

.method public j()F
    .locals 1

    .line 1
    invoke-static {p0}, LtY0/a$a;->c(LtY0/a;)F

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method
