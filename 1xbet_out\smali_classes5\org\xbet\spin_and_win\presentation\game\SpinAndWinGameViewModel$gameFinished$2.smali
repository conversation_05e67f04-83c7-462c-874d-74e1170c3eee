.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameViewModel$gameFinished$2"
    f = "SpinAndWinGameViewModel.kt"
    l = {
        0x148
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->e4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;

    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v1

    .line 26
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 30
    .line 31
    new-instance v4, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 32
    .line 33
    invoke-direct {v4, v3}, Lorg/xbet/spin_and_win/presentation/game/s$a;-><init>(Z)V

    .line 34
    .line 35
    .line 36
    invoke-static {v2, v4}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->Z3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/s;)V

    .line 37
    .line 38
    .line 39
    iget-object v2, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 40
    .line 41
    invoke-static {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->E3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/d;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    invoke-virtual {v2}, Lez0/d;->a()Ldz0/b;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    iget-object v4, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 50
    .line 51
    invoke-static {v4}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->w3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    new-instance v5, LTv/a$j;

    .line 56
    .line 57
    invoke-virtual {v2}, Ldz0/b;->f()D

    .line 58
    .line 59
    .line 60
    move-result-wide v6

    .line 61
    sget-object v8, Lorg/xbet/core/domain/StatusBetEnum;->UNDEFINED:Lorg/xbet/core/domain/StatusBetEnum;

    .line 62
    .line 63
    invoke-virtual {v2}, Ldz0/b;->b()D

    .line 64
    .line 65
    .line 66
    move-result-wide v10

    .line 67
    invoke-virtual {v2}, Ldz0/b;->c()D

    .line 68
    .line 69
    .line 70
    move-result-wide v12

    .line 71
    iget-object v9, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 72
    .line 73
    invoke-static {v9}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->C3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lorg/xbet/core/domain/usecases/bonus/e;

    .line 74
    .line 75
    .line 76
    move-result-object v9

    .line 77
    invoke-virtual {v9}, Lorg/xbet/core/domain/usecases/bonus/e;->a()Lorg/xbet/games_section/api/models/GameBonus;

    .line 78
    .line 79
    .line 80
    move-result-object v9

    .line 81
    invoke-virtual {v9}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 82
    .line 83
    .line 84
    move-result-object v14

    .line 85
    invoke-virtual {v2}, Ldz0/b;->a()J

    .line 86
    .line 87
    .line 88
    move-result-wide v15

    .line 89
    const/4 v9, 0x0

    .line 90
    invoke-direct/range {v5 .. v16}, LTv/a$j;-><init>(DLorg/xbet/core/domain/StatusBetEnum;ZDDLorg/xbet/games_section/api/models/GameBonusType;J)V

    .line 91
    .line 92
    .line 93
    iput v3, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$gameFinished$2;->label:I

    .line 94
    .line 95
    invoke-virtual {v4, v5, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    if-ne v2, v1, :cond_2

    .line 100
    .line 101
    return-object v1

    .line 102
    :cond_2
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object v1
.end method
