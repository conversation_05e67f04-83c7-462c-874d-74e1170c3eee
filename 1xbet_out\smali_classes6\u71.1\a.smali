.class public final Lu71/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lu71/a$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u001a\u0011\u0010\u0002\u001a\u00020\u0001*\u00020\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
        "",
        "a",
        "(Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;)Ljava/lang/String;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;)Ljava/lang/String;
    .locals 1
    .param p0    # Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lu71/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    packed-switch p0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    const-string p0, "1xgames"

    .line 19
    .line 20
    return-object p0

    .line 21
    :pswitch_1
    const-string p0, "casino"

    .line 22
    .line 23
    return-object p0

    .line 24
    :pswitch_2
    const-string p0, "search"

    .line 25
    .line 26
    return-object p0

    .line 27
    :pswitch_3
    const-string p0, "cyber"

    .line 28
    .line 29
    return-object p0

    .line 30
    :pswitch_4
    const-string p0, "results"

    .line 31
    .line 32
    return-object p0

    .line 33
    :pswitch_5
    const-string p0, "live"

    .line 34
    .line 35
    return-object p0

    .line 36
    nop

    .line 37
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
