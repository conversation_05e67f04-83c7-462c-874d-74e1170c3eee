.class public final enum Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0011\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "SIZE_96",
        "SIZE_80",
        "SIZE_72",
        "SIZE_64",
        "SIZE_56",
        "SIZE_48",
        "SIZE_44",
        "SIZE_40",
        "SIZE_36",
        "SIZE_32",
        "SIZE_28",
        "SIZE_24",
        "SIZE_20",
        "SIZE_16",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_16:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_20:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_24:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_28:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_32:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_36:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_40:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_44:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_48:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_56:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_64:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_72:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_80:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

.field public static final enum SIZE_96:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 2
    .line 3
    const-string v1, "SIZE_96"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_96:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 12
    .line 13
    const-string v1, "SIZE_80"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_80:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 22
    .line 23
    const-string v1, "SIZE_72"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_72:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 32
    .line 33
    const-string v1, "SIZE_64"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_64:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 42
    .line 43
    const-string v1, "SIZE_56"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_56:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 50
    .line 51
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 52
    .line 53
    const-string v1, "SIZE_48"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_48:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 60
    .line 61
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 62
    .line 63
    const-string v1, "SIZE_44"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_44:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 72
    .line 73
    const-string v1, "SIZE_40"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_40:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 80
    .line 81
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 82
    .line 83
    const-string v1, "SIZE_36"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_36:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 91
    .line 92
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 93
    .line 94
    const-string v1, "SIZE_32"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_32:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 102
    .line 103
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 104
    .line 105
    const-string v1, "SIZE_28"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_28:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 113
    .line 114
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 115
    .line 116
    const-string v1, "SIZE_24"

    .line 117
    .line 118
    const/16 v2, 0xb

    .line 119
    .line 120
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 121
    .line 122
    .line 123
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_24:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 124
    .line 125
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 126
    .line 127
    const-string v1, "SIZE_20"

    .line 128
    .line 129
    const/16 v2, 0xc

    .line 130
    .line 131
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 132
    .line 133
    .line 134
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_20:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 135
    .line 136
    new-instance v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 137
    .line 138
    const-string v1, "SIZE_16"

    .line 139
    .line 140
    const/16 v2, 0xd

    .line 141
    .line 142
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;-><init>(Ljava/lang/String;I)V

    .line 143
    .line 144
    .line 145
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_16:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 146
    .line 147
    invoke-static {}, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->a()[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 148
    .line 149
    .line 150
    move-result-object v0

    .line 151
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->$VALUES:[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 152
    .line 153
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 154
    .line 155
    .line 156
    move-result-object v0

    .line 157
    sput-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->$ENTRIES:Lkotlin/enums/a;

    .line 158
    .line 159
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;
    .locals 3

    .line 1
    const/16 v0, 0xe

    new-array v0, v0, [Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_96:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_80:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_72:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_64:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_56:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_48:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_44:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_40:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_36:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_32:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_28:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_24:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0xb

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_20:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0xc

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->SIZE_16:Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    const/16 v2, 0xd

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;->$VALUES:[Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_sport/compose/team_logo/model/DsTeamLogoSizeType;

    .line 8
    .line 9
    return-object v0
.end method
