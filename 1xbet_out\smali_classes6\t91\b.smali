.class public final Lt91/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lr4/d;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u0007H\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0004\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\r\u00a8\u0006\u000f"
    }
    d2 = {
        "Lt91/b;",
        "Lr4/d;",
        "",
        "balanceId",
        "productId",
        "<init>",
        "(JJ)V",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "a",
        "J",
        "b",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:J

.field public final b:J


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(JJ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lt91/b;->a:J

    .line 5
    .line 6
    iput-wide p3, p0, Lt91/b;->b:J

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 4
    .param p1    # Landroidx/fragment/app/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object p1, Lorg/xplatform/aggregator/game/impl/gamessingle/presentation/WalletAddGetFragment;->m0:Lorg/xplatform/aggregator/game/impl/gamessingle/presentation/WalletAddGetFragment$a;

    .line 2
    .line 3
    iget-wide v0, p0, Lt91/b;->a:J

    .line 4
    .line 5
    iget-wide v2, p0, Lt91/b;->b:J

    .line 6
    .line 7
    invoke-virtual {p1, v0, v1, v2, v3}, Lorg/xplatform/aggregator/game/impl/gamessingle/presentation/WalletAddGetFragment$a;->a(JJ)Lorg/xplatform/aggregator/game/impl/gamessingle/presentation/WalletAddGetFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method

.method public getClearContainer()Z
    .locals 1

    .line 1
    invoke-static {p0}, Lr4/d$b;->a(Lr4/d;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public getScreenKey()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p0}, Lr4/d$b;->b(Lr4/d;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
