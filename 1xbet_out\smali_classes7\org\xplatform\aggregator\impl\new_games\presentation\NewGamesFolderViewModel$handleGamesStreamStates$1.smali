.class final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.new_games.presentation.NewGamesFolderViewModel$handleGamesStreamStates$1"
    f = "NewGamesFolderViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->d5()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Landroidx/paging/PagingData<",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ljava/util/Set<",
        "+",
        "Ljava/lang/Long;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Landroidx/paging/PagingData<",
        "LN21/d;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\"\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00002\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00040\u0003H\n\u00a2\u0006\u0004\u0008\u0007\u0010\u0008"
    }
    d2 = {
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "pagingData",
        "",
        "",
        "favoriteIds",
        "LN21/d;",
        "<anonymous>",
        "(Landroidx/paging/PagingData;Ljava/util/Set;)Landroidx/paging/PagingData;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invoke(Landroidx/paging/PagingData;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/Set<",
            "Ljava/lang/Long;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Landroidx/paging/PagingData<",
            "LN21/d;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Landroidx/paging/PagingData;

    check-cast p2, Ljava/util/Set;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->invoke(Landroidx/paging/PagingData;Ljava/util/Set;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Landroidx/paging/PagingData;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Ljava/util/Set;

    .line 18
    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;

    .line 20
    .line 21
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1;->this$0:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 22
    .line 23
    const/4 v3, 0x0

    .line 24
    invoke-direct {v1, v2, v0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$handleGamesStreamStates$1$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;Ljava/util/Set;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {p1, v1}, Landroidx/paging/E;->c(Landroidx/paging/PagingData;Lkotlin/jvm/functions/Function2;)Landroidx/paging/PagingData;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    return-object p1

    .line 32
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 33
    .line 34
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 35
    .line 36
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    throw p1
.end method
