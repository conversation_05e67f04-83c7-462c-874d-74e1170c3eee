.class public final Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a1\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0014\u0010\u0003\u001a\u0010\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LP31/g;",
        "",
        "clickListener",
        "LA4/c;",
        "",
        "LP31/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/S;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/S;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LP31/g;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LP31/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LO31/g;

    .line 2
    .line 3
    invoke-direct {v0}, LO31/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LO31/h;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LO31/h;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt$sportCollectionDelegateAdapter$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt$sportCollectionDelegateAdapter$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt$sportCollectionDelegateAdapter$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt$sportCollectionDelegateAdapter$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/S;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/S;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/S;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC31/S;

    .line 6
    .line 7
    iget-object v0, v0, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 8
    .line 9
    new-instance v1, LO31/i;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, LO31/i;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 12
    .line 13
    .line 14
    const/4 p0, 0x1

    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 17
    .line 18
    .line 19
    new-instance p0, LO31/j;

    .line 20
    .line 21
    invoke-direct {p0, p1}, LO31/j;-><init>(LB4/a;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 25
    .line 26
    .line 27
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 28
    .line 29
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, LC31/S;

    .line 12
    .line 13
    iget-object p1, p1, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 14
    .line 15
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    check-cast v0, LP31/g;

    .line 20
    .line 21
    invoke-virtual {v0}, LP31/g;->f()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setTitle(Ljava/lang/CharSequence;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    check-cast p1, LC31/S;

    .line 33
    .line 34
    iget-object p1, p1, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 35
    .line 36
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    check-cast v0, LP31/g;

    .line 41
    .line 42
    invoke-virtual {v0}, LP31/g;->d()LL11/c;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(LL11/c;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    check-cast p1, LC31/S;

    .line 54
    .line 55
    iget-object p1, p1, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 56
    .line 57
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    check-cast v0, LP31/g;

    .line 62
    .line 63
    invoke-virtual {v0}, LP31/g;->e()Z

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->f(Z)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    check-cast p1, LC31/S;

    .line 75
    .line 76
    iget-object p1, p1, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 77
    .line 78
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p0

    .line 82
    check-cast p0, LP31/g;

    .line 83
    .line 84
    invoke-virtual {p0}, LP31/g;->g()Z

    .line 85
    .line 86
    .line 87
    move-result p0

    .line 88
    invoke-virtual {p1, p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setItemSelected(Z)V

    .line 89
    .line 90
    .line 91
    goto/16 :goto_2

    .line 92
    .line 93
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 94
    .line 95
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 96
    .line 97
    .line 98
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    if-eqz v1, :cond_1

    .line 107
    .line 108
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    check-cast v1, Ljava/util/Collection;

    .line 113
    .line 114
    check-cast v1, Ljava/lang/Iterable;

    .line 115
    .line 116
    invoke-static {v0, v1}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 117
    .line 118
    .line 119
    goto :goto_0

    .line 120
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    :cond_2
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 125
    .line 126
    .line 127
    move-result v0

    .line 128
    if-eqz v0, :cond_6

    .line 129
    .line 130
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    instance-of v1, v0, LP31/f;

    .line 135
    .line 136
    if-eqz v1, :cond_3

    .line 137
    .line 138
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 139
    .line 140
    .line 141
    move-result-object v0

    .line 142
    check-cast v0, LC31/S;

    .line 143
    .line 144
    iget-object v0, v0, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 145
    .line 146
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 147
    .line 148
    .line 149
    move-result-object v1

    .line 150
    check-cast v1, LP31/g;

    .line 151
    .line 152
    invoke-virtual {v1}, LP31/g;->f()Ljava/lang/String;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setTitle(Ljava/lang/CharSequence;)V

    .line 157
    .line 158
    .line 159
    goto :goto_1

    .line 160
    :cond_3
    instance-of v1, v0, LP31/c;

    .line 161
    .line 162
    if-eqz v1, :cond_4

    .line 163
    .line 164
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 165
    .line 166
    .line 167
    move-result-object v0

    .line 168
    check-cast v0, LC31/S;

    .line 169
    .line 170
    iget-object v0, v0, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 171
    .line 172
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    check-cast v1, LP31/g;

    .line 177
    .line 178
    invoke-virtual {v1}, LP31/g;->d()LL11/c;

    .line 179
    .line 180
    .line 181
    move-result-object v1

    .line 182
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(LL11/c;)V

    .line 183
    .line 184
    .line 185
    goto :goto_1

    .line 186
    :cond_4
    instance-of v1, v0, LP31/e;

    .line 187
    .line 188
    if-eqz v1, :cond_5

    .line 189
    .line 190
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    check-cast v0, LC31/S;

    .line 195
    .line 196
    iget-object v0, v0, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 197
    .line 198
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object v1

    .line 202
    check-cast v1, LP31/g;

    .line 203
    .line 204
    invoke-virtual {v1}, LP31/g;->e()Z

    .line 205
    .line 206
    .line 207
    move-result v1

    .line 208
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->f(Z)V

    .line 209
    .line 210
    .line 211
    goto :goto_1

    .line 212
    :cond_5
    instance-of v0, v0, LP31/d;

    .line 213
    .line 214
    if-eqz v0, :cond_2

    .line 215
    .line 216
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 217
    .line 218
    .line 219
    move-result-object v0

    .line 220
    check-cast v0, LC31/S;

    .line 221
    .line 222
    iget-object v0, v0, LC31/S;->b:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    .line 223
    .line 224
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    move-result-object v1

    .line 228
    check-cast v1, LP31/g;

    .line 229
    .line 230
    invoke-virtual {v1}, LP31/g;->g()Z

    .line 231
    .line 232
    .line 233
    move-result v1

    .line 234
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setItemSelected(Z)V

    .line 235
    .line 236
    .line 237
    goto :goto_1

    .line 238
    :cond_6
    :goto_2
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 239
    .line 240
    return-object p0
.end method
