.class public final Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u00002\u00020\u0001B)\u0012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0002\u0012\u0012\u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0019\u0010\u000e\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\r0\u000c0\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u001a\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R \u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;",
        "",
        "Lkotlin/Function0;",
        "",
        "getCheckedIndex",
        "Lkotlin/Function1;",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "",
        "clickListener",
        "<init>",
        "(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V",
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "()LA4/c;",
        "a",
        "Lkotlin/jvm/functions/Function0;",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Ljava/lang/Integer;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->a:Lkotlin/jvm/functions/Function0;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->b:Lkotlin/jvm/functions/Function1;

    .line 7
    .line 8
    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->f(Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Laa1/c;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->g(LB4/a;Laa1/c;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s0;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/s0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/s0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/s0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    new-instance v0, Laa1/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->b:Lkotlin/jvm/functions/Function1;

    .line 4
    .line 5
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;->a:Lkotlin/jvm/functions/Function0;

    .line 6
    .line 7
    invoke-direct {v0, v1, p0}, Laa1/c;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V

    .line 8
    .line 9
    .line 10
    new-instance p0, Lba1/t;

    .line 11
    .line 12
    invoke-direct {p0, p1, v0}, Lba1/t;-><init>(LB4/a;Laa1/c;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final g(LB4/a;Laa1/c;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lha1/c;

    .line 6
    .line 7
    invoke-virtual {p2}, Lha1/c;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, LS91/s0;

    .line 16
    .line 17
    iget-object p0, p0, LS91/s0;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 18
    .line 19
    invoke-virtual {p0, p1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1, p2}, LA4/e;->setItems(Ljava/util/List;)V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method


# virtual methods
.method public final d()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lba1/r;

    .line 2
    .line 3
    invoke-direct {v0}, Lba1/r;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lba1/s;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lba1/s;-><init>(Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate;)V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate$getAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate$getAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate$getAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/gifts/adapter_delegate/ContainerForChipAdapterDelegate$getAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method
