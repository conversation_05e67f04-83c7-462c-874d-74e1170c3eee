.class public final LNA0/m;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a%\u0010\u0005\u001a\u0004\u0018\u00010\u0004*\u00020\u00002\u000e\u0010\u0003\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LYA0/a;",
        "",
        "LcB0/a;",
        "shortStatisticItemModelList",
        "LRA0/e;",
        "a",
        "(LYA0/a;Ljava/util/List;)LRA0/e;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LYA0/a;Ljava/util/List;)LRA0/e;
    .locals 10
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LYA0/a;",
            "Ljava/util/List<",
            "LcB0/a;",
            ">;)",
            "LRA0/e;"
        }
    .end annotation

    .line 1
    if-eqz p1, :cond_a

    .line 2
    .line 3
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x1

    .line 8
    xor-int/2addr v0, v1

    .line 9
    if-ne v0, v1, :cond_a

    .line 10
    .line 11
    new-instance v2, LRA0/e;

    .line 12
    .line 13
    sget-object v0, LDX0/e;->a:LDX0/e;

    .line 14
    .line 15
    invoke-virtual {p0}, LYA0/a;->D()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    invoke-static {v3}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    check-cast v3, Ljava/lang/String;

    .line 24
    .line 25
    const-string v4, ""

    .line 26
    .line 27
    if-nez v3, :cond_0

    .line 28
    .line 29
    move-object v3, v4

    .line 30
    :cond_0
    invoke-virtual {p0}, LYA0/a;->F()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    invoke-static {v5}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v5

    .line 38
    check-cast v5, Ljava/lang/Long;

    .line 39
    .line 40
    const-wide/16 v6, 0x0

    .line 41
    .line 42
    if-eqz v5, :cond_1

    .line 43
    .line 44
    invoke-virtual {v5}, Ljava/lang/Long;->longValue()J

    .line 45
    .line 46
    .line 47
    move-result-wide v8

    .line 48
    goto :goto_0

    .line 49
    :cond_1
    move-wide v8, v6

    .line 50
    :goto_0
    invoke-virtual {v0, v3, v8, v9}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {p0}, LYA0/a;->D()Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object v5

    .line 58
    invoke-static {v5, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v5

    .line 62
    check-cast v5, Ljava/lang/String;

    .line 63
    .line 64
    if-nez v5, :cond_2

    .line 65
    .line 66
    move-object v5, v4

    .line 67
    :cond_2
    invoke-virtual {p0}, LYA0/a;->F()Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v8

    .line 71
    invoke-static {v8, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v8

    .line 75
    check-cast v8, Ljava/lang/Long;

    .line 76
    .line 77
    if-eqz v8, :cond_3

    .line 78
    .line 79
    invoke-virtual {v8}, Ljava/lang/Long;->longValue()J

    .line 80
    .line 81
    .line 82
    move-result-wide v8

    .line 83
    goto :goto_1

    .line 84
    :cond_3
    move-wide v8, v6

    .line 85
    :goto_1
    invoke-virtual {v0, v5, v8, v9}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v5

    .line 89
    filled-new-array {v3, v5}, [Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object v3

    .line 93
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    invoke-virtual {p0}, LYA0/a;->G()Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object v5

    .line 101
    invoke-static {v5}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object v5

    .line 105
    check-cast v5, Ljava/lang/String;

    .line 106
    .line 107
    if-nez v5, :cond_4

    .line 108
    .line 109
    move-object v5, v4

    .line 110
    :cond_4
    invoke-virtual {p0}, LYA0/a;->I()Ljava/util/List;

    .line 111
    .line 112
    .line 113
    move-result-object v8

    .line 114
    invoke-static {v8}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v8

    .line 118
    check-cast v8, Ljava/lang/Long;

    .line 119
    .line 120
    if-eqz v8, :cond_5

    .line 121
    .line 122
    invoke-virtual {v8}, Ljava/lang/Long;->longValue()J

    .line 123
    .line 124
    .line 125
    move-result-wide v8

    .line 126
    goto :goto_2

    .line 127
    :cond_5
    move-wide v8, v6

    .line 128
    :goto_2
    invoke-virtual {v0, v5, v8, v9}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v5

    .line 132
    invoke-virtual {p0}, LYA0/a;->G()Ljava/util/List;

    .line 133
    .line 134
    .line 135
    move-result-object v8

    .line 136
    invoke-static {v8, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 137
    .line 138
    .line 139
    move-result-object v8

    .line 140
    check-cast v8, Ljava/lang/String;

    .line 141
    .line 142
    if-nez v8, :cond_6

    .line 143
    .line 144
    goto :goto_3

    .line 145
    :cond_6
    move-object v4, v8

    .line 146
    :goto_3
    invoke-virtual {p0}, LYA0/a;->I()Ljava/util/List;

    .line 147
    .line 148
    .line 149
    move-result-object v8

    .line 150
    invoke-static {v8, v1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v8

    .line 154
    check-cast v8, Ljava/lang/Long;

    .line 155
    .line 156
    if-eqz v8, :cond_7

    .line 157
    .line 158
    invoke-virtual {v8}, Ljava/lang/Long;->longValue()J

    .line 159
    .line 160
    .line 161
    move-result-wide v6

    .line 162
    :cond_7
    invoke-virtual {v0, v4, v6, v7}, LDX0/e;->b(Ljava/lang/String;J)Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object v0

    .line 166
    filled-new-array {v5, v0}, [Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 171
    .line 172
    .line 173
    move-result-object v4

    .line 174
    invoke-virtual {p0}, LYA0/a;->U()Ljava/util/List;

    .line 175
    .line 176
    .line 177
    move-result-object v0

    .line 178
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 179
    .line 180
    .line 181
    move-result v0

    .line 182
    const/4 v5, 0x0

    .line 183
    if-le v0, v1, :cond_8

    .line 184
    .line 185
    invoke-virtual {p0}, LYA0/a;->V()Ljava/util/List;

    .line 186
    .line 187
    .line 188
    move-result-object v0

    .line 189
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 190
    .line 191
    .line 192
    move-result v0

    .line 193
    if-le v0, v1, :cond_8

    .line 194
    .line 195
    const/4 v5, 0x1

    .line 196
    :cond_8
    const/4 v0, 0x0

    .line 197
    invoke-virtual {p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 198
    .line 199
    .line 200
    move-result-object p0

    .line 201
    sget-object v6, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->HOSTS_VS_GUESTS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 202
    .line 203
    if-ne p0, v6, :cond_9

    .line 204
    .line 205
    const/4 v6, 0x1

    .line 206
    :goto_4
    move-object v7, p1

    .line 207
    goto :goto_5

    .line 208
    :cond_9
    const/4 v6, 0x0

    .line 209
    goto :goto_4

    .line 210
    :goto_5
    invoke-direct/range {v2 .. v7}, LRA0/e;-><init>(Ljava/util/List;Ljava/util/List;ZZLjava/util/List;)V

    .line 211
    .line 212
    .line 213
    return-object v2

    .line 214
    :cond_a
    const/4 p0, 0x0

    .line 215
    return-object p0
.end method
