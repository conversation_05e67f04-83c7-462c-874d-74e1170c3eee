.class public final Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LAT0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000f\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ0\u0010\u0015\u001a\u00020\u00142\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J8\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u0013\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0018\u0010\u001d\u001a\u00020\u00142\u0006\u0010\u0013\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u001e\u0010!\u001a\u0008\u0012\u0004\u0012\u00020 0\u001f2\u0006\u0010\u0013\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0004\u0008!\u0010\u001eJ\u0017\u0010$\u001a\u00020#2\u0006\u0010\"\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008$\u0010%J\u000f\u0010&\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010)\u001a\u00020(H\u0016\u00a2\u0006\u0004\u0008)\u0010*J\u001d\u0010-\u001a\u00020#2\u000c\u0010,\u001a\u0008\u0012\u0004\u0012\u00020+0\u001fH\u0016\u00a2\u0006\u0004\u0008-\u0010.J\u001d\u00100\u001a\u00020#2\u000c\u0010/\u001a\u0008\u0012\u0004\u0012\u00020 0\u001fH\u0016\u00a2\u0006\u0004\u00080\u0010.J0\u00105\u001a\u0002042\u0006\u00102\u001a\u0002012\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u00103\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0004\u00085\u00106J8\u00109\u001a\u0002082\u0006\u00102\u001a\u0002012\u0006\u00107\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u00103\u001a\u00020\u0010H\u0082@\u00a2\u0006\u0004\u00089\u0010:J8\u0010;\u001a\u0002042\u0006\u00102\u001a\u0002012\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u0017H\u0082@\u00a2\u0006\u0004\u0008;\u0010<J6\u0010>\u001a\u0002082\u0006\u00102\u001a\u0002012\u0006\u00107\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00172\u000c\u0010=\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u001fH\u0082@\u00a2\u0006\u0004\u0008>\u0010?J\u0018\u0010@\u001a\u0002042\u0006\u00102\u001a\u000201H\u0082@\u00a2\u0006\u0004\u0008@\u0010AJ\u0018\u0010B\u001a\u0002082\u0006\u00102\u001a\u000201H\u0082@\u00a2\u0006\u0004\u0008B\u0010AR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010CR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010DR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010ER\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010F\u00a8\u0006G"
    }
    d2 = {
        "Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;",
        "LAT0/a;",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
        "tileMatchingRemoteDataSource",
        "Lorg/xbet/tile_matching/data/data_sources/a;",
        "tileMatchingDataSource",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "<init>",
        "(Lc8/h;Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lorg/xbet/tile_matching/data/data_sources/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "bonus",
        "",
        "betSum",
        "",
        "accountId",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
        "gameType",
        "LzT0/e;",
        "f",
        "(Lorg/xbet/games_section/api/models/GameBonus;DJLcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "actionStep",
        "row",
        "column",
        "b",
        "(JIIILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "d",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "LzT0/c;",
        "h",
        "game",
        "",
        "i",
        "(LzT0/e;)V",
        "c",
        "()LzT0/e;",
        "LzT0/d;",
        "g",
        "()LzT0/d;",
        "LzT0/b;",
        "gameField",
        "a",
        "(Ljava/util/List;)V",
        "coeffs",
        "e",
        "",
        "auth",
        "bonusId",
        "LvT0/d;",
        "s",
        "(Ljava/lang/String;JDJLkotlin/coroutines/e;)Ljava/lang/Object;",
        "userId",
        "LwT0/d;",
        "t",
        "(Ljava/lang/String;JJDJLkotlin/coroutines/e;)Ljava/lang/Object;",
        "u",
        "(Ljava/lang/String;JIIILkotlin/coroutines/e;)Ljava/lang/Object;",
        "userChoice",
        "v",
        "(Ljava/lang/String;JILjava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "q",
        "(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "r",
        "Lc8/h;",
        "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
        "Lorg/xbet/tile_matching/data/data_sources/a;",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/tile_matching/data/data_sources/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lc8/h;Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lorg/xbet/tile_matching/data/data_sources/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V
    .locals 0
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/tile_matching/data/data_sources/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->a:Lc8/h;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic j(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->q(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic k(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->r(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic l(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;)Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic m(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JDJLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p8}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->s(Ljava/lang/String;JDJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic n(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JJDJLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p10}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->t(Ljava/lang/String;JJDJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic o(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JIIILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p7}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->u(Ljava/lang/String;JIIILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic p(Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Ljava/lang/String;JILjava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->v(Ljava/lang/String;JILjava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public a(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/data/data_sources/a;->d(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b(JIIILcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p6    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JIII",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;

    .line 4
    .line 5
    const/4 v9, 0x0

    .line 6
    move-object v3, p0

    .line 7
    move-wide v4, p1

    .line 8
    move v6, p3

    .line 9
    move v7, p4

    .line 10
    move v8, p5

    .line 11
    move-object/from16 v2, p6

    .line 12
    .line 13
    invoke-direct/range {v1 .. v9}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeAction$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JIIILkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    move-object/from16 p1, p7

    .line 17
    .line 18
    invoke-virtual {v0, v1, p1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->l(LOc/n;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    return-object p1
.end method

.method public c()LzT0/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/tile_matching/data/data_sources/a;->b()LzT0/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public d(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p1, p0, v2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getActiveGame$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {v0, v1, p2}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public e(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LzT0/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/data/data_sources/a;->c(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public f(Lorg/xbet/games_section/api/models/GameBonus;DJLcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Lorg/xbet/games_section/api/models/GameBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "DJ",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "LzT0/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;

    .line 4
    .line 5
    const/4 v9, 0x0

    .line 6
    move-object v4, p0

    .line 7
    move-object v3, p1

    .line 8
    move-wide v7, p2

    .line 9
    move-wide v5, p4

    .line 10
    move-object/from16 v2, p6

    .line 11
    .line 12
    invoke-direct/range {v1 .. v9}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$makeBetGame$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/games_section/api/models/GameBonus;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;JDLkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    move-object/from16 p1, p7

    .line 16
    .line 17
    invoke-virtual {v0, v1, p1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->l(LOc/n;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    return-object p1
.end method

.method public g()LzT0/d;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/tile_matching/data/data_sources/a;->a()LzT0/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public h(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 3
    .param p1    # Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LzT0/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->d:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getCoef$2;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p1, p0, v2}, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl$getCoef$2;-><init>(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {v0, v1, p2}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method

.method public i(LzT0/e;)V
    .locals 1
    .param p1    # LzT0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->c:Lorg/xbet/tile_matching/data/data_sources/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/data/data_sources/a;->e(LzT0/e;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final q(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->e(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final r(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->g(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final s(Ljava/lang/String;JDJLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JDJ",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    new-instance v1, LuT0/b;

    .line 4
    .line 5
    move-wide v4, p2

    .line 6
    move-wide v2, p4

    .line 7
    move-wide v6, p6

    .line 8
    invoke-direct/range {v1 .. v7}, LuT0/b;-><init>(DJJ)V

    .line 9
    .line 10
    .line 11
    move-object/from16 p2, p8

    .line 12
    .line 13
    invoke-virtual {v0, p1, v1, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->i(Ljava/lang/String;LuT0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public final t(Ljava/lang/String;JJDJLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JJDJ",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    new-instance v1, LuT0/d;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->a:Lc8/h;

    .line 6
    .line 7
    invoke-interface {v2}, Lc8/h;->c()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v10

    .line 11
    iget-object v2, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->a:Lc8/h;

    .line 12
    .line 13
    invoke-interface {v2}, Lc8/h;->d()I

    .line 14
    .line 15
    .line 16
    move-result v11

    .line 17
    move-wide v2, p2

    .line 18
    move-wide/from16 v8, p4

    .line 19
    .line 20
    move-wide/from16 v6, p6

    .line 21
    .line 22
    move-wide/from16 v4, p8

    .line 23
    .line 24
    invoke-direct/range {v1 .. v11}, LuT0/d;-><init>(JJDJLjava/lang/String;I)V

    .line 25
    .line 26
    .line 27
    move-object/from16 p2, p10

    .line 28
    .line 29
    invoke-virtual {v0, p1, v1, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->j(Ljava/lang/String;LuT0/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    return-object p1
.end method

.method public final u(Ljava/lang/String;JIIILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JIII",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    new-instance v1, LuT0/a;

    .line 4
    .line 5
    move-wide v2, p2

    .line 6
    move v4, p4

    .line 7
    move v5, p5

    .line 8
    move v6, p6

    .line 9
    invoke-direct/range {v1 .. v6}, LuT0/a;-><init>(JIII)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, p1, v1, p7}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->k(Ljava/lang/String;LuT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    return-object p1
.end method

.method public final v(Ljava/lang/String;JILjava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->b:Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->a:Lc8/h;

    .line 4
    .line 5
    invoke-interface {v1}, Lc8/h;->d()I

    .line 6
    .line 7
    .line 8
    move-result v7

    .line 9
    iget-object v1, p0, Lorg/xbet/tile_matching/data/repositories/TileMatchingRepositoryImpl;->a:Lc8/h;

    .line 10
    .line 11
    invoke-interface {v1}, Lc8/h;->c()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v8

    .line 15
    new-instance v2, LuT0/c;

    .line 16
    .line 17
    move-wide v3, p2

    .line 18
    move v6, p4

    .line 19
    move-object v5, p5

    .line 20
    invoke-direct/range {v2 .. v8}, LuT0/c;-><init>(JLjava/util/List;IILjava/lang/String;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, p1, v2, p6}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->l(Ljava/lang/String;LuT0/c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    return-object p1
.end method
