.class public final Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\r\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u0017\u0010\u000e\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000e\u0010\u000cJ\u0015\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001b\u0010\u0015\u001a\u00020\n*\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001b\u0010\u0017\u001a\u00020\n*\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0016J!\u0010\u0019\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u00132\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u001e\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001d\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;",
        "indication",
        "",
        "setIndication",
        "(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V",
        "setTopIndication",
        "setBotIndication",
        "",
        "toLeftSide",
        "s",
        "(Z)V",
        "Landroid/view/View;",
        "top",
        "t",
        "(Landroid/view/View;Z)V",
        "u",
        "indicator",
        "v",
        "(Landroid/view/View;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V",
        "LC31/m;",
        "a",
        "LC31/m;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/m;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/m;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method


# virtual methods
.method public final s(Z)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 6
    .line 7
    iget-object p1, p1, LC31/m;->d:Landroid/view/View;

    .line 8
    .line 9
    invoke-virtual {p0, p1, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->t(Landroid/view/View;Z)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 13
    .line 14
    iget-object p1, p1, LC31/m;->b:Landroid/view/View;

    .line 15
    .line 16
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->t(Landroid/view/View;Z)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 21
    .line 22
    iget-object p1, p1, LC31/m;->d:Landroid/view/View;

    .line 23
    .line 24
    invoke-virtual {p0, p1, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->u(Landroid/view/View;Z)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 28
    .line 29
    iget-object p1, p1, LC31/m;->b:Landroid/view/View;

    .line 30
    .line 31
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->u(Landroid/view/View;Z)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final setBotIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 2
    .line 3
    iget-object v0, v0, LC31/m;->b:Landroid/view/View;

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->v(Landroid/view/View;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setTopIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setBotIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final setTopIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->a:LC31/m;

    .line 2
    .line 3
    iget-object v0, v0, LC31/m;->d:Landroid/view/View;

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->v(Landroid/view/View;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final t(Landroid/view/View;Z)V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-virtual {p1, v0}, Landroid/view/View;->setClipToOutline(Z)V

    .line 3
    .line 4
    .line 5
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator$a;

    .line 6
    .line 7
    invoke-direct {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator$a;-><init>(Landroid/view/View;Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p1, v0}, Landroid/view/View;->setOutlineProvider(Landroid/view/ViewOutlineProvider;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final u(Landroid/view/View;Z)V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-virtual {p1, v0}, Landroid/view/View;->setClipToOutline(Z)V

    .line 3
    .line 4
    .line 5
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator$b;

    .line 6
    .line 7
    invoke-direct {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator$b;-><init>(Landroid/view/View;Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p1, v0}, Landroid/view/View;->setOutlineProvider(Landroid/view/ViewOutlineProvider;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final v(Landroid/view/View;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V
    .locals 1

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->getColor()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const p2, 0x106000d

    .line 9
    .line 10
    .line 11
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-static {v0, p2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    invoke-virtual {p1, p2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method
