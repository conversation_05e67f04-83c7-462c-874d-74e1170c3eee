.class public final LGJ0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LGJ0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGJ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LHX0/e;

.field public final b:LGJ0/a$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCJ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LCJ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/data/PlayersStatisticCricketRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKJ0/o;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKJ0/i;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKJ0/e;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKJ0/g;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJJ0/g;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/g;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/e;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKJ0/m;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJJ0/e;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJJ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/e;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LGJ0/a$b;->b:LGJ0/a$b;

    .line 4
    iput-object p6, p0, LGJ0/a$b;->a:LHX0/e;

    .line 5
    invoke-virtual/range {p0 .. p6}, LGJ0/a$b;->d(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)V

    return-void
.end method

.method public synthetic constructor <init>(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;LGJ0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p6}, LGJ0/a$b;-><init>(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LGJ0/a$b;->e(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LGJ0/a$b;->g(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LGJ0/a$b;->f(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final d(Lf8/g;Lc8/h;LwX0/c;LCJ0/a;LSX0/a;LHX0/e;)V
    .locals 0

    .line 1
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LGJ0/a$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LGJ0/a$b;->d:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LGJ0/a$b;->e:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p1}, LCJ0/d;->a(LBc/a;)LCJ0/d;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iput-object p1, p0, LGJ0/a$b;->f:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iput-object p1, p0, LGJ0/a$b;->g:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, LGJ0/a$b;->h:Ldagger/internal/h;

    .line 36
    .line 37
    iget-object p2, p0, LGJ0/a$b;->f:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object p3, p0, LGJ0/a$b;->g:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static {p2, p3, p1}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/data/b;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/data/b;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, LGJ0/a$b;->i:Ldagger/internal/h;

    .line 46
    .line 47
    invoke-static {p1}, LKJ0/p;->a(LBc/a;)LKJ0/p;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iput-object p1, p0, LGJ0/a$b;->j:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object p1, p0, LGJ0/a$b;->i:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p1}, LKJ0/j;->a(LBc/a;)LKJ0/j;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput-object p1, p0, LGJ0/a$b;->k:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object p1, p0, LGJ0/a$b;->i:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static {p1}, LKJ0/f;->a(LBc/a;)LKJ0/f;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, LGJ0/a$b;->l:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object p1, p0, LGJ0/a$b;->i:Ldagger/internal/h;

    .line 70
    .line 71
    invoke-static {p1}, LKJ0/h;->a(LBc/a;)LKJ0/h;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    iput-object p1, p0, LGJ0/a$b;->m:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object p2, p0, LGJ0/a$b;->j:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p3, p0, LGJ0/a$b;->k:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object p4, p0, LGJ0/a$b;->l:Ldagger/internal/h;

    .line 82
    .line 83
    invoke-static {p2, p3, p4, p1}, LJJ0/h;->a(LBc/a;LBc/a;LBc/a;LBc/a;)LJJ0/h;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    iput-object p1, p0, LGJ0/a$b;->n:Ldagger/internal/h;

    .line 88
    .line 89
    iget-object p2, p0, LGJ0/a$b;->c:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object p3, p0, LGJ0/a$b;->d:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static {p2, p3, p1}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/h;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/h;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LGJ0/a$b;->o:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p1, p0, LGJ0/a$b;->n:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static {p1}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/f;->a(LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/f;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iput-object p1, p0, LGJ0/a$b;->p:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object p1, p0, LGJ0/a$b;->i:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p1}, LKJ0/n;->a(LBc/a;)LKJ0/n;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LGJ0/a$b;->q:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p2, p0, LGJ0/a$b;->k:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p3, p0, LGJ0/a$b;->l:Ldagger/internal/h;

    .line 118
    .line 119
    invoke-static {p2, p3, p1}, LJJ0/f;->a(LBc/a;LBc/a;LBc/a;)LJJ0/f;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    iput-object p1, p0, LGJ0/a$b;->r:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p1, p0, LGJ0/a$b;->k:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object p2, p0, LGJ0/a$b;->l:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object p3, p0, LGJ0/a$b;->m:Ldagger/internal/h;

    .line 130
    .line 131
    invoke-static {p1, p2, p3}, LJJ0/d;->a(LBc/a;LBc/a;LBc/a;)LJJ0/d;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    iput-object p1, p0, LGJ0/a$b;->s:Ldagger/internal/h;

    .line 136
    .line 137
    iget-object p2, p0, LGJ0/a$b;->r:Ldagger/internal/h;

    .line 138
    .line 139
    invoke-static {p2, p1}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/f;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/f;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    iput-object p1, p0, LGJ0/a$b;->t:Ldagger/internal/h;

    .line 144
    .line 145
    return-void
.end method

.method public final e(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGJ0/a$b;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/c;->b(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LGJ0/a$b;->a:LHX0/e;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/c;->a(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/AdditionalInfoBottomSheetDialogFragment;LHX0/e;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final f(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGJ0/a$b;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/c;->b(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LGJ0/a$b;->a:LHX0/e;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/c;->a(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/ChoseTableDataTypeBottomSheetDialogFragment;LHX0/e;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LGJ0/a$b;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/d;->a(Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/PlayersStatisticCricketResultsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x3

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/g;

    .line 7
    .line 8
    iget-object v2, p0, LGJ0/a$b;->o:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/additional_info/e;

    .line 15
    .line 16
    iget-object v2, p0, LGJ0/a$b;->p:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/chose_table_data_type/e;

    .line 23
    .line 24
    iget-object v2, p0, LGJ0/a$b;->t:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    return-object v0
.end method

.method public final i()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LGJ0/a$b;->h()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
