.class public final Lorg/xplatform/aggregator/impl/core/presentation/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/core/presentation/c;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->c:LBc/a;

    .line 9
    .line 10
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/core/presentation/d;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/core/presentation/d;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/d;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/d;-><init>(LBc/a;LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)Lorg/xplatform/aggregator/impl/core/presentation/c;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/c;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/core/presentation/c;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, LP91/b;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/d;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, Lorg/xbet/analytics/domain/scope/g0;

    .line 24
    .line 25
    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/d;->c(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/d;->b()Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
