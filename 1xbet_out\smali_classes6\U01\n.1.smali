.class public final synthetic LU01/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Lcom/bumptech/glide/load/engine/GlideException;

.field public final synthetic c:Lorg/xbet/uikit/components/views/LoadableImageView;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Lcom/bumptech/glide/load/engine/GlideException;Lorg/xbet/uikit/components/views/LoadableImageView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/n;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LU01/n;->b:Lcom/bumptech/glide/load/engine/GlideException;

    iput-object p3, p0, LU01/n;->c:Lorg/xbet/uikit/components/views/LoadableImageView;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, LU01/n;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LU01/n;->b:Lcom/bumptech/glide/load/engine/GlideException;

    iget-object v2, p0, LU01/n;->c:Lorg/xbet/uikit/components/views/LoadableImageView;

    invoke-static {v0, v1, v2}, Lorg/xbet/uikit/components/views/LoadableImageView;->D(Lkotlin/jvm/functions/Function1;Lcom/bumptech/glide/load/engine/GlideException;Lorg/xbet/uikit/components/views/LoadableImageView;)V

    return-void
.end method
