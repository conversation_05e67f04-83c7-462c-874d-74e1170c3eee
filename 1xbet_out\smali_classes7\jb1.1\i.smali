.class public final Ljb1/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/i$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u0004\u0018\u00010\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a#\u0010\t\u001a\u0004\u0018\u00010\u0008*\u00020\u00002\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u0001\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\'\u0010\r\u001a\u00020\u000b2\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000e\u001a\u0017\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u001aA\u0010\u0019\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "Li81/a;",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;",
        "styleType",
        "Lq21/a;",
        "d",
        "(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lq21/a;",
        "Lq21/f;",
        "e",
        "(Li81/a;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;)Lq21/f;",
        "",
        "place",
        "b",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;Ljava/lang/String;)Ljava/lang/String;",
        "",
        "c",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)I",
        "",
        "decoration",
        "firstImage",
        "palace",
        "active",
        "wait",
        "LL11/c;",
        "a",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const-string v1, ""

    .line 3
    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;->COLOR:Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 7
    .line 8
    if-ne p0, p1, :cond_12

    .line 9
    .line 10
    const-string v1, "Color_Bottom.webp"

    .line 11
    .line 12
    goto/16 :goto_2

    .line 13
    .line 14
    :cond_0
    if-eqz p2, :cond_11

    .line 15
    .line 16
    sget-object p1, Ljb1/i$a;->b:[I

    .line 17
    .line 18
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 19
    .line 20
    .line 21
    move-result p0

    .line 22
    aget p0, p1, p0

    .line 23
    .line 24
    const/4 p1, 0x3

    .line 25
    const/4 p2, 0x2

    .line 26
    const/4 v1, 0x1

    .line 27
    if-eq p0, v1, :cond_b

    .line 28
    .line 29
    if-eq p0, p2, :cond_9

    .line 30
    .line 31
    if-eq p0, p1, :cond_3

    .line 32
    .line 33
    const/4 p1, 0x4

    .line 34
    if-ne p0, p1, :cond_2

    .line 35
    .line 36
    if-eqz p5, :cond_1

    .line 37
    .line 38
    const-string p0, "Cup_Inactive_Wait.webp"

    .line 39
    .line 40
    move-object v1, p0

    .line 41
    goto :goto_0

    .line 42
    :cond_1
    move-object v1, v0

    .line 43
    :goto_0
    if-nez v1, :cond_12

    .line 44
    .line 45
    const-string v1, "Cup.webp"

    .line 46
    .line 47
    goto/16 :goto_2

    .line 48
    .line 49
    :cond_2
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 50
    .line 51
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 52
    .line 53
    .line 54
    throw p0

    .line 55
    :cond_3
    if-eqz p4, :cond_7

    .line 56
    .line 57
    if-eq p3, v1, :cond_6

    .line 58
    .line 59
    if-eq p3, p2, :cond_5

    .line 60
    .line 61
    if-eq p3, p1, :cond_4

    .line 62
    .line 63
    const-string v1, "Chevron_Other.webp"

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_4
    const-string v1, "Chevron_Bronze.webp"

    .line 67
    .line 68
    goto :goto_2

    .line 69
    :cond_5
    const-string v1, "Chevron_Silver.webp"

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_6
    const-string v1, "Chevron_Gold.webp"

    .line 73
    .line 74
    goto :goto_2

    .line 75
    :cond_7
    if-eqz p5, :cond_8

    .line 76
    .line 77
    const-string v1, "Chevron_Inactive_Wait.webp"

    .line 78
    .line 79
    goto :goto_2

    .line 80
    :cond_8
    const-string v1, "Chevron_Inactive.webp"

    .line 81
    .line 82
    goto :goto_2

    .line 83
    :cond_9
    if-eqz p5, :cond_a

    .line 84
    .line 85
    const-string p0, "Color_Inactive_Wait.webp"

    .line 86
    .line 87
    move-object v1, p0

    .line 88
    goto :goto_1

    .line 89
    :cond_a
    move-object v1, v0

    .line 90
    :goto_1
    if-nez v1, :cond_12

    .line 91
    .line 92
    const-string v1, "Color_Top.webp"

    .line 93
    .line 94
    goto :goto_2

    .line 95
    :cond_b
    if-eqz p4, :cond_f

    .line 96
    .line 97
    if-eq p3, v1, :cond_e

    .line 98
    .line 99
    if-eq p3, p2, :cond_d

    .line 100
    .line 101
    if-eq p3, p1, :cond_c

    .line 102
    .line 103
    const-string v1, "Icon_Other.webp"

    .line 104
    .line 105
    goto :goto_2

    .line 106
    :cond_c
    const-string v1, "Icon_Bronze.webp"

    .line 107
    .line 108
    goto :goto_2

    .line 109
    :cond_d
    const-string v1, "Icon_Silver.webp"

    .line 110
    .line 111
    goto :goto_2

    .line 112
    :cond_e
    const-string v1, "Icon_Gold.webp"

    .line 113
    .line 114
    goto :goto_2

    .line 115
    :cond_f
    if-eqz p5, :cond_10

    .line 116
    .line 117
    const-string v1, "Icon_Inactive_Wait.webp"

    .line 118
    .line 119
    goto :goto_2

    .line 120
    :cond_10
    const-string v1, "Icon_Inactive.webp"

    .line 121
    .line 122
    goto :goto_2

    .line 123
    :cond_11
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;->CHEVRON:Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 124
    .line 125
    if-ne p0, p1, :cond_12

    .line 126
    .line 127
    if-eqz p4, :cond_12

    .line 128
    .line 129
    const-string v1, "Chevron_Points.webp"

    .line 130
    .line 131
    :cond_12
    :goto_2
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 132
    .line 133
    .line 134
    move-result p0

    .line 135
    if-lez p0, :cond_13

    .line 136
    .line 137
    sget-object p0, LCX0/l;->a:LCX0/l;

    .line 138
    .line 139
    new-instance p1, Ljava/lang/StringBuilder;

    .line 140
    .line 141
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 142
    .line 143
    .line 144
    const-string p2, "/static/img/android/casino/alt_design/aggregator_tournament_progress/"

    .line 145
    .line 146
    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 147
    .line 148
    .line 149
    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 150
    .line 151
    .line 152
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    invoke-virtual {p0, p1}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 157
    .line 158
    .line 159
    move-result-object p0

    .line 160
    invoke-static {p0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 161
    .line 162
    .line 163
    move-result-object p0

    .line 164
    invoke-static {p0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 165
    .line 166
    .line 167
    move-result-object p0

    .line 168
    return-object p0

    .line 169
    :cond_13
    return-object v0
.end method

.method public static final b(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    sget-object v2, Ljb1/i$a;->b:[I

    .line 4
    .line 5
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    aget p0, v2, p0

    .line 10
    .line 11
    if-eq p0, v1, :cond_1

    .line 12
    .line 13
    const/4 p2, 0x2

    .line 14
    if-eq p0, p2, :cond_0

    .line 15
    .line 16
    sget p0, Lpb/k;->tournament_place:I

    .line 17
    .line 18
    new-array p2, v0, [Ljava/lang/Object;

    .line 19
    .line 20
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object p0

    .line 24
    return-object p0

    .line 25
    :cond_0
    sget p0, Lpb/k;->place:I

    .line 26
    .line 27
    new-array p2, v0, [Ljava/lang/Object;

    .line 28
    .line 29
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    return-object p0

    .line 34
    :cond_1
    sget p0, Lpb/k;->tournament_place_number:I

    .line 35
    .line 36
    new-array v1, v1, [Ljava/lang/Object;

    .line 37
    .line 38
    aput-object p2, v1, v0

    .line 39
    .line 40
    invoke-interface {p1, p0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object p0

    .line 44
    return-object p0
.end method

.method public static final c(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;->ICON:Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    sget p0, Lpb/k;->tournament_score:I

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    sget p0, Lpb/k;->tournament_your_score:I

    .line 9
    .line 10
    return p0
.end method

.method public static final d(Li81/a;LHX0/e;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)Lq21/a;
    .locals 3
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-ne v0, v1, :cond_0

    .line 9
    .line 10
    invoke-static {p0, p2, p1}, Ljb1/i;->e(Li81/a;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;)Lq21/f;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    if-eqz p0, :cond_0

    .line 15
    .line 16
    new-instance v0, Lq21/a;

    .line 17
    .line 18
    sget v1, Lpb/k;->tournament_your_progress:I

    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    new-array v2, v2, [Ljava/lang/Object;

    .line 22
    .line 23
    invoke-interface {p1, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-direct {v0, p2, p1, p0}, Lq21/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Ljava/lang/String;Lq21/f;)V

    .line 28
    .line 29
    .line 30
    return-object v0

    .line 31
    :cond_0
    return-object v2
.end method

.method public static final e(Li81/a;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;)Lq21/f;
    .locals 18
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v6, p2

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, Li81/a;->d()Lj81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lj81/a;->i()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    sget-object v1, Ljb1/i$a;->a:[I

    .line 12
    .line 13
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    aget v0, v1, v0

    .line 18
    .line 19
    const/4 v1, 0x1

    .line 20
    const/4 v7, 0x0

    .line 21
    if-eq v0, v1, :cond_15

    .line 22
    .line 23
    const/4 v1, 0x2

    .line 24
    const/4 v8, 0x0

    .line 25
    if-eq v0, v1, :cond_10

    .line 26
    .line 27
    const/4 v1, 0x3

    .line 28
    if-ne v0, v1, :cond_f

    .line 29
    .line 30
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    if-eqz v0, :cond_e

    .line 35
    .line 36
    invoke-virtual/range {p0 .. p0}, Li81/a;->f()Lm81/a;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {v0}, Lm81/a;->a()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 49
    .line 50
    .line 51
    move-result v1

    .line 52
    if-eqz v1, :cond_1

    .line 53
    .line 54
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    move-object v2, v1

    .line 59
    check-cast v2, Lm81/b;

    .line 60
    .line 61
    invoke-virtual {v2}, Lm81/b;->b()Z

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    if-eqz v2, :cond_0

    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    move-object v1, v8

    .line 69
    :goto_0
    move-object v9, v1

    .line 70
    check-cast v9, Lm81/b;

    .line 71
    .line 72
    if-nez v9, :cond_2

    .line 73
    .line 74
    new-instance v8, Lq21/f$c;

    .line 75
    .line 76
    new-instance v9, Lq21/c;

    .line 77
    .line 78
    const/4 v4, 0x0

    .line 79
    const/4 v5, 0x0

    .line 80
    const/4 v1, 0x0

    .line 81
    const/4 v2, 0x1

    .line 82
    const/4 v3, 0x0

    .line 83
    move-object/from16 v0, p1

    .line 84
    .line 85
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    sget v0, Lpb/k;->you_participating_tournament:I

    .line 90
    .line 91
    new-array v2, v7, [Ljava/lang/Object;

    .line 92
    .line 93
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 98
    .line 99
    new-array v3, v7, [Ljava/lang/Object;

    .line 100
    .line 101
    invoke-interface {v6, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v2

    .line 105
    invoke-direct {v9, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    const/4 v1, 0x1

    .line 109
    const/4 v2, 0x0

    .line 110
    const/4 v3, 0x0

    .line 111
    move-object/from16 v0, p1

    .line 112
    .line 113
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    invoke-direct {v8, v9, v0}, Lq21/f$c;-><init>(Lq21/c;LL11/c;)V

    .line 118
    .line 119
    .line 120
    return-object v8

    .line 121
    :cond_2
    invoke-virtual/range {p0 .. p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    sget-object v1, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->CRM:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 126
    .line 127
    if-ne v0, v1, :cond_d

    .line 128
    .line 129
    invoke-virtual/range {p0 .. p0}, Li81/a;->s()I

    .line 130
    .line 131
    .line 132
    move-result v0

    .line 133
    const/4 v1, 0x4

    .line 134
    if-ne v0, v1, :cond_d

    .line 135
    .line 136
    invoke-virtual/range {p0 .. p0}, Li81/a;->i()Lo81/a;

    .line 137
    .line 138
    .line 139
    move-result-object v0

    .line 140
    invoke-virtual {v0}, Lo81/a;->a()Ljava/util/List;

    .line 141
    .line 142
    .line 143
    move-result-object v0

    .line 144
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 145
    .line 146
    .line 147
    move-result-object v0

    .line 148
    :cond_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 149
    .line 150
    .line 151
    move-result v1

    .line 152
    if-eqz v1, :cond_4

    .line 153
    .line 154
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    move-object v2, v1

    .line 159
    check-cast v2, Lo81/b;

    .line 160
    .line 161
    invoke-virtual {v2}, Lo81/b;->c()J

    .line 162
    .line 163
    .line 164
    move-result-wide v2

    .line 165
    invoke-virtual/range {p0 .. p0}, Li81/a;->l()J

    .line 166
    .line 167
    .line 168
    move-result-wide v4

    .line 169
    cmp-long v10, v2, v4

    .line 170
    .line 171
    if-nez v10, :cond_3

    .line 172
    .line 173
    goto :goto_1

    .line 174
    :cond_4
    move-object v1, v8

    .line 175
    :goto_1
    move-object v10, v1

    .line 176
    check-cast v10, Lo81/b;

    .line 177
    .line 178
    if-eqz v10, :cond_c

    .line 179
    .line 180
    invoke-virtual {v9}, Lm81/b;->d()I

    .line 181
    .line 182
    .line 183
    move-result v0

    .line 184
    int-to-float v11, v0

    .line 185
    invoke-virtual {v10}, Lo81/b;->b()I

    .line 186
    .line 187
    .line 188
    move-result v0

    .line 189
    int-to-float v15, v0

    .line 190
    new-instance v12, Lq21/c;

    .line 191
    .line 192
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 193
    .line 194
    .line 195
    move-result v3

    .line 196
    const/4 v4, 0x1

    .line 197
    const/4 v5, 0x0

    .line 198
    const/4 v1, 0x0

    .line 199
    const/4 v2, 0x1

    .line 200
    move-object/from16 v0, p1

    .line 201
    .line 202
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 207
    .line 208
    .line 209
    move-result v2

    .line 210
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 211
    .line 212
    .line 213
    move-result-object v2

    .line 214
    invoke-static {v0, v6, v2}, Ljb1/i;->b(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;Ljava/lang/String;)Ljava/lang/String;

    .line 215
    .line 216
    .line 217
    move-result-object v2

    .line 218
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 219
    .line 220
    .line 221
    move-result v3

    .line 222
    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 223
    .line 224
    .line 225
    move-result-object v3

    .line 226
    invoke-direct {v12, v1, v2, v3}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 227
    .line 228
    .line 229
    const/4 v4, 0x0

    .line 230
    const/4 v1, 0x1

    .line 231
    const/4 v2, 0x0

    .line 232
    const/4 v3, 0x0

    .line 233
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 234
    .line 235
    .line 236
    move-result-object v13

    .line 237
    new-instance v14, Lq21/c;

    .line 238
    .line 239
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 240
    .line 241
    .line 242
    move-result v3

    .line 243
    const/4 v4, 0x1

    .line 244
    const/4 v1, 0x0

    .line 245
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 246
    .line 247
    .line 248
    move-result-object v1

    .line 249
    invoke-static/range {p1 .. p1}, Ljb1/i;->c(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)I

    .line 250
    .line 251
    .line 252
    move-result v0

    .line 253
    new-array v2, v7, [Ljava/lang/Object;

    .line 254
    .line 255
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 256
    .line 257
    .line 258
    move-result-object v0

    .line 259
    invoke-virtual {v9}, Lm81/b;->d()I

    .line 260
    .line 261
    .line 262
    move-result v2

    .line 263
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 264
    .line 265
    .line 266
    move-result-object v2

    .line 267
    invoke-direct {v14, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 268
    .line 269
    .line 270
    sget v0, Lpb/k;->tournament_points_to_next_stage:I

    .line 271
    .line 272
    new-array v1, v7, [Ljava/lang/Object;

    .line 273
    .line 274
    invoke-interface {v6, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 275
    .line 276
    .line 277
    move-result-object v0

    .line 278
    invoke-virtual/range {p0 .. p0}, Li81/a;->i()Lo81/a;

    .line 279
    .line 280
    .line 281
    move-result-object v1

    .line 282
    invoke-virtual {v1}, Lo81/a;->a()Ljava/util/List;

    .line 283
    .line 284
    .line 285
    move-result-object v1

    .line 286
    new-instance v2, Ljava/util/ArrayList;

    .line 287
    .line 288
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 289
    .line 290
    .line 291
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 292
    .line 293
    .line 294
    move-result-object v1

    .line 295
    :cond_5
    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 296
    .line 297
    .line 298
    move-result v3

    .line 299
    if-eqz v3, :cond_6

    .line 300
    .line 301
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 302
    .line 303
    .line 304
    move-result-object v3

    .line 305
    move-object v4, v3

    .line 306
    check-cast v4, Lo81/b;

    .line 307
    .line 308
    invoke-virtual {v4}, Lo81/b;->b()I

    .line 309
    .line 310
    .line 311
    move-result v4

    .line 312
    invoke-virtual {v10}, Lo81/b;->b()I

    .line 313
    .line 314
    .line 315
    move-result v5

    .line 316
    if-ge v4, v5, :cond_5

    .line 317
    .line 318
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 319
    .line 320
    .line 321
    goto :goto_2

    .line 322
    :cond_6
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 323
    .line 324
    .line 325
    move-result-object v1

    .line 326
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 327
    .line 328
    .line 329
    move-result v2

    .line 330
    if-nez v2, :cond_7

    .line 331
    .line 332
    goto :goto_4

    .line 333
    :cond_7
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 334
    .line 335
    .line 336
    move-result-object v2

    .line 337
    check-cast v2, Lo81/b;

    .line 338
    .line 339
    invoke-virtual {v2}, Lo81/b;->b()I

    .line 340
    .line 341
    .line 342
    move-result v2

    .line 343
    int-to-float v2, v2

    .line 344
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 345
    .line 346
    .line 347
    move-result v3

    .line 348
    if-eqz v3, :cond_8

    .line 349
    .line 350
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 351
    .line 352
    .line 353
    move-result-object v3

    .line 354
    check-cast v3, Lo81/b;

    .line 355
    .line 356
    invoke-virtual {v3}, Lo81/b;->b()I

    .line 357
    .line 358
    .line 359
    move-result v3

    .line 360
    int-to-float v3, v3

    .line 361
    invoke-static {v2, v3}, Ljava/lang/Math;->max(FF)F

    .line 362
    .line 363
    .line 364
    move-result v2

    .line 365
    goto :goto_3

    .line 366
    :cond_8
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 367
    .line 368
    .line 369
    move-result-object v8

    .line 370
    :goto_4
    const/4 v1, 0x0

    .line 371
    if-eqz v8, :cond_9

    .line 372
    .line 373
    invoke-virtual {v8}, Ljava/lang/Float;->floatValue()F

    .line 374
    .line 375
    .line 376
    move-result v2

    .line 377
    goto :goto_5

    .line 378
    :cond_9
    const/4 v2, 0x0

    .line 379
    :goto_5
    invoke-virtual {v9}, Lm81/b;->d()I

    .line 380
    .line 381
    .line 382
    move-result v3

    .line 383
    invoke-virtual {v10}, Lo81/b;->b()I

    .line 384
    .line 385
    .line 386
    move-result v4

    .line 387
    if-ge v3, v4, :cond_a

    .line 388
    .line 389
    invoke-virtual {v9}, Lm81/b;->d()I

    .line 390
    .line 391
    .line 392
    move-result v3

    .line 393
    :goto_6
    int-to-float v3, v3

    .line 394
    goto :goto_7

    .line 395
    :cond_a
    invoke-virtual {v10}, Lo81/b;->b()I

    .line 396
    .line 397
    .line 398
    move-result v3

    .line 399
    goto :goto_6

    .line 400
    :goto_7
    sub-float v4, v15, v11

    .line 401
    .line 402
    cmpg-float v1, v4, v1

    .line 403
    .line 404
    if-gez v1, :cond_b

    .line 405
    .line 406
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressRatingDSType;->COMPLETE:Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressRatingDSType;

    .line 407
    .line 408
    :goto_8
    move-object/from16 v17, v1

    .line 409
    .line 410
    move/from16 v16, v11

    .line 411
    .line 412
    goto :goto_9

    .line 413
    :cond_b
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressRatingDSType;->STEP:Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressRatingDSType;

    .line 414
    .line 415
    goto :goto_8

    .line 416
    :goto_9
    new-instance v11, Lq21/d;

    .line 417
    .line 418
    move-object v1, v12

    .line 419
    move-object v12, v0

    .line 420
    move-object v0, v1

    .line 421
    move-object v1, v14

    .line 422
    move v14, v2

    .line 423
    move-object v2, v1

    .line 424
    move-object v1, v13

    .line 425
    move v13, v3

    .line 426
    invoke-direct/range {v11 .. v17}, Lq21/d;-><init>(Ljava/lang/String;FFFFLorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressRatingDSType;)V

    .line 427
    .line 428
    .line 429
    new-instance v3, Lq21/f$b;

    .line 430
    .line 431
    invoke-direct {v3, v0, v1, v2, v11}, Lq21/f$b;-><init>(Lq21/c;LL11/c;Lq21/c;Lq21/d;)V

    .line 432
    .line 433
    .line 434
    return-object v3

    .line 435
    :cond_c
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 436
    .line 437
    const-string v1, "Current stage not found"

    .line 438
    .line 439
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 440
    .line 441
    .line 442
    throw v0

    .line 443
    :cond_d
    new-instance v8, Lq21/f$a;

    .line 444
    .line 445
    new-instance v10, Lq21/c;

    .line 446
    .line 447
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 448
    .line 449
    .line 450
    move-result v3

    .line 451
    const/4 v4, 0x1

    .line 452
    const/4 v5, 0x0

    .line 453
    const/4 v1, 0x0

    .line 454
    const/4 v2, 0x1

    .line 455
    move-object/from16 v0, p1

    .line 456
    .line 457
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 458
    .line 459
    .line 460
    move-result-object v1

    .line 461
    sget v0, Lpb/k;->place:I

    .line 462
    .line 463
    new-array v2, v7, [Ljava/lang/Object;

    .line 464
    .line 465
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 466
    .line 467
    .line 468
    move-result-object v0

    .line 469
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 470
    .line 471
    .line 472
    move-result v2

    .line 473
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 474
    .line 475
    .line 476
    move-result-object v2

    .line 477
    invoke-direct {v10, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 478
    .line 479
    .line 480
    const/4 v4, 0x0

    .line 481
    const/4 v1, 0x1

    .line 482
    const/4 v2, 0x0

    .line 483
    const/4 v3, 0x0

    .line 484
    move-object/from16 v0, p1

    .line 485
    .line 486
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 487
    .line 488
    .line 489
    move-result-object v11

    .line 490
    new-instance v12, Lq21/c;

    .line 491
    .line 492
    invoke-virtual {v9}, Lm81/b;->c()I

    .line 493
    .line 494
    .line 495
    move-result v3

    .line 496
    const/4 v4, 0x1

    .line 497
    const/4 v1, 0x0

    .line 498
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 499
    .line 500
    .line 501
    move-result-object v0

    .line 502
    sget v1, Lpb/k;->tournament_your_score:I

    .line 503
    .line 504
    new-array v2, v7, [Ljava/lang/Object;

    .line 505
    .line 506
    invoke-interface {v6, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 507
    .line 508
    .line 509
    move-result-object v1

    .line 510
    invoke-virtual {v9}, Lm81/b;->d()I

    .line 511
    .line 512
    .line 513
    move-result v2

    .line 514
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 515
    .line 516
    .line 517
    move-result-object v2

    .line 518
    invoke-direct {v12, v0, v1, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 519
    .line 520
    .line 521
    invoke-direct {v8, v10, v11, v12}, Lq21/f$a;-><init>(Lq21/c;LL11/c;Lq21/c;)V

    .line 522
    .line 523
    .line 524
    return-object v8

    .line 525
    :cond_e
    new-instance v8, Lq21/f$c;

    .line 526
    .line 527
    new-instance v9, Lq21/c;

    .line 528
    .line 529
    const/4 v4, 0x0

    .line 530
    const/4 v5, 0x0

    .line 531
    const/4 v1, 0x0

    .line 532
    const/4 v2, 0x1

    .line 533
    const/4 v3, 0x0

    .line 534
    move-object/from16 v0, p1

    .line 535
    .line 536
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 537
    .line 538
    .line 539
    move-result-object v1

    .line 540
    sget v0, Lpb/k;->tournament_participate:I

    .line 541
    .line 542
    new-array v2, v7, [Ljava/lang/Object;

    .line 543
    .line 544
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 545
    .line 546
    .line 547
    move-result-object v0

    .line 548
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 549
    .line 550
    new-array v3, v7, [Ljava/lang/Object;

    .line 551
    .line 552
    invoke-interface {v6, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 553
    .line 554
    .line 555
    move-result-object v2

    .line 556
    invoke-direct {v9, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 557
    .line 558
    .line 559
    const/4 v1, 0x1

    .line 560
    const/4 v2, 0x0

    .line 561
    const/4 v3, 0x0

    .line 562
    move-object/from16 v0, p1

    .line 563
    .line 564
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 565
    .line 566
    .line 567
    move-result-object v0

    .line 568
    invoke-direct {v8, v9, v0}, Lq21/f$c;-><init>(Lq21/c;LL11/c;)V

    .line 569
    .line 570
    .line 571
    return-object v8

    .line 572
    :cond_f
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 573
    .line 574
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 575
    .line 576
    .line 577
    throw v0

    .line 578
    :cond_10
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 579
    .line 580
    .line 581
    move-result v0

    .line 582
    if-eqz v0, :cond_14

    .line 583
    .line 584
    invoke-virtual/range {p0 .. p0}, Li81/a;->f()Lm81/a;

    .line 585
    .line 586
    .line 587
    move-result-object v0

    .line 588
    invoke-virtual {v0}, Lm81/a;->a()Ljava/util/List;

    .line 589
    .line 590
    .line 591
    move-result-object v0

    .line 592
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 593
    .line 594
    .line 595
    move-result-object v0

    .line 596
    :cond_11
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 597
    .line 598
    .line 599
    move-result v1

    .line 600
    if-eqz v1, :cond_12

    .line 601
    .line 602
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 603
    .line 604
    .line 605
    move-result-object v1

    .line 606
    move-object v2, v1

    .line 607
    check-cast v2, Lm81/b;

    .line 608
    .line 609
    invoke-virtual {v2}, Lm81/b;->b()Z

    .line 610
    .line 611
    .line 612
    move-result v2

    .line 613
    if-eqz v2, :cond_11

    .line 614
    .line 615
    move-object v8, v1

    .line 616
    :cond_12
    check-cast v8, Lm81/b;

    .line 617
    .line 618
    if-nez v8, :cond_13

    .line 619
    .line 620
    new-instance v8, Lq21/f$c;

    .line 621
    .line 622
    new-instance v9, Lq21/c;

    .line 623
    .line 624
    const/4 v4, 0x0

    .line 625
    const/4 v5, 0x0

    .line 626
    const/4 v1, 0x0

    .line 627
    const/4 v2, 0x1

    .line 628
    const/4 v3, 0x0

    .line 629
    move-object/from16 v0, p1

    .line 630
    .line 631
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 632
    .line 633
    .line 634
    move-result-object v1

    .line 635
    sget v0, Lpb/k;->you_participating_tournament:I

    .line 636
    .line 637
    new-array v2, v7, [Ljava/lang/Object;

    .line 638
    .line 639
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 640
    .line 641
    .line 642
    move-result-object v0

    .line 643
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 644
    .line 645
    new-array v3, v7, [Ljava/lang/Object;

    .line 646
    .line 647
    invoke-interface {v6, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 648
    .line 649
    .line 650
    move-result-object v2

    .line 651
    invoke-direct {v9, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 652
    .line 653
    .line 654
    const/4 v1, 0x1

    .line 655
    const/4 v2, 0x0

    .line 656
    const/4 v3, 0x0

    .line 657
    move-object/from16 v0, p1

    .line 658
    .line 659
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 660
    .line 661
    .line 662
    move-result-object v0

    .line 663
    invoke-direct {v8, v9, v0}, Lq21/f$c;-><init>(Lq21/c;LL11/c;)V

    .line 664
    .line 665
    .line 666
    return-object v8

    .line 667
    :cond_13
    new-instance v9, Lq21/f$a;

    .line 668
    .line 669
    new-instance v10, Lq21/c;

    .line 670
    .line 671
    invoke-virtual {v8}, Lm81/b;->c()I

    .line 672
    .line 673
    .line 674
    move-result v3

    .line 675
    const/4 v4, 0x1

    .line 676
    const/4 v5, 0x0

    .line 677
    const/4 v1, 0x0

    .line 678
    const/4 v2, 0x1

    .line 679
    move-object/from16 v0, p1

    .line 680
    .line 681
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 682
    .line 683
    .line 684
    move-result-object v1

    .line 685
    invoke-virtual {v8}, Lm81/b;->c()I

    .line 686
    .line 687
    .line 688
    move-result v2

    .line 689
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 690
    .line 691
    .line 692
    move-result-object v2

    .line 693
    invoke-static {v0, v6, v2}, Ljb1/i;->b(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;LHX0/e;Ljava/lang/String;)Ljava/lang/String;

    .line 694
    .line 695
    .line 696
    move-result-object v2

    .line 697
    invoke-virtual {v8}, Lm81/b;->c()I

    .line 698
    .line 699
    .line 700
    move-result v3

    .line 701
    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 702
    .line 703
    .line 704
    move-result-object v3

    .line 705
    invoke-direct {v10, v1, v2, v3}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 706
    .line 707
    .line 708
    const/4 v4, 0x0

    .line 709
    const/4 v1, 0x1

    .line 710
    const/4 v2, 0x0

    .line 711
    const/4 v3, 0x0

    .line 712
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 713
    .line 714
    .line 715
    move-result-object v11

    .line 716
    new-instance v12, Lq21/c;

    .line 717
    .line 718
    invoke-virtual {v8}, Lm81/b;->c()I

    .line 719
    .line 720
    .line 721
    move-result v3

    .line 722
    const/4 v4, 0x1

    .line 723
    const/4 v1, 0x0

    .line 724
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 725
    .line 726
    .line 727
    move-result-object v1

    .line 728
    invoke-static/range {p1 .. p1}, Ljb1/i;->c(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;)I

    .line 729
    .line 730
    .line 731
    move-result v0

    .line 732
    new-array v2, v7, [Ljava/lang/Object;

    .line 733
    .line 734
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 735
    .line 736
    .line 737
    move-result-object v0

    .line 738
    invoke-virtual {v8}, Lm81/b;->d()I

    .line 739
    .line 740
    .line 741
    move-result v2

    .line 742
    invoke-static {v2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 743
    .line 744
    .line 745
    move-result-object v2

    .line 746
    invoke-direct {v12, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 747
    .line 748
    .line 749
    invoke-direct {v9, v10, v11, v12}, Lq21/f$a;-><init>(Lq21/c;LL11/c;Lq21/c;)V

    .line 750
    .line 751
    .line 752
    return-object v9

    .line 753
    :cond_14
    return-object v8

    .line 754
    :cond_15
    new-instance v8, Lq21/f$c;

    .line 755
    .line 756
    new-instance v9, Lq21/c;

    .line 757
    .line 758
    const/4 v4, 0x0

    .line 759
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 760
    .line 761
    .line 762
    move-result v5

    .line 763
    const/4 v1, 0x0

    .line 764
    const/4 v2, 0x1

    .line 765
    const/4 v3, 0x0

    .line 766
    move-object/from16 v0, p1

    .line 767
    .line 768
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 769
    .line 770
    .line 771
    move-result-object v1

    .line 772
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 773
    .line 774
    .line 775
    move-result v0

    .line 776
    if-eqz v0, :cond_16

    .line 777
    .line 778
    sget v0, Lpb/k;->you_participating_tournament:I

    .line 779
    .line 780
    goto :goto_a

    .line 781
    :cond_16
    sget v0, Lpb/k;->tournament_participate:I

    .line 782
    .line 783
    :goto_a
    new-array v2, v7, [Ljava/lang/Object;

    .line 784
    .line 785
    invoke-interface {v6, v0, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 786
    .line 787
    .line 788
    move-result-object v0

    .line 789
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 790
    .line 791
    .line 792
    move-result v2

    .line 793
    if-eqz v2, :cond_17

    .line 794
    .line 795
    sget v2, Lpb/k;->tournament_participating_waiting_start:I

    .line 796
    .line 797
    goto :goto_b

    .line 798
    :cond_17
    sget v2, Lpb/k;->tournament_compete_win:I

    .line 799
    .line 800
    :goto_b
    new-array v3, v7, [Ljava/lang/Object;

    .line 801
    .line 802
    invoke-interface {v6, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 803
    .line 804
    .line 805
    move-result-object v2

    .line 806
    invoke-direct {v9, v1, v0, v2}, Lq21/c;-><init>(LL11/c;Ljava/lang/String;Ljava/lang/String;)V

    .line 807
    .line 808
    .line 809
    const/4 v4, 0x0

    .line 810
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 811
    .line 812
    .line 813
    move-result v5

    .line 814
    const/4 v1, 0x1

    .line 815
    const/4 v2, 0x0

    .line 816
    const/4 v3, 0x0

    .line 817
    move-object/from16 v0, p1

    .line 818
    .line 819
    invoke-static/range {v0 .. v5}, Ljb1/i;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;ZZIZZ)LL11/c;

    .line 820
    .line 821
    .line 822
    move-result-object v0

    .line 823
    invoke-direct {v8, v9, v0}, Lq21/f$c;-><init>(Lq21/c;LL11/c;)V

    .line 824
    .line 825
    .line 826
    return-object v8
.end method
