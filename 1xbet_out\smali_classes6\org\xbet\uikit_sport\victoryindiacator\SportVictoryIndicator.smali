.class public final Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;
.super Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;,
        Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u00002\u00020\u0001:\u0001\u001eB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\'\u0010\u0014\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0012H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J/\u0010\u0017\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J/\u0010\u0019\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0018R\u0016\u0010\u001d\u001a\u00020\u001a8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001c\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;",
        "Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "index",
        "",
        "win",
        "a",
        "(Landroid/graphics/Canvas;IZ)V",
        "startY",
        "d",
        "(Landroid/graphics/Canvas;IZI)V",
        "c",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;",
        "l",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;",
        "indicatorType",
        "IndicatorType",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public l:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    sget-object p3, Lm31/g;->SportVictoryIndicator:[I

    const/4 v0, 0x0

    .line 6
    invoke-virtual {p1, p2, p3, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    .line 7
    sget-object p2, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;->Companion:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType$a;

    .line 8
    sget p3, Lm31/g;->SportVictoryIndicator_typeSportIndicator:I

    .line 9
    invoke-virtual {p1, p3, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p3

    .line 10
    invoke-virtual {p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType$a;->a(I)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;->l:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    .line 11
    sget p2, Lm31/g;->SportVictoryIndicator_regularSportIndicator:I

    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setRegularIndicator(Landroid/graphics/drawable/Drawable;)V

    .line 12
    sget p2, Lm31/g;->SportVictoryIndicator_winSportIndicator:I

    invoke-virtual {p1, p2}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setWinIndicator(Landroid/graphics/drawable/Drawable;)V

    .line 13
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_10:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setIndicatorWidth(I)V

    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->b()V

    .line 15
    sget p2, Lm31/g;->SportVictoryIndicator_winColorSportIndicator:I

    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p2

    invoke-static {p1, p2}, Lorg/xbet/uikit/utils/I;->b(Landroid/content/res/TypedArray;Ljava/lang/Integer;)Ljava/lang/Integer;

    move-result-object p2

    if-eqz p2, :cond_0

    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    move-result p2

    .line 16
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setWinIndicatorColor(I)V

    .line 17
    :cond_0
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public a(Landroid/graphics/Canvas;IZ)V
    .locals 5
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getTotalCount()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorHeight()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    mul-int v0, v0, v1

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getTotalCount()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    const/4 v2, 0x1

    .line 16
    sub-int/2addr v1, v2

    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 18
    .line 19
    .line 20
    move-result v3

    .line 21
    mul-int v1, v1, v3

    .line 22
    .line 23
    add-int/2addr v0, v1

    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    sub-int/2addr v1, v0

    .line 29
    const/4 v0, 0x2

    .line 30
    div-int/2addr v1, v0

    .line 31
    iget-object v3, p0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;->l:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    .line 32
    .line 33
    if-nez v3, :cond_0

    .line 34
    .line 35
    const/4 v3, 0x0

    .line 36
    :cond_0
    sget-object v4, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$a;->a:[I

    .line 37
    .line 38
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    aget v3, v4, v3

    .line 43
    .line 44
    if-eq v3, v2, :cond_2

    .line 45
    .line 46
    if-ne v3, v0, :cond_1

    .line 47
    .line 48
    invoke-virtual {p0, p1, p2, p3, v1}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;->c(Landroid/graphics/Canvas;IZI)V

    .line 49
    .line 50
    .line 51
    return-void

    .line 52
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 53
    .line 54
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    invoke-virtual {p0, p1, p2, p3, v1}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;->d(Landroid/graphics/Canvas;IZI)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public final c(Landroid/graphics/Canvas;IZI)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    mul-int v0, v0, p2

    .line 6
    .line 7
    if-nez p2, :cond_0

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorHeight()I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    add-int/2addr v2, v1

    .line 20
    mul-int p2, p2, v2

    .line 21
    .line 22
    add-int/2addr p4, p2

    .line 23
    int-to-float p2, v0

    .line 24
    int-to-float p4, p4

    .line 25
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-virtual {p1, p2, p4}, Landroid/graphics/Canvas;->translate(FF)V

    .line 30
    .line 31
    .line 32
    if-eqz p3, :cond_1

    .line 33
    .line 34
    :try_start_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getWinIndicator()Landroid/graphics/drawable/Drawable;

    .line 35
    .line 36
    .line 37
    move-result-object p2

    .line 38
    if-eqz p2, :cond_2

    .line 39
    .line 40
    invoke-virtual {p2, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 41
    .line 42
    .line 43
    goto :goto_1

    .line 44
    :catchall_0
    move-exception p2

    .line 45
    goto :goto_2

    .line 46
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getRegularIndicator()Landroid/graphics/drawable/Drawable;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    if-eqz p2, :cond_2

    .line 51
    .line 52
    invoke-virtual {p2, p1}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 53
    .line 54
    .line 55
    :cond_2
    :goto_1
    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 56
    .line 57
    .line 58
    return-void

    .line 59
    :goto_2
    invoke-virtual {p1, v0}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 60
    .line 61
    .line 62
    throw p2
.end method

.method public final d(Landroid/graphics/Canvas;IZI)V
    .locals 5

    .line 1
    invoke-virtual {p1}, Landroid/graphics/Canvas;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    int-to-float v0, v0

    .line 6
    const/high16 v1, 0x40000000    # 2.0f

    .line 7
    .line 8
    div-float/2addr v0, v1

    .line 9
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/high16 v2, -0x40800000    # -1.0f

    .line 14
    .line 15
    const/high16 v3, 0x3f800000    # 1.0f

    .line 16
    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-virtual {p1, v2, v3, v0, v4}, Landroid/graphics/Canvas;->scale(FFFF)V

    .line 19
    .line 20
    .line 21
    :try_start_0
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;->c(Landroid/graphics/Canvas;IZI)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 22
    .line 23
    .line 24
    invoke-virtual {p1, v1}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 25
    .line 26
    .line 27
    return-void

    .line 28
    :catchall_0
    move-exception p2

    .line 29
    invoke-virtual {p1, v1}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 30
    .line 31
    .line 32
    throw p2
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getSpaceCount()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    mul-int p2, p2, v0

    .line 14
    .line 15
    add-int/2addr p1, p2

    .line 16
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getIndicatorHeight()I

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getMaxTotalCount()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    mul-int p2, p2, v0

    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getGapSize()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->getSpaceCount()I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    mul-int v0, v0, v1

    .line 35
    .line 36
    add-int/2addr p2, v0

    .line 37
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 38
    .line 39
    .line 40
    return-void
.end method
