.class public LJ4/g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<TResult:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# instance fields
.field public final a:LK4/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LK4/e<",
            "TTResult;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, LK4/e;

    invoke-direct {v0}, LK4/e;-><init>()V

    iput-object v0, p0, LJ4/g;->a:LK4/e;

    return-void
.end method

.method public constructor <init>(LJ4/a;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, LK4/e;

    invoke-direct {v0}, LK4/e;-><init>()V

    iput-object v0, p0, LJ4/g;->a:LK4/e;

    new-instance v0, LJ4/g$a;

    invoke-direct {v0, p0}, LJ4/g$a;-><init>(LJ4/g;)V

    invoke-virtual {p1, v0}, LJ4/a;->b(Ljava/lang/Runnable;)LJ4/a;

    return-void
.end method

.method public static synthetic a(LJ4/g;)LK4/e;
    .locals 0

    .line 1
    iget-object p0, p0, LJ4/g;->a:LK4/e;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public b()LJ4/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LJ4/f<",
            "TTResult;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LJ4/g;->a:LK4/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public c(Ljava/lang/Exception;)V
    .locals 1

    .line 1
    iget-object v0, p0, LJ4/g;->a:LK4/e;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LK4/e;->j(Ljava/lang/Exception;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public d(Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TTResult;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LJ4/g;->a:LK4/e;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LK4/e;->k(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
