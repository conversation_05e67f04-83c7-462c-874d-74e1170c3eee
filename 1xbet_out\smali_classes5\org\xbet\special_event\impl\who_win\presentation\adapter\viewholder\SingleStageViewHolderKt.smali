.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LHy0/b;",
        "whoWinCardClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "c",
        "(LHy0/b;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/v1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt;->d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/v1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt;->e(LHy0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LHy0/b;)LA4/c;
    .locals 4
    .param p0    # LHy0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHy0/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LJy0/t;

    .line 2
    .line 3
    invoke-direct {v0}, LJy0/t;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LJy0/u;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LJy0/u;-><init>(LHy0/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$singleStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$singleStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$singleStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$singleStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/v1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/v1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/v1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final e(LHy0/b;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LGy0/c;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LGy0/c;-><init>(LHy0/b;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, LGq0/v1;

    .line 11
    .line 12
    invoke-virtual {p0}, LGq0/v1;->b()Landroidx/recyclerview/widget/RecyclerView;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 17
    .line 18
    .line 19
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;

    .line 20
    .line 21
    invoke-direct {p0, v0, p1, v0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/SingleStageViewHolderKt$a;-><init>(LGy0/c;LB4/a;LGy0/c;LB4/a;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 25
    .line 26
    .line 27
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 28
    .line 29
    return-object p0
.end method
