.class final Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.jackpot.presentation.viewmodel.JackpotViewModel$getJackpot$2"
    f = "JackpotViewModel.kt"
    l = {
        0x7c,
        0x7d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->D3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;-><init>(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->L$0:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, LP40/c;

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto :goto_2

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->t3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)LR40/c;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iput v3, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->label:I

    .line 45
    .line 46
    invoke-virtual {p1, p0}, LR40/c;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    if-ne p1, v0, :cond_3

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_3
    :goto_0
    check-cast p1, Lkotlin/Pair;

    .line 54
    .line 55
    invoke-virtual {p1}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    check-cast v1, LP40/c;

    .line 60
    .line 61
    invoke-virtual {p1}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    check-cast p1, Ljava/lang/Number;

    .line 66
    .line 67
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 68
    .line 69
    .line 70
    move-result-wide v3

    .line 71
    iget-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 72
    .line 73
    invoke-static {p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->s3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;)Lgk/b;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iput-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->L$0:Ljava/lang/Object;

    .line 78
    .line 79
    iput v2, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->label:I

    .line 80
    .line 81
    invoke-interface {p1, v3, v4, p0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    if-ne p1, v0, :cond_4

    .line 86
    .line 87
    :goto_1
    return-object v0

    .line 88
    :cond_4
    move-object v0, v1

    .line 89
    :goto_2
    check-cast p1, Lbk/a;

    .line 90
    .line 91
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;

    .line 92
    .line 93
    new-instance v2, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$b;

    .line 94
    .line 95
    invoke-virtual {p1}, Lbk/a;->o()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    invoke-direct {v2, v0, p1}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c$b;-><init>(LP40/c;Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    invoke-static {v1, v2}, Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;->y3(Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel;Lorg/xbet/games_section/feature/jackpot/presentation/viewmodel/JackpotViewModel$c;)V

    .line 103
    .line 104
    .line 105
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 106
    .line 107
    return-object p1
.end method
