.class public final Ljb1/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u0004\u0018\u00010\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;",
        "LHX0/e;",
        "resourceManager",
        "Ljava/util/Locale;",
        "locale",
        "Lv21/g;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;LHX0/e;Ljava/util/Locale;)Lv21/g;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;LHX0/e;Ljava/util/Locale;)Lv21/g;
    .locals 17
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    invoke-interface/range {p1 .. p1}, LHX0/e;->c()Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    const-string v1, "HH:mm"

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const-string v1, "HH:mm a"

    .line 13
    .line 14
    :goto_0
    new-instance v2, Lv21/f;

    .line 15
    .line 16
    new-instance v3, Ljava/text/SimpleDateFormat;

    .line 17
    .line 18
    const-string v8, "d"

    .line 19
    .line 20
    invoke-direct {v3, v8, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-virtual {v3, v4}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    new-instance v4, Ljava/text/SimpleDateFormat;

    .line 32
    .line 33
    const-string v9, "MMMM"

    .line 34
    .line 35
    invoke-direct {v4, v9, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 39
    .line 40
    .line 41
    move-result-object v5

    .line 42
    invoke-virtual {v4, v5}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    new-instance v5, Ljava/text/SimpleDateFormat;

    .line 47
    .line 48
    const-string v10, "yyyy"

    .line 49
    .line 50
    invoke-direct {v5, v10, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    invoke-virtual {v5, v6}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v5

    .line 61
    new-instance v6, Ljava/text/SimpleDateFormat;

    .line 62
    .line 63
    invoke-direct {v6, v1, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 67
    .line 68
    .line 69
    move-result-object v7

    .line 70
    invoke-virtual {v6, v7}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v6

    .line 74
    const-string v7, " "

    .line 75
    .line 76
    invoke-direct/range {v2 .. v7}, Lv21/f;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    new-instance v11, Lv21/f;

    .line 80
    .line 81
    new-instance v3, Ljava/text/SimpleDateFormat;

    .line 82
    .line 83
    invoke-direct {v3, v8, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 87
    .line 88
    .line 89
    move-result-object v4

    .line 90
    invoke-virtual {v3, v4}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v12

    .line 94
    new-instance v3, Ljava/text/SimpleDateFormat;

    .line 95
    .line 96
    invoke-direct {v3, v9, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 97
    .line 98
    .line 99
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 100
    .line 101
    .line 102
    move-result-object v4

    .line 103
    invoke-virtual {v3, v4}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v13

    .line 107
    new-instance v3, Ljava/text/SimpleDateFormat;

    .line 108
    .line 109
    invoke-direct {v3, v10, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 110
    .line 111
    .line 112
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 113
    .line 114
    .line 115
    move-result-object v4

    .line 116
    invoke-virtual {v3, v4}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object v14

    .line 120
    new-instance v3, Ljava/text/SimpleDateFormat;

    .line 121
    .line 122
    invoke-direct {v3, v1, v0}, Ljava/text/SimpleDateFormat;-><init>(Ljava/lang/String;Ljava/util/Locale;)V

    .line 123
    .line 124
    .line 125
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-virtual {v3, v0}, Ljava/text/DateFormat;->format(Ljava/util/Date;)Ljava/lang/String;

    .line 130
    .line 131
    .line 132
    move-result-object v15

    .line 133
    const-string v16, " "

    .line 134
    .line 135
    invoke-direct/range {v11 .. v16}, Lv21/f;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 136
    .line 137
    .line 138
    new-instance v0, Lv21/g;

    .line 139
    .line 140
    sget v1, Lpb/k;->dates:I

    .line 141
    .line 142
    const/4 v3, 0x0

    .line 143
    new-array v3, v3, [Ljava/lang/Object;

    .line 144
    .line 145
    move-object/from16 v4, p1

    .line 146
    .line 147
    invoke-interface {v4, v1, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v1

    .line 151
    const-string v3, " - "

    .line 152
    .line 153
    invoke-direct {v0, v1, v3, v2, v11}, Lv21/g;-><init>(Ljava/lang/String;Ljava/lang/String;Lv21/f;Lv21/f;)V

    .line 154
    .line 155
    .line 156
    return-object v0
.end method
