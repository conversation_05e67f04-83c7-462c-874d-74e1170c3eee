.class public interface abstract LhB0/m;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008f\u0018\u00002\u00020\u0001J6\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LhB0/m;",
        "",
        "LQA0/l;",
        "sportGameModel",
        "LQA0/c;",
        "countryModel",
        "",
        "insightsEnabled",
        "isNewFeedEnable",
        "Lkotlin/Result;",
        "LYA0/a;",
        "a",
        "(LQA0/l;LQA0/c;ZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(LQA0/l;LQA0/c;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # LQA0/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LQA0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LQA0/l;",
            "LQA0/c;",
            "ZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "LYA0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
