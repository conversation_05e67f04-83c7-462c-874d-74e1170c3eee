.class public final LIN0/z;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u001aE\u0010\n\u001a\u00020\u00082\u0008\u0008\u0002\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u000e\u0008\u0001\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00080\u0007H\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "selectedIndex",
        "",
        "",
        "segments",
        "Lkotlin/Function1;",
        "",
        "onTabClick",
        "d",
        "(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "statistic_core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ljava/util/List;ILorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LIN0/z;->f(Ljava/util/List;ILorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;Landroid/content/Context;)Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LIN0/z;->e(Lkotlin/jvm/functions/Function1;Landroid/content/Context;)Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, LIN0/z;->g(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 12
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/l;",
            "I",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v5, p5

    .line 2
    .line 3
    const v0, 0x70a4c3a3

    .line 4
    .line 5
    .line 6
    move-object/from16 v1, p4

    .line 7
    .line 8
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 9
    .line 10
    .line 11
    move-result-object v9

    .line 12
    and-int/lit8 v1, p6, 0x1

    .line 13
    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    or-int/lit8 v2, v5, 0x6

    .line 17
    .line 18
    goto :goto_1

    .line 19
    :cond_0
    and-int/lit8 v2, v5, 0x6

    .line 20
    .line 21
    if-nez v2, :cond_2

    .line 22
    .line 23
    invoke-interface {v9, p0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    if-eqz v2, :cond_1

    .line 28
    .line 29
    const/4 v2, 0x4

    .line 30
    goto :goto_0

    .line 31
    :cond_1
    const/4 v2, 0x2

    .line 32
    :goto_0
    or-int/2addr v2, v5

    .line 33
    goto :goto_1

    .line 34
    :cond_2
    move v2, v5

    .line 35
    :goto_1
    and-int/lit8 v6, p6, 0x2

    .line 36
    .line 37
    const/16 v7, 0x20

    .line 38
    .line 39
    if-eqz v6, :cond_3

    .line 40
    .line 41
    or-int/lit8 v2, v2, 0x30

    .line 42
    .line 43
    goto :goto_3

    .line 44
    :cond_3
    and-int/lit8 v6, v5, 0x30

    .line 45
    .line 46
    if-nez v6, :cond_5

    .line 47
    .line 48
    invoke-interface {v9, p1}, Landroidx/compose/runtime/j;->x(I)Z

    .line 49
    .line 50
    .line 51
    move-result v6

    .line 52
    if-eqz v6, :cond_4

    .line 53
    .line 54
    const/16 v6, 0x20

    .line 55
    .line 56
    goto :goto_2

    .line 57
    :cond_4
    const/16 v6, 0x10

    .line 58
    .line 59
    :goto_2
    or-int/2addr v2, v6

    .line 60
    :cond_5
    :goto_3
    and-int/lit8 v6, p6, 0x4

    .line 61
    .line 62
    if-eqz v6, :cond_6

    .line 63
    .line 64
    or-int/lit16 v2, v2, 0x180

    .line 65
    .line 66
    goto :goto_5

    .line 67
    :cond_6
    and-int/lit16 v6, v5, 0x180

    .line 68
    .line 69
    if-nez v6, :cond_8

    .line 70
    .line 71
    invoke-interface {v9, p2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 72
    .line 73
    .line 74
    move-result v6

    .line 75
    if-eqz v6, :cond_7

    .line 76
    .line 77
    const/16 v6, 0x100

    .line 78
    .line 79
    goto :goto_4

    .line 80
    :cond_7
    const/16 v6, 0x80

    .line 81
    .line 82
    :goto_4
    or-int/2addr v2, v6

    .line 83
    :cond_8
    :goto_5
    and-int/lit8 v6, p6, 0x8

    .line 84
    .line 85
    const/16 v8, 0x800

    .line 86
    .line 87
    if-eqz v6, :cond_9

    .line 88
    .line 89
    or-int/lit16 v2, v2, 0xc00

    .line 90
    .line 91
    goto :goto_7

    .line 92
    :cond_9
    and-int/lit16 v6, v5, 0xc00

    .line 93
    .line 94
    if-nez v6, :cond_b

    .line 95
    .line 96
    invoke-interface {v9, p3}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 97
    .line 98
    .line 99
    move-result v6

    .line 100
    if-eqz v6, :cond_a

    .line 101
    .line 102
    const/16 v6, 0x800

    .line 103
    .line 104
    goto :goto_6

    .line 105
    :cond_a
    const/16 v6, 0x400

    .line 106
    .line 107
    :goto_6
    or-int/2addr v2, v6

    .line 108
    :cond_b
    :goto_7
    and-int/lit16 v6, v2, 0x493

    .line 109
    .line 110
    const/16 v10, 0x492

    .line 111
    .line 112
    if-ne v6, v10, :cond_e

    .line 113
    .line 114
    invoke-interface {v9}, Landroidx/compose/runtime/j;->c()Z

    .line 115
    .line 116
    .line 117
    move-result v6

    .line 118
    if-nez v6, :cond_c

    .line 119
    .line 120
    goto :goto_9

    .line 121
    :cond_c
    invoke-interface {v9}, Landroidx/compose/runtime/j;->n()V

    .line 122
    .line 123
    .line 124
    :cond_d
    :goto_8
    move-object v1, p0

    .line 125
    goto/16 :goto_b

    .line 126
    .line 127
    :cond_e
    :goto_9
    if-eqz v1, :cond_f

    .line 128
    .line 129
    sget-object p0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 130
    .line 131
    :cond_f
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 132
    .line 133
    .line 134
    move-result v1

    .line 135
    if-eqz v1, :cond_10

    .line 136
    .line 137
    const/4 v1, -0x1

    .line 138
    const-string v6, "org.xbet.statistic.statistic_core.presentation.composable.StatisticSegmentedControl (StatisticSegmentedControl.kt:18)"

    .line 139
    .line 140
    invoke-static {v0, v2, v1, v6}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 141
    .line 142
    .line 143
    :cond_10
    const/4 v0, 0x3

    .line 144
    const/4 v1, 0x0

    .line 145
    const/4 v6, 0x0

    .line 146
    invoke-static {p0, v1, v6, v0, v1}, Landroidx/compose/foundation/layout/SizeKt;->E(Landroidx/compose/ui/l;Landroidx/compose/ui/e$c;ZILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 147
    .line 148
    .line 149
    move-result-object v0

    .line 150
    const/4 v10, 0x0

    .line 151
    const/4 v11, 0x1

    .line 152
    invoke-static {v0, v10, v11, v1}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 153
    .line 154
    .line 155
    move-result-object v0

    .line 156
    const v1, 0x4c5de2

    .line 157
    .line 158
    .line 159
    invoke-interface {v9, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 160
    .line 161
    .line 162
    and-int/lit16 v1, v2, 0x1c00

    .line 163
    .line 164
    if-ne v1, v8, :cond_11

    .line 165
    .line 166
    const/4 v1, 0x1

    .line 167
    goto :goto_a

    .line 168
    :cond_11
    const/4 v1, 0x0

    .line 169
    :goto_a
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 170
    .line 171
    .line 172
    move-result-object v8

    .line 173
    if-nez v1, :cond_12

    .line 174
    .line 175
    sget-object v1, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 176
    .line 177
    invoke-virtual {v1}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 178
    .line 179
    .line 180
    move-result-object v1

    .line 181
    if-ne v8, v1, :cond_13

    .line 182
    .line 183
    :cond_12
    new-instance v8, LIN0/w;

    .line 184
    .line 185
    invoke-direct {v8, p3}, LIN0/w;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 186
    .line 187
    .line 188
    invoke-interface {v9, v8}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 189
    .line 190
    .line 191
    :cond_13
    check-cast v8, Lkotlin/jvm/functions/Function1;

    .line 192
    .line 193
    invoke-interface {v9}, Landroidx/compose/runtime/j;->q()V

    .line 194
    .line 195
    .line 196
    const v1, -0x615d173a

    .line 197
    .line 198
    .line 199
    invoke-interface {v9, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 200
    .line 201
    .line 202
    invoke-interface {v9, p2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 203
    .line 204
    .line 205
    move-result v1

    .line 206
    and-int/lit8 v2, v2, 0x70

    .line 207
    .line 208
    if-ne v2, v7, :cond_14

    .line 209
    .line 210
    const/4 v6, 0x1

    .line 211
    :cond_14
    or-int/2addr v1, v6

    .line 212
    invoke-interface {v9}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v2

    .line 216
    if-nez v1, :cond_15

    .line 217
    .line 218
    sget-object v1, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 219
    .line 220
    invoke-virtual {v1}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 221
    .line 222
    .line 223
    move-result-object v1

    .line 224
    if-ne v2, v1, :cond_16

    .line 225
    .line 226
    :cond_15
    new-instance v2, LIN0/x;

    .line 227
    .line 228
    invoke-direct {v2, p2, p1}, LIN0/x;-><init>(Ljava/util/List;I)V

    .line 229
    .line 230
    .line 231
    invoke-interface {v9, v2}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 232
    .line 233
    .line 234
    :cond_16
    check-cast v2, Lkotlin/jvm/functions/Function1;

    .line 235
    .line 236
    invoke-interface {v9}, Landroidx/compose/runtime/j;->q()V

    .line 237
    .line 238
    .line 239
    const/4 v10, 0x0

    .line 240
    const/4 v11, 0x0

    .line 241
    move-object v7, v0

    .line 242
    move-object v6, v8

    .line 243
    move-object v8, v2

    .line 244
    invoke-static/range {v6 .. v11}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 245
    .line 246
    .line 247
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 248
    .line 249
    .line 250
    move-result v0

    .line 251
    if-eqz v0, :cond_d

    .line 252
    .line 253
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 254
    .line 255
    .line 256
    goto/16 :goto_8

    .line 257
    .line 258
    :goto_b
    invoke-interface {v9}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 259
    .line 260
    .line 261
    move-result-object p0

    .line 262
    if-eqz p0, :cond_17

    .line 263
    .line 264
    new-instance v0, LIN0/y;

    .line 265
    .line 266
    move v2, p1

    .line 267
    move-object v3, p2

    .line 268
    move-object v4, p3

    .line 269
    move/from16 v6, p6

    .line 270
    .line 271
    invoke-direct/range {v0 .. v6}, LIN0/y;-><init>(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;II)V

    .line 272
    .line 273
    .line 274
    invoke-interface {p0, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 275
    .line 276
    .line 277
    :cond_17
    return-void
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;Landroid/content/Context;)Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .locals 7

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 2
    .line 3
    const/16 v5, 0xe

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    move-object v1, p1

    .line 10
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    new-instance p1, Landroid/view/ViewGroup$LayoutParams;

    .line 14
    .line 15
    const/4 v1, -0x1

    .line 16
    const/4 v2, -0x2

    .line 17
    invoke-direct {p1, v1, v2}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentClickedAndSelectedListener(Lkotlin/jvm/functions/Function1;)V

    .line 24
    .line 25
    .line 26
    return-object v0
.end method

.method public static final f(Ljava/util/List;ILorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;)Lkotlin/Unit;
    .locals 10

    .line 1
    invoke-virtual {p2}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->getSegments()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ljava/util/ArrayList;

    .line 6
    .line 7
    const/16 v2, 0xa

    .line 8
    .line 9
    invoke-static {v0, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 14
    .line 15
    .line 16
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v2

    .line 24
    if-eqz v2, :cond_0

    .line 25
    .line 26
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    check-cast v2, Lu01/a;

    .line 31
    .line 32
    invoke-virtual {v2}, Lu01/a;->b()Ljava/lang/CharSequence;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_0
    invoke-static {p0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    if-nez v0, :cond_3

    .line 45
    .line 46
    invoke-virtual {p2}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->r()V

    .line 47
    .line 48
    .line 49
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 50
    .line 51
    .line 52
    move-result-object p0

    .line 53
    const/4 v0, 0x0

    .line 54
    const/4 v1, 0x0

    .line 55
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 56
    .line 57
    .line 58
    move-result v2

    .line 59
    if-eqz v2, :cond_3

    .line 60
    .line 61
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    add-int/lit8 v3, v1, 0x1

    .line 66
    .line 67
    if-gez v1, :cond_1

    .line 68
    .line 69
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 70
    .line 71
    .line 72
    :cond_1
    check-cast v2, Ljava/lang/String;

    .line 73
    .line 74
    new-instance v5, Lu01/a;

    .line 75
    .line 76
    invoke-direct {v5}, Lu01/a;-><init>()V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v5, v2}, Lu01/a;->d(Ljava/lang/CharSequence;)V

    .line 80
    .line 81
    .line 82
    if-ne v1, p1, :cond_2

    .line 83
    .line 84
    const/4 v1, 0x1

    .line 85
    const/4 v7, 0x1

    .line 86
    goto :goto_2

    .line 87
    :cond_2
    const/4 v7, 0x0

    .line 88
    :goto_2
    const/4 v8, 0x2

    .line 89
    const/4 v9, 0x0

    .line 90
    const/4 v6, 0x0

    .line 91
    move-object v4, p2

    .line 92
    invoke-static/range {v4 .. v9}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->h(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lu01/a;IZILjava/lang/Object;)V

    .line 93
    .line 94
    .line 95
    move v1, v3

    .line 96
    goto :goto_1

    .line 97
    :cond_3
    move-object v4, p2

    .line 98
    invoke-virtual {v4, p1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setSelectedPosition(I)V

    .line 99
    .line 100
    .line 101
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 102
    .line 103
    return-object p0
.end method

.method public static final g(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 7

    .line 1
    or-int/lit8 p4, p4, 0x1

    .line 2
    .line 3
    invoke-static {p4}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v5

    .line 7
    move-object v0, p0

    .line 8
    move v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move-object v3, p3

    .line 11
    move v6, p5

    .line 12
    move-object v4, p6

    .line 13
    invoke-static/range {v0 .. v6}, LIN0/z;->d(Landroidx/compose/ui/l;ILjava/util/List;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method
