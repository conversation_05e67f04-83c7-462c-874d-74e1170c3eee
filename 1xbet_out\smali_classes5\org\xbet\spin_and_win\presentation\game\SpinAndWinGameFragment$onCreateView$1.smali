.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameFragment$onCreateView$1"
    f = "SpinAndWinGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->onCreateView(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/spin_and_win/presentation/game/r;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/r;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/spin_and_win/presentation/game/r;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;

    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/r;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->invoke(Lorg/xbet/spin_and_win/presentation/game/r;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/spin_and_win/presentation/game/r;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/r;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/r;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/r$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 20
    .line 21
    const/4 v1, 0x1

    .line 22
    invoke-static {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->L2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 26
    .line 27
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/r$a;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/r$a;->a()I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    invoke-static {v0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->M2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;I)V

    .line 34
    .line 35
    .line 36
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 37
    .line 38
    return-object p1

    .line 39
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 40
    .line 41
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 42
    .line 43
    .line 44
    throw p1

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1
.end method
