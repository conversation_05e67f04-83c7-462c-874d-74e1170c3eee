.class public Lh4/e;
.super Lh4/a;
.source "SourceFile"


# direct methods
.method public constructor <init>(Li4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lh4/a;-><init>(Li4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(FF)Lh4/d;
    .locals 4

    .line 1
    iget-object v0, p0, Lh4/b;->a:Li4/b;

    .line 2
    .line 3
    check-cast v0, Li4/a;

    .line 4
    .line 5
    invoke-interface {v0}, Li4/a;->getBarData()Lf4/a;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, p2, p1}, Lh4/b;->j(FF)Lp4/d;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iget-wide v2, v1, Lp4/d;->d:D

    .line 14
    .line 15
    double-to-float v2, v2

    .line 16
    invoke-virtual {p0, v2, p2, p1}, Lh4/b;->f(FFF)Lh4/d;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    if-nez p1, :cond_0

    .line 21
    .line 22
    const/4 p1, 0x0

    .line 23
    return-object p1

    .line 24
    :cond_0
    invoke-virtual {p1}, Lh4/d;->d()I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    invoke-virtual {v0, p2}, Lf4/h;->h(I)Lj4/e;

    .line 29
    .line 30
    .line 31
    move-result-object p2

    .line 32
    check-cast p2, Lj4/a;

    .line 33
    .line 34
    invoke-interface {p2}, Lj4/a;->Q()Z

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    if-eqz v0, :cond_1

    .line 39
    .line 40
    iget-wide v2, v1, Lp4/d;->d:D

    .line 41
    .line 42
    double-to-float v0, v2

    .line 43
    iget-wide v1, v1, Lp4/d;->c:D

    .line 44
    .line 45
    double-to-float v1, v1

    .line 46
    invoke-virtual {p0, p1, p2, v0, v1}, Lh4/a;->l(Lh4/d;Lj4/a;FF)Lh4/d;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    return-object p1

    .line 51
    :cond_1
    invoke-static {v1}, Lp4/d;->c(Lp4/d;)V

    .line 52
    .line 53
    .line 54
    return-object p1
.end method

.method public b(Lj4/e;IFLcom/github/mikephil/charting/data/DataSet$Rounding;)Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lj4/e;",
            "IF",
            "Lcom/github/mikephil/charting/data/DataSet$Rounding;",
            ")",
            "Ljava/util/List<",
            "Lh4/d;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1, p3}, Lj4/e;->r(F)Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    if-nez v2, :cond_0

    .line 15
    .line 16
    const/high16 v2, 0x7fc00000    # Float.NaN

    .line 17
    .line 18
    invoke-interface {p1, p3, v2, p4}, Lj4/e;->F0(FFLcom/github/mikephil/charting/data/DataSet$Rounding;)Lcom/github/mikephil/charting/data/Entry;

    .line 19
    .line 20
    .line 21
    move-result-object p3

    .line 22
    if-eqz p3, :cond_0

    .line 23
    .line 24
    invoke-virtual {p3}, Lcom/github/mikephil/charting/data/Entry;->f()F

    .line 25
    .line 26
    .line 27
    move-result p3

    .line 28
    invoke-interface {p1, p3}, Lj4/e;->r(F)Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    :cond_0
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 33
    .line 34
    .line 35
    move-result p3

    .line 36
    if-nez p3, :cond_1

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 40
    .line 41
    .line 42
    move-result-object p3

    .line 43
    :goto_0
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result p4

    .line 47
    if-eqz p4, :cond_2

    .line 48
    .line 49
    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object p4

    .line 53
    check-cast p4, Lcom/github/mikephil/charting/data/Entry;

    .line 54
    .line 55
    iget-object v1, p0, Lh4/b;->a:Li4/b;

    .line 56
    .line 57
    check-cast v1, Li4/a;

    .line 58
    .line 59
    invoke-interface {p1}, Lj4/e;->n0()Lcom/github/mikephil/charting/components/YAxis$AxisDependency;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    invoke-interface {v1, v2}, Li4/b;->d(Lcom/github/mikephil/charting/components/YAxis$AxisDependency;)Lp4/g;

    .line 64
    .line 65
    .line 66
    move-result-object v1

    .line 67
    invoke-virtual {p4}, Lf4/e;->c()F

    .line 68
    .line 69
    .line 70
    move-result v2

    .line 71
    invoke-virtual {p4}, Lcom/github/mikephil/charting/data/Entry;->f()F

    .line 72
    .line 73
    .line 74
    move-result v3

    .line 75
    invoke-virtual {v1, v2, v3}, Lp4/g;->e(FF)Lp4/d;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    new-instance v2, Lh4/d;

    .line 80
    .line 81
    invoke-virtual {p4}, Lcom/github/mikephil/charting/data/Entry;->f()F

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    invoke-virtual {p4}, Lf4/e;->c()F

    .line 86
    .line 87
    .line 88
    move-result v4

    .line 89
    iget-wide v5, v1, Lp4/d;->c:D

    .line 90
    .line 91
    double-to-float v5, v5

    .line 92
    iget-wide v6, v1, Lp4/d;->d:D

    .line 93
    .line 94
    double-to-float v6, v6

    .line 95
    invoke-interface {p1}, Lj4/e;->n0()Lcom/github/mikephil/charting/components/YAxis$AxisDependency;

    .line 96
    .line 97
    .line 98
    move-result-object v8

    .line 99
    move v7, p2

    .line 100
    invoke-direct/range {v2 .. v8}, Lh4/d;-><init>(FFFFILcom/github/mikephil/charting/components/YAxis$AxisDependency;)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 104
    .line 105
    .line 106
    goto :goto_0

    .line 107
    :cond_2
    :goto_1
    return-object v0
.end method

.method public e(FFFF)F
    .locals 0

    .line 1
    sub-float/2addr p2, p4

    .line 2
    invoke-static {p2}, Ljava/lang/Math;->abs(F)F

    .line 3
    .line 4
    .line 5
    move-result p1

    .line 6
    return p1
.end method
