.class public final Lorg/xbet/tile_matching/domain/usecases/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0080\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0008J\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u000f\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/e;",
        "",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(LAT0/a;)V",
        "",
        "b",
        "()V",
        "a",
        "LzT0/d;",
        "model",
        "",
        "d",
        "(LzT0/d;)Z",
        "c",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LAT0/a;)V
    .locals 0
    .param p1    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a()V
    .locals 11

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lorg/xbet/tile_matching/domain/models/TileMatchingType;->values()[Lorg/xbet/tile_matching/domain/models/TileMatchingType;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x5

    .line 12
    invoke-static {v2, v3}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    :cond_0
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v5

    .line 24
    if-eqz v5, :cond_1

    .line 25
    .line 26
    move-object v5, v4

    .line 27
    check-cast v5, Lkotlin/collections/L;

    .line 28
    .line 29
    invoke-virtual {v5}, Lkotlin/collections/L;->b()I

    .line 30
    .line 31
    .line 32
    move-result v5

    .line 33
    invoke-static {v2, v3}, Lkotlin/ranges/f;->z(II)Lkotlin/ranges/IntRange;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 38
    .line 39
    .line 40
    move-result-object v6

    .line 41
    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 42
    .line 43
    .line 44
    move-result v7

    .line 45
    if-eqz v7, :cond_0

    .line 46
    .line 47
    move-object v7, v6

    .line 48
    check-cast v7, Lkotlin/collections/L;

    .line 49
    .line 50
    invoke-virtual {v7}, Lkotlin/collections/L;->b()I

    .line 51
    .line 52
    .line 53
    move-result v7

    .line 54
    new-instance v8, LzT0/b;

    .line 55
    .line 56
    invoke-static {v1}, Lkotlin/collections/r;->l0([Ljava/lang/Object;)Lkotlin/ranges/IntRange;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    sget-object v10, Lkotlin/random/Random;->Default:Lkotlin/random/Random$Default;

    .line 61
    .line 62
    invoke-static {v9, v10}, Lkotlin/ranges/f;->w(Lkotlin/ranges/IntRange;Lkotlin/random/Random;)I

    .line 63
    .line 64
    .line 65
    move-result v9

    .line 66
    aget-object v9, v1, v9

    .line 67
    .line 68
    invoke-direct {v8, v9, v5, v7}, LzT0/b;-><init>(Lorg/xbet/tile_matching/domain/models/TileMatchingType;II)V

    .line 69
    .line 70
    .line 71
    invoke-interface {v0, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 72
    .line 73
    .line 74
    goto :goto_0

    .line 75
    :cond_1
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 76
    .line 77
    invoke-interface {v1, v0}, LAT0/a;->a(Ljava/util/List;)V

    .line 78
    .line 79
    .line 80
    return-void
.end method

.method public final b()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 2
    .line 3
    invoke-interface {v0}, LAT0/a;->c()LzT0/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, LzT0/e;->f()LzT0/d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/domain/usecases/e;->d(LzT0/d;)Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 18
    .line 19
    sget-object v2, LzT0/e;->i:LzT0/e$a;

    .line 20
    .line 21
    invoke-virtual {v2}, LzT0/e$a;->a()LzT0/e;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-interface {v1, v2}, LAT0/a;->i(LzT0/e;)V

    .line 26
    .line 27
    .line 28
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 29
    .line 30
    invoke-virtual {v0}, LzT0/d;->c()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-interface {v1, v2}, LAT0/a;->a(Ljava/util/List;)V

    .line 35
    .line 36
    .line 37
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/e;->a:LAT0/a;

    .line 38
    .line 39
    invoke-virtual {v0}, LzT0/d;->a()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-interface {v1, v2}, LAT0/a;->e(Ljava/util/List;)V

    .line 44
    .line 45
    .line 46
    :cond_0
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/domain/usecases/e;->d(LzT0/d;)Z

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    if-eqz v1, :cond_1

    .line 51
    .line 52
    invoke-virtual {p0, v0}, Lorg/xbet/tile_matching/domain/usecases/e;->c(LzT0/d;)Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    if-eqz v0, :cond_1

    .line 57
    .line 58
    invoke-virtual {p0}, Lorg/xbet/tile_matching/domain/usecases/e;->a()V

    .line 59
    .line 60
    .line 61
    :cond_1
    return-void
.end method

.method public final c(LzT0/d;)Z
    .locals 0

    .line 1
    invoke-virtual {p1}, LzT0/d;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public final d(LzT0/d;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, LzT0/d;->e()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p1}, LzT0/d;->d()Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    invoke-virtual {p1}, LzT0/d;->b()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-eqz p1, :cond_0

    .line 30
    .line 31
    const/4 p1, 0x1

    .line 32
    return p1

    .line 33
    :cond_0
    const/4 p1, 0x0

    .line 34
    return p1
.end method
