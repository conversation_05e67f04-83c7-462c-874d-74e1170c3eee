.class public final LgT0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LgT0/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LhT0/g;",
            ">;",
            "LBc/a<",
            "LhT0/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LgT0/b;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LgT0/b;->b:LBc/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(LBc/a;LBc/a;)LgT0/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LhT0/g;",
            ">;",
            "LBc/a<",
            "LhT0/c;",
            ">;)",
            "LgT0/b;"
        }
    .end annotation

    .line 1
    new-instance v0, LgT0/b;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LgT0/b;-><init>(LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LhT0/g;LhT0/c;)LgT0/a;
    .locals 1

    .line 1
    new-instance v0, LgT0/a;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LgT0/a;-><init>(LhT0/g;LhT0/c;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LgT0/a;
    .locals 2

    .line 1
    iget-object v0, p0, LgT0/b;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LhT0/g;

    .line 8
    .line 9
    iget-object v1, p0, LgT0/b;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, LhT0/c;

    .line 16
    .line 17
    invoke-static {v0, v1}, LgT0/b;->c(LhT0/g;LhT0/c;)LgT0/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LgT0/b;->b()LgT0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
