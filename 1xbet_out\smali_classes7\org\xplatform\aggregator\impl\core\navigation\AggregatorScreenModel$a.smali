.class public final Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 12

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v3

    invoke-virtual {p1}, Landroid/os/Parcel;->readSerializable()Ljava/io/Serializable;

    move-result-object v5

    check-cast v5, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType;

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->valueOf(Ljava/lang/String;)Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    move-result-object v6

    invoke-virtual {p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v7

    invoke-virtual {p1}, Landroid/os/Parcel;->readLong()J

    move-result-wide v9

    invoke-virtual {p1}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v11

    invoke-direct/range {v0 .. v11}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;)V

    return-object v0
.end method

.method public final b(I)[Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 0

    .line 1
    new-array p1, p1, [Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel$a;->a(Landroid/os/Parcel;)Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel$a;->b(I)[Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    move-result-object p1

    return-object p1
.end method
