.class public final Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0088\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u0017\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u0011\u0010\u000f\u001a\u0004\u0018\u00010\u000eH\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0015\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0015\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001f\u001a\u00020\n2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010\"\u001a\u00020\n2\u0008\u0010!\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008\"\u0010 J\u0017\u0010$\u001a\u00020\n2\u0008\u0010#\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008$\u0010 J\u0017\u0010&\u001a\u00020\n2\u0008\u0010%\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008&\u0010 J\u0017\u0010(\u001a\u00020\n2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0006\u00a2\u0006\u0004\u0008(\u0010)J\u0017\u0010+\u001a\u00020\n2\u0008\u0010*\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008+\u0010 J\u0017\u0010-\u001a\u00020\n2\u0008\u0008\u0001\u0010,\u001a\u00020\u0006\u00a2\u0006\u0004\u0008-\u0010)J\u0017\u0010/\u001a\u00020\n2\u0008\u0010.\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008/\u0010 J\u0017\u00101\u001a\u00020\n2\u0008\u0008\u0001\u00100\u001a\u00020\u0006\u00a2\u0006\u0004\u00081\u0010)J\u0017\u00103\u001a\u00020\n2\u0008\u00102\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u00083\u0010 J\u001f\u00107\u001a\u00020\n2\u0008\u00104\u001a\u0004\u0018\u00010\u001d2\u0006\u00106\u001a\u000205\u00a2\u0006\u0004\u00087\u00108J#\u0010<\u001a\u00020\n2\u0014\u0010;\u001a\u0010\u0012\u0004\u0012\u00020:\u0012\u0004\u0012\u00020\n\u0018\u000109\u00a2\u0006\u0004\u0008<\u0010=J#\u0010>\u001a\u00020\n2\u0014\u0010;\u001a\u0010\u0012\u0004\u0012\u00020:\u0012\u0004\u0012\u00020\n\u0018\u000109\u00a2\u0006\u0004\u0008>\u0010=J\u0015\u0010@\u001a\u00020\n2\u0006\u0010?\u001a\u00020\u0006\u00a2\u0006\u0004\u0008@\u0010)J\u0015\u0010B\u001a\u00020\n2\u0006\u0010A\u001a\u00020\u0015\u00a2\u0006\u0004\u0008B\u0010\u0018J\u0017\u0010C\u001a\u00020\n2\u0008\u0008\u0001\u00100\u001a\u00020\u0006\u00a2\u0006\u0004\u0008C\u0010)J\u0015\u0010F\u001a\u00020\n2\u0006\u0010E\u001a\u00020D\u00a2\u0006\u0004\u0008F\u0010GJ\u0015\u0010J\u001a\u00020\n2\u0006\u0010I\u001a\u00020H\u00a2\u0006\u0004\u0008J\u0010KR\u0016\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010NR\u001b\u0010T\u001a\u00020O8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008P\u0010Q\u001a\u0004\u0008R\u0010SR\u0016\u0010U\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010NR\u0016\u0010X\u001a\u00020V8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010WR$\u0010Z\u001a\u0010\u0012\u0004\u0012\u00020:\u0012\u0004\u0012\u00020\n\u0018\u0001098\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010YR$\u0010\\\u001a\u0010\u0012\u0004\u0012\u00020:\u0012\u0004\u0012\u00020\n\u0018\u0001098\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008[\u0010YR(\u0010c\u001a\u0004\u0018\u00010]2\u0008\u0010^\u001a\u0004\u0018\u00010]8\u0006@BX\u0086\u000e\u00a2\u0006\u000c\n\u0004\u0008_\u0010`\u001a\u0004\u0008a\u0010b\u00a8\u0006d"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "e",
        "()V",
        "b",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "LX31/c;",
        "model",
        "setModel",
        "(LX31/c;)V",
        "",
        "couponCardStyle",
        "d",
        "(Ljava/lang/String;)V",
        "",
        "live",
        "f",
        "(Z)V",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "subTitle",
        "setSubTitle",
        "caption",
        "setCaption",
        "tagText",
        "setTagText",
        "tagColor",
        "setTagColor",
        "(I)V",
        "error",
        "setError",
        "marketStyle",
        "setMarketStyle",
        "description",
        "setMarketDescription",
        "textStyle",
        "setMarketDescriptionStyle",
        "marketHeader",
        "setMarketHeader",
        "coef",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoef",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "res",
        "setSportImage",
        "bonusTitle",
        "setCouponBonusTitle",
        "setCouponBonusTitleStyle",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "LX31/b;",
        "teamsUiModel",
        "setTeamsUiModel",
        "(LX31/b;)V",
        "a",
        "Landroid/util/AttributeSet;",
        "I",
        "Lorg/xbet/uikit/utils/e;",
        "c",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "cornerRadius",
        "Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;",
        "Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;",
        "currentStyle",
        "Lkotlin/jvm/functions/Function1;",
        "onMoveButtonClickListener",
        "g",
        "onCancelButtonClickListener",
        "LU31/a;",
        "value",
        "h",
        "LU31/a;",
        "getCurrentStyledView",
        "()LU31/a;",
        "currentStyledView",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Landroid/util/AttributeSet;

.field public final b:I

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:I

.field public e:Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public g:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public h:LU31/a;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->a:Landroid/util/AttributeSet;

    .line 7
    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->b:I

    .line 8
    new-instance p1, LT31/d;

    invoke-direct {p1, p0}, LT31/d;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;)V

    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->c:Lkotlin/j;

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget v0, LlZ0/g;->radius_16:I

    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->d:I

    .line 10
    sget-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;->COUPON_CARD_WITH_BG_ACCENT_TEAM:Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->e:Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;

    .line 11
    invoke-static {p0, p1}, Lorg/xbet/uikit/utils/S;->m(Landroid/view/View;I)V

    .line 12
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object p1

    invoke-virtual {p1, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->c(Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final b()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->e:Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard$a;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_4

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_3

    .line 16
    .line 17
    const/4 v1, 0x3

    .line 18
    if-eq v0, v1, :cond_2

    .line 19
    .line 20
    const/4 v1, 0x4

    .line 21
    if-eq v0, v1, :cond_1

    .line 22
    .line 23
    const/4 v1, 0x5

    .line 24
    if-ne v0, v1, :cond_0

    .line 25
    .line 26
    new-instance v2, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardBigTeamLogo;

    .line 27
    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    const/4 v6, 0x6

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v4, 0x0

    .line 35
    const/4 v5, 0x0

    .line 36
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardBigTeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 41
    .line 42
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 43
    .line 44
    .line 45
    throw v0

    .line 46
    :cond_1
    new-instance v1, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;

    .line 47
    .line 48
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    const/4 v5, 0x6

    .line 53
    const/4 v6, 0x0

    .line 54
    const/4 v3, 0x0

    .line 55
    const/4 v4, 0x0

    .line 56
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 57
    .line 58
    .line 59
    move-object v2, v1

    .line 60
    goto :goto_0

    .line 61
    :cond_2
    new-instance v2, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;

    .line 62
    .line 63
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 64
    .line 65
    .line 66
    move-result-object v3

    .line 67
    const/4 v6, 0x6

    .line 68
    const/4 v7, 0x0

    .line 69
    const/4 v4, 0x0

    .line 70
    const/4 v5, 0x0

    .line 71
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 72
    .line 73
    .line 74
    goto :goto_0

    .line 75
    :cond_3
    new-instance v3, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;

    .line 76
    .line 77
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 78
    .line 79
    .line 80
    move-result-object v4

    .line 81
    const/4 v7, 0x6

    .line 82
    const/4 v8, 0x0

    .line 83
    const/4 v5, 0x0

    .line 84
    const/4 v6, 0x0

    .line 85
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 86
    .line 87
    .line 88
    move-object v2, v3

    .line 89
    goto :goto_0

    .line 90
    :cond_4
    new-instance v4, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;

    .line 91
    .line 92
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 93
    .line 94
    .line 95
    move-result-object v5

    .line 96
    const/4 v8, 0x6

    .line 97
    const/4 v9, 0x0

    .line 98
    const/4 v6, 0x0

    .line 99
    const/4 v7, 0x0

    .line 100
    invoke-direct/range {v4 .. v9}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 101
    .line 102
    .line 103
    move-object v2, v4

    .line 104
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->a:Landroid/util/AttributeSet;

    .line 109
    .line 110
    sget-object v3, Lm31/g;->SportCouponCardView:[I

    .line 111
    .line 112
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->b:I

    .line 113
    .line 114
    const/4 v5, 0x0

    .line 115
    invoke-virtual {v0, v1, v3, v4, v5}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    sget v1, Lm31/g;->SportCouponCardView_showSkeleton:I

    .line 120
    .line 121
    invoke-virtual {v0, v1, v5}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 122
    .line 123
    .line 124
    move-result v1

    .line 125
    if-eqz v1, :cond_5

    .line 126
    .line 127
    invoke-interface {v2}, LU31/h;->a()V

    .line 128
    .line 129
    .line 130
    :cond_5
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 131
    .line 132
    .line 133
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 134
    .line 135
    .line 136
    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 137
    .line 138
    return-void
.end method

.method public final d(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    invoke-static {p1}, LT31/b;->a(Ljava/lang/String;)Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->e:Lorg/xbet/uikit_sport/sport_coupon_card/common/CouponCardRemoteConfigStyleType;

    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->e()V

    .line 13
    .line 14
    .line 15
    const-string p1, "DsCouponCard"

    .line 16
    .line 17
    invoke-virtual {p0, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final e()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->b()V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->g:Lkotlin/jvm/functions/Function1;

    .line 12
    .line 13
    invoke-interface {v0, v1}, LU31/a;->setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->f:Lkotlin/jvm/functions/Function1;

    .line 21
    .line 22
    invoke-interface {v0, v1}, LU31/a;->setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 23
    .line 24
    .line 25
    :cond_1
    return-void
.end method

.method public final f(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/e;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/e;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/e;->b(Z)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final getCurrentStyledView()LU31/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->g:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0, p1}, LU31/a;->setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final setCaption(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/d;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/d;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/d;->setCaption(Ljava/lang/CharSequence;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setCouponBonusTitle(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setCouponBonusTitle(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setCouponBonusTitleStyle(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/b;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/b;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/b;->setCouponBonusTitleStyle(I)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setError(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setError(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setMarketCoef(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1, p2}, LU31/a;->setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setMarketDescriptionStyle(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/c;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/c;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/c;->setCouponMarketDescriptionStyle(I)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setMarketHeader(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/f;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/f;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/f;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setMarketStyle(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {v0, p1}, LU31/a;->setMarketStyle(Ljava/lang/Integer;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    return-void
.end method

.method public final setModel(LX31/c;)V
    .locals 1
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setModel(LX31/c;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0, p1}, LU31/a;->setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final setScoreUiModel(LX31/a;)V
    .locals 2
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/g;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/g;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/g;->setScoreUiModel(LX31/a;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setSportImage(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/i;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/i;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/i;->setSportImage(I)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setSubTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setTagColor(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setTagColor(I)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, LU31/a;->setTagText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setTeamsUiModel(LX31/b;)V
    .locals 2
    .param p1    # LX31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/g;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/g;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/g;->setTeamsUiModel(LX31/b;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/common/DsSportCouponCard;->h:LU31/a;

    .line 2
    .line 3
    instance-of v1, v0, LU31/j;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    check-cast v0, LU31/j;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    if-eqz v0, :cond_1

    .line 12
    .line 13
    invoke-interface {v0, p1}, LU31/j;->setTitle(Ljava/lang/CharSequence;)V

    .line 14
    .line 15
    .line 16
    :cond_1
    return-void
.end method
