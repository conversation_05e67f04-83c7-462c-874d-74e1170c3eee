.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

.field public final synthetic b:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;->b:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/b;->b:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;->r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedFragment;Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
