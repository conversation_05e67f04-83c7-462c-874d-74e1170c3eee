.class public final synthetic Lorg/xbet/games_section/feature/jackpot/presentation/fragments/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/d;->a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/d;->a:Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;

    invoke-static {v0}, Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;->y2(Lorg/xbet/games_section/feature/jackpot/presentation/fragments/JackpotFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
