.class public final LIa1/g$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIa1/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIa1/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LIa1/g$a$j;,
        LIa1/g$a$c;,
        LIa1/g$a$m;,
        LIa1/g$a$a;,
        LIa1/g$a$b;,
        LIa1/g$a$i;,
        LIa1/g$a$n;,
        LIa1/g$a$d;,
        LIa1/g$a$l;,
        LIa1/g$a$t;,
        LIa1/g$a$h;,
        LIa1/g$a$f;,
        LIa1/g$a$g;,
        LIa1/g$a$o;,
        LIa1/g$a$r;,
        LIa1/g$a$q;,
        LIa1/g$a$k;,
        LIa1/g$a$e;,
        LIa1/g$a$p;,
        LIa1/g$a$s;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le81/c;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LZR/a;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/g;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LG81/b;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LC81/f;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGg/a;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAR/a;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/s;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/d;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/u;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LzX0/k;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LXa0/h;",
            ">;"
        }
    .end annotation
.end field

.field public final c:Lak/b;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LIa1/g$a;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/o;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LE91/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/category/domain/usecases/p;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/category/domain/scenarios/GetItemCategoryPagesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/d;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lu81/b;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le81/d;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJT/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/b;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LIa1/g$a;->d:LIa1/g$a;

    .line 4
    iput-object p7, p0, LIa1/g$a;->a:LTZ0/a;

    move-object/from16 v0, p36

    .line 5
    iput-object v0, p0, LIa1/g$a;->b:LzX0/k;

    .line 6
    iput-object p3, p0, LIa1/g$a;->c:Lak/b;

    .line 7
    invoke-virtual/range {p0 .. p38}, LIa1/g$a;->b(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V

    .line 8
    invoke-virtual/range {p0 .. p38}, LIa1/g$a;->c(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V

    .line 9
    invoke-virtual/range {p0 .. p38}, LIa1/g$a;->d(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;LIa1/h;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p38}, LIa1/g$a;-><init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LIa1/g$a;->e(Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;)Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V
    .locals 5

    .line 1
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LIa1/g$a;->e:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LIa1/g$a;->f:Ldagger/internal/h;

    .line 12
    .line 13
    new-instance p3, LIa1/g$a$j;

    .line 14
    .line 15
    invoke-direct {p3, p2}, LIa1/g$a$j;-><init>(LN91/e;)V

    .line 16
    .line 17
    .line 18
    iput-object p3, p0, LIa1/g$a;->g:Ldagger/internal/h;

    .line 19
    .line 20
    new-instance p3, LIa1/g$a$c;

    .line 21
    .line 22
    invoke-direct {p3, p2}, LIa1/g$a$c;-><init>(LN91/e;)V

    .line 23
    .line 24
    .line 25
    iput-object p3, p0, LIa1/g$a;->h:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/category/domain/usecases/q;->a(LBc/a;)Lorg/xplatform/aggregator/impl/category/domain/usecases/q;

    .line 28
    .line 29
    .line 30
    move-result-object p3

    .line 31
    iput-object p3, p0, LIa1/g$a;->i:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static/range {p33 .. p33}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object p3

    .line 37
    iput-object p3, p0, LIa1/g$a;->j:Ldagger/internal/h;

    .line 38
    .line 39
    invoke-static/range {p31 .. p31}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 40
    .line 41
    .line 42
    move-result-object p3

    .line 43
    iput-object p3, p0, LIa1/g$a;->k:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object p5, p0, LIa1/g$a;->g:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object v0, p0, LIa1/g$a;->i:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v1, p0, LIa1/g$a;->j:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {p5, v0, v1, p3}, Lorg/xplatform/aggregator/impl/category/domain/scenarios/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/category/domain/scenarios/a;

    .line 52
    .line 53
    .line 54
    move-result-object p3

    .line 55
    iput-object p3, p0, LIa1/g$a;->l:Ldagger/internal/h;

    .line 56
    .line 57
    new-instance p3, LIa1/g$a$m;

    .line 58
    .line 59
    invoke-direct {p3, p2}, LIa1/g$a$m;-><init>(LN91/e;)V

    .line 60
    .line 61
    .line 62
    iput-object p3, p0, LIa1/g$a;->m:Ldagger/internal/h;

    .line 63
    .line 64
    new-instance p3, LIa1/g$a$a;

    .line 65
    .line 66
    invoke-direct {p3, p2}, LIa1/g$a$a;-><init>(LN91/e;)V

    .line 67
    .line 68
    .line 69
    iput-object p3, p0, LIa1/g$a;->n:Ldagger/internal/h;

    .line 70
    .line 71
    new-instance p3, LIa1/g$a$b;

    .line 72
    .line 73
    invoke-direct {p3, p2}, LIa1/g$a$b;-><init>(LN91/e;)V

    .line 74
    .line 75
    .line 76
    iput-object p3, p0, LIa1/g$a;->o:Ldagger/internal/h;

    .line 77
    .line 78
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/k;->a(LBc/a;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/k;

    .line 79
    .line 80
    .line 81
    move-result-object p3

    .line 82
    iput-object p3, p0, LIa1/g$a;->p:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 85
    .line 86
    .line 87
    move-result-object p3

    .line 88
    iput-object p3, p0, LIa1/g$a;->q:Ldagger/internal/h;

    .line 89
    .line 90
    invoke-static {p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LIa1/g$a;->r:Ldagger/internal/h;

    .line 95
    .line 96
    new-instance p1, LIa1/g$a$i;

    .line 97
    .line 98
    invoke-direct {p1, p2}, LIa1/g$a$i;-><init>(LN91/e;)V

    .line 99
    .line 100
    .line 101
    iput-object p1, p0, LIa1/g$a;->s:Ldagger/internal/h;

    .line 102
    .line 103
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iput-object p1, p0, LIa1/g$a;->t:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LIa1/g$a;->u:Ldagger/internal/h;

    .line 114
    .line 115
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iput-object p1, p0, LIa1/g$a;->v:Ldagger/internal/h;

    .line 120
    .line 121
    new-instance p1, LIa1/g$a$n;

    .line 122
    .line 123
    invoke-direct {p1, p4}, LIa1/g$a$n;-><init>(Lak/a;)V

    .line 124
    .line 125
    .line 126
    iput-object p1, p0, LIa1/g$a;->w:Ldagger/internal/h;

    .line 127
    .line 128
    new-instance p1, LIa1/g$a$d;

    .line 129
    .line 130
    invoke-direct {p1, p2}, LIa1/g$a$d;-><init>(LN91/e;)V

    .line 131
    .line 132
    .line 133
    iput-object p1, p0, LIa1/g$a;->x:Ldagger/internal/h;

    .line 134
    .line 135
    new-instance p1, LIa1/g$a$l;

    .line 136
    .line 137
    invoke-direct {p1, p4}, LIa1/g$a$l;-><init>(Lak/a;)V

    .line 138
    .line 139
    .line 140
    iput-object p1, p0, LIa1/g$a;->y:Ldagger/internal/h;

    .line 141
    .line 142
    new-instance p1, LIa1/g$a$t;

    .line 143
    .line 144
    invoke-direct {p1, p4}, LIa1/g$a$t;-><init>(Lak/a;)V

    .line 145
    .line 146
    .line 147
    iput-object p1, p0, LIa1/g$a;->z:Ldagger/internal/h;

    .line 148
    .line 149
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    iput-object p1, p0, LIa1/g$a;->A:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object p2, p0, LIa1/g$a;->r:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object p3, p0, LIa1/g$a;->s:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p4, p0, LIa1/g$a;->t:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object p5, p0, LIa1/g$a;->u:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object v0, p0, LIa1/g$a;->v:Ldagger/internal/h;

    .line 164
    .line 165
    iget-object v1, p0, LIa1/g$a;->w:Ldagger/internal/h;

    .line 166
    .line 167
    iget-object v2, p0, LIa1/g$a;->x:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object v3, p0, LIa1/g$a;->y:Ldagger/internal/h;

    .line 170
    .line 171
    iget-object v4, p0, LIa1/g$a;->z:Ldagger/internal/h;

    .line 172
    .line 173
    move-object/from16 p11, p1

    .line 174
    .line 175
    move-object p6, v0

    .line 176
    move-object p7, v1

    .line 177
    move-object p8, v2

    .line 178
    move-object p9, v3

    .line 179
    move-object p10, v4

    .line 180
    invoke-static/range {p2 .. p11}, Lorg/xplatform/aggregator/impl/core/presentation/C;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/core/presentation/C;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    invoke-static {p1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 185
    .line 186
    .line 187
    move-result-object p1

    .line 188
    iput-object p1, p0, LIa1/g$a;->B:Ldagger/internal/h;

    .line 189
    .line 190
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    iput-object p1, p0, LIa1/g$a;->C:Ldagger/internal/h;

    .line 195
    .line 196
    return-void
.end method

.method public final c(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V
    .locals 39

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    move-object/from16 v2, p4

    .line 6
    .line 7
    move-object/from16 v3, p5

    .line 8
    .line 9
    invoke-static/range {p35 .. p35}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 10
    .line 11
    .line 12
    move-result-object v4

    .line 13
    iput-object v4, v0, LIa1/g$a;->D:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {v4}, Lp9/d;->a(LBc/a;)Lp9/d;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    iput-object v4, v0, LIa1/g$a;->E:Ldagger/internal/h;

    .line 20
    .line 21
    invoke-static/range {p26 .. p26}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    iput-object v4, v0, LIa1/g$a;->F:Ldagger/internal/h;

    .line 26
    .line 27
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 28
    .line 29
    .line 30
    move-result-object v4

    .line 31
    iput-object v4, v0, LIa1/g$a;->G:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object v4

    .line 37
    iput-object v4, v0, LIa1/g$a;->H:Ldagger/internal/h;

    .line 38
    .line 39
    new-instance v4, LIa1/g$a$h;

    .line 40
    .line 41
    invoke-direct {v4, v1}, LIa1/g$a$h;-><init>(LN91/e;)V

    .line 42
    .line 43
    .line 44
    iput-object v4, v0, LIa1/g$a;->I:Ldagger/internal/h;

    .line 45
    .line 46
    new-instance v4, LIa1/g$a$f;

    .line 47
    .line 48
    move-object/from16 v5, p1

    .line 49
    .line 50
    invoke-direct {v4, v5}, LIa1/g$a$f;-><init>(LQW0/c;)V

    .line 51
    .line 52
    .line 53
    iput-object v4, v0, LIa1/g$a;->J:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 56
    .line 57
    .line 58
    move-result-object v4

    .line 59
    iput-object v4, v0, LIa1/g$a;->K:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static/range {p38 .. p38}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    iput-object v4, v0, LIa1/g$a;->L:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v4, v0, LIa1/g$a;->D:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {v4}, Lp9/h;->a(LBc/a;)Lp9/h;

    .line 70
    .line 71
    .line 72
    move-result-object v4

    .line 73
    iput-object v4, v0, LIa1/g$a;->M:Ldagger/internal/h;

    .line 74
    .line 75
    new-instance v4, LIa1/g$a$g;

    .line 76
    .line 77
    invoke-direct {v4, v3}, LIa1/g$a$g;-><init>(Lz81/a;)V

    .line 78
    .line 79
    .line 80
    iput-object v4, v0, LIa1/g$a;->N:Ldagger/internal/h;

    .line 81
    .line 82
    new-instance v4, LIa1/g$a$o;

    .line 83
    .line 84
    invoke-direct {v4, v3}, LIa1/g$a$o;-><init>(Lz81/a;)V

    .line 85
    .line 86
    .line 87
    iput-object v4, v0, LIa1/g$a;->O:Ldagger/internal/h;

    .line 88
    .line 89
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 90
    .line 91
    .line 92
    move-result-object v3

    .line 93
    iput-object v3, v0, LIa1/g$a;->P:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    iput-object v3, v0, LIa1/g$a;->Q:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    iput-object v3, v0, LIa1/g$a;->R:Ldagger/internal/h;

    .line 106
    .line 107
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 108
    .line 109
    .line 110
    move-result-object v3

    .line 111
    iput-object v3, v0, LIa1/g$a;->S:Ldagger/internal/h;

    .line 112
    .line 113
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 114
    .line 115
    .line 116
    move-result-object v3

    .line 117
    iput-object v3, v0, LIa1/g$a;->T:Ldagger/internal/h;

    .line 118
    .line 119
    new-instance v3, LIa1/g$a$r;

    .line 120
    .line 121
    invoke-direct {v3, v2}, LIa1/g$a$r;-><init>(Lak/a;)V

    .line 122
    .line 123
    .line 124
    iput-object v3, v0, LIa1/g$a;->U:Ldagger/internal/h;

    .line 125
    .line 126
    new-instance v3, LIa1/g$a$q;

    .line 127
    .line 128
    invoke-direct {v3, v2}, LIa1/g$a$q;-><init>(Lak/a;)V

    .line 129
    .line 130
    .line 131
    iput-object v3, v0, LIa1/g$a;->V:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static/range {p37 .. p37}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 134
    .line 135
    .line 136
    move-result-object v3

    .line 137
    iput-object v3, v0, LIa1/g$a;->W:Ldagger/internal/h;

    .line 138
    .line 139
    new-instance v3, LIa1/g$a$k;

    .line 140
    .line 141
    invoke-direct {v3, v2}, LIa1/g$a$k;-><init>(Lak/a;)V

    .line 142
    .line 143
    .line 144
    iput-object v3, v0, LIa1/g$a;->X:Ldagger/internal/h;

    .line 145
    .line 146
    iget-object v4, v0, LIa1/g$a;->e:Ldagger/internal/h;

    .line 147
    .line 148
    iget-object v5, v0, LIa1/g$a;->f:Ldagger/internal/h;

    .line 149
    .line 150
    iget-object v6, v0, LIa1/g$a;->l:Ldagger/internal/h;

    .line 151
    .line 152
    iget-object v7, v0, LIa1/g$a;->m:Ldagger/internal/h;

    .line 153
    .line 154
    iget-object v8, v0, LIa1/g$a;->n:Ldagger/internal/h;

    .line 155
    .line 156
    iget-object v9, v0, LIa1/g$a;->p:Ldagger/internal/h;

    .line 157
    .line 158
    iget-object v10, v0, LIa1/g$a;->q:Ldagger/internal/h;

    .line 159
    .line 160
    iget-object v11, v0, LIa1/g$a;->B:Ldagger/internal/h;

    .line 161
    .line 162
    iget-object v12, v0, LIa1/g$a;->C:Ldagger/internal/h;

    .line 163
    .line 164
    iget-object v13, v0, LIa1/g$a;->u:Ldagger/internal/h;

    .line 165
    .line 166
    iget-object v14, v0, LIa1/g$a;->E:Ldagger/internal/h;

    .line 167
    .line 168
    iget-object v15, v0, LIa1/g$a;->F:Ldagger/internal/h;

    .line 169
    .line 170
    iget-object v2, v0, LIa1/g$a;->j:Ldagger/internal/h;

    .line 171
    .line 172
    move-object/from16 v16, v2

    .line 173
    .line 174
    iget-object v2, v0, LIa1/g$a;->G:Ldagger/internal/h;

    .line 175
    .line 176
    move-object/from16 v17, v2

    .line 177
    .line 178
    iget-object v2, v0, LIa1/g$a;->H:Ldagger/internal/h;

    .line 179
    .line 180
    move-object/from16 v18, v2

    .line 181
    .line 182
    iget-object v2, v0, LIa1/g$a;->I:Ldagger/internal/h;

    .line 183
    .line 184
    move-object/from16 v19, v2

    .line 185
    .line 186
    iget-object v2, v0, LIa1/g$a;->J:Ldagger/internal/h;

    .line 187
    .line 188
    move-object/from16 v20, v2

    .line 189
    .line 190
    iget-object v2, v0, LIa1/g$a;->K:Ldagger/internal/h;

    .line 191
    .line 192
    move-object/from16 v21, v2

    .line 193
    .line 194
    iget-object v2, v0, LIa1/g$a;->L:Ldagger/internal/h;

    .line 195
    .line 196
    move-object/from16 v22, v2

    .line 197
    .line 198
    iget-object v2, v0, LIa1/g$a;->M:Ldagger/internal/h;

    .line 199
    .line 200
    move-object/from16 v23, v2

    .line 201
    .line 202
    iget-object v2, v0, LIa1/g$a;->N:Ldagger/internal/h;

    .line 203
    .line 204
    move-object/from16 v24, v2

    .line 205
    .line 206
    iget-object v2, v0, LIa1/g$a;->O:Ldagger/internal/h;

    .line 207
    .line 208
    move-object/from16 v25, v2

    .line 209
    .line 210
    iget-object v2, v0, LIa1/g$a;->P:Ldagger/internal/h;

    .line 211
    .line 212
    move-object/from16 v26, v2

    .line 213
    .line 214
    iget-object v2, v0, LIa1/g$a;->v:Ldagger/internal/h;

    .line 215
    .line 216
    move-object/from16 v27, v2

    .line 217
    .line 218
    iget-object v2, v0, LIa1/g$a;->w:Ldagger/internal/h;

    .line 219
    .line 220
    move-object/from16 v28, v2

    .line 221
    .line 222
    iget-object v2, v0, LIa1/g$a;->Q:Ldagger/internal/h;

    .line 223
    .line 224
    move-object/from16 v29, v2

    .line 225
    .line 226
    iget-object v2, v0, LIa1/g$a;->R:Ldagger/internal/h;

    .line 227
    .line 228
    move-object/from16 v30, v2

    .line 229
    .line 230
    iget-object v2, v0, LIa1/g$a;->S:Ldagger/internal/h;

    .line 231
    .line 232
    move-object/from16 v31, v2

    .line 233
    .line 234
    iget-object v2, v0, LIa1/g$a;->T:Ldagger/internal/h;

    .line 235
    .line 236
    move-object/from16 v32, v2

    .line 237
    .line 238
    iget-object v2, v0, LIa1/g$a;->U:Ldagger/internal/h;

    .line 239
    .line 240
    move-object/from16 v34, v2

    .line 241
    .line 242
    iget-object v2, v0, LIa1/g$a;->V:Ldagger/internal/h;

    .line 243
    .line 244
    move-object/from16 v35, v2

    .line 245
    .line 246
    iget-object v2, v0, LIa1/g$a;->W:Ldagger/internal/h;

    .line 247
    .line 248
    move-object/from16 v36, v2

    .line 249
    .line 250
    iget-object v2, v0, LIa1/g$a;->z:Ldagger/internal/h;

    .line 251
    .line 252
    move-object/from16 v33, v16

    .line 253
    .line 254
    move-object/from16 v37, v2

    .line 255
    .line 256
    move-object/from16 v38, v3

    .line 257
    .line 258
    invoke-static/range {v4 .. v38}, Lorg/xplatform/aggregator/impl/publishers/games/m;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/publishers/games/m;

    .line 259
    .line 260
    .line 261
    move-result-object v2

    .line 262
    iput-object v2, v0, LIa1/g$a;->Y:Ldagger/internal/h;

    .line 263
    .line 264
    new-instance v2, LIa1/g$a$e;

    .line 265
    .line 266
    invoke-direct {v2, v1}, LIa1/g$a$e;-><init>(LN91/e;)V

    .line 267
    .line 268
    .line 269
    iput-object v2, v0, LIa1/g$a;->Z:Ldagger/internal/h;

    .line 270
    .line 271
    new-instance v2, LIa1/g$a$p;

    .line 272
    .line 273
    invoke-direct {v2, v1}, LIa1/g$a$p;-><init>(LN91/e;)V

    .line 274
    .line 275
    .line 276
    iput-object v2, v0, LIa1/g$a;->a0:Ldagger/internal/h;

    .line 277
    .line 278
    new-instance v1, LIa1/g$a$s;

    .line 279
    .line 280
    move-object/from16 v2, p6

    .line 281
    .line 282
    invoke-direct {v1, v2}, LIa1/g$a$s;-><init>(LWa0/a;)V

    .line 283
    .line 284
    .line 285
    iput-object v1, v0, LIa1/g$a;->b0:Ldagger/internal/h;

    .line 286
    .line 287
    return-void
.end method

.method public final d(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LWa0/a;LTZ0/a;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LJT/a;Lf8/g;LfX/b;Lcom/xbet/onexuser/domain/user/c;Lkc1/c;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lej0/d;Lorg/xbet/ui_common/utils/internet/a;LP91/b;Lc81/c;LxX0/a;LGg/a;Lorg/xbet/analytics/domain/scope/I;Ljava/lang/Long;Ljava/lang/Long;Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;Lorg/xbet/ui_common/utils/M;LSX0/c;LwX0/a;LHX0/e;Lau/a;Li8/j;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;Lo9/a;LzX0/k;Lgk0/a;LnR/a;)V
    .locals 5

    .line 1
    iget-object p1, p0, LIa1/g$a;->M:Ldagger/internal/h;

    .line 2
    .line 3
    iget-object p2, p0, LIa1/g$a;->F:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object p3, p0, LIa1/g$a;->J:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object p4, p0, LIa1/g$a;->x:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object p5, p0, LIa1/g$a;->Z:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object v0, p0, LIa1/g$a;->a0:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v1, p0, LIa1/g$a;->w:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object v2, p0, LIa1/g$a;->y:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v3, p0, LIa1/g$a;->z:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v4, p0, LIa1/g$a;->b0:Ldagger/internal/h;

    .line 20
    .line 21
    move-object p6, v0

    .line 22
    move-object p7, v1

    .line 23
    move-object p8, v2

    .line 24
    move-object p9, v3

    .line 25
    move-object p10, v4

    .line 26
    invoke-static/range {p1 .. p10}, Lorg/xplatform/aggregator/impl/base/presentation/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/base/presentation/a;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LIa1/g$a;->c0:Ldagger/internal/h;

    .line 31
    .line 32
    return-void
.end method

.method public final e(Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;)Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LIa1/g$a;->a:LTZ0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->a(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LTZ0/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LIa1/g$a;->b:LzX0/k;

    .line 7
    .line 8
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->c(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LzX0/k;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, LIa1/g$a;->c:Lak/b;

    .line 12
    .line 13
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lck/a;

    .line 22
    .line 23
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->b(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lck/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, LIa1/g$a;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/publishers/games/h;->a(Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 31
    .line 32
    .line 33
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xplatform/aggregator/impl/publishers/games/AggregatorPublisherGamesViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LIa1/g$a;->Y:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LIa1/g$a;->c0:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LIa1/g$a;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
