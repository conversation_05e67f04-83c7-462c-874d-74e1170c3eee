.class final Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.promo.domain.usecases.GetPromoGiftsUseCase$invoke$2$1"
    f = "GetPromoGiftsUseCase.kt"
    l = {
        0x29,
        0x2a,
        0x2b
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Ljava/lang/String;",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lg81/h;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "",
        "token",
        "",
        "userId",
        "Lg81/h;",
        "<anonymous>",
        "(Ljava/lang/String;J)Lg81/h;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $$this$coroutineScope:Lkotlinx/coroutines/N;

.field final synthetic $currentAccountId:J

.field final synthetic $onlyActive:Z

.field synthetic J$0:J

.field synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;


# direct methods
.method public constructor <init>(JLkotlinx/coroutines/N;Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlinx/coroutines/N;",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;",
            ">;)V"
        }
    .end annotation

    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$currentAccountId:J

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$$this$coroutineScope:Lkotlinx/coroutines/N;

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iput-boolean p5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$onlyActive:Z

    const/4 p1, 0x3

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, v0, v1, p3}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/h;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$currentAccountId:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$$this$coroutineScope:Lkotlinx/coroutines/N;

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iget-boolean v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$onlyActive:Z

    move-object v6, p4

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;-><init>(JLkotlinx/coroutines/N;Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;ZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    iput-wide p2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->J$0:J

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_3

    .line 13
    .line 14
    if-eq v2, v5, :cond_2

    .line 15
    .line 16
    if-eq v2, v4, :cond_1

    .line 17
    .line 18
    if-ne v2, v3, :cond_0

    .line 19
    .line 20
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 21
    .line 22
    check-cast v1, Lg81/e;

    .line 23
    .line 24
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v2, Lg81/e;

    .line 27
    .line 28
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    move-object/from16 v3, p1

    .line 32
    .line 33
    goto/16 :goto_3

    .line 34
    .line 35
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 36
    .line 37
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 38
    .line 39
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    throw v1

    .line 43
    :cond_1
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v2, Lg81/e;

    .line 46
    .line 47
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v4, Lkotlinx/coroutines/T;

    .line 50
    .line 51
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    move-object v6, v4

    .line 55
    move-object/from16 v4, p1

    .line 56
    .line 57
    goto/16 :goto_1

    .line 58
    .line 59
    :cond_2
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v2, Lkotlinx/coroutines/T;

    .line 62
    .line 63
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 64
    .line 65
    check-cast v5, Lkotlinx/coroutines/T;

    .line 66
    .line 67
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    move-object v6, v2

    .line 71
    move-object/from16 v2, p1

    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 75
    .line 76
    .line 77
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 78
    .line 79
    move-object v8, v2

    .line 80
    check-cast v8, Ljava/lang/String;

    .line 81
    .line 82
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->J$0:J

    .line 83
    .line 84
    iget-wide v9, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$currentAccountId:J

    .line 85
    .line 86
    const-wide/16 v11, 0x0

    .line 87
    .line 88
    cmp-long v2, v9, v11

    .line 89
    .line 90
    if-nez v2, :cond_4

    .line 91
    .line 92
    move-wide v9, v6

    .line 93
    :cond_4
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$$this$coroutineScope:Lkotlinx/coroutines/N;

    .line 94
    .line 95
    new-instance v14, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;

    .line 96
    .line 97
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 98
    .line 99
    const/4 v11, 0x0

    .line 100
    move-object v6, v14

    .line 101
    invoke-direct/range {v6 .. v11}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$bonusesDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)V

    .line 102
    .line 103
    .line 104
    const/4 v15, 0x3

    .line 105
    const/16 v16, 0x0

    .line 106
    .line 107
    const/4 v12, 0x0

    .line 108
    const/4 v13, 0x0

    .line 109
    move-object v11, v2

    .line 110
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$$this$coroutineScope:Lkotlinx/coroutines/N;

    .line 115
    .line 116
    new-instance v14, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;

    .line 117
    .line 118
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 119
    .line 120
    iget-boolean v11, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$onlyActive:Z

    .line 121
    .line 122
    move-object v6, v14

    .line 123
    invoke-direct/range {v6 .. v12}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JZLkotlin/coroutines/e;)V

    .line 124
    .line 125
    .line 126
    move-object v11, v13

    .line 127
    const/4 v13, 0x0

    .line 128
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 129
    .line 130
    .line 131
    move-result-object v12

    .line 132
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->$$this$coroutineScope:Lkotlinx/coroutines/N;

    .line 133
    .line 134
    new-instance v16, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$activeBonusDeferred$1;

    .line 135
    .line 136
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 137
    .line 138
    const/4 v11, 0x0

    .line 139
    move-object/from16 v6, v16

    .line 140
    .line 141
    invoke-direct/range {v6 .. v11}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$activeBonusDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JLkotlin/coroutines/e;)V

    .line 142
    .line 143
    .line 144
    const/16 v17, 0x3

    .line 145
    .line 146
    const/16 v18, 0x0

    .line 147
    .line 148
    const/4 v14, 0x0

    .line 149
    const/4 v15, 0x0

    .line 150
    invoke-static/range {v13 .. v18}, Lkotlinx/coroutines/h;->b(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/T;

    .line 151
    .line 152
    .line 153
    move-result-object v6

    .line 154
    iput-object v12, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 155
    .line 156
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 157
    .line 158
    iput v5, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->label:I

    .line 159
    .line 160
    invoke-interface {v2, v0}, Lkotlinx/coroutines/T;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    if-ne v2, v1, :cond_5

    .line 165
    .line 166
    goto :goto_2

    .line 167
    :cond_5
    move-object v5, v12

    .line 168
    :goto_0
    check-cast v2, Lg81/e;

    .line 169
    .line 170
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 171
    .line 172
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 173
    .line 174
    iput v4, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->label:I

    .line 175
    .line 176
    invoke-interface {v5, v0}, Lkotlinx/coroutines/T;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 177
    .line 178
    .line 179
    move-result-object v4

    .line 180
    if-ne v4, v1, :cond_6

    .line 181
    .line 182
    goto :goto_2

    .line 183
    :cond_6
    :goto_1
    check-cast v4, Lg81/e;

    .line 184
    .line 185
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$0:Ljava/lang/Object;

    .line 186
    .line 187
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->L$1:Ljava/lang/Object;

    .line 188
    .line 189
    iput v3, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->label:I

    .line 190
    .line 191
    invoke-interface {v6, v0}, Lkotlinx/coroutines/T;->b(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 192
    .line 193
    .line 194
    move-result-object v3

    .line 195
    if-ne v3, v1, :cond_7

    .line 196
    .line 197
    :goto_2
    return-object v1

    .line 198
    :cond_7
    move-object v1, v4

    .line 199
    :goto_3
    check-cast v3, Lg81/a;

    .line 200
    .line 201
    new-instance v4, Lg81/h;

    .line 202
    .line 203
    invoke-direct {v4, v2, v1, v3}, Lg81/h;-><init>(Lg81/e;Lg81/e;Lg81/a;)V

    .line 204
    .line 205
    .line 206
    return-object v4
.end method
