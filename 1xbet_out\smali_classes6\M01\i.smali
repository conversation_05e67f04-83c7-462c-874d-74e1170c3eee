.class public final synthetic LM01/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;

.field public final synthetic b:L<PERSON>lin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LM01/i;->a:Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;

    iput-object p2, p0, LM01/i;->b:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LM01/i;->a:Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;

    iget-object v1, p0, LM01/i;->b:<PERSON><PERSON><PERSON>/jvm/functions/Function0;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;->a(Lorg/xbet/uikit/components/toolbar/base/components/TitleWithChevron;Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method
