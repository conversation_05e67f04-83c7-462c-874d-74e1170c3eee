.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/bottom/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002:\u0001#B\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000c\u001a\u00020\u000b2\u0008\u0010\n\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u000c\u001a\u00020\u000b2\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u000c\u0010\u0010J\u001b\u0010\u0014\u001a\u00020\u000b2\u000c\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001d\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\r\u0010\u001b\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ!\u0010 \u001a\u00020\u000b2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00160\u001d\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "text",
        "",
        "setInfoTitle",
        "(Ljava/lang/CharSequence;)V",
        "",
        "resId",
        "(I)V",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;",
        "infoList",
        "setInformationLines",
        "(Ljava/util/List;)V",
        "",
        "expanded",
        "animate",
        "y",
        "(ZZ)Z",
        "z",
        "()Z",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setAccordionClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "LC31/g;",
        "a",
        "LC31/g;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/g;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/g;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 5
    new-instance p1, Lorg/xbet/uikit_sport/eventcard/bottom/e;

    invoke-direct {p1, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/e;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;)V

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->setAccordionClickListener(Lkotlin/jvm/functions/Function1;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic s(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->w(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic t(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;Landroid/view/View;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->v(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;Landroid/view/View;)Z

    move-result p0

    return p0
.end method

.method public static synthetic u(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->x(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V

    return-void
.end method

.method public static final v(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;Landroid/view/View;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->z()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final w(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final x(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final setAccordionClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 2
    .line 3
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    new-instance v1, Lorg/xbet/uikit_sport/eventcard/bottom/f;

    .line 6
    .line 7
    invoke-direct {v1, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/f;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 14
    .line 15
    iget-object v0, v0, LC31/g;->d:Landroid/widget/TextView;

    .line 16
    .line 17
    new-instance v1, Lorg/xbet/uikit_sport/eventcard/bottom/g;

    .line 18
    .line 19
    invoke-direct {v1, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/g;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public final setInfoTitle(I)V
    .locals 1

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->setInfoTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoTitle(Ljava/lang/CharSequence;)V
    .locals 4

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    .line 1
    :goto_0
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    invoke-virtual {v3}, LC31/g;->getRoot()Landroid/view/View;

    move-result-object v3

    .line 2
    invoke-virtual {v3}, Landroid/view/View;->getVisibility()I

    move-result v3

    if-nez v3, :cond_1

    goto :goto_1

    :cond_1
    const/4 v0, 0x0

    :goto_1
    if-eq v0, v2, :cond_3

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    invoke-virtual {v0}, LC31/g;->getRoot()Landroid/view/View;

    move-result-object v0

    if-eqz v2, :cond_2

    goto :goto_2

    :cond_2
    const/16 v1, 0x8

    .line 4
    :goto_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    :cond_3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    iget-object v0, v0, LC31/g;->d:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInformationLines(Ljava/util/List;)V
    .locals 7
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 2
    .line 3
    iget-object v0, v0, LC31/g;->c:Landroid/widget/LinearLayout;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/ViewGroup;->getChildCount()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-static {p1}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-gt v0, v1, :cond_0

    .line 14
    .line 15
    :goto_0
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 16
    .line 17
    iget-object v2, v2, LC31/g;->c:Landroid/widget/LinearLayout;

    .line 18
    .line 19
    new-instance v3, Lorg/xbet/uikit_sport/eventcard/bottom/BottomInfoCell;

    .line 20
    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    const/4 v5, 0x2

    .line 26
    const/4 v6, 0x0

    .line 27
    invoke-direct {v3, v4, v6, v5, v6}, Lorg/xbet/uikit_sport/eventcard/bottom/BottomInfoCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v2, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 31
    .line 32
    .line 33
    if-eq v0, v1, :cond_0

    .line 34
    .line 35
    add-int/lit8 v0, v0, 0x1

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 39
    .line 40
    iget-object v0, v0, LC31/g;->c:Landroid/widget/LinearLayout;

    .line 41
    .line 42
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$b;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$b;

    .line 47
    .line 48
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    invoke-interface {v0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    const/4 v1, 0x0

    .line 57
    const/4 v2, 0x0

    .line 58
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    if-eqz v3, :cond_5

    .line 63
    .line 64
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object v3

    .line 68
    add-int/lit8 v4, v2, 0x1

    .line 69
    .line 70
    if-gez v2, :cond_1

    .line 71
    .line 72
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 73
    .line 74
    .line 75
    :cond_1
    check-cast v3, Lorg/xbet/uikit_sport/eventcard/bottom/BottomInfoCell;

    .line 76
    .line 77
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 78
    .line 79
    .line 80
    move-result v5

    .line 81
    if-ge v2, v5, :cond_2

    .line 82
    .line 83
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v5

    .line 87
    check-cast v5, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;

    .line 88
    .line 89
    invoke-virtual {v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;->b()Ljava/lang/CharSequence;

    .line 90
    .line 91
    .line 92
    move-result-object v5

    .line 93
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v6

    .line 97
    check-cast v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;

    .line 98
    .line 99
    invoke-virtual {v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo$a;->a()Ljava/lang/CharSequence;

    .line 100
    .line 101
    .line 102
    move-result-object v6

    .line 103
    invoke-virtual {v3, v5, v6}, Lorg/xbet/uikit_sport/eventcard/bottom/BottomInfoCell;->setTitle(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 104
    .line 105
    .line 106
    :cond_2
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 107
    .line 108
    .line 109
    move-result v5

    .line 110
    if-ge v2, v5, :cond_3

    .line 111
    .line 112
    const/4 v2, 0x1

    .line 113
    goto :goto_2

    .line 114
    :cond_3
    const/4 v2, 0x0

    .line 115
    :goto_2
    if-eqz v2, :cond_4

    .line 116
    .line 117
    const/4 v2, 0x0

    .line 118
    goto :goto_3

    .line 119
    :cond_4
    const/16 v2, 0x8

    .line 120
    .line 121
    :goto_3
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 122
    .line 123
    .line 124
    move v2, v4

    .line 125
    goto :goto_1

    .line 126
    :cond_5
    return-void
.end method

.method public final y(ZZ)Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 2
    .line 3
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(ZZ)V

    .line 6
    .line 7
    .line 8
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 9
    .line 10
    iget-object p2, p2, LC31/g;->c:Landroid/widget/LinearLayout;

    .line 11
    .line 12
    if-eqz p1, :cond_0

    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/16 v0, 0x8

    .line 17
    .line 18
    :goto_0
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 22
    .line 23
    iget-object p2, p2, LC31/g;->d:Landroid/widget/TextView;

    .line 24
    .line 25
    const/4 v0, 0x2

    .line 26
    const/4 v1, 0x0

    .line 27
    if-eqz p1, :cond_1

    .line 28
    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    sget v2, LlZ0/d;->uikitSecondary:I

    .line 34
    .line 35
    invoke-static {p1, v2, v1, v0, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    goto :goto_1

    .line 40
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    sget v2, LlZ0/d;->uikitPrimary:I

    .line 45
    .line 46
    invoke-static {p1, v2, v1, v0, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    :goto_1
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 54
    .line 55
    iget-object p1, p1, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 56
    .line 57
    invoke-virtual {p1}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    return p1
.end method

.method public final z()Z
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 2
    .line 3
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    xor-int/lit8 v1, v1, 0x1

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(Z)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 15
    .line 16
    iget-object v1, v0, LC31/g;->c:Landroid/widget/LinearLayout;

    .line 17
    .line 18
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 19
    .line 20
    invoke-virtual {v0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    const/4 v0, 0x0

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    const/16 v0, 0x8

    .line 29
    .line 30
    :goto_0
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 31
    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 34
    .line 35
    iget-object v1, v0, LC31/g;->d:Landroid/widget/TextView;

    .line 36
    .line 37
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 38
    .line 39
    invoke-virtual {v0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    const/4 v2, 0x2

    .line 44
    const/4 v3, 0x0

    .line 45
    if-eqz v0, :cond_1

    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    sget v4, LlZ0/d;->uikitSecondary:I

    .line 52
    .line 53
    invoke-static {v0, v4, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    goto :goto_1

    .line 58
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    sget v4, LlZ0/d;->uikitPrimary:I

    .line 63
    .line 64
    invoke-static {v0, v4, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    :goto_1
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setTextColor(I)V

    .line 69
    .line 70
    .line 71
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->a:LC31/g;

    .line 72
    .line 73
    iget-object v0, v0, LC31/g;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 74
    .line 75
    invoke-virtual {v0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 76
    .line 77
    .line 78
    move-result v0

    .line 79
    return v0
.end method
