.class public final synthetic Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/j;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/j;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    invoke-static {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->F2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;

    move-result-object v0

    return-object v0
.end method
