.class public final LnO0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lr4/d;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LnO0/a;",
        "Lr4/d;",
        "",
        "gameId",
        "",
        "sportId",
        "<init>",
        "(Ljava/lang/String;J)V",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "a",
        "Ljava/lang/String;",
        "b",
        "J",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:J


# direct methods
.method public constructor <init>(Ljava/lang/String;J)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LnO0/a;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-wide p2, p0, LnO0/a;->b:J

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 3
    .param p1    # Landroidx/fragment/app/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object p1, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;->x1:Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment$a;

    .line 2
    .line 3
    iget-object v0, p0, LnO0/a;->a:Ljava/lang/String;

    .line 4
    .line 5
    iget-wide v1, p0, LnO0/a;->b:J

    .line 6
    .line 7
    invoke-virtual {p1, v0, v1, v2}, Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment$a;->a(Ljava/lang/String;J)Lorg/xbet/statistic/team/impl/team_characterstic_statistic/presentation/fragments/TeamCharacteristicsStatisticFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method

.method public getClearContainer()Z
    .locals 1

    .line 1
    invoke-static {p0}, Lr4/d$b;->a(Lr4/d;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public getScreenKey()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {p0}, Lr4/d$b;->b(Lr4/d;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
