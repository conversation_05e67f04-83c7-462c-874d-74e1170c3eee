.class public LHN0/d;
.super LZY0/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LHN0/d$a;,
        LHN0/d$b;,
        LHN0/d$c;,
        LHN0/d$d;,
        LHN0/d$e;,
        LHN0/d$f;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010%\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0017\u0018\u0000 12\u00020\u0001:\u00059>\u0013\u0011;B%\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0014\u0008\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000c\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\'\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001f\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001f\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u000eH\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001b\u0010\u001f\u001a\u00020\u00062\u000c\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001d0\u001c\u00a2\u0006\u0004\u0008\u001f\u0010 J\u001b\u0010#\u001a\u00020\u00062\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020!0\u001c\u00a2\u0006\u0004\u0008#\u0010 J!\u0010&\u001a\u00020\u00062\u0012\u0010%\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020$0\u001c0\u001c\u00a2\u0006\u0004\u0008&\u0010 J\'\u0010*\u001a\u00020\u00062\u0006\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020\u001d2\u0006\u0010)\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008*\u0010+J\u001f\u0010.\u001a\u00020\u00062\u0006\u0010,\u001a\u00020\n2\u0006\u0010)\u001a\u00020-H\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u001f\u00101\u001a\u00020\u00062\u0006\u0010,\u001a\u00020\n2\u0006\u0010)\u001a\u000200H\u0002\u00a2\u0006\u0004\u00081\u00102J\u0017\u00104\u001a\u00020\u00062\u0006\u0010)\u001a\u000203H\u0002\u00a2\u0006\u0004\u00084\u00105J\'\u00107\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010)\u001a\u000206H\u0002\u00a2\u0006\u0004\u00087\u00108R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u001c\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001d0\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010=R\u001c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020!0\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008>\u0010=R\"\u0010?\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020$0\u001c0\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010=R \u0010B\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n0@8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010AR\u0014\u0010E\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010DR\u0014\u0010H\u001a\u00020F8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010GR\u0014\u0010J\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010IR\u0014\u0010K\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u0010IR\u0014\u0010M\u001a\u00020\n8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008;\u0010LR\u0014\u0010N\u001a\u00020\n8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008>\u0010LR\u0014\u0010O\u001a\u00020\n8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u00089\u0010L\u00a8\u0006P"
    }
    d2 = {
        "LHN0/d;",
        "LZY0/b;",
        "Landroid/content/Context;",
        "context",
        "Lkotlin/Function1;",
        "",
        "",
        "onGameClick",
        "<init>",
        "(Landroid/content/Context;Lkotlin/jvm/functions/Function1;)V",
        "",
        "column",
        "m",
        "(I)I",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "holder",
        "row",
        "e",
        "(Landroidx/recyclerview/widget/RecyclerView$D;II)V",
        "c",
        "(II)I",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "f",
        "(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;",
        "g",
        "(Landroidx/recyclerview/widget/RecyclerView$D;)V",
        "",
        "LGN0/f;",
        "rowTitles",
        "p",
        "(Ljava/util/List;)V",
        "LaZ0/a;",
        "columnTitles",
        "n",
        "LNN0/f;",
        "data",
        "o",
        "position",
        "item",
        "viewHolder",
        "h",
        "(ILGN0/f;Landroidx/recyclerview/widget/RecyclerView$D;)V",
        "pos",
        "LHN0/d$a;",
        "i",
        "(ILHN0/d$a;)V",
        "LHN0/d$d;",
        "k",
        "(ILHN0/d$d;)V",
        "LHN0/d$e;",
        "l",
        "(LHN0/d$e;)V",
        "LHN0/d$c;",
        "j",
        "(IILHN0/d$c;)V",
        "a",
        "Landroid/content/Context;",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "Ljava/util/List;",
        "d",
        "dataItems",
        "",
        "Ljava/util/Map;",
        "cachedWidthMap",
        "LNN0/f$b;",
        "LNN0/f$b;",
        "emptyItem",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "titlePaint",
        "I",
        "padding",
        "iconColumnWidth",
        "()I",
        "firstColumnWidth",
        "rowCount",
        "columnCount",
        "statistic_core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k:LHN0/d$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final l:I


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "LGN0/f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "LaZ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LNN0/f;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LNN0/f$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:I

.field public final j:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LHN0/d$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LHN0/d$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LHN0/d;->k:LHN0/d$b;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LHN0/d;->l:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lkotlin/jvm/functions/Function1;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, LZY0/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LHN0/d;->a:Landroid/content/Context;

    .line 5
    .line 6
    iput-object p2, p0, LHN0/d;->b:Lkotlin/jvm/functions/Function1;

    .line 7
    .line 8
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 9
    .line 10
    .line 11
    move-result-object p2

    .line 12
    iput-object p2, p0, LHN0/d;->c:Ljava/util/List;

    .line 13
    .line 14
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object p2

    .line 18
    iput-object p2, p0, LHN0/d;->d:Ljava/util/List;

    .line 19
    .line 20
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    iput-object p2, p0, LHN0/d;->e:Ljava/util/List;

    .line 25
    .line 26
    new-instance p2, Ljava/util/LinkedHashMap;

    .line 27
    .line 28
    invoke-direct {p2}, Ljava/util/LinkedHashMap;-><init>()V

    .line 29
    .line 30
    .line 31
    iput-object p2, p0, LHN0/d;->f:Ljava/util/Map;

    .line 32
    .line 33
    new-instance v0, LNN0/f$b;

    .line 34
    .line 35
    sget-object v1, Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;->NO_COLOR:Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;

    .line 36
    .line 37
    const/4 v4, 0x4

    .line 38
    const/4 v5, 0x0

    .line 39
    const-string v2, ""

    .line 40
    .line 41
    const/4 v3, 0x0

    .line 42
    invoke-direct/range {v0 .. v5}, LNN0/f$b;-><init>(Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 43
    .line 44
    .line 45
    iput-object v0, p0, LHN0/d;->g:LNN0/f$b;

    .line 46
    .line 47
    new-instance p2, Landroid/graphics/Paint;

    .line 48
    .line 49
    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 50
    .line 51
    .line 52
    sget v0, Lpb/h;->roboto_regular:I

    .line 53
    .line 54
    invoke-static {p1, v0}, LH0/h;->h(Landroid/content/Context;I)Landroid/graphics/Typeface;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 59
    .line 60
    .line 61
    move-result-object v1

    .line 62
    sget v2, Lpb/f;->text_16:I

    .line 63
    .line 64
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimension(I)F

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    invoke-virtual {p2, v1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 72
    .line 73
    .line 74
    iput-object p2, p0, LHN0/d;->h:Landroid/graphics/Paint;

    .line 75
    .line 76
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 77
    .line 78
    .line 79
    move-result-object p2

    .line 80
    sget v0, Lpb/f;->space_8:I

    .line 81
    .line 82
    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 83
    .line 84
    .line 85
    move-result p2

    .line 86
    iput p2, p0, LHN0/d;->i:I

    .line 87
    .line 88
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    sget p2, LxN0/a;->column_icon_width:I

    .line 93
    .line 94
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    iput p1, p0, LHN0/d;->j:I

    .line 99
    .line 100
    return-void
.end method

.method private final m(I)I
    .locals 3

    .line 1
    iget-object v0, p0, LHN0/d;->f:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    if-nez v2, :cond_4

    .line 12
    .line 13
    iget-object v2, p0, LHN0/d;->d:Ljava/util/List;

    .line 14
    .line 15
    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    check-cast p1, LaZ0/a;

    .line 20
    .line 21
    instance-of v2, p1, LaZ0/a$d;

    .line 22
    .line 23
    if-eqz v2, :cond_0

    .line 24
    .line 25
    iget-object v2, p0, LHN0/d;->a:Landroid/content/Context;

    .line 26
    .line 27
    check-cast p1, LaZ0/a$d;

    .line 28
    .line 29
    invoke-virtual {p1}, LaZ0/a$d;->a()I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    invoke-virtual {v2, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iget-object v2, p0, LHN0/d;->h:Landroid/graphics/Paint;

    .line 38
    .line 39
    invoke-virtual {v2, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    float-to-int p1, p1

    .line 44
    iget v2, p0, LHN0/d;->i:I

    .line 45
    .line 46
    mul-int/lit8 v2, v2, 0x6

    .line 47
    .line 48
    :goto_0
    add-int/2addr p1, v2

    .line 49
    goto :goto_2

    .line 50
    :cond_0
    instance-of v2, p1, LaZ0/a$c;

    .line 51
    .line 52
    if-eqz v2, :cond_1

    .line 53
    .line 54
    iget-object v2, p0, LHN0/d;->h:Landroid/graphics/Paint;

    .line 55
    .line 56
    check-cast p1, LaZ0/a$c;

    .line 57
    .line 58
    invoke-virtual {p1}, LaZ0/a$c;->a()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-virtual {v2, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 63
    .line 64
    .line 65
    move-result p1

    .line 66
    float-to-int p1, p1

    .line 67
    iget v2, p0, LHN0/d;->i:I

    .line 68
    .line 69
    mul-int/lit8 v2, v2, 0x2

    .line 70
    .line 71
    goto :goto_0

    .line 72
    :cond_1
    instance-of v2, p1, LaZ0/a$a;

    .line 73
    .line 74
    if-nez v2, :cond_3

    .line 75
    .line 76
    instance-of p1, p1, LaZ0/a$b;

    .line 77
    .line 78
    if-eqz p1, :cond_2

    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 82
    .line 83
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 84
    .line 85
    .line 86
    throw p1

    .line 87
    :cond_3
    :goto_1
    iget p1, p0, LHN0/d;->j:I

    .line 88
    .line 89
    :goto_2
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    :cond_4
    check-cast v2, Ljava/lang/Number;

    .line 97
    .line 98
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 99
    .line 100
    .line 101
    move-result p1

    .line 102
    return p1
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 1
    iget-object v0, p0, LHN0/d;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public b()I
    .locals 2

    .line 1
    iget-object v0, p0, LHN0/d;->a:Landroid/content/Context;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget v1, LxN0/a;->column_title_width:I

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    return v0
.end method

.method public c(II)I
    .locals 0

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x3

    .line 6
    return p1

    .line 7
    :cond_0
    if-nez p2, :cond_1

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    return p1

    .line 11
    :cond_1
    if-nez p1, :cond_2

    .line 12
    .line 13
    const/4 p1, 0x1

    .line 14
    return p1

    .line 15
    :cond_2
    const/4 p1, 0x2

    .line 16
    return p1
.end method

.method public d()I
    .locals 1

    .line 1
    iget-object v0, p0, LHN0/d;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public e(Landroidx/recyclerview/widget/RecyclerView$D;II)V
    .locals 2
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2, p3}, LHN0/d;->c(II)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_2

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eq v0, v1, :cond_1

    .line 9
    .line 10
    const/4 v1, 0x3

    .line 11
    if-eq v0, v1, :cond_0

    .line 12
    .line 13
    check-cast p1, LHN0/d$c;

    .line 14
    .line 15
    invoke-virtual {p0, p2, p3, p1}, LHN0/d;->j(IILHN0/d$c;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    check-cast p1, LHN0/d$e;

    .line 20
    .line 21
    invoke-virtual {p0, p1}, LHN0/d;->l(LHN0/d$e;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_1
    check-cast p1, LHN0/d$a;

    .line 26
    .line 27
    invoke-virtual {p0, p3, p1}, LHN0/d;->i(ILHN0/d$a;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_2
    check-cast p1, LHN0/d$d;

    .line 32
    .line 33
    invoke-virtual {p0, p2, p1}, LHN0/d;->k(ILHN0/d$d;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public f(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 3
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz p2, :cond_2

    .line 11
    .line 12
    const/4 v2, 0x1

    .line 13
    if-eq p2, v2, :cond_1

    .line 14
    .line 15
    const/4 v2, 0x3

    .line 16
    if-eq p2, v2, :cond_0

    .line 17
    .line 18
    new-instance p2, LHN0/d$c;

    .line 19
    .line 20
    invoke-static {v0, p1, v1}, LDN0/B;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/B;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-direct {p2, p1}, LHN0/d$c;-><init>(LDN0/B;)V

    .line 25
    .line 26
    .line 27
    return-object p2

    .line 28
    :cond_0
    new-instance p2, LHN0/d$e;

    .line 29
    .line 30
    invoke-static {v0, p1, v1}, LDN0/C;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/C;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-direct {p2, p1}, LHN0/d$e;-><init>(LDN0/C;)V

    .line 35
    .line 36
    .line 37
    return-object p2

    .line 38
    :cond_1
    new-instance p2, LHN0/d$a;

    .line 39
    .line 40
    invoke-static {v0, p1, v1}, LDN0/x;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/x;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-direct {p2, p1}, LHN0/d$a;-><init>(LDN0/x;)V

    .line 45
    .line 46
    .line 47
    return-object p2

    .line 48
    :cond_2
    new-instance p2, LHN0/d$d;

    .line 49
    .line 50
    invoke-static {v0, p1, v1}, LDN0/D;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/D;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object v0, p0, LHN0/d;->b:Lkotlin/jvm/functions/Function1;

    .line 55
    .line 56
    invoke-direct {p2, p1, v0}, LHN0/d$d;-><init>(LDN0/D;Lkotlin/jvm/functions/Function1;)V

    .line 57
    .line 58
    .line 59
    return-object p2
.end method

.method public g(Landroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 1
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, LZY0/b;->g(Landroidx/recyclerview/widget/RecyclerView$D;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, LHN0/d$d;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p1, LHN0/d$d;

    .line 9
    .line 10
    invoke-virtual {p1}, LHN0/d$d;->g()V

    .line 11
    .line 12
    .line 13
    :cond_0
    return-void
.end method

.method public h(ILGN0/f;Landroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 6
    .param p2    # LGN0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p2}, LGN0/f;->a()Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    sget-object p2, LHN0/d$f;->a:[I

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    aget p1, p2, p1

    .line 12
    .line 13
    packed-switch p1, :pswitch_data_0

    .line 14
    .line 15
    .line 16
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 17
    .line 18
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 19
    .line 20
    .line 21
    throw p1

    .line 22
    :pswitch_0
    sget-object p1, Lub/b;->a:Lub/b;

    .line 23
    .line 24
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 25
    .line 26
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    sget v0, Lpb/e;->red_30:I

    .line 31
    .line 32
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    goto :goto_0

    .line 37
    :pswitch_1
    sget-object p1, Lub/b;->a:Lub/b;

    .line 38
    .line 39
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 40
    .line 41
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 42
    .line 43
    .line 44
    move-result-object p2

    .line 45
    sget v0, Lpb/e;->red_10:I

    .line 46
    .line 47
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    goto :goto_0

    .line 52
    :pswitch_2
    sget-object p1, Lub/b;->a:Lub/b;

    .line 53
    .line 54
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 55
    .line 56
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    sget v0, Lpb/e;->market_yellow_10:I

    .line 61
    .line 62
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 63
    .line 64
    .line 65
    move-result p1

    .line 66
    goto :goto_0

    .line 67
    :pswitch_3
    sget-object p1, Lub/b;->a:Lub/b;

    .line 68
    .line 69
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 70
    .line 71
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 72
    .line 73
    .line 74
    move-result-object p2

    .line 75
    sget v0, Lpb/e;->market_yellow_30:I

    .line 76
    .line 77
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    goto :goto_0

    .line 82
    :pswitch_4
    sget-object p1, Lub/b;->a:Lub/b;

    .line 83
    .line 84
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 85
    .line 86
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 87
    .line 88
    .line 89
    move-result-object p2

    .line 90
    sget v0, Lpb/e;->green_10:I

    .line 91
    .line 92
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 93
    .line 94
    .line 95
    move-result p1

    .line 96
    goto :goto_0

    .line 97
    :pswitch_5
    sget-object p1, Lub/b;->a:Lub/b;

    .line 98
    .line 99
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 100
    .line 101
    invoke-virtual {p2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 102
    .line 103
    .line 104
    move-result-object p2

    .line 105
    sget v0, Lpb/e;->green_30:I

    .line 106
    .line 107
    invoke-virtual {p1, p2, v0}, Lub/b;->d(Landroid/content/Context;I)I

    .line 108
    .line 109
    .line 110
    move-result p1

    .line 111
    goto :goto_0

    .line 112
    :pswitch_6
    sget-object v0, Lub/b;->a:Lub/b;

    .line 113
    .line 114
    iget-object p1, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 115
    .line 116
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    sget v2, Lpb/c;->contentBackground:I

    .line 121
    .line 122
    const/4 v4, 0x4

    .line 123
    const/4 v5, 0x0

    .line 124
    const/4 v3, 0x0

    .line 125
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 126
    .line 127
    .line 128
    move-result p1

    .line 129
    :goto_0
    iget-object p2, p3, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 130
    .line 131
    invoke-virtual {p2, p1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 132
    .line 133
    .line 134
    return-void

    .line 135
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final i(ILHN0/d$a;)V
    .locals 1

    .line 1
    iget-object v0, p0, LHN0/d;->d:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 p1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, LaZ0/a;

    .line 10
    .line 11
    invoke-direct {p0, p1}, LHN0/d;->m(I)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p2, v0, p1}, LHN0/d$a;->d(LaZ0/a;I)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final j(IILHN0/d$c;)V
    .locals 3

    .line 1
    iget-object v0, p0, LHN0/d;->e:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v1, p1, -0x1

    .line 8
    .line 9
    if-le v0, v1, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, LHN0/d;->e:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    add-int/lit8 v2, p2, -0x1

    .line 24
    .line 25
    if-le v0, v2, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, LHN0/d;->e:Ljava/util/List;

    .line 28
    .line 29
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Ljava/util/List;

    .line 34
    .line 35
    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, LNN0/f;

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    iget-object v0, p0, LHN0/d;->g:LNN0/f$b;

    .line 43
    .line 44
    :goto_0
    add-int/lit8 p2, p2, -0x1

    .line 45
    .line 46
    invoke-direct {p0, p2}, LHN0/d;->m(I)I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    invoke-virtual {p3, v0, p2}, LHN0/d$c;->d(LNN0/f;I)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0, p1, v0, p3}, LHN0/d;->h(ILGN0/f;Landroidx/recyclerview/widget/RecyclerView$D;)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final k(ILHN0/d$d;)V
    .locals 2

    .line 1
    iget-object v0, p0, LHN0/d;->c:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 v1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, LGN0/f;

    .line 10
    .line 11
    invoke-virtual {p2, v0, p1}, LHN0/d$d;->e(LGN0/f;I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, p1, v0, p2}, LHN0/d;->h(ILGN0/f;Landroidx/recyclerview/widget/RecyclerView$D;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final l(LHN0/d$e;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LHN0/d$e;->e()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final n(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LaZ0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LHN0/d;->d:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method

.method public final o(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LNN0/f;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LHN0/d;->e:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method

.method public final p(Ljava/util/List;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LGN0/f;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LHN0/d;->c:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method
