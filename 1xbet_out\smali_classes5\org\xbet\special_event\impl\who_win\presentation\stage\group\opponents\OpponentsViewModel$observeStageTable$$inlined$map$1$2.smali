.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlinx/coroutines/flow/f;

.field public final synthetic b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/flow/f;Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10

    .line 1
    instance-of v0, p2, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto/16 :goto_6

    .line 42
    .line 43
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 44
    .line 45
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 46
    .line 47
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    throw p1

    .line 51
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    iget-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    .line 55
    .line 56
    check-cast p1, LKo0/a;

    .line 57
    .line 58
    instance-of v2, p1, LKo0/a$a;

    .line 59
    .line 60
    if-eqz v2, :cond_4

    .line 61
    .line 62
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 63
    .line 64
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;->r3(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    :cond_3
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    move-object v4, p1

    .line 73
    check-cast v4, LTy0/a;

    .line 74
    .line 75
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 80
    .line 81
    .line 82
    move-result-object v6

    .line 83
    invoke-virtual {v4, v5, v6}, LTy0/a;->a(Ljava/util/List;Ljava/util/List;)LTy0/a;

    .line 84
    .line 85
    .line 86
    move-result-object v4

    .line 87
    invoke-interface {v2, p1, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result p1

    .line 91
    if-eqz p1, :cond_3

    .line 92
    .line 93
    goto/16 :goto_5

    .line 94
    .line 95
    :cond_4
    instance-of v2, p1, LKo0/a$b;

    .line 96
    .line 97
    if-eqz v2, :cond_e

    .line 98
    .line 99
    check-cast p1, LKo0/a$b;

    .line 100
    .line 101
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    check-cast p1, Ljava/lang/Iterable;

    .line 106
    .line 107
    const-class v2, LDy0/a$a;

    .line 108
    .line 109
    invoke-static {p1, v2}, Lkotlin/collections/C;->b0(Ljava/lang/Iterable;Ljava/lang/Class;)Ljava/util/List;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    check-cast p1, LDy0/a$a;

    .line 118
    .line 119
    const/4 v2, 0x0

    .line 120
    if-eqz p1, :cond_7

    .line 121
    .line 122
    invoke-virtual {p1}, LDy0/a$a;->e()Ljava/util/List;

    .line 123
    .line 124
    .line 125
    move-result-object v4

    .line 126
    if-eqz v4, :cond_7

    .line 127
    .line 128
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 129
    .line 130
    .line 131
    move-result-object v4

    .line 132
    :cond_5
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 133
    .line 134
    .line 135
    move-result v5

    .line 136
    if-eqz v5, :cond_6

    .line 137
    .line 138
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object v5

    .line 142
    move-object v6, v5

    .line 143
    check-cast v6, LDy0/a$a$a;

    .line 144
    .line 145
    invoke-virtual {v6}, LDy0/a$a$a;->d()Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object v6

    .line 149
    iget-object v7, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 150
    .line 151
    invoke-static {v7}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;->p3(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 152
    .line 153
    .line 154
    move-result-object v7

    .line 155
    invoke-virtual {v7}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;->b()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v7

    .line 159
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 160
    .line 161
    .line 162
    move-result v6

    .line 163
    if-eqz v6, :cond_5

    .line 164
    .line 165
    goto :goto_1

    .line 166
    :cond_6
    move-object v5, v2

    .line 167
    :goto_1
    check-cast v5, LDy0/a$a$a;

    .line 168
    .line 169
    goto :goto_2

    .line 170
    :cond_7
    move-object v5, v2

    .line 171
    :goto_2
    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 172
    .line 173
    invoke-static {v4}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;->r3(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 174
    .line 175
    .line 176
    move-result-object v4

    .line 177
    :cond_8
    invoke-interface {v4}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 178
    .line 179
    .line 180
    move-result-object v6

    .line 181
    move-object v7, v6

    .line 182
    check-cast v7, LTy0/a;

    .line 183
    .line 184
    if-eqz v5, :cond_9

    .line 185
    .line 186
    invoke-virtual {v5}, LDy0/a$a$a;->c()Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object v8

    .line 190
    goto :goto_3

    .line 191
    :cond_9
    move-object v8, v2

    .line 192
    :goto_3
    if-nez v8, :cond_a

    .line 193
    .line 194
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 195
    .line 196
    .line 197
    move-result-object v8

    .line 198
    :cond_a
    if-eqz p1, :cond_b

    .line 199
    .line 200
    invoke-virtual {p1}, LDy0/a$a;->a()Ljava/util/List;

    .line 201
    .line 202
    .line 203
    move-result-object v9

    .line 204
    goto :goto_4

    .line 205
    :cond_b
    move-object v9, v2

    .line 206
    :goto_4
    if-nez v9, :cond_c

    .line 207
    .line 208
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 209
    .line 210
    .line 211
    move-result-object v9

    .line 212
    :cond_c
    invoke-virtual {v7, v8, v9}, LTy0/a;->a(Ljava/util/List;Ljava/util/List;)LTy0/a;

    .line 213
    .line 214
    .line 215
    move-result-object v7

    .line 216
    invoke-interface {v4, v6, v7}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 217
    .line 218
    .line 219
    move-result v6

    .line 220
    if-eqz v6, :cond_8

    .line 221
    .line 222
    :goto_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 223
    .line 224
    iput v3, v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 225
    .line 226
    invoke-interface {p2, p1, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 227
    .line 228
    .line 229
    move-result-object p1

    .line 230
    if-ne p1, v1, :cond_d

    .line 231
    .line 232
    return-object v1

    .line 233
    :cond_d
    :goto_6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 234
    .line 235
    return-object p1

    .line 236
    :cond_e
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 237
    .line 238
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 239
    .line 240
    .line 241
    throw p1
.end method
