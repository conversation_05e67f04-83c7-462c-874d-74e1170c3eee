.class public final synthetic LNR0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:LNR0/b$a;

.field public final synthetic b:LOR0/b;


# direct methods
.method public synthetic constructor <init>(LNR0/b$a;LOR0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNR0/a;->a:LNR0/b$a;

    iput-object p2, p0, LNR0/a;->b:LOR0/b;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LNR0/a;->a:LNR0/b$a;

    iget-object v1, p0, LNR0/a;->b:LOR0/b;

    invoke-static {v0, v1, p1}, LNR0/b$a;->d(LNR0/b$a;LOR0/b;Landroid/view/View;)V

    return-void
.end method
