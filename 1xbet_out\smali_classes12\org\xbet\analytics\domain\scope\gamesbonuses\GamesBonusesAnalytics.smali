.class public final Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$a;,
        Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\u0018\u0000 \u00102\u00020\u0001:\u0002\u0015\rB\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J%\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001d\u0010\u0010\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0015\u0010\u0012\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u0014\u00a8\u0006\u0016"
    }
    d2 = {
        "Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;",
        "",
        "Lorg/xbet/analytics/domain/b;",
        "analytics",
        "<init>",
        "(Lorg/xbet/analytics/domain/b;)V",
        "",
        "gameId",
        "",
        "filter",
        "Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;",
        "screen",
        "",
        "a",
        "(ILjava/lang/String;Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;)V",
        "",
        "b",
        "(IZ)V",
        "c",
        "(I)V",
        "Lorg/xbet/analytics/domain/b;",
        "Screen",
        "analytics_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b:Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lorg/xbet/analytics/domain/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;->b:Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$a;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/analytics/domain/b;)V
    .locals 0
    .param p1    # Lorg/xbet/analytics/domain/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;->a:Lorg/xbet/analytics/domain/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(ILjava/lang/String;Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;)V
    .locals 3
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const-string v1, "game_id"

    .line 8
    .line 9
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    const-string v1, "filter"

    .line 14
    .line 15
    invoke-static {v1, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    const-string v1, "screen"

    .line 20
    .line 21
    invoke-virtual {p3}, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics$Screen;->getValue()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p3

    .line 25
    invoke-static {v1, p3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 26
    .line 27
    .line 28
    move-result-object p3

    .line 29
    const/4 v1, 0x3

    .line 30
    new-array v1, v1, [Lkotlin/Pair;

    .line 31
    .line 32
    const/4 v2, 0x0

    .line 33
    aput-object p1, v1, v2

    .line 34
    .line 35
    const/4 p1, 0x1

    .line 36
    aput-object p2, v1, p1

    .line 37
    .line 38
    const/4 p1, 0x2

    .line 39
    aput-object p3, v1, p1

    .line 40
    .line 41
    invoke-static {v1}, Lkotlin/collections/Q;->m([Lkotlin/Pair;)Ljava/util/Map;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    const-string p2, "games_promos_bonus_activated"

    .line 46
    .line 47
    invoke-interface {v0, p2, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public final b(IZ)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const-string v1, "game_id"

    .line 8
    .line 9
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    if-eqz p2, :cond_0

    .line 14
    .line 15
    const-string p2, "single"

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const-string p2, "multy"

    .line 19
    .line 20
    :goto_0
    const-string v1, "filter"

    .line 21
    .line 22
    invoke-static {v1, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    const-string v1, "screen"

    .line 27
    .line 28
    const-string v2, "lucky_wheel"

    .line 29
    .line 30
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    const/4 v2, 0x3

    .line 35
    new-array v2, v2, [Lkotlin/Pair;

    .line 36
    .line 37
    const/4 v3, 0x0

    .line 38
    aput-object p1, v2, v3

    .line 39
    .line 40
    const/4 p1, 0x1

    .line 41
    aput-object p2, v2, p1

    .line 42
    .line 43
    const/4 p1, 0x2

    .line 44
    aput-object v1, v2, p1

    .line 45
    .line 46
    invoke-static {v2}, Lkotlin/collections/Q;->m([Lkotlin/Pair;)Ljava/util/Map;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    const-string p2, "games_promos_bonus_activated"

    .line 51
    .line 52
    invoke-interface {v0, p2, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final c(I)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/analytics/domain/scope/gamesbonuses/GamesBonusesAnalytics;->a:Lorg/xbet/analytics/domain/b;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const-string v1, "game_id"

    .line 8
    .line 9
    invoke-static {v1, p1}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    const-string v1, "filter"

    .line 14
    .line 15
    const-string v2, "nothing"

    .line 16
    .line 17
    invoke-static {v1, v2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    const-string v2, "screen"

    .line 22
    .line 23
    const-string v3, "daily_quest"

    .line 24
    .line 25
    invoke-static {v2, v3}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    const/4 v3, 0x3

    .line 30
    new-array v3, v3, [Lkotlin/Pair;

    .line 31
    .line 32
    const/4 v4, 0x0

    .line 33
    aput-object p1, v3, v4

    .line 34
    .line 35
    const/4 p1, 0x1

    .line 36
    aput-object v1, v3, p1

    .line 37
    .line 38
    const/4 p1, 0x2

    .line 39
    aput-object v2, v3, p1

    .line 40
    .line 41
    invoke-static {v3}, Lkotlin/collections/Q;->m([Lkotlin/Pair;)Ljava/util/Map;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    const-string v1, "games_promos_bonus_activated"

    .line 46
    .line 47
    invoke-interface {v0, v1, p1}, Lorg/xbet/analytics/domain/b;->b(Ljava/lang/String;Ljava/util/Map;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method
