.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingEndGameViewModel$sendAction$2"
    f = "TileMatchingEndGameViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->Q3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $event:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    iput-object p2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->$event:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->$event:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->C3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$sendAction$2;->$event:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$a;

    .line 18
    .line 19
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p1

    .line 25
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 28
    .line 29
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw p1
.end method
