.class public final LtE0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtE0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtE0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtE0/a$b$a;,
        LtE0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LtE0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/forecast/data/repository/ForecastStatisticsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwE0/b;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/forecast/presentation/viewmodel/ForecastStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtE0/a$b;->a:LtE0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p14}, LtE0/a$b;->b(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;LtE0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p14}, LtE0/a$b;-><init>(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtE0/a$b;->c(Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;)Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;LEN0/f;LwX0/c;LHX0/e;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;Ljava/lang/String;Lorg/xbet/onexdatabase/OnexDatabase;Lorg/xbet/ui_common/utils/internet/a;Li8/l;Ljava/lang/Long;LSX0/a;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LtE0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p3}, LqE0/c;->a(LBc/a;)LqE0/c;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LtE0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LtE0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p3, LtE0/a$b$a;

    .line 20
    .line 21
    invoke-direct {p3, p1}, LtE0/a$b$a;-><init>(LQW0/c;)V

    .line 22
    .line 23
    .line 24
    iput-object p3, p0, LtE0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    iget-object p1, p0, LtE0/a$b;->c:Ldagger/internal/h;

    .line 27
    .line 28
    iget-object p5, p0, LtE0/a$b;->d:Ldagger/internal/h;

    .line 29
    .line 30
    invoke-static {p1, p5, p3}, Lorg/xbet/statistic/forecast/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/forecast/data/repository/a;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, LtE0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p1}, LwE0/c;->a(LBc/a;)LwE0/c;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LtE0/a$b;->g:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LtE0/a$b;->h:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LtE0/a$b;->i:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LtE0/a$b;->j:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LtE0/a$b;->k:Ldagger/internal/h;

    .line 65
    .line 66
    new-instance p1, LtE0/a$b$b;

    .line 67
    .line 68
    invoke-direct {p1, p2}, LtE0/a$b$b;-><init>(LEN0/f;)V

    .line 69
    .line 70
    .line 71
    iput-object p1, p0, LtE0/a$b;->l:Ldagger/internal/h;

    .line 72
    .line 73
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iput-object p1, p0, LtE0/a$b;->m:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    iput-object p1, p0, LtE0/a$b;->n:Ldagger/internal/h;

    .line 84
    .line 85
    iget-object p2, p0, LtE0/a$b;->e:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LtE0/a$b;->o:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p1, p0, LtE0/a$b;->l:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iput-object p1, p0, LtE0/a$b;->p:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object p1, p0, LtE0/a$b;->l:Ldagger/internal/h;

    .line 102
    .line 103
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iput-object p1, p0, LtE0/a$b;->q:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 110
    .line 111
    .line 112
    move-result-object p7

    .line 113
    iput-object p7, p0, LtE0/a$b;->r:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p2, p0, LtE0/a$b;->m:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p3, p0, LtE0/a$b;->o:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p4, p0, LtE0/a$b;->p:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p5, p0, LtE0/a$b;->j:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p6, p0, LtE0/a$b;->q:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p8, p0, LtE0/a$b;->h:Ldagger/internal/h;

    .line 126
    .line 127
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    iput-object p1, p0, LtE0/a$b;->s:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    iput-object p1, p0, LtE0/a$b;->t:Ldagger/internal/h;

    .line 138
    .line 139
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 140
    .line 141
    .line 142
    move-result-object p9

    .line 143
    iput-object p9, p0, LtE0/a$b;->u:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object p2, p0, LtE0/a$b;->g:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object p3, p0, LtE0/a$b;->h:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object p4, p0, LtE0/a$b;->i:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object p5, p0, LtE0/a$b;->j:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object p6, p0, LtE0/a$b;->k:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object p7, p0, LtE0/a$b;->s:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object p8, p0, LtE0/a$b;->t:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object p10, p0, LtE0/a$b;->e:Ldagger/internal/h;

    .line 160
    .line 161
    invoke-static/range {p2 .. p10}, Lorg/xbet/statistic/forecast/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/forecast/presentation/viewmodel/a;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    iput-object p1, p0, LtE0/a$b;->v:Ldagger/internal/h;

    .line 166
    .line 167
    return-void
.end method

.method public final c(Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;)Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtE0/a$b;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/forecast/presentation/fragment/c;->a(Lorg/xbet/statistic/forecast/presentation/fragment/ForecastStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/statistic/forecast/presentation/viewmodel/ForecastStatisticViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LtE0/a$b;->v:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LtE0/a$b;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
