.class public final LGD0/b$e;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LGD0/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0002\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\u000c\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000eR \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "LGD0/b$e;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "LJD0/f;",
        "binding",
        "Lkotlin/Function1;",
        "",
        "",
        "onPlayerClick",
        "<init>",
        "(LJD0/f;Lkotlin/jvm/functions/Function1;)V",
        "LID0/c;",
        "rowTitle",
        "e",
        "(LID0/c;)V",
        "LJD0/f;",
        "f",
        "Lkotlin/jvm/functions/Function1;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LJD0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LJD0/f;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # LJD0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LJD0/f;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LJD0/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LGD0/b$e;->e:LJD0/f;

    .line 9
    .line 10
    iput-object p2, p0, LGD0/b$e;->f:Lkotlin/jvm/functions/Function1;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic d(LGD0/b$e;LID0/c;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LGD0/b$e;->f(LGD0/b$e;LID0/c;Landroid/view/View;)V

    return-void
.end method

.method public static final f(LGD0/b$e;LID0/c;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, LGD0/b$e;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-virtual {p1}, LID0/c;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final e(LID0/c;)V
    .locals 9
    .param p1    # LID0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LGD0/b$e;->e:LJD0/f;

    .line 2
    .line 3
    iget-object v0, v0, LJD0/f;->d:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {p1}, LID0/c;->c()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p1}, LID0/c;->b()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-lez v0, :cond_0

    .line 21
    .line 22
    sget-object v1, LCX0/l;->a:LCX0/l;

    .line 23
    .line 24
    iget-object v0, p0, LGD0/b$e;->e:LJD0/f;

    .line 25
    .line 26
    iget-object v2, v0, LJD0/f;->b:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 27
    .line 28
    sget-object v3, Lorg/xbet/ui_common/utils/image/ImageCropType;->CIRCLE_IMAGE:Lorg/xbet/ui_common/utils/image/ImageCropType;

    .line 29
    .line 30
    invoke-virtual {p1}, LID0/c;->b()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    sget v6, Lpb/g;->ic_profile:I

    .line 35
    .line 36
    const/4 v7, 0x2

    .line 37
    const/4 v8, 0x0

    .line 38
    const/4 v4, 0x0

    .line 39
    invoke-static/range {v1 .. v8}, LCX0/l;->F(LCX0/l;Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;IILjava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, LGD0/b$e;->e:LJD0/f;

    .line 43
    .line 44
    iget-object v0, v0, LJD0/f;->e:Landroid/widget/TextView;

    .line 45
    .line 46
    invoke-virtual {p1}, LID0/c;->e()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 51
    .line 52
    .line 53
    iget-object v0, p0, LGD0/b$e;->e:LJD0/f;

    .line 54
    .line 55
    invoke-virtual {v0}, LJD0/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    new-instance v1, LGD0/c;

    .line 60
    .line 61
    invoke-direct {v1, p0, p1}, LGD0/c;-><init>(LGD0/b$e;LID0/c;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 65
    .line 66
    .line 67
    iget-object v0, p0, LGD0/b$e;->e:LJD0/f;

    .line 68
    .line 69
    iget-object v0, v0, LJD0/f;->c:Landroidx/appcompat/widget/AppCompatImageView;

    .line 70
    .line 71
    invoke-virtual {p1}, LID0/c;->d()I

    .line 72
    .line 73
    .line 74
    move-result p1

    .line 75
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 76
    .line 77
    .line 78
    return-void
.end method
