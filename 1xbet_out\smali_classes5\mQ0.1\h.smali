.class public interface abstract LmQ0/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/ui_common/viewmodel/core/e;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lorg/xbet/ui_common/viewmodel/core/e<",
        "Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008a\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001\u00a8\u0006\u0003"
    }
    d2 = {
        "LmQ0/h;",
        "Lorg/xbet/ui_common/viewmodel/core/e;",
        "Lorg/xbet/statistic/tennis/impl/player_menu/presentation/viewmodel/PlayersStatisticViewModel;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
