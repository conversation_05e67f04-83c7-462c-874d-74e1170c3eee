.class public final LiC0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a-\u0010\u000b\u001a\u00020\u0005*\u00020\u00082\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0008\u0008\u0002\u0010\n\u001a\u00020\tH\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LZB0/b;",
        "Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;",
        "marketSettingType",
        "",
        "available",
        "LjC0/d;",
        "a",
        "(LZB0/b;Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;Z)LjC0/d;",
        "LaB0/d;",
        "",
        "pinnedPosition",
        "b",
        "(LaB0/d;Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;ZI)LjC0/d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LZB0/b;Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;Z)LjC0/d;
    .locals 7
    .param p0    # LZB0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LZB0/b;->b()J

    .line 2
    .line 3
    .line 4
    move-result-wide v2

    .line 5
    invoke-virtual {p0}, LZB0/b;->c()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {p0}, LZB0/b;->d()I

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    new-instance v0, LjC0/d;

    .line 14
    .line 15
    move-object v4, p1

    .line 16
    move v5, p2

    .line 17
    invoke-direct/range {v0 .. v6}, LjC0/d;-><init>(Ljava/lang/String;JLorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;ZI)V

    .line 18
    .line 19
    .line 20
    return-object v0
.end method

.method public static final b(LaB0/d;Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;ZI)LjC0/d;
    .locals 7
    .param p0    # LaB0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LaB0/d;->f()J

    .line 2
    .line 3
    .line 4
    move-result-wide v2

    .line 5
    invoke-virtual {p0}, LaB0/d;->g()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v0, LjC0/d;

    .line 10
    .line 11
    move-object v4, p1

    .line 12
    move v5, p2

    .line 13
    move v6, p3

    .line 14
    invoke-direct/range {v0 .. v6}, LjC0/d;-><init>(Ljava/lang/String;JLorg/xbet/sportgame/markets_settings/impl/presentation/models/MarketSettingType;ZI)V

    .line 15
    .line 16
    .line 17
    return-object v0
.end method
