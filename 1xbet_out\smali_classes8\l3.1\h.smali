.class public Ll3/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ll3/e;
.implements Lm3/a$b;
.implements Ll3/k;


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Z

.field public final c:Lcom/airbnb/lottie/model/layer/a;

.field public final d:Landroidx/collection/C;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/C<",
            "Landroid/graphics/LinearGradient;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Landroidx/collection/C;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/C<",
            "Landroid/graphics/RadialGradient;",
            ">;"
        }
    .end annotation
.end field

.field public final f:Landroid/graphics/Path;

.field public final g:Landroid/graphics/Paint;

.field public final h:Landroid/graphics/RectF;

.field public final i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ll3/m;",
            ">;"
        }
    .end annotation
.end field

.field public final j:Lcom/airbnb/lottie/model/content/GradientType;

.field public final k:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Lq3/d;",
            "Lq3/d;",
            ">;"
        }
    .end annotation
.end field

.field public final l:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final m:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field public final n:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Landroid/graphics/PointF;",
            "Landroid/graphics/PointF;",
            ">;"
        }
    .end annotation
.end field

.field public o:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Landroid/graphics/ColorFilter;",
            "Landroid/graphics/ColorFilter;",
            ">;"
        }
    .end annotation
.end field

.field public p:Lm3/q;

.field public final q:Lcom/airbnb/lottie/LottieDrawable;

.field public final r:I

.field public s:Lm3/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lm3/a<",
            "Ljava/lang/Float;",
            "Ljava/lang/Float;",
            ">;"
        }
    .end annotation
.end field

.field public t:F

.field public u:Lm3/c;


# direct methods
.method public constructor <init>(Lcom/airbnb/lottie/LottieDrawable;Lcom/airbnb/lottie/i;Lcom/airbnb/lottie/model/layer/a;Lq3/e;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Landroidx/collection/C;

    .line 5
    .line 6
    invoke-direct {v0}, Landroidx/collection/C;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Ll3/h;->d:Landroidx/collection/C;

    .line 10
    .line 11
    new-instance v0, Landroidx/collection/C;

    .line 12
    .line 13
    invoke-direct {v0}, Landroidx/collection/C;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Ll3/h;->e:Landroidx/collection/C;

    .line 17
    .line 18
    new-instance v0, Landroid/graphics/Path;

    .line 19
    .line 20
    invoke-direct {v0}, Landroid/graphics/Path;-><init>()V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 24
    .line 25
    new-instance v1, Lk3/a;

    .line 26
    .line 27
    const/4 v2, 0x1

    .line 28
    invoke-direct {v1, v2}, Lk3/a;-><init>(I)V

    .line 29
    .line 30
    .line 31
    iput-object v1, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 32
    .line 33
    new-instance v1, Landroid/graphics/RectF;

    .line 34
    .line 35
    invoke-direct {v1}, Landroid/graphics/RectF;-><init>()V

    .line 36
    .line 37
    .line 38
    iput-object v1, p0, Ll3/h;->h:Landroid/graphics/RectF;

    .line 39
    .line 40
    new-instance v1, Ljava/util/ArrayList;

    .line 41
    .line 42
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 43
    .line 44
    .line 45
    iput-object v1, p0, Ll3/h;->i:Ljava/util/List;

    .line 46
    .line 47
    const/4 v1, 0x0

    .line 48
    iput v1, p0, Ll3/h;->t:F

    .line 49
    .line 50
    iput-object p3, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 51
    .line 52
    invoke-virtual {p4}, Lq3/e;->f()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    iput-object v1, p0, Ll3/h;->a:Ljava/lang/String;

    .line 57
    .line 58
    invoke-virtual {p4}, Lq3/e;->i()Z

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    iput-boolean v1, p0, Ll3/h;->b:Z

    .line 63
    .line 64
    iput-object p1, p0, Ll3/h;->q:Lcom/airbnb/lottie/LottieDrawable;

    .line 65
    .line 66
    invoke-virtual {p4}, Lq3/e;->e()Lcom/airbnb/lottie/model/content/GradientType;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, Ll3/h;->j:Lcom/airbnb/lottie/model/content/GradientType;

    .line 71
    .line 72
    invoke-virtual {p4}, Lq3/e;->c()Landroid/graphics/Path$FillType;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-virtual {v0, p1}, Landroid/graphics/Path;->setFillType(Landroid/graphics/Path$FillType;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p2}, Lcom/airbnb/lottie/i;->d()F

    .line 80
    .line 81
    .line 82
    move-result p1

    .line 83
    const/high16 p2, 0x42000000    # 32.0f

    .line 84
    .line 85
    div-float/2addr p1, p2

    .line 86
    float-to-int p1, p1

    .line 87
    iput p1, p0, Ll3/h;->r:I

    .line 88
    .line 89
    invoke-virtual {p4}, Lq3/e;->d()Lp3/c;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-virtual {p1}, Lp3/c;->a()Lm3/a;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, Ll3/h;->k:Lm3/a;

    .line 98
    .line 99
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 100
    .line 101
    .line 102
    invoke-virtual {p3, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 103
    .line 104
    .line 105
    invoke-virtual {p4}, Lq3/e;->g()Lp3/d;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    invoke-virtual {p1}, Lp3/d;->a()Lm3/a;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, Ll3/h;->l:Lm3/a;

    .line 114
    .line 115
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {p3, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 119
    .line 120
    .line 121
    invoke-virtual {p4}, Lq3/e;->h()Lp3/f;

    .line 122
    .line 123
    .line 124
    move-result-object p1

    .line 125
    invoke-virtual {p1}, Lp3/f;->a()Lm3/a;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, Ll3/h;->m:Lm3/a;

    .line 130
    .line 131
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 132
    .line 133
    .line 134
    invoke-virtual {p3, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 135
    .line 136
    .line 137
    invoke-virtual {p4}, Lq3/e;->b()Lp3/f;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    invoke-virtual {p1}, Lp3/f;->a()Lm3/a;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object p1, p0, Ll3/h;->n:Lm3/a;

    .line 146
    .line 147
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 148
    .line 149
    .line 150
    invoke-virtual {p3, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 151
    .line 152
    .line 153
    invoke-virtual {p3}, Lcom/airbnb/lottie/model/layer/a;->x()Lq3/a;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    if-eqz p1, :cond_0

    .line 158
    .line 159
    invoke-virtual {p3}, Lcom/airbnb/lottie/model/layer/a;->x()Lq3/a;

    .line 160
    .line 161
    .line 162
    move-result-object p1

    .line 163
    invoke-virtual {p1}, Lq3/a;->a()Lp3/b;

    .line 164
    .line 165
    .line 166
    move-result-object p1

    .line 167
    invoke-virtual {p1}, Lp3/b;->c()Lm3/d;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    iput-object p1, p0, Ll3/h;->s:Lm3/a;

    .line 172
    .line 173
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 174
    .line 175
    .line 176
    iget-object p1, p0, Ll3/h;->s:Lm3/a;

    .line 177
    .line 178
    invoke-virtual {p3, p1}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 179
    .line 180
    .line 181
    :cond_0
    invoke-virtual {p3}, Lcom/airbnb/lottie/model/layer/a;->z()Lt3/j;

    .line 182
    .line 183
    .line 184
    move-result-object p1

    .line 185
    if-eqz p1, :cond_1

    .line 186
    .line 187
    new-instance p1, Lm3/c;

    .line 188
    .line 189
    invoke-virtual {p3}, Lcom/airbnb/lottie/model/layer/a;->z()Lt3/j;

    .line 190
    .line 191
    .line 192
    move-result-object p2

    .line 193
    invoke-direct {p1, p0, p3, p2}, Lm3/c;-><init>(Lm3/a$b;Lcom/airbnb/lottie/model/layer/a;Lt3/j;)V

    .line 194
    .line 195
    .line 196
    iput-object p1, p0, Ll3/h;->u:Lm3/c;

    .line 197
    .line 198
    :cond_1
    return-void
.end method

.method private c([I)[I
    .locals 4

    .line 1
    iget-object v0, p0, Ll3/h;->p:Lm3/q;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Lm3/q;->h()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, [Ljava/lang/Integer;

    .line 10
    .line 11
    array-length v1, p1

    .line 12
    array-length v2, v0

    .line 13
    const/4 v3, 0x0

    .line 14
    if-ne v1, v2, :cond_0

    .line 15
    .line 16
    :goto_0
    array-length v1, p1

    .line 17
    if-ge v3, v1, :cond_1

    .line 18
    .line 19
    aget-object v1, v0, v3

    .line 20
    .line 21
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    aput v1, p1, v3

    .line 26
    .line 27
    add-int/lit8 v3, v3, 0x1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    array-length p1, v0

    .line 31
    new-array p1, p1, [I

    .line 32
    .line 33
    :goto_1
    array-length v1, v0

    .line 34
    if-ge v3, v1, :cond_1

    .line 35
    .line 36
    aget-object v1, v0, v3

    .line 37
    .line 38
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    aput v1, p1, v3

    .line 43
    .line 44
    add-int/lit8 v3, v3, 0x1

    .line 45
    .line 46
    goto :goto_1

    .line 47
    :cond_1
    return-object p1
.end method

.method private j()I
    .locals 4

    .line 1
    iget-object v0, p0, Ll3/h;->m:Lm3/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lm3/a;->f()F

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Ll3/h;->r:I

    .line 8
    .line 9
    int-to-float v1, v1

    .line 10
    mul-float v0, v0, v1

    .line 11
    .line 12
    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    iget-object v1, p0, Ll3/h;->n:Lm3/a;

    .line 17
    .line 18
    invoke-virtual {v1}, Lm3/a;->f()F

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    iget v2, p0, Ll3/h;->r:I

    .line 23
    .line 24
    int-to-float v2, v2

    .line 25
    mul-float v1, v1, v2

    .line 26
    .line 27
    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    iget-object v2, p0, Ll3/h;->k:Lm3/a;

    .line 32
    .line 33
    invoke-virtual {v2}, Lm3/a;->f()F

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    iget v3, p0, Ll3/h;->r:I

    .line 38
    .line 39
    int-to-float v3, v3

    .line 40
    mul-float v2, v2, v3

    .line 41
    .line 42
    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    if-eqz v0, :cond_0

    .line 47
    .line 48
    const/16 v3, 0x20f

    .line 49
    .line 50
    mul-int v3, v3, v0

    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_0
    const/16 v3, 0x11

    .line 54
    .line 55
    :goto_0
    if-eqz v1, :cond_1

    .line 56
    .line 57
    mul-int/lit8 v3, v3, 0x1f

    .line 58
    .line 59
    mul-int v3, v3, v1

    .line 60
    .line 61
    :cond_1
    if-eqz v2, :cond_2

    .line 62
    .line 63
    mul-int/lit8 v3, v3, 0x1f

    .line 64
    .line 65
    mul-int v3, v3, v2

    .line 66
    .line 67
    :cond_2
    return v3
.end method

.method private k()Landroid/graphics/LinearGradient;
    .locals 14

    .line 1
    invoke-direct {p0}, Ll3/h;->j()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget-object v1, p0, Ll3/h;->d:Landroidx/collection/C;

    .line 6
    .line 7
    int-to-long v2, v0

    .line 8
    invoke-virtual {v1, v2, v3}, Landroidx/collection/C;->e(J)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Landroid/graphics/LinearGradient;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    return-object v0

    .line 17
    :cond_0
    iget-object v0, p0, Ll3/h;->m:Lm3/a;

    .line 18
    .line 19
    invoke-virtual {v0}, Lm3/a;->h()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    check-cast v0, Landroid/graphics/PointF;

    .line 24
    .line 25
    iget-object v1, p0, Ll3/h;->n:Lm3/a;

    .line 26
    .line 27
    invoke-virtual {v1}, Lm3/a;->h()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, Landroid/graphics/PointF;

    .line 32
    .line 33
    iget-object v4, p0, Ll3/h;->k:Lm3/a;

    .line 34
    .line 35
    invoke-virtual {v4}, Lm3/a;->h()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    check-cast v4, Lq3/d;

    .line 40
    .line 41
    invoke-virtual {v4}, Lq3/d;->d()[I

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    invoke-direct {p0, v5}, Ll3/h;->c([I)[I

    .line 46
    .line 47
    .line 48
    move-result-object v11

    .line 49
    invoke-virtual {v4}, Lq3/d;->e()[F

    .line 50
    .line 51
    .line 52
    move-result-object v12

    .line 53
    new-instance v6, Landroid/graphics/LinearGradient;

    .line 54
    .line 55
    iget v7, v0, Landroid/graphics/PointF;->x:F

    .line 56
    .line 57
    iget v8, v0, Landroid/graphics/PointF;->y:F

    .line 58
    .line 59
    iget v9, v1, Landroid/graphics/PointF;->x:F

    .line 60
    .line 61
    iget v10, v1, Landroid/graphics/PointF;->y:F

    .line 62
    .line 63
    sget-object v13, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    .line 64
    .line 65
    invoke-direct/range {v6 .. v13}, Landroid/graphics/LinearGradient;-><init>(FFFF[I[FLandroid/graphics/Shader$TileMode;)V

    .line 66
    .line 67
    .line 68
    iget-object v0, p0, Ll3/h;->d:Landroidx/collection/C;

    .line 69
    .line 70
    invoke-virtual {v0, v2, v3, v6}, Landroidx/collection/C;->l(JLjava/lang/Object;)V

    .line 71
    .line 72
    .line 73
    return-object v6
.end method

.method private l()Landroid/graphics/RadialGradient;
    .locals 13

    .line 1
    invoke-direct {p0}, Ll3/h;->j()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget-object v1, p0, Ll3/h;->e:Landroidx/collection/C;

    .line 6
    .line 7
    int-to-long v2, v0

    .line 8
    invoke-virtual {v1, v2, v3}, Landroidx/collection/C;->e(J)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Landroid/graphics/RadialGradient;

    .line 13
    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    return-object v0

    .line 17
    :cond_0
    iget-object v0, p0, Ll3/h;->m:Lm3/a;

    .line 18
    .line 19
    invoke-virtual {v0}, Lm3/a;->h()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    check-cast v0, Landroid/graphics/PointF;

    .line 24
    .line 25
    iget-object v1, p0, Ll3/h;->n:Lm3/a;

    .line 26
    .line 27
    invoke-virtual {v1}, Lm3/a;->h()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, Landroid/graphics/PointF;

    .line 32
    .line 33
    iget-object v4, p0, Ll3/h;->k:Lm3/a;

    .line 34
    .line 35
    invoke-virtual {v4}, Lm3/a;->h()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    check-cast v4, Lq3/d;

    .line 40
    .line 41
    invoke-virtual {v4}, Lq3/d;->d()[I

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    invoke-direct {p0, v5}, Ll3/h;->c([I)[I

    .line 46
    .line 47
    .line 48
    move-result-object v10

    .line 49
    invoke-virtual {v4}, Lq3/d;->e()[F

    .line 50
    .line 51
    .line 52
    move-result-object v11

    .line 53
    iget v7, v0, Landroid/graphics/PointF;->x:F

    .line 54
    .line 55
    iget v8, v0, Landroid/graphics/PointF;->y:F

    .line 56
    .line 57
    iget v0, v1, Landroid/graphics/PointF;->x:F

    .line 58
    .line 59
    iget v1, v1, Landroid/graphics/PointF;->y:F

    .line 60
    .line 61
    sub-float/2addr v0, v7

    .line 62
    float-to-double v4, v0

    .line 63
    sub-float/2addr v1, v8

    .line 64
    float-to-double v0, v1

    .line 65
    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->hypot(DD)D

    .line 66
    .line 67
    .line 68
    move-result-wide v0

    .line 69
    double-to-float v0, v0

    .line 70
    const/4 v1, 0x0

    .line 71
    cmpg-float v1, v0, v1

    .line 72
    .line 73
    if-gtz v1, :cond_1

    .line 74
    .line 75
    const v0, 0x3a83126f

    .line 76
    .line 77
    .line 78
    const v9, 0x3a83126f

    .line 79
    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_1
    move v9, v0

    .line 83
    :goto_0
    new-instance v6, Landroid/graphics/RadialGradient;

    .line 84
    .line 85
    sget-object v12, Landroid/graphics/Shader$TileMode;->CLAMP:Landroid/graphics/Shader$TileMode;

    .line 86
    .line 87
    invoke-direct/range {v6 .. v12}, Landroid/graphics/RadialGradient;-><init>(FFF[I[FLandroid/graphics/Shader$TileMode;)V

    .line 88
    .line 89
    .line 90
    iget-object v0, p0, Ll3/h;->e:Landroidx/collection/C;

    .line 91
    .line 92
    invoke-virtual {v0, v2, v3, v6}, Landroidx/collection/C;->l(JLjava/lang/Object;)V

    .line 93
    .line 94
    .line 95
    return-object v6
.end method


# virtual methods
.method public a(Lo3/d;ILjava/util/List;Lo3/d;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lo3/d;",
            "I",
            "Ljava/util/List<",
            "Lo3/d;",
            ">;",
            "Lo3/d;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-static {p1, p2, p3, p4, p0}, Lu3/k;->k(Lo3/d;ILjava/util/List;Lo3/d;Ll3/k;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Landroid/graphics/RectF;Landroid/graphics/Matrix;Z)V
    .locals 3

    .line 1
    iget-object p3, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 2
    .line 3
    invoke-virtual {p3}, Landroid/graphics/Path;->reset()V

    .line 4
    .line 5
    .line 6
    const/4 p3, 0x0

    .line 7
    const/4 v0, 0x0

    .line 8
    :goto_0
    iget-object v1, p0, Ll3/h;->i:Ljava/util/List;

    .line 9
    .line 10
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-ge v0, v1, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 17
    .line 18
    iget-object v2, p0, Ll3/h;->i:Ljava/util/List;

    .line 19
    .line 20
    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v2

    .line 24
    check-cast v2, Ll3/m;

    .line 25
    .line 26
    invoke-interface {v2}, Ll3/m;->d()Landroid/graphics/Path;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    invoke-virtual {v1, v2, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    .line 31
    .line 32
    .line 33
    add-int/lit8 v0, v0, 0x1

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    iget-object p2, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 37
    .line 38
    invoke-virtual {p2, p1, p3}, Landroid/graphics/Path;->computeBounds(Landroid/graphics/RectF;Z)V

    .line 39
    .line 40
    .line 41
    iget p2, p1, Landroid/graphics/RectF;->left:F

    .line 42
    .line 43
    const/high16 p3, 0x3f800000    # 1.0f

    .line 44
    .line 45
    sub-float/2addr p2, p3

    .line 46
    iget v0, p1, Landroid/graphics/RectF;->top:F

    .line 47
    .line 48
    sub-float/2addr v0, p3

    .line 49
    iget v1, p1, Landroid/graphics/RectF;->right:F

    .line 50
    .line 51
    add-float/2addr v1, p3

    .line 52
    iget v2, p1, Landroid/graphics/RectF;->bottom:F

    .line 53
    .line 54
    add-float/2addr v2, p3

    .line 55
    invoke-virtual {p1, p2, v0, v1, v2}, Landroid/graphics/RectF;->set(FFFF)V

    .line 56
    .line 57
    .line 58
    return-void
.end method

.method public e(Landroid/graphics/Canvas;Landroid/graphics/Matrix;I)V
    .locals 5

    .line 1
    iget-boolean v0, p0, Ll3/h;->b:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    goto/16 :goto_3

    .line 6
    .line 7
    :cond_0
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    const-string v1, "GradientFillContent#draw"

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-static {v1}, Lcom/airbnb/lottie/d;->b(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    iget-object v0, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/graphics/Path;->reset()V

    .line 21
    .line 22
    .line 23
    const/4 v0, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    :goto_0
    iget-object v3, p0, Ll3/h;->i:Ljava/util/List;

    .line 26
    .line 27
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    if-ge v2, v3, :cond_2

    .line 32
    .line 33
    iget-object v3, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 34
    .line 35
    iget-object v4, p0, Ll3/h;->i:Ljava/util/List;

    .line 36
    .line 37
    invoke-interface {v4, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    check-cast v4, Ll3/m;

    .line 42
    .line 43
    invoke-interface {v4}, Ll3/m;->d()Landroid/graphics/Path;

    .line 44
    .line 45
    .line 46
    move-result-object v4

    .line 47
    invoke-virtual {v3, v4, p2}, Landroid/graphics/Path;->addPath(Landroid/graphics/Path;Landroid/graphics/Matrix;)V

    .line 48
    .line 49
    .line 50
    add-int/lit8 v2, v2, 0x1

    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_2
    iget-object v2, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 54
    .line 55
    iget-object v3, p0, Ll3/h;->h:Landroid/graphics/RectF;

    .line 56
    .line 57
    invoke-virtual {v2, v3, v0}, Landroid/graphics/Path;->computeBounds(Landroid/graphics/RectF;Z)V

    .line 58
    .line 59
    .line 60
    iget-object v2, p0, Ll3/h;->j:Lcom/airbnb/lottie/model/content/GradientType;

    .line 61
    .line 62
    sget-object v3, Lcom/airbnb/lottie/model/content/GradientType;->LINEAR:Lcom/airbnb/lottie/model/content/GradientType;

    .line 63
    .line 64
    if-ne v2, v3, :cond_3

    .line 65
    .line 66
    invoke-direct {p0}, Ll3/h;->k()Landroid/graphics/LinearGradient;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    goto :goto_1

    .line 71
    :cond_3
    invoke-direct {p0}, Ll3/h;->l()Landroid/graphics/RadialGradient;

    .line 72
    .line 73
    .line 74
    move-result-object v2

    .line 75
    :goto_1
    invoke-virtual {v2, p2}, Landroid/graphics/Shader;->setLocalMatrix(Landroid/graphics/Matrix;)V

    .line 76
    .line 77
    .line 78
    iget-object v3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 79
    .line 80
    invoke-virtual {v3, v2}, Landroid/graphics/Paint;->setShader(Landroid/graphics/Shader;)Landroid/graphics/Shader;

    .line 81
    .line 82
    .line 83
    iget-object v2, p0, Ll3/h;->o:Lm3/a;

    .line 84
    .line 85
    if-eqz v2, :cond_4

    .line 86
    .line 87
    iget-object v3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 88
    .line 89
    invoke-virtual {v2}, Lm3/a;->h()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    check-cast v2, Landroid/graphics/ColorFilter;

    .line 94
    .line 95
    invoke-virtual {v3, v2}, Landroid/graphics/Paint;->setColorFilter(Landroid/graphics/ColorFilter;)Landroid/graphics/ColorFilter;

    .line 96
    .line 97
    .line 98
    :cond_4
    iget-object v2, p0, Ll3/h;->s:Lm3/a;

    .line 99
    .line 100
    if-eqz v2, :cond_7

    .line 101
    .line 102
    invoke-virtual {v2}, Lm3/a;->h()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    check-cast v2, Ljava/lang/Float;

    .line 107
    .line 108
    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    .line 109
    .line 110
    .line 111
    move-result v2

    .line 112
    const/4 v3, 0x0

    .line 113
    cmpl-float v3, v2, v3

    .line 114
    .line 115
    if-nez v3, :cond_5

    .line 116
    .line 117
    iget-object v3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 118
    .line 119
    const/4 v4, 0x0

    .line 120
    invoke-virtual {v3, v4}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    .line 121
    .line 122
    .line 123
    goto :goto_2

    .line 124
    :cond_5
    iget v3, p0, Ll3/h;->t:F

    .line 125
    .line 126
    cmpl-float v3, v2, v3

    .line 127
    .line 128
    if-eqz v3, :cond_6

    .line 129
    .line 130
    new-instance v3, Landroid/graphics/BlurMaskFilter;

    .line 131
    .line 132
    sget-object v4, Landroid/graphics/BlurMaskFilter$Blur;->NORMAL:Landroid/graphics/BlurMaskFilter$Blur;

    .line 133
    .line 134
    invoke-direct {v3, v2, v4}, Landroid/graphics/BlurMaskFilter;-><init>(FLandroid/graphics/BlurMaskFilter$Blur;)V

    .line 135
    .line 136
    .line 137
    iget-object v4, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 138
    .line 139
    invoke-virtual {v4, v3}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    .line 140
    .line 141
    .line 142
    :cond_6
    :goto_2
    iput v2, p0, Ll3/h;->t:F

    .line 143
    .line 144
    :cond_7
    int-to-float v2, p3

    .line 145
    const/high16 v3, 0x437f0000    # 255.0f

    .line 146
    .line 147
    div-float/2addr v2, v3

    .line 148
    iget-object v4, p0, Ll3/h;->l:Lm3/a;

    .line 149
    .line 150
    invoke-virtual {v4}, Lm3/a;->h()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v4

    .line 154
    check-cast v4, Ljava/lang/Integer;

    .line 155
    .line 156
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 157
    .line 158
    .line 159
    move-result v4

    .line 160
    int-to-float v4, v4

    .line 161
    mul-float v2, v2, v4

    .line 162
    .line 163
    const/high16 v4, 0x42c80000    # 100.0f

    .line 164
    .line 165
    div-float/2addr v2, v4

    .line 166
    mul-float v2, v2, v3

    .line 167
    .line 168
    float-to-int v2, v2

    .line 169
    iget-object v3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 170
    .line 171
    const/16 v4, 0xff

    .line 172
    .line 173
    invoke-static {v2, v0, v4}, Lu3/k;->c(III)I

    .line 174
    .line 175
    .line 176
    move-result v0

    .line 177
    invoke-virtual {v3, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 178
    .line 179
    .line 180
    iget-object v0, p0, Ll3/h;->u:Lm3/c;

    .line 181
    .line 182
    if-eqz v0, :cond_8

    .line 183
    .line 184
    iget-object v3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 185
    .line 186
    invoke-static {p3, v2}, Lu3/l;->l(II)I

    .line 187
    .line 188
    .line 189
    move-result p3

    .line 190
    invoke-virtual {v0, v3, p2, p3}, Lm3/c;->a(Landroid/graphics/Paint;Landroid/graphics/Matrix;I)V

    .line 191
    .line 192
    .line 193
    :cond_8
    iget-object p2, p0, Ll3/h;->f:Landroid/graphics/Path;

    .line 194
    .line 195
    iget-object p3, p0, Ll3/h;->g:Landroid/graphics/Paint;

    .line 196
    .line 197
    invoke-virtual {p1, p2, p3}, Landroid/graphics/Canvas;->drawPath(Landroid/graphics/Path;Landroid/graphics/Paint;)V

    .line 198
    .line 199
    .line 200
    invoke-static {}, Lcom/airbnb/lottie/d;->g()Z

    .line 201
    .line 202
    .line 203
    move-result p1

    .line 204
    if-eqz p1, :cond_9

    .line 205
    .line 206
    invoke-static {v1}, Lcom/airbnb/lottie/d;->c(Ljava/lang/String;)F

    .line 207
    .line 208
    .line 209
    :cond_9
    :goto_3
    return-void
.end method

.method public f()V
    .locals 1

    .line 1
    iget-object v0, p0, Ll3/h;->q:Lcom/airbnb/lottie/LottieDrawable;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/airbnb/lottie/LottieDrawable;->invalidateSelf()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public g(Ljava/util/List;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ll3/c;",
            ">;",
            "Ljava/util/List<",
            "Ll3/c;",
            ">;)V"
        }
    .end annotation

    .line 1
    const/4 p1, 0x0

    .line 2
    :goto_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 3
    .line 4
    .line 5
    move-result v0

    .line 6
    if-ge p1, v0, :cond_1

    .line 7
    .line 8
    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Ll3/c;

    .line 13
    .line 14
    instance-of v1, v0, Ll3/m;

    .line 15
    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    iget-object v1, p0, Ll3/h;->i:Ljava/util/List;

    .line 19
    .line 20
    check-cast v0, Ll3/m;

    .line 21
    .line 22
    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 23
    .line 24
    .line 25
    :cond_0
    add-int/lit8 p1, p1, 0x1

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    return-void
.end method

.method public getName()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Ll3/h;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public h(Ljava/lang/Object;Lv3/c;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(TT;",
            "Lv3/c<",
            "TT;>;)V"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/airbnb/lottie/S;->d:Ljava/lang/Integer;

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Ll3/h;->l:Lm3/a;

    .line 6
    .line 7
    invoke-virtual {p1, p2}, Lm3/a;->o(Lv3/c;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    sget-object v0, Lcom/airbnb/lottie/S;->K:Landroid/graphics/ColorFilter;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    if-ne p1, v0, :cond_3

    .line 15
    .line 16
    iget-object p1, p0, Ll3/h;->o:Lm3/a;

    .line 17
    .line 18
    if-eqz p1, :cond_1

    .line 19
    .line 20
    iget-object v0, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 21
    .line 22
    invoke-virtual {v0, p1}, Lcom/airbnb/lottie/model/layer/a;->H(Lm3/a;)V

    .line 23
    .line 24
    .line 25
    :cond_1
    if-nez p2, :cond_2

    .line 26
    .line 27
    iput-object v1, p0, Ll3/h;->o:Lm3/a;

    .line 28
    .line 29
    return-void

    .line 30
    :cond_2
    new-instance p1, Lm3/q;

    .line 31
    .line 32
    invoke-direct {p1, p2}, Lm3/q;-><init>(Lv3/c;)V

    .line 33
    .line 34
    .line 35
    iput-object p1, p0, Ll3/h;->o:Lm3/a;

    .line 36
    .line 37
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 38
    .line 39
    .line 40
    iget-object p1, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 41
    .line 42
    iget-object p2, p0, Ll3/h;->o:Lm3/a;

    .line 43
    .line 44
    invoke-virtual {p1, p2}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 45
    .line 46
    .line 47
    return-void

    .line 48
    :cond_3
    sget-object v0, Lcom/airbnb/lottie/S;->L:[Ljava/lang/Integer;

    .line 49
    .line 50
    if-ne p1, v0, :cond_6

    .line 51
    .line 52
    iget-object p1, p0, Ll3/h;->p:Lm3/q;

    .line 53
    .line 54
    if-eqz p1, :cond_4

    .line 55
    .line 56
    iget-object v0, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 57
    .line 58
    invoke-virtual {v0, p1}, Lcom/airbnb/lottie/model/layer/a;->H(Lm3/a;)V

    .line 59
    .line 60
    .line 61
    :cond_4
    if-nez p2, :cond_5

    .line 62
    .line 63
    iput-object v1, p0, Ll3/h;->p:Lm3/q;

    .line 64
    .line 65
    return-void

    .line 66
    :cond_5
    iget-object p1, p0, Ll3/h;->d:Landroidx/collection/C;

    .line 67
    .line 68
    invoke-virtual {p1}, Landroidx/collection/C;->b()V

    .line 69
    .line 70
    .line 71
    iget-object p1, p0, Ll3/h;->e:Landroidx/collection/C;

    .line 72
    .line 73
    invoke-virtual {p1}, Landroidx/collection/C;->b()V

    .line 74
    .line 75
    .line 76
    new-instance p1, Lm3/q;

    .line 77
    .line 78
    invoke-direct {p1, p2}, Lm3/q;-><init>(Lv3/c;)V

    .line 79
    .line 80
    .line 81
    iput-object p1, p0, Ll3/h;->p:Lm3/q;

    .line 82
    .line 83
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 84
    .line 85
    .line 86
    iget-object p1, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 87
    .line 88
    iget-object p2, p0, Ll3/h;->p:Lm3/q;

    .line 89
    .line 90
    invoke-virtual {p1, p2}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 91
    .line 92
    .line 93
    return-void

    .line 94
    :cond_6
    sget-object v0, Lcom/airbnb/lottie/S;->j:Ljava/lang/Float;

    .line 95
    .line 96
    if-ne p1, v0, :cond_8

    .line 97
    .line 98
    iget-object p1, p0, Ll3/h;->s:Lm3/a;

    .line 99
    .line 100
    if-eqz p1, :cond_7

    .line 101
    .line 102
    invoke-virtual {p1, p2}, Lm3/a;->o(Lv3/c;)V

    .line 103
    .line 104
    .line 105
    return-void

    .line 106
    :cond_7
    new-instance p1, Lm3/q;

    .line 107
    .line 108
    invoke-direct {p1, p2}, Lm3/q;-><init>(Lv3/c;)V

    .line 109
    .line 110
    .line 111
    iput-object p1, p0, Ll3/h;->s:Lm3/a;

    .line 112
    .line 113
    invoke-virtual {p1, p0}, Lm3/a;->a(Lm3/a$b;)V

    .line 114
    .line 115
    .line 116
    iget-object p1, p0, Ll3/h;->c:Lcom/airbnb/lottie/model/layer/a;

    .line 117
    .line 118
    iget-object p2, p0, Ll3/h;->s:Lm3/a;

    .line 119
    .line 120
    invoke-virtual {p1, p2}, Lcom/airbnb/lottie/model/layer/a;->j(Lm3/a;)V

    .line 121
    .line 122
    .line 123
    return-void

    .line 124
    :cond_8
    sget-object v0, Lcom/airbnb/lottie/S;->e:Ljava/lang/Integer;

    .line 125
    .line 126
    if-ne p1, v0, :cond_9

    .line 127
    .line 128
    iget-object v0, p0, Ll3/h;->u:Lm3/c;

    .line 129
    .line 130
    if-eqz v0, :cond_9

    .line 131
    .line 132
    invoke-virtual {v0, p2}, Lm3/c;->b(Lv3/c;)V

    .line 133
    .line 134
    .line 135
    return-void

    .line 136
    :cond_9
    sget-object v0, Lcom/airbnb/lottie/S;->G:Ljava/lang/Float;

    .line 137
    .line 138
    if-ne p1, v0, :cond_a

    .line 139
    .line 140
    iget-object v0, p0, Ll3/h;->u:Lm3/c;

    .line 141
    .line 142
    if-eqz v0, :cond_a

    .line 143
    .line 144
    invoke-virtual {v0, p2}, Lm3/c;->e(Lv3/c;)V

    .line 145
    .line 146
    .line 147
    return-void

    .line 148
    :cond_a
    sget-object v0, Lcom/airbnb/lottie/S;->H:Ljava/lang/Float;

    .line 149
    .line 150
    if-ne p1, v0, :cond_b

    .line 151
    .line 152
    iget-object v0, p0, Ll3/h;->u:Lm3/c;

    .line 153
    .line 154
    if-eqz v0, :cond_b

    .line 155
    .line 156
    invoke-virtual {v0, p2}, Lm3/c;->c(Lv3/c;)V

    .line 157
    .line 158
    .line 159
    return-void

    .line 160
    :cond_b
    sget-object v0, Lcom/airbnb/lottie/S;->I:Ljava/lang/Float;

    .line 161
    .line 162
    if-ne p1, v0, :cond_c

    .line 163
    .line 164
    iget-object v0, p0, Ll3/h;->u:Lm3/c;

    .line 165
    .line 166
    if-eqz v0, :cond_c

    .line 167
    .line 168
    invoke-virtual {v0, p2}, Lm3/c;->d(Lv3/c;)V

    .line 169
    .line 170
    .line 171
    return-void

    .line 172
    :cond_c
    sget-object v0, Lcom/airbnb/lottie/S;->J:Ljava/lang/Float;

    .line 173
    .line 174
    if-ne p1, v0, :cond_d

    .line 175
    .line 176
    iget-object p1, p0, Ll3/h;->u:Lm3/c;

    .line 177
    .line 178
    if-eqz p1, :cond_d

    .line 179
    .line 180
    invoke-virtual {p1, p2}, Lm3/c;->g(Lv3/c;)V

    .line 181
    .line 182
    .line 183
    :cond_d
    return-void
.end method
