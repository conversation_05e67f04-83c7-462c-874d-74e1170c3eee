.class public interface abstract LcZ0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008f\u0018\u00002\u00020\u0001J\u0017\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0017\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0006\u0010\u0005J\u001f\u0010\t\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u00022\u0006\u0010\u0008\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J/\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0007\u001a\u00020\u00022\u0006\u0010\u0008\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u001f\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0017\u0010\u0010J+\u0010\u001a\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u000e2\n\u0010\u0019\u001a\u00060\u0012j\u0002`\u00182\u0006\u0010\u0007\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001f\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u001c\u0010\u0010J+\u0010\u001f\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u000e2\n\u0010\u001e\u001a\u00060\u0012j\u0002`\u001d2\u0006\u0010\u0008\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u001f\u0010\u001bJ\u001f\u0010\"\u001a\u00020!2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\u0012H&\u00a2\u0006\u0004\u0008\"\u0010#R\u001c\u0010)\u001a\u00020$8&@&X\u00a6\u000e\u00a2\u0006\u000c\u001a\u0004\u0008%\u0010&\"\u0004\u0008\'\u0010(\u00a8\u0006*"
    }
    d2 = {
        "LcZ0/c;",
        "",
        "",
        "position",
        "k",
        "(I)I",
        "c",
        "columnPosition",
        "rowPosition",
        "b",
        "(II)I",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "LeZ0/a;",
        "a",
        "(Landroid/view/ViewGroup;I)LeZ0/a;",
        "holder",
        "LhZ0/a;",
        "cellItemModel",
        "",
        "g",
        "(LeZ0/a;LhZ0/a;II)V",
        "i",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/model/ColumnHeader;",
        "columnHeaderItemModel",
        "j",
        "(LeZ0/a;LhZ0/a;I)V",
        "e",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/model/RowHeader;",
        "rowHeaderItemModel",
        "h",
        "cell",
        "Landroid/view/View;",
        "l",
        "(Landroid/view/ViewGroup;LhZ0/a;)Landroid/view/View;",
        "LbZ0/a;",
        "d",
        "()LbZ0/a;",
        "f",
        "(LbZ0/a;)V",
        "tableView",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Landroid/view/ViewGroup;I)LeZ0/a;
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract b(II)I
.end method

.method public abstract c(I)I
.end method

.method public abstract d()LbZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract e(Landroid/view/ViewGroup;I)LeZ0/a;
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract f(LbZ0/a;)V
    .param p1    # LbZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract g(LeZ0/a;LhZ0/a;II)V
    .param p1    # LeZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LhZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract h(LeZ0/a;LhZ0/a;I)V
    .param p1    # LeZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LhZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract i(Landroid/view/ViewGroup;I)LeZ0/a;
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract j(LeZ0/a;LhZ0/a;I)V
    .param p1    # LeZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LhZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract k(I)I
.end method

.method public abstract l(Landroid/view/ViewGroup;LhZ0/a;)Landroid/view/View;
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LhZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
