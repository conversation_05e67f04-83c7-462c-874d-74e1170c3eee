.class public final Ljb1/q;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ljb1/q$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\t\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a9\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\n0\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u0013\u0010\r\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\r\u0010\u000e\u001a\'\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013\u001a\u001f\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0010\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017\u001a!\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a!\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001a\u001a!\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001a\u00a8\u0006\u001e"
    }
    d2 = {
        "Li81/a;",
        "",
        "preview",
        "LHX0/e;",
        "resourceManager",
        "Ljava/util/Locale;",
        "locale",
        "",
        "typeStage",
        "",
        "Lc31/a;",
        "g",
        "(Li81/a;ZLHX0/e;Ljava/util/Locale;Ljava/lang/String;)Ljava/util/List;",
        "a",
        "(Li81/a;)Z",
        "Ljava/util/Date;",
        "stageStart",
        "stageEnd",
        "f",
        "(Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;",
        "",
        "",
        "e",
        "(JJ)I",
        "Lo81/b;",
        "d",
        "(Li81/a;Z)Ljava/util/List;",
        "Ln81/d;",
        "b",
        "c",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Li81/a;)Z
    .locals 6
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    new-instance v0, Ljb1/q$b;

    .line 10
    .line 11
    invoke-direct {v0}, Ljb1/q$b;-><init>()V

    .line 12
    .line 13
    .line 14
    invoke-static {p0, v0}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const/4 v1, 0x0

    .line 23
    const/4 v2, 0x0

    .line 24
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v3

    .line 28
    if-eqz v3, :cond_2

    .line 29
    .line 30
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    add-int/lit8 v4, v2, 0x1

    .line 35
    .line 36
    if-gez v2, :cond_0

    .line 37
    .line 38
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 39
    .line 40
    .line 41
    :cond_0
    check-cast v3, Ln81/d;

    .line 42
    .line 43
    invoke-static {p0}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 44
    .line 45
    .line 46
    move-result v5

    .line 47
    if-ge v2, v5, :cond_1

    .line 48
    .line 49
    invoke-virtual {v3}, Ln81/d;->c()Ljava/util/Date;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-interface {p0, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    check-cast v3, Ln81/d;

    .line 58
    .line 59
    invoke-virtual {v3}, Ln81/d;->d()Ljava/util/Date;

    .line 60
    .line 61
    .line 62
    move-result-object v3

    .line 63
    invoke-virtual {v2, v3}, Ljava/util/Date;->compareTo(Ljava/util/Date;)I

    .line 64
    .line 65
    .line 66
    move-result v2

    .line 67
    if-gez v2, :cond_1

    .line 68
    .line 69
    const/4 p0, 0x1

    .line 70
    return p0

    .line 71
    :cond_1
    move v2, v4

    .line 72
    goto :goto_0

    .line 73
    :cond_2
    return v1
.end method

.method public static final b(Li81/a;Z)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Z)",
            "Ljava/util/List<",
            "Ln81/d;",
            ">;"
        }
    .end annotation

    .line 1
    if-eqz p1, :cond_d

    .line 2
    .line 3
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p1}, Ln81/b;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    const/4 v0, 0x3

    .line 16
    if-le p1, v0, :cond_d

    .line 17
    .line 18
    invoke-virtual {p0}, Li81/a;->d()Lj81/a;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-virtual {p1}, Lj81/a;->i()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    sget-object v1, Ljb1/q$a;->a:[I

    .line 27
    .line 28
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    aget p1, v1, p1

    .line 33
    .line 34
    const/4 v1, 0x1

    .line 35
    if-eq p1, v1, :cond_2

    .line 36
    .line 37
    const/4 v1, 0x2

    .line 38
    if-eq p1, v1, :cond_1

    .line 39
    .line 40
    if-ne p1, v0, :cond_0

    .line 41
    .line 42
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object p0

    .line 50
    invoke-static {p0, v0}, Lkotlin/collections/CollectionsKt;->q1(Ljava/util/List;I)Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    return-object p0

    .line 55
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 56
    .line 57
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 58
    .line 59
    .line 60
    throw p0

    .line 61
    :cond_1
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    invoke-static {p0, v0}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    return-object p0

    .line 74
    :cond_2
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-virtual {p1}, Ln81/b;->c()Ljava/util/List;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 83
    .line 84
    .line 85
    move-result p1

    .line 86
    new-instance v1, Ljava/util/Date;

    .line 87
    .line 88
    invoke-direct {v1}, Ljava/util/Date;-><init>()V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 92
    .line 93
    .line 94
    move-result-object v2

    .line 95
    invoke-virtual {v2}, Ln81/b;->c()Ljava/util/List;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 100
    .line 101
    .line 102
    move-result-object v2

    .line 103
    const/4 v3, 0x0

    .line 104
    const/4 v4, 0x0

    .line 105
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 106
    .line 107
    .line 108
    move-result v5

    .line 109
    const/4 v6, -0x1

    .line 110
    if-eqz v5, :cond_4

    .line 111
    .line 112
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v5

    .line 116
    check-cast v5, Ln81/d;

    .line 117
    .line 118
    sget-object v7, Ll8/b;->a:Ll8/b;

    .line 119
    .line 120
    invoke-virtual {v5}, Ln81/d;->d()Ljava/util/Date;

    .line 121
    .line 122
    .line 123
    move-result-object v8

    .line 124
    invoke-virtual {v5}, Ln81/d;->c()Ljava/util/Date;

    .line 125
    .line 126
    .line 127
    move-result-object v5

    .line 128
    invoke-virtual {v7, v1, v8, v5}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 129
    .line 130
    .line 131
    move-result v5

    .line 132
    if-eqz v5, :cond_3

    .line 133
    .line 134
    goto :goto_1

    .line 135
    :cond_3
    add-int/lit8 v4, v4, 0x1

    .line 136
    .line 137
    goto :goto_0

    .line 138
    :cond_4
    const/4 v4, -0x1

    .line 139
    :goto_1
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 144
    .line 145
    .line 146
    move-result v2

    .line 147
    const/4 v4, 0x0

    .line 148
    if-ltz v2, :cond_5

    .line 149
    .line 150
    goto :goto_2

    .line 151
    :cond_5
    move-object v1, v4

    .line 152
    :goto_2
    if-eqz v1, :cond_6

    .line 153
    .line 154
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 155
    .line 156
    .line 157
    move-result v1

    .line 158
    goto :goto_5

    .line 159
    :cond_6
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 160
    .line 161
    .line 162
    move-result-object v1

    .line 163
    invoke-virtual {v1}, Ln81/b;->c()Ljava/util/List;

    .line 164
    .line 165
    .line 166
    move-result-object v1

    .line 167
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 168
    .line 169
    .line 170
    move-result-object v1

    .line 171
    const/4 v2, 0x0

    .line 172
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 173
    .line 174
    .line 175
    move-result v5

    .line 176
    if-eqz v5, :cond_8

    .line 177
    .line 178
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 179
    .line 180
    .line 181
    move-result-object v5

    .line 182
    check-cast v5, Ln81/d;

    .line 183
    .line 184
    invoke-virtual {v5}, Ln81/d;->b()J

    .line 185
    .line 186
    .line 187
    move-result-wide v7

    .line 188
    invoke-virtual {p0}, Li81/a;->l()J

    .line 189
    .line 190
    .line 191
    move-result-wide v9

    .line 192
    cmp-long v5, v7, v9

    .line 193
    .line 194
    if-nez v5, :cond_7

    .line 195
    .line 196
    move v6, v2

    .line 197
    goto :goto_4

    .line 198
    :cond_7
    add-int/lit8 v2, v2, 0x1

    .line 199
    .line 200
    goto :goto_3

    .line 201
    :cond_8
    :goto_4
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 202
    .line 203
    .line 204
    move-result-object v1

    .line 205
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 206
    .line 207
    .line 208
    move-result v2

    .line 209
    if-ltz v2, :cond_9

    .line 210
    .line 211
    move-object v4, v1

    .line 212
    :cond_9
    if-eqz v4, :cond_a

    .line 213
    .line 214
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 215
    .line 216
    .line 217
    move-result v1

    .line 218
    goto :goto_5

    .line 219
    :cond_a
    const/4 v1, 0x0

    .line 220
    :goto_5
    add-int/lit8 v2, p1, -0x2

    .line 221
    .line 222
    if-lt v1, v2, :cond_b

    .line 223
    .line 224
    add-int/lit8 v3, p1, -0x3

    .line 225
    .line 226
    goto :goto_6

    .line 227
    :cond_b
    if-lez v1, :cond_c

    .line 228
    .line 229
    move v3, v1

    .line 230
    :cond_c
    :goto_6
    add-int/lit8 p1, v3, 0x3

    .line 231
    .line 232
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 233
    .line 234
    .line 235
    move-result-object p0

    .line 236
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 237
    .line 238
    .line 239
    move-result-object p0

    .line 240
    invoke-interface {p0, v3, p1}, Ljava/util/List;->subList(II)Ljava/util/List;

    .line 241
    .line 242
    .line 243
    move-result-object p0

    .line 244
    return-object p0

    .line 245
    :cond_d
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 246
    .line 247
    .line 248
    move-result-object p0

    .line 249
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 250
    .line 251
    .line 252
    move-result-object p0

    .line 253
    return-object p0
.end method

.method public static final c(Li81/a;Z)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Z)",
            "Ljava/util/List<",
            "Ln81/d;",
            ">;"
        }
    .end annotation

    .line 1
    if-eqz p1, :cond_8

    .line 2
    .line 3
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p1}, Ln81/b;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    new-instance v0, Ljava/util/Date;

    .line 16
    .line 17
    invoke-direct {v0}, Ljava/util/Date;-><init>()V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, Ln81/b;->c()Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    invoke-interface {v1, v2}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    :cond_0
    invoke-interface {v1}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    const/4 v3, -0x1

    .line 41
    if-eqz v2, :cond_1

    .line 42
    .line 43
    invoke-interface {v1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    check-cast v2, Ln81/d;

    .line 48
    .line 49
    invoke-virtual {v2}, Ln81/d;->c()Ljava/util/Date;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-virtual {v2, v0}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 54
    .line 55
    .line 56
    move-result v2

    .line 57
    if-eqz v2, :cond_0

    .line 58
    .line 59
    invoke-interface {v1}, Ljava/util/ListIterator;->nextIndex()I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    goto :goto_0

    .line 64
    :cond_1
    const/4 v0, -0x1

    .line 65
    :goto_0
    const/4 v1, 0x3

    .line 66
    if-ne v0, v3, :cond_2

    .line 67
    .line 68
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 69
    .line 70
    .line 71
    move-result-object p0

    .line 72
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object p0

    .line 76
    invoke-static {p0, v1}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    return-object p0

    .line 81
    :cond_2
    add-int/lit8 v2, v0, 0x1

    .line 82
    .line 83
    new-instance v4, Ljava/util/ArrayList;

    .line 84
    .line 85
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 86
    .line 87
    .line 88
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 89
    .line 90
    .line 91
    move-result-object v5

    .line 92
    invoke-virtual {v5}, Ln81/b;->c()Ljava/util/List;

    .line 93
    .line 94
    .line 95
    move-result-object v5

    .line 96
    invoke-interface {v5, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v5

    .line 100
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 101
    .line 102
    .line 103
    :goto_1
    if-ge v2, p1, :cond_3

    .line 104
    .line 105
    invoke-interface {v4}, Ljava/util/List;->size()I

    .line 106
    .line 107
    .line 108
    move-result v5

    .line 109
    if-ge v5, v1, :cond_3

    .line 110
    .line 111
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 112
    .line 113
    .line 114
    move-result-object v5

    .line 115
    invoke-virtual {v5}, Ln81/b;->c()Ljava/util/List;

    .line 116
    .line 117
    .line 118
    move-result-object v5

    .line 119
    invoke-interface {v5, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object v5

    .line 123
    invoke-interface {v4, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 124
    .line 125
    .line 126
    add-int/lit8 v2, v2, 0x1

    .line 127
    .line 128
    goto :goto_1

    .line 129
    :cond_3
    invoke-interface {v4}, Ljava/util/List;->size()I

    .line 130
    .line 131
    .line 132
    move-result v2

    .line 133
    const/4 v5, 0x0

    .line 134
    if-ge v2, v1, :cond_5

    .line 135
    .line 136
    add-int/lit8 v0, v0, -0x1

    .line 137
    .line 138
    :goto_2
    if-ge v3, v0, :cond_4

    .line 139
    .line 140
    invoke-interface {v4}, Ljava/util/List;->size()I

    .line 141
    .line 142
    .line 143
    move-result p1

    .line 144
    if-ge p1, v1, :cond_4

    .line 145
    .line 146
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    invoke-virtual {p1}, Ln81/b;->c()Ljava/util/List;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    invoke-interface {v4, v5, p1}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 159
    .line 160
    .line 161
    add-int/lit8 v0, v0, -0x1

    .line 162
    .line 163
    goto :goto_2

    .line 164
    :cond_4
    return-object v4

    .line 165
    :cond_5
    add-int/lit8 v2, p1, -0x2

    .line 166
    .line 167
    if-lt v0, v2, :cond_6

    .line 168
    .line 169
    add-int/lit8 v0, p1, -0x3

    .line 170
    .line 171
    goto :goto_3

    .line 172
    :cond_6
    if-lez v0, :cond_7

    .line 173
    .line 174
    goto :goto_3

    .line 175
    :cond_7
    const/4 v0, 0x0

    .line 176
    :goto_3
    add-int/lit8 p1, v0, 0x3

    .line 177
    .line 178
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 179
    .line 180
    .line 181
    move-result-object p0

    .line 182
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 183
    .line 184
    .line 185
    move-result-object p0

    .line 186
    invoke-interface {p0, v0, p1}, Ljava/util/List;->subList(II)Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object p0

    .line 190
    return-object p0

    .line 191
    :cond_8
    invoke-virtual {p0}, Li81/a;->h()Ln81/b;

    .line 192
    .line 193
    .line 194
    move-result-object p0

    .line 195
    invoke-virtual {p0}, Ln81/b;->c()Ljava/util/List;

    .line 196
    .line 197
    .line 198
    move-result-object p0

    .line 199
    return-object p0
.end method

.method public static final d(Li81/a;Z)Ljava/util/List;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Z)",
            "Ljava/util/List<",
            "Lo81/b;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Li81/a;->i()Lo81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lo81/a;->a()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/4 v2, 0x0

    .line 14
    const/4 v3, 0x3

    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    if-le p1, v3, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    move-object v0, v2

    .line 27
    :goto_0
    if-eqz v0, :cond_7

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    const/4 v1, 0x0

    .line 34
    const/4 v4, 0x0

    .line 35
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 36
    .line 37
    .line 38
    move-result v5

    .line 39
    if-eqz v5, :cond_2

    .line 40
    .line 41
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    check-cast v5, Lo81/b;

    .line 46
    .line 47
    invoke-virtual {v5}, Lo81/b;->c()J

    .line 48
    .line 49
    .line 50
    move-result-wide v5

    .line 51
    invoke-virtual {p0}, Li81/a;->l()J

    .line 52
    .line 53
    .line 54
    move-result-wide v7

    .line 55
    cmp-long v9, v5, v7

    .line 56
    .line 57
    if-nez v9, :cond_1

    .line 58
    .line 59
    goto :goto_2

    .line 60
    :cond_1
    add-int/lit8 v4, v4, 0x1

    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_2
    const/4 v4, -0x1

    .line 64
    :goto_2
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 69
    .line 70
    .line 71
    move-result v4

    .line 72
    if-ltz v4, :cond_3

    .line 73
    .line 74
    move-object v2, p1

    .line 75
    :cond_3
    if-eqz v2, :cond_4

    .line 76
    .line 77
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    goto :goto_3

    .line 82
    :cond_4
    const/4 p1, 0x0

    .line 83
    :goto_3
    if-nez p1, :cond_5

    .line 84
    .line 85
    goto :goto_4

    .line 86
    :cond_5
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 87
    .line 88
    .line 89
    move-result v1

    .line 90
    add-int/lit8 v1, v1, -0x2

    .line 91
    .line 92
    if-lt p1, v1, :cond_6

    .line 93
    .line 94
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    add-int/lit8 v1, p1, -0x3

    .line 99
    .line 100
    goto :goto_4

    .line 101
    :cond_6
    add-int/lit8 v1, p1, -0x1

    .line 102
    .line 103
    :goto_4
    add-int/lit8 p1, v1, 0x3

    .line 104
    .line 105
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 106
    .line 107
    .line 108
    move-result v2

    .line 109
    invoke-static {p1, v2}, Lkotlin/ranges/f;->l(II)I

    .line 110
    .line 111
    .line 112
    move-result p1

    .line 113
    invoke-interface {v0, v1, p1}, Ljava/util/List;->subList(II)Ljava/util/List;

    .line 114
    .line 115
    .line 116
    move-result-object p1

    .line 117
    if-eqz p1, :cond_7

    .line 118
    .line 119
    return-object p1

    .line 120
    :cond_7
    invoke-virtual {p0}, Li81/a;->i()Lo81/a;

    .line 121
    .line 122
    .line 123
    move-result-object p0

    .line 124
    invoke-virtual {p0}, Lo81/a;->a()Ljava/util/List;

    .line 125
    .line 126
    .line 127
    move-result-object p0

    .line 128
    return-object p0
.end method

.method public static final e(JJ)I
    .locals 3

    .line 1
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 6
    .line 7
    sub-long/2addr p2, p0

    .line 8
    invoke-virtual {v2, p2, p3}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 9
    .line 10
    .line 11
    move-result-wide p2

    .line 12
    sub-long/2addr v0, p0

    .line 13
    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 14
    .line 15
    .line 16
    move-result-wide p0

    .line 17
    long-to-double p0, p0

    .line 18
    long-to-double p2, p2

    .line 19
    div-double/2addr p0, p2

    .line 20
    const/16 p2, 0x64

    .line 21
    .line 22
    int-to-double p2, p2

    .line 23
    mul-double p0, p0, p2

    .line 24
    .line 25
    const-wide/16 p2, 0x0

    .line 26
    .line 27
    cmpl-double v0, p0, p2

    .line 28
    .line 29
    if-lez v0, :cond_0

    .line 30
    .line 31
    const-wide/high16 p2, 0x3ff0000000000000L    # 1.0

    .line 32
    .line 33
    cmpg-double v0, p0, p2

    .line 34
    .line 35
    if-gez v0, :cond_0

    .line 36
    .line 37
    const/4 p0, 0x1

    .line 38
    return p0

    .line 39
    :cond_0
    double-to-int p0, p0

    .line 40
    return p0
.end method

.method public static final f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;
    .locals 2

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    const-string v1, "d MMMM"

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1, p2}, Ll8/b;->g(Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-virtual {v0, p1, v1, p2}, Ll8/b;->g(Ljava/util/Date;Ljava/lang/String;Ljava/util/Locale;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    new-instance p2, Ljava/lang/StringBuilder;

    .line 14
    .line 15
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 19
    .line 20
    .line 21
    const-string p0, " - "

    .line 22
    .line 23
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 27
    .line 28
    .line 29
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    return-object p0
.end method

.method public static final g(Li81/a;ZLHX0/e;Ljava/util/Locale;Ljava/lang/String;)Ljava/util/List;
    .locals 17
    .param p0    # Li81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Z",
            "LHX0/e;",
            "Ljava/util/Locale;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lc31/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    move-object/from16 v1, p4

    .line 4
    .line 5
    const/4 v8, 0x1

    .line 6
    invoke-virtual/range {p0 .. p0}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 7
    .line 8
    .line 9
    move-result-object v2

    .line 10
    sget-object v3, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->PROVIDER:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 11
    .line 12
    const/16 v4, 0xa

    .line 13
    .line 14
    const/4 v10, 0x0

    .line 15
    if-ne v2, v3, :cond_7

    .line 16
    .line 17
    invoke-virtual/range {p0 .. p0}, Li81/a;->h()Ln81/b;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Ln81/b;->c()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-eqz v0, :cond_0

    .line 30
    .line 31
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    goto :goto_0

    .line 36
    :cond_0
    invoke-virtual/range {p0 .. p0}, Li81/a;->h()Ln81/b;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {v0}, Ln81/b;->c()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    const/4 v2, 0x3

    .line 49
    if-gt v0, v2, :cond_1

    .line 50
    .line 51
    invoke-virtual/range {p0 .. p0}, Li81/a;->h()Ln81/b;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0}, Ln81/b;->c()Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    goto :goto_0

    .line 60
    :cond_1
    invoke-static/range {p0 .. p0}, Ljb1/q;->a(Li81/a;)Z

    .line 61
    .line 62
    .line 63
    move-result v0

    .line 64
    if-eqz v0, :cond_2

    .line 65
    .line 66
    invoke-static/range {p0 .. p1}, Ljb1/q;->c(Li81/a;Z)Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    goto :goto_0

    .line 71
    :cond_2
    invoke-static/range {p0 .. p1}, Ljb1/q;->b(Li81/a;Z)Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    :goto_0
    new-instance v7, Ljava/util/ArrayList;

    .line 76
    .line 77
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 78
    .line 79
    .line 80
    move-result v2

    .line 81
    invoke-direct {v7, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 82
    .line 83
    .line 84
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 85
    .line 86
    .line 87
    move-result-object v11

    .line 88
    :goto_1
    invoke-interface {v11}, Ljava/util/Iterator;->hasNext()Z

    .line 89
    .line 90
    .line 91
    move-result v0

    .line 92
    if-eqz v0, :cond_6

    .line 93
    .line 94
    invoke-interface {v11}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    add-int/lit8 v12, v10, 0x1

    .line 99
    .line 100
    if-gez v10, :cond_3

    .line 101
    .line 102
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 103
    .line 104
    .line 105
    :cond_3
    check-cast v0, Ln81/d;

    .line 106
    .line 107
    new-instance v2, Ljava/util/Date;

    .line 108
    .line 109
    invoke-direct {v2}, Ljava/util/Date;-><init>()V

    .line 110
    .line 111
    .line 112
    invoke-virtual {v0}, Ln81/d;->d()Ljava/util/Date;

    .line 113
    .line 114
    .line 115
    move-result-object v3

    .line 116
    invoke-virtual {v0}, Ln81/d;->c()Ljava/util/Date;

    .line 117
    .line 118
    .line 119
    move-result-object v4

    .line 120
    invoke-virtual {v0}, Ln81/d;->d()Ljava/util/Date;

    .line 121
    .line 122
    .line 123
    move-result-object v5

    .line 124
    invoke-virtual {v0}, Ln81/d;->c()Ljava/util/Date;

    .line 125
    .line 126
    .line 127
    move-result-object v6

    .line 128
    move-object/from16 v10, p3

    .line 129
    .line 130
    invoke-static {v5, v6, v10}, Ljb1/q;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Locale;)Ljava/lang/String;

    .line 131
    .line 132
    .line 133
    move-result-object v5

    .line 134
    invoke-virtual {v0}, Ln81/d;->b()J

    .line 135
    .line 136
    .line 137
    move-result-wide v13

    .line 138
    invoke-static {v13, v14}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v6

    .line 142
    invoke-virtual {v0}, Ln81/d;->d()Ljava/util/Date;

    .line 143
    .line 144
    .line 145
    move-result-object v13

    .line 146
    invoke-virtual {v13}, Ljava/util/Date;->getTime()J

    .line 147
    .line 148
    .line 149
    move-result-wide v13

    .line 150
    invoke-virtual {v0}, Ln81/d;->c()Ljava/util/Date;

    .line 151
    .line 152
    .line 153
    move-result-object v15

    .line 154
    const/16 v16, 0x1

    .line 155
    .line 156
    invoke-virtual {v15}, Ljava/util/Date;->getTime()J

    .line 157
    .line 158
    .line 159
    move-result-wide v8

    .line 160
    invoke-static {v13, v14, v8, v9}, Ljb1/q;->e(JJ)I

    .line 161
    .line 162
    .line 163
    move-result v8

    .line 164
    invoke-virtual {v2, v3}, Ljava/util/Date;->before(Ljava/util/Date;)Z

    .line 165
    .line 166
    .line 167
    move-result v9

    .line 168
    if-eqz v9, :cond_4

    .line 169
    .line 170
    invoke-virtual {v0}, Ln81/d;->a()Ljava/lang/String;

    .line 171
    .line 172
    .line 173
    move-result-object v0

    .line 174
    new-instance v2, Lc31/a$a;

    .line 175
    .line 176
    invoke-direct {v2, v1, v6, v5, v0}, Lc31/a$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 177
    .line 178
    .line 179
    goto :goto_3

    .line 180
    :cond_4
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 181
    .line 182
    invoke-virtual {v0, v2, v3, v4}, Ll8/b;->f(Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;)Z

    .line 183
    .line 184
    .line 185
    move-result v0

    .line 186
    if-eqz v0, :cond_5

    .line 187
    .line 188
    new-instance v0, Lc31/a$c;

    .line 189
    .line 190
    const/4 v4, 0x0

    .line 191
    move-object v2, v6

    .line 192
    const/16 v6, 0x64

    .line 193
    .line 194
    move-object v3, v5

    .line 195
    move v5, v8

    .line 196
    invoke-direct/range {v0 .. v6}, Lc31/a$c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V

    .line 197
    .line 198
    .line 199
    :goto_2
    move-object v2, v0

    .line 200
    goto :goto_3

    .line 201
    :cond_5
    move-object v3, v5

    .line 202
    move-object v2, v6

    .line 203
    new-instance v0, Lc31/a$b;

    .line 204
    .line 205
    const/4 v4, 0x0

    .line 206
    invoke-direct {v0, v1, v2, v3, v4}, Lc31/a$b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 207
    .line 208
    .line 209
    goto :goto_2

    .line 210
    :goto_3
    invoke-interface {v7, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 211
    .line 212
    .line 213
    move v10, v12

    .line 214
    const/4 v8, 0x1

    .line 215
    goto :goto_1

    .line 216
    :cond_6
    return-object v7

    .line 217
    :cond_7
    const/16 v16, 0x1

    .line 218
    .line 219
    invoke-static/range {p0 .. p1}, Ljb1/q;->d(Li81/a;Z)Ljava/util/List;

    .line 220
    .line 221
    .line 222
    move-result-object v8

    .line 223
    new-instance v9, Ljava/util/ArrayList;

    .line 224
    .line 225
    invoke-static {v8, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 226
    .line 227
    .line 228
    move-result v2

    .line 229
    invoke-direct {v9, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 230
    .line 231
    .line 232
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 233
    .line 234
    .line 235
    move-result-object v11

    .line 236
    const/4 v2, 0x0

    .line 237
    :goto_4
    invoke-interface {v11}, Ljava/util/Iterator;->hasNext()Z

    .line 238
    .line 239
    .line 240
    move-result v3

    .line 241
    if-eqz v3, :cond_11

    .line 242
    .line 243
    invoke-interface {v11}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 244
    .line 245
    .line 246
    move-result-object v3

    .line 247
    add-int/lit8 v12, v2, 0x1

    .line 248
    .line 249
    if-gez v2, :cond_8

    .line 250
    .line 251
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 252
    .line 253
    .line 254
    :cond_8
    check-cast v3, Lo81/b;

    .line 255
    .line 256
    invoke-virtual/range {p0 .. p0}, Li81/a;->f()Lm81/a;

    .line 257
    .line 258
    .line 259
    move-result-object v4

    .line 260
    invoke-virtual {v4}, Lm81/a;->a()Ljava/util/List;

    .line 261
    .line 262
    .line 263
    move-result-object v4

    .line 264
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 265
    .line 266
    .line 267
    move-result-object v4

    .line 268
    :cond_9
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 269
    .line 270
    .line 271
    move-result v5

    .line 272
    if-eqz v5, :cond_a

    .line 273
    .line 274
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 275
    .line 276
    .line 277
    move-result-object v5

    .line 278
    move-object v6, v5

    .line 279
    check-cast v6, Lm81/b;

    .line 280
    .line 281
    invoke-virtual {v6}, Lm81/b;->b()Z

    .line 282
    .line 283
    .line 284
    move-result v6

    .line 285
    if-eqz v6, :cond_9

    .line 286
    .line 287
    goto :goto_5

    .line 288
    :cond_a
    const/4 v5, 0x0

    .line 289
    :goto_5
    check-cast v5, Lm81/b;

    .line 290
    .line 291
    if-eqz v5, :cond_b

    .line 292
    .line 293
    invoke-virtual {v5}, Lm81/b;->d()I

    .line 294
    .line 295
    .line 296
    move-result v4

    .line 297
    goto :goto_6

    .line 298
    :cond_b
    const/4 v4, 0x0

    .line 299
    :goto_6
    invoke-virtual {v3}, Lo81/b;->b()I

    .line 300
    .line 301
    .line 302
    move-result v5

    .line 303
    if-lt v4, v5, :cond_c

    .line 304
    .line 305
    const/4 v5, 0x1

    .line 306
    goto :goto_7

    .line 307
    :cond_c
    const/4 v5, 0x0

    .line 308
    :goto_7
    invoke-virtual {v3}, Lo81/b;->c()J

    .line 309
    .line 310
    .line 311
    move-result-wide v6

    .line 312
    long-to-int v7, v6

    .line 313
    invoke-virtual/range {p0 .. p0}, Li81/a;->l()J

    .line 314
    .line 315
    .line 316
    move-result-wide v13

    .line 317
    long-to-int v6, v13

    .line 318
    add-int/lit8 v2, v2, -0x1

    .line 319
    .line 320
    invoke-static {v8, v2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 321
    .line 322
    .line 323
    move-result-object v2

    .line 324
    check-cast v2, Lo81/b;

    .line 325
    .line 326
    if-eqz v2, :cond_d

    .line 327
    .line 328
    invoke-virtual {v2}, Lo81/b;->b()I

    .line 329
    .line 330
    .line 331
    move-result v2

    .line 332
    goto :goto_8

    .line 333
    :cond_d
    const/4 v2, 0x0

    .line 334
    :goto_8
    if-ne v7, v6, :cond_f

    .line 335
    .line 336
    invoke-virtual/range {p0 .. p0}, Li81/a;->p()Z

    .line 337
    .line 338
    .line 339
    move-result v13

    .line 340
    if-eqz v13, :cond_f

    .line 341
    .line 342
    if-nez v5, :cond_e

    .line 343
    .line 344
    move-object v5, v3

    .line 345
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 346
    .line 347
    .line 348
    move-result-object v3

    .line 349
    sget v6, Lpb/k;->points_count:I

    .line 350
    .line 351
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 352
    .line 353
    .line 354
    move-result v7

    .line 355
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 356
    .line 357
    .line 358
    move-result-object v7

    .line 359
    const/4 v13, 0x1

    .line 360
    new-array v14, v13, [Ljava/lang/Object;

    .line 361
    .line 362
    aput-object v7, v14, v10

    .line 363
    .line 364
    invoke-interface {v0, v6, v14}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 365
    .line 366
    .line 367
    move-result-object v6

    .line 368
    sget v7, Lpb/k;->tournament_stage_points_left_2:I

    .line 369
    .line 370
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 371
    .line 372
    .line 373
    move-result v14

    .line 374
    sub-int/2addr v14, v4

    .line 375
    invoke-static {v14}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 376
    .line 377
    .line 378
    move-result-object v14

    .line 379
    new-array v15, v13, [Ljava/lang/Object;

    .line 380
    .line 381
    aput-object v14, v15, v10

    .line 382
    .line 383
    invoke-interface {v0, v7, v15}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 384
    .line 385
    .line 386
    move-result-object v7

    .line 387
    sub-int/2addr v4, v2

    .line 388
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 389
    .line 390
    .line 391
    move-result v5

    .line 392
    sub-int/2addr v5, v2

    .line 393
    new-instance v1, Lc31/a$c;

    .line 394
    .line 395
    move-object v2, v6

    .line 396
    move v6, v4

    .line 397
    move-object v4, v2

    .line 398
    move-object v2, v7

    .line 399
    move v7, v5

    .line 400
    move-object v5, v2

    .line 401
    move-object/from16 v2, p4

    .line 402
    .line 403
    invoke-direct/range {v1 .. v7}, Lc31/a$c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V

    .line 404
    .line 405
    .line 406
    const/4 v6, 0x0

    .line 407
    :goto_9
    const/4 v13, 0x1

    .line 408
    goto :goto_b

    .line 409
    :cond_e
    move-object v2, v1

    .line 410
    move-object v5, v3

    .line 411
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 412
    .line 413
    .line 414
    move-result-object v1

    .line 415
    sget v3, Lpb/k;->points_count:I

    .line 416
    .line 417
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 418
    .line 419
    .line 420
    move-result v4

    .line 421
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 422
    .line 423
    .line 424
    move-result-object v4

    .line 425
    const/4 v13, 0x1

    .line 426
    new-array v5, v13, [Ljava/lang/Object;

    .line 427
    .line 428
    aput-object v4, v5, v10

    .line 429
    .line 430
    invoke-interface {v0, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 431
    .line 432
    .line 433
    move-result-object v3

    .line 434
    new-instance v4, Lc31/a$b;

    .line 435
    .line 436
    const/4 v5, 0x0

    .line 437
    invoke-direct {v4, v2, v1, v3, v5}, Lc31/a$b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 438
    .line 439
    .line 440
    move-object v1, v4

    .line 441
    move-object v6, v5

    .line 442
    goto :goto_9

    .line 443
    :cond_f
    move-object v2, v1

    .line 444
    move-object v5, v3

    .line 445
    if-le v6, v7, :cond_10

    .line 446
    .line 447
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 448
    .line 449
    .line 450
    move-result-object v1

    .line 451
    sget v3, Lpb/k;->points_count:I

    .line 452
    .line 453
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 454
    .line 455
    .line 456
    move-result v4

    .line 457
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 458
    .line 459
    .line 460
    move-result-object v4

    .line 461
    const/4 v13, 0x1

    .line 462
    new-array v5, v13, [Ljava/lang/Object;

    .line 463
    .line 464
    aput-object v4, v5, v10

    .line 465
    .line 466
    invoke-interface {v0, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 467
    .line 468
    .line 469
    move-result-object v3

    .line 470
    new-instance v4, Lc31/a$b;

    .line 471
    .line 472
    const/4 v6, 0x0

    .line 473
    invoke-direct {v4, v2, v1, v3, v6}, Lc31/a$b;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 474
    .line 475
    .line 476
    :goto_a
    move-object v1, v4

    .line 477
    goto :goto_b

    .line 478
    :cond_10
    const/4 v6, 0x0

    .line 479
    const/4 v13, 0x1

    .line 480
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 481
    .line 482
    .line 483
    move-result-object v1

    .line 484
    sget v3, Lpb/k;->points_count:I

    .line 485
    .line 486
    invoke-virtual {v5}, Lo81/b;->b()I

    .line 487
    .line 488
    .line 489
    move-result v4

    .line 490
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 491
    .line 492
    .line 493
    move-result-object v4

    .line 494
    new-array v5, v13, [Ljava/lang/Object;

    .line 495
    .line 496
    aput-object v4, v5, v10

    .line 497
    .line 498
    invoke-interface {v0, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 499
    .line 500
    .line 501
    move-result-object v3

    .line 502
    new-instance v4, Lc31/a$a;

    .line 503
    .line 504
    invoke-direct {v4, v2, v1, v3, v6}, Lc31/a$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 505
    .line 506
    .line 507
    goto :goto_a

    .line 508
    :goto_b
    invoke-interface {v9, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 509
    .line 510
    .line 511
    move-object v1, v2

    .line 512
    move v2, v12

    .line 513
    const/16 v16, 0x1

    .line 514
    .line 515
    goto/16 :goto_4

    .line 516
    .line 517
    :cond_11
    return-object v9
.end method
