.class public final LIN0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LIN0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/o<",
        "Landroidx/compose/foundation/lazy/c;",
        "Ljava/lang/Integer;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LIN0/a$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LIN0/a$b;

    .line 2
    .line 3
    invoke-direct {v0}, LIN0/a$b;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LIN0/a$b;->a:LIN0/a$b;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic a(Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LIN0/a$b;->c(Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 8

    .line 1
    sget-object v0, LIN0/a;->a:LIN0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LIN0/a;->a()LOc/o;

    .line 4
    .line 5
    .line 6
    move-result-object v5

    .line 7
    const/4 v6, 0x6

    .line 8
    const/4 v7, 0x0

    .line 9
    const/4 v2, 0x6

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    move-object v1, p0

    .line 13
    invoke-static/range {v1 .. v7}, Landroidx/compose/foundation/lazy/LazyListScope$-CC;->b(Landroidx/compose/foundation/lazy/t;ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/o;ILjava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method


# virtual methods
.method public final b(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V
    .locals 18

    .line 1
    move/from16 v0, p2

    .line 2
    .line 3
    move-object/from16 v10, p3

    .line 4
    .line 5
    and-int/lit8 v1, p4, 0x30

    .line 6
    .line 7
    if-nez v1, :cond_1

    .line 8
    .line 9
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->x(I)Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    const/16 v1, 0x20

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/16 v1, 0x10

    .line 19
    .line 20
    :goto_0
    or-int v1, p4, v1

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_1
    move/from16 v1, p4

    .line 24
    .line 25
    :goto_1
    and-int/lit16 v2, v1, 0x91

    .line 26
    .line 27
    const/16 v3, 0x90

    .line 28
    .line 29
    if-ne v2, v3, :cond_3

    .line 30
    .line 31
    invoke-interface {v10}, Landroidx/compose/runtime/j;->c()Z

    .line 32
    .line 33
    .line 34
    move-result v2

    .line 35
    if-nez v2, :cond_2

    .line 36
    .line 37
    goto :goto_2

    .line 38
    :cond_2
    invoke-interface {v10}, Landroidx/compose/runtime/j;->n()V

    .line 39
    .line 40
    .line 41
    return-void

    .line 42
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    if-eqz v2, :cond_4

    .line 47
    .line 48
    const/4 v2, -0x1

    .line 49
    const-string v3, "org.xbet.statistic.statistic_core.presentation.composable.ComposableSingletons$ShimmerContentKt.lambda$592536682.<anonymous> (ShimmerContent.kt:31)"

    .line 50
    .line 51
    const v4, 0x2351646a

    .line 52
    .line 53
    .line 54
    invoke-static {v4, v1, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 55
    .line 56
    .line 57
    :cond_4
    const/4 v1, 0x0

    .line 58
    if-eqz v0, :cond_7

    .line 59
    .line 60
    const/4 v2, 0x0

    .line 61
    const/4 v3, 0x0

    .line 62
    const/4 v4, 0x1

    .line 63
    if-eq v0, v4, :cond_6

    .line 64
    .line 65
    const/4 v5, 0x2

    .line 66
    if-eq v0, v5, :cond_5

    .line 67
    .line 68
    const v0, -0x328ea36

    .line 69
    .line 70
    .line 71
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 72
    .line 73
    .line 74
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 75
    .line 76
    sget-object v5, LA11/a;->a:LA11/a;

    .line 77
    .line 78
    invoke-virtual {v5}, LA11/a;->K0()F

    .line 79
    .line 80
    .line 81
    move-result v6

    .line 82
    invoke-static {v0, v6}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 83
    .line 84
    .line 85
    move-result-object v11

    .line 86
    invoke-virtual {v5}, LA11/a;->q1()F

    .line 87
    .line 88
    .line 89
    move-result v15

    .line 90
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-virtual {v0}, LA11/b;->a()F

    .line 95
    .line 96
    .line 97
    move-result v12

    .line 98
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    invoke-virtual {v0}, LA11/b;->a()F

    .line 103
    .line 104
    .line 105
    move-result v14

    .line 106
    const/16 v16, 0x2

    .line 107
    .line 108
    const/16 v17, 0x0

    .line 109
    .line 110
    const/4 v13, 0x0

    .line 111
    invoke-static/range {v11 .. v17}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 112
    .line 113
    .line 114
    move-result-object v0

    .line 115
    invoke-static {v0, v2, v4, v3}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    invoke-virtual {v5}, LA11/a;->U()F

    .line 120
    .line 121
    .line 122
    move-result v2

    .line 123
    invoke-static {v2}, LR/i;->f(F)LR/h;

    .line 124
    .line 125
    .line 126
    move-result-object v2

    .line 127
    invoke-static {v0, v2}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 128
    .line 129
    .line 130
    move-result-object v0

    .line 131
    invoke-static {v3, v10, v1, v4}, Lp11/a;->b(Lorg/xbet/uikit/compose/components/skeleton/model/DsSkeletonContentBackgroundStyle;Landroidx/compose/runtime/j;II)Lp11/c;

    .line 132
    .line 133
    .line 134
    move-result-object v2

    .line 135
    invoke-static {v0, v2}, Lo11/a;->a(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    invoke-static {v0, v10, v1}, Landroidx/compose/foundation/layout/BoxKt;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 140
    .line 141
    .line 142
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 143
    .line 144
    .line 145
    goto/16 :goto_3

    .line 146
    .line 147
    :cond_5
    const v0, -0x332b917

    .line 148
    .line 149
    .line 150
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 151
    .line 152
    .line 153
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 154
    .line 155
    sget-object v5, LA11/a;->a:LA11/a;

    .line 156
    .line 157
    invoke-virtual {v5}, LA11/a;->Y()F

    .line 158
    .line 159
    .line 160
    move-result v6

    .line 161
    invoke-static {v0, v6}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 162
    .line 163
    .line 164
    move-result-object v11

    .line 165
    invoke-virtual {v5}, LA11/a;->q1()F

    .line 166
    .line 167
    .line 168
    move-result v15

    .line 169
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 170
    .line 171
    .line 172
    move-result-object v0

    .line 173
    invoke-virtual {v0}, LA11/b;->a()F

    .line 174
    .line 175
    .line 176
    move-result v12

    .line 177
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 178
    .line 179
    .line 180
    move-result-object v0

    .line 181
    invoke-virtual {v0}, LA11/b;->a()F

    .line 182
    .line 183
    .line 184
    move-result v14

    .line 185
    const/16 v16, 0x2

    .line 186
    .line 187
    const/16 v17, 0x0

    .line 188
    .line 189
    const/4 v13, 0x0

    .line 190
    invoke-static/range {v11 .. v17}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 191
    .line 192
    .line 193
    move-result-object v0

    .line 194
    invoke-static {v0, v2, v4, v3}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 195
    .line 196
    .line 197
    move-result-object v0

    .line 198
    invoke-virtual {v5}, LA11/a;->U()F

    .line 199
    .line 200
    .line 201
    move-result v2

    .line 202
    invoke-static {v2}, LR/i;->f(F)LR/h;

    .line 203
    .line 204
    .line 205
    move-result-object v2

    .line 206
    invoke-static {v0, v2}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-static {v3, v10, v1, v4}, Lp11/a;->b(Lorg/xbet/uikit/compose/components/skeleton/model/DsSkeletonContentBackgroundStyle;Landroidx/compose/runtime/j;II)Lp11/c;

    .line 211
    .line 212
    .line 213
    move-result-object v2

    .line 214
    invoke-static {v0, v2}, Lo11/a;->a(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;

    .line 215
    .line 216
    .line 217
    move-result-object v0

    .line 218
    invoke-static {v0, v10, v1}, Landroidx/compose/foundation/layout/BoxKt;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 219
    .line 220
    .line 221
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 222
    .line 223
    .line 224
    goto/16 :goto_3

    .line 225
    .line 226
    :cond_6
    const v0, -0x33c7c77

    .line 227
    .line 228
    .line 229
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 230
    .line 231
    .line 232
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 233
    .line 234
    sget-object v5, LA11/a;->a:LA11/a;

    .line 235
    .line 236
    invoke-virtual {v5}, LA11/a;->s0()F

    .line 237
    .line 238
    .line 239
    move-result v6

    .line 240
    invoke-static {v0, v6}, Landroidx/compose/foundation/layout/SizeKt;->i(Landroidx/compose/ui/l;F)Landroidx/compose/ui/l;

    .line 241
    .line 242
    .line 243
    move-result-object v11

    .line 244
    invoke-virtual {v5}, LA11/a;->q1()F

    .line 245
    .line 246
    .line 247
    move-result v15

    .line 248
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 249
    .line 250
    .line 251
    move-result-object v0

    .line 252
    invoke-virtual {v0}, LA11/b;->a()F

    .line 253
    .line 254
    .line 255
    move-result v12

    .line 256
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 257
    .line 258
    .line 259
    move-result-object v0

    .line 260
    invoke-virtual {v0}, LA11/b;->a()F

    .line 261
    .line 262
    .line 263
    move-result v14

    .line 264
    const/16 v16, 0x2

    .line 265
    .line 266
    const/16 v17, 0x0

    .line 267
    .line 268
    const/4 v13, 0x0

    .line 269
    invoke-static/range {v11 .. v17}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 270
    .line 271
    .line 272
    move-result-object v0

    .line 273
    invoke-static {v0, v2, v4, v3}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 274
    .line 275
    .line 276
    move-result-object v0

    .line 277
    invoke-virtual {v5}, LA11/a;->U()F

    .line 278
    .line 279
    .line 280
    move-result v2

    .line 281
    invoke-static {v2}, LR/i;->f(F)LR/h;

    .line 282
    .line 283
    .line 284
    move-result-object v2

    .line 285
    invoke-static {v0, v2}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 286
    .line 287
    .line 288
    move-result-object v0

    .line 289
    invoke-static {v3, v10, v1, v4}, Lp11/a;->b(Lorg/xbet/uikit/compose/components/skeleton/model/DsSkeletonContentBackgroundStyle;Landroidx/compose/runtime/j;II)Lp11/c;

    .line 290
    .line 291
    .line 292
    move-result-object v2

    .line 293
    invoke-static {v0, v2}, Lo11/a;->a(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;

    .line 294
    .line 295
    .line 296
    move-result-object v0

    .line 297
    invoke-static {v0, v10, v1}, Landroidx/compose/foundation/layout/BoxKt;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;I)V

    .line 298
    .line 299
    .line 300
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 301
    .line 302
    .line 303
    goto :goto_3

    .line 304
    :cond_7
    const v0, -0x34a2de3

    .line 305
    .line 306
    .line 307
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 308
    .line 309
    .line 310
    sget-object v2, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 311
    .line 312
    sget-object v0, LA11/a;->a:LA11/a;

    .line 313
    .line 314
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 315
    .line 316
    .line 317
    move-result v4

    .line 318
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 319
    .line 320
    .line 321
    move-result v6

    .line 322
    invoke-static {v10, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 323
    .line 324
    .line 325
    move-result-object v1

    .line 326
    invoke-virtual {v1}, LA11/b;->a()F

    .line 327
    .line 328
    .line 329
    move-result v3

    .line 330
    const/4 v7, 0x4

    .line 331
    const/4 v8, 0x0

    .line 332
    const/4 v5, 0x0

    .line 333
    invoke-static/range {v2 .. v8}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 334
    .line 335
    .line 336
    move-result-object v1

    .line 337
    sget-object v2, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 338
    .line 339
    invoke-virtual {v0}, LA11/a;->L1()F

    .line 340
    .line 341
    .line 342
    move-result v0

    .line 343
    invoke-virtual {v2, v0}, Landroidx/compose/foundation/layout/Arrangement;->o(F)Landroidx/compose/foundation/layout/Arrangement$f;

    .line 344
    .line 345
    .line 346
    move-result-object v4

    .line 347
    const v0, 0x6e3c21fe

    .line 348
    .line 349
    .line 350
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 351
    .line 352
    .line 353
    invoke-interface {v10}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 354
    .line 355
    .line 356
    move-result-object v0

    .line 357
    sget-object v2, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 358
    .line 359
    invoke-virtual {v2}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 360
    .line 361
    .line 362
    move-result-object v2

    .line 363
    if-ne v0, v2, :cond_8

    .line 364
    .line 365
    new-instance v0, LIN0/b;

    .line 366
    .line 367
    invoke-direct {v0}, LIN0/b;-><init>()V

    .line 368
    .line 369
    .line 370
    invoke-interface {v10, v0}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 371
    .line 372
    .line 373
    :cond_8
    move-object v9, v0

    .line 374
    check-cast v9, Lkotlin/jvm/functions/Function1;

    .line 375
    .line 376
    invoke-interface {v10}, Landroidx/compose/runtime/j;->q()V

    .line 377
    .line 378
    .line 379
    const/high16 v11, 0x30c00000

    .line 380
    .line 381
    const/16 v12, 0x16e

    .line 382
    .line 383
    move-object v0, v1

    .line 384
    const/4 v1, 0x0

    .line 385
    const/4 v2, 0x0

    .line 386
    const/4 v3, 0x0

    .line 387
    const/4 v5, 0x0

    .line 388
    const/4 v6, 0x0

    .line 389
    const/4 v7, 0x0

    .line 390
    const/4 v8, 0x0

    .line 391
    invoke-static/range {v0 .. v12}, Landroidx/compose/foundation/lazy/LazyDslKt;->e(Landroidx/compose/ui/l;Landroidx/compose/foundation/lazy/LazyListState;Landroidx/compose/foundation/layout/Y;ZLandroidx/compose/foundation/layout/Arrangement$e;Landroidx/compose/ui/e$c;Landroidx/compose/foundation/gestures/q;ZLandroidx/compose/foundation/O;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 392
    .line 393
    .line 394
    invoke-interface/range {p3 .. p3}, Landroidx/compose/runtime/j;->q()V

    .line 395
    .line 396
    .line 397
    :goto_3
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 398
    .line 399
    .line 400
    move-result v0

    .line 401
    if-eqz v0, :cond_9

    .line 402
    .line 403
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 404
    .line 405
    .line 406
    :cond_9
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/lazy/c;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    check-cast p3, Landroidx/compose/runtime/j;

    .line 10
    .line 11
    check-cast p4, Ljava/lang/Number;

    .line 12
    .line 13
    invoke-virtual {p4}, Ljava/lang/Number;->intValue()I

    .line 14
    .line 15
    .line 16
    move-result p4

    .line 17
    invoke-virtual {p0, p1, p2, p3, p4}, LIN0/a$b;->b(Landroidx/compose/foundation/lazy/c;ILandroidx/compose/runtime/j;I)V

    .line 18
    .line 19
    .line 20
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p1
.end method
