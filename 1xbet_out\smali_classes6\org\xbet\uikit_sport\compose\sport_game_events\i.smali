.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ls31/a$b;

.field public final synthetic b:Z

.field public final synthetic c:Landroidx/compose/ui/l;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:I

.field public final synthetic f:I


# direct methods
.method public synthetic constructor <init>(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->a:Ls31/a$b;

    iput-boolean p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->b:Z

    iput-object p3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->c:Landroidx/compose/ui/l;

    iput-object p4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->d:Lkotlin/jvm/functions/Function1;

    iput p5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->e:I

    iput p6, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->f:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->a:Ls31/a$b;

    iget-boolean v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->b:Z

    iget-object v2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->c:Landroidx/compose/ui/l;

    iget-object v3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->d:Lkotlin/jvm/functions/Function1;

    iget v4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->e:I

    iget v5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/i;->f:I

    move-object v6, p1

    check-cast v6, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v7

    invoke-static/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->d(Ls31/a$b;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
