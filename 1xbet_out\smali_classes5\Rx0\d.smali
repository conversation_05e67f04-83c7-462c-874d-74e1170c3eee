.class public final LRx0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u000e\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u001a!\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\t\u001a!\u0010\r\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000e\u001a!\u0010\u000f\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u000e\u001a!\u0010\u0010\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000e\u001a!\u0010\u0011\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u000e\u001a!\u0010\u0012\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u000e\u001a!\u0010\u0013\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u000e\u001a)\u0010\u0014\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u001a)\u0010\u0016\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0015\u001a)\u0010\u0017\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0015\u001a)\u0010\u0018\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0015\u001a)\u0010\u0019\u001a\u00020\u000c*\u0008\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0015\u00a8\u0006\u001a"
    }
    d2 = {
        "LZx0/k;",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/special_event/impl/tournament/presentation/e;",
        "q",
        "(LZx0/k;LHX0/e;)Lorg/xbet/special_event/impl/tournament/presentation/e;",
        "",
        "LVX0/i;",
        "n",
        "(LZx0/k;LHX0/e;)Ljava/util/List;",
        "",
        "contentUiModel",
        "",
        "i",
        "(Ljava/util/List;LZx0/k;)V",
        "d",
        "g",
        "l",
        "k",
        "m",
        "h",
        "(Ljava/util/List;LHX0/e;LZx0/k;)V",
        "c",
        "e",
        "f",
        "j",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LHX0/e;)LVX0/i;
    .locals 0

    .line 1
    invoke-static {p0}, LRx0/d;->p(LHX0/e;)LVX0/i;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LHX0/e;)LVX0/i;
    .locals 0

    .line 1
    invoke-static {p0}, LRx0/d;->o(LHX0/e;)LVX0/i;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Ljava/util/List;LHX0/e;LZx0/k;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LHX0/e;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LZx0/k;->i()Ldy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Ldy0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lax0/a;

    .line 16
    .line 17
    sget v1, Lpb/k;->popular_heroes:I

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    new-array v3, v2, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {p1, v1, v3}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-direct {v0, v1}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    new-instance v0, Lgq0/a;

    .line 33
    .line 34
    sget v1, Lpb/k;->hero_title:I

    .line 35
    .line 36
    new-array v3, v2, [Ljava/lang/Object;

    .line 37
    .line 38
    invoke-interface {p1, v1, v3}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    sget v3, Lpb/k;->picks_title:I

    .line 43
    .line 44
    new-array v4, v2, [Ljava/lang/Object;

    .line 45
    .line 46
    invoke-interface {p1, v3, v4}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    sget v4, Lpb/k;->wins_title:I

    .line 51
    .line 52
    new-array v2, v2, [Ljava/lang/Object;

    .line 53
    .line 54
    invoke-interface {p1, v4, v2}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-direct {v0, v1, v3, p1}, Lgq0/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 62
    .line 63
    .line 64
    invoke-virtual {p2}, LZx0/k;->i()Ldy0/a;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-virtual {p1}, Ldy0/a;->c()Ljava/util/List;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 73
    .line 74
    .line 75
    :cond_0
    return-void
.end method

.method public static final d(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->j()Ley0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Ley0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->j()Ley0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Ley0/a;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final e(Ljava/util/List;LHX0/e;LZx0/k;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LHX0/e;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LZx0/k;->k()Lfy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lfy0/a;->d()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lax0/a;

    .line 16
    .line 17
    sget v1, Lpb/k;->popular_champions:I

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    new-array v3, v2, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {p1, v1, v3}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-direct {v0, v1}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    new-instance v3, LCq0/a;

    .line 33
    .line 34
    sget v0, Lpb/k;->number:I

    .line 35
    .line 36
    new-array v1, v2, [Ljava/lang/Object;

    .line 37
    .line 38
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v4

    .line 42
    sget v0, Lpb/k;->champion:I

    .line 43
    .line 44
    new-array v1, v2, [Ljava/lang/Object;

    .line 45
    .line 46
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v5

    .line 50
    sget v0, Lpb/k;->lol_picks:I

    .line 51
    .line 52
    new-array v1, v2, [Ljava/lang/Object;

    .line 53
    .line 54
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v6

    .line 58
    sget v0, Lpb/k;->lol_tier:I

    .line 59
    .line 60
    new-array v1, v2, [Ljava/lang/Object;

    .line 61
    .line 62
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v7

    .line 66
    sget v0, Lpb/k;->lol_lane:I

    .line 67
    .line 68
    new-array v1, v2, [Ljava/lang/Object;

    .line 69
    .line 70
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v8

    .line 74
    sget v0, Lpb/k;->lol_win_rate:I

    .line 75
    .line 76
    new-array v1, v2, [Ljava/lang/Object;

    .line 77
    .line 78
    invoke-interface {p1, v0, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v9

    .line 82
    invoke-direct/range {v3 .. v9}, LCq0/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    invoke-interface {p0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    invoke-virtual {p2}, LZx0/k;->k()Lfy0/a;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    invoke-virtual {p1}, Lfy0/a;->d()Ljava/util/List;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 97
    .line 98
    .line 99
    :cond_0
    return-void
.end method

.method public static final f(Ljava/util/List;LHX0/e;LZx0/k;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LHX0/e;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LZx0/k;->m()Lgy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lgy0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lax0/a;

    .line 16
    .line 17
    sget v1, Lpb/k;->cs2_tournament_winrate_title:I

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    new-array v2, v2, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {p1, v1, v2}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-direct {v0, p1}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    invoke-virtual {p2}, LZx0/k;->m()Lgy0/a;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {p1}, Lgy0/a;->c()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 41
    .line 42
    .line 43
    :cond_0
    return-void
.end method

.method public static final g(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->n()Lhy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lhy0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->n()Lhy0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Lhy0/a;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final h(Ljava/util/List;LHX0/e;LZx0/k;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LHX0/e;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LZx0/k;->u()Lmy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-virtual {p2}, Lmy0/a;->h()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p2}, Lmy0/a;->h()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {p0, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 20
    .line 21
    .line 22
    invoke-static {p2}, LQx0/b;->l(Lmy0/a;)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_0

    .line 27
    .line 28
    sget-object p1, LYw0/a;->a:LYw0/a;

    .line 29
    .line 30
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    return-void

    .line 34
    :cond_0
    new-instance v0, Lax0/a;

    .line 35
    .line 36
    sget v1, Lpb/k;->my_special_event_games:I

    .line 37
    .line 38
    const/4 v2, 0x0

    .line 39
    new-array v2, v2, [Ljava/lang/Object;

    .line 40
    .line 41
    invoke-interface {p1, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    invoke-direct {v0, p1}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 46
    .line 47
    .line 48
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 49
    .line 50
    .line 51
    invoke-virtual {p2}, Lmy0/a;->f()Lny0/c;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    invoke-virtual {p1}, Lny0/c;->e()Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-virtual {p2}, Lmy0/a;->e()Lny0/b;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {v0}, Lny0/b;->e()Ljava/util/List;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    invoke-virtual {p2}, Lmy0/a;->d()Lny0/a;

    .line 72
    .line 73
    .line 74
    move-result-object p2

    .line 75
    invoke-virtual {p2}, Lny0/a;->f()Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    invoke-static {p1, p2}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    const/4 p2, 0x2

    .line 84
    invoke-static {p1, p2}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 89
    .line 90
    .line 91
    sget-object p1, LWw0/a;->a:LWw0/a;

    .line 92
    .line 93
    invoke-interface {p0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 94
    .line 95
    .line 96
    :cond_1
    return-void
.end method

.method public static final i(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->q()Ljy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Ljy0/a;->d()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->q()Ljy0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Ljy0/a;->d()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final j(Ljava/util/List;LHX0/e;LZx0/k;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LHX0/e;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LZx0/k;->r()Lky0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lky0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    new-instance v0, Lax0/a;

    .line 16
    .line 17
    sget v1, Lpb/k;->social_networks:I

    .line 18
    .line 19
    const/4 v2, 0x0

    .line 20
    new-array v2, v2, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {p1, v1, v2}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-direct {v0, p1}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    invoke-virtual {p2}, LZx0/k;->r()Lky0/a;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {p1}, Lky0/a;->c()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 41
    .line 42
    .line 43
    :cond_0
    return-void
.end method

.method public static final k(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->s()Lly0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lly0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->s()Lly0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Lly0/a;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final l(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->v()Loy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Loy0/a;->c()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->v()Loy0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Loy0/a;->c()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final m(Ljava/util/List;LZx0/k;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;",
            "LZx0/k;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, LZx0/k;->x()Lpy0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lpy0/a;->d()Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {p1}, LZx0/k;->x()Lpy0/a;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Lpy0/a;->d()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method

.method public static final n(LZx0/k;LHX0/e;)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LZx0/k;",
            "LHX0/e;",
            ")",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, LQx0/b;->b(LZx0/k;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Ljava/util/ArrayList;

    .line 10
    .line 11
    const/16 v3, 0xa

    .line 12
    .line 13
    invoke-static {v1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 18
    .line 19
    .line 20
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 25
    .line 26
    .line 27
    move-result v3

    .line 28
    if-eqz v3, :cond_14

    .line 29
    .line 30
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    check-cast v3, LZx0/i;

    .line 35
    .line 36
    instance-of v4, v3, LZx0/i$a$f;

    .line 37
    .line 38
    if-eqz v4, :cond_0

    .line 39
    .line 40
    invoke-static {v0, p0}, LRx0/d;->d(Ljava/util/List;LZx0/k;)V

    .line 41
    .line 42
    .line 43
    goto/16 :goto_1

    .line 44
    .line 45
    :cond_0
    instance-of v4, v3, LZx0/i$a$i;

    .line 46
    .line 47
    if-eqz v4, :cond_1

    .line 48
    .line 49
    invoke-static {v0, p0}, LRx0/d;->g(Ljava/util/List;LZx0/k;)V

    .line 50
    .line 51
    .line 52
    goto/16 :goto_1

    .line 53
    .line 54
    :cond_1
    instance-of v4, v3, LZx0/i$a$k;

    .line 55
    .line 56
    if-eqz v4, :cond_2

    .line 57
    .line 58
    invoke-static {v0, p0}, LRx0/d;->i(Ljava/util/List;LZx0/k;)V

    .line 59
    .line 60
    .line 61
    goto/16 :goto_1

    .line 62
    .line 63
    :cond_2
    instance-of v4, v3, LZx0/i$a$m;

    .line 64
    .line 65
    if-eqz v4, :cond_3

    .line 66
    .line 67
    invoke-static {v0, p0}, LRx0/d;->k(Ljava/util/List;LZx0/k;)V

    .line 68
    .line 69
    .line 70
    goto/16 :goto_1

    .line 71
    .line 72
    :cond_3
    instance-of v4, v3, LZx0/i$a$n;

    .line 73
    .line 74
    if-eqz v4, :cond_4

    .line 75
    .line 76
    invoke-static {v0, p1, p0}, LRx0/d;->h(Ljava/util/List;LHX0/e;LZx0/k;)V

    .line 77
    .line 78
    .line 79
    goto/16 :goto_1

    .line 80
    .line 81
    :cond_4
    instance-of v4, v3, LZx0/i$a$o;

    .line 82
    .line 83
    if-eqz v4, :cond_5

    .line 84
    .line 85
    invoke-static {v0, p0}, LRx0/d;->l(Ljava/util/List;LZx0/k;)V

    .line 86
    .line 87
    .line 88
    goto/16 :goto_1

    .line 89
    .line 90
    :cond_5
    instance-of v4, v3, LZx0/i$a$p;

    .line 91
    .line 92
    if-eqz v4, :cond_6

    .line 93
    .line 94
    invoke-static {v0, p0}, LRx0/d;->m(Ljava/util/List;LZx0/k;)V

    .line 95
    .line 96
    .line 97
    goto/16 :goto_1

    .line 98
    .line 99
    :cond_6
    instance-of v4, v3, LZx0/i$b$a;

    .line 100
    .line 101
    if-eqz v4, :cond_7

    .line 102
    .line 103
    invoke-virtual {p0}, LZx0/k;->o()LUw0/a;

    .line 104
    .line 105
    .line 106
    move-result-object v3

    .line 107
    if-eqz v3, :cond_12

    .line 108
    .line 109
    invoke-virtual {p0}, LZx0/k;->o()LUw0/a;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 114
    .line 115
    .line 116
    goto/16 :goto_1

    .line 117
    .line 118
    :cond_7
    instance-of v4, v3, LZx0/i$b$b;

    .line 119
    .line 120
    if-eqz v4, :cond_8

    .line 121
    .line 122
    invoke-virtual {p0}, LZx0/k;->t()LUw0/a;

    .line 123
    .line 124
    .line 125
    move-result-object v3

    .line 126
    if-eqz v3, :cond_12

    .line 127
    .line 128
    invoke-virtual {p0}, LZx0/k;->t()LUw0/a;

    .line 129
    .line 130
    .line 131
    move-result-object v3

    .line 132
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 133
    .line 134
    .line 135
    goto/16 :goto_1

    .line 136
    .line 137
    :cond_8
    instance-of v4, v3, LZx0/i$b$c;

    .line 138
    .line 139
    if-eqz v4, :cond_9

    .line 140
    .line 141
    invoke-virtual {p0}, LZx0/k;->w()LUw0/a;

    .line 142
    .line 143
    .line 144
    move-result-object v3

    .line 145
    if-eqz v3, :cond_12

    .line 146
    .line 147
    invoke-virtual {p0}, LZx0/k;->w()LUw0/a;

    .line 148
    .line 149
    .line 150
    move-result-object v3

    .line 151
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 152
    .line 153
    .line 154
    goto/16 :goto_1

    .line 155
    .line 156
    :cond_9
    instance-of v4, v3, LZx0/i$a$a;

    .line 157
    .line 158
    if-eqz v4, :cond_a

    .line 159
    .line 160
    invoke-virtual {p0}, LZx0/k;->c()Lay0/a;

    .line 161
    .line 162
    .line 163
    move-result-object v3

    .line 164
    invoke-virtual {v3}, Lay0/a;->c()Lkp0/a;

    .line 165
    .line 166
    .line 167
    move-result-object v3

    .line 168
    new-instance v4, LRx0/b;

    .line 169
    .line 170
    invoke-direct {v4, p1}, LRx0/b;-><init>(LHX0/e;)V

    .line 171
    .line 172
    .line 173
    invoke-static {v0, v3, v4}, Lnp0/b;->a(Ljava/util/List;Lkp0/a;Lkotlin/jvm/functions/Function0;)V

    .line 174
    .line 175
    .line 176
    goto/16 :goto_1

    .line 177
    .line 178
    :cond_a
    instance-of v4, v3, LZx0/i$a$e;

    .line 179
    .line 180
    if-eqz v4, :cond_b

    .line 181
    .line 182
    invoke-static {v0, p1, p0}, LRx0/d;->c(Ljava/util/List;LHX0/e;LZx0/k;)V

    .line 183
    .line 184
    .line 185
    goto/16 :goto_1

    .line 186
    .line 187
    :cond_b
    instance-of v4, v3, LZx0/i$a$g;

    .line 188
    .line 189
    if-eqz v4, :cond_c

    .line 190
    .line 191
    invoke-static {v0, p1, p0}, LRx0/d;->e(Ljava/util/List;LHX0/e;LZx0/k;)V

    .line 192
    .line 193
    .line 194
    goto/16 :goto_1

    .line 195
    .line 196
    :cond_c
    instance-of v4, v3, LZx0/i$a$d;

    .line 197
    .line 198
    const/4 v5, 0x0

    .line 199
    if-eqz v4, :cond_d

    .line 200
    .line 201
    invoke-virtual {p0}, LZx0/k;->h()Lay0/b;

    .line 202
    .line 203
    .line 204
    move-result-object v3

    .line 205
    invoke-virtual {v3}, Lay0/b;->b()Ljava/util/List;

    .line 206
    .line 207
    .line 208
    move-result-object v3

    .line 209
    new-instance v4, Lax0/a;

    .line 210
    .line 211
    sget v6, Lpb/k;->cs2_tournament_top_players:I

    .line 212
    .line 213
    new-array v5, v5, [Ljava/lang/Object;

    .line 214
    .line 215
    invoke-interface {p1, v6, v5}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 216
    .line 217
    .line 218
    move-result-object v5

    .line 219
    invoke-direct {v4, v5}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 220
    .line 221
    .line 222
    invoke-static {v0, v3, v4}, Lsp0/d;->a(Ljava/util/List;Ljava/util/List;LVX0/i;)V

    .line 223
    .line 224
    .line 225
    goto :goto_1

    .line 226
    :cond_d
    instance-of v4, v3, LZx0/i$a$j;

    .line 227
    .line 228
    if-eqz v4, :cond_e

    .line 229
    .line 230
    invoke-virtual {p0}, LZx0/k;->p()Liy0/a;

    .line 231
    .line 232
    .line 233
    move-result-object v3

    .line 234
    invoke-virtual {v3}, Liy0/a;->c()Lxp0/b;

    .line 235
    .line 236
    .line 237
    move-result-object v3

    .line 238
    new-instance v4, LRx0/c;

    .line 239
    .line 240
    invoke-direct {v4, p1}, LRx0/c;-><init>(LHX0/e;)V

    .line 241
    .line 242
    .line 243
    invoke-static {v0, v3, v4}, Lqp0/a;->a(Ljava/util/List;Lxp0/b;Lkotlin/jvm/functions/Function0;)V

    .line 244
    .line 245
    .line 246
    goto :goto_1

    .line 247
    :cond_e
    instance-of v4, v3, LZx0/i$a$h;

    .line 248
    .line 249
    if-eqz v4, :cond_f

    .line 250
    .line 251
    invoke-static {v0, p1, p0}, LRx0/d;->f(Ljava/util/List;LHX0/e;LZx0/k;)V

    .line 252
    .line 253
    .line 254
    goto :goto_1

    .line 255
    :cond_f
    instance-of v4, v3, LZx0/i$a$c;

    .line 256
    .line 257
    if-eqz v4, :cond_10

    .line 258
    .line 259
    invoke-virtual {p0}, LZx0/k;->g()Lcy0/a;

    .line 260
    .line 261
    .line 262
    move-result-object v3

    .line 263
    invoke-virtual {v3}, Lcy0/a;->b()LAp0/b;

    .line 264
    .line 265
    .line 266
    move-result-object v3

    .line 267
    new-instance v4, Lax0/a;

    .line 268
    .line 269
    sget v6, Lpb/k;->teams:I

    .line 270
    .line 271
    new-array v5, v5, [Ljava/lang/Object;

    .line 272
    .line 273
    invoke-interface {p1, v6, v5}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 274
    .line 275
    .line 276
    move-result-object v5

    .line 277
    invoke-direct {v4, v5}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 278
    .line 279
    .line 280
    invoke-static {v0, v3, v4}, Lpp0/a;->a(Ljava/util/List;LAp0/b;LVX0/i;)V

    .line 281
    .line 282
    .line 283
    goto :goto_1

    .line 284
    :cond_10
    instance-of v4, v3, LZx0/i$a$l;

    .line 285
    .line 286
    if-eqz v4, :cond_11

    .line 287
    .line 288
    invoke-static {v0, p1, p0}, LRx0/d;->j(Ljava/util/List;LHX0/e;LZx0/k;)V

    .line 289
    .line 290
    .line 291
    goto :goto_1

    .line 292
    :cond_11
    instance-of v3, v3, LZx0/i$a$b;

    .line 293
    .line 294
    if-eqz v3, :cond_13

    .line 295
    .line 296
    invoke-virtual {p0}, LZx0/k;->f()Lby0/a;

    .line 297
    .line 298
    .line 299
    move-result-object v3

    .line 300
    invoke-virtual {v3}, Lby0/a;->c()Lwp0/f;

    .line 301
    .line 302
    .line 303
    move-result-object v3

    .line 304
    new-instance v4, Lax0/a;

    .line 305
    .line 306
    sget v6, Lpb/k;->group_stage:I

    .line 307
    .line 308
    new-array v5, v5, [Ljava/lang/Object;

    .line 309
    .line 310
    invoke-interface {p1, v6, v5}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 311
    .line 312
    .line 313
    move-result-object v5

    .line 314
    invoke-direct {v4, v5}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 315
    .line 316
    .line 317
    invoke-static {v0, v3, p1, v4}, Lop0/f;->a(Ljava/util/List;Lwp0/f;LHX0/e;LVX0/i;)V

    .line 318
    .line 319
    .line 320
    :cond_12
    :goto_1
    sget-object v3, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 321
    .line 322
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 323
    .line 324
    .line 325
    goto/16 :goto_0

    .line 326
    .line 327
    :cond_13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 328
    .line 329
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 330
    .line 331
    .line 332
    throw p0

    .line 333
    :cond_14
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 334
    .line 335
    .line 336
    move-result-object p0

    .line 337
    return-object p0
.end method

.method public static final o(LHX0/e;)LVX0/i;
    .locals 3

    .line 1
    new-instance v0, Lax0/a;

    .line 2
    .line 3
    sget v1, Lpb/k;->about_tournament:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/Object;

    .line 7
    .line 8
    invoke-interface {p0, v1, v2}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-direct {v0, p0}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final p(LHX0/e;)LVX0/i;
    .locals 3

    .line 1
    new-instance v0, Lax0/a;

    .line 2
    .line 3
    sget v1, Lpb/k;->tournament_prize_pool:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    new-array v2, v2, [Ljava/lang/Object;

    .line 7
    .line 8
    invoke-interface {p0, v1, v2}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-direct {v0, p0}, Lax0/a;-><init>(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final q(LZx0/k;LHX0/e;)Lorg/xbet/special_event/impl/tournament/presentation/e;
    .locals 2
    .param p0    # LZx0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LZx0/k;->l()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->c()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget-object v1, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;->NO_ERROR:Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel$TournamentLottieErrorType;

    .line 10
    .line 11
    if-eq v0, v1, :cond_0

    .line 12
    .line 13
    new-instance p1, Lorg/xbet/special_event/impl/tournament/presentation/e$b;

    .line 14
    .line 15
    invoke-virtual {p0}, LZx0/k;->l()Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/tournament/presentation/model/lottie/TournamentLottieStateModel;->b()Lorg/xbet/uikit/components/lottie/a;

    .line 20
    .line 21
    .line 22
    move-result-object p0

    .line 23
    invoke-direct {p1, p0}, Lorg/xbet/special_event/impl/tournament/presentation/e$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 24
    .line 25
    .line 26
    return-object p1

    .line 27
    :cond_0
    invoke-static {p0}, LQx0/b;->g(LZx0/k;)Z

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/e$a;

    .line 34
    .line 35
    invoke-static {p0, p1}, LRx0/d;->n(LZx0/k;LHX0/e;)Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/tournament/presentation/e$a;-><init>(Ljava/util/List;)V

    .line 40
    .line 41
    .line 42
    return-object v0

    .line 43
    :cond_1
    sget-object p0, Lorg/xbet/special_event/impl/tournament/presentation/e$c;->a:Lorg/xbet/special_event/impl/tournament/presentation/e$c;

    .line 44
    .line 45
    return-object p0
.end method
