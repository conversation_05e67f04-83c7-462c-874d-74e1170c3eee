.class final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.domain.usecases.RemoveFavoriteUseCaseImpl$invoke$2"
    f = "RemoveFavoriteUseCaseImpl.kt"
    l = {
        0x13
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $gameId:J

.field final synthetic $subcategoryId:I

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;JZILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;",
            "JZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$gameId:J

    iput-boolean p4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$brandsApi:Z

    iput p5, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$subcategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$gameId:J

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$brandsApi:Z

    iget v5, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$subcategoryId:I

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;JZILkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;->b(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl;)Lu81/b;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$gameId:J

    .line 34
    .line 35
    iget-boolean v6, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$brandsApi:Z

    .line 36
    .line 37
    iget v7, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->$subcategoryId:I

    .line 38
    .line 39
    iput v2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/RemoveFavoriteUseCaseImpl$invoke$2;->label:I

    .line 40
    .line 41
    move-object v8, p0

    .line 42
    invoke-interface/range {v3 .. v8}, Lu81/b;->k(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    if-ne p1, v0, :cond_2

    .line 47
    .line 48
    return-object v0

    .line 49
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 50
    .line 51
    return-object p1
.end method
