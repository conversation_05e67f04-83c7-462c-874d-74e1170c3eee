.class public final synthetic LHx0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LFx0/a;


# direct methods
.method public synthetic constructor <init>(LFx0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LHx0/b;->a:LFx0/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LHx0/b;->a:LFx0/a;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/extendedrating/viewholder/TournamentExtendedRatingViewHolderKt;->a(LFx0/a;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
