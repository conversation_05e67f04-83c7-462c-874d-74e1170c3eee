.class public final synthetic Lorg/xplatform/aggregator/impl/new_games/presentation/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/f;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/f;->a:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->t3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
