.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;
.super Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
        "<init>",
        "()V",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;

    invoke-direct {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;-><init>()V

    sput-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 3
    .line 4
    .line 5
    return-void
.end method
