.class public Lgd1/b;
.super Lru/tinkoff/decoro/watchers/a;
.source "SourceFile"


# instance fields
.field public j:Lru/tinkoff/decoro/MaskImpl;


# direct methods
.method public constructor <init>(Lru/tinkoff/decoro/MaskImpl;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lru/tinkoff/decoro/watchers/a;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lgd1/b;->k(Lru/tinkoff/decoro/MaskImpl;)V

    .line 5
    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public a()Lru/tinkoff/decoro/Mask;
    .locals 2
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    new-instance v0, Lru/tinkoff/decoro/MaskImpl;

    .line 2
    .line 3
    iget-object v1, p0, Lgd1/b;->j:Lru/tinkoff/decoro/MaskImpl;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lru/tinkoff/decoro/MaskImpl;-><init>(Lru/tinkoff/decoro/MaskImpl;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public k(Lru/tinkoff/decoro/MaskImpl;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lgd1/b;->j:Lru/tinkoff/decoro/MaskImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, Lru/tinkoff/decoro/watchers/a;->g()V

    .line 4
    .line 5
    .line 6
    return-void
.end method
