.class public final synthetic LU01/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

.field public final synthetic b:Landroid/util/AttributeSet;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Landroid/util/AttributeSet;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/C;->a:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    iput-object p2, p0, LU01/C;->b:Landroid/util/AttributeSet;

    iput p3, p0, LU01/C;->c:I

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LU01/C;->a:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    iget-object v1, p0, LU01/C;->b:Landroid/util/AttributeSet;

    iget v2, p0, LU01/C;->c:I

    invoke-static {v0, v1, v2}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->z(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Landroid/util/AttributeSet;I)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
