.class public final synthetic LrA0/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LZz0/a;

.field public final synthetic c:LYz0/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;LZz0/a;LYz0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/j;->a:LB4/a;

    iput-object p2, p0, LrA0/j;->b:LZz0/a;

    iput-object p3, p0, LrA0/j;->c:LYz0/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LrA0/j;->a:LB4/a;

    iget-object v1, p0, LrA0/j;->b:LZz0/a;

    iget-object v2, p0, LrA0/j;->c:LYz0/a;

    invoke-static {v0, v1, v2}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/LineStatisticViewHolderKt;->d(LB4/a;LZz0/a;LYz0/a;)Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/LineStatisticViewHolderKt$a;

    move-result-object v0

    return-object v0
.end method
