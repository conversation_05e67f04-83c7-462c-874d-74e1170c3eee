.class public final synthetic Lorg/xplatform/aggregator/impl/core/presentation/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

.field public final synthetic b:Lorg/xplatform/banners/api/domain/models/BannerModel;

.field public final synthetic c:I

.field public final synthetic d:Lkotlinx/coroutines/N;

.field public final synthetic e:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->a:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->b:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iput p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->c:I

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->d:Lkotlinx/coroutines/N;

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->e:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->a:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->b:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iget v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->c:I

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->d:Lkotlinx/coroutines/N;

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/core/presentation/a;->e:Lkotlin/jvm/functions/Function1;

    invoke-static {v0, v1, v2, v3, v4}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->a(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
