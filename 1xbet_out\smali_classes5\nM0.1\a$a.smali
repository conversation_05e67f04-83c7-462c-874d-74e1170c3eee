.class public final LnM0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnM0/h$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LnM0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LnM0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LnM0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;JJJLkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;)LnM0/h;
    .locals 30

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    invoke-static/range {p19 .. p20}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    invoke-static/range {p21 .. p22}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    invoke-static/range {p23 .. p24}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    invoke-static/range {p25 .. p25}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    invoke-static/range {p26 .. p26}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    invoke-static/range {p27 .. p27}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    invoke-static/range {p28 .. p28}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    invoke-static/range {p29 .. p29}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    invoke-static/range {p30 .. p30}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    new-instance v1, LnM0/a$b;

    .line 95
    .line 96
    invoke-static/range {p19 .. p20}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 97
    .line 98
    .line 99
    move-result-object v20

    .line 100
    invoke-static/range {p21 .. p22}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 101
    .line 102
    .line 103
    move-result-object v21

    .line 104
    invoke-static/range {p23 .. p24}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 105
    .line 106
    .line 107
    move-result-object v22

    .line 108
    const/16 v29, 0x0

    .line 109
    .line 110
    move-object/from16 v2, p1

    .line 111
    .line 112
    move-object/from16 v3, p2

    .line 113
    .line 114
    move-object/from16 v4, p3

    .line 115
    .line 116
    move-object/from16 v5, p4

    .line 117
    .line 118
    move-object/from16 v6, p5

    .line 119
    .line 120
    move-object/from16 v7, p6

    .line 121
    .line 122
    move-object/from16 v8, p7

    .line 123
    .line 124
    move-object/from16 v9, p8

    .line 125
    .line 126
    move-object/from16 v10, p9

    .line 127
    .line 128
    move-object/from16 v11, p10

    .line 129
    .line 130
    move-object/from16 v12, p11

    .line 131
    .line 132
    move-object/from16 v13, p12

    .line 133
    .line 134
    move-object/from16 v14, p13

    .line 135
    .line 136
    move-object/from16 v15, p14

    .line 137
    .line 138
    move-object/from16 v16, p15

    .line 139
    .line 140
    move-object/from16 v17, p16

    .line 141
    .line 142
    move-object/from16 v18, p17

    .line 143
    .line 144
    move-object/from16 v19, p18

    .line 145
    .line 146
    move-object/from16 v23, p25

    .line 147
    .line 148
    move-object/from16 v24, p26

    .line 149
    .line 150
    move-object/from16 v25, p27

    .line 151
    .line 152
    move-object/from16 v26, p28

    .line 153
    .line 154
    move-object/from16 v27, p29

    .line 155
    .line 156
    move-object/from16 v28, p30

    .line 157
    .line 158
    invoke-direct/range {v1 .. v29}, LnM0/a$b;-><init>(LQW0/c;LJo0/a;LfR0/a;LVC0/a;LGL0/a;LQN0/b;LLD0/a;LAP0/a;LbL0/a;LwX0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LTn/a;LrM0/a;Lorg/xbet/onexdatabase/OnexDatabase;LHX0/e;Lorg/xbet/statistic/stage/api/domain/TypeStageId;Li8/m;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;LkC0/a;Lqy/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LNF0/a;LnM0/b;)V

    .line 159
    .line 160
    .line 161
    return-object v1
.end method
