.class public final LsM0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a%\u0010\n\u001a\u00020\t2\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001aq\u0010\u0017\u001a\u00020\u00162\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042.\u0010\u0011\u001a*\u0012\u0004\u0012\u00020\r\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0\u000cj\u0014\u0012\u0004\u0012\u00020\r\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e`\u00102\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00122\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\r0\u0014H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018\u001a\u0017\u0010\u0019\u001a\u00020\r2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001a\u001a\u0017\u0010\u001b\u001a\u00020\r2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001a\u00a8\u0006\u001c"
    }
    d2 = {
        "LtM0/d;",
        "LuM0/g;",
        "e",
        "(LtM0/d;)LuM0/g;",
        "",
        "LCN0/p;",
        "teams",
        "LtM0/a;",
        "cellResponse",
        "LuM0/d;",
        "c",
        "(Ljava/util/List;LtM0/a;)LuM0/d;",
        "Ljava/util/HashMap;",
        "",
        "",
        "LuM0/a;",
        "Lkotlin/collections/HashMap;",
        "stageNet",
        "",
        "position",
        "Ljava/util/TreeMap;",
        "stageNetTitles",
        "",
        "d",
        "(Ljava/util/List;Ljava/util/HashMap;LtM0/a;ILjava/util/TreeMap;)V",
        "a",
        "(LtM0/a;)Ljava/lang/String;",
        "b",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LtM0/a;)Ljava/lang/String;
    .locals 9

    .line 1
    invoke-virtual {p0}, LtM0/a;->f()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x2

    .line 6
    const/4 v2, 0x1

    .line 7
    const/4 v3, 0x0

    .line 8
    if-eqz v0, :cond_5

    .line 9
    .line 10
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v4

    .line 14
    if-eqz v4, :cond_0

    .line 15
    .line 16
    goto :goto_2

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    const/4 v4, 0x0

    .line 22
    :cond_1
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v5

    .line 26
    if-eqz v5, :cond_6

    .line 27
    .line 28
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v5

    .line 32
    check-cast v5, LtM0/c;

    .line 33
    .line 34
    invoke-virtual {v5}, LtM0/c;->c()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    invoke-virtual {p0}, LtM0/a;->e()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v7

    .line 42
    invoke-static {v6, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v6

    .line 46
    if-eqz v6, :cond_3

    .line 47
    .line 48
    invoke-virtual {v5}, LtM0/c;->i()Ljava/lang/Integer;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    if-nez v5, :cond_2

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_2
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 56
    .line 57
    .line 58
    move-result v5

    .line 59
    if-ne v5, v2, :cond_1

    .line 60
    .line 61
    goto :goto_1

    .line 62
    :cond_3
    invoke-virtual {v5}, LtM0/c;->i()Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v5

    .line 66
    if-nez v5, :cond_4

    .line 67
    .line 68
    goto :goto_0

    .line 69
    :cond_4
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 70
    .line 71
    .line 72
    move-result v5

    .line 73
    if-ne v5, v1, :cond_1

    .line 74
    .line 75
    :goto_1
    add-int/lit8 v4, v4, 0x1

    .line 76
    .line 77
    if-gez v4, :cond_1

    .line 78
    .line 79
    invoke-static {}, Lkotlin/collections/v;->w()V

    .line 80
    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_5
    :goto_2
    const/4 v4, 0x0

    .line 84
    :cond_6
    invoke-virtual {p0}, LtM0/a;->f()Ljava/util/List;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    if-eqz v0, :cond_c

    .line 89
    .line 90
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 91
    .line 92
    .line 93
    move-result v5

    .line 94
    if-eqz v5, :cond_7

    .line 95
    .line 96
    goto :goto_5

    .line 97
    :cond_7
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    const/4 v5, 0x0

    .line 102
    :cond_8
    :goto_3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 103
    .line 104
    .line 105
    move-result v6

    .line 106
    if-eqz v6, :cond_d

    .line 107
    .line 108
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 109
    .line 110
    .line 111
    move-result-object v6

    .line 112
    check-cast v6, LtM0/c;

    .line 113
    .line 114
    invoke-virtual {v6}, LtM0/c;->f()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v7

    .line 118
    invoke-virtual {p0}, LtM0/a;->g()Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object v8

    .line 122
    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result v7

    .line 126
    if-eqz v7, :cond_a

    .line 127
    .line 128
    invoke-virtual {v6}, LtM0/c;->i()Ljava/lang/Integer;

    .line 129
    .line 130
    .line 131
    move-result-object v6

    .line 132
    if-nez v6, :cond_9

    .line 133
    .line 134
    goto :goto_3

    .line 135
    :cond_9
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 136
    .line 137
    .line 138
    move-result v6

    .line 139
    if-ne v6, v1, :cond_8

    .line 140
    .line 141
    goto :goto_4

    .line 142
    :cond_a
    invoke-virtual {v6}, LtM0/c;->i()Ljava/lang/Integer;

    .line 143
    .line 144
    .line 145
    move-result-object v6

    .line 146
    if-nez v6, :cond_b

    .line 147
    .line 148
    goto :goto_3

    .line 149
    :cond_b
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 150
    .line 151
    .line 152
    move-result v6

    .line 153
    if-ne v6, v2, :cond_8

    .line 154
    .line 155
    :goto_4
    add-int/lit8 v5, v5, 0x1

    .line 156
    .line 157
    if-gez v5, :cond_8

    .line 158
    .line 159
    invoke-static {}, Lkotlin/collections/v;->w()V

    .line 160
    .line 161
    .line 162
    goto :goto_3

    .line 163
    :cond_c
    :goto_5
    const/4 v5, 0x0

    .line 164
    :cond_d
    const-string v0, ""

    .line 165
    .line 166
    if-le v4, v5, :cond_f

    .line 167
    .line 168
    invoke-virtual {p0}, LtM0/a;->e()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object p0

    .line 172
    if-nez p0, :cond_e

    .line 173
    .line 174
    return-object v0

    .line 175
    :cond_e
    return-object p0

    .line 176
    :cond_f
    if-ge v4, v5, :cond_11

    .line 177
    .line 178
    invoke-virtual {p0}, LtM0/a;->g()Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object p0

    .line 182
    if-nez p0, :cond_10

    .line 183
    .line 184
    return-object v0

    .line 185
    :cond_10
    return-object p0

    .line 186
    :cond_11
    invoke-virtual {p0}, LtM0/a;->f()Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object v2

    .line 190
    if-eqz v2, :cond_12

    .line 191
    .line 192
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 193
    .line 194
    .line 195
    move-result v3

    .line 196
    :cond_12
    rem-int/2addr v3, v1

    .line 197
    if-nez v3, :cond_13

    .line 198
    .line 199
    invoke-static {p0}, LsM0/b;->b(LtM0/a;)Ljava/lang/String;

    .line 200
    .line 201
    .line 202
    move-result-object p0

    .line 203
    return-object p0

    .line 204
    :cond_13
    return-object v0
.end method

.method public static final b(LtM0/a;)Ljava/lang/String;
    .locals 7

    .line 1
    invoke-virtual {p0}, LtM0/a;->f()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_2

    .line 7
    .line 8
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    const/4 v2, 0x0

    .line 13
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    if-eqz v3, :cond_3

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    check-cast v3, LtM0/c;

    .line 24
    .line 25
    invoke-virtual {v3}, LtM0/c;->c()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    invoke-virtual {p0}, LtM0/a;->e()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    if-eqz v4, :cond_1

    .line 38
    .line 39
    invoke-virtual {v3}, LtM0/c;->d()Ljava/lang/Integer;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    if-eqz v3, :cond_0

    .line 44
    .line 45
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    goto :goto_1

    .line 50
    :cond_0
    const/4 v3, 0x0

    .line 51
    goto :goto_1

    .line 52
    :cond_1
    invoke-virtual {v3}, LtM0/c;->g()Ljava/lang/Integer;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    if-eqz v3, :cond_0

    .line 57
    .line 58
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    :goto_1
    add-int/2addr v2, v3

    .line 63
    goto :goto_0

    .line 64
    :cond_2
    const/4 v2, 0x0

    .line 65
    :cond_3
    invoke-virtual {p0}, LtM0/a;->f()Ljava/util/List;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    if-eqz v0, :cond_7

    .line 70
    .line 71
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    const/4 v3, 0x0

    .line 76
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 77
    .line 78
    .line 79
    move-result v4

    .line 80
    if-eqz v4, :cond_6

    .line 81
    .line 82
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object v4

    .line 86
    check-cast v4, LtM0/c;

    .line 87
    .line 88
    invoke-virtual {v4}, LtM0/c;->f()Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object v5

    .line 92
    invoke-virtual {p0}, LtM0/a;->g()Ljava/lang/String;

    .line 93
    .line 94
    .line 95
    move-result-object v6

    .line 96
    invoke-static {v5, v6}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 97
    .line 98
    .line 99
    move-result v5

    .line 100
    if-eqz v5, :cond_5

    .line 101
    .line 102
    invoke-virtual {v4}, LtM0/c;->g()Ljava/lang/Integer;

    .line 103
    .line 104
    .line 105
    move-result-object v4

    .line 106
    if-eqz v4, :cond_4

    .line 107
    .line 108
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 109
    .line 110
    .line 111
    move-result v4

    .line 112
    goto :goto_3

    .line 113
    :cond_4
    const/4 v4, 0x0

    .line 114
    goto :goto_3

    .line 115
    :cond_5
    invoke-virtual {v4}, LtM0/c;->d()Ljava/lang/Integer;

    .line 116
    .line 117
    .line 118
    move-result-object v4

    .line 119
    if-eqz v4, :cond_4

    .line 120
    .line 121
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 122
    .line 123
    .line 124
    move-result v4

    .line 125
    :goto_3
    add-int/2addr v3, v4

    .line 126
    goto :goto_2

    .line 127
    :cond_6
    move v1, v3

    .line 128
    :cond_7
    const-string v0, ""

    .line 129
    .line 130
    if-le v2, v1, :cond_9

    .line 131
    .line 132
    invoke-virtual {p0}, LtM0/a;->e()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object p0

    .line 136
    if-nez p0, :cond_8

    .line 137
    .line 138
    return-object v0

    .line 139
    :cond_8
    return-object p0

    .line 140
    :cond_9
    if-ge v2, v1, :cond_b

    .line 141
    .line 142
    invoke-virtual {p0}, LtM0/a;->g()Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object p0

    .line 146
    if-nez p0, :cond_a

    .line 147
    .line 148
    return-object v0

    .line 149
    :cond_a
    return-object p0

    .line 150
    :cond_b
    return-object v0
.end method

.method public static final c(Ljava/util/List;LtM0/a;)LuM0/d;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LCN0/p;",
            ">;",
            "LtM0/a;",
            ")",
            "LuM0/d;"
        }
    .end annotation

    .line 1
    new-instance v2, Ljava/util/HashMap;

    .line 2
    .line 3
    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v0, Ljava/util/TreeMap;

    .line 7
    .line 8
    invoke-direct {v0}, Ljava/util/TreeMap;-><init>()V

    .line 9
    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-static {p0, v2, p1, v1, v0}, LsM0/b;->d(Ljava/util/List;Ljava/util/HashMap;LtM0/a;ILjava/util/TreeMap;)V

    .line 13
    .line 14
    .line 15
    move-object p0, v0

    .line 16
    new-instance v0, LuM0/d;

    .line 17
    .line 18
    invoke-virtual {p0}, Ljava/util/TreeMap;->values()Ljava/util/Collection;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    check-cast p0, Ljava/lang/Iterable;

    .line 23
    .line 24
    invoke-static {p0}, Lkotlin/collections/CollectionsKt;->d1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    const/4 v4, 0x4

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v3, 0x0

    .line 31
    invoke-direct/range {v0 .. v5}, LuM0/d;-><init>(Ljava/util/List;Ljava/util/Map;LuM0/d;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method public static final d(Ljava/util/List;Ljava/util/HashMap;LtM0/a;ILjava/util/TreeMap;)V
    .locals 23
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LCN0/p;",
            ">;",
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "LuM0/a;",
            ">;>;",
            "LtM0/a;",
            "I",
            "Ljava/util/TreeMap<",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move-object/from16 v2, p4

    .line 1
    invoke-static/range {p3 .. p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/util/TreeMap;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    const-string v4, ""

    if-nez v3, :cond_1

    invoke-static/range {p3 .. p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 2
    invoke-virtual/range {p2 .. p2}, LtM0/a;->j()Ljava/lang/String;

    move-result-object v5

    if-nez v5, :cond_0

    move-object v5, v4

    :cond_0
    invoke-interface {v2, v3, v5}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 3
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_3

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    move-object v7, v5

    check-cast v7, LCN0/p;

    invoke-virtual {v7}, LCN0/p;->a()Ljava/lang/String;

    move-result-object v7

    invoke-virtual/range {p2 .. p2}, LtM0/a;->e()Ljava/lang/String;

    move-result-object v8

    invoke-static {v7, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_2

    goto :goto_0

    :cond_3
    const/4 v5, 0x0

    :goto_0
    check-cast v5, LCN0/p;

    .line 4
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :cond_4
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_5

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    move-object v8, v7

    check-cast v8, LCN0/p;

    invoke-virtual {v8}, LCN0/p;->a()Ljava/lang/String;

    move-result-object v8

    invoke-virtual/range {p2 .. p2}, LtM0/a;->g()Ljava/lang/String;

    move-result-object v9

    invoke-static {v8, v9}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_4

    goto :goto_1

    :cond_5
    const/4 v7, 0x0

    :goto_1
    check-cast v7, LCN0/p;

    .line 5
    invoke-virtual/range {p2 .. p2}, LtM0/a;->j()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_6

    move-object v3, v4

    .line 6
    :cond_6
    invoke-virtual {v1, v3}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v8

    if-nez v8, :cond_7

    .line 7
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    invoke-interface {v1, v3, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    :cond_7
    invoke-virtual/range {p2 .. p2}, LtM0/a;->e()Ljava/lang/String;

    move-result-object v8

    if-nez v8, :cond_8

    move-object v12, v4

    goto :goto_2

    :cond_8
    move-object v12, v8

    .line 9
    :goto_2
    invoke-virtual/range {p2 .. p2}, LtM0/a;->g()Ljava/lang/String;

    move-result-object v8

    if-nez v8, :cond_9

    move-object v13, v4

    goto :goto_3

    :cond_9
    move-object v13, v8

    .line 10
    :goto_3
    invoke-virtual/range {p2 .. p2}, LtM0/a;->f()Ljava/util/List;

    move-result-object v8

    const/16 v9, 0xa

    if-eqz v8, :cond_a

    .line 11
    new-instance v10, Ljava/util/ArrayList;

    invoke-static {v8, v9}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    move-result v11

    invoke-direct {v10, v11}, Ljava/util/ArrayList;-><init>(I)V

    .line 12
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v8

    :goto_4
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    move-result v11

    if-eqz v11, :cond_b

    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v11

    .line 13
    check-cast v11, LtM0/c;

    .line 14
    invoke-static {v11, v12, v13}, LsM0/a;->a(LtM0/c;Ljava/lang/String;Ljava/lang/String;)LuM0/b;

    move-result-object v11

    .line 15
    invoke-interface {v10, v11}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_4

    :cond_a
    const/4 v10, 0x0

    :cond_b
    if-nez v10, :cond_c

    .line 16
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v10

    .line 17
    :cond_c
    new-instance v8, LsM0/b$a;

    invoke-direct {v8}, LsM0/b$a;-><init>()V

    invoke-static {v10, v8}, Lkotlin/collections/CollectionsKt;->l1(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/util/List;

    move-result-object v17

    .line 18
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v8

    const/4 v11, 0x1

    if-nez v8, :cond_12

    .line 19
    invoke-static {v0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    move-result v8

    if-eqz v8, :cond_e

    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    move-result v8

    if-eqz v8, :cond_e

    :cond_d
    const/4 v8, 0x1

    goto :goto_7

    .line 20
    :cond_e
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v8

    :cond_f
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    move-result v14

    if-eqz v14, :cond_d

    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v14

    check-cast v14, LCN0/p;

    .line 21
    invoke-virtual {v14}, LCN0/p;->c()Ljava/util/List;

    move-result-object v14

    if-eqz v14, :cond_10

    invoke-interface {v14}, Ljava/util/List;->size()I

    move-result v14

    goto :goto_5

    :cond_10
    const/4 v14, 0x0

    :goto_5
    if-le v14, v11, :cond_11

    const/4 v14, 0x1

    goto :goto_6

    :cond_11
    const/4 v14, 0x0

    :goto_6
    if-nez v14, :cond_f

    const/4 v8, 0x0

    :goto_7
    if-eqz v8, :cond_12

    const/4 v8, 0x1

    goto :goto_8

    :cond_12
    const/4 v8, 0x0

    .line 22
    :goto_8
    new-instance v14, Ljava/util/ArrayList;

    invoke-direct {v14}, Ljava/util/ArrayList;-><init>()V

    .line 23
    invoke-interface/range {v17 .. v17}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v15

    :goto_9
    invoke-interface {v15}, Ljava/util/Iterator;->hasNext()Z

    move-result v16

    if-eqz v16, :cond_15

    invoke-interface {v15}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    move-object/from16 v18, v6

    check-cast v18, LuM0/b;

    .line 24
    invoke-virtual/range {v18 .. v18}, LuM0/b;->h()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    move-result-object v10

    sget-object v9, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->UNKNOWN:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    if-eq v10, v9, :cond_13

    const/4 v9, 0x1

    goto :goto_a

    :cond_13
    const/4 v9, 0x0

    :goto_a
    if-eqz v9, :cond_14

    .line 25
    invoke-interface {v14, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    :cond_14
    const/16 v9, 0xa

    goto :goto_9

    :cond_15
    if-eqz v8, :cond_16

    .line 26
    sget-object v6, Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;->MULTI_PLAYER_GAME:Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;

    goto :goto_b

    .line 27
    :cond_16
    invoke-interface {v14}, Ljava/util/List;->size()I

    move-result v6

    if-le v6, v11, :cond_17

    sget-object v6, Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;->MULTI_GAME:Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;

    goto :goto_b

    .line 28
    :cond_17
    sget-object v6, Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;->SINGLE_GAME:Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;

    :goto_b
    if-eqz v5, :cond_18

    .line 29
    invoke-virtual {v5}, LCN0/p;->d()Ljava/lang/String;

    move-result-object v8

    goto :goto_c

    :cond_18
    const/4 v8, 0x0

    :goto_c
    if-nez v8, :cond_19

    move-object v10, v4

    goto :goto_d

    :cond_19
    move-object v10, v8

    :goto_d
    if-eqz v7, :cond_1a

    .line 30
    invoke-virtual {v7}, LCN0/p;->d()Ljava/lang/String;

    move-result-object v8

    goto :goto_e

    :cond_1a
    const/4 v8, 0x0

    :goto_e
    if-nez v8, :cond_1b

    move-object v8, v4

    .line 31
    :cond_1b
    invoke-static/range {p2 .. p2}, LsM0/b;->a(LtM0/a;)Ljava/lang/String;

    move-result-object v14

    .line 32
    sget-object v9, LDX0/e;->a:LDX0/e;

    if-eqz v5, :cond_1c

    invoke-virtual {v5}, LCN0/p;->b()Ljava/lang/String;

    move-result-object v15

    goto :goto_f

    :cond_1c
    const/4 v15, 0x0

    :goto_f
    if-nez v15, :cond_1d

    move-object v15, v4

    :cond_1d
    invoke-virtual {v9, v15}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v15

    if-eqz v7, :cond_1e

    .line 33
    invoke-virtual {v7}, LCN0/p;->b()Ljava/lang/String;

    move-result-object v19

    goto :goto_10

    :cond_1e
    const/16 v19, 0x0

    :goto_10
    if-nez v19, :cond_1f

    goto :goto_11

    :cond_1f
    move-object/from16 v4, v19

    :goto_11
    invoke-virtual {v9, v4}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    if-eqz v5, :cond_20

    .line 34
    invoke-virtual {v5}, LCN0/p;->c()Ljava/util/List;

    move-result-object v5

    if-eqz v5, :cond_20

    .line 35
    new-instance v9, Ljava/util/ArrayList;

    move-object/from16 v20, v4

    const/16 v11, 0xa

    invoke-static {v5, v11}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    move-result v4

    invoke-direct {v9, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 36
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_12
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_21

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    .line 37
    check-cast v5, LCN0/o;

    .line 38
    invoke-static {v5}, LBN0/m;->a(LCN0/o;)LND0/j;

    move-result-object v5

    .line 39
    invoke-interface {v9, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_12

    :cond_20
    move-object/from16 v20, v4

    const/4 v9, 0x0

    :cond_21
    if-nez v9, :cond_22

    .line 40
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v9

    :cond_22
    if-eqz v7, :cond_24

    .line 41
    invoke-virtual {v7}, LCN0/p;->c()Ljava/util/List;

    move-result-object v4

    if-eqz v4, :cond_24

    .line 42
    new-instance v5, Ljava/util/ArrayList;

    const/16 v11, 0xa

    invoke-static {v4, v11}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    move-result v7

    invoke-direct {v5, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 43
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_13
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_23

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    .line 44
    check-cast v7, LCN0/o;

    .line 45
    invoke-static {v7}, LBN0/m;->a(LCN0/o;)LND0/j;

    move-result-object v7

    .line 46
    invoke-interface {v5, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    goto :goto_13

    :cond_23
    move-object/from16 v16, v5

    goto :goto_14

    :cond_24
    const/16 v16, 0x0

    :goto_14
    if-nez v16, :cond_25

    .line 47
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v16

    .line 48
    :cond_25
    invoke-static/range {p2 .. p2}, LsM0/c;->a(LtM0/a;)LuM0/f;

    move-result-object v21

    move-object/from16 v19, v9

    const/4 v4, 0x1

    .line 49
    new-instance v9, LuM0/a;

    move-object/from16 v11, v20

    move-object/from16 v20, v16

    move-object/from16 v16, v11

    move/from16 v22, p3

    move-object/from16 v18, v6

    move-object v11, v8

    invoke-direct/range {v9 .. v22}, LuM0/a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lorg/xbet/statistic/statistic_core/domain/models/TypeCardGame;Ljava/util/List;Ljava/util/List;LuM0/f;I)V

    .line 50
    invoke-virtual {v1, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/List;

    if-eqz v3, :cond_26

    invoke-interface {v3, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 51
    :cond_26
    invoke-virtual/range {p2 .. p2}, LtM0/a;->c()LtM0/a;

    move-result-object v3

    if-eqz v3, :cond_27

    .line 52
    invoke-virtual/range {p2 .. p2}, LtM0/a;->c()LtM0/a;

    move-result-object v3

    add-int/lit8 v5, p3, 0x1

    .line 53
    invoke-static {v0, v1, v3, v5, v2}, LsM0/b;->d(Ljava/util/List;Ljava/util/HashMap;LtM0/a;ILjava/util/TreeMap;)V

    .line 54
    :cond_27
    invoke-virtual/range {p2 .. p2}, LtM0/a;->d()LtM0/a;

    move-result-object v3

    if-eqz v3, :cond_28

    .line 55
    invoke-virtual/range {p2 .. p2}, LtM0/a;->d()LtM0/a;

    move-result-object v3

    add-int/lit8 v4, p3, 0x1

    .line 56
    invoke-static {v0, v1, v3, v4, v2}, LsM0/b;->d(Ljava/util/List;Ljava/util/HashMap;LtM0/a;ILjava/util/TreeMap;)V

    :cond_28
    return-void
.end method

.method public static final e(LtM0/d;)LuM0/g;
    .locals 17
    .param p0    # LtM0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LuM0/c;

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, LtM0/d;->b()Ljava/lang/Long;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 10
    .line 11
    .line 12
    move-result-wide v1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const-wide/16 v1, 0x0

    .line 15
    .line 16
    :goto_0
    invoke-virtual/range {p0 .. p0}, LtM0/d;->a()LtM0/b;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    const/4 v4, 0x0

    .line 21
    if-eqz v3, :cond_1

    .line 22
    .line 23
    invoke-virtual {v3}, LtM0/b;->c()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    goto :goto_1

    .line 28
    :cond_1
    move-object v3, v4

    .line 29
    :goto_1
    const-string v5, ""

    .line 30
    .line 31
    if-nez v3, :cond_2

    .line 32
    .line 33
    move-object v3, v5

    .line 34
    :cond_2
    invoke-virtual/range {p0 .. p0}, LtM0/d;->a()LtM0/b;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    if-eqz v6, :cond_3

    .line 39
    .line 40
    invoke-virtual {v6}, LtM0/b;->b()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v6

    .line 44
    goto :goto_2

    .line 45
    :cond_3
    move-object v6, v4

    .line 46
    :goto_2
    if-nez v6, :cond_4

    .line 47
    .line 48
    goto :goto_3

    .line 49
    :cond_4
    move-object v5, v6

    .line 50
    :goto_3
    invoke-direct {v0, v1, v2, v3, v5}, LuM0/c;-><init>(JLjava/lang/String;Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual/range {p0 .. p0}, LtM0/d;->a()LtM0/b;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    if-eqz v1, :cond_9

    .line 58
    .line 59
    invoke-virtual {v1}, LtM0/b;->a()Ljava/util/List;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    if-eqz v1, :cond_9

    .line 64
    .line 65
    new-instance v4, Ljava/util/ArrayList;

    .line 66
    .line 67
    const/16 v2, 0xa

    .line 68
    .line 69
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 70
    .line 71
    .line 72
    move-result v2

    .line 73
    invoke-direct {v4, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 74
    .line 75
    .line 76
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    :goto_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 81
    .line 82
    .line 83
    move-result v2

    .line 84
    if-eqz v2, :cond_9

    .line 85
    .line 86
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v2

    .line 90
    move-object v5, v2

    .line 91
    check-cast v5, LtM0/a;

    .line 92
    .line 93
    invoke-virtual {v5}, LtM0/a;->c()LtM0/a;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    if-eqz v2, :cond_7

    .line 98
    .line 99
    invoke-virtual {v5}, LtM0/a;->d()LtM0/a;

    .line 100
    .line 101
    .line 102
    move-result-object v2

    .line 103
    if-eqz v2, :cond_7

    .line 104
    .line 105
    invoke-virtual {v5}, LtM0/a;->c()LtM0/a;

    .line 106
    .line 107
    .line 108
    move-result-object v2

    .line 109
    invoke-virtual {v2}, LtM0/a;->j()Ljava/lang/String;

    .line 110
    .line 111
    .line 112
    move-result-object v2

    .line 113
    invoke-virtual {v5}, LtM0/a;->d()LtM0/a;

    .line 114
    .line 115
    .line 116
    move-result-object v3

    .line 117
    invoke-virtual {v3}, LtM0/a;->j()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v3

    .line 121
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 122
    .line 123
    .line 124
    move-result v2

    .line 125
    if-nez v2, :cond_7

    .line 126
    .line 127
    const/16 v15, 0x1bf

    .line 128
    .line 129
    const/16 v16, 0x0

    .line 130
    .line 131
    const/4 v6, 0x0

    .line 132
    const/4 v7, 0x0

    .line 133
    const/4 v8, 0x0

    .line 134
    const/4 v9, 0x0

    .line 135
    const/4 v10, 0x0

    .line 136
    const/4 v11, 0x0

    .line 137
    const/4 v12, 0x0

    .line 138
    const/4 v13, 0x0

    .line 139
    const/4 v14, 0x0

    .line 140
    invoke-static/range {v5 .. v16}, LtM0/a;->b(LtM0/a;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;LtM0/a;LtM0/a;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LtM0/a;

    .line 141
    .line 142
    .line 143
    move-result-object v2

    .line 144
    const/16 v15, 0x1df

    .line 145
    .line 146
    invoke-static/range {v5 .. v16}, LtM0/a;->b(LtM0/a;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;LtM0/a;LtM0/a;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)LtM0/a;

    .line 147
    .line 148
    .line 149
    move-result-object v3

    .line 150
    invoke-virtual/range {p0 .. p0}, LtM0/d;->c()Ljava/util/List;

    .line 151
    .line 152
    .line 153
    move-result-object v5

    .line 154
    if-nez v5, :cond_5

    .line 155
    .line 156
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 157
    .line 158
    .line 159
    move-result-object v5

    .line 160
    :cond_5
    invoke-static {v5, v2}, LsM0/b;->c(Ljava/util/List;LtM0/a;)LuM0/d;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    invoke-virtual/range {p0 .. p0}, LtM0/d;->c()Ljava/util/List;

    .line 165
    .line 166
    .line 167
    move-result-object v5

    .line 168
    if-nez v5, :cond_6

    .line 169
    .line 170
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 171
    .line 172
    .line 173
    move-result-object v5

    .line 174
    :cond_6
    invoke-static {v5, v3}, LsM0/b;->c(Ljava/util/List;LtM0/a;)LuM0/d;

    .line 175
    .line 176
    .line 177
    move-result-object v3

    .line 178
    new-instance v5, LuM0/e$a;

    .line 179
    .line 180
    invoke-direct {v5, v2, v3}, LuM0/e$a;-><init>(LuM0/d;LuM0/d;)V

    .line 181
    .line 182
    .line 183
    goto :goto_5

    .line 184
    :cond_7
    new-instance v2, LuM0/e$c;

    .line 185
    .line 186
    invoke-virtual/range {p0 .. p0}, LtM0/d;->c()Ljava/util/List;

    .line 187
    .line 188
    .line 189
    move-result-object v3

    .line 190
    if-nez v3, :cond_8

    .line 191
    .line 192
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 193
    .line 194
    .line 195
    move-result-object v3

    .line 196
    :cond_8
    invoke-static {v3, v5}, LsM0/b;->c(Ljava/util/List;LtM0/a;)LuM0/d;

    .line 197
    .line 198
    .line 199
    move-result-object v3

    .line 200
    invoke-direct {v2, v3}, LuM0/e$c;-><init>(LuM0/d;)V

    .line 201
    .line 202
    .line 203
    move-object v5, v2

    .line 204
    :goto_5
    invoke-interface {v4, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 205
    .line 206
    .line 207
    goto :goto_4

    .line 208
    :cond_9
    if-nez v4, :cond_a

    .line 209
    .line 210
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 211
    .line 212
    .line 213
    move-result-object v4

    .line 214
    :cond_a
    new-instance v1, LuM0/g;

    .line 215
    .line 216
    invoke-direct {v1, v0, v4}, LuM0/g;-><init>(LuM0/c;Ljava/util/List;)V

    .line 217
    .line 218
    .line 219
    return-object v1
.end method
