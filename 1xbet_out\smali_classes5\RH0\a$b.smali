.class public final LRH0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRH0/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LRH0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LRH0/a$b;


# direct methods
.method public constructor <init>(LDH0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LRH0/a$b;->a:LRH0/a$b;

    return-void
.end method

.method public synthetic constructor <init>(LDH0/a;LRH0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LRH0/a$b;-><init>(LDH0/a;)V

    return-void
.end method


# virtual methods
.method public a()LPH0/b;
    .locals 1

    .line 1
    new-instance v0, LRH0/i;

    .line 2
    .line 3
    invoke-direct {v0}, LRH0/i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
