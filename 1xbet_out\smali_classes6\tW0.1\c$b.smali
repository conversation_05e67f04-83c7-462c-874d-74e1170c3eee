.class public final LtW0/c$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtW0/m;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtW0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LzX0/k;

.field public final b:LtW0/c$b;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/a;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/e;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/o;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LyW0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public n:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/r;

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LtW0/m$b;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtW0/c$b;->b:LtW0/c$b;

    .line 4
    iput-object p14, p0, LtW0/c$b;->a:LzX0/k;

    .line 5
    invoke-virtual/range {p0 .. p14}, LtW0/c$b;->b(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;)V

    return-void
.end method

.method public synthetic constructor <init>(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;LtW0/d;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p14}, LtW0/c$b;-><init>(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtW0/c$b;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(Lak/a;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LwW0/a;LwW0/e;LwW0/o;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;)V
    .locals 0

    .line 1
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, LtW0/c$b;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, LtW0/c$b;->d:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LtW0/c$b;->e:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iput-object p1, p0, LtW0/c$b;->f:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iput-object p1, p0, LtW0/c$b;->g:Ldagger/internal/h;

    .line 30
    .line 31
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    iput-object p1, p0, LtW0/c$b;->h:Ldagger/internal/h;

    .line 36
    .line 37
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iput-object p1, p0, LtW0/c$b;->i:Ldagger/internal/h;

    .line 42
    .line 43
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    iput-object p1, p0, LtW0/c$b;->j:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iput-object p1, p0, LtW0/c$b;->k:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput-object p1, p0, LtW0/c$b;->l:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 62
    .line 63
    .line 64
    move-result-object p12

    .line 65
    iput-object p12, p0, LtW0/c$b;->m:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object p2, p0, LtW0/c$b;->c:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object p3, p0, LtW0/c$b;->d:Ldagger/internal/h;

    .line 70
    .line 71
    iget-object p4, p0, LtW0/c$b;->e:Ldagger/internal/h;

    .line 72
    .line 73
    iget-object p5, p0, LtW0/c$b;->f:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object p6, p0, LtW0/c$b;->g:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object p7, p0, LtW0/c$b;->h:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p8, p0, LtW0/c$b;->i:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object p9, p0, LtW0/c$b;->j:Ldagger/internal/h;

    .line 82
    .line 83
    iget-object p10, p0, LtW0/c$b;->k:Ldagger/internal/h;

    .line 84
    .line 85
    iget-object p11, p0, LtW0/c$b;->l:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p2 .. p12}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/r;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/r;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LtW0/c$b;->n:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/r;

    .line 92
    .line 93
    invoke-static {p1}, LtW0/p;->c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/r;)Ldagger/internal/h;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LtW0/c$b;->o:Ldagger/internal/h;

    .line 98
    .line 99
    return-void
.end method

.method public final c(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LtW0/c$b;->o:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LtW0/m$b;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/k;->b(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;LtW0/m$b;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LtW0/c$b;->a:LzX0/k;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/k;->a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryFragment;LzX0/k;)V

    .line 15
    .line 16
    .line 17
    return-object p1
.end method
