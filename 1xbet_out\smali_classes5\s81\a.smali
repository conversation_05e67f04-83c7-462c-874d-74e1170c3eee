.class public final Ls81/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls81/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0011\u0010\u0002\u001a\u00020\u0001*\u00020\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/remoteconfig/domain/models/AggregatorCategoryStyleType;",
        "Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;",
        "a",
        "(Lorg/xbet/remoteconfig/domain/models/AggregatorCategoryStyleType;)Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;",
        "api_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/remoteconfig/domain/models/AggregatorCategoryStyleType;)Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;
    .locals 1
    .param p0    # Lorg/xbet/remoteconfig/domain/models/AggregatorCategoryStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Ls81/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p0, v0, :cond_3

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p0, v0, :cond_2

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p0, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-ne p0, v0, :cond_0

    .line 20
    .line 21
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;->TABS_FILLED:Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;

    .line 22
    .line 23
    return-object p0

    .line 24
    :cond_0
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw p0

    .line 30
    :cond_1
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;->TABS_LINE:Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;

    .line 31
    .line 32
    return-object p0

    .line 33
    :cond_2
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;->CHIPS_L:Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;

    .line 34
    .line 35
    return-object p0

    .line 36
    :cond_3
    sget-object p0, Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;->CHIPS_S:Lorg/xbet/uikit_aggregator/aggregatorFilter/AggregatorFilterType;

    .line 37
    .line 38
    return-object p0
.end method
