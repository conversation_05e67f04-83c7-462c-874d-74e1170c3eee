.class public final Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lf81/c;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0015\u0018\u00002\u00020\u0001BQ\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015JD\u0010 \u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0096B\u00a2\u0006\u0004\u0008 \u0010!J4\u0010\"\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008\"\u0010#J,\u0010$\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008$\u0010%J<\u0010+\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020*0\u001e0\u001d2\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00162\u0006\u0010)\u001a\u00020(2\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008+\u0010,JD\u0010.\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020*0\u001e0\u001d2\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00162\u0006\u0010)\u001a\u00020(2\u0006\u0010-\u001a\u00020(2\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008.\u0010/JD\u00100\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020*0\u001e0\u001d2\u0006\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u001b\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00162\u0006\u0010)\u001a\u00020(2\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u00080\u00101JD\u00102\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020*0\u001e0\u001d2\u0006\u0010&\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00162\u0006\u0010)\u001a\u00020(2\u0006\u0010-\u001a\u00020(2\u0006\u0010\u001c\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u00082\u0010/J?\u0010:\u001a\u00020\u001f2\u0006\u00104\u001a\u0002032\u0012\u00105\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020*0\u001e0\u001d2\u0008\u0008\u0002\u00107\u001a\u0002062\u0008\u0008\u0002\u00109\u001a\u000208H\u0002\u00a2\u0006\u0004\u0008:\u0010;R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010<R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010L\u00a8\u0006M"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;",
        "Lf81/c;",
        "Lv81/j;",
        "getCategoriesUseCase",
        "Lv81/q;",
        "getPopularGamesScenario",
        "Lu81/b;",
        "repository",
        "LHX0/e;",
        "resourceManager",
        "LfX/b;",
        "testRepository",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xplatform/aggregator/impl/category/domain/usecases/p;",
        "getFilterTypeFromSavedFiltersUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "<init>",
        "(Lv81/j;Lv81/q;Lu81/b;LHX0/e;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;Lm8/a;Lorg/xplatform/aggregator/impl/category/domain/usecases/p;Li8/j;)V",
        "",
        "fromVirtual",
        "isLoggedIn",
        "",
        "limitLoadGames",
        "fromPopular",
        "isForceUpdate",
        "Lkotlin/Result;",
        "",
        "Ld81/b;",
        "a",
        "(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "p",
        "(ZZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "u",
        "(IZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "test",
        "hasBrands",
        "",
        "endPoint",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "r",
        "(ZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "filterType",
        "s",
        "(ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "t",
        "(ZZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "q",
        "Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;",
        "category",
        "gamesResult",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "partitionType",
        "",
        "partType",
        "n",
        "(Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;J)Ld81/b;",
        "Lv81/j;",
        "b",
        "Lv81/q;",
        "c",
        "Lu81/b;",
        "d",
        "LHX0/e;",
        "e",
        "LfX/b;",
        "f",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "g",
        "Lm8/a;",
        "h",
        "Lorg/xplatform/aggregator/impl/category/domain/usecases/p;",
        "i",
        "Li8/j;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lv81/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lv81/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lu81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lv81/j;Lv81/q;Lu81/b;LHX0/e;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;Lm8/a;Lorg/xplatform/aggregator/impl/category/domain/usecases/p;Li8/j;)V
    .locals 0
    .param p1    # Lv81/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lv81/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lu81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xplatform/aggregator/impl/category/domain/usecases/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->a:Lv81/j;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->b:Lv81/q;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->d:LHX0/e;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->e:LfX/b;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->f:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->g:Lm8/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->h:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->i:Li8/j;

    .line 21
    .line 22
    return-void
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lv81/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->a:Lv81/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lorg/xplatform/aggregator/impl/category/domain/usecases/p;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->h:Lorg/xplatform/aggregator/impl/category/domain/usecases/p;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lv81/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->b:Lv81/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->f:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)Li8/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->i:Li8/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;)LfX/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->e:LfX/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->p(ZZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->q(ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic j(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->r(ZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic k(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->s(ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic l(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->t(ZZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic m(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;IZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->u(IZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic o(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;JILjava/lang/Object;)Ld81/b;
    .locals 6

    .line 1
    and-int/lit8 p7, p6, 0x4

    .line 2
    .line 3
    if-eqz p7, :cond_0

    .line 4
    .line 5
    sget-object p3, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 6
    .line 7
    :cond_0
    move-object v3, p3

    .line 8
    and-int/lit8 p3, p6, 0x8

    .line 9
    .line 10
    if-eqz p3, :cond_1

    .line 11
    .line 12
    const-wide/16 p4, 0x1

    .line 13
    .line 14
    :cond_1
    move-object v0, p0

    .line 15
    move-object v1, p1

    .line 16
    move-object v2, p2

    .line 17
    move-wide v4, p4

    .line 18
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->n(Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;J)Ld81/b;

    .line 19
    .line 20
    .line 21
    move-result-object p0

    .line 22
    return-object p0
.end method


# virtual methods
.method public a(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZIZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;

    .line 9
    .line 10
    iget v3, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    .line 11
    .line 12
    const/high16 v4, -0x80000000

    .line 13
    .line 14
    and-int v5, v3, v4

    .line 15
    .line 16
    if-eqz v5, :cond_0

    .line 17
    .line 18
    sub-int/2addr v3, v4

    .line 19
    iput v3, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    .line 20
    .line 21
    :goto_0
    move-object v8, v1

    .line 22
    goto :goto_1

    .line 23
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;

    .line 24
    .line 25
    invoke-direct {v1, p0, v0}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    goto :goto_0

    .line 29
    :goto_1
    iget-object v0, v8, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v9

    .line 35
    iget v1, v8, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    .line 36
    .line 37
    const/4 v10, 0x1

    .line 38
    if-eqz v1, :cond_2

    .line 39
    .line 40
    if-ne v1, v10, :cond_1

    .line 41
    .line 42
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_2

    .line 46
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw v0

    .line 54
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->g:Lm8/a;

    .line 58
    .line 59
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 60
    .line 61
    .line 62
    move-result-object v11

    .line 63
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$2;

    .line 64
    .line 65
    const/4 v7, 0x0

    .line 66
    move-object v2, p0

    .line 67
    move v1, p1

    .line 68
    move v5, p2

    .line 69
    move v3, p3

    .line 70
    move/from16 v6, p4

    .line 71
    .line 72
    move/from16 v4, p5

    .line 73
    .line 74
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$2;-><init>(ZLorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;IZZZLkotlin/coroutines/e;)V

    .line 75
    .line 76
    .line 77
    iput v10, v8, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    .line 78
    .line 79
    invoke-static {v11, v0, v8}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    if-ne v0, v9, :cond_3

    .line 84
    .line 85
    return-object v9

    .line 86
    :cond_3
    :goto_2
    check-cast v0, Lkotlin/Result;

    .line 87
    .line 88
    invoke-virtual {v0}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    return-object v0
.end method

.method public final n(Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;Ljava/lang/Object;Lorg/xplatform/aggregator/api/model/PartitionType;J)Ld81/b;
    .locals 9

    .line 1
    new-instance v0, Ld81/b;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->d:LHX0/e;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->d(Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    const/4 v4, 0x0

    .line 14
    new-array v4, v4, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {v3, p1, v4}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-static {p2}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    if-eqz v4, :cond_0

    .line 29
    .line 30
    move-object p2, p1

    .line 31
    :cond_0
    move-object v4, p2

    .line 32
    check-cast v4, Ljava/util/List;

    .line 33
    .line 34
    invoke-virtual {p3}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 35
    .line 36
    .line 37
    move-result-wide v5

    .line 38
    move-wide v7, p4

    .line 39
    invoke-direct/range {v0 .. v8}, Ld81/b;-><init>(JLjava/lang/String;Ljava/util/List;JJ)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method

.method public final p(ZZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    new-instance v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;

    .line 54
    .line 55
    const/4 v9, 0x0

    .line 56
    move-object v5, p0

    .line 57
    move v6, p1

    .line 58
    move v8, p2

    .line 59
    move v7, p3

    .line 60
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$2;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;ZZZLkotlin/coroutines/e;)V

    .line 61
    .line 62
    .line 63
    iput v3, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadAggregatorGames$1;->label:I

    .line 64
    .line 65
    invoke-static {v4, v0}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p4

    .line 69
    if-ne p4, v1, :cond_3

    .line 70
    .line 71
    return-object v1

    .line 72
    :cond_3
    :goto_1
    check-cast p4, Lkotlin/Result;

    .line 73
    .line 74
    invoke-virtual {p4}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    return-object p1
.end method

.method public final q(ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v7, 0x1

    .line 41
    if-eqz v4, :cond_4

    .line 42
    .line 43
    if-eq v4, v7, :cond_3

    .line 44
    .line 45
    if-ne v4, v5, :cond_2

    .line 46
    .line 47
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$1:I

    .line 48
    .line 49
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$0:I

    .line 50
    .line 51
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$2:Z

    .line 52
    .line 53
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$1:Z

    .line 54
    .line 55
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$0:Z

    .line 56
    .line 57
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$2:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 60
    .line 61
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$1:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v13, Ljava/lang/String;

    .line 64
    .line 65
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    check-cast v14, Ljava/lang/String;

    .line 68
    .line 69
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    :cond_1
    move/from16 v26, v11

    .line 73
    .line 74
    move-object v11, v1

    .line 75
    move/from16 v1, v26

    .line 76
    .line 77
    move-object/from16 v26, v12

    .line 78
    .line 79
    move v12, v4

    .line 80
    move v4, v10

    .line 81
    move v10, v9

    .line 82
    move-object v9, v13

    .line 83
    move v13, v8

    .line 84
    move-object v8, v14

    .line 85
    move-object/from16 v14, v26

    .line 86
    .line 87
    goto/16 :goto_9

    .line 88
    .line 89
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 90
    .line 91
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 92
    .line 93
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    throw v0

    .line 97
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$1:I

    .line 98
    .line 99
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$0:I

    .line 100
    .line 101
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$2:Z

    .line 102
    .line 103
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$1:Z

    .line 104
    .line 105
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$0:Z

    .line 106
    .line 107
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$2:Ljava/lang/Object;

    .line 108
    .line 109
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 110
    .line 111
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$1:Ljava/lang/Object;

    .line 112
    .line 113
    check-cast v13, Ljava/lang/String;

    .line 114
    .line 115
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    check-cast v14, Ljava/lang/String;

    .line 118
    .line 119
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 120
    .line 121
    .line 122
    goto/16 :goto_2

    .line 123
    .line 124
    :catchall_0
    move-exception v0

    .line 125
    goto/16 :goto_4

    .line 126
    .line 127
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 128
    .line 129
    .line 130
    move/from16 v4, p2

    .line 131
    .line 132
    move-object/from16 v8, p3

    .line 133
    .line 134
    move-object/from16 v9, p4

    .line 135
    .line 136
    move/from16 v10, p5

    .line 137
    .line 138
    move-object v11, v1

    .line 139
    move-object v14, v2

    .line 140
    const/4 v12, 0x0

    .line 141
    const/4 v13, 0x0

    .line 142
    move/from16 v1, p1

    .line 143
    .line 144
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 145
    .line 146
    iget-object v15, v14, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 147
    .line 148
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 149
    .line 150
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 151
    .line 152
    .line 153
    move-result-wide v16

    .line 154
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 155
    .line 156
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 157
    .line 158
    .line 159
    move-result-wide v18

    .line 160
    invoke-static/range {v18 .. v19}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 165
    .line 166
    .line 167
    move-result-object v18

    .line 168
    iput-object v8, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$0:Ljava/lang/Object;

    .line 169
    .line 170
    iput-object v9, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$1:Ljava/lang/Object;

    .line 171
    .line 172
    iput-object v14, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$2:Ljava/lang/Object;

    .line 173
    .line 174
    iput-boolean v1, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$0:Z

    .line 175
    .line 176
    iput-boolean v4, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$1:Z

    .line 177
    .line 178
    iput-boolean v10, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$2:Z

    .line 179
    .line 180
    iput v13, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$0:I

    .line 181
    .line 182
    iput v12, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$1:I

    .line 183
    .line 184
    iput v7, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 185
    .line 186
    const/16 v19, 0x8

    .line 187
    .line 188
    move/from16 v20, v1

    .line 189
    .line 190
    move/from16 v22, v4

    .line 191
    .line 192
    move-object/from16 v23, v8

    .line 193
    .line 194
    move-object/from16 v21, v9

    .line 195
    .line 196
    move/from16 v24, v10

    .line 197
    .line 198
    move-object/from16 v25, v11

    .line 199
    .line 200
    :try_start_2
    invoke-interface/range {v15 .. v25}, Lu81/b;->d(JLjava/util/List;IZLjava/lang/String;ZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 204
    if-ne v0, v3, :cond_5

    .line 205
    .line 206
    goto/16 :goto_8

    .line 207
    .line 208
    :cond_5
    move v4, v12

    .line 209
    move v8, v13

    .line 210
    move-object v12, v14

    .line 211
    move/from16 v11, v20

    .line 212
    .line 213
    move-object/from16 v13, v21

    .line 214
    .line 215
    move/from16 v10, v22

    .line 216
    .line 217
    move-object/from16 v14, v23

    .line 218
    .line 219
    move/from16 v9, v24

    .line 220
    .line 221
    move-object/from16 v1, v25

    .line 222
    .line 223
    :goto_2
    :try_start_3
    check-cast v0, Ljava/util/List;

    .line 224
    .line 225
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 229
    goto/16 :goto_b

    .line 230
    .line 231
    :catchall_1
    move-exception v0

    .line 232
    :goto_3
    move v4, v12

    .line 233
    move v8, v13

    .line 234
    move-object v12, v14

    .line 235
    move/from16 v11, v20

    .line 236
    .line 237
    move-object/from16 v13, v21

    .line 238
    .line 239
    move/from16 v10, v22

    .line 240
    .line 241
    move-object/from16 v14, v23

    .line 242
    .line 243
    move/from16 v9, v24

    .line 244
    .line 245
    move-object/from16 v1, v25

    .line 246
    .line 247
    goto :goto_4

    .line 248
    :catchall_2
    move-exception v0

    .line 249
    move/from16 v20, v1

    .line 250
    .line 251
    move/from16 v22, v4

    .line 252
    .line 253
    move-object/from16 v23, v8

    .line 254
    .line 255
    move-object/from16 v21, v9

    .line 256
    .line 257
    move/from16 v24, v10

    .line 258
    .line 259
    move-object/from16 v25, v11

    .line 260
    .line 261
    goto :goto_3

    .line 262
    :goto_4
    if-eqz v8, :cond_6

    .line 263
    .line 264
    instance-of v15, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 265
    .line 266
    if-eqz v15, :cond_6

    .line 267
    .line 268
    move-object v15, v0

    .line 269
    check-cast v15, Lcom/xbet/onexcore/data/model/ServerException;

    .line 270
    .line 271
    invoke-virtual {v15}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 272
    .line 273
    .line 274
    move-result v15

    .line 275
    if-eqz v15, :cond_6

    .line 276
    .line 277
    const/4 v15, 0x1

    .line 278
    goto :goto_5

    .line 279
    :cond_6
    const/4 v15, 0x0

    .line 280
    :goto_5
    instance-of v6, v0, Ljava/util/concurrent/CancellationException;

    .line 281
    .line 282
    if-nez v6, :cond_c

    .line 283
    .line 284
    instance-of v6, v0, Ljava/net/ConnectException;

    .line 285
    .line 286
    if-nez v6, :cond_c

    .line 287
    .line 288
    if-nez v15, :cond_c

    .line 289
    .line 290
    instance-of v6, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 291
    .line 292
    if-eqz v6, :cond_9

    .line 293
    .line 294
    move-object v6, v0

    .line 295
    check-cast v6, Lcom/xbet/onexcore/data/model/ServerException;

    .line 296
    .line 297
    invoke-virtual {v6}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 298
    .line 299
    .line 300
    move-result v15

    .line 301
    if-nez v15, :cond_8

    .line 302
    .line 303
    invoke-virtual {v6}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 304
    .line 305
    .line 306
    move-result v6

    .line 307
    if-eqz v6, :cond_7

    .line 308
    .line 309
    goto :goto_6

    .line 310
    :cond_7
    const/4 v6, 0x0

    .line 311
    goto :goto_7

    .line 312
    :cond_8
    :goto_6
    const/4 v6, 0x1

    .line 313
    goto :goto_7

    .line 314
    :cond_9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 315
    .line 316
    .line 317
    move-result v6

    .line 318
    if-nez v6, :cond_7

    .line 319
    .line 320
    goto :goto_6

    .line 321
    :goto_7
    add-int/2addr v4, v7

    .line 322
    const/4 v15, 0x3

    .line 323
    if-gt v4, v15, :cond_b

    .line 324
    .line 325
    if-eqz v6, :cond_a

    .line 326
    .line 327
    goto :goto_a

    .line 328
    :cond_a
    new-instance v6, Ljava/lang/StringBuilder;

    .line 329
    .line 330
    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    .line 331
    .line 332
    .line 333
    const-string v15, "error ("

    .line 334
    .line 335
    invoke-virtual {v6, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 336
    .line 337
    .line 338
    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 339
    .line 340
    .line 341
    const-string v15, "): "

    .line 342
    .line 343
    invoke-virtual {v6, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 344
    .line 345
    .line 346
    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 347
    .line 348
    .line 349
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 350
    .line 351
    .line 352
    move-result-object v0

    .line 353
    sget-object v6, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 354
    .line 355
    invoke-virtual {v6, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 356
    .line 357
    .line 358
    iput-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$0:Ljava/lang/Object;

    .line 359
    .line 360
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$1:Ljava/lang/Object;

    .line 361
    .line 362
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->L$2:Ljava/lang/Object;

    .line 363
    .line 364
    iput-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$0:Z

    .line 365
    .line 366
    iput-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$1:Z

    .line 367
    .line 368
    iput-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->Z$2:Z

    .line 369
    .line 370
    iput v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$0:I

    .line 371
    .line 372
    iput v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->I$1:I

    .line 373
    .line 374
    iput v5, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveAggregatorGames$1;->label:I

    .line 375
    .line 376
    const-wide/16 v5, 0xbb8

    .line 377
    .line 378
    invoke-static {v5, v6, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 379
    .line 380
    .line 381
    move-result-object v0

    .line 382
    if-ne v0, v3, :cond_1

    .line 383
    .line 384
    :goto_8
    return-object v3

    .line 385
    :goto_9
    const/4 v5, 0x2

    .line 386
    goto/16 :goto_1

    .line 387
    .line 388
    :cond_b
    :goto_a
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 389
    .line 390
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 391
    .line 392
    .line 393
    move-result-object v0

    .line 394
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 395
    .line 396
    .line 397
    move-result-object v0

    .line 398
    :goto_b
    return-object v0

    .line 399
    :cond_c
    throw v0
.end method

.method public final r(ZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 24
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p5

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v6, 0x0

    .line 41
    const/4 v7, 0x1

    .line 42
    if-eqz v4, :cond_4

    .line 43
    .line 44
    if-eq v4, v7, :cond_3

    .line 45
    .line 46
    if-ne v4, v5, :cond_2

    .line 47
    .line 48
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$1:I

    .line 49
    .line 50
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$0:I

    .line 51
    .line 52
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$2:Z

    .line 53
    .line 54
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$1:Z

    .line 55
    .line 56
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$0:Z

    .line 57
    .line 58
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$1:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 61
    .line 62
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$0:Ljava/lang/Object;

    .line 63
    .line 64
    check-cast v13, Ljava/lang/String;

    .line 65
    .line 66
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    :cond_1
    move/from16 v23, v10

    .line 70
    .line 71
    move-object v10, v1

    .line 72
    move v1, v11

    .line 73
    move v11, v4

    .line 74
    move/from16 v4, v23

    .line 75
    .line 76
    move-object/from16 v23, v12

    .line 77
    .line 78
    move v12, v8

    .line 79
    move-object v8, v13

    .line 80
    move-object/from16 v13, v23

    .line 81
    .line 82
    goto :goto_1

    .line 83
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 84
    .line 85
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 86
    .line 87
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 88
    .line 89
    .line 90
    throw v0

    .line 91
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$1:I

    .line 92
    .line 93
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$0:I

    .line 94
    .line 95
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$2:Z

    .line 96
    .line 97
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$1:Z

    .line 98
    .line 99
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$0:Z

    .line 100
    .line 101
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$1:Ljava/lang/Object;

    .line 102
    .line 103
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 104
    .line 105
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$0:Ljava/lang/Object;

    .line 106
    .line 107
    check-cast v13, Ljava/lang/String;

    .line 108
    .line 109
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 110
    .line 111
    .line 112
    goto :goto_2

    .line 113
    :catchall_0
    move-exception v0

    .line 114
    goto/16 :goto_4

    .line 115
    .line 116
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    move/from16 v4, p2

    .line 120
    .line 121
    move-object/from16 v8, p3

    .line 122
    .line 123
    move/from16 v9, p4

    .line 124
    .line 125
    move-object v10, v1

    .line 126
    move-object v13, v2

    .line 127
    const/4 v11, 0x0

    .line 128
    const/4 v12, 0x0

    .line 129
    move/from16 v1, p1

    .line 130
    .line 131
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 132
    .line 133
    iget-object v14, v13, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 134
    .line 135
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 136
    .line 137
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 138
    .line 139
    .line 140
    move-result-wide v15

    .line 141
    iput-object v8, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$0:Ljava/lang/Object;

    .line 142
    .line 143
    iput-object v13, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$1:Ljava/lang/Object;

    .line 144
    .line 145
    iput-boolean v1, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$0:Z

    .line 146
    .line 147
    iput-boolean v4, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$1:Z

    .line 148
    .line 149
    iput-boolean v9, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$2:Z

    .line 150
    .line 151
    iput v12, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$0:I

    .line 152
    .line 153
    iput v11, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$1:I

    .line 154
    .line 155
    iput v7, v10, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 156
    .line 157
    const/16 v17, 0x8

    .line 158
    .line 159
    move/from16 v18, v1

    .line 160
    .line 161
    move/from16 v20, v4

    .line 162
    .line 163
    move-object/from16 v19, v8

    .line 164
    .line 165
    move/from16 v21, v9

    .line 166
    .line 167
    move-object/from16 v22, v10

    .line 168
    .line 169
    :try_start_2
    invoke-interface/range {v14 .. v22}, Lu81/b;->g(JIZLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 170
    .line 171
    .line 172
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 173
    if-ne v0, v3, :cond_5

    .line 174
    .line 175
    goto/16 :goto_8

    .line 176
    .line 177
    :cond_5
    move v4, v11

    .line 178
    move v8, v12

    .line 179
    move-object v12, v13

    .line 180
    move/from16 v11, v18

    .line 181
    .line 182
    move-object/from16 v13, v19

    .line 183
    .line 184
    move/from16 v10, v20

    .line 185
    .line 186
    move/from16 v9, v21

    .line 187
    .line 188
    move-object/from16 v1, v22

    .line 189
    .line 190
    :goto_2
    :try_start_3
    check-cast v0, Ljava/util/List;

    .line 191
    .line 192
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 193
    .line 194
    .line 195
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 196
    goto/16 :goto_a

    .line 197
    .line 198
    :catchall_1
    move-exception v0

    .line 199
    move v4, v11

    .line 200
    move v8, v12

    .line 201
    move-object v12, v13

    .line 202
    move/from16 v11, v18

    .line 203
    .line 204
    move-object/from16 v13, v19

    .line 205
    .line 206
    move/from16 v10, v20

    .line 207
    .line 208
    move/from16 v9, v21

    .line 209
    .line 210
    :goto_3
    move-object/from16 v1, v22

    .line 211
    .line 212
    goto :goto_4

    .line 213
    :catchall_2
    move-exception v0

    .line 214
    move/from16 v18, v1

    .line 215
    .line 216
    move/from16 v20, v4

    .line 217
    .line 218
    move-object/from16 v19, v8

    .line 219
    .line 220
    move/from16 v21, v9

    .line 221
    .line 222
    move-object/from16 v22, v10

    .line 223
    .line 224
    move v4, v11

    .line 225
    move v8, v12

    .line 226
    move-object v12, v13

    .line 227
    move/from16 v11, v18

    .line 228
    .line 229
    move-object/from16 v13, v19

    .line 230
    .line 231
    move/from16 v10, v20

    .line 232
    .line 233
    goto :goto_3

    .line 234
    :goto_4
    if-eqz v8, :cond_6

    .line 235
    .line 236
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 237
    .line 238
    if-eqz v14, :cond_6

    .line 239
    .line 240
    move-object v14, v0

    .line 241
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 242
    .line 243
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 244
    .line 245
    .line 246
    move-result v14

    .line 247
    if-eqz v14, :cond_6

    .line 248
    .line 249
    const/4 v14, 0x1

    .line 250
    goto :goto_5

    .line 251
    :cond_6
    const/4 v14, 0x0

    .line 252
    :goto_5
    instance-of v15, v0, Ljava/util/concurrent/CancellationException;

    .line 253
    .line 254
    if-nez v15, :cond_c

    .line 255
    .line 256
    instance-of v15, v0, Ljava/net/ConnectException;

    .line 257
    .line 258
    if-nez v15, :cond_c

    .line 259
    .line 260
    if-nez v14, :cond_c

    .line 261
    .line 262
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 263
    .line 264
    if-eqz v14, :cond_9

    .line 265
    .line 266
    move-object v14, v0

    .line 267
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 268
    .line 269
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 270
    .line 271
    .line 272
    move-result v15

    .line 273
    if-nez v15, :cond_8

    .line 274
    .line 275
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 276
    .line 277
    .line 278
    move-result v14

    .line 279
    if-eqz v14, :cond_7

    .line 280
    .line 281
    goto :goto_6

    .line 282
    :cond_7
    const/4 v14, 0x0

    .line 283
    goto :goto_7

    .line 284
    :cond_8
    :goto_6
    const/4 v14, 0x1

    .line 285
    goto :goto_7

    .line 286
    :cond_9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 287
    .line 288
    .line 289
    move-result v14

    .line 290
    if-nez v14, :cond_7

    .line 291
    .line 292
    goto :goto_6

    .line 293
    :goto_7
    add-int/2addr v4, v7

    .line 294
    const/4 v15, 0x3

    .line 295
    if-gt v4, v15, :cond_b

    .line 296
    .line 297
    if-eqz v14, :cond_a

    .line 298
    .line 299
    goto :goto_9

    .line 300
    :cond_a
    new-instance v14, Ljava/lang/StringBuilder;

    .line 301
    .line 302
    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    .line 303
    .line 304
    .line 305
    const-string v15, "error ("

    .line 306
    .line 307
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 308
    .line 309
    .line 310
    invoke-virtual {v14, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 311
    .line 312
    .line 313
    const-string v15, "): "

    .line 314
    .line 315
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 316
    .line 317
    .line 318
    invoke-virtual {v14, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 319
    .line 320
    .line 321
    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 322
    .line 323
    .line 324
    move-result-object v0

    .line 325
    sget-object v14, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 326
    .line 327
    invoke-virtual {v14, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 328
    .line 329
    .line 330
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$0:Ljava/lang/Object;

    .line 331
    .line 332
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->L$1:Ljava/lang/Object;

    .line 333
    .line 334
    iput-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$0:Z

    .line 335
    .line 336
    iput-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$1:Z

    .line 337
    .line 338
    iput-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->Z$2:Z

    .line 339
    .line 340
    iput v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$0:I

    .line 341
    .line 342
    iput v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->I$1:I

    .line 343
    .line 344
    iput v5, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadLiveGames$1;->label:I

    .line 345
    .line 346
    const-wide/16 v14, 0xbb8

    .line 347
    .line 348
    invoke-static {v14, v15, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 349
    .line 350
    .line 351
    move-result-object v0

    .line 352
    if-ne v0, v3, :cond_1

    .line 353
    .line 354
    :goto_8
    return-object v3

    .line 355
    :cond_b
    :goto_9
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 356
    .line 357
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 358
    .line 359
    .line 360
    move-result-object v0

    .line 361
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 362
    .line 363
    .line 364
    move-result-object v0

    .line 365
    :goto_a
    return-object v0

    .line 366
    :cond_c
    throw v0
.end method

.method public final s(ZZLjava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v7, 0x1

    .line 41
    if-eqz v4, :cond_4

    .line 42
    .line 43
    if-eq v4, v7, :cond_3

    .line 44
    .line 45
    if-ne v4, v5, :cond_2

    .line 46
    .line 47
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$1:I

    .line 48
    .line 49
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$0:I

    .line 50
    .line 51
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$2:Z

    .line 52
    .line 53
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$1:Z

    .line 54
    .line 55
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$0:Z

    .line 56
    .line 57
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$2:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 60
    .line 61
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$1:Ljava/lang/Object;

    .line 62
    .line 63
    check-cast v13, Ljava/lang/String;

    .line 64
    .line 65
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$0:Ljava/lang/Object;

    .line 66
    .line 67
    check-cast v14, Ljava/lang/String;

    .line 68
    .line 69
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    :cond_1
    move/from16 v26, v11

    .line 73
    .line 74
    move-object v11, v1

    .line 75
    move/from16 v1, v26

    .line 76
    .line 77
    move-object/from16 v26, v12

    .line 78
    .line 79
    move v12, v4

    .line 80
    move v4, v10

    .line 81
    move v10, v9

    .line 82
    move-object v9, v13

    .line 83
    move v13, v8

    .line 84
    move-object v8, v14

    .line 85
    move-object/from16 v14, v26

    .line 86
    .line 87
    goto/16 :goto_9

    .line 88
    .line 89
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 90
    .line 91
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 92
    .line 93
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    throw v0

    .line 97
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$1:I

    .line 98
    .line 99
    iget v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$0:I

    .line 100
    .line 101
    iget-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$2:Z

    .line 102
    .line 103
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$1:Z

    .line 104
    .line 105
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$0:Z

    .line 106
    .line 107
    iget-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$2:Ljava/lang/Object;

    .line 108
    .line 109
    check-cast v12, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 110
    .line 111
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$1:Ljava/lang/Object;

    .line 112
    .line 113
    check-cast v13, Ljava/lang/String;

    .line 114
    .line 115
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    check-cast v14, Ljava/lang/String;

    .line 118
    .line 119
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 120
    .line 121
    .line 122
    goto/16 :goto_2

    .line 123
    .line 124
    :catchall_0
    move-exception v0

    .line 125
    goto/16 :goto_4

    .line 126
    .line 127
    :cond_4
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 128
    .line 129
    .line 130
    move/from16 v4, p2

    .line 131
    .line 132
    move-object/from16 v8, p3

    .line 133
    .line 134
    move-object/from16 v9, p4

    .line 135
    .line 136
    move/from16 v10, p5

    .line 137
    .line 138
    move-object v11, v1

    .line 139
    move-object v14, v2

    .line 140
    const/4 v12, 0x0

    .line 141
    const/4 v13, 0x0

    .line 142
    move/from16 v1, p1

    .line 143
    .line 144
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 145
    .line 146
    iget-object v15, v14, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 147
    .line 148
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 149
    .line 150
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 151
    .line 152
    .line 153
    move-result-wide v16

    .line 154
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 155
    .line 156
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 157
    .line 158
    .line 159
    move-result-wide v18

    .line 160
    invoke-static/range {v18 .. v19}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 165
    .line 166
    .line 167
    move-result-object v18

    .line 168
    iput-object v8, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$0:Ljava/lang/Object;

    .line 169
    .line 170
    iput-object v9, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$1:Ljava/lang/Object;

    .line 171
    .line 172
    iput-object v14, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$2:Ljava/lang/Object;

    .line 173
    .line 174
    iput-boolean v1, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$0:Z

    .line 175
    .line 176
    iput-boolean v4, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$1:Z

    .line 177
    .line 178
    iput-boolean v10, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$2:Z

    .line 179
    .line 180
    iput v13, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$0:I

    .line 181
    .line 182
    iput v12, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$1:I

    .line 183
    .line 184
    iput v7, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 185
    .line 186
    const/16 v19, 0x8

    .line 187
    .line 188
    move/from16 v20, v1

    .line 189
    .line 190
    move/from16 v22, v4

    .line 191
    .line 192
    move-object/from16 v23, v8

    .line 193
    .line 194
    move-object/from16 v21, v9

    .line 195
    .line 196
    move/from16 v24, v10

    .line 197
    .line 198
    move-object/from16 v25, v11

    .line 199
    .line 200
    :try_start_2
    invoke-interface/range {v15 .. v25}, Lu81/b;->d(JLjava/util/List;IZLjava/lang/String;ZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 204
    if-ne v0, v3, :cond_5

    .line 205
    .line 206
    goto/16 :goto_8

    .line 207
    .line 208
    :cond_5
    move v4, v12

    .line 209
    move v8, v13

    .line 210
    move-object v12, v14

    .line 211
    move/from16 v11, v20

    .line 212
    .line 213
    move-object/from16 v13, v21

    .line 214
    .line 215
    move/from16 v10, v22

    .line 216
    .line 217
    move-object/from16 v14, v23

    .line 218
    .line 219
    move/from16 v9, v24

    .line 220
    .line 221
    move-object/from16 v1, v25

    .line 222
    .line 223
    :goto_2
    :try_start_3
    check-cast v0, Ljava/util/List;

    .line 224
    .line 225
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 229
    goto/16 :goto_b

    .line 230
    .line 231
    :catchall_1
    move-exception v0

    .line 232
    :goto_3
    move v4, v12

    .line 233
    move v8, v13

    .line 234
    move-object v12, v14

    .line 235
    move/from16 v11, v20

    .line 236
    .line 237
    move-object/from16 v13, v21

    .line 238
    .line 239
    move/from16 v10, v22

    .line 240
    .line 241
    move-object/from16 v14, v23

    .line 242
    .line 243
    move/from16 v9, v24

    .line 244
    .line 245
    move-object/from16 v1, v25

    .line 246
    .line 247
    goto :goto_4

    .line 248
    :catchall_2
    move-exception v0

    .line 249
    move/from16 v20, v1

    .line 250
    .line 251
    move/from16 v22, v4

    .line 252
    .line 253
    move-object/from16 v23, v8

    .line 254
    .line 255
    move-object/from16 v21, v9

    .line 256
    .line 257
    move/from16 v24, v10

    .line 258
    .line 259
    move-object/from16 v25, v11

    .line 260
    .line 261
    goto :goto_3

    .line 262
    :goto_4
    if-eqz v8, :cond_6

    .line 263
    .line 264
    instance-of v15, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 265
    .line 266
    if-eqz v15, :cond_6

    .line 267
    .line 268
    move-object v15, v0

    .line 269
    check-cast v15, Lcom/xbet/onexcore/data/model/ServerException;

    .line 270
    .line 271
    invoke-virtual {v15}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 272
    .line 273
    .line 274
    move-result v15

    .line 275
    if-eqz v15, :cond_6

    .line 276
    .line 277
    const/4 v15, 0x1

    .line 278
    goto :goto_5

    .line 279
    :cond_6
    const/4 v15, 0x0

    .line 280
    :goto_5
    instance-of v6, v0, Ljava/util/concurrent/CancellationException;

    .line 281
    .line 282
    if-nez v6, :cond_c

    .line 283
    .line 284
    instance-of v6, v0, Ljava/net/ConnectException;

    .line 285
    .line 286
    if-nez v6, :cond_c

    .line 287
    .line 288
    if-nez v15, :cond_c

    .line 289
    .line 290
    instance-of v6, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 291
    .line 292
    if-eqz v6, :cond_9

    .line 293
    .line 294
    move-object v6, v0

    .line 295
    check-cast v6, Lcom/xbet/onexcore/data/model/ServerException;

    .line 296
    .line 297
    invoke-virtual {v6}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 298
    .line 299
    .line 300
    move-result v15

    .line 301
    if-nez v15, :cond_8

    .line 302
    .line 303
    invoke-virtual {v6}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 304
    .line 305
    .line 306
    move-result v6

    .line 307
    if-eqz v6, :cond_7

    .line 308
    .line 309
    goto :goto_6

    .line 310
    :cond_7
    const/4 v6, 0x0

    .line 311
    goto :goto_7

    .line 312
    :cond_8
    :goto_6
    const/4 v6, 0x1

    .line 313
    goto :goto_7

    .line 314
    :cond_9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 315
    .line 316
    .line 317
    move-result v6

    .line 318
    if-nez v6, :cond_7

    .line 319
    .line 320
    goto :goto_6

    .line 321
    :goto_7
    add-int/2addr v4, v7

    .line 322
    const/4 v15, 0x3

    .line 323
    if-gt v4, v15, :cond_b

    .line 324
    .line 325
    if-eqz v6, :cond_a

    .line 326
    .line 327
    goto :goto_a

    .line 328
    :cond_a
    new-instance v6, Ljava/lang/StringBuilder;

    .line 329
    .line 330
    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    .line 331
    .line 332
    .line 333
    const-string v15, "error ("

    .line 334
    .line 335
    invoke-virtual {v6, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 336
    .line 337
    .line 338
    invoke-virtual {v6, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 339
    .line 340
    .line 341
    const-string v15, "): "

    .line 342
    .line 343
    invoke-virtual {v6, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 344
    .line 345
    .line 346
    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 347
    .line 348
    .line 349
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 350
    .line 351
    .line 352
    move-result-object v0

    .line 353
    sget-object v6, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 354
    .line 355
    invoke-virtual {v6, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 356
    .line 357
    .line 358
    iput-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$0:Ljava/lang/Object;

    .line 359
    .line 360
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$1:Ljava/lang/Object;

    .line 361
    .line 362
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->L$2:Ljava/lang/Object;

    .line 363
    .line 364
    iput-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$0:Z

    .line 365
    .line 366
    iput-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$1:Z

    .line 367
    .line 368
    iput-boolean v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->Z$2:Z

    .line 369
    .line 370
    iput v8, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$0:I

    .line 371
    .line 372
    iput v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->I$1:I

    .line 373
    .line 374
    iput v5, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadPopularGames$1;->label:I

    .line 375
    .line 376
    const-wide/16 v5, 0xbb8

    .line 377
    .line 378
    invoke-static {v5, v6, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 379
    .line 380
    .line 381
    move-result-object v0

    .line 382
    if-ne v0, v3, :cond_1

    .line 383
    .line 384
    :goto_8
    return-object v3

    .line 385
    :goto_9
    const/4 v5, 0x2

    .line 386
    goto/16 :goto_1

    .line 387
    .line 388
    :cond_b
    :goto_a
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 389
    .line 390
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 391
    .line 392
    .line 393
    move-result-object v0

    .line 394
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 395
    .line 396
    .line 397
    move-result-object v0

    .line 398
    :goto_b
    return-object v0

    .line 399
    :cond_c
    throw v0
.end method

.method public final t(ZZZLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 20
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZZ",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I

    .line 20
    .line 21
    move-object/from16 v2, p0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;

    .line 25
    .line 26
    move-object/from16 v2, p0

    .line 27
    .line 28
    invoke-direct {v1, v2, v0}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    :goto_0
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->result:Ljava/lang/Object;

    .line 32
    .line 33
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I

    .line 38
    .line 39
    const/4 v5, 0x2

    .line 40
    const/4 v6, 0x3

    .line 41
    const/4 v8, 0x1

    .line 42
    if-eqz v4, :cond_5

    .line 43
    .line 44
    if-eq v4, v8, :cond_4

    .line 45
    .line 46
    if-eq v4, v5, :cond_3

    .line 47
    .line 48
    if-ne v4, v6, :cond_2

    .line 49
    .line 50
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 51
    .line 52
    iget v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 53
    .line 54
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 55
    .line 56
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 57
    .line 58
    iget-boolean v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 59
    .line 60
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v13, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 63
    .line 64
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 65
    .line 66
    check-cast v14, Ljava/lang/String;

    .line 67
    .line 68
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    const/16 v17, 0x1

    .line 72
    .line 73
    :cond_1
    move-object v15, v14

    .line 74
    move v14, v11

    .line 75
    move-object v11, v13

    .line 76
    move-object v13, v15

    .line 77
    move v15, v10

    .line 78
    move v10, v9

    .line 79
    move v9, v4

    .line 80
    move-object v4, v1

    .line 81
    move v1, v12

    .line 82
    goto/16 :goto_d

    .line 83
    .line 84
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 85
    .line 86
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 87
    .line 88
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw v0

    .line 92
    :cond_3
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 93
    .line 94
    iget v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 95
    .line 96
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 97
    .line 98
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 99
    .line 100
    iget-boolean v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 101
    .line 102
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 103
    .line 104
    check-cast v13, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 105
    .line 106
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 107
    .line 108
    check-cast v14, Ljava/lang/String;

    .line 109
    .line 110
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 111
    .line 112
    .line 113
    const/16 v17, 0x1

    .line 114
    .line 115
    goto/16 :goto_5

    .line 116
    .line 117
    :catchall_0
    move-exception v0

    .line 118
    const/16 v17, 0x1

    .line 119
    .line 120
    goto/16 :goto_8

    .line 121
    .line 122
    :cond_4
    iget v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 123
    .line 124
    iget v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 125
    .line 126
    iget-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 127
    .line 128
    iget-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 129
    .line 130
    iget-boolean v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 131
    .line 132
    iget-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 133
    .line 134
    check-cast v13, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    .line 135
    .line 136
    iget-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 137
    .line 138
    check-cast v14, Ljava/lang/String;

    .line 139
    .line 140
    :try_start_1
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 141
    .line 142
    .line 143
    const/16 v17, 0x1

    .line 144
    .line 145
    goto :goto_2

    .line 146
    :cond_5
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 147
    .line 148
    .line 149
    if-eqz p1, :cond_10

    .line 150
    .line 151
    move/from16 v14, p3

    .line 152
    .line 153
    move-object/from16 v13, p4

    .line 154
    .line 155
    move/from16 v15, p5

    .line 156
    .line 157
    move-object v4, v1

    .line 158
    move-object v11, v2

    .line 159
    const/4 v9, 0x0

    .line 160
    const/4 v10, 0x0

    .line 161
    move/from16 v1, p2

    .line 162
    .line 163
    :goto_1
    :try_start_2
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_6

    .line 164
    .line 165
    if-eqz v1, :cond_7

    .line 166
    .line 167
    :try_start_3
    iget-object v0, v11, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 168
    .line 169
    sget-object v12, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 170
    .line 171
    invoke-virtual {v12}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 172
    .line 173
    .line 174
    move-result-wide v16

    .line 175
    iput-object v13, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 176
    .line 177
    iput-object v11, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 178
    .line 179
    iput-boolean v1, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 180
    .line 181
    iput-boolean v14, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 182
    .line 183
    iput-boolean v15, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 184
    .line 185
    iput v10, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 186
    .line 187
    iput v9, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 188
    .line 189
    iput v8, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_3

    .line 190
    .line 191
    move v12, v10

    .line 192
    const/16 v10, 0x8

    .line 193
    .line 194
    move v8, v9

    .line 195
    move v7, v12

    .line 196
    move-object v9, v0

    .line 197
    move-wide/from16 v18, v16

    .line 198
    .line 199
    move-object/from16 v16, v4

    .line 200
    .line 201
    move-object v4, v11

    .line 202
    move-wide/from16 v11, v18

    .line 203
    .line 204
    const/16 v17, 0x1

    .line 205
    .line 206
    :try_start_4
    invoke-interface/range {v9 .. v16}, Lu81/b;->i(IJLjava/lang/String;ZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 207
    .line 208
    .line 209
    move-result-object v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_2

    .line 210
    move v9, v15

    .line 211
    move-object/from16 v15, v16

    .line 212
    .line 213
    if-ne v0, v3, :cond_6

    .line 214
    .line 215
    goto/16 :goto_c

    .line 216
    .line 217
    :cond_6
    move v12, v1

    .line 218
    move v10, v9

    .line 219
    move v11, v14

    .line 220
    move-object v1, v15

    .line 221
    move v9, v7

    .line 222
    move-object v14, v13

    .line 223
    move-object v13, v4

    .line 224
    move v4, v8

    .line 225
    :goto_2
    :try_start_5
    check-cast v0, Ljava/util/List;
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 226
    .line 227
    goto/16 :goto_6

    .line 228
    .line 229
    :catchall_1
    move-exception v0

    .line 230
    goto/16 :goto_8

    .line 231
    .line 232
    :catchall_2
    move-exception v0

    .line 233
    move v9, v15

    .line 234
    move-object/from16 v15, v16

    .line 235
    .line 236
    :goto_3
    move v12, v1

    .line 237
    move v10, v9

    .line 238
    move v11, v14

    .line 239
    move-object v1, v15

    .line 240
    move v9, v7

    .line 241
    :goto_4
    move-object v14, v13

    .line 242
    move-object v13, v4

    .line 243
    move v4, v8

    .line 244
    goto/16 :goto_8

    .line 245
    .line 246
    :catchall_3
    move-exception v0

    .line 247
    move v8, v9

    .line 248
    move v7, v10

    .line 249
    move v9, v15

    .line 250
    const/16 v17, 0x1

    .line 251
    .line 252
    move-object v15, v4

    .line 253
    move-object v4, v11

    .line 254
    goto :goto_3

    .line 255
    :cond_7
    move v8, v9

    .line 256
    move v7, v10

    .line 257
    move v9, v15

    .line 258
    const/16 v17, 0x1

    .line 259
    .line 260
    move-object v15, v4

    .line 261
    move-object v4, v11

    .line 262
    :try_start_6
    iget-object v0, v4, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->c:Lu81/b;

    .line 263
    .line 264
    sget-object v10, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 265
    .line 266
    invoke-virtual {v10}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 267
    .line 268
    .line 269
    move-result-wide v11

    .line 270
    iput-object v13, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 271
    .line 272
    iput-object v4, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 273
    .line 274
    iput-boolean v1, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 275
    .line 276
    iput-boolean v14, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 277
    .line 278
    iput-boolean v9, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 279
    .line 280
    iput v7, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 281
    .line 282
    iput v8, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 283
    .line 284
    iput v5, v15, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_5

    .line 285
    .line 286
    const/16 v10, 0x8

    .line 287
    .line 288
    move/from16 v16, v9

    .line 289
    .line 290
    move-object v9, v0

    .line 291
    :try_start_7
    invoke-interface/range {v9 .. v15}, Lu81/b;->c(IJLjava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 292
    .line 293
    .line 294
    move-result-object v0
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_4

    .line 295
    if-ne v0, v3, :cond_8

    .line 296
    .line 297
    goto/16 :goto_c

    .line 298
    .line 299
    :cond_8
    move v12, v1

    .line 300
    move v9, v7

    .line 301
    move v11, v14

    .line 302
    move-object v1, v15

    .line 303
    move/from16 v10, v16

    .line 304
    .line 305
    move-object v14, v13

    .line 306
    move-object v13, v4

    .line 307
    move v4, v8

    .line 308
    :goto_5
    :try_start_8
    check-cast v0, Ljava/util/List;

    .line 309
    .line 310
    :goto_6
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 311
    .line 312
    .line 313
    move-result-object v0
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_1

    .line 314
    goto/16 :goto_f

    .line 315
    .line 316
    :catchall_4
    move-exception v0

    .line 317
    :goto_7
    move v12, v1

    .line 318
    move v9, v7

    .line 319
    move v11, v14

    .line 320
    move-object v1, v15

    .line 321
    move/from16 v10, v16

    .line 322
    .line 323
    goto :goto_4

    .line 324
    :catchall_5
    move-exception v0

    .line 325
    move/from16 v16, v9

    .line 326
    .line 327
    goto :goto_7

    .line 328
    :catchall_6
    move-exception v0

    .line 329
    move v8, v9

    .line 330
    move v7, v10

    .line 331
    move/from16 v16, v15

    .line 332
    .line 333
    const/16 v17, 0x1

    .line 334
    .line 335
    move-object v15, v4

    .line 336
    move-object v4, v11

    .line 337
    goto :goto_7

    .line 338
    :goto_8
    if-eqz v9, :cond_9

    .line 339
    .line 340
    instance-of v7, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 341
    .line 342
    if-eqz v7, :cond_9

    .line 343
    .line 344
    move-object v7, v0

    .line 345
    check-cast v7, Lcom/xbet/onexcore/data/model/ServerException;

    .line 346
    .line 347
    invoke-virtual {v7}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 348
    .line 349
    .line 350
    move-result v7

    .line 351
    if-eqz v7, :cond_9

    .line 352
    .line 353
    const/4 v7, 0x1

    .line 354
    goto :goto_9

    .line 355
    :cond_9
    const/4 v7, 0x0

    .line 356
    :goto_9
    instance-of v8, v0, Ljava/util/concurrent/CancellationException;

    .line 357
    .line 358
    if-nez v8, :cond_f

    .line 359
    .line 360
    instance-of v8, v0, Ljava/net/ConnectException;

    .line 361
    .line 362
    if-nez v8, :cond_f

    .line 363
    .line 364
    if-nez v7, :cond_f

    .line 365
    .line 366
    instance-of v7, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 367
    .line 368
    if-eqz v7, :cond_c

    .line 369
    .line 370
    move-object v7, v0

    .line 371
    check-cast v7, Lcom/xbet/onexcore/data/model/ServerException;

    .line 372
    .line 373
    invoke-virtual {v7}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 374
    .line 375
    .line 376
    move-result v8

    .line 377
    if-nez v8, :cond_b

    .line 378
    .line 379
    invoke-virtual {v7}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 380
    .line 381
    .line 382
    move-result v7

    .line 383
    if-eqz v7, :cond_a

    .line 384
    .line 385
    goto :goto_a

    .line 386
    :cond_a
    const/4 v7, 0x0

    .line 387
    goto :goto_b

    .line 388
    :cond_b
    :goto_a
    const/4 v7, 0x1

    .line 389
    goto :goto_b

    .line 390
    :cond_c
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 391
    .line 392
    .line 393
    move-result v7

    .line 394
    if-nez v7, :cond_a

    .line 395
    .line 396
    goto :goto_a

    .line 397
    :goto_b
    add-int/lit8 v4, v4, 0x1

    .line 398
    .line 399
    if-gt v4, v6, :cond_e

    .line 400
    .line 401
    if-eqz v7, :cond_d

    .line 402
    .line 403
    goto :goto_e

    .line 404
    :cond_d
    new-instance v7, Ljava/lang/StringBuilder;

    .line 405
    .line 406
    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    .line 407
    .line 408
    .line 409
    const-string v8, "error ("

    .line 410
    .line 411
    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 412
    .line 413
    .line 414
    invoke-virtual {v7, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 415
    .line 416
    .line 417
    const-string v8, "): "

    .line 418
    .line 419
    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 420
    .line 421
    .line 422
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 423
    .line 424
    .line 425
    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 426
    .line 427
    .line 428
    move-result-object v0

    .line 429
    sget-object v7, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 430
    .line 431
    invoke-virtual {v7, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 432
    .line 433
    .line 434
    iput-object v14, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$0:Ljava/lang/Object;

    .line 435
    .line 436
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->L$1:Ljava/lang/Object;

    .line 437
    .line 438
    iput-boolean v12, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$0:Z

    .line 439
    .line 440
    iput-boolean v11, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$1:Z

    .line 441
    .line 442
    iput-boolean v10, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->Z$2:Z

    .line 443
    .line 444
    iput v9, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$0:I

    .line 445
    .line 446
    iput v4, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->I$1:I

    .line 447
    .line 448
    iput v6, v1, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadRecommendedGames$1;->label:I

    .line 449
    .line 450
    const-wide/16 v7, 0xbb8

    .line 451
    .line 452
    invoke-static {v7, v8, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 453
    .line 454
    .line 455
    move-result-object v0

    .line 456
    if-ne v0, v3, :cond_1

    .line 457
    .line 458
    :goto_c
    return-object v3

    .line 459
    :goto_d
    const/4 v8, 0x1

    .line 460
    goto/16 :goto_1

    .line 461
    .line 462
    :cond_e
    :goto_e
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 463
    .line 464
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 465
    .line 466
    .line 467
    move-result-object v0

    .line 468
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 469
    .line 470
    .line 471
    move-result-object v0

    .line 472
    :goto_f
    return-object v0

    .line 473
    :cond_f
    throw v0

    .line 474
    :cond_10
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 475
    .line 476
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 477
    .line 478
    .line 479
    move-result-object v0

    .line 480
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 481
    .line 482
    .line 483
    move-result-object v0

    .line 484
    return-object v0
.end method

.method public final u(IZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "+",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;>;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    new-instance p3, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;

    .line 54
    .line 55
    const/4 v2, 0x0

    .line 56
    invoke-direct {p3, p0, p1, p2, v2}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$2;-><init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;IZLkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    iput v3, v0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$loadVirtualGames$1;->label:I

    .line 60
    .line 61
    invoke-static {p3, v0}, Lkotlinx/coroutines/O;->f(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object p3

    .line 65
    if-ne p3, v1, :cond_3

    .line 66
    .line 67
    return-object v1

    .line 68
    :cond_3
    :goto_1
    check-cast p3, Lkotlin/Result;

    .line 69
    .line 70
    invoke-virtual {p3}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    return-object p1
.end method
