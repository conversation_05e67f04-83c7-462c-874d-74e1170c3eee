.class public final Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Prizes"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\r\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u0011\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u00032\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u000b\u0010\u000cR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\r\u0010\u000c\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;",
        "Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;",
        "giftType",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "bonusesCount",
        "",
        "freeSpinsCount",
        "<init>",
        "(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;II)V",
        "getGiftType",
        "()Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "getBonusesCount",
        "()I",
        "getFreeSpinsCount",
        "component1",
        "component2",
        "component3",
        "copy",
        "equals",
        "",
        "other",
        "",
        "hashCode",
        "toString",
        "",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final bonusesCount:I

.field private final freeSpinsCount:I

.field private final giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;II)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 5
    .line 6
    iput p2, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    .line 7
    .line 8
    iput p3, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic copy$default(Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;IIILjava/lang/Object;)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;
    .locals 0

    and-int/lit8 p5, p4, 0x1

    if-eqz p5, :cond_0

    iget-object p1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    :cond_0
    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_1

    iget p2, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    :cond_1
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_2

    iget p3, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    :cond_2
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->copy(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;II)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final component1()Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    return-object v0
.end method

.method public final component2()I
    .locals 1

    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    return v0
.end method

.method public final component3()I
    .locals 1

    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    return v0
.end method

.method public final copy(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;II)Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;
    .locals 1
    .param p1    # Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;

    invoke-direct {v0, p1, p2, p3}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;-><init>(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;II)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;

    iget-object v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    iget-object v3, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    iget v3, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    iget p1, p1, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    if-eq v1, p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public final getBonusesCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    .line 2
    .line 3
    return v0
.end method

.method public final getFreeSpinsCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    .line 2
    .line 3
    return v0
.end method

.method public final getGiftType()Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->giftType:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    iget v1, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->bonusesCount:I

    iget v2, p0, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Prizes;->freeSpinsCount:I

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Prizes(giftType="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", bonusesCount="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", freeSpinsCount="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
