.class public final synthetic LU11/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;

.field public final synthetic b:LL11/c;

.field public final synthetic c:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;LL11/c;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU11/r;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;

    iput-object p2, p0, LU11/r;->b:LL11/c;

    iput-object p3, p0, LU11/r;->c:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LU11/r;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;

    iget-object v1, p0, LU11/r;->b:LL11/c;

    iget-object v2, p0, LU11/r;->c:Lkotlin/jvm/functions/Function1;

    check-cast p1, Landroid/graphics/drawable/Drawable;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;->u(Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureLStyleBonus;LL11/c;Lkotlin/jvm/functions/Function1;Landroid/graphics/drawable/Drawable;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
