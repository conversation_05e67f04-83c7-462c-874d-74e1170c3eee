.class final Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.jackpot.data.repository.JackpotRepositoryImpl$getJackpot$2$1"
    f = "JackpotRepositoryImpl.kt"
    l = {
        0x16
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Pair<",
        "+",
        "LP40/c;",
        "+",
        "Ljava/lang/Long;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "",
        "token",
        "Lkotlin/Pair;",
        "LP40/c;",
        "",
        "<anonymous>",
        "(Ljava/lang/String;)Lkotlin/Pair;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;

    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    invoke-direct {v0, v1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;-><init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "LP40/c;",
            "Ljava/lang/Long;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Ljava/lang/String;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    .line 32
    .line 33
    invoke-static {v1}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->b(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)LJ40/b;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v3, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    .line 38
    .line 39
    invoke-static {v3}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->c(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)Lc8/h;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-interface {v3}, Lc8/h;->d()I

    .line 44
    .line 45
    .line 46
    move-result v3

    .line 47
    iget-object v4, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    .line 48
    .line 49
    invoke-static {v4}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->c(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)Lc8/h;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    invoke-interface {v4}, Lc8/h;->c()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    iput v2, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;->label:I

    .line 58
    .line 59
    invoke-virtual {v1, p1, v3, v4, p0}, LJ40/b;->b(Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    if-ne p1, v0, :cond_2

    .line 64
    .line 65
    return-object v0

    .line 66
    :cond_2
    :goto_0
    check-cast p1, LL40/a;

    .line 67
    .line 68
    invoke-virtual {p1}, Lg9/d;->a()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    check-cast p1, LL40/a$b;

    .line 73
    .line 74
    invoke-static {p1}, LK40/a;->a(LL40/a$b;)LP40/c;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-virtual {p1}, LL40/a$b;->a()Ljava/lang/Long;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-eqz p1, :cond_3

    .line 83
    .line 84
    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    .line 85
    .line 86
    .line 87
    move-result-wide v1

    .line 88
    goto :goto_1

    .line 89
    :cond_3
    const-wide/16 v1, 0x0

    .line 90
    .line 91
    :goto_1
    new-instance p1, Lkotlin/Pair;

    .line 92
    .line 93
    invoke-static {v1, v2}, LHc/a;->f(J)Ljava/lang/Long;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    invoke-direct {p1, v0, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 98
    .line 99
    .line 100
    return-object p1
.end method
