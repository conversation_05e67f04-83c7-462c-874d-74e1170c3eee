.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0014\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001:\u0001CBC\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001d0\u001cH\u0000\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010\"\u001a\u00020\u00122\u0006\u0010!\u001a\u00020 H\u0000\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008$\u0010\u0014J\u0013\u0010%\u001a\u00020\u0012*\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008%\u0010&R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u001a\u00108\u001a\u0008\u0012\u0004\u0012\u00020\u001d058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0018\u0010<\u001a\u0004\u0018\u0001098\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0018\u0010>\u001a\u0004\u0018\u0001098\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008=\u0010;R\u0016\u0010B\u001a\u00020?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008@\u0010A\u00a8\u0006D"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;",
        "getTournamentWinnerDataUseCase",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;",
        "getWinnersByDayUseCase",
        "LwX0/c;",
        "router",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V",
        "",
        "H3",
        "()V",
        "",
        "throwable",
        "C3",
        "(Ljava/lang/Throwable;)V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "A3",
        "()Lorg/xbet/uikit/components/lottie/a;",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
        "B3",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "date",
        "F3",
        "(Ljava/lang/String;)V",
        "D3",
        "I3",
        "(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V",
        "v1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;",
        "x1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;",
        "y1",
        "LwX0/c;",
        "F1",
        "Lm8/a;",
        "H1",
        "Lorg/xbet/ui_common/utils/M;",
        "I1",
        "LSX0/a;",
        "P1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lkotlinx/coroutines/flow/V;",
        "S1",
        "Lkotlinx/coroutines/flow/V;",
        "state",
        "Lkotlinx/coroutines/x0;",
        "V1",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "b2",
        "loadWinnersJob",
        "",
        "v2",
        "Z",
        "contentLoaded",
        "a",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V1:Lkotlinx/coroutines/x0;

.field public b2:Lkotlinx/coroutines/x0;

.field public final v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v2:Z

.field public final x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->y1:LwX0/c;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->F1:Lm8/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->H1:Lorg/xbet/ui_common/utils/M;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->I1:LSX0/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 17
    .line 18
    sget-object p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$a;

    .line 19
    .line 20
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->H3()V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method private final A3()Lorg/xbet/uikit/components/lottie/a;
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->I1:LSX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v7, 0x1c

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    return-object v0
.end method

.method private final C3(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->A3()Lorg/xbet/uikit/components/lottie/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->I3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->H1:Lorg/xbet/ui_common/utils/M;

    .line 14
    .line 15
    invoke-interface {v0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public static final E3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->C3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final G3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->C3(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final H3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$observeConnection$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    return-void
.end method

.method private static final J3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->G3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->E3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->J3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->v2:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lorg/xbet/uikit/components/lottie/a;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->A3()Lorg/xbet/uikit/components/lottie/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/GetTournamentWinnerDataUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/k;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->D3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->I3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->v2:Z

    .line 2
    .line 3
    return-void
.end method


# virtual methods
.method public final B3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final D3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->b2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->F1:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/i;

    .line 24
    .line 25
    invoke-direct {v3, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/i;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)V

    .line 26
    .line 27
    .line 28
    new-instance v7, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinners$2;

    .line 29
    .line 30
    const/4 v0, 0x0

    .line 31
    invoke-direct {v7, p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinners$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/16 v8, 0xa

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    const/4 v6, 0x0

    .line 39
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->b2:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public final F3(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->F1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/j;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/j;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$loadWinnersByDay$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final I3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;->F1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/h;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/h;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$send$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$send$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentWinnerViewModel$a;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
