.class public final LuV0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a1\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0005H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LnV0/b;",
        "Lorg/xbet/toto_bet/domain/TotoBetType;",
        "totoBetType",
        "LHX0/e;",
        "resourceManager",
        "",
        "currencySymbol",
        "",
        "LvV0/d;",
        "a",
        "(LnV0/b;Lorg/xbet/toto_bet/domain/TotoBetType;LHX0/e;Ljava/lang/String;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LnV0/b;Lorg/xbet/toto_bet/domain/TotoBetType;LHX0/e;Ljava/lang/String;)Ljava/util/List;
    .locals 24
    .param p0    # LnV0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/toto_bet/domain/TotoBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LnV0/b;",
            "Lorg/xbet/toto_bet/domain/TotoBetType;",
            "LHX0/e;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "LvV0/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    new-instance v2, LvV0/a;

    .line 6
    .line 7
    invoke-virtual/range {p0 .. p0}, LnV0/b;->k()Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    invoke-virtual/range {p0 .. p0}, LnV0/b;->i()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    sget v5, Lpb/k;->tirag:I

    .line 16
    .line 17
    invoke-virtual/range {p0 .. p0}, LnV0/b;->l()I

    .line 18
    .line 19
    .line 20
    move-result v6

    .line 21
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    const/4 v8, 0x1

    .line 26
    new-array v7, v8, [Ljava/lang/Object;

    .line 27
    .line 28
    const/4 v9, 0x0

    .line 29
    aput-object v6, v7, v9

    .line 30
    .line 31
    invoke-interface {v0, v5, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    invoke-virtual/range {p0 .. p0}, LnV0/b;->j()J

    .line 36
    .line 37
    .line 38
    move-result-wide v6

    .line 39
    invoke-direct/range {v2 .. v7}, LvV0/a;-><init>(Lorg/xbet/toto_bet/tirage/domain/model/TirageState;Ljava/lang/String;Ljava/lang/String;J)V

    .line 40
    .line 41
    .line 42
    sget-object v3, Lorg/xbet/toto_bet/domain/TotoBetType;->TOTO_1XTOTO:Lorg/xbet/toto_bet/domain/TotoBetType;

    .line 43
    .line 44
    const-string v4, ""

    .line 45
    .line 46
    const/4 v5, 0x0

    .line 47
    move-object/from16 v6, p1

    .line 48
    .line 49
    if-ne v6, v3, :cond_4

    .line 50
    .line 51
    invoke-virtual/range {p0 .. p0}, LnV0/b;->c()I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object v11

    .line 59
    invoke-virtual/range {p0 .. p0}, LnV0/b;->a()I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object v12

    .line 67
    sget-object v0, LY7/a;->a:LY7/a;

    .line 68
    .line 69
    invoke-virtual {v0}, LY7/a;->b()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-virtual/range {p0 .. p0}, LnV0/b;->h()Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    check-cast v1, LnV0/a;

    .line 82
    .line 83
    if-eqz v1, :cond_0

    .line 84
    .line 85
    invoke-virtual {v1}, LnV0/a;->a()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    goto :goto_0

    .line 90
    :cond_0
    move-object v1, v5

    .line 91
    :goto_0
    if-nez v1, :cond_1

    .line 92
    .line 93
    move-object v1, v4

    .line 94
    :cond_1
    new-instance v3, Ljava/lang/StringBuilder;

    .line 95
    .line 96
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 97
    .line 98
    .line 99
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 100
    .line 101
    .line 102
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object v15

    .line 109
    invoke-virtual/range {p0 .. p0}, LnV0/b;->h()Ljava/util/List;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    check-cast v0, LnV0/a;

    .line 118
    .line 119
    if-eqz v0, :cond_2

    .line 120
    .line 121
    invoke-virtual {v0}, LnV0/a;->b()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object v5

    .line 125
    :cond_2
    if-nez v5, :cond_3

    .line 126
    .line 127
    move-object/from16 v16, v4

    .line 128
    .line 129
    goto :goto_1

    .line 130
    :cond_3
    move-object/from16 v16, v5

    .line 131
    .line 132
    :goto_1
    invoke-virtual/range {p0 .. p0}, LnV0/b;->l()I

    .line 133
    .line 134
    .line 135
    move-result v0

    .line 136
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 137
    .line 138
    .line 139
    move-result-object v13

    .line 140
    invoke-virtual/range {p0 .. p0}, LnV0/b;->k()Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 141
    .line 142
    .line 143
    move-result-object v14

    .line 144
    new-instance v10, LvV0/c;

    .line 145
    .line 146
    invoke-direct/range {v10 .. v16}, LvV0/c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/toto_bet/tirage/domain/model/TirageState;Ljava/lang/String;Ljava/lang/String;)V

    .line 147
    .line 148
    .line 149
    goto/16 :goto_4

    .line 150
    .line 151
    :cond_4
    invoke-virtual/range {p0 .. p0}, LnV0/b;->k()Lorg/xbet/toto_bet/tirage/domain/model/TirageState;

    .line 152
    .line 153
    .line 154
    move-result-object v12

    .line 155
    invoke-virtual/range {p0 .. p0}, LnV0/b;->j()J

    .line 156
    .line 157
    .line 158
    move-result-wide v13

    .line 159
    sget v3, Lpb/k;->jackpot:I

    .line 160
    .line 161
    sget-object v6, LBU0/b;->a:LBU0/b;

    .line 162
    .line 163
    invoke-virtual/range {p0 .. p0}, LnV0/b;->b()Ljava/lang/String;

    .line 164
    .line 165
    .line 166
    move-result-object v7

    .line 167
    invoke-virtual {v6, v7, v1}, LBU0/b;->a(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 168
    .line 169
    .line 170
    move-result-object v7

    .line 171
    new-array v10, v8, [Ljava/lang/Object;

    .line 172
    .line 173
    aput-object v7, v10, v9

    .line 174
    .line 175
    invoke-interface {v0, v3, v10}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 176
    .line 177
    .line 178
    move-result-object v15

    .line 179
    invoke-virtual/range {p0 .. p0}, LnV0/b;->g()Ljava/lang/String;

    .line 180
    .line 181
    .line 182
    move-result-object v0

    .line 183
    invoke-virtual {v6, v0, v1}, LBU0/b;->a(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v16

    .line 187
    invoke-virtual/range {p0 .. p0}, LnV0/b;->c()I

    .line 188
    .line 189
    .line 190
    move-result v0

    .line 191
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 192
    .line 193
    .line 194
    move-result-object v17

    .line 195
    invoke-virtual/range {p0 .. p0}, LnV0/b;->e()I

    .line 196
    .line 197
    .line 198
    move-result v0

    .line 199
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 200
    .line 201
    .line 202
    move-result-object v18

    .line 203
    invoke-virtual/range {p0 .. p0}, LnV0/b;->d()I

    .line 204
    .line 205
    .line 206
    move-result v0

    .line 207
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 208
    .line 209
    .line 210
    move-result-object v19

    .line 211
    invoke-virtual/range {p0 .. p0}, LnV0/b;->f()Ljava/lang/String;

    .line 212
    .line 213
    .line 214
    move-result-object v0

    .line 215
    invoke-virtual {v6, v0, v1}, LBU0/b;->a(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 216
    .line 217
    .line 218
    move-result-object v20

    .line 219
    sget-object v0, LY7/a;->a:LY7/a;

    .line 220
    .line 221
    invoke-virtual {v0}, LY7/a;->b()Ljava/lang/String;

    .line 222
    .line 223
    .line 224
    move-result-object v0

    .line 225
    invoke-virtual/range {p0 .. p0}, LnV0/b;->h()Ljava/util/List;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 230
    .line 231
    .line 232
    move-result-object v1

    .line 233
    check-cast v1, LnV0/a;

    .line 234
    .line 235
    if-eqz v1, :cond_5

    .line 236
    .line 237
    invoke-virtual {v1}, LnV0/a;->a()Ljava/lang/String;

    .line 238
    .line 239
    .line 240
    move-result-object v1

    .line 241
    goto :goto_2

    .line 242
    :cond_5
    move-object v1, v5

    .line 243
    :goto_2
    if-nez v1, :cond_6

    .line 244
    .line 245
    move-object v1, v4

    .line 246
    :cond_6
    new-instance v3, Ljava/lang/StringBuilder;

    .line 247
    .line 248
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 249
    .line 250
    .line 251
    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 252
    .line 253
    .line 254
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 255
    .line 256
    .line 257
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 258
    .line 259
    .line 260
    move-result-object v22

    .line 261
    invoke-virtual/range {p0 .. p0}, LnV0/b;->h()Ljava/util/List;

    .line 262
    .line 263
    .line 264
    move-result-object v0

    .line 265
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 266
    .line 267
    .line 268
    move-result-object v0

    .line 269
    check-cast v0, LnV0/a;

    .line 270
    .line 271
    if-eqz v0, :cond_7

    .line 272
    .line 273
    invoke-virtual {v0}, LnV0/a;->b()Ljava/lang/String;

    .line 274
    .line 275
    .line 276
    move-result-object v5

    .line 277
    :cond_7
    if-nez v5, :cond_8

    .line 278
    .line 279
    move-object/from16 v23, v4

    .line 280
    .line 281
    goto :goto_3

    .line 282
    :cond_8
    move-object/from16 v23, v5

    .line 283
    .line 284
    :goto_3
    invoke-virtual/range {p0 .. p0}, LnV0/b;->l()I

    .line 285
    .line 286
    .line 287
    move-result v0

    .line 288
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 289
    .line 290
    .line 291
    move-result-object v21

    .line 292
    new-instance v11, LvV0/b;

    .line 293
    .line 294
    invoke-direct/range {v11 .. v23}, LvV0/b;-><init>(Lorg/xbet/toto_bet/tirage/domain/model/TirageState;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 295
    .line 296
    .line 297
    move-object v10, v11

    .line 298
    :goto_4
    const/4 v0, 0x2

    .line 299
    new-array v0, v0, [LvV0/d;

    .line 300
    .line 301
    aput-object v2, v0, v9

    .line 302
    .line 303
    aput-object v10, v0, v8

    .line 304
    .line 305
    invoke-static {v0}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 306
    .line 307
    .line 308
    move-result-object v0

    .line 309
    return-object v0
.end method
