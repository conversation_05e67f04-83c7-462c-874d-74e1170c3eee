.class public final synthetic Lorg/xbet/main_menu/impl/presentation/container/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/a;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/a;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;

    invoke-static {v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;->J2(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
