.class public final LtH0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LuH0/b;",
        "",
        "LwH0/a;",
        "a",
        "(LuH0/b;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LuH0/b;)Ljava/util/List;
    .locals 33
    .param p0    # LuH0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LuH0/b;",
            ")",
            "Ljava/util/List<",
            "LwH0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LuH0/b;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_25

    .line 7
    .line 8
    new-instance v2, Ljava/util/ArrayList;

    .line 9
    .line 10
    const/16 v3, 0xa

    .line 11
    .line 12
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 13
    .line 14
    .line 15
    move-result v4

    .line 16
    invoke-direct {v2, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-eqz v4, :cond_24

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v4

    .line 33
    check-cast v4, LuH0/a;

    .line 34
    .line 35
    invoke-virtual {v4}, LuH0/a;->d()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    const-string v6, ""

    .line 40
    .line 41
    if-nez v5, :cond_0

    .line 42
    .line 43
    move-object v5, v6

    .line 44
    :cond_0
    invoke-virtual {v4}, LuH0/a;->a()Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object v7

    .line 48
    if-eqz v7, :cond_21

    .line 49
    .line 50
    new-instance v8, Ljava/util/ArrayList;

    .line 51
    .line 52
    invoke-static {v7, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 53
    .line 54
    .line 55
    move-result v9

    .line 56
    invoke-direct {v8, v9}, Ljava/util/ArrayList;-><init>(I)V

    .line 57
    .line 58
    .line 59
    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 60
    .line 61
    .line 62
    move-result-object v7

    .line 63
    const/4 v9, 0x0

    .line 64
    const/4 v10, 0x0

    .line 65
    :goto_1
    invoke-interface {v7}, Ljava/util/Iterator;->hasNext()Z

    .line 66
    .line 67
    .line 68
    move-result v11

    .line 69
    if-eqz v11, :cond_22

    .line 70
    .line 71
    invoke-interface {v7}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v11

    .line 75
    add-int/lit8 v12, v10, 0x1

    .line 76
    .line 77
    if-gez v10, :cond_1

    .line 78
    .line 79
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 80
    .line 81
    .line 82
    :cond_1
    check-cast v11, LuH0/c;

    .line 83
    .line 84
    invoke-virtual {v11}, LuH0/c;->g()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v13

    .line 88
    if-nez v13, :cond_2

    .line 89
    .line 90
    move-object v15, v6

    .line 91
    goto :goto_2

    .line 92
    :cond_2
    move-object v15, v13

    .line 93
    :goto_2
    invoke-virtual {v11}, LuH0/c;->h()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v13

    .line 97
    if-nez v13, :cond_3

    .line 98
    .line 99
    move-object/from16 v16, v6

    .line 100
    .line 101
    goto :goto_3

    .line 102
    :cond_3
    move-object/from16 v16, v13

    .line 103
    .line 104
    :goto_3
    invoke-virtual {v11}, LuH0/c;->d()Ljava/lang/Integer;

    .line 105
    .line 106
    .line 107
    move-result-object v13

    .line 108
    if-eqz v13, :cond_4

    .line 109
    .line 110
    invoke-virtual {v13}, Ljava/lang/Integer;->intValue()I

    .line 111
    .line 112
    .line 113
    move-result v13

    .line 114
    move/from16 v17, v13

    .line 115
    .line 116
    goto :goto_4

    .line 117
    :cond_4
    const/16 v17, 0x0

    .line 118
    .line 119
    :goto_4
    invoke-virtual {v11}, LuH0/c;->e()Ljava/lang/Integer;

    .line 120
    .line 121
    .line 122
    move-result-object v13

    .line 123
    if-eqz v13, :cond_5

    .line 124
    .line 125
    invoke-virtual {v13}, Ljava/lang/Integer;->intValue()I

    .line 126
    .line 127
    .line 128
    move-result v13

    .line 129
    move/from16 v18, v13

    .line 130
    .line 131
    goto :goto_5

    .line 132
    :cond_5
    const/16 v18, 0x0

    .line 133
    .line 134
    :goto_5
    invoke-virtual {v4}, LuH0/a;->a()Ljava/util/List;

    .line 135
    .line 136
    .line 137
    move-result-object v13

    .line 138
    invoke-static {v13}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 139
    .line 140
    .line 141
    move-result v13

    .line 142
    if-ne v10, v13, :cond_7

    .line 143
    .line 144
    invoke-virtual {v4}, LuH0/a;->b()Ljava/lang/Integer;

    .line 145
    .line 146
    .line 147
    move-result-object v13

    .line 148
    if-eqz v13, :cond_7

    .line 149
    .line 150
    invoke-virtual {v13}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 151
    .line 152
    .line 153
    move-result-object v13

    .line 154
    if-nez v13, :cond_6

    .line 155
    .line 156
    goto :goto_6

    .line 157
    :cond_6
    move-object/from16 v19, v13

    .line 158
    .line 159
    goto :goto_7

    .line 160
    :cond_7
    :goto_6
    move-object/from16 v19, v6

    .line 161
    .line 162
    :goto_7
    invoke-virtual {v4}, LuH0/a;->a()Ljava/util/List;

    .line 163
    .line 164
    .line 165
    move-result-object v13

    .line 166
    invoke-static {v13}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 167
    .line 168
    .line 169
    move-result v13

    .line 170
    if-ne v10, v13, :cond_9

    .line 171
    .line 172
    invoke-virtual {v4}, LuH0/a;->c()Ljava/lang/Integer;

    .line 173
    .line 174
    .line 175
    move-result-object v10

    .line 176
    if-eqz v10, :cond_9

    .line 177
    .line 178
    invoke-virtual {v10}, Ljava/lang/Integer;->toString()Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object v10

    .line 182
    if-nez v10, :cond_8

    .line 183
    .line 184
    goto :goto_8

    .line 185
    :cond_8
    move-object/from16 v20, v10

    .line 186
    .line 187
    goto :goto_9

    .line 188
    :cond_9
    :goto_8
    move-object/from16 v20, v6

    .line 189
    .line 190
    :goto_9
    invoke-virtual {v11}, LuH0/c;->f()Ljava/lang/Integer;

    .line 191
    .line 192
    .line 193
    move-result-object v10

    .line 194
    if-eqz v10, :cond_a

    .line 195
    .line 196
    invoke-virtual {v10}, Ljava/lang/Integer;->intValue()I

    .line 197
    .line 198
    .line 199
    move-result v10

    .line 200
    move/from16 v21, v10

    .line 201
    .line 202
    goto :goto_a

    .line 203
    :cond_a
    const/16 v21, 0x0

    .line 204
    .line 205
    :goto_a
    invoke-virtual {v11}, LuH0/c;->j()Ljava/lang/Boolean;

    .line 206
    .line 207
    .line 208
    move-result-object v10

    .line 209
    if-eqz v10, :cond_b

    .line 210
    .line 211
    invoke-virtual {v10}, Ljava/lang/Boolean;->booleanValue()Z

    .line 212
    .line 213
    .line 214
    move-result v10

    .line 215
    move/from16 v22, v10

    .line 216
    .line 217
    goto :goto_b

    .line 218
    :cond_b
    const/16 v22, 0x0

    .line 219
    .line 220
    :goto_b
    invoke-virtual {v11}, LuH0/c;->a()Ljava/lang/String;

    .line 221
    .line 222
    .line 223
    move-result-object v10

    .line 224
    if-nez v10, :cond_c

    .line 225
    .line 226
    move-object/from16 v23, v6

    .line 227
    .line 228
    goto :goto_c

    .line 229
    :cond_c
    move-object/from16 v23, v10

    .line 230
    .line 231
    :goto_c
    invoke-virtual {v11}, LuH0/c;->b()Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object v10

    .line 235
    if-nez v10, :cond_d

    .line 236
    .line 237
    move-object/from16 v24, v6

    .line 238
    .line 239
    goto :goto_d

    .line 240
    :cond_d
    move-object/from16 v24, v10

    .line 241
    .line 242
    :goto_d
    invoke-virtual {v11}, LuH0/c;->c()Ljava/util/List;

    .line 243
    .line 244
    .line 245
    move-result-object v10

    .line 246
    if-eqz v10, :cond_15

    .line 247
    .line 248
    new-instance v13, Ljava/util/ArrayList;

    .line 249
    .line 250
    invoke-static {v10, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 251
    .line 252
    .line 253
    move-result v14

    .line 254
    invoke-direct {v13, v14}, Ljava/util/ArrayList;-><init>(I)V

    .line 255
    .line 256
    .line 257
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 258
    .line 259
    .line 260
    move-result-object v10

    .line 261
    :goto_e
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 262
    .line 263
    .line 264
    move-result v14

    .line 265
    if-eqz v14, :cond_16

    .line 266
    .line 267
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 268
    .line 269
    .line 270
    move-result-object v14

    .line 271
    check-cast v14, LuH0/d;

    .line 272
    .line 273
    new-instance v25, LwH0/c;

    .line 274
    .line 275
    invoke-virtual {v14}, LuH0/d;->a()Ljava/lang/String;

    .line 276
    .line 277
    .line 278
    move-result-object v26

    .line 279
    if-nez v26, :cond_e

    .line 280
    .line 281
    move-object/from16 v26, v6

    .line 282
    .line 283
    :cond_e
    invoke-virtual {v14}, LuH0/d;->b()Ljava/lang/String;

    .line 284
    .line 285
    .line 286
    move-result-object v27

    .line 287
    if-nez v27, :cond_f

    .line 288
    .line 289
    move-object/from16 v27, v6

    .line 290
    .line 291
    :cond_f
    invoke-virtual {v14}, LuH0/d;->c()Ljava/lang/Boolean;

    .line 292
    .line 293
    .line 294
    move-result-object v28

    .line 295
    if-eqz v28, :cond_10

    .line 296
    .line 297
    invoke-virtual/range {v28 .. v28}, Ljava/lang/Boolean;->booleanValue()Z

    .line 298
    .line 299
    .line 300
    move-result v28

    .line 301
    goto :goto_f

    .line 302
    :cond_10
    const/16 v28, 0x0

    .line 303
    .line 304
    :goto_f
    invoke-virtual {v14}, LuH0/d;->g()Ljava/lang/Boolean;

    .line 305
    .line 306
    .line 307
    move-result-object v29

    .line 308
    if-eqz v29, :cond_11

    .line 309
    .line 310
    invoke-virtual/range {v29 .. v29}, Ljava/lang/Boolean;->booleanValue()Z

    .line 311
    .line 312
    .line 313
    move-result v29

    .line 314
    goto :goto_10

    .line 315
    :cond_11
    const/16 v29, 0x0

    .line 316
    .line 317
    :goto_10
    invoke-virtual {v14}, LuH0/d;->f()Ljava/lang/Boolean;

    .line 318
    .line 319
    .line 320
    move-result-object v30

    .line 321
    if-eqz v30, :cond_12

    .line 322
    .line 323
    invoke-virtual/range {v30 .. v30}, Ljava/lang/Boolean;->booleanValue()Z

    .line 324
    .line 325
    .line 326
    move-result v30

    .line 327
    goto :goto_11

    .line 328
    :cond_12
    const/16 v30, 0x0

    .line 329
    .line 330
    :goto_11
    invoke-virtual {v14}, LuH0/d;->d()Ljava/lang/Boolean;

    .line 331
    .line 332
    .line 333
    move-result-object v31

    .line 334
    if-eqz v31, :cond_13

    .line 335
    .line 336
    invoke-virtual/range {v31 .. v31}, Ljava/lang/Boolean;->booleanValue()Z

    .line 337
    .line 338
    .line 339
    move-result v31

    .line 340
    goto :goto_12

    .line 341
    :cond_13
    const/16 v31, 0x0

    .line 342
    .line 343
    :goto_12
    invoke-virtual {v14}, LuH0/d;->e()Ljava/lang/Boolean;

    .line 344
    .line 345
    .line 346
    move-result-object v14

    .line 347
    if-eqz v14, :cond_14

    .line 348
    .line 349
    invoke-virtual {v14}, Ljava/lang/Boolean;->booleanValue()Z

    .line 350
    .line 351
    .line 352
    move-result v14

    .line 353
    move/from16 v32, v14

    .line 354
    .line 355
    goto :goto_13

    .line 356
    :cond_14
    const/16 v32, 0x0

    .line 357
    .line 358
    :goto_13
    invoke-direct/range {v25 .. v32}, LwH0/c;-><init>(Ljava/lang/String;Ljava/lang/String;ZZZZZ)V

    .line 359
    .line 360
    .line 361
    move-object/from16 v14, v25

    .line 362
    .line 363
    invoke-interface {v13, v14}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 364
    .line 365
    .line 366
    goto :goto_e

    .line 367
    :cond_15
    move-object v13, v1

    .line 368
    :cond_16
    if-nez v13, :cond_17

    .line 369
    .line 370
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 371
    .line 372
    .line 373
    move-result-object v13

    .line 374
    :cond_17
    move-object/from16 v25, v13

    .line 375
    .line 376
    invoke-virtual {v11}, LuH0/c;->i()Ljava/util/List;

    .line 377
    .line 378
    .line 379
    move-result-object v10

    .line 380
    if-eqz v10, :cond_1e

    .line 381
    .line 382
    new-instance v11, Ljava/util/ArrayList;

    .line 383
    .line 384
    invoke-static {v10, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 385
    .line 386
    .line 387
    move-result v13

    .line 388
    invoke-direct {v11, v13}, Ljava/util/ArrayList;-><init>(I)V

    .line 389
    .line 390
    .line 391
    invoke-interface {v10}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 392
    .line 393
    .line 394
    move-result-object v10

    .line 395
    :goto_14
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 396
    .line 397
    .line 398
    move-result v13

    .line 399
    if-eqz v13, :cond_1f

    .line 400
    .line 401
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 402
    .line 403
    .line 404
    move-result-object v13

    .line 405
    check-cast v13, LuH0/e;

    .line 406
    .line 407
    new-instance v26, LwH0/d;

    .line 408
    .line 409
    invoke-virtual {v13}, LuH0/e;->a()Ljava/lang/Integer;

    .line 410
    .line 411
    .line 412
    move-result-object v14

    .line 413
    if-eqz v14, :cond_18

    .line 414
    .line 415
    invoke-virtual {v14}, Ljava/lang/Integer;->intValue()I

    .line 416
    .line 417
    .line 418
    move-result v14

    .line 419
    move/from16 v27, v14

    .line 420
    .line 421
    goto :goto_15

    .line 422
    :cond_18
    const/16 v27, 0x0

    .line 423
    .line 424
    :goto_15
    invoke-virtual {v13}, LuH0/e;->b()Ljava/lang/Integer;

    .line 425
    .line 426
    .line 427
    move-result-object v14

    .line 428
    if-eqz v14, :cond_19

    .line 429
    .line 430
    invoke-virtual {v14}, Ljava/lang/Integer;->intValue()I

    .line 431
    .line 432
    .line 433
    move-result v14

    .line 434
    move/from16 v28, v14

    .line 435
    .line 436
    goto :goto_16

    .line 437
    :cond_19
    const/16 v28, 0x0

    .line 438
    .line 439
    :goto_16
    invoke-virtual {v13}, LuH0/e;->c()Ljava/lang/Integer;

    .line 440
    .line 441
    .line 442
    move-result-object v14

    .line 443
    if-eqz v14, :cond_1a

    .line 444
    .line 445
    invoke-virtual {v14}, Ljava/lang/Integer;->intValue()I

    .line 446
    .line 447
    .line 448
    move-result v14

    .line 449
    move/from16 v29, v14

    .line 450
    .line 451
    goto :goto_17

    .line 452
    :cond_1a
    const/16 v29, 0x0

    .line 453
    .line 454
    :goto_17
    invoke-virtual {v13}, LuH0/e;->d()Ljava/lang/Boolean;

    .line 455
    .line 456
    .line 457
    move-result-object v14

    .line 458
    if-eqz v14, :cond_1b

    .line 459
    .line 460
    invoke-virtual {v14}, Ljava/lang/Boolean;->booleanValue()Z

    .line 461
    .line 462
    .line 463
    move-result v14

    .line 464
    move/from16 v30, v14

    .line 465
    .line 466
    goto :goto_18

    .line 467
    :cond_1b
    const/16 v30, 0x0

    .line 468
    .line 469
    :goto_18
    invoke-virtual {v13}, LuH0/e;->f()Ljava/lang/Boolean;

    .line 470
    .line 471
    .line 472
    move-result-object v14

    .line 473
    if-eqz v14, :cond_1c

    .line 474
    .line 475
    invoke-virtual {v14}, Ljava/lang/Boolean;->booleanValue()Z

    .line 476
    .line 477
    .line 478
    move-result v14

    .line 479
    move/from16 v31, v14

    .line 480
    .line 481
    goto :goto_19

    .line 482
    :cond_1c
    const/16 v31, 0x0

    .line 483
    .line 484
    :goto_19
    invoke-virtual {v13}, LuH0/e;->e()Ljava/lang/Boolean;

    .line 485
    .line 486
    .line 487
    move-result-object v13

    .line 488
    if-eqz v13, :cond_1d

    .line 489
    .line 490
    invoke-virtual {v13}, Ljava/lang/Boolean;->booleanValue()Z

    .line 491
    .line 492
    .line 493
    move-result v13

    .line 494
    move/from16 v32, v13

    .line 495
    .line 496
    goto :goto_1a

    .line 497
    :cond_1d
    const/16 v32, 0x0

    .line 498
    .line 499
    :goto_1a
    invoke-direct/range {v26 .. v32}, LwH0/d;-><init>(IIIZZZ)V

    .line 500
    .line 501
    .line 502
    move-object/from16 v13, v26

    .line 503
    .line 504
    invoke-interface {v11, v13}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 505
    .line 506
    .line 507
    goto :goto_14

    .line 508
    :cond_1e
    move-object v11, v1

    .line 509
    :cond_1f
    if-nez v11, :cond_20

    .line 510
    .line 511
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 512
    .line 513
    .line 514
    move-result-object v11

    .line 515
    :cond_20
    move-object/from16 v26, v11

    .line 516
    .line 517
    new-instance v14, LwH0/b;

    .line 518
    .line 519
    invoke-direct/range {v14 .. v26}, LwH0/b;-><init>(Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;IZLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V

    .line 520
    .line 521
    .line 522
    invoke-interface {v8, v14}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 523
    .line 524
    .line 525
    move v10, v12

    .line 526
    goto/16 :goto_1

    .line 527
    .line 528
    :cond_21
    move-object v8, v1

    .line 529
    :cond_22
    if-nez v8, :cond_23

    .line 530
    .line 531
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 532
    .line 533
    .line 534
    move-result-object v8

    .line 535
    :cond_23
    new-instance v4, LwH0/a;

    .line 536
    .line 537
    invoke-direct {v4, v5, v8}, LwH0/a;-><init>(Ljava/lang/String;Ljava/util/List;)V

    .line 538
    .line 539
    .line 540
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 541
    .line 542
    .line 543
    goto/16 :goto_0

    .line 544
    .line 545
    :cond_24
    move-object v1, v2

    .line 546
    :cond_25
    if-nez v1, :cond_26

    .line 547
    .line 548
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 549
    .line 550
    .line 551
    move-result-object v0

    .line 552
    return-object v0

    .line 553
    :cond_26
    return-object v1
.end method
