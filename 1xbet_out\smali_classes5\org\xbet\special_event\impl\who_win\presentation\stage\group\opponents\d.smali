.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->e:LBc/a;

    .line 13
    .line 14
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
            ">;)",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static c(Landroidx/lifecycle/Q;LHX0/e;Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;Lm8/a;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;
    .locals 7

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    invoke-direct/range {v0 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;-><init>(Landroidx/lifecycle/Q;LHX0/e;Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;Lm8/a;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method


# virtual methods
.method public b(Landroidx/lifecycle/Q;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v2, v0

    .line 8
    check-cast v2, LHX0/e;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v3, v0

    .line 17
    check-cast v3, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v4, v0

    .line 26
    check-cast v4, Lm8/a;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v5, v0

    .line 35
    check-cast v5, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v6, v0

    .line 44
    check-cast v6, Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    .line 45
    .line 46
    move-object v1, p1

    .line 47
    invoke-static/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/d;->c(Landroidx/lifecycle/Q;LHX0/e;Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/model/OpponentsScreenParams;Lm8/a;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;)Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsViewModel;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    return-object p1
.end method
