.class public final Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;
.super Landroidx/recyclerview/widget/RecyclerView$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0019\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0006*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\n\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "org/xbet/ui_common/circleindicator/CircleIndicator$c",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "",
        "onChanged",
        "()V",
        "",
        "positionStart",
        "itemCount",
        "onItemRangeInserted",
        "(II)V",
        "onItemRangeRemoved",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;


# direct methods
.method public constructor <init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onChanged()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->f(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/viewpager2/widget/ViewPager2;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v1}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const/4 v1, 0x0

    .line 21
    :goto_0
    invoke-static {v0, v1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public onItemRangeInserted(II)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->f(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/viewpager2/widget/ViewPager2;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v1}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const/4 v1, 0x0

    .line 21
    :goto_0
    invoke-static {v0, v1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 22
    .line 23
    .line 24
    invoke-super {p0, p1, p2}, Landroidx/recyclerview/widget/RecyclerView$i;->onItemRangeInserted(II)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public onItemRangeRemoved(II)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->f(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/viewpager2/widget/ViewPager2;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v1}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-virtual {v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const/4 v1, 0x0

    .line 21
    :goto_0
    invoke-static {v0, v1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 22
    .line 23
    .line 24
    invoke-super {p0, p1, p2}, Landroidx/recyclerview/widget/RecyclerView$i;->onItemRangeRemoved(II)V

    .line 25
    .line 26
    .line 27
    return-void
.end method
