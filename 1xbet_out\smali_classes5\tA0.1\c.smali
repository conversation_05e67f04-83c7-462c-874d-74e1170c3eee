.class public final LtA0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008!\u0018\u00002\u00020\u0001BW\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\u0006\u0010\n\u001a\u00020\u0002\u0012\u0006\u0010\u000b\u001a\u00020\u0002\u0012\u0006\u0010\u000c\u001a\u00020\u0002\u0012\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0015\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0015\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\u0015\u0010\u0017\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0018J\u001d\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u001b\u001a\u00020\u00022\u0006\u0010\t\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001e\u001a\u0004\u0008\u001f\u0010 R\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u001e\u001a\u0004\u0008!\u0010 R\"\u0010\u0006\u001a\u00020\u00058\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0013\u0010\"\u001a\u0004\u0008#\u0010$\"\u0004\u0008%\u0010\u0014R\"\u0010\u0007\u001a\u00020\u00058\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0017\u0010\"\u001a\u0004\u0008&\u0010$\"\u0004\u0008\'\u0010\u0014R\"\u0010\u0008\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008(\u0010\u001e\u001a\u0004\u0008)\u0010 \"\u0004\u0008*\u0010\u0018R\"\u0010\t\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008+\u0010\u001e\u001a\u0004\u0008,\u0010 \"\u0004\u0008-\u0010\u0018R\"\u0010\n\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008&\u0010\u001e\u001a\u0004\u0008+\u0010 \"\u0004\u0008.\u0010\u0018R\u0017\u0010\u000b\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008,\u0010\u001e\u001a\u0004\u0008/\u0010 R\u0017\u0010\u000c\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u00080\u0010\u001e\u001a\u0004\u00080\u0010 R\u0017\u0010\u000e\u001a\u00020\r8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001f\u00101\u001a\u0004\u0008(\u00102\u00a8\u00063"
    }
    d2 = {
        "LtA0/c;",
        "",
        "",
        "maxHeight",
        "minHeight",
        "",
        "normalAlpha",
        "compressedAlpha",
        "normalHeight",
        "compressedHeight",
        "bottomMargin",
        "minBottomMargin",
        "maxBottomMargin",
        "LtA0/b;",
        "animateChangeListener",
        "<init>",
        "(IIFFIIIIILtA0/b;)V",
        "newAlpha",
        "",
        "c",
        "(F)V",
        "b",
        "newHeight",
        "d",
        "(I)V",
        "newMargin",
        "a",
        "expandedHeight",
        "o",
        "(II)V",
        "I",
        "j",
        "()I",
        "l",
        "F",
        "m",
        "()F",
        "setNormalAlpha",
        "g",
        "setCompressedAlpha",
        "e",
        "n",
        "setNormalHeight",
        "f",
        "h",
        "setCompressedHeight",
        "setBottomMargin",
        "k",
        "i",
        "LtA0/b;",
        "()LtA0/b;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:I

.field public c:F

.field public d:F

.field public e:I

.field public f:I

.field public g:I

.field public final h:I

.field public final i:I

.field public final j:LtA0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(IIFFIIIIILtA0/b;)V
    .locals 0
    .param p10    # LtA0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, LtA0/c;->a:I

    .line 5
    .line 6
    iput p2, p0, LtA0/c;->b:I

    .line 7
    .line 8
    iput p3, p0, LtA0/c;->c:F

    .line 9
    .line 10
    iput p4, p0, LtA0/c;->d:F

    .line 11
    .line 12
    iput p5, p0, LtA0/c;->e:I

    .line 13
    .line 14
    iput p6, p0, LtA0/c;->f:I

    .line 15
    .line 16
    iput p7, p0, LtA0/c;->g:I

    .line 17
    .line 18
    iput p8, p0, LtA0/c;->h:I

    .line 19
    .line 20
    iput p9, p0, LtA0/c;->i:I

    .line 21
    .line 22
    iput-object p10, p0, LtA0/c;->j:LtA0/b;

    .line 23
    .line 24
    return-void
.end method


# virtual methods
.method public final a(I)V
    .locals 1

    .line 1
    iput p1, p0, LtA0/c;->g:I

    .line 2
    .line 3
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 4
    .line 5
    invoke-interface {v0, p1}, LtA0/b;->r(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final b(F)V
    .locals 1

    .line 1
    iput p1, p0, LtA0/c;->d:F

    .line 2
    .line 3
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 4
    .line 5
    invoke-interface {v0, p1}, LtA0/b;->c(F)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final c(F)V
    .locals 1

    .line 1
    iput p1, p0, LtA0/c;->c:F

    .line 2
    .line 3
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 4
    .line 5
    invoke-interface {v0, p1}, LtA0/b;->l(F)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final d(I)V
    .locals 1

    .line 1
    iput p1, p0, LtA0/c;->e:I

    .line 2
    .line 3
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 4
    .line 5
    invoke-interface {v0, p1}, LtA0/b;->o(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final e()LtA0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->g:I

    .line 2
    .line 3
    return v0
.end method

.method public final g()F
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->d:F

    .line 2
    .line 3
    return v0
.end method

.method public final h()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->f:I

    .line 2
    .line 3
    return v0
.end method

.method public final i()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->i:I

    .line 2
    .line 3
    return v0
.end method

.method public final j()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->h:I

    .line 2
    .line 3
    return v0
.end method

.method public final l()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final m()F
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->c:F

    .line 2
    .line 3
    return v0
.end method

.method public final n()I
    .locals 1

    .line 1
    iget v0, p0, LtA0/c;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public final o(II)V
    .locals 1

    .line 1
    iput p1, p0, LtA0/c;->e:I

    .line 2
    .line 3
    iput p2, p0, LtA0/c;->f:I

    .line 4
    .line 5
    iget-object v0, p0, LtA0/c;->j:LtA0/b;

    .line 6
    .line 7
    invoke-interface {v0, p1}, LtA0/b;->o(I)V

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, LtA0/c;->j:LtA0/b;

    .line 11
    .line 12
    invoke-interface {p1, p2}, LtA0/b;->h(I)V

    .line 13
    .line 14
    .line 15
    return-void
.end method
