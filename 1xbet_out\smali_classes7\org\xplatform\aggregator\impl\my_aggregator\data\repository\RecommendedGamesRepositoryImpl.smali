.class public final Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Loa1/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J3\u0010\u0011\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00100\u000f0\u000e2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015R \u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00180\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;",
        "Loa1/a;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;",
        "recommendedGamesPagingDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Lc8/h;)V",
        "",
        "partitionId",
        "",
        "endPoint",
        "",
        "hasAggregatorBrands",
        "Lkotlinx/coroutines/flow/e;",
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "a",
        "(JLjava/lang/String;Z)Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;",
        "b",
        "Lc8/h;",
        "Landroidx/paging/o;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
        "LL91/d;",
        "c",
        "Landroidx/paging/o;",
        "factory",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroidx/paging/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/paging/o<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;",
            "LL91/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;Lc8/h;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->b:Lc8/h;

    .line 7
    .line 8
    new-instance p1, Landroidx/paging/o;

    .line 9
    .line 10
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/a;

    .line 11
    .line 12
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/a;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;)V

    .line 13
    .line 14
    .line 15
    invoke-direct {p1, p2}, Landroidx/paging/o;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 16
    .line 17
    .line 18
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->c:Landroidx/paging/o;

    .line 19
    .line 20
    return-void
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->c(Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;)Landroidx/paging/PagingSource;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;)Landroidx/paging/PagingSource;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/RecommendedGamesPagingDataSource;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a(JLjava/lang/String;Z)Lkotlinx/coroutines/flow/e;
    .locals 9
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Z)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroidx/paging/C;

    .line 2
    .line 3
    const/16 v7, 0x38

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/16 v1, 0x1e

    .line 7
    .line 8
    const/4 v2, 0x3

    .line 9
    const/4 v3, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    invoke-direct/range {v0 .. v8}, Landroidx/paging/C;-><init>(IIZIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 14
    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->c:Landroidx/paging/o;

    .line 17
    .line 18
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;

    .line 19
    .line 20
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl;->b:Lc8/h;

    .line 25
    .line 26
    invoke-interface {p2}, Lc8/h;->a()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    invoke-direct {v2, p1, v3, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/data/datasource/remote/a;-><init>(Ljava/lang/Long;ILjava/lang/String;)V

    .line 31
    .line 32
    .line 33
    new-instance p1, Landroidx/paging/Pager;

    .line 34
    .line 35
    invoke-direct {p1, v0, v2, v1}, Landroidx/paging/Pager;-><init>(Landroidx/paging/C;Ljava/lang/Object;Lkotlin/jvm/functions/Function0;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p1}, Landroidx/paging/Pager;->a()Lkotlinx/coroutines/flow/e;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl$getRecommendedGames$$inlined$map$1;

    .line 43
    .line 44
    invoke-direct {p2, p1, p3, p4}, Lorg/xplatform/aggregator/impl/my_aggregator/data/repository/RecommendedGamesRepositoryImpl$getRecommendedGames$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Ljava/lang/String;Z)V

    .line 45
    .line 46
    .line 47
    return-object p2
.end method
