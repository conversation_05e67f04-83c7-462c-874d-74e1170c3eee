.class public final LHL0/b;
.super Ljava/lang/Object;


# static fields
.field public static appCompatImageView:I = 0x7f0a0103

.field public static circuitsStadiumRv:I = 0x7f0a0478

.field public static divider:I = 0x7f0a0674

.field public static errorBackground:I = 0x7f0a073c

.field public static errorGroup:I = 0x7f0a073d

.field public static errorIv:I = 0x7f0a0740

.field public static expandIv:I = 0x7f0a0793

.field public static headerTv:I = 0x7f0a0a7d

.field public static imageStadiumRv:I = 0x7f0a0af1

.field public static indicator:I = 0x7f0a0b8e

.field public static infoGroup:I = 0x7f0a0b98

.field public static infoStadiumRv:I = 0x7f0a0b9e

.field public static infoTv:I = 0x7f0a0ba2

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static parentIndicator:I = 0x7f0a104f

.field public static recyclerView:I = 0x7f0a11ad

.field public static rvHorsesInfo:I = 0x7f0a1277

.field public static separator:I = 0x7f0a13c7

.field public static shimmer:I = 0x7f0a1400

.field public static stadiumIv:I = 0x7f0a15cc

.field public static toolbar:I = 0x7f0a183e

.field public static tvNameHorse:I = 0x7f0a1b32

.field public static tvTextTitle:I = 0x7f0a1c8d


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
