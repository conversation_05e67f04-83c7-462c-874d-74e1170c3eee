.class public final LV01/n;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\"\u001d\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00008\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0002\u0010\u0003\u001a\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0007"
    }
    d2 = {
        "Landroidx/compose/runtime/x0;",
        "Lorg/xbet/uikit/compose/color/IndividualColors;",
        "a",
        "Landroidx/compose/runtime/x0;",
        "c",
        "()Landroidx/compose/runtime/x0;",
        "LocalIndividualColors",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/x0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/IndividualColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LV01/m;

    .line 2
    .line 3
    invoke-direct {v0}, LV01/m;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Landroidx/compose/runtime/CompositionLocalKt;->g(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/x0;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, LV01/n;->a:Landroidx/compose/runtime/x0;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic a()Lorg/xbet/uikit/compose/color/IndividualColors;
    .locals 1

    .line 1
    invoke-static {}, LV01/n;->b()Lorg/xbet/uikit/compose/color/IndividualColors;

    move-result-object v0

    return-object v0
.end method

.method public static final b()Lorg/xbet/uikit/compose/color/IndividualColors;
    .locals 18

    .line 1
    new-instance v0, Lorg/xbet/uikit/compose/color/IndividualColors;

    .line 2
    .line 3
    sget-object v1, Landroidx/compose/ui/graphics/v0;->b:Landroidx/compose/ui/graphics/v0$a;

    .line 4
    .line 5
    move-object v3, v1

    .line 6
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 7
    .line 8
    .line 9
    move-result-wide v1

    .line 10
    move-object v5, v3

    .line 11
    invoke-virtual {v5}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v3

    .line 15
    move-object v7, v5

    .line 16
    invoke-virtual {v7}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 17
    .line 18
    .line 19
    move-result-wide v5

    .line 20
    move-object v9, v7

    .line 21
    invoke-virtual {v9}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 22
    .line 23
    .line 24
    move-result-wide v7

    .line 25
    move-object v11, v9

    .line 26
    invoke-virtual {v11}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 27
    .line 28
    .line 29
    move-result-wide v9

    .line 30
    move-object v13, v11

    .line 31
    invoke-virtual {v13}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 32
    .line 33
    .line 34
    move-result-wide v11

    .line 35
    move-object v15, v13

    .line 36
    invoke-virtual {v15}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 37
    .line 38
    .line 39
    move-result-wide v13

    .line 40
    invoke-virtual {v15}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 41
    .line 42
    .line 43
    move-result-wide v15

    .line 44
    const/16 v17, 0x0

    .line 45
    .line 46
    invoke-direct/range {v0 .. v17}, Lorg/xbet/uikit/compose/color/IndividualColors;-><init>(JJJJJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 47
    .line 48
    .line 49
    return-object v0
.end method

.method public static final c()Landroidx/compose/runtime/x0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/x0<",
            "Lorg/xbet/uikit/compose/color/IndividualColors;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LV01/n;->a:Landroidx/compose/runtime/x0;

    .line 2
    .line 3
    return-object v0
.end method
