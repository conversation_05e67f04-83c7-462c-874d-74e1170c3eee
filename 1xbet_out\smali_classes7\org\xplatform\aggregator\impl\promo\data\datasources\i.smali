.class public final Lorg/xplatform/aggregator/impl/promo/data/datasources/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/promo/data/datasources/i$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()Lorg/xplatform/aggregator/impl/promo/data/datasources/i;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/promo/data/datasources/i$a;->a()Lorg/xplatform/aggregator/impl/promo/data/datasources/i;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public static c()Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/promo/data/datasources/i;->c()Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/i;->b()Lorg/xplatform/aggregator/impl/promo/data/datasources/AggregatorSocialNetworksLocalDataSource;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
