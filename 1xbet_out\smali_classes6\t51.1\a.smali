.class public final Lt51/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lt51/a$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0008\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a\u0013\u0010\t\u001a\u00020\u0008*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\u0013\u0010\u000b\u001a\u00020\u0003*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u001a\u001b\u0010\u000e\u001a\u00020\r*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u001a#\u0010\u0010\u001a\u00020\r*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011\u001a\u0013\u0010\u0012\u001a\u00020\u0003*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u000c\u001a\u0013\u0010\u0013\u001a\u00020\u0003*\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u000c\u001a\u001b\u0010\u0014\u001a\u00020\r*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u000f\u00a8\u0006\u0015"
    }
    d2 = {
        "Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;",
        "LHX0/e;",
        "resourceManager",
        "",
        "hasBlockAuthUprid",
        "Lu51/a;",
        "h",
        "(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;Z)Lu51/a;",
        "",
        "d",
        "(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)I",
        "f",
        "(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z",
        "",
        "e",
        "(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;)Ljava/lang/String;",
        "a",
        "(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;Z)Ljava/lang/String;",
        "g",
        "c",
        "b",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;Z)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    packed-switch p0, :pswitch_data_0

    .line 11
    .line 12
    .line 13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 14
    .line 15
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 16
    .line 17
    .line 18
    throw p0

    .line 19
    :pswitch_0
    sget p0, Lpb/k;->verification_completed:I

    .line 20
    .line 21
    new-array p2, v0, [Ljava/lang/Object;

    .line 22
    .line 23
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    return-object p0

    .line 28
    :pswitch_1
    sget p0, Lpb/k;->verification_status_denied:I

    .line 29
    .line 30
    new-array p2, v0, [Ljava/lang/Object;

    .line 31
    .line 32
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    return-object p0

    .line 37
    :pswitch_2
    sget p0, Lpb/k;->verification_error:I

    .line 38
    .line 39
    new-array p2, v0, [Ljava/lang/Object;

    .line 40
    .line 41
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object p0

    .line 45
    return-object p0

    .line 46
    :pswitch_3
    sget p0, Lpb/k;->verification_status_in_progress:I

    .line 47
    .line 48
    new-array p2, v0, [Ljava/lang/Object;

    .line 49
    .line 50
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    return-object p0

    .line 55
    :pswitch_4
    sget p0, Lpb/k;->verification_status_accepted:I

    .line 56
    .line 57
    new-array p2, v0, [Ljava/lang/Object;

    .line 58
    .line 59
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object p0

    .line 63
    return-object p0

    .line 64
    :pswitch_5
    if-eqz p2, :cond_0

    .line 65
    .line 66
    sget p0, Lpb/k;->verification_status_required:I

    .line 67
    .line 68
    new-array p2, v0, [Ljava/lang/Object;

    .line 69
    .line 70
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object p0

    .line 74
    return-object p0

    .line 75
    :cond_0
    sget p0, Lpb/k;->verification_need_update_data:I

    .line 76
    .line 77
    new-array p2, v0, [Ljava/lang/Object;

    .line 78
    .line 79
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object p0

    .line 83
    return-object p0

    .line 84
    :pswitch_6
    sget p0, Lpb/k;->verification_temp_denied:I

    .line 85
    .line 86
    new-array p2, v0, [Ljava/lang/Object;

    .line 87
    .line 88
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    return-object p0

    .line 93
    :pswitch_7
    if-eqz p2, :cond_1

    .line 94
    .line 95
    sget p0, Lpb/k;->verification_status_required:I

    .line 96
    .line 97
    new-array p2, v0, [Ljava/lang/Object;

    .line 98
    .line 99
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object p0

    .line 103
    return-object p0

    .line 104
    :cond_1
    sget p0, Lpb/k;->verification_need_to_pass:I

    .line 105
    .line 106
    new-array p2, v0, [Ljava/lang/Object;

    .line 107
    .line 108
    invoke-interface {p1, p0, p2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object p0

    .line 112
    return-object p0

    .line 113
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final b(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    packed-switch p0, :pswitch_data_0

    .line 11
    .line 12
    .line 13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 14
    .line 15
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 16
    .line 17
    .line 18
    throw p0

    .line 19
    :pswitch_0
    sget p0, Lpb/k;->refill_account:I

    .line 20
    .line 21
    new-array v0, v0, [Ljava/lang/Object;

    .line 22
    .line 23
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    return-object p0

    .line 28
    :pswitch_1
    const-string p0, ""

    .line 29
    .line 30
    return-object p0

    .line 31
    :pswitch_2
    sget p0, Lpb/k;->verification_other_methods:I

    .line 32
    .line 33
    new-array v0, v0, [Ljava/lang/Object;

    .line 34
    .line 35
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    return-object p0

    .line 40
    :pswitch_3
    sget p0, Lpb/k;->verification:I

    .line 41
    .line 42
    new-array v0, v0, [Ljava/lang/Object;

    .line 43
    .line 44
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0

    .line 49
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_3
        :pswitch_3
        :pswitch_2
        :pswitch_3
        :pswitch_1
        :pswitch_1
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final c(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x5

    .line 10
    if-eq p0, v0, :cond_0

    .line 11
    .line 12
    const/4 v0, 0x6

    .line 13
    if-eq p0, v0, :cond_0

    .line 14
    .line 15
    const/16 v0, 0x8

    .line 16
    .line 17
    if-eq p0, v0, :cond_0

    .line 18
    .line 19
    const/4 p0, 0x1

    .line 20
    return p0

    .line 21
    :cond_0
    const/4 p0, 0x0

    .line 22
    return p0
.end method

.method public static final d(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)I
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    packed-switch p0, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p0

    .line 18
    :pswitch_0
    sget p0, Lo51/a;->ic_complete_verification_status:I

    .line 19
    .line 20
    return p0

    .line 21
    :pswitch_1
    sget p0, Lo51/a;->ic_denied_verification_status:I

    .line 22
    .line 23
    return p0

    .line 24
    :pswitch_2
    sget p0, Lo51/a;->ic_in_progress_verification_status:I

    .line 25
    .line 26
    return p0

    .line 27
    :pswitch_3
    sget p0, Lo51/a;->ic_accepted_verification_status:I

    .line 28
    .line 29
    return p0

    .line 30
    :pswitch_4
    sget p0, Lo51/a;->ic_required_verification_status:I

    .line 31
    .line 32
    return p0

    .line 33
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static final e(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;)Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x0

    .line 10
    packed-switch p0, :pswitch_data_0

    .line 11
    .line 12
    .line 13
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 14
    .line 15
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 16
    .line 17
    .line 18
    throw p0

    .line 19
    :pswitch_0
    sget p0, Lpb/k;->verification_title_success:I

    .line 20
    .line 21
    new-array v0, v0, [Ljava/lang/Object;

    .line 22
    .line 23
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    return-object p0

    .line 28
    :pswitch_1
    sget p0, Lpb/k;->payment_reject:I

    .line 29
    .line 30
    new-array v0, v0, [Ljava/lang/Object;

    .line 31
    .line 32
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p0

    .line 36
    return-object p0

    .line 37
    :pswitch_2
    const-string p0, ""

    .line 38
    .line 39
    return-object p0

    .line 40
    :pswitch_3
    sget p0, Lpb/k;->verigram_temporary_block_title:I

    .line 41
    .line 42
    new-array v0, v0, [Ljava/lang/Object;

    .line 43
    .line 44
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    return-object p0

    .line 49
    :pswitch_4
    sget p0, Lpb/k;->verification:I

    .line 50
    .line 51
    new-array v0, v0, [Ljava/lang/Object;

    .line 52
    .line 53
    invoke-interface {p1, p0, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    return-object p0

    .line 58
    nop

    .line 59
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_4
        :pswitch_4
        :pswitch_3
        :pswitch_4
        :pswitch_2
        :pswitch_2
        :pswitch_1
        :pswitch_2
        :pswitch_0
    .end packed-switch
.end method

.method public static final f(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z
    .locals 1

    .line 1
    sget-object v0, Lt51/a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x5

    .line 10
    if-eq p0, v0, :cond_0

    .line 11
    .line 12
    const/4 v0, 0x6

    .line 13
    if-eq p0, v0, :cond_0

    .line 14
    .line 15
    const/16 v0, 0x8

    .line 16
    .line 17
    if-eq p0, v0, :cond_0

    .line 18
    .line 19
    const/4 p0, 0x1

    .line 20
    return p0

    .line 21
    :cond_0
    const/4 p0, 0x0

    .line 22
    return p0
.end method

.method public static final g(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z
    .locals 1

    .line 1
    sget-object v0, Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;->VERIFICATION_DENIED:Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;

    .line 2
    .line 3
    if-eq p0, v0, :cond_0

    .line 4
    .line 5
    const/4 p0, 0x1

    .line 6
    return p0

    .line 7
    :cond_0
    const/4 p0, 0x0

    .line 8
    return p0
.end method

.method public static final h(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;Z)Lu51/a;
    .locals 8
    .param p0    # Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lu51/a;

    .line 2
    .line 3
    invoke-static {p0}, Lt51/a;->d(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-static {p0}, Lt51/a;->f(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-static {p0, p1}, Lt51/a;->e(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-static {p0, p1, p2}, Lt51/a;->a(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;Z)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    invoke-static {p0}, Lt51/a;->g(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z

    .line 20
    .line 21
    .line 22
    move-result v5

    .line 23
    invoke-static {p0}, Lt51/a;->c(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;)Z

    .line 24
    .line 25
    .line 26
    move-result v6

    .line 27
    invoke-static {p0, p1}, Lt51/a;->b(Lcom/xbet/onexuser/domain/models/VerificationStatusEnum;LHX0/e;)Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v7

    .line 31
    invoke-direct/range {v0 .. v7}, Lu51/a;-><init>(IZLjava/lang/String;Ljava/lang/String;ZZLjava/lang/String;)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method
