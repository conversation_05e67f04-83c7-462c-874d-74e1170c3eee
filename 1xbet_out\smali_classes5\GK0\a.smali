.class public final LGK0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a\u0013\u0010\u0006\u001a\u00020\u0005*\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u001a\u0013\u0010\n\u001a\u00020\t*\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LHK0/c;",
        "LKK0/c;",
        "c",
        "(LHK0/c;)LKK0/c;",
        "LHK0/b;",
        "LKK0/b;",
        "a",
        "(LHK0/b;)LKK0/b;",
        "LHK0/a;",
        "LKK0/a;",
        "b",
        "(LHK0/a;)LKK0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LHK0/b;)LKK0/b;
    .locals 20

    .line 1
    new-instance v0, LKK0/b;

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, LHK0/b;->h()LHK0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_9

    .line 8
    .line 9
    invoke-static {v1}, LGK0/a;->b(LHK0/a;)LKK0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    if-eqz v1, :cond_9

    .line 14
    .line 15
    invoke-virtual/range {p0 .. p0}, LHK0/b;->c()Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    const/4 v3, 0x0

    .line 20
    if-eqz v2, :cond_0

    .line 21
    .line 22
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    goto :goto_0

    .line 27
    :cond_0
    const/4 v2, 0x0

    .line 28
    :goto_0
    invoke-virtual/range {p0 .. p0}, LHK0/b;->a()Ljava/lang/Double;

    .line 29
    .line 30
    .line 31
    move-result-object v4

    .line 32
    const-wide/16 v5, 0x0

    .line 33
    .line 34
    if-eqz v4, :cond_1

    .line 35
    .line 36
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 37
    .line 38
    .line 39
    move-result-wide v7

    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-wide v7, v5

    .line 42
    :goto_1
    invoke-virtual/range {p0 .. p0}, LHK0/b;->b()Ljava/lang/Double;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    if-eqz v4, :cond_2

    .line 47
    .line 48
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 49
    .line 50
    .line 51
    move-result-wide v9

    .line 52
    goto :goto_2

    .line 53
    :cond_2
    move-wide v9, v5

    .line 54
    :goto_2
    invoke-virtual/range {p0 .. p0}, LHK0/b;->e()Ljava/lang/Double;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    if-eqz v4, :cond_3

    .line 59
    .line 60
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 61
    .line 62
    .line 63
    move-result-wide v11

    .line 64
    goto :goto_3

    .line 65
    :cond_3
    move-wide v11, v5

    .line 66
    :goto_3
    invoke-virtual/range {p0 .. p0}, LHK0/b;->j()Ljava/lang/Double;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    if-eqz v4, :cond_4

    .line 71
    .line 72
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 73
    .line 74
    .line 75
    move-result-wide v13

    .line 76
    goto :goto_4

    .line 77
    :cond_4
    move-wide v13, v5

    .line 78
    :goto_4
    invoke-virtual/range {p0 .. p0}, LHK0/b;->g()Ljava/lang/Double;

    .line 79
    .line 80
    .line 81
    move-result-object v4

    .line 82
    if-eqz v4, :cond_5

    .line 83
    .line 84
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 85
    .line 86
    .line 87
    move-result-wide v5

    .line 88
    :cond_5
    invoke-virtual/range {p0 .. p0}, LHK0/b;->i()Ljava/lang/Integer;

    .line 89
    .line 90
    .line 91
    move-result-object v4

    .line 92
    if-eqz v4, :cond_6

    .line 93
    .line 94
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 95
    .line 96
    .line 97
    move-result v4

    .line 98
    goto :goto_5

    .line 99
    :cond_6
    const/4 v4, 0x0

    .line 100
    :goto_5
    invoke-virtual/range {p0 .. p0}, LHK0/b;->f()Ljava/lang/Integer;

    .line 101
    .line 102
    .line 103
    move-result-object v15

    .line 104
    if-eqz v15, :cond_7

    .line 105
    .line 106
    invoke-virtual {v15}, Ljava/lang/Integer;->intValue()I

    .line 107
    .line 108
    .line 109
    move-result v15

    .line 110
    goto :goto_6

    .line 111
    :cond_7
    const/4 v15, 0x0

    .line 112
    :goto_6
    invoke-virtual/range {p0 .. p0}, LHK0/b;->d()Ljava/lang/Integer;

    .line 113
    .line 114
    .line 115
    move-result-object v16

    .line 116
    if-eqz v16, :cond_8

    .line 117
    .line 118
    invoke-virtual/range {v16 .. v16}, Ljava/lang/Integer;->intValue()I

    .line 119
    .line 120
    .line 121
    move-result v3

    .line 122
    move/from16 v17, v15

    .line 123
    .line 124
    move v15, v3

    .line 125
    move-wide/from16 v18, v13

    .line 126
    .line 127
    move v13, v4

    .line 128
    move-wide v3, v7

    .line 129
    move-wide v7, v11

    .line 130
    move/from16 v14, v17

    .line 131
    .line 132
    move-wide v11, v5

    .line 133
    move-wide v5, v9

    .line 134
    move-wide/from16 v9, v18

    .line 135
    .line 136
    goto :goto_7

    .line 137
    :cond_8
    move-wide/from16 v17, v13

    .line 138
    .line 139
    move v13, v4

    .line 140
    move-wide v3, v7

    .line 141
    move-wide v7, v11

    .line 142
    move-wide v11, v5

    .line 143
    move-wide v5, v9

    .line 144
    move-wide/from16 v9, v17

    .line 145
    .line 146
    move v14, v15

    .line 147
    const/4 v15, 0x0

    .line 148
    :goto_7
    invoke-direct/range {v0 .. v15}, LKK0/b;-><init>(LKK0/a;IDDDDDIII)V

    .line 149
    .line 150
    .line 151
    return-object v0

    .line 152
    :cond_9
    new-instance v0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 153
    .line 154
    const/4 v1, 0x1

    .line 155
    const/4 v2, 0x0

    .line 156
    invoke-direct {v0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 157
    .line 158
    .line 159
    throw v0
.end method

.method public static final b(LHK0/a;)LKK0/a;
    .locals 2

    .line 1
    new-instance v0, LKK0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LHK0/a;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_1

    .line 8
    .line 9
    invoke-virtual {p0}, LHK0/a;->b()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    if-nez p0, :cond_0

    .line 14
    .line 15
    const-string p0, ""

    .line 16
    .line 17
    :cond_0
    invoke-direct {v0, v1, p0}, LKK0/a;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    return-object v0

    .line 21
    :cond_1
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 22
    .line 23
    const/4 v0, 0x1

    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-direct {p0, v1, v0, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    throw p0
.end method

.method public static final c(LHK0/c;)LKK0/c;
    .locals 5
    .param p0    # LHK0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LHK0/c;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v1, 0xa

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    new-instance v3, Ljava/util/ArrayList;

    .line 11
    .line 12
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 13
    .line 14
    .line 15
    move-result v4

    .line 16
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    if-eqz v4, :cond_1

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v4

    .line 33
    check-cast v4, LCN0/i;

    .line 34
    .line 35
    invoke-static {v4}, LBN0/h;->b(LCN0/i;)LND0/h;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    move-object v3, v2

    .line 44
    :cond_1
    if-nez v3, :cond_2

    .line 45
    .line 46
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    :cond_2
    invoke-virtual {p0}, LHK0/c;->c()Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    const/4 v4, 0x1

    .line 55
    if-eqz v0, :cond_5

    .line 56
    .line 57
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    invoke-virtual {p0}, LHK0/c;->b()Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object p0

    .line 65
    if-eqz p0, :cond_4

    .line 66
    .line 67
    new-instance v2, Ljava/util/ArrayList;

    .line 68
    .line 69
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    invoke-direct {v2, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 74
    .line 75
    .line 76
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    :goto_1
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 81
    .line 82
    .line 83
    move-result v1

    .line 84
    if-eqz v1, :cond_3

    .line 85
    .line 86
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    check-cast v1, LHK0/b;

    .line 91
    .line 92
    invoke-static {v1}, LGK0/a;->a(LHK0/b;)LKK0/b;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-interface {v2, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 97
    .line 98
    .line 99
    goto :goto_1

    .line 100
    :cond_3
    new-instance p0, LKK0/c;

    .line 101
    .line 102
    invoke-direct {p0, v3, v0, v2}, LKK0/c;-><init>(Ljava/util/List;ILjava/util/List;)V

    .line 103
    .line 104
    .line 105
    return-object p0

    .line 106
    :cond_4
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 107
    .line 108
    invoke-direct {p0, v2, v4, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 109
    .line 110
    .line 111
    throw p0

    .line 112
    :cond_5
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 113
    .line 114
    invoke-direct {p0, v2, v4, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 115
    .line 116
    .line 117
    throw p0
.end method
