.class public final LN1/W$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN1/W;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:Z

.field public final j:[B


# direct methods
.method public constructor <init>(IIIIIIIIZ[B)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, LN1/W$c;->a:I

    .line 5
    .line 6
    iput p2, p0, LN1/W$c;->b:I

    .line 7
    .line 8
    iput p3, p0, LN1/W$c;->c:I

    .line 9
    .line 10
    iput p4, p0, LN1/W$c;->d:I

    .line 11
    .line 12
    iput p5, p0, LN1/W$c;->e:I

    .line 13
    .line 14
    iput p6, p0, LN1/W$c;->f:I

    .line 15
    .line 16
    iput p7, p0, LN1/W$c;->g:I

    .line 17
    .line 18
    iput p8, p0, LN1/W$c;->h:I

    .line 19
    .line 20
    iput-boolean p9, p0, LN1/W$c;->i:Z

    .line 21
    .line 22
    iput-object p10, p0, LN1/W$c;->j:[B

    .line 23
    .line 24
    return-void
.end method
