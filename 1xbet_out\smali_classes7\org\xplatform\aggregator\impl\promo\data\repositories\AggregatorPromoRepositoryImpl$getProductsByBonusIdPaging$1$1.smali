.class final Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.promo.data.repositories.AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1"
    f = "AggregatorPromoRepositoryImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->l(IILjava/lang/String;Z)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lua1/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lua1/b;",
        "item",
        "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
        "<anonymous>",
        "(Lua1/b;)Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $endPoint:Ljava/lang/String;

.field final synthetic $nightMode:Z

.field synthetic L$0:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Ljava/lang/String;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$endPoint:Ljava/lang/String;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$nightMode:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$endPoint:Ljava/lang/String;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$nightMode:Z

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;-><init>(Ljava/lang/String;ZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lua1/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->invoke(Lua1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lua1/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lua1/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lua1/b;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$endPoint:Ljava/lang/String;

    .line 16
    .line 17
    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$1$1;->$nightMode:Z

    .line 18
    .line 19
    invoke-static {p1, v0, v1}, LJa1/a;->a(Lua1/b;Ljava/lang/String;Z)Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1

    .line 24
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 25
    .line 26
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 27
    .line 28
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    throw p1
.end method
