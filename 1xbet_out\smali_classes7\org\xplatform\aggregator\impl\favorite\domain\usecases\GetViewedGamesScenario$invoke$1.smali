.class final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.domain.usecases.GetViewedGamesScenario$invoke$1"
    f = "GetViewedGamesScenario.kt"
    l = {
        0x14,
        0x15
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->b(Z)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;>;",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u0004*\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "games",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;Ljava/util/List;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Ljava/util/List;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast v1, Ljava/util/List;

    .line 30
    .line 31
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v3, Lkotlinx/coroutines/flow/f;

    .line 34
    .line 35
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 45
    .line 46
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v1, Ljava/util/List;

    .line 49
    .line 50
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 51
    .line 52
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 53
    .line 54
    iput v3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->label:I

    .line 55
    .line 56
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    if-ne v3, v0, :cond_3

    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_3
    move-object v3, p1

    .line 64
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    .line 65
    .line 66
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;->a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/f;->a()Lkotlinx/coroutines/flow/e;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1$invokeSuspend$$inlined$map$1;

    .line 75
    .line 76
    invoke-direct {v4, p1, v1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1$invokeSuspend$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Ljava/util/List;)V

    .line 77
    .line 78
    .line 79
    const/4 p1, 0x0

    .line 80
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 81
    .line 82
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 83
    .line 84
    iput v2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario$invoke$1;->label:I

    .line 85
    .line 86
    invoke-static {v3, v4, p0}, Lkotlinx/coroutines/flow/g;->H(Lkotlinx/coroutines/flow/f;Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-ne p1, v0, :cond_4

    .line 91
    .line 92
    :goto_1
    return-object v0

    .line 93
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 94
    .line 95
    return-object p1
.end method
