.class public final LV01/g;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0007\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001aK\u0010\n\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u00032\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00032\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u00032\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00032\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001aC\u0010\u000c\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\u000b\u001aC\u0010\r\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000b\u001a\u0017\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u000eH\u0007\u00a2\u0006\u0004\u0008\u000f\u0010\u0010\u001a\u001b\u0010\u0014\u001a\u00020\u0013*\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "Landroidx/compose/ui/l;",
        "Landroidx/compose/ui/graphics/v0;",
        "color",
        "Lt0/i;",
        "cornersRadius",
        "shadowBlurRadius",
        "offsetY",
        "offsetX",
        "",
        "spread",
        "c",
        "(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;",
        "g",
        "e",
        "",
        "i",
        "(ILandroidx/compose/runtime/j;I)J",
        "Landroid/content/Context;",
        "attr",
        "Landroid/util/TypedValue;",
        "j",
        "(Landroid/content/Context;I)Landroid/util/TypedValue;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(JFFFFFLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, LV01/g;->f(JFFFFFLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(FFFFFJLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p7}, LV01/g;->h(FFFFFJLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;
    .locals 2
    .param p0    # Landroidx/compose/ui/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 2
    .line 3
    const/16 v1, 0x1d

    .line 4
    .line 5
    if-lt v0, v1, :cond_0

    .line 6
    .line 7
    invoke-static/range {p0 .. p7}, LV01/g;->e(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0

    .line 12
    :cond_0
    invoke-static/range {p0 .. p7}, LV01/g;->g(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0
.end method

.method public static synthetic d(Landroidx/compose/ui/l;JFFFFFILjava/lang/Object;)Landroidx/compose/ui/l;
    .locals 10

    .line 1
    and-int/lit8 v0, p8, 0x2

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    int-to-float p3, v1

    .line 7
    invoke-static {p3}, Lt0/i;->k(F)F

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    :cond_0
    move v5, p3

    .line 12
    and-int/lit8 p3, p8, 0x4

    .line 13
    .line 14
    if-eqz p3, :cond_1

    .line 15
    .line 16
    int-to-float p3, v1

    .line 17
    invoke-static {p3}, Lt0/i;->k(F)F

    .line 18
    .line 19
    .line 20
    move-result p4

    .line 21
    :cond_1
    move v6, p4

    .line 22
    and-int/lit8 p3, p8, 0x8

    .line 23
    .line 24
    if-eqz p3, :cond_2

    .line 25
    .line 26
    int-to-float p3, v1

    .line 27
    invoke-static {p3}, Lt0/i;->k(F)F

    .line 28
    .line 29
    .line 30
    move-result p5

    .line 31
    :cond_2
    move v7, p5

    .line 32
    and-int/lit8 p3, p8, 0x10

    .line 33
    .line 34
    if-eqz p3, :cond_3

    .line 35
    .line 36
    int-to-float p3, v1

    .line 37
    invoke-static {p3}, Lt0/i;->k(F)F

    .line 38
    .line 39
    .line 40
    move-result p3

    .line 41
    move v8, p3

    .line 42
    goto :goto_0

    .line 43
    :cond_3
    move/from16 v8, p6

    .line 44
    .line 45
    :goto_0
    and-int/lit8 p3, p8, 0x20

    .line 46
    .line 47
    if-eqz p3, :cond_4

    .line 48
    .line 49
    const/4 p3, 0x0

    .line 50
    const/4 v9, 0x0

    .line 51
    :goto_1
    move-object v2, p0

    .line 52
    move-wide v3, p1

    .line 53
    goto :goto_2

    .line 54
    :cond_4
    move/from16 v9, p7

    .line 55
    .line 56
    goto :goto_1

    .line 57
    :goto_2
    invoke-static/range {v2 .. v9}, LV01/g;->c(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;

    .line 58
    .line 59
    .line 60
    move-result-object p0

    .line 61
    return-object p0
.end method

.method public static final e(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;
    .locals 8

    .line 1
    new-instance v0, LV01/e;

    .line 2
    .line 3
    move-wide v1, p1

    .line 4
    move v7, p3

    .line 5
    move v4, p4

    .line 6
    move v6, p5

    .line 7
    move v5, p6

    .line 8
    move v3, p7

    .line 9
    invoke-direct/range {v0 .. v7}, LV01/e;-><init>(JFFFFF)V

    .line 10
    .line 11
    .line 12
    invoke-static {p0, v0}, Landroidx/compose/ui/draw/h;->b(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/l;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0
.end method

.method public static final f(JFFFFFLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 8

    .line 1
    const/16 v6, 0xe

    .line 2
    .line 3
    const/4 v7, 0x0

    .line 4
    const/4 v3, 0x0

    .line 5
    const/4 v4, 0x0

    .line 6
    const/4 v5, 0x0

    .line 7
    move-wide v0, p0

    .line 8
    move v2, p2

    .line 9
    invoke-static/range {v0 .. v7}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 10
    .line 11
    .line 12
    move-result-wide p0

    .line 13
    invoke-static {p0, p1}, Landroidx/compose/ui/graphics/x0;->j(J)I

    .line 14
    .line 15
    .line 16
    move-result p0

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static/range {v0 .. v7}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 19
    .line 20
    .line 21
    move-result-wide p1

    .line 22
    invoke-static {p1, p2}, Landroidx/compose/ui/graphics/x0;->j(J)I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-interface {p7}, Landroidx/compose/ui/graphics/drawscope/f;->M0()Landroidx/compose/ui/graphics/drawscope/d;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    invoke-interface {p2}, Landroidx/compose/ui/graphics/drawscope/d;->c()Landroidx/compose/ui/graphics/n0;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-static {}, Landroidx/compose/ui/graphics/S;->a()Landroidx/compose/ui/graphics/w1;

    .line 35
    .line 36
    .line 37
    move-result-object v7

    .line 38
    invoke-interface {v7}, Landroidx/compose/ui/graphics/w1;->v()Landroid/graphics/Paint;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 43
    .line 44
    .line 45
    invoke-interface {p7, p3}, Lt0/e;->J1(F)F

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    invoke-interface {p7, p4}, Lt0/e;->J1(F)F

    .line 50
    .line 51
    .line 52
    move-result p3

    .line 53
    invoke-interface {p7, p5}, Lt0/e;->J1(F)F

    .line 54
    .line 55
    .line 56
    move-result p4

    .line 57
    invoke-virtual {p2, p1, p3, p4, p0}, Landroid/graphics/Paint;->setShadowLayer(FFFI)V

    .line 58
    .line 59
    .line 60
    invoke-interface {p7}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 61
    .line 62
    .line 63
    move-result-wide p0

    .line 64
    const/16 p2, 0x20

    .line 65
    .line 66
    shr-long/2addr p0, p2

    .line 67
    long-to-int p1, p0

    .line 68
    invoke-static {p1}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 69
    .line 70
    .line 71
    move-result v3

    .line 72
    invoke-interface {p7}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 73
    .line 74
    .line 75
    move-result-wide p0

    .line 76
    const-wide p2, 0xffffffffL

    .line 77
    .line 78
    .line 79
    .line 80
    .line 81
    and-long/2addr p0, p2

    .line 82
    long-to-int p1, p0

    .line 83
    invoke-static {p1}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 84
    .line 85
    .line 86
    move-result v4

    .line 87
    invoke-interface {p7, p6}, Lt0/e;->J1(F)F

    .line 88
    .line 89
    .line 90
    move-result v5

    .line 91
    invoke-interface {p7, p6}, Lt0/e;->J1(F)F

    .line 92
    .line 93
    .line 94
    move-result v6

    .line 95
    const/4 v1, 0x0

    .line 96
    invoke-interface/range {v0 .. v7}, Landroidx/compose/ui/graphics/n0;->w(FFFFFFLandroidx/compose/ui/graphics/w1;)V

    .line 97
    .line 98
    .line 99
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 100
    .line 101
    return-object p0
.end method

.method public static final g(Landroidx/compose/ui/l;JFFFFF)Landroidx/compose/ui/l;
    .locals 8

    .line 1
    new-instance v0, LV01/f;

    .line 2
    .line 3
    move-wide v6, p1

    .line 4
    move v4, p3

    .line 5
    move v5, p4

    .line 6
    move v3, p5

    .line 7
    move v2, p6

    .line 8
    move v1, p7

    .line 9
    invoke-direct/range {v0 .. v7}, LV01/f;-><init>(FFFFFJ)V

    .line 10
    .line 11
    .line 12
    invoke-static {p0, v0}, Landroidx/compose/ui/draw/h;->b(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/l;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0
.end method

.method public static final h(FFFFFJLandroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-interface {p7}, Landroidx/compose/ui/graphics/drawscope/f;->M0()Landroidx/compose/ui/graphics/drawscope/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Landroidx/compose/ui/graphics/drawscope/d;->c()Landroidx/compose/ui/graphics/n0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    move-object v1, p7

    .line 10
    invoke-static {}, Landroidx/compose/ui/graphics/S;->a()Landroidx/compose/ui/graphics/w1;

    .line 11
    .line 12
    .line 13
    move-result-object p7

    .line 14
    invoke-interface {p7}, Landroidx/compose/ui/graphics/w1;->v()Landroid/graphics/Paint;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    invoke-static {p0}, Lt0/i;->k(F)F

    .line 19
    .line 20
    .line 21
    move-result p0

    .line 22
    invoke-interface {v1, p0}, Lt0/e;->J1(F)F

    .line 23
    .line 24
    .line 25
    move-result p0

    .line 26
    const/4 v3, 0x0

    .line 27
    sub-float/2addr v3, p0

    .line 28
    invoke-interface {v1, p1}, Lt0/e;->J1(F)F

    .line 29
    .line 30
    .line 31
    move-result p1

    .line 32
    add-float/2addr p1, v3

    .line 33
    invoke-interface {v1, p2}, Lt0/e;->J1(F)F

    .line 34
    .line 35
    .line 36
    move-result p2

    .line 37
    add-float/2addr p2, v3

    .line 38
    invoke-interface {v1}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 39
    .line 40
    .line 41
    move-result-wide v3

    .line 42
    const/16 v5, 0x20

    .line 43
    .line 44
    shr-long/2addr v3, v5

    .line 45
    long-to-int v4, v3

    .line 46
    invoke-static {v4}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    add-float/2addr v3, p0

    .line 51
    invoke-interface {v1}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 52
    .line 53
    .line 54
    move-result-wide v4

    .line 55
    const-wide v6, 0xffffffffL

    .line 56
    .line 57
    .line 58
    .line 59
    .line 60
    and-long/2addr v4, v6

    .line 61
    long-to-int v5, v4

    .line 62
    invoke-static {v5}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 63
    .line 64
    .line 65
    move-result v4

    .line 66
    add-float/2addr v4, p0

    .line 67
    const/4 p0, 0x0

    .line 68
    int-to-float p0, p0

    .line 69
    invoke-static {p0}, Lt0/i;->k(F)F

    .line 70
    .line 71
    .line 72
    move-result p0

    .line 73
    invoke-static {p3, p0}, Lt0/i;->m(FF)Z

    .line 74
    .line 75
    .line 76
    move-result p0

    .line 77
    if-nez p0, :cond_0

    .line 78
    .line 79
    new-instance p0, Landroid/graphics/BlurMaskFilter;

    .line 80
    .line 81
    invoke-interface {v1, p4}, Lt0/e;->J1(F)F

    .line 82
    .line 83
    .line 84
    move-result p4

    .line 85
    sget-object v5, Landroid/graphics/BlurMaskFilter$Blur;->NORMAL:Landroid/graphics/BlurMaskFilter$Blur;

    .line 86
    .line 87
    invoke-direct {p0, p4, v5}, Landroid/graphics/BlurMaskFilter;-><init>(FLandroid/graphics/BlurMaskFilter$Blur;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v2, p0}, Landroid/graphics/Paint;->setMaskFilter(Landroid/graphics/MaskFilter;)Landroid/graphics/MaskFilter;

    .line 91
    .line 92
    .line 93
    :cond_0
    invoke-static {p5, p6}, Landroidx/compose/ui/graphics/x0;->j(J)I

    .line 94
    .line 95
    .line 96
    move-result p0

    .line 97
    invoke-virtual {v2, p0}, Landroid/graphics/Paint;->setColor(I)V

    .line 98
    .line 99
    .line 100
    invoke-interface {v1, p3}, Lt0/e;->J1(F)F

    .line 101
    .line 102
    .line 103
    move-result p5

    .line 104
    invoke-interface {v1, p3}, Lt0/e;->J1(F)F

    .line 105
    .line 106
    .line 107
    move-result p6

    .line 108
    move-object p0, v0

    .line 109
    move p3, v3

    .line 110
    move p4, v4

    .line 111
    invoke-interface/range {p0 .. p7}, Landroidx/compose/ui/graphics/n0;->w(FFFFFFLandroidx/compose/ui/graphics/w1;)V

    .line 112
    .line 113
    .line 114
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 115
    .line 116
    return-object p0
.end method

.method public static final i(ILandroidx/compose/runtime/j;I)J
    .locals 3

    .line 1
    const v0, -0x4dce063d

    .line 2
    .line 3
    .line 4
    invoke-interface {p1, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 5
    .line 6
    .line 7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    const/4 v1, -0x1

    .line 14
    const-string v2, "org.xbet.uikit.compose.color.getColor (ColorUtils.kt:133)"

    .line 15
    .line 16
    invoke-static {v0, p2, v1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    invoke-static {}, Landroidx/compose/ui/platform/AndroidCompositionLocals_androidKt;->g()Landroidx/compose/runtime/x0;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-interface {p1, p2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    check-cast p2, Landroid/content/Context;

    .line 28
    .line 29
    invoke-static {p2, p0}, LV01/g;->j(Landroid/content/Context;I)Landroid/util/TypedValue;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    iget p0, p0, Landroid/util/TypedValue;->resourceId:I

    .line 34
    .line 35
    const/4 p2, 0x0

    .line 36
    invoke-static {p0, p1, p2}, Lm0/a;->a(ILandroidx/compose/runtime/j;I)J

    .line 37
    .line 38
    .line 39
    move-result-wide v0

    .line 40
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 41
    .line 42
    .line 43
    move-result p0

    .line 44
    if-eqz p0, :cond_1

    .line 45
    .line 46
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 47
    .line 48
    .line 49
    :cond_1
    invoke-interface {p1}, Landroidx/compose/runtime/j;->q()V

    .line 50
    .line 51
    .line 52
    return-wide v0
.end method

.method public static final j(Landroid/content/Context;I)Landroid/util/TypedValue;
    .locals 2

    .line 1
    new-instance v0, Landroid/util/TypedValue;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/util/TypedValue;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/content/Context;->getTheme()Landroid/content/res/Resources$Theme;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    const/4 v1, 0x1

    .line 11
    invoke-virtual {p0, p1, v0, v1}, Landroid/content/res/Resources$Theme;->resolveAttribute(ILandroid/util/TypedValue;Z)Z

    .line 12
    .line 13
    .line 14
    return-object v0
.end method
