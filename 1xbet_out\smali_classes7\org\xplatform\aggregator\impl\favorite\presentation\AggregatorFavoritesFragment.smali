.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"

# interfaces
.implements LZ91/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        ">;",
        "LZ91/a;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 C2\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u00020\u0003:\u0001DB\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u000f\u0010\u0007\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0007\u0010\u0005J\u000f\u0010\t\u001a\u00020\u0008H\u0010\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u0005J\u0019\u0010\u0011\u001a\u00020\u00062\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0005J\u0017\u0010\u0016\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0005J\u000f\u0010\u0019\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0005J\u000f\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0005R\u001b\u0010 \u001a\u00020\u001b8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u001fR\"\u0010(\u001a\u00020!8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%\"\u0004\u0008&\u0010\'R+\u00101\u001a\u00020)2\u0006\u0010*\u001a\u00020)8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\"\u0004\u0008/\u00100R\u001b\u00106\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105R\u001a\u0010<\u001a\u0002078\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u00088\u00109\u001a\u0004\u0008:\u0010;R\u001a\u0010B\u001a\u00020=8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\u00a8\u0006E"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        "LZ91/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "v2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "onDestroyView",
        "",
        "lock",
        "G",
        "(Z)V",
        "w3",
        "x3",
        "t3",
        "LS91/E;",
        "o0",
        "LRc/c;",
        "q3",
        "()LS91/E;",
        "viewBinding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "b1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "s3",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/api/navigation/FavoriteType;",
        "<set-?>",
        "k1",
        "LeX0/j;",
        "p3",
        "()Lorg/xplatform/aggregator/api/navigation/FavoriteType;",
        "v3",
        "(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V",
        "favoriteType",
        "v1",
        "Lkotlin/j;",
        "r3",
        "()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;",
        "viewModel",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "x1",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "y1",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "F1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final F1:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic H1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public b1:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k1:LeX0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAggregatorFavoritesBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "favoriteType"

    .line 20
    .line 21
    const-string v5, "getFavoriteType()Lorg/xplatform/aggregator/api/navigation/FavoriteType;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->H1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->F1:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$a;

    .line 47
    .line 48
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lu91/c;->fragment_aggregator_favorites:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->o0:LRc/c;

    .line 13
    .line 14
    new-instance v0, LeX0/j;

    .line 15
    .line 16
    const-string v1, "FAVORITE_TYPE"

    .line 17
    .line 18
    invoke-direct {v0, v1}, LeX0/j;-><init>(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->k1:LeX0/j;

    .line 22
    .line 23
    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/presentation/c;

    .line 24
    .line 25
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/c;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)V

    .line 26
    .line 27
    .line 28
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$1;

    .line 29
    .line 30
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 31
    .line 32
    .line 33
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 34
    .line 35
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$2;

    .line 36
    .line 37
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 38
    .line 39
    .line 40
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    const-class v2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 45
    .line 46
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$3;

    .line 51
    .line 52
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 53
    .line 54
    .line 55
    new-instance v4, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$4;

    .line 56
    .line 57
    const/4 v5, 0x0

    .line 58
    invoke-direct {v4, v5, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 59
    .line 60
    .line 61
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->v1:Lkotlin/j;

    .line 66
    .line 67
    sget-object v0, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->FAVORITES:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 68
    .line 69
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->x1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 70
    .line 71
    sget-object v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->AggregatorFavorites:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 72
    .line 73
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->y1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 74
    .line 75
    return-void
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lcom/google/android/material/tabs/TabLayout$Tab;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->u3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lcom/google/android/material/tabs/TabLayout$Tab;I)V

    return-void
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->y3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic n3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)LS91/E;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic o3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->v3(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final u3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lcom/google/android/material/tabs/TabLayout$Tab;I)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    instance-of v1, v0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;

    .line 12
    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/a;

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v0, 0x0

    .line 19
    :goto_0
    const/4 v1, 0x0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0, p2}, LkY0/a;->I(I)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    check-cast p2, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;

    .line 27
    .line 28
    if-eqz p2, :cond_1

    .line 29
    .line 30
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;->name()I

    .line 31
    .line 32
    .line 33
    move-result p2

    .line 34
    goto :goto_1

    .line 35
    :cond_1
    const/4 p2, 0x0

    .line 36
    :goto_1
    invoke-virtual {p0, p2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p2

    .line 40
    invoke-virtual {p1, p2}, Lcom/google/android/material/tabs/TabLayout$Tab;->setText(Ljava/lang/CharSequence;)Lcom/google/android/material/tabs/TabLayout$Tab;

    .line 41
    .line 42
    .line 43
    iget-object p1, p1, Lcom/google/android/material/tabs/TabLayout$Tab;->view:Lcom/google/android/material/tabs/TabLayout$TabView;

    .line 44
    .line 45
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    sget v0, Lpb/f;->space_12:I

    .line 50
    .line 51
    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 52
    .line 53
    .line 54
    move-result p2

    .line 55
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    sget v0, Lpb/f;->space_12:I

    .line 60
    .line 61
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 62
    .line 63
    .line 64
    move-result p0

    .line 65
    invoke-virtual {p1, p2, v1, p0, v1}, Landroid/view/View;->setPadding(IIII)V

    .line 66
    .line 67
    .line 68
    return-void
.end method

.method public static final y3(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->s3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public G(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/E;->d:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 6
    .line 7
    xor-int/lit8 p1, p1, 0x1

    .line 8
    .line 9
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/E;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->y1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/E;->g:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->x1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->r3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->r3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->I4()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final p3()Lorg/xplatform/aggregator/api/navigation/FavoriteType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->k1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->H1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/j;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/io/Serializable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xplatform/aggregator/api/navigation/FavoriteType;

    .line 13
    .line 14
    return-object v0
.end method

.method public final q3()LS91/E;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->H1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/E;

    .line 13
    .line 14
    return-object v0
.end method

.method public r3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final s3()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->b1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->r3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    const-class v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;

    .line 9
    .line 10
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-virtual {v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->X4(Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->x3()V

    .line 18
    .line 19
    .line 20
    if-nez p1, :cond_0

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->w3()V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method

.method public final t3()V
    .locals 4

    .line 1
    new-instance v0, Lcom/google/android/material/tabs/TabLayoutMediator;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v1, v1, LS91/E;->h:Lorg/xbet/ui_common/viewcomponents/tabs/TabLayoutRectangle;

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    iget-object v2, v2, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 14
    .line 15
    new-instance v3, Lorg/xplatform/aggregator/impl/favorite/presentation/b;

    .line 16
    .line 17
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/b;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)V

    .line 18
    .line 19
    .line 20
    invoke-direct {v0, v1, v2, v3}, Lcom/google/android/material/tabs/TabLayoutMediator;-><init>(Lcom/google/android/material/tabs/TabLayout;Landroidx/viewpager2/widget/ViewPager2;Lcom/google/android/material/tabs/TabLayoutMediator$TabConfigurationStrategy;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0}, Lcom/google/android/material/tabs/TabLayoutMediator;->attach()V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/f;->a(Landroidx/fragment/app/Fragment;)LN91/k;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-interface {v0, p0}, LN91/k;->b(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->r3()Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesSharedViewModel;->J4()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final v3(Lorg/xplatform/aggregator/api/navigation/FavoriteType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->k1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->H1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/j;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/io/Serializable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final w3()V
    .locals 6

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;->values()[Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    array-length v1, v0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    :goto_0
    if-ge v3, v1, :cond_1

    .line 9
    .line 10
    aget-object v4, v0, v3

    .line 11
    .line 12
    invoke-virtual {v4}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->p3()Lorg/xplatform/aggregator/api/navigation/FavoriteType;

    .line 17
    .line 18
    .line 19
    move-result-object v5

    .line 20
    invoke-virtual {v5}, Ljava/lang/Enum;->name()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v5

    .line 24
    invoke-static {v4, v5}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    if-eqz v4, :cond_0

    .line 29
    .line 30
    goto :goto_1

    .line 31
    :cond_0
    add-int/lit8 v3, v3, 0x1

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_1
    const/4 v3, -0x1

    .line 35
    :goto_1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 40
    .line 41
    invoke-virtual {v0, v3, v2}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(IZ)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final x3()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/a;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    invoke-interface {v3}, Landroidx/lifecycle/w;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;->values()[Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoriteType;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-static {v4}, Lkotlin/collections/r;->K1([Ljava/lang/Object;)Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->R2()Z

    .line 30
    .line 31
    .line 32
    move-result v6

    .line 33
    move-object v5, p0

    .line 34
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/favorite/presentation/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;LZ91/a;Z)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 45
    .line 46
    const/4 v1, 0x0

    .line 47
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setUserInputEnabled(Z)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 55
    .line 56
    const/4 v1, 0x2

    .line 57
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setOffscreenPageLimit(I)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->q3()LS91/E;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    iget-object v0, v0, LS91/E;->f:Landroidx/viewpager2/widget/ViewPager2;

    .line 65
    .line 66
    new-instance v1, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$b;

    .line 67
    .line 68
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment$b;-><init>(Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/favorite/presentation/AggregatorFavoritesFragment;->t3()V

    .line 75
    .line 76
    .line 77
    return-void
.end method
