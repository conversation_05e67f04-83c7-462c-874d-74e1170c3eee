.class public final synthetic LrM0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LrM0/c;


# direct methods
.method public synthetic constructor <init>(LrM0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrM0/b;->a:LrM0/c;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LrM0/b;->a:LrM0/c;

    invoke-static {v0}, LrM0/c;->a(LrM0/c;)LqM0/a;

    move-result-object v0

    return-object v0
.end method
