.class public interface abstract LI91/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LI91/a$a;,
        LI91/a$b;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0002\u0006\u0003R\u0014\u0010\u0005\u001a\u00020\u00028&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0003\u0010\u0004\u0082\u0001\u0002\u0007\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LI91/a;",
        "",
        "Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;",
        "a",
        "()Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;",
        "style",
        "b",
        "LI91/a$a;",
        "LI91/a$b;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a()Lorg/xbet/uikit_aggregator/aggregatorCategory/AggregatorCategoryType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
