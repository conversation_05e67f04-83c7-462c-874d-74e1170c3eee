.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/v;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/v;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->m(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->j(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->k(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->l(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt;->i(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LP80/a;

    .line 2
    .line 3
    invoke-direct {v0}, LP80/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LP80/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LP80/b;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt$getAccordionIsolatedCyberEndIconLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt$getAccordionIsolatedCyberEndIconLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt$getAccordionIsolatedCyberEndIconLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/accordion/AccordionIsolatedCyberEndIconLineItemViewHolderKt$getAccordionIsolatedCyberEndIconLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/v;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/v;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/v;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/v;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/v;->b()Lorg/xbet/uikit/components/cells/MenuCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LP80/c;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LP80/c;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LP80/d;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LP80/d;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final j(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final k(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 10

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LN80/c$d;

    .line 6
    .line 7
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lv80/v;

    .line 12
    .line 13
    iget-object v0, v0, Lv80/v;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 14
    .line 15
    invoke-virtual {p1}, LN80/c$d;->getIcon()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setIconResource(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Lv80/v;

    .line 27
    .line 28
    iget-object v0, v0, Lv80/v;->d:Lorg/xbet/uikit/components/cells/left/CellLeftIcon;

    .line 29
    .line 30
    invoke-virtual {p1}, LN80/c$d;->u()Z

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/left/CellLeftIcon;->setBadgeVisible(Z)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, Lv80/v;

    .line 42
    .line 43
    iget-object v0, v0, Lv80/v;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 44
    .line 45
    invoke-virtual {p1}, LN80/c$d;->getTitle()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setTitle(Ljava/lang/CharSequence;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    check-cast v0, Lv80/v;

    .line 57
    .line 58
    iget-object v0, v0, Lv80/v;->e:Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;

    .line 59
    .line 60
    invoke-virtual {p1}, LN80/c$d;->j()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/cells/middle/CellMiddleTitle;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    check-cast p0, Lv80/v;

    .line 72
    .line 73
    iget-object p0, p0, Lv80/v;->c:Lorg/xbet/uikit/components/cells/right/CellRightBanner;

    .line 74
    .line 75
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 76
    .line 77
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    invoke-virtual {p1}, LN80/c$d;->f()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    new-instance v6, LP80/e;

    .line 86
    .line 87
    invoke-direct {v6, p0}, LP80/e;-><init>(Lorg/xbet/uikit/components/cells/right/CellRightBanner;)V

    .line 88
    .line 89
    .line 90
    new-instance v7, LP80/f;

    .line 91
    .line 92
    invoke-direct {v7, p0}, LP80/f;-><init>(Lorg/xbet/uikit/components/cells/right/CellRightBanner;)V

    .line 93
    .line 94
    .line 95
    const/16 v8, 0xe

    .line 96
    .line 97
    const/4 v9, 0x0

    .line 98
    const/4 v3, 0x0

    .line 99
    const/4 v4, 0x0

    .line 100
    const/4 v5, 0x0

    .line 101
    invoke-static/range {v0 .. v9}, LCX0/l;->M(LCX0/l;Landroid/content/Context;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;[LYW0/d;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 102
    .line 103
    .line 104
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 105
    .line 106
    return-object p0
.end method

.method public static final l(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->onRtlPropertiesChanged(I)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final m(Lorg/xbet/uikit/components/cells/right/CellRightBanner;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    const/4 p1, 0x0

    .line 2
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/right/CellRightBanner;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 3
    .line 4
    .line 5
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 6
    .line 7
    return-object p0
.end method
