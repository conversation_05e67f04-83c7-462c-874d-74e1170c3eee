.class public final synthetic LU01/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/views/LoadableImageView;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Landroid/graphics/drawable/Drawable;

.field public final synthetic d:Lkotlin/jvm/functions/Function1;

.field public final synthetic e:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU01/o;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iput-object p2, p0, LU01/o;->b:Ljava/lang/String;

    iput-object p3, p0, LU01/o;->c:Landroid/graphics/drawable/Drawable;

    iput-object p4, p0, LU01/o;->d:Lkotlin/jvm/functions/Function1;

    iput-object p5, p0, LU01/o;->e:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 5

    .line 1
    iget-object v0, p0, LU01/o;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    iget-object v1, p0, LU01/o;->b:Ljava/lang/String;

    iget-object v2, p0, LU01/o;->c:Landroid/graphics/drawable/Drawable;

    iget-object v3, p0, LU01/o;->d:Lkotlin/jvm/functions/Function1;

    iget-object v4, p0, LU01/o;->e:Lkotlin/jvm/functions/Function1;

    invoke-static {v0, v1, v2, v3, v4}, Lorg/xbet/uikit/components/views/LoadableImageView;->E(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    return-void
.end method
