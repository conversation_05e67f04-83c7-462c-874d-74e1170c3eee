.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingEndGameViewModel$onReplenishClicked$2"
    f = "TileMatchingEndGameViewModel.kt"
    l = {
        0x5a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->O3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;

    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    move-object v6, p0

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->s3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lak/a;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-interface {p1}, Lak/a;->F()Lek/d;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    sget-object v4, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 39
    .line 40
    iput v2, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->label:I

    .line 41
    .line 42
    const/4 v5, 0x0

    .line 43
    const/4 v7, 0x2

    .line 44
    const/4 v8, 0x0

    .line 45
    move-object v6, p0

    .line 46
    invoke-static/range {v3 .. v8}, Lek/d$a;->a(Lek/d;Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    if-ne p1, v0, :cond_2

    .line 51
    .line 52
    return-object v0

    .line 53
    :cond_2
    :goto_0
    iget-object v0, v6, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 54
    .line 55
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->s3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)Lak/a;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-interface {v0}, Lak/a;->m()Lek/f;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 66
    .line 67
    invoke-interface {v0, v1, p1}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 68
    .line 69
    .line 70
    iget-object v0, v6, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 71
    .line 72
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->t3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)LxX0/a;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    iget-object v1, v6, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel$onReplenishClicked$2;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 77
    .line 78
    invoke-static {v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->A3(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;)LwX0/c;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 83
    .line 84
    .line 85
    move-result-wide v3

    .line 86
    invoke-interface {v0, v1, v2, v3, v4}, LxX0/a;->b(LwX0/c;ZJ)V

    .line 87
    .line 88
    .line 89
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1
.end method
