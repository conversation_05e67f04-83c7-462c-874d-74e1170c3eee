.class public final LJb1/g$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LJb1/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LJb1/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:LwX0/C;

.field public final b:Lc81/a;

.field public final c:Lorg/xbet/ui_common/utils/M;

.field public final d:Lak/a;

.field public final e:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

.field public final f:LTZ0/a;

.field public final g:LzX0/k;

.field public final h:Lak/b;

.field public final i:LfX/b;

.field public final j:Lm8/a;

.field public final k:Lc8/h;

.field public final l:Lf8/g;

.field public final m:LS8/a;

.field public final n:LJb1/g$a;


# direct methods
.method public constructor <init>(Lc81/a;Lak/b;Lak/a;LTZ0/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LfX/b;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;Lf8/g;Lcom/xbet/onexcore/utils/ext/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LS8/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LJb1/g$a;->n:LJb1/g$a;

    .line 4
    iput-object p7, p0, LJb1/g$a;->a:LwX0/C;

    .line 5
    iput-object p1, p0, LJb1/g$a;->b:Lc81/a;

    .line 6
    iput-object p8, p0, LJb1/g$a;->c:Lorg/xbet/ui_common/utils/M;

    .line 7
    iput-object p3, p0, LJb1/g$a;->d:Lak/a;

    .line 8
    iput-object p5, p0, LJb1/g$a;->e:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 9
    iput-object p4, p0, LJb1/g$a;->f:LTZ0/a;

    move-object/from16 p1, p20

    .line 10
    iput-object p1, p0, LJb1/g$a;->g:LzX0/k;

    .line 11
    iput-object p2, p0, LJb1/g$a;->h:Lak/b;

    .line 12
    iput-object p6, p0, LJb1/g$a;->i:LfX/b;

    .line 13
    iput-object p9, p0, LJb1/g$a;->j:Lm8/a;

    .line 14
    iput-object p14, p0, LJb1/g$a;->k:Lc8/h;

    .line 15
    iput-object p10, p0, LJb1/g$a;->l:Lf8/g;

    move-object/from16 p1, p18

    .line 16
    iput-object p1, p0, LJb1/g$a;->m:LS8/a;

    return-void
.end method

.method public synthetic constructor <init>(Lc81/a;Lak/b;Lak/a;LTZ0/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LfX/b;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;Lf8/g;Lcom/xbet/onexcore/utils/ext/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LS8/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;LJb1/h;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p22}, LJb1/g$a;-><init>(Lc81/a;Lak/b;Lak/a;LTZ0/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;LfX/b;LwX0/C;Lorg/xbet/ui_common/utils/M;Lm8/a;Lf8/g;Lcom/xbet/onexcore/utils/ext/c;Lorg/xbet/remoteconfig/domain/usecases/i;Lcom/xbet/onexuser/domain/user/c;Lc8/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Li8/j;LHX0/e;LS8/a;LVg0/a;LzX0/k;Lau/a;Lp9/c;)V

    return-void
.end method


# virtual methods
.method public a()Lzb1/b;
    .locals 1

    .line 1
    new-instance v0, LJb1/d;

    .line 2
    .line 3
    invoke-direct {v0}, LJb1/d;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
