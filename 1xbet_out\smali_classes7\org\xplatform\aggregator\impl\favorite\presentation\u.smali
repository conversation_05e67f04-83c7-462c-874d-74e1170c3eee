.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->n0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xplatform/aggregator/impl/core/presentation/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->l0:Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lck/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->m0:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->o0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static e(Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/FavoriteItemFragment;->k0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
