.class public final LV01/v;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\"\u001d\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00008\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0002\u0010\u0003\u001a\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0007"
    }
    d2 = {
        "Landroidx/compose/runtime/x0;",
        "LV01/t;",
        "a",
        "Landroidx/compose/runtime/x0;",
        "c",
        "()Landroidx/compose/runtime/x0;",
        "LocalWidgetColors",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Landroidx/compose/runtime/x0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/x0<",
            "LV01/t;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LV01/u;

    .line 2
    .line 3
    invoke-direct {v0}, LV01/u;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Landroidx/compose/runtime/CompositionLocalKt;->g(Lkotlin/jvm/functions/Function0;)Landroidx/compose/runtime/x0;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, LV01/v;->a:Landroidx/compose/runtime/x0;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic a()LV01/t;
    .locals 1

    .line 1
    invoke-static {}, LV01/v;->b()LV01/t;

    move-result-object v0

    return-object v0
.end method

.method public static final b()LV01/t;
    .locals 12

    .line 1
    new-instance v0, LV01/t;

    .line 2
    .line 3
    sget-object v1, Landroidx/compose/ui/graphics/v0;->b:Landroidx/compose/ui/graphics/v0$a;

    .line 4
    .line 5
    move-object v3, v1

    .line 6
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 7
    .line 8
    .line 9
    move-result-wide v1

    .line 10
    move-object v5, v3

    .line 11
    invoke-virtual {v5}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v3

    .line 15
    move-object v7, v5

    .line 16
    invoke-virtual {v7}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 17
    .line 18
    .line 19
    move-result-wide v5

    .line 20
    move-object v9, v7

    .line 21
    invoke-virtual {v9}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 22
    .line 23
    .line 24
    move-result-wide v7

    .line 25
    invoke-virtual {v9}, Landroidx/compose/ui/graphics/v0$a;->e()J

    .line 26
    .line 27
    .line 28
    move-result-wide v9

    .line 29
    const/4 v11, 0x0

    .line 30
    invoke-direct/range {v0 .. v11}, LV01/t;-><init>(JJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    return-object v0
.end method

.method public static final c()Landroidx/compose/runtime/x0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Landroidx/compose/runtime/x0<",
            "LV01/t;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, LV01/v;->a:Landroidx/compose/runtime/x0;

    .line 2
    .line 3
    return-object v0
.end method
