.class public final Lk2/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN1/T;


# instance fields
.field public final a:LN1/T;

.field public final b:Lk2/s$a;

.field public final c:Lk2/d;

.field public final d:Lt1/G;

.field public e:I

.field public f:I

.field public g:[B

.field public h:Lk2/s;

.field public i:Landroidx/media3/common/r;

.field public j:Z


# direct methods
.method public constructor <init>(LN1/T;Lk2/s$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lk2/v;->a:LN1/T;

    .line 5
    .line 6
    iput-object p2, p0, Lk2/v;->b:Lk2/s$a;

    .line 7
    .line 8
    new-instance p1, Lk2/d;

    .line 9
    .line 10
    invoke-direct {p1}, Lk2/d;-><init>()V

    .line 11
    .line 12
    .line 13
    iput-object p1, p0, Lk2/v;->c:Lk2/d;

    .line 14
    .line 15
    const/4 p1, 0x0

    .line 16
    iput p1, p0, Lk2/v;->e:I

    .line 17
    .line 18
    iput p1, p0, Lk2/v;->f:I

    .line 19
    .line 20
    sget-object p1, Lt1/a0;->f:[B

    .line 21
    .line 22
    iput-object p1, p0, Lk2/v;->g:[B

    .line 23
    .line 24
    new-instance p1, Lt1/G;

    .line 25
    .line 26
    invoke-direct {p1}, Lt1/G;-><init>()V

    .line 27
    .line 28
    .line 29
    iput-object p1, p0, Lk2/v;->d:Lt1/G;

    .line 30
    .line 31
    return-void
.end method

.method public static synthetic h(Lk2/v;JILk2/e;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p4, p1, p2, p3}, Lk2/v;->j(Lk2/e;JI)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public synthetic a(Lt1/G;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LN1/S;->c(LN1/T;Lt1/G;I)V

    return-void
.end method

.method public synthetic b(J)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LN1/S;->a(LN1/T;J)V

    return-void
.end method

.method public c(Landroidx/media3/common/j;IZI)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lk2/v;->h:Lk2/s;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lk2/v;->a:LN1/T;

    .line 6
    .line 7
    invoke-interface {v0, p1, p2, p3, p4}, LN1/T;->c(Landroidx/media3/common/j;IZI)I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    return p1

    .line 12
    :cond_0
    invoke-virtual {p0, p2}, Lk2/v;->i(I)V

    .line 13
    .line 14
    .line 15
    iget-object p4, p0, Lk2/v;->g:[B

    .line 16
    .line 17
    iget v0, p0, Lk2/v;->f:I

    .line 18
    .line 19
    invoke-interface {p1, p4, v0, p2}, Landroidx/media3/common/j;->b([BII)I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    const/4 p2, -0x1

    .line 24
    if-ne p1, p2, :cond_2

    .line 25
    .line 26
    if-eqz p3, :cond_1

    .line 27
    .line 28
    return p2

    .line 29
    :cond_1
    new-instance p1, Ljava/io/EOFException;

    .line 30
    .line 31
    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_2
    iget p2, p0, Lk2/v;->f:I

    .line 36
    .line 37
    add-int/2addr p2, p1

    .line 38
    iput p2, p0, Lk2/v;->f:I

    .line 39
    .line 40
    return p1
.end method

.method public d(JIIILN1/T$a;)V
    .locals 8

    .line 1
    iget-object v0, p0, Lk2/v;->h:Lk2/s;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lk2/v;->a:LN1/T;

    .line 6
    .line 7
    move-wide v2, p1

    .line 8
    move v4, p3

    .line 9
    move v5, p4

    .line 10
    move v6, p5

    .line 11
    move-object v7, p6

    .line 12
    invoke-interface/range {v1 .. v7}, LN1/T;->d(JIIILN1/T$a;)V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    move-wide v2, p1

    .line 17
    move v4, p3

    .line 18
    move v6, p5

    .line 19
    move-object v7, p6

    .line 20
    const/4 v1, 0x0

    .line 21
    if-nez v7, :cond_1

    .line 22
    .line 23
    const/4 p1, 0x1

    .line 24
    goto :goto_0

    .line 25
    :cond_1
    const/4 p1, 0x0

    .line 26
    :goto_0
    const-string p2, "DRM on subtitles is not supported"

    .line 27
    .line 28
    invoke-static {p1, p2}, Lt1/a;->b(ZLjava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget p1, p0, Lk2/v;->f:I

    .line 32
    .line 33
    sub-int/2addr p1, v6

    .line 34
    sub-int p3, p1, p4

    .line 35
    .line 36
    :try_start_0
    iget-object p1, p0, Lk2/v;->h:Lk2/s;

    .line 37
    .line 38
    iget-object p2, p0, Lk2/v;->g:[B

    .line 39
    .line 40
    invoke-static {}, Lk2/s$b;->b()Lk2/s$b;

    .line 41
    .line 42
    .line 43
    move-result-object p5

    .line 44
    new-instance p6, Lk2/u;

    .line 45
    .line 46
    invoke-direct {p6, p0, v2, v3, v4}, Lk2/u;-><init>(Lk2/v;JI)V

    .line 47
    .line 48
    .line 49
    invoke-interface/range {p1 .. p6}, Lk2/s;->c([BIILk2/s$b;Lt1/l;)V
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    .line 50
    .line 51
    .line 52
    goto :goto_1

    .line 53
    :catch_0
    move-exception v0

    .line 54
    move-object p1, v0

    .line 55
    iget-boolean p2, p0, Lk2/v;->j:Z

    .line 56
    .line 57
    if-eqz p2, :cond_3

    .line 58
    .line 59
    const-string p2, "SubtitleTranscodingTO"

    .line 60
    .line 61
    const-string p5, "Parsing subtitles failed, ignoring sample."

    .line 62
    .line 63
    invoke-static {p2, p5, p1}, Lt1/r;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 64
    .line 65
    .line 66
    :goto_1
    add-int/2addr p3, p4

    .line 67
    iput p3, p0, Lk2/v;->e:I

    .line 68
    .line 69
    iget p1, p0, Lk2/v;->f:I

    .line 70
    .line 71
    if-ne p3, p1, :cond_2

    .line 72
    .line 73
    iput v1, p0, Lk2/v;->e:I

    .line 74
    .line 75
    iput v1, p0, Lk2/v;->f:I

    .line 76
    .line 77
    :cond_2
    return-void

    .line 78
    :cond_3
    throw p1
.end method

.method public e(Landroidx/media3/common/r;)V
    .locals 4

    .line 1
    iget-object v0, p1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 2
    .line 3
    invoke-static {v0}, Lt1/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget-object v0, p1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 7
    .line 8
    invoke-static {v0}, Landroidx/media3/common/y;->k(Ljava/lang/String;)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    const/4 v1, 0x3

    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    const/4 v0, 0x1

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 v0, 0x0

    .line 18
    :goto_0
    invoke-static {v0}, Lt1/a;->a(Z)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lk2/v;->i:Landroidx/media3/common/r;

    .line 22
    .line 23
    invoke-virtual {p1, v0}, Landroidx/media3/common/r;->equals(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-nez v0, :cond_2

    .line 28
    .line 29
    iput-object p1, p0, Lk2/v;->i:Landroidx/media3/common/r;

    .line 30
    .line 31
    iget-object v0, p0, Lk2/v;->b:Lk2/s$a;

    .line 32
    .line 33
    invoke-interface {v0, p1}, Lk2/s$a;->a(Landroidx/media3/common/r;)Z

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    iget-object v0, p0, Lk2/v;->b:Lk2/s$a;

    .line 40
    .line 41
    invoke-interface {v0, p1}, Lk2/s$a;->c(Landroidx/media3/common/r;)Lk2/s;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    goto :goto_1

    .line 46
    :cond_1
    const/4 v0, 0x0

    .line 47
    :goto_1
    iput-object v0, p0, Lk2/v;->h:Lk2/s;

    .line 48
    .line 49
    :cond_2
    iget-object v0, p0, Lk2/v;->h:Lk2/s;

    .line 50
    .line 51
    if-nez v0, :cond_3

    .line 52
    .line 53
    iget-object v0, p0, Lk2/v;->a:LN1/T;

    .line 54
    .line 55
    invoke-interface {v0, p1}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 56
    .line 57
    .line 58
    return-void

    .line 59
    :cond_3
    iget-object v0, p0, Lk2/v;->a:LN1/T;

    .line 60
    .line 61
    invoke-virtual {p1}, Landroidx/media3/common/r;->b()Landroidx/media3/common/r$b;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    const-string v2, "application/x-media3-cues"

    .line 66
    .line 67
    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->u0(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    iget-object v2, p1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 72
    .line 73
    invoke-virtual {v1, v2}, Landroidx/media3/common/r$b;->S(Ljava/lang/String;)Landroidx/media3/common/r$b;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    const-wide v2, 0x7fffffffffffffffL

    .line 78
    .line 79
    .line 80
    .line 81
    .line 82
    invoke-virtual {v1, v2, v3}, Landroidx/media3/common/r$b;->y0(J)Landroidx/media3/common/r$b;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    iget-object v2, p0, Lk2/v;->b:Lk2/s$a;

    .line 87
    .line 88
    invoke-interface {v2, p1}, Lk2/s$a;->b(Landroidx/media3/common/r;)I

    .line 89
    .line 90
    .line 91
    move-result p1

    .line 92
    invoke-virtual {v1, p1}, Landroidx/media3/common/r$b;->W(I)Landroidx/media3/common/r$b;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-virtual {p1}, Landroidx/media3/common/r$b;->N()Landroidx/media3/common/r;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    invoke-interface {v0, p1}, LN1/T;->e(Landroidx/media3/common/r;)V

    .line 101
    .line 102
    .line 103
    return-void
.end method

.method public f(Lt1/G;II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lk2/v;->h:Lk2/s;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lk2/v;->a:LN1/T;

    .line 6
    .line 7
    invoke-interface {v0, p1, p2, p3}, LN1/T;->f(Lt1/G;II)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    invoke-virtual {p0, p2}, Lk2/v;->i(I)V

    .line 12
    .line 13
    .line 14
    iget-object p3, p0, Lk2/v;->g:[B

    .line 15
    .line 16
    iget v0, p0, Lk2/v;->f:I

    .line 17
    .line 18
    invoke-virtual {p1, p3, v0, p2}, Lt1/G;->l([BII)V

    .line 19
    .line 20
    .line 21
    iget p1, p0, Lk2/v;->f:I

    .line 22
    .line 23
    add-int/2addr p1, p2

    .line 24
    iput p1, p0, Lk2/v;->f:I

    .line 25
    .line 26
    return-void
.end method

.method public synthetic g(Landroidx/media3/common/j;IZ)I
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LN1/S;->b(LN1/T;Landroidx/media3/common/j;IZ)I

    move-result p1

    return p1
.end method

.method public final i(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lk2/v;->g:[B

    .line 2
    .line 3
    array-length v0, v0

    .line 4
    iget v1, p0, Lk2/v;->f:I

    .line 5
    .line 6
    sub-int/2addr v0, v1

    .line 7
    if-lt v0, p1, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    iget v0, p0, Lk2/v;->e:I

    .line 11
    .line 12
    sub-int/2addr v1, v0

    .line 13
    mul-int/lit8 v0, v1, 0x2

    .line 14
    .line 15
    add-int/2addr p1, v1

    .line 16
    invoke-static {v0, p1}, Ljava/lang/Math;->max(II)I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    iget-object v0, p0, Lk2/v;->g:[B

    .line 21
    .line 22
    array-length v2, v0

    .line 23
    if-gt p1, v2, :cond_1

    .line 24
    .line 25
    move-object p1, v0

    .line 26
    goto :goto_0

    .line 27
    :cond_1
    new-array p1, p1, [B

    .line 28
    .line 29
    :goto_0
    iget v2, p0, Lk2/v;->e:I

    .line 30
    .line 31
    const/4 v3, 0x0

    .line 32
    invoke-static {v0, v2, p1, v3, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 33
    .line 34
    .line 35
    iput v3, p0, Lk2/v;->e:I

    .line 36
    .line 37
    iput v1, p0, Lk2/v;->f:I

    .line 38
    .line 39
    iput-object p1, p0, Lk2/v;->g:[B

    .line 40
    .line 41
    return-void
.end method

.method public final j(Lk2/e;JI)V
    .locals 8

    .line 1
    iget-object v0, p0, Lk2/v;->i:Landroidx/media3/common/r;

    .line 2
    .line 3
    invoke-static {v0}, Lt1/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lk2/v;->c:Lk2/d;

    .line 7
    .line 8
    iget-object v1, p1, Lk2/e;->a:Lcom/google/common/collect/ImmutableList;

    .line 9
    .line 10
    iget-wide v2, p1, Lk2/e;->c:J

    .line 11
    .line 12
    invoke-virtual {v0, v1, v2, v3}, Lk2/d;->a(Ljava/util/List;J)[B

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v1, p0, Lk2/v;->d:Lt1/G;

    .line 17
    .line 18
    invoke-virtual {v1, v0}, Lt1/G;->T([B)V

    .line 19
    .line 20
    .line 21
    iget-object v1, p0, Lk2/v;->a:LN1/T;

    .line 22
    .line 23
    iget-object v2, p0, Lk2/v;->d:Lt1/G;

    .line 24
    .line 25
    array-length v3, v0

    .line 26
    invoke-interface {v1, v2, v3}, LN1/T;->a(Lt1/G;I)V

    .line 27
    .line 28
    .line 29
    iget-wide v1, p1, Lk2/e;->b:J

    .line 30
    .line 31
    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    .line 32
    .line 33
    .line 34
    .line 35
    .line 36
    const/4 p1, 0x1

    .line 37
    const-wide v5, 0x7fffffffffffffffL

    .line 38
    .line 39
    .line 40
    .line 41
    .line 42
    cmp-long v7, v1, v3

    .line 43
    .line 44
    if-nez v7, :cond_1

    .line 45
    .line 46
    iget-object v1, p0, Lk2/v;->i:Landroidx/media3/common/r;

    .line 47
    .line 48
    iget-wide v1, v1, Landroidx/media3/common/r;->t:J

    .line 49
    .line 50
    cmp-long v3, v1, v5

    .line 51
    .line 52
    if-nez v3, :cond_0

    .line 53
    .line 54
    const/4 v1, 0x1

    .line 55
    goto :goto_0

    .line 56
    :cond_0
    const/4 v1, 0x0

    .line 57
    :goto_0
    invoke-static {v1}, Lt1/a;->g(Z)V

    .line 58
    .line 59
    .line 60
    :goto_1
    move-wide v2, p2

    .line 61
    goto :goto_2

    .line 62
    :cond_1
    iget-object v3, p0, Lk2/v;->i:Landroidx/media3/common/r;

    .line 63
    .line 64
    iget-wide v3, v3, Landroidx/media3/common/r;->t:J

    .line 65
    .line 66
    cmp-long v7, v3, v5

    .line 67
    .line 68
    if-nez v7, :cond_2

    .line 69
    .line 70
    add-long/2addr p2, v1

    .line 71
    goto :goto_1

    .line 72
    :cond_2
    add-long p2, v1, v3

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :goto_2
    iget-object v1, p0, Lk2/v;->a:LN1/T;

    .line 76
    .line 77
    or-int/lit8 v4, p4, 0x1

    .line 78
    .line 79
    array-length v5, v0

    .line 80
    const/4 v6, 0x0

    .line 81
    const/4 v7, 0x0

    .line 82
    invoke-interface/range {v1 .. v7}, LN1/T;->d(JIIILN1/T$a;)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public k(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lk2/v;->j:Z

    .line 2
    .line 3
    return-void
.end method
