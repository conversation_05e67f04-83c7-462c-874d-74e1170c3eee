.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;
.super LXW0/a;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u00012\u00020\u0002B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\t\u0010\u0004J\u0019\u0010\u000c\u001a\u00020\u00082\u0008\u0010\u000b\u001a\u0004\u0018\u00010\nH\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u0004J\u000f\u0010\u000f\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0004J\u000f\u0010\u0010\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0004J\u001f\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0019\u0010\u0019\u001a\u00020\u00082\u0008\u0010\u0018\u001a\u0004\u0018\u00010\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0004R\"\u0010#\u001a\u00020\u001c8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 \"\u0004\u0008!\u0010\"R\u001b\u0010)\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u001b\u0010,\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008*\u0010&\u001a\u0004\u0008+\u0010\u0007R\u001b\u00102\u001a\u00020-8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\u00a8\u00063"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;",
        "LXW0/a;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;",
        "<init>",
        "()V",
        "Ln40/e;",
        "a1",
        "()Ln40/e;",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onDestroyView",
        "x2",
        "Lq40/b;",
        "item",
        "",
        "imageUrl",
        "K2",
        "(Lq40/b;Ljava/lang/String;)V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "config",
        "J2",
        "(Lorg/xbet/uikit/components/lottie/a;)V",
        "I2",
        "Ln40/e$c;",
        "i0",
        "Ln40/e$c;",
        "H2",
        "()Ln40/e$c;",
        "setViewModelFactory",
        "(Ln40/e$c;)V",
        "viewModelFactory",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;",
        "j0",
        "Lkotlin/j;",
        "G2",
        "()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;",
        "viewModel",
        "k0",
        "E2",
        "component",
        "Lm40/e;",
        "l0",
        "LRc/c;",
        "F2",
        "()Lm40/e;",
        "viewBinding",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic m0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:Ln40/e$c;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xbet/games_section/feature/daily_tournament/databinding/DailyTournamentScreenFgBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->m0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lh40/b;->daily_tournament_screen_fg:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/b;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/b;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/c;

    .line 51
    .line 52
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/c;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->k0:Lkotlin/j;

    .line 60
    .line 61
    sget-object v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$viewBinding$2;->INSTANCE:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$viewBinding$2;

    .line 62
    .line 63
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->l0:LRc/c;

    .line 68
    .line 69
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->I2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;Lorg/xbet/uikit/components/lottie/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->J2(Lorg/xbet/uikit/components/lottie/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;Lq40/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->K2(Lq40/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final D2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Ln40/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;

    .line 6
    .line 7
    invoke-interface {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;->a1()Ln40/e;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static final L2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->H2()Ln40/e$c;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Ln40/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->D2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Ln40/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->L2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final E2()Ln40/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ln40/e;

    .line 8
    .line 9
    return-object v0
.end method

.method public final F2()Lm40/e;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->m0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lm40/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final G2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final H2()Ln40/e$c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->i0:Ln40/e$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final I2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/e;->d:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, Lm40/e;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public final J2(Lorg/xbet/uikit/components/lottie/a;)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lm40/e;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 8
    .line 9
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iget-object p1, p1, Lm40/e;->c:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 17
    .line 18
    const/4 v0, 0x0

    .line 19
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iget-object p1, p1, Lm40/e;->d:Landroid/widget/FrameLayout;

    .line 27
    .line 28
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final K2(Lq40/b;Ljava/lang/String;)V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->I2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, Lm40/e;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, Lm40/e;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 21
    .line 22
    new-instance v1, Lr40/b;

    .line 23
    .line 24
    new-instance v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem;

    .line 25
    .line 26
    sget-object v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;->TABLE_RESULT:Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;

    .line 27
    .line 28
    invoke-direct {v2, v3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;)V

    .line 29
    .line 30
    .line 31
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem;

    .line 32
    .line 33
    sget-object v4, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;->PRIZE:Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;

    .line 34
    .line 35
    invoke-direct {v3, v4}, Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem$Type;)V

    .line 36
    .line 37
    .line 38
    const/4 v4, 0x2

    .line 39
    new-array v4, v4, [Lorg/xbet/games_section/feature/daily_tournament/presentation/adapter/DailyAdapterItem;

    .line 40
    .line 41
    const/4 v5, 0x0

    .line 42
    aput-object v2, v4, v5

    .line 43
    .line 44
    const/4 v2, 0x1

    .line 45
    aput-object v3, v4, v2

    .line 46
    .line 47
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    sget v3, Lpb/k;->dt_today_prize:I

    .line 52
    .line 53
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v3

    .line 57
    invoke-direct {v1, v2, p1, p2, v3}, Lr40/b;-><init>(Ljava/util/List;Lq40/b;Ljava/lang/String;Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 61
    .line 62
    .line 63
    :cond_0
    return-void
.end method

.method public a1()Ln40/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->E2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, Lm40/e;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 2

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget v0, Lpb/f;->space_8:I

    .line 9
    .line 10
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->F2()Lm40/e;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, Lm40/e;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$a;

    .line 21
    .line 22
    invoke-direct {v1, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$a;-><init>(I)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->E2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, Ln40/e;->c(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->G2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentViewModel;->B3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
