.class public final synthetic LG91/y;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;

.field public final synthetic c:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LG91/y;->a:LB4/a;

    iput-object p2, p0, LG91/y;->b:Lkotlin/jvm/functions/Function1;

    iput-object p3, p0, LG91/y;->c:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LG91/y;->a:LB4/a;

    iget-object v1, p0, LG91/y;->b:Lkot<PERSON>/jvm/functions/Function1;

    iget-object v2, p0, LG91/y;->c:Lkotlin/jvm/functions/Function0;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeProviderChipsAdapterDelegateKt;->c(LB4/a;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
