.class public final synthetic LrA0/F;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Ljava/util/List;

.field public final synthetic c:LmA0/a;

.field public final synthetic d:Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>(LB4/a;Ljava/util/List;LmA0/a;Lkotlin/jvm/functions/Function2;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/F;->a:LB4/a;

    iput-object p2, p0, LrA0/F;->b:Ljava/util/List;

    iput-object p3, p0, LrA0/F;->c:LmA0/a;

    iput-object p4, p0, LrA0/F;->d:Lkotlin/jvm/functions/Function2;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LrA0/F;->a:LB4/a;

    iget-object v1, p0, LrA0/F;->b:Ljava/util/List;

    iget-object v2, p0, LrA0/F;->c:LmA0/a;

    iget-object v3, p0, LrA0/F;->d:Lkotlin/jvm/functions/Function2;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/StadiumInfoAdapterDelegateKt;->d(LB4/a;Ljava/util/List;LmA0/a;Lkotlin/jvm/functions/Function2;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
