.class public final LQP0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQP0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQP0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LQP0/a$a$a;
    }
.end annotation


# instance fields
.field public final a:LSX0/a;

.field public final b:LQP0/a$a;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNP0/b;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/tennis/impl/earned_points/data/repository/EarnedPointsRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTP0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Lorg/xbet/statistic/tennis/impl/earned_points/presentation/viewmodel/a;

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQP0/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LQP0/a$a;->b:LQP0/a$a;

    .line 4
    iput-object p4, p0, LQP0/a$a;->a:LSX0/a;

    .line 5
    invoke-virtual/range {p0 .. p9}, LQP0/a$a;->b(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;LQP0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p9}, LQP0/a$a;-><init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LQP0/a$a;->c(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;)Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p7

    .line 5
    iput-object p7, p0, LQP0/a$a;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    iput-object p2, p0, LQP0/a$a;->d:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p2}, LNP0/c;->a(LBc/a;)LNP0/c;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    iput-object p2, p0, LQP0/a$a;->e:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    iput-object p2, p0, LQP0/a$a;->f:Ldagger/internal/h;

    .line 24
    .line 25
    new-instance p2, LQP0/a$a$a;

    .line 26
    .line 27
    invoke-direct {p2, p1}, LQP0/a$a$a;-><init>(LQW0/c;)V

    .line 28
    .line 29
    .line 30
    iput-object p2, p0, LQP0/a$a;->g:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p1, p0, LQP0/a$a;->e:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object p7, p0, LQP0/a$a;->f:Ldagger/internal/h;

    .line 35
    .line 36
    invoke-static {p1, p7, p2}, Lorg/xbet/statistic/tennis/impl/earned_points/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/earned_points/data/repository/a;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, LQP0/a$a;->h:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {p1}, LTP0/b;->a(LBc/a;)LTP0/b;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    iput-object p1, p0, LQP0/a$a;->i:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LQP0/a$a;->j:Ldagger/internal/h;

    .line 53
    .line 54
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, LQP0/a$a;->k:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LQP0/a$a;->l:Ldagger/internal/h;

    .line 65
    .line 66
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 67
    .line 68
    .line 69
    move-result-object p7

    .line 70
    iput-object p7, p0, LQP0/a$a;->m:Ldagger/internal/h;

    .line 71
    .line 72
    iget-object p2, p0, LQP0/a$a;->c:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object p3, p0, LQP0/a$a;->i:Ldagger/internal/h;

    .line 75
    .line 76
    iget-object p4, p0, LQP0/a$a;->j:Ldagger/internal/h;

    .line 77
    .line 78
    iget-object p5, p0, LQP0/a$a;->k:Ldagger/internal/h;

    .line 79
    .line 80
    iget-object p6, p0, LQP0/a$a;->l:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object p8, p0, LQP0/a$a;->g:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static/range {p2 .. p8}, Lorg/xbet/statistic/tennis/impl/earned_points/presentation/viewmodel/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/tennis/impl/earned_points/presentation/viewmodel/a;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LQP0/a$a;->n:Lorg/xbet/statistic/tennis/impl/earned_points/presentation/viewmodel/a;

    .line 89
    .line 90
    invoke-static {p1}, LQP0/g;->c(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/viewmodel/a;)Ldagger/internal/h;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iput-object p1, p0, LQP0/a$a;->o:Ldagger/internal/h;

    .line 95
    .line 96
    return-void
.end method

.method public final c(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;)Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LQP0/a$a;->a:LSX0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/f;->a(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;LSX0/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LQP0/a$a;->o:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LQP0/f;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/f;->b(Lorg/xbet/statistic/tennis/impl/earned_points/presentation/fragment/EarnedPointsFragment;LQP0/f;)V

    .line 15
    .line 16
    .line 17
    return-object p1
.end method
