.class public final LC31/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final A:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final B:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final C:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final D:Landroidx/constraintlayout/widget/Barrier;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final a:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Landroidx/constraintlayout/widget/Group;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final n:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final o:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final p:Landroidx/constraintlayout/widget/Group;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final q:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final r:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final s:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final t:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final u:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final w:Landroid/widget/ImageView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final x:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final y:Landroid/widget/TextView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Landroid/widget/ImageView;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;Landroidx/constraintlayout/widget/Group;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroidx/constraintlayout/widget/Group;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Landroid/widget/ImageView;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;Landroidx/constraintlayout/widget/Barrier;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/teamlogo/TeamLogo;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/uikit/components/teamlogo/TeamLogo;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Landroidx/constraintlayout/widget/Group;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p14    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p15    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p16    # Landroidx/constraintlayout/widget/Group;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p17    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p18    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p19    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p20    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p21    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p22    # Lorg/xbet/uikit/components/teamlogo/TeamLogo;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p23    # Landroid/widget/ImageView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p25    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p26    # Lorg/xbet/uikit/components/teamlogo/TeamLogo;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p27    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p28    # Landroid/widget/TextView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p29    # Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p30    # Landroidx/constraintlayout/widget/Barrier;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, LC31/k;->a:Landroid/view/View;

    .line 3
    iput-object p2, p0, LC31/k;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 4
    iput-object p3, p0, LC31/k;->c:Landroid/widget/ImageView;

    .line 5
    iput-object p4, p0, LC31/k;->d:Lorg/xbet/uikit/components/separator/Separator;

    .line 6
    iput-object p5, p0, LC31/k;->e:Landroid/widget/TextView;

    .line 7
    iput-object p6, p0, LC31/k;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 8
    iput-object p7, p0, LC31/k;->g:Lorg/xbet/uikit/components/separator/Separator;

    .line 9
    iput-object p8, p0, LC31/k;->h:Landroid/widget/TextView;

    .line 10
    iput-object p9, p0, LC31/k;->i:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 11
    iput-object p10, p0, LC31/k;->j:Landroidx/constraintlayout/widget/Group;

    .line 12
    iput-object p11, p0, LC31/k;->k:Landroid/widget/TextView;

    .line 13
    iput-object p12, p0, LC31/k;->l:Landroid/widget/TextView;

    .line 14
    iput-object p13, p0, LC31/k;->m:Landroid/widget/TextView;

    .line 15
    iput-object p14, p0, LC31/k;->n:Landroid/widget/TextView;

    .line 16
    iput-object p15, p0, LC31/k;->o:Landroid/widget/TextView;

    move-object/from16 p1, p16

    .line 17
    iput-object p1, p0, LC31/k;->p:Landroidx/constraintlayout/widget/Group;

    move-object/from16 p1, p17

    .line 18
    iput-object p1, p0, LC31/k;->q:Landroid/widget/TextView;

    move-object/from16 p1, p18

    .line 19
    iput-object p1, p0, LC31/k;->r:Landroid/widget/TextView;

    move-object/from16 p1, p19

    .line 20
    iput-object p1, p0, LC31/k;->s:Landroid/widget/TextView;

    move-object/from16 p1, p20

    .line 21
    iput-object p1, p0, LC31/k;->t:Landroid/widget/TextView;

    move-object/from16 p1, p21

    .line 22
    iput-object p1, p0, LC31/k;->u:Landroid/widget/TextView;

    move-object/from16 p1, p22

    .line 23
    iput-object p1, p0, LC31/k;->v:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    move-object/from16 p1, p23

    .line 24
    iput-object p1, p0, LC31/k;->w:Landroid/widget/ImageView;

    move-object/from16 p1, p24

    .line 25
    iput-object p1, p0, LC31/k;->x:Lorg/xbet/uikit/components/separator/Separator;

    move-object/from16 p1, p25

    .line 26
    iput-object p1, p0, LC31/k;->y:Landroid/widget/TextView;

    move-object/from16 p1, p26

    .line 27
    iput-object p1, p0, LC31/k;->z:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    move-object/from16 p1, p27

    .line 28
    iput-object p1, p0, LC31/k;->A:Lorg/xbet/uikit/components/separator/Separator;

    move-object/from16 p1, p28

    .line 29
    iput-object p1, p0, LC31/k;->B:Landroid/widget/TextView;

    move-object/from16 p1, p29

    .line 30
    iput-object p1, p0, LC31/k;->C:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    move-object/from16 p1, p30

    .line 31
    iput-object p1, p0, LC31/k;->D:Landroidx/constraintlayout/widget/Barrier;

    return-void
.end method

.method public static a(Landroid/view/View;)LC31/k;
    .locals 31
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    sget v0, Lm31/d;->botFirstLogo:I

    .line 4
    .line 5
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    check-cast v2, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    if-eqz v2, :cond_0

    .line 12
    .line 13
    sget v0, Lm31/d;->botGameIndicator:I

    .line 14
    .line 15
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    check-cast v3, Landroid/widget/ImageView;

    .line 20
    .line 21
    if-eqz v3, :cond_0

    .line 22
    .line 23
    sget v0, Lm31/d;->botResultSeparator:I

    .line 24
    .line 25
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    check-cast v4, Lorg/xbet/uikit/components/separator/Separator;

    .line 30
    .line 31
    if-eqz v4, :cond_0

    .line 32
    .line 33
    sget v0, Lm31/d;->botScore:I

    .line 34
    .line 35
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    check-cast v5, Landroid/widget/TextView;

    .line 40
    .line 41
    if-eqz v5, :cond_0

    .line 42
    .line 43
    sget v0, Lm31/d;->botSecondLogo:I

    .line 44
    .line 45
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 46
    .line 47
    .line 48
    move-result-object v6

    .line 49
    check-cast v6, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 50
    .line 51
    if-eqz v6, :cond_0

    .line 52
    .line 53
    sget v0, Lm31/d;->botSetSeparator:I

    .line 54
    .line 55
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 56
    .line 57
    .line 58
    move-result-object v7

    .line 59
    check-cast v7, Lorg/xbet/uikit/components/separator/Separator;

    .line 60
    .line 61
    if-eqz v7, :cond_0

    .line 62
    .line 63
    sget v0, Lm31/d;->botTeamName:I

    .line 64
    .line 65
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 66
    .line 67
    .line 68
    move-result-object v8

    .line 69
    check-cast v8, Landroid/widget/TextView;

    .line 70
    .line 71
    if-eqz v8, :cond_0

    .line 72
    .line 73
    sget v0, Lm31/d;->botVictoryIndicator:I

    .line 74
    .line 75
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 76
    .line 77
    .line 78
    move-result-object v9

    .line 79
    check-cast v9, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 80
    .line 81
    if-eqz v9, :cond_0

    .line 82
    .line 83
    sget v0, Lm31/d;->columnsGroup:I

    .line 84
    .line 85
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 86
    .line 87
    .line 88
    move-result-object v10

    .line 89
    check-cast v10, Landroidx/constraintlayout/widget/Group;

    .line 90
    .line 91
    if-eqz v10, :cond_0

    .line 92
    .line 93
    sget v0, Lm31/d;->firstColumnBotScore:I

    .line 94
    .line 95
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 96
    .line 97
    .line 98
    move-result-object v11

    .line 99
    check-cast v11, Landroid/widget/TextView;

    .line 100
    .line 101
    if-eqz v11, :cond_0

    .line 102
    .line 103
    sget v0, Lm31/d;->firstColumnTopScore:I

    .line 104
    .line 105
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 106
    .line 107
    .line 108
    move-result-object v12

    .line 109
    check-cast v12, Landroid/widget/TextView;

    .line 110
    .line 111
    if-eqz v12, :cond_0

    .line 112
    .line 113
    sget v0, Lm31/d;->gameText:I

    .line 114
    .line 115
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 116
    .line 117
    .line 118
    move-result-object v13

    .line 119
    check-cast v13, Landroid/widget/TextView;

    .line 120
    .line 121
    if-eqz v13, :cond_0

    .line 122
    .line 123
    sget v0, Lm31/d;->liveInfo:I

    .line 124
    .line 125
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 126
    .line 127
    .line 128
    move-result-object v14

    .line 129
    check-cast v14, Landroid/widget/TextView;

    .line 130
    .line 131
    if-eqz v14, :cond_0

    .line 132
    .line 133
    sget v0, Lm31/d;->resultText:I

    .line 134
    .line 135
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 136
    .line 137
    .line 138
    move-result-object v15

    .line 139
    check-cast v15, Landroid/widget/TextView;

    .line 140
    .line 141
    if-eqz v15, :cond_0

    .line 142
    .line 143
    sget v0, Lm31/d;->scoreGroup:I

    .line 144
    .line 145
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 146
    .line 147
    .line 148
    move-result-object v16

    .line 149
    check-cast v16, Landroidx/constraintlayout/widget/Group;

    .line 150
    .line 151
    if-eqz v16, :cond_0

    .line 152
    .line 153
    sget v0, Lm31/d;->secondColumnBotScore:I

    .line 154
    .line 155
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 156
    .line 157
    .line 158
    move-result-object v17

    .line 159
    check-cast v17, Landroid/widget/TextView;

    .line 160
    .line 161
    if-eqz v17, :cond_0

    .line 162
    .line 163
    sget v0, Lm31/d;->secondColumnTopScore:I

    .line 164
    .line 165
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 166
    .line 167
    .line 168
    move-result-object v18

    .line 169
    check-cast v18, Landroid/widget/TextView;

    .line 170
    .line 171
    if-eqz v18, :cond_0

    .line 172
    .line 173
    sget v0, Lm31/d;->setText:I

    .line 174
    .line 175
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 176
    .line 177
    .line 178
    move-result-object v19

    .line 179
    check-cast v19, Landroid/widget/TextView;

    .line 180
    .line 181
    if-eqz v19, :cond_0

    .line 182
    .line 183
    sget v0, Lm31/d;->thirdColumnBotScore:I

    .line 184
    .line 185
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 186
    .line 187
    .line 188
    move-result-object v20

    .line 189
    check-cast v20, Landroid/widget/TextView;

    .line 190
    .line 191
    if-eqz v20, :cond_0

    .line 192
    .line 193
    sget v0, Lm31/d;->thirdColumnTopScore:I

    .line 194
    .line 195
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 196
    .line 197
    .line 198
    move-result-object v21

    .line 199
    check-cast v21, Landroid/widget/TextView;

    .line 200
    .line 201
    if-eqz v21, :cond_0

    .line 202
    .line 203
    sget v0, Lm31/d;->topFirstLogo:I

    .line 204
    .line 205
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 206
    .line 207
    .line 208
    move-result-object v22

    .line 209
    check-cast v22, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 210
    .line 211
    if-eqz v22, :cond_0

    .line 212
    .line 213
    sget v0, Lm31/d;->topGameIndicator:I

    .line 214
    .line 215
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 216
    .line 217
    .line 218
    move-result-object v23

    .line 219
    check-cast v23, Landroid/widget/ImageView;

    .line 220
    .line 221
    if-eqz v23, :cond_0

    .line 222
    .line 223
    sget v0, Lm31/d;->topResultSeparator:I

    .line 224
    .line 225
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 226
    .line 227
    .line 228
    move-result-object v24

    .line 229
    check-cast v24, Lorg/xbet/uikit/components/separator/Separator;

    .line 230
    .line 231
    if-eqz v24, :cond_0

    .line 232
    .line 233
    sget v0, Lm31/d;->topScore:I

    .line 234
    .line 235
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 236
    .line 237
    .line 238
    move-result-object v25

    .line 239
    check-cast v25, Landroid/widget/TextView;

    .line 240
    .line 241
    if-eqz v25, :cond_0

    .line 242
    .line 243
    sget v0, Lm31/d;->topSecondLogo:I

    .line 244
    .line 245
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 246
    .line 247
    .line 248
    move-result-object v26

    .line 249
    check-cast v26, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 250
    .line 251
    if-eqz v26, :cond_0

    .line 252
    .line 253
    sget v0, Lm31/d;->topSetSeparator:I

    .line 254
    .line 255
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 256
    .line 257
    .line 258
    move-result-object v27

    .line 259
    check-cast v27, Lorg/xbet/uikit/components/separator/Separator;

    .line 260
    .line 261
    if-eqz v27, :cond_0

    .line 262
    .line 263
    sget v0, Lm31/d;->topTeamName:I

    .line 264
    .line 265
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 266
    .line 267
    .line 268
    move-result-object v28

    .line 269
    check-cast v28, Landroid/widget/TextView;

    .line 270
    .line 271
    if-eqz v28, :cond_0

    .line 272
    .line 273
    sget v0, Lm31/d;->topVictoryIndicator:I

    .line 274
    .line 275
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 276
    .line 277
    .line 278
    move-result-object v29

    .line 279
    check-cast v29, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 280
    .line 281
    if-eqz v29, :cond_0

    .line 282
    .line 283
    sget v0, Lm31/d;->verticalBarrier:I

    .line 284
    .line 285
    invoke-static {v1, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 286
    .line 287
    .line 288
    move-result-object v30

    .line 289
    check-cast v30, Landroidx/constraintlayout/widget/Barrier;

    .line 290
    .line 291
    if-eqz v30, :cond_0

    .line 292
    .line 293
    new-instance v0, LC31/k;

    .line 294
    .line 295
    invoke-direct/range {v0 .. v30}, LC31/k;-><init>(Landroid/view/View;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Landroid/widget/ImageView;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;Landroidx/constraintlayout/widget/Group;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroidx/constraintlayout/widget/Group;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Landroid/widget/ImageView;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/separator/Separator;Landroid/widget/TextView;Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;Landroidx/constraintlayout/widget/Barrier;)V

    .line 296
    .line 297
    .line 298
    return-object v0

    .line 299
    :cond_0
    invoke-virtual/range {p0 .. p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 300
    .line 301
    .line 302
    move-result-object v1

    .line 303
    invoke-virtual {v1, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 304
    .line 305
    .line 306
    move-result-object v0

    .line 307
    new-instance v1, Ljava/lang/NullPointerException;

    .line 308
    .line 309
    const-string v2, "Missing required view with ID: "

    .line 310
    .line 311
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 312
    .line 313
    .line 314
    move-result-object v0

    .line 315
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 316
    .line 317
    .line 318
    throw v1
.end method

.method public static b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/k;
    .locals 1
    .param p0    # Landroid/view/LayoutInflater;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget v0, Lm31/e;->event_card_compact_live_info_view:I

    .line 4
    .line 5
    invoke-virtual {p0, v0, p1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    invoke-static {p1}, LC31/k;->a(Landroid/view/View;)LC31/k;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    return-object p0

    .line 13
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 14
    .line 15
    const-string p1, "parent"

    .line 16
    .line 17
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    throw p0
.end method


# virtual methods
.method public getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LC31/k;->a:Landroid/view/View;

    .line 2
    .line 3
    return-object v0
.end method
