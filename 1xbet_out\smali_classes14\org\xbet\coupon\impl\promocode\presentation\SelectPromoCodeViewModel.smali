.class public final Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001:\u0001=Bm\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000c\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0013\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0013\u0010\u001d\u001a\u0008\u0012\u0004\u0012\u00020\u001c0\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\u0004\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010#R\u0014\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010&R\u0014\u0010\u0008\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010\n\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0014\u0010\u000e\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0014\u0010\u0010\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u0010\u0012\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u0014\u001a\u00020\u00138\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u0010\u0016\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\u0018\u001a\u00020\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u001a\u0010<\u001a\u0008\u0012\u0004\u0012\u00020\u001c098\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;\u00a8\u0006>"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "",
        "isDesignSystem",
        "fromMakeBet",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/G;",
        "getAllBetEventEntitiesUseCase",
        "Ltw/d;",
        "getCouponTypeUseCase",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;",
        "getVidUseCase",
        "LHX0/e;",
        "resourceManager",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Ltw/o;",
        "updateCouponUseCase",
        "LMx/a;",
        "getBetInfoModelUseCase",
        "Ltw/e;",
        "getExpressNumWithCheckEventsUseCase",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "<init>",
        "(ZZLorg/xbet/coupon/impl/coupon/domain/usecases/G;Ltw/d;Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;LHX0/e;Lm8/a;Lfk/m;Ltw/o;LMx/a;Ltw/e;Lcom/xbet/onexuser/domain/user/c;)V",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
        "C3",
        "()Lkotlinx/coroutines/flow/f0;",
        "",
        "D3",
        "()V",
        "v1",
        "Z",
        "x1",
        "y1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/G;",
        "F1",
        "Ltw/d;",
        "H1",
        "Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;",
        "I1",
        "LHX0/e;",
        "P1",
        "Lm8/a;",
        "S1",
        "Lfk/m;",
        "V1",
        "Ltw/o;",
        "b2",
        "LMx/a;",
        "v2",
        "Ltw/e;",
        "x2",
        "Lcom/xbet/onexuser/domain/user/c;",
        "Lkotlinx/coroutines/flow/V;",
        "y2",
        "Lkotlinx/coroutines/flow/V;",
        "promoCodeScreenStateStream",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Ltw/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Ltw/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LMx/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Z

.field public final v2:Ltw/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Z

.field public final x2:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(ZZLorg/xbet/coupon/impl/coupon/domain/usecases/G;Ltw/d;Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;LHX0/e;Lm8/a;Lfk/m;Ltw/o;LMx/a;Ltw/e;Lcom/xbet/onexuser/domain/user/c;)V
    .locals 0
    .param p3    # Lorg/xbet/coupon/impl/coupon/domain/usecases/G;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ltw/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Ltw/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LMx/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ltw/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-boolean p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->v1:Z

    .line 5
    .line 6
    iput-boolean p2, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x1:Z

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->F1:Ltw/d;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->I1:LHX0/e;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->P1:Lm8/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->S1:Lfk/m;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->V1:Ltw/o;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->b2:LMx/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->v2:Ltw/e;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x2:Lcom/xbet/onexuser/domain/user/c;

    .line 27
    .line 28
    sget-object p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;

    .line 29
    .line 30
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->D3()V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lcom/xbet/onexuser/domain/user/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x2:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->v1:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final E3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->E3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x1:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic r3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/G;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y1:Lorg/xbet/coupon/impl/coupon/domain/usecases/G;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)LMx/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->b2:LMx/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->F1:Ltw/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->v2:Ltw/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->S1:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->H1:Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->I1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->V1:Ltw/o;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final C3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final D3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/coupon/impl/promocode/presentation/g;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/coupon/impl/promocode/presentation/g;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->P1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
