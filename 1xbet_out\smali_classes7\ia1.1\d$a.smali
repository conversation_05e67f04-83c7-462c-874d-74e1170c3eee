.class public final Lia1/d$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lia1/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lia1/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lia1/d$a$c;,
        Lia1/d$a$g;,
        Lia1/d$a$b;,
        Lia1/d$a$a;,
        Lia1/d$a$d;,
        Lia1/d$a$n;,
        Lia1/d$a$j;,
        Lia1/d$a$o;,
        Lia1/d$a$e;,
        Lia1/d$a$m;,
        Lia1/d$a$s;,
        Lia1/d$a$k;,
        Lia1/d$a$h;,
        Lia1/d$a$r;,
        Lia1/d$a$i;,
        Lia1/d$a$f;,
        Lia1/d$a$q;,
        Lia1/d$a$l;,
        Lia1/d$a$p;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/a;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/j;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/d;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQW0/c;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le81/d;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/b;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf81/c;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/g;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/T;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Le81/c;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/n;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LUR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LzX0/k;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:Lak/b;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LP91/b;

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Lorg/xbet/analytics/domain/b;

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LAR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LSX0/c;

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LZR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:Lia1/d$a;

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/s;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lya1/a;",
            ">;"
        }
    .end annotation
.end field

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LC81/f;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lgk/d;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Leu/l;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lu81/b;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/h;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJT/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LT91/a;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/impl/gifts/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lia1/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V
    .locals 4

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, Lia1/d$a;->g:Lia1/d$a;

    .line 4
    iput-object p6, p0, Lia1/d$a;->a:LTZ0/a;

    move-object/from16 v0, p31

    .line 5
    iput-object v0, p0, Lia1/d$a;->b:LzX0/k;

    .line 6
    iput-object p3, p0, Lia1/d$a;->c:Lak/b;

    move-object/from16 v1, p17

    .line 7
    iput-object v1, p0, Lia1/d$a;->d:LP91/b;

    move-object/from16 v2, p21

    .line 8
    iput-object v2, p0, Lia1/d$a;->e:Lorg/xbet/analytics/domain/b;

    move-object/from16 v3, p20

    .line 9
    iput-object v3, p0, Lia1/d$a;->f:LSX0/c;

    .line 10
    invoke-virtual/range {p0 .. p34}, Lia1/d$a;->c(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V

    .line 11
    invoke-virtual/range {p0 .. p34}, Lia1/d$a;->d(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V

    .line 12
    invoke-virtual/range {p0 .. p34}, Lia1/d$a;->e(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;Lia1/e;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p34}, Lia1/d$a;-><init>(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lia1/d$a;->f(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b()Lorg/xplatform/aggregator/impl/core/presentation/c;
    .locals 4

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 2
    .line 3
    iget-object v1, p0, Lia1/d$a;->N:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    check-cast v1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 10
    .line 11
    iget-object v2, p0, Lia1/d$a;->d:LP91/b;

    .line 12
    .line 13
    invoke-virtual {p0}, Lia1/d$a;->h()Lorg/xbet/analytics/domain/scope/g0;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    invoke-direct {v0, v1, v2, v3}, Lorg/xplatform/aggregator/impl/core/presentation/c;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LP91/b;Lorg/xbet/analytics/domain/scope/g0;)V

    .line 18
    .line 19
    .line 20
    return-object v0
.end method

.method public final c(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V
    .locals 1

    .line 1
    new-instance p3, Lia1/d$a$c;

    .line 2
    .line 3
    invoke-direct {p3, p2}, Lia1/d$a$c;-><init>(LN91/e;)V

    .line 4
    .line 5
    .line 6
    iput-object p3, p0, Lia1/d$a;->h:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 9
    .line 10
    .line 11
    move-result-object p3

    .line 12
    iput-object p3, p0, Lia1/d$a;->i:Ldagger/internal/h;

    .line 13
    .line 14
    iget-object p5, p0, Lia1/d$a;->h:Ldagger/internal/h;

    .line 15
    .line 16
    invoke-static {p5, p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/c;->a(LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/c;

    .line 17
    .line 18
    .line 19
    move-result-object p3

    .line 20
    iput-object p3, p0, Lia1/d$a;->j:Ldagger/internal/h;

    .line 21
    .line 22
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 23
    .line 24
    .line 25
    move-result-object p3

    .line 26
    iput-object p3, p0, Lia1/d$a;->k:Ldagger/internal/h;

    .line 27
    .line 28
    new-instance p3, Lia1/d$a$g;

    .line 29
    .line 30
    invoke-direct {p3, p4}, Lia1/d$a$g;-><init>(Lak/a;)V

    .line 31
    .line 32
    .line 33
    iput-object p3, p0, Lia1/d$a;->l:Ldagger/internal/h;

    .line 34
    .line 35
    invoke-static/range {p29 .. p29}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 36
    .line 37
    .line 38
    move-result-object p3

    .line 39
    iput-object p3, p0, Lia1/d$a;->m:Ldagger/internal/h;

    .line 40
    .line 41
    invoke-static/range {p33 .. p33}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 42
    .line 43
    .line 44
    move-result-object p3

    .line 45
    iput-object p3, p0, Lia1/d$a;->n:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object p5, p0, Lia1/d$a;->j:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object p6, p0, Lia1/d$a;->k:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object p8, p0, Lia1/d$a;->l:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object v0, p0, Lia1/d$a;->m:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p5, p6, p8, v0, p3}, Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/b;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/b;

    .line 56
    .line 57
    .line 58
    move-result-object p3

    .line 59
    iput-object p3, p0, Lia1/d$a;->o:Ldagger/internal/h;

    .line 60
    .line 61
    new-instance p3, Lia1/d$a$b;

    .line 62
    .line 63
    invoke-direct {p3, p2}, Lia1/d$a$b;-><init>(LN91/e;)V

    .line 64
    .line 65
    .line 66
    iput-object p3, p0, Lia1/d$a;->p:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/k;->a(LBc/a;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/k;

    .line 69
    .line 70
    .line 71
    move-result-object p3

    .line 72
    iput-object p3, p0, Lia1/d$a;->q:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object p3, p0, Lia1/d$a;->j:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/i;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/i;

    .line 77
    .line 78
    .line 79
    move-result-object p3

    .line 80
    iput-object p3, p0, Lia1/d$a;->r:Ldagger/internal/h;

    .line 81
    .line 82
    invoke-static/range {p27 .. p27}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 83
    .line 84
    .line 85
    move-result-object p3

    .line 86
    iput-object p3, p0, Lia1/d$a;->s:Ldagger/internal/h;

    .line 87
    .line 88
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/b;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/b;

    .line 89
    .line 90
    .line 91
    move-result-object p3

    .line 92
    iput-object p3, p0, Lia1/d$a;->t:Ldagger/internal/h;

    .line 93
    .line 94
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 95
    .line 96
    .line 97
    move-result-object p3

    .line 98
    iput-object p3, p0, Lia1/d$a;->u:Ldagger/internal/h;

    .line 99
    .line 100
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/b;->a(LBc/a;)Lorg/xplatform/aggregator/impl/favorite/domain/usecases/b;

    .line 101
    .line 102
    .line 103
    move-result-object p3

    .line 104
    iput-object p3, p0, Lia1/d$a;->v:Ldagger/internal/h;

    .line 105
    .line 106
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/g;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/g;

    .line 107
    .line 108
    .line 109
    move-result-object p3

    .line 110
    iput-object p3, p0, Lia1/d$a;->w:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p3, p0, Lia1/d$a;->v:Ldagger/internal/h;

    .line 113
    .line 114
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/e;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/e;

    .line 115
    .line 116
    .line 117
    move-result-object p3

    .line 118
    iput-object p3, p0, Lia1/d$a;->x:Ldagger/internal/h;

    .line 119
    .line 120
    iget-object p3, p0, Lia1/d$a;->h:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/gifts/usecases/m;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/m;

    .line 123
    .line 124
    .line 125
    move-result-object p3

    .line 126
    iput-object p3, p0, Lia1/d$a;->y:Ldagger/internal/h;

    .line 127
    .line 128
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 129
    .line 130
    .line 131
    move-result-object p3

    .line 132
    iput-object p3, p0, Lia1/d$a;->z:Ldagger/internal/h;

    .line 133
    .line 134
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 135
    .line 136
    .line 137
    move-result-object p3

    .line 138
    iput-object p3, p0, Lia1/d$a;->A:Ldagger/internal/h;

    .line 139
    .line 140
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 141
    .line 142
    .line 143
    move-result-object p3

    .line 144
    iput-object p3, p0, Lia1/d$a;->B:Ldagger/internal/h;

    .line 145
    .line 146
    new-instance p3, Lia1/d$a$a;

    .line 147
    .line 148
    invoke-direct {p3, p2}, Lia1/d$a$a;-><init>(LN91/e;)V

    .line 149
    .line 150
    .line 151
    iput-object p3, p0, Lia1/d$a;->C:Ldagger/internal/h;

    .line 152
    .line 153
    new-instance p3, Lia1/d$a$d;

    .line 154
    .line 155
    invoke-direct {p3, p4}, Lia1/d$a$d;-><init>(Lak/a;)V

    .line 156
    .line 157
    .line 158
    iput-object p3, p0, Lia1/d$a;->D:Ldagger/internal/h;

    .line 159
    .line 160
    new-instance p3, Lia1/d$a$n;

    .line 161
    .line 162
    invoke-direct {p3, p2}, Lia1/d$a$n;-><init>(LN91/e;)V

    .line 163
    .line 164
    .line 165
    iput-object p3, p0, Lia1/d$a;->E:Ldagger/internal/h;

    .line 166
    .line 167
    invoke-static {p1}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    iput-object p1, p0, Lia1/d$a;->F:Ldagger/internal/h;

    .line 172
    .line 173
    return-void
.end method

.method public final d(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V
    .locals 12

    .line 1
    move-object/from16 v0, p4

    .line 2
    .line 3
    new-instance v1, Lia1/d$a$j;

    .line 4
    .line 5
    invoke-direct {v1, p2}, Lia1/d$a$j;-><init>(LN91/e;)V

    .line 6
    .line 7
    .line 8
    iput-object v1, p0, Lia1/d$a;->G:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    iput-object v1, p0, Lia1/d$a;->H:Ldagger/internal/h;

    .line 15
    .line 16
    new-instance v1, Lia1/d$a$o;

    .line 17
    .line 18
    invoke-direct {v1, v0}, Lia1/d$a$o;-><init>(Lak/a;)V

    .line 19
    .line 20
    .line 21
    iput-object v1, p0, Lia1/d$a;->I:Ldagger/internal/h;

    .line 22
    .line 23
    new-instance v1, Lia1/d$a$e;

    .line 24
    .line 25
    invoke-direct {v1, p2}, Lia1/d$a$e;-><init>(LN91/e;)V

    .line 26
    .line 27
    .line 28
    iput-object v1, p0, Lia1/d$a;->J:Ldagger/internal/h;

    .line 29
    .line 30
    new-instance v1, Lia1/d$a$m;

    .line 31
    .line 32
    invoke-direct {v1, v0}, Lia1/d$a$m;-><init>(Lak/a;)V

    .line 33
    .line 34
    .line 35
    iput-object v1, p0, Lia1/d$a;->K:Ldagger/internal/h;

    .line 36
    .line 37
    new-instance v1, Lia1/d$a$s;

    .line 38
    .line 39
    invoke-direct {v1, v0}, Lia1/d$a$s;-><init>(Lak/a;)V

    .line 40
    .line 41
    .line 42
    iput-object v1, p0, Lia1/d$a;->L:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 45
    .line 46
    .line 47
    move-result-object v11

    .line 48
    iput-object v11, p0, Lia1/d$a;->M:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object v2, p0, Lia1/d$a;->F:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object v3, p0, Lia1/d$a;->G:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object v4, p0, Lia1/d$a;->s:Ldagger/internal/h;

    .line 55
    .line 56
    iget-object v5, p0, Lia1/d$a;->A:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object v6, p0, Lia1/d$a;->H:Ldagger/internal/h;

    .line 59
    .line 60
    iget-object v7, p0, Lia1/d$a;->I:Ldagger/internal/h;

    .line 61
    .line 62
    iget-object v8, p0, Lia1/d$a;->J:Ldagger/internal/h;

    .line 63
    .line 64
    iget-object v9, p0, Lia1/d$a;->K:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object v10, p0, Lia1/d$a;->L:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static/range {v2 .. v11}, Lorg/xplatform/aggregator/impl/core/presentation/C;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/core/presentation/C;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    invoke-static {v1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    iput-object v1, p0, Lia1/d$a;->N:Ldagger/internal/h;

    .line 77
    .line 78
    new-instance v1, Lia1/d$a$k;

    .line 79
    .line 80
    invoke-direct {v1, p2}, Lia1/d$a$k;-><init>(LN91/e;)V

    .line 81
    .line 82
    .line 83
    iput-object v1, p0, Lia1/d$a;->O:Ldagger/internal/h;

    .line 84
    .line 85
    new-instance v1, Lia1/d$a$h;

    .line 86
    .line 87
    invoke-direct {v1, p2}, Lia1/d$a$h;-><init>(LN91/e;)V

    .line 88
    .line 89
    .line 90
    iput-object v1, p0, Lia1/d$a;->P:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    iput-object v1, p0, Lia1/d$a;->Q:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static {v1}, Lorg/xbet/analytics/domain/scope/U;->a(LBc/a;)Lorg/xbet/analytics/domain/scope/U;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v1, p0, Lia1/d$a;->R:Ldagger/internal/h;

    .line 103
    .line 104
    new-instance v1, Lia1/d$a$r;

    .line 105
    .line 106
    invoke-direct {v1, v0}, Lia1/d$a$r;-><init>(Lak/a;)V

    .line 107
    .line 108
    .line 109
    iput-object v1, p0, Lia1/d$a;->S:Ldagger/internal/h;

    .line 110
    .line 111
    new-instance v0, Lia1/d$a$i;

    .line 112
    .line 113
    invoke-direct {v0, p2}, Lia1/d$a$i;-><init>(LN91/e;)V

    .line 114
    .line 115
    .line 116
    iput-object v0, p0, Lia1/d$a;->T:Ldagger/internal/h;

    .line 117
    .line 118
    iget-object p2, p0, Lia1/d$a;->h:Ldagger/internal/h;

    .line 119
    .line 120
    invoke-static {p2}, Lorg/xplatform/aggregator/impl/gifts/usecases/o;->a(LBc/a;)Lorg/xplatform/aggregator/impl/gifts/usecases/o;

    .line 121
    .line 122
    .line 123
    move-result-object p2

    .line 124
    iput-object p2, p0, Lia1/d$a;->U:Ldagger/internal/h;

    .line 125
    .line 126
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 127
    .line 128
    .line 129
    move-result-object p2

    .line 130
    iput-object p2, p0, Lia1/d$a;->V:Ldagger/internal/h;

    .line 131
    .line 132
    new-instance p2, Lia1/d$a$f;

    .line 133
    .line 134
    invoke-direct {p2, p1}, Lia1/d$a$f;-><init>(LQW0/c;)V

    .line 135
    .line 136
    .line 137
    iput-object p2, p0, Lia1/d$a;->W:Ldagger/internal/h;

    .line 138
    .line 139
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    iput-object p1, p0, Lia1/d$a;->X:Ldagger/internal/h;

    .line 144
    .line 145
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    iput-object p1, p0, Lia1/d$a;->Y:Ldagger/internal/h;

    .line 150
    .line 151
    invoke-static {p1}, Lp9/d;->a(LBc/a;)Lp9/d;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    iput-object p1, p0, Lia1/d$a;->Z:Ldagger/internal/h;

    .line 156
    .line 157
    invoke-static/range {p34 .. p34}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    iput-object p1, p0, Lia1/d$a;->a0:Ldagger/internal/h;

    .line 162
    .line 163
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 164
    .line 165
    .line 166
    move-result-object p1

    .line 167
    iput-object p1, p0, Lia1/d$a;->b0:Ldagger/internal/h;

    .line 168
    .line 169
    iget-object p1, p0, Lia1/d$a;->Q:Ldagger/internal/h;

    .line 170
    .line 171
    invoke-static {p1}, Lorg/xbet/analytics/domain/scope/J;->a(LBc/a;)Lorg/xbet/analytics/domain/scope/J;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    iput-object p1, p0, Lia1/d$a;->c0:Ldagger/internal/h;

    .line 176
    .line 177
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 178
    .line 179
    .line 180
    move-result-object p1

    .line 181
    iput-object p1, p0, Lia1/d$a;->d0:Ldagger/internal/h;

    .line 182
    .line 183
    invoke-static/range {p28 .. p28}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 184
    .line 185
    .line 186
    move-result-object p1

    .line 187
    iput-object p1, p0, Lia1/d$a;->e0:Ldagger/internal/h;

    .line 188
    .line 189
    return-void
.end method

.method public final e(LQW0/c;LN91/e;Lak/b;Lak/a;Lz81/a;LTZ0/a;LwX0/C;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LT91/a;Lf8/g;LfX/b;Lo9/a;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/ui_common/utils/M;Lia1/f;Lorg/xbet/ui_common/utils/internet/a;LP91/b;LxX0/a;Lc81/c;LSX0/c;Lorg/xbet/analytics/domain/b;LGg/a;LwX0/a;LHX0/e;Lau/a;Li8/j;LJT/a;LAR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LZR/a;LzX0/k;Lgk0/a;Leu/l;LUR/a;)V
    .locals 43

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p4

    .line 4
    .line 5
    invoke-static/range {p30 .. p30}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, Lia1/d$a;->f0:Ldagger/internal/h;

    .line 10
    .line 11
    new-instance v2, Lia1/d$a$q;

    .line 12
    .line 13
    invoke-direct {v2, v1}, Lia1/d$a$q;-><init>(Lak/a;)V

    .line 14
    .line 15
    .line 16
    iput-object v2, v0, Lia1/d$a;->g0:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-static/range {p32 .. p32}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    iput-object v2, v0, Lia1/d$a;->h0:Ldagger/internal/h;

    .line 23
    .line 24
    new-instance v2, Lia1/d$a$l;

    .line 25
    .line 26
    invoke-direct {v2, v1}, Lia1/d$a$l;-><init>(Lak/a;)V

    .line 27
    .line 28
    .line 29
    iput-object v2, v0, Lia1/d$a;->i0:Ldagger/internal/h;

    .line 30
    .line 31
    new-instance v1, Lia1/d$a$p;

    .line 32
    .line 33
    move-object/from16 v2, p5

    .line 34
    .line 35
    invoke-direct {v1, v2}, Lia1/d$a$p;-><init>(Lz81/a;)V

    .line 36
    .line 37
    .line 38
    iput-object v1, v0, Lia1/d$a;->j0:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object v3, v0, Lia1/d$a;->o:Ldagger/internal/h;

    .line 41
    .line 42
    iget-object v4, v0, Lia1/d$a;->q:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object v5, v0, Lia1/d$a;->r:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object v6, v0, Lia1/d$a;->t:Ldagger/internal/h;

    .line 47
    .line 48
    iget-object v7, v0, Lia1/d$a;->w:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object v8, v0, Lia1/d$a;->x:Ldagger/internal/h;

    .line 51
    .line 52
    iget-object v9, v0, Lia1/d$a;->y:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object v10, v0, Lia1/d$a;->z:Ldagger/internal/h;

    .line 55
    .line 56
    iget-object v11, v0, Lia1/d$a;->A:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object v12, v0, Lia1/d$a;->B:Ldagger/internal/h;

    .line 59
    .line 60
    iget-object v13, v0, Lia1/d$a;->C:Ldagger/internal/h;

    .line 61
    .line 62
    iget-object v14, v0, Lia1/d$a;->D:Ldagger/internal/h;

    .line 63
    .line 64
    iget-object v15, v0, Lia1/d$a;->E:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object v2, v0, Lia1/d$a;->N:Ldagger/internal/h;

    .line 67
    .line 68
    move-object/from16 v42, v1

    .line 69
    .line 70
    iget-object v1, v0, Lia1/d$a;->O:Ldagger/internal/h;

    .line 71
    .line 72
    move-object/from16 v17, v1

    .line 73
    .line 74
    iget-object v1, v0, Lia1/d$a;->P:Ldagger/internal/h;

    .line 75
    .line 76
    move-object/from16 v18, v1

    .line 77
    .line 78
    iget-object v1, v0, Lia1/d$a;->R:Ldagger/internal/h;

    .line 79
    .line 80
    move-object/from16 v19, v1

    .line 81
    .line 82
    iget-object v1, v0, Lia1/d$a;->S:Ldagger/internal/h;

    .line 83
    .line 84
    move-object/from16 v20, v1

    .line 85
    .line 86
    iget-object v1, v0, Lia1/d$a;->T:Ldagger/internal/h;

    .line 87
    .line 88
    move-object/from16 v21, v1

    .line 89
    .line 90
    iget-object v1, v0, Lia1/d$a;->U:Ldagger/internal/h;

    .line 91
    .line 92
    move-object/from16 v22, v1

    .line 93
    .line 94
    iget-object v1, v0, Lia1/d$a;->V:Ldagger/internal/h;

    .line 95
    .line 96
    move-object/from16 v23, v1

    .line 97
    .line 98
    iget-object v1, v0, Lia1/d$a;->H:Ldagger/internal/h;

    .line 99
    .line 100
    move-object/from16 v24, v1

    .line 101
    .line 102
    iget-object v1, v0, Lia1/d$a;->W:Ldagger/internal/h;

    .line 103
    .line 104
    move-object/from16 v25, v1

    .line 105
    .line 106
    iget-object v1, v0, Lia1/d$a;->X:Ldagger/internal/h;

    .line 107
    .line 108
    move-object/from16 v26, v1

    .line 109
    .line 110
    iget-object v1, v0, Lia1/d$a;->M:Ldagger/internal/h;

    .line 111
    .line 112
    move-object/from16 v27, v1

    .line 113
    .line 114
    iget-object v1, v0, Lia1/d$a;->k:Ldagger/internal/h;

    .line 115
    .line 116
    move-object/from16 v28, v1

    .line 117
    .line 118
    iget-object v1, v0, Lia1/d$a;->I:Ldagger/internal/h;

    .line 119
    .line 120
    move-object/from16 v29, v1

    .line 121
    .line 122
    iget-object v1, v0, Lia1/d$a;->L:Ldagger/internal/h;

    .line 123
    .line 124
    move-object/from16 v30, v1

    .line 125
    .line 126
    iget-object v1, v0, Lia1/d$a;->Z:Ldagger/internal/h;

    .line 127
    .line 128
    move-object/from16 v31, v1

    .line 129
    .line 130
    iget-object v1, v0, Lia1/d$a;->a0:Ldagger/internal/h;

    .line 131
    .line 132
    move-object/from16 v32, v1

    .line 133
    .line 134
    iget-object v1, v0, Lia1/d$a;->m:Ldagger/internal/h;

    .line 135
    .line 136
    move-object/from16 v33, v1

    .line 137
    .line 138
    iget-object v1, v0, Lia1/d$a;->b0:Ldagger/internal/h;

    .line 139
    .line 140
    move-object/from16 v34, v1

    .line 141
    .line 142
    iget-object v1, v0, Lia1/d$a;->c0:Ldagger/internal/h;

    .line 143
    .line 144
    move-object/from16 v35, v1

    .line 145
    .line 146
    iget-object v1, v0, Lia1/d$a;->d0:Ldagger/internal/h;

    .line 147
    .line 148
    move-object/from16 v36, v1

    .line 149
    .line 150
    iget-object v1, v0, Lia1/d$a;->e0:Ldagger/internal/h;

    .line 151
    .line 152
    move-object/from16 v37, v1

    .line 153
    .line 154
    iget-object v1, v0, Lia1/d$a;->f0:Ldagger/internal/h;

    .line 155
    .line 156
    move-object/from16 v38, v1

    .line 157
    .line 158
    iget-object v1, v0, Lia1/d$a;->g0:Ldagger/internal/h;

    .line 159
    .line 160
    move-object/from16 v39, v1

    .line 161
    .line 162
    iget-object v1, v0, Lia1/d$a;->h0:Ldagger/internal/h;

    .line 163
    .line 164
    move-object/from16 v40, v1

    .line 165
    .line 166
    iget-object v1, v0, Lia1/d$a;->i0:Ldagger/internal/h;

    .line 167
    .line 168
    move-object/from16 v41, v1

    .line 169
    .line 170
    move-object/from16 v16, v2

    .line 171
    .line 172
    invoke-static/range {v3 .. v42}, Lorg/xplatform/aggregator/impl/gifts/x;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/gifts/x;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    iput-object v1, v0, Lia1/d$a;->k0:Ldagger/internal/h;

    .line 177
    .line 178
    return-void
.end method

.method public final f(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, Lia1/d$a;->a:LTZ0/a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->a(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LTZ0/a;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lia1/d$a;->b:LzX0/k;

    .line 7
    .line 8
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->c(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;LzX0/k;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lia1/d$a;->c:Lak/b;

    .line 12
    .line 13
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lck/a;

    .line 22
    .line 23
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/t;->b(Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;Lck/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lia1/d$a;->i()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/s;->c(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0}, Lia1/d$a;->b()Lorg/xplatform/aggregator/impl/core/presentation/c;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/s;->a(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;Lorg/xplatform/aggregator/impl/core/presentation/c;)V

    .line 38
    .line 39
    .line 40
    iget-object v0, p0, Lia1/d$a;->f:LSX0/c;

    .line 41
    .line 42
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/s;->b(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;LSX0/c;)V

    .line 43
    .line 44
    .line 45
    return-object p1
.end method

.method public final g()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 2
    .line 3
    iget-object v1, p0, Lia1/d$a;->k0:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final h()Lorg/xbet/analytics/domain/scope/g0;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/analytics/domain/scope/g0;

    .line 2
    .line 3
    iget-object v1, p0, Lia1/d$a;->e:Lorg/xbet/analytics/domain/b;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lorg/xbet/analytics/domain/scope/g0;-><init>(Lorg/xbet/analytics/domain/b;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final i()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, Lia1/d$a;->g()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
