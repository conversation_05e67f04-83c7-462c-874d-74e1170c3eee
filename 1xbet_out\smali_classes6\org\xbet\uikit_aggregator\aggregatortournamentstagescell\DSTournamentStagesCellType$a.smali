.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0015\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;",
        "",
        "<init>",
        "()V",
        "",
        "style",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "b",
        "(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "",
        "value",
        "a",
        "(I)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(I)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 10
    .line 11
    if-nez p1, :cond_0

    .line 12
    .line 13
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->RADIAL:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 14
    .line 15
    :cond_0
    return-object p1
.end method

.method public final b(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const v1, -0x74423897

    .line 6
    .line 7
    .line 8
    if-eq v0, v1, :cond_4

    .line 9
    .line 10
    const v1, 0x3e2ebee

    .line 11
    .line 12
    .line 13
    if-eq v0, v1, :cond_2

    .line 14
    .line 15
    const v1, 0x348d5be1

    .line 16
    .line 17
    .line 18
    if-eq v0, v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const-string v0, "ProgressLine"

    .line 22
    .line 23
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    if-nez p1, :cond_1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->PROGRESS_LINE:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 31
    .line 32
    return-object p1

    .line 33
    :cond_2
    const-string v0, "Clock"

    .line 34
    .line 35
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    if-nez p1, :cond_3

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_3
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->CLOCK:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 43
    .line 44
    return-object p1

    .line 45
    :cond_4
    const-string v0, "Number"

    .line 46
    .line 47
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-nez p1, :cond_5

    .line 52
    .line 53
    :goto_0
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->RADIAL:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 54
    .line 55
    return-object p1

    .line 56
    :cond_5
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->NUMBER:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 57
    .line 58
    return-object p1
.end method
