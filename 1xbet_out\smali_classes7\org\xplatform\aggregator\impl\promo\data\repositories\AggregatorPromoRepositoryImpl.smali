.class public final Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lya1/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ&\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J.\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00102\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u0014H\u0096@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0016\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u0010H\u0096@\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0016\u0010\u001b\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u0010H\u0096@\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u0017\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001c\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ0\u0010%\u001a\u00020$2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010 \u001a\u00020\u000e2\u0006\u0010!\u001a\u00020\u00142\u0006\u0010#\u001a\u00020\"H\u0096@\u00a2\u0006\u0004\u0008%\u0010&J;\u0010.\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020-0,0+2\u0006\u0010!\u001a\u00020\u00142\u0006\u0010\'\u001a\u00020\u000c2\u0006\u0010)\u001a\u00020(2\u0006\u0010*\u001a\u00020(H\u0016\u00a2\u0006\u0004\u0008.\u0010/J;\u00103\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u0002020,0+2\u0006\u0010!\u001a\u00020\u00142\u0006\u00100\u001a\u00020\u00142\u0006\u0010\'\u001a\u00020\u000c2\u0006\u00101\u001a\u00020(H\u0016\u00a2\u0006\u0004\u00083\u00104J\u000f\u00105\u001a\u00020\u001dH\u0016\u00a2\u0006\u0004\u00085\u00106J \u00108\u001a\u0002072\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0004\u00088\u0010\u0013J(\u0010:\u001a\u0002072\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u00109\u001a\u00020(H\u0096@\u00a2\u0006\u0004\u0008:\u0010;J \u0010=\u001a\u00020<2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0004\u0008=\u0010\u0013J\u000f\u0010>\u001a\u00020\u001dH\u0016\u00a2\u0006\u0004\u0008>\u00106J \u0010?\u001a\u00020<2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0004\u0008?\u0010\u0013J\u0017\u0010A\u001a\u00020<2\u0006\u0010@\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008A\u0010BR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010CR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010DR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010ER\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010F\u00a8\u0006G"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;",
        "Lya1/a;",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/e;",
        "promoDataSource",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/a;",
        "aggregatorGiftsDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/g;",
        "promoRemoteDataSource",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;Lorg/xplatform/aggregator/impl/promo/data/datasources/a;Lc8/h;Lorg/xplatform/aggregator/impl/promo/data/datasources/g;)V",
        "",
        "token",
        "",
        "accountId",
        "",
        "Lxa1/a;",
        "k",
        "(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "country",
        "Lxa1/c;",
        "j",
        "(Ljava/lang/String;JILkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "c",
        "id",
        "",
        "i",
        "(I)V",
        "currentAccountId",
        "bonusId",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "statusBonus",
        "Lxa1/b;",
        "h",
        "(Ljava/lang/String;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "endPoint",
        "",
        "test",
        "hasAggregatorBrands",
        "Lkotlinx/coroutines/flow/e;",
        "Landroidx/paging/PagingData;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "g",
        "(ILjava/lang/String;ZZ)Lkotlinx/coroutines/flow/e;",
        "currentCountryId",
        "nightMode",
        "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
        "l",
        "(IILjava/lang/String;Z)Lkotlinx/coroutines/flow/e;",
        "f",
        "()V",
        "Lg81/e;",
        "m",
        "onlyActive",
        "b",
        "(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lg81/a;",
        "e",
        "d",
        "p",
        "activeBonus",
        "o",
        "(Lxa1/a;)Lg81/a;",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/e;",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/a;",
        "Lc8/h;",
        "Lorg/xplatform/aggregator/impl/promo/data/datasources/g;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xplatform/aggregator/impl/promo/data/datasources/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;Lorg/xplatform/aggregator/impl/promo/data/datasources/a;Lc8/h;Lorg/xplatform/aggregator/impl/promo/data/datasources/g;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/promo/data/datasources/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/promo/data/datasources/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/impl/promo/data/datasources/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->d:Lorg/xplatform/aggregator/impl/promo/data/datasources/g;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic n(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->p(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->b()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public b(Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p5, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p5

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v8, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p5}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p5, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x2

    .line 36
    const/4 v3, 0x1

    .line 37
    if-eqz v1, :cond_4

    .line 38
    .line 39
    if-eq v1, v3, :cond_2

    .line 40
    .line 41
    if-ne v1, v2, :cond_1

    .line 42
    .line 43
    invoke-static {p5}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto :goto_4

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1

    .line 55
    :cond_2
    iget-boolean p4, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->Z$0:Z

    .line 56
    .line 57
    iget-wide p2, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->J$0:J

    .line 58
    .line 59
    iget-object p1, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast p1, Ljava/lang/String;

    .line 62
    .line 63
    invoke-static {p5}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    :cond_3
    move-wide v3, p2

    .line 67
    move v5, p4

    .line 68
    goto :goto_2

    .line 69
    :cond_4
    invoke-static {p5}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    iput-object p1, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 73
    .line 74
    iput-wide p2, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->J$0:J

    .line 75
    .line 76
    iput-boolean p4, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->Z$0:Z

    .line 77
    .line 78
    iput v3, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->label:I

    .line 79
    .line 80
    invoke-virtual {p0, v8}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object p5

    .line 84
    if-ne p5, v0, :cond_3

    .line 85
    .line 86
    goto :goto_3

    .line 87
    :goto_2
    check-cast p5, Ljava/util/List;

    .line 88
    .line 89
    invoke-interface {p5}, Ljava/util/Collection;->isEmpty()Z

    .line 90
    .line 91
    .line 92
    move-result p2

    .line 93
    if-nez p2, :cond_5

    .line 94
    .line 95
    new-instance p1, Lg81/e;

    .line 96
    .line 97
    invoke-interface {p5}, Ljava/util/List;->size()I

    .line 98
    .line 99
    .line 100
    move-result p2

    .line 101
    invoke-direct {p1, p2}, Lg81/e;-><init>(I)V

    .line 102
    .line 103
    .line 104
    return-object p1

    .line 105
    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->d:Lorg/xplatform/aggregator/impl/promo/data/datasources/g;

    .line 106
    .line 107
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 108
    .line 109
    invoke-interface {p2}, Lc8/h;->f()I

    .line 110
    .line 111
    .line 112
    move-result v6

    .line 113
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 114
    .line 115
    invoke-interface {p2}, Lc8/h;->d()I

    .line 116
    .line 117
    .line 118
    move-result v7

    .line 119
    const/4 p2, 0x0

    .line 120
    iput-object p2, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->L$0:Ljava/lang/Object;

    .line 121
    .line 122
    iput v2, v8, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableFreeSpins$1;->label:I

    .line 123
    .line 124
    move-object v2, p1

    .line 125
    invoke-virtual/range {v1 .. v8}, Lorg/xplatform/aggregator/impl/promo/data/datasources/g;->e(Ljava/lang/String;JZIILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object p5

    .line 129
    if-ne p5, v0, :cond_6

    .line 130
    .line 131
    :goto_3
    return-object v0

    .line 132
    :cond_6
    :goto_4
    check-cast p5, Lua1/i;

    .line 133
    .line 134
    new-instance p1, Lg81/e;

    .line 135
    .line 136
    invoke-virtual {p5}, Le8/f;->a()Ljava/lang/Object;

    .line 137
    .line 138
    .line 139
    move-result-object p2

    .line 140
    check-cast p2, Lua1/i$a;

    .line 141
    .line 142
    invoke-static {p2}, Lua1/j;->a(Lua1/i$a;)I

    .line 143
    .line 144
    .line 145
    move-result p2

    .line 146
    invoke-direct {p1, p2}, Lg81/e;-><init>(I)V

    .line 147
    .line 148
    .line 149
    return-object p1
.end method

.method public c(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->c()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->a()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public e(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p4, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_5

    .line 45
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 46
    .line 47
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 48
    .line 49
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_2
    iget-wide p2, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->J$0:J

    .line 54
    .line 55
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->L$0:Ljava/lang/Object;

    .line 56
    .line 57
    check-cast p1, Ljava/lang/String;

    .line 58
    .line 59
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 60
    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_3
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->L$0:Ljava/lang/Object;

    .line 67
    .line 68
    iput-wide p2, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->J$0:J

    .line 69
    .line 70
    iput v4, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->label:I

    .line 71
    .line 72
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p4

    .line 76
    if-ne p4, v1, :cond_4

    .line 77
    .line 78
    goto :goto_4

    .line 79
    :cond_4
    :goto_1
    check-cast p4, Ljava/util/List;

    .line 80
    .line 81
    invoke-interface {p4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 82
    .line 83
    .line 84
    move-result-object p4

    .line 85
    :cond_5
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    .line 86
    .line 87
    .line 88
    move-result v2

    .line 89
    const/4 v4, 0x0

    .line 90
    if-eqz v2, :cond_6

    .line 91
    .line 92
    invoke-interface {p4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v2

    .line 96
    move-object v5, v2

    .line 97
    check-cast v5, Lxa1/a;

    .line 98
    .line 99
    invoke-virtual {v5}, Lxa1/a;->i()Lxa1/h;

    .line 100
    .line 101
    .line 102
    move-result-object v5

    .line 103
    invoke-virtual {v5}, Lxa1/h;->a()Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 104
    .line 105
    .line 106
    move-result-object v5

    .line 107
    sget-object v6, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 108
    .line 109
    if-ne v5, v6, :cond_5

    .line 110
    .line 111
    goto :goto_2

    .line 112
    :cond_6
    move-object v2, v4

    .line 113
    :goto_2
    check-cast v2, Lxa1/a;

    .line 114
    .line 115
    if-eqz v2, :cond_8

    .line 116
    .line 117
    invoke-virtual {p0, v2}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->o(Lxa1/a;)Lg81/a;

    .line 118
    .line 119
    .line 120
    move-result-object p4

    .line 121
    if-nez p4, :cond_7

    .line 122
    .line 123
    goto :goto_3

    .line 124
    :cond_7
    return-object p4

    .line 125
    :cond_8
    :goto_3
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->L$0:Ljava/lang/Object;

    .line 126
    .line 127
    iput v3, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonus$1;->label:I

    .line 128
    .line 129
    invoke-virtual {p0, p1, p2, p3, v0}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->p(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    move-result-object p4

    .line 133
    if-ne p4, v1, :cond_9

    .line 134
    .line 135
    :goto_4
    return-object v1

    .line 136
    :cond_9
    :goto_5
    check-cast p4, Lg81/a;

    .line 137
    .line 138
    return-object p4
.end method

.method public f()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->i()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public g(ILjava/lang/String;ZZ)Lkotlinx/coroutines/flow/e;
    .locals 6
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "ZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 2
    .line 3
    const/4 v4, 0x2

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    move v1, p1

    .line 7
    move v3, p3

    .line 8
    invoke-static/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->i(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;IIZILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    new-instance p3, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getGamesByBonusIdPaging$$inlined$map$1;

    .line 13
    .line 14
    invoke-direct {p3, p1, p2, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getGamesByBonusIdPaging$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Ljava/lang/String;Z)V

    .line 15
    .line 16
    .line 17
    return-object p3
.end method

.method public h(Ljava/lang/String;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lxa1/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p6, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p6

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p6}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p6, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    iget p4, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->I$0:I

    .line 41
    .line 42
    :try_start_0
    invoke-static {p6}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 43
    .line 44
    .line 45
    goto :goto_3

    .line 46
    :catchall_0
    move-exception v0

    .line 47
    :goto_2
    move-object p1, v0

    .line 48
    goto :goto_4

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p6}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    :try_start_1
    sget-object p6, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 61
    .line 62
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 63
    .line 64
    iput p4, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->I$0:I

    .line 65
    .line 66
    iput v2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$setStatusBonus$1;->label:I
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    .line 67
    .line 68
    move-object v2, p1

    .line 69
    move-wide v3, p2

    .line 70
    move v5, p4

    .line 71
    move-object v6, p5

    .line 72
    :try_start_2
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->n(Ljava/lang/String;JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p6
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 76
    if-ne p6, v0, :cond_3

    .line 77
    .line 78
    return-object v0

    .line 79
    :cond_3
    move p4, v5

    .line 80
    :goto_3
    :try_start_3
    check-cast p6, Lua1/e;

    .line 81
    .line 82
    invoke-static {p6}, Lja1/a;->a(Lua1/e;)Lxa1/b;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 90
    goto :goto_5

    .line 91
    :catchall_1
    move-exception v0

    .line 92
    move-object p1, v0

    .line 93
    move p4, v5

    .line 94
    goto :goto_4

    .line 95
    :catchall_2
    move-exception v0

    .line 96
    move v5, p4

    .line 97
    goto :goto_2

    .line 98
    :goto_4
    sget-object p2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 99
    .line 100
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    :goto_5
    invoke-static {p1}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    move-result p2

    .line 112
    if-eqz p2, :cond_4

    .line 113
    .line 114
    move-object p2, p1

    .line 115
    check-cast p2, Lxa1/b;

    .line 116
    .line 117
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 118
    .line 119
    invoke-virtual {p2}, Lxa1/b;->a()Ljava/util/List;

    .line 120
    .line 121
    .line 122
    move-result-object p2

    .line 123
    invoke-virtual {p3, p2}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->d(Ljava/util/List;)V

    .line 124
    .line 125
    .line 126
    :cond_4
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 127
    .line 128
    .line 129
    move-result-object p2

    .line 130
    if-eqz p2, :cond_7

    .line 131
    .line 132
    instance-of p1, p2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 133
    .line 134
    if-eqz p1, :cond_6

    .line 135
    .line 136
    new-instance p1, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;

    .line 137
    .line 138
    new-instance p3, Lwa1/a;

    .line 139
    .line 140
    invoke-virtual {p2}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 141
    .line 142
    .line 143
    move-result-object p5

    .line 144
    if-nez p5, :cond_5

    .line 145
    .line 146
    const-string p5, ""

    .line 147
    .line 148
    :cond_5
    check-cast p2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 149
    .line 150
    invoke-virtual {p2}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorCode()Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 151
    .line 152
    .line 153
    move-result-object p2

    .line 154
    invoke-interface {p2}, Lcom/xbet/onexcore/data/errors/IErrorCode;->getErrorCode()I

    .line 155
    .line 156
    .line 157
    move-result p2

    .line 158
    invoke-direct {p3, p4, p5, p2}, Lwa1/a;-><init>(ILjava/lang/String;I)V

    .line 159
    .line 160
    .line 161
    invoke-direct {p1, p3}, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;-><init>(Lwa1/a;)V

    .line 162
    .line 163
    .line 164
    throw p1

    .line 165
    :cond_6
    throw p2

    .line 166
    :cond_7
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 167
    .line 168
    .line 169
    return-object p1
.end method

.method public i(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->f(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public j(Ljava/lang/String;JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p5, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p5

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p5}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p5, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p5}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p5}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 56
    .line 57
    iget-object p5, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 58
    .line 59
    invoke-interface {p5}, Lc8/h;->d()I

    .line 60
    .line 61
    .line 62
    move-result v6

    .line 63
    iput v2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableFreeSpins$1;->label:I

    .line 64
    .line 65
    move-object v2, p1

    .line 66
    move-wide v3, p2

    .line 67
    move v5, p4

    .line 68
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->g(Ljava/lang/String;JIILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p5

    .line 72
    if-ne p5, v0, :cond_3

    .line 73
    .line 74
    return-object v0

    .line 75
    :cond_3
    :goto_2
    check-cast p5, Lua1/f;

    .line 76
    .line 77
    invoke-virtual {p5}, Le8/f;->a()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    check-cast p1, Lua1/f$b;

    .line 82
    .line 83
    invoke-static {p1}, Lsa1/b;->a(Lua1/f$b;)Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 88
    .line 89
    invoke-virtual {p2, p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->e(Ljava/util/List;)V

    .line 90
    .line 91
    .line 92
    return-object p1
.end method

.method public k(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lxa1/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 56
    .line 57
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 58
    .line 59
    invoke-interface {p4}, Lc8/h;->d()I

    .line 60
    .line 61
    .line 62
    move-result v5

    .line 63
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 64
    .line 65
    invoke-interface {p4}, Lc8/h;->c()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    iput v2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getAvailableBonuses$1;->label:I

    .line 70
    .line 71
    move-object v2, p1

    .line 72
    move-wide v3, p2

    .line 73
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->f(Ljava/lang/String;JILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p4

    .line 77
    if-ne p4, v0, :cond_3

    .line 78
    .line 79
    return-object v0

    .line 80
    :cond_3
    :goto_2
    check-cast p4, Lua1/e;

    .line 81
    .line 82
    invoke-static {p4}, Lja1/a;->a(Lua1/e;)Lxa1/b;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-virtual {p1}, Lxa1/b;->a()Ljava/util/List;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->b:Lorg/xplatform/aggregator/impl/promo/data/datasources/a;

    .line 91
    .line 92
    invoke-virtual {p2, p1}, Lorg/xplatform/aggregator/impl/promo/data/datasources/a;->d(Ljava/util/List;)V

    .line 93
    .line 94
    .line 95
    return-object p1
.end method

.method public l(IILjava/lang/String;Z)Lkotlinx/coroutines/flow/e;
    .locals 7
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/lang/String;",
            "Z)",
            "Lkotlinx/coroutines/flow/e<",
            "Landroidx/paging/PagingData<",
            "Lorg/xplatform/aggregator/impl/promo/data/models/AggregatorProduct;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a:Lorg/xplatform/aggregator/impl/promo/data/datasources/e;

    .line 2
    .line 3
    const/16 v5, 0xa

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v4, 0x0

    .line 8
    move v1, p1

    .line 9
    move v3, p2

    .line 10
    invoke-static/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/promo/data/datasources/e;->k(Lorg/xplatform/aggregator/impl/promo/data/datasources/e;ILjava/lang/String;IIILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    new-instance p2, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$$inlined$map$1;

    .line 15
    .line 16
    invoke-direct {p2, p1, p3, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getProductsByBonusIdPaging$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Ljava/lang/String;Z)V

    .line 17
    .line 18
    .line 19
    return-object p2
.end method

.method public m(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x2

    .line 36
    const/4 v3, 0x1

    .line 37
    if-eqz v1, :cond_4

    .line 38
    .line 39
    if-eq v1, v3, :cond_2

    .line 40
    .line 41
    if-ne v1, v2, :cond_1

    .line 42
    .line 43
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto :goto_4

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1

    .line 55
    :cond_2
    iget-wide p2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->J$0:J

    .line 56
    .line 57
    iget-object p1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast p1, Ljava/lang/String;

    .line 60
    .line 61
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    :cond_3
    move-wide v3, p2

    .line 65
    goto :goto_2

    .line 66
    :cond_4
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    iput-object p1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 70
    .line 71
    iput-wide p2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->J$0:J

    .line 72
    .line 73
    iput v3, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->label:I

    .line 74
    .line 75
    invoke-virtual {p0, v7}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object p4

    .line 79
    if-ne p4, v0, :cond_3

    .line 80
    .line 81
    goto :goto_3

    .line 82
    :goto_2
    check-cast p4, Ljava/util/List;

    .line 83
    .line 84
    invoke-interface {p4}, Ljava/util/Collection;->isEmpty()Z

    .line 85
    .line 86
    .line 87
    move-result p2

    .line 88
    if-nez p2, :cond_5

    .line 89
    .line 90
    new-instance p1, Lg81/e;

    .line 91
    .line 92
    invoke-interface {p4}, Ljava/util/List;->size()I

    .line 93
    .line 94
    .line 95
    move-result p2

    .line 96
    invoke-direct {p1, p2}, Lg81/e;-><init>(I)V

    .line 97
    .line 98
    .line 99
    return-object p1

    .line 100
    :cond_5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->d:Lorg/xplatform/aggregator/impl/promo/data/datasources/g;

    .line 101
    .line 102
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 103
    .line 104
    invoke-interface {p2}, Lc8/h;->d()I

    .line 105
    .line 106
    .line 107
    move-result v5

    .line 108
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 109
    .line 110
    invoke-interface {p2}, Lc8/h;->c()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v6

    .line 114
    const/4 p2, 0x0

    .line 115
    iput-object p2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    iput v2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getCountAvailableBonuses$1;->label:I

    .line 118
    .line 119
    move-object v2, p1

    .line 120
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/promo/data/datasources/g;->d(Ljava/lang/String;JILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object p4

    .line 124
    if-ne p4, v0, :cond_6

    .line 125
    .line 126
    :goto_3
    return-object v0

    .line 127
    :cond_6
    :goto_4
    check-cast p4, Lua1/g;

    .line 128
    .line 129
    invoke-static {p4}, Lsa1/c;->a(Lua1/g;)Lg81/e;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    return-object p1
.end method

.method public final o(Lxa1/a;)Lg81/a;
    .locals 6

    .line 1
    new-instance v0, Lg81/a;

    .line 2
    .line 3
    invoke-virtual {p1}, Lxa1/a;->h()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    int-to-long v1, v1

    .line 8
    invoke-virtual {p1}, Lxa1/a;->c()D

    .line 9
    .line 10
    .line 11
    move-result-wide v3

    .line 12
    invoke-virtual {p1}, Lxa1/a;->f()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v5

    .line 16
    invoke-direct/range {v0 .. v5}, Lg81/a;-><init>(JDLjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-object v0
.end method

.method public final p(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p4, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p4

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v7, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p4}, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;-><init>(Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p4, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p4}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->d:Lorg/xplatform/aggregator/impl/promo/data/datasources/g;

    .line 56
    .line 57
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 58
    .line 59
    invoke-interface {p4}, Lc8/h;->d()I

    .line 60
    .line 61
    .line 62
    move-result v5

    .line 63
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl;->c:Lc8/h;

    .line 64
    .line 65
    invoke-interface {p4}, Lc8/h;->c()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    iput v2, v7, Lorg/xplatform/aggregator/impl/promo/data/repositories/AggregatorPromoRepositoryImpl$getActiveUserBonusRemoteFlow$1;->label:I

    .line 70
    .line 71
    move-object v2, p1

    .line 72
    move-wide v3, p2

    .line 73
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/promo/data/datasources/g;->c(Ljava/lang/String;JILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p4

    .line 77
    if-ne p4, v0, :cond_3

    .line 78
    .line 79
    return-object v0

    .line 80
    :cond_3
    :goto_2
    check-cast p4, Lua1/a;

    .line 81
    .line 82
    invoke-static {p4}, Lsa1/a;->a(Lua1/a;)Lg81/a;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    return-object p1
.end method
