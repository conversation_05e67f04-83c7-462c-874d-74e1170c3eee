.class public final synthetic LkZ0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

.field public final synthetic b:Landroid/content/Context;

.field public final synthetic c:Landroid/util/AttributeSet;

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LkZ0/e;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    iput-object p2, p0, LkZ0/e;->b:Landroid/content/Context;

    iput-object p3, p0, LkZ0/e;->c:Landroid/util/AttributeSet;

    iput p4, p0, LkZ0/e;->d:I

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, LkZ0/e;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;

    iget-object v1, p0, LkZ0/e;->b:Landroid/content/Context;

    iget-object v2, p0, LkZ0/e;->c:Landroid/util/AttributeSet;

    iget v3, p0, LkZ0/e;->d:I

    invoke-static {v0, v1, v2, v3}, Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;->e(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView;Landroid/content/Context;Landroid/util/AttributeSet;I)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
