.class public final LQK0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQK0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQK0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LQK0/a$b$a;,
        LQK0/a$b$c;,
        LQK0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LQK0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LNK0/b;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/b;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/races/data/repositories/RacesStatisticRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTK0/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTK0/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LGL0/b;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LaN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/races/presentation/viewmodels/RacesStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/races/presentation/viewmodels/RaceStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LQK0/a$b;->a:LQK0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p18}, LQK0/a$b;->c(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;LQK0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p18}, LQK0/a$b;-><init>(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LQK0/a$b;->e(Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;)Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LQK0/a$b;->d(Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;)Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;LEN0/f;LGL0/a;LwX0/c;LSX0/c;Lorg/xbet/ui_common/utils/M;Lc8/b;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Li8/m;LHX0/e;Ljava/lang/Long;LSX0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lc8/h;LTn/a;LaN0/a;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    new-instance v1, LQK0/a$b$a;

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    invoke-direct {v1, v2}, LQK0/a$b$a;-><init>(LQW0/c;)V

    .line 8
    .line 9
    .line 10
    iput-object v1, v0, LQK0/a$b;->b:Ldagger/internal/h;

    .line 11
    .line 12
    invoke-static/range {p8 .. p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    iput-object v1, v0, LQK0/a$b;->c:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-static {v1}, LNK0/c;->a(LBc/a;)LNK0/c;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    iput-object v1, v0, LQK0/a$b;->d:Ldagger/internal/h;

    .line 23
    .line 24
    invoke-static/range {p7 .. p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iput-object v1, v0, LQK0/a$b;->e:Ldagger/internal/h;

    .line 29
    .line 30
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    iput-object v1, v0, LQK0/a$b;->f:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object v2, v0, LQK0/a$b;->b:Ldagger/internal/h;

    .line 37
    .line 38
    iget-object v3, v0, LQK0/a$b;->d:Ldagger/internal/h;

    .line 39
    .line 40
    iget-object v4, v0, LQK0/a$b;->e:Ldagger/internal/h;

    .line 41
    .line 42
    invoke-static {v2, v3, v4, v1}, Lorg/xbet/statistic/races/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/races/data/repositories/a;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    iput-object v1, v0, LQK0/a$b;->g:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {v1}, LTK0/d;->a(LBc/a;)LTK0/d;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    iput-object v1, v0, LQK0/a$b;->h:Ldagger/internal/h;

    .line 53
    .line 54
    iget-object v1, v0, LQK0/a$b;->g:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {v1}, LTK0/b;->a(LBc/a;)LTK0/b;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    iput-object v1, v0, LQK0/a$b;->i:Ldagger/internal/h;

    .line 61
    .line 62
    invoke-static/range {p9 .. p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    iput-object v1, v0, LQK0/a$b;->j:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static/range {p13 .. p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    iput-object v1, v0, LQK0/a$b;->k:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static/range {p10 .. p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    iput-object v1, v0, LQK0/a$b;->l:Ldagger/internal/h;

    .line 79
    .line 80
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    iput-object v1, v0, LQK0/a$b;->m:Ldagger/internal/h;

    .line 85
    .line 86
    invoke-static/range {p4 .. p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    iput-object v1, v0, LQK0/a$b;->n:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    iput-object v1, v0, LQK0/a$b;->o:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    iput-object v1, v0, LQK0/a$b;->p:Ldagger/internal/h;

    .line 103
    .line 104
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    iput-object v1, v0, LQK0/a$b;->q:Ldagger/internal/h;

    .line 109
    .line 110
    new-instance v1, LQK0/a$b$c;

    .line 111
    .line 112
    move-object/from16 v2, p2

    .line 113
    .line 114
    invoke-direct {v1, v2}, LQK0/a$b$c;-><init>(LEN0/f;)V

    .line 115
    .line 116
    .line 117
    iput-object v1, v0, LQK0/a$b;->r:Ldagger/internal/h;

    .line 118
    .line 119
    invoke-static {v1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 120
    .line 121
    .line 122
    move-result-object v1

    .line 123
    iput-object v1, v0, LQK0/a$b;->s:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    iput-object v1, v0, LQK0/a$b;->t:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object v2, v0, LQK0/a$b;->b:Ldagger/internal/h;

    .line 132
    .line 133
    invoke-static {v2, v1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 134
    .line 135
    .line 136
    move-result-object v1

    .line 137
    iput-object v1, v0, LQK0/a$b;->u:Ldagger/internal/h;

    .line 138
    .line 139
    new-instance v1, LQK0/a$b$b;

    .line 140
    .line 141
    move-object/from16 v2, p3

    .line 142
    .line 143
    invoke-direct {v1, v2}, LQK0/a$b$b;-><init>(LGL0/a;)V

    .line 144
    .line 145
    .line 146
    iput-object v1, v0, LQK0/a$b;->v:Ldagger/internal/h;

    .line 147
    .line 148
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 149
    .line 150
    .line 151
    move-result-object v15

    .line 152
    iput-object v15, v0, LQK0/a$b;->w:Ldagger/internal/h;

    .line 153
    .line 154
    iget-object v2, v0, LQK0/a$b;->h:Ldagger/internal/h;

    .line 155
    .line 156
    iget-object v3, v0, LQK0/a$b;->i:Ldagger/internal/h;

    .line 157
    .line 158
    iget-object v4, v0, LQK0/a$b;->j:Ldagger/internal/h;

    .line 159
    .line 160
    iget-object v5, v0, LQK0/a$b;->k:Ldagger/internal/h;

    .line 161
    .line 162
    iget-object v6, v0, LQK0/a$b;->l:Ldagger/internal/h;

    .line 163
    .line 164
    iget-object v7, v0, LQK0/a$b;->m:Ldagger/internal/h;

    .line 165
    .line 166
    iget-object v8, v0, LQK0/a$b;->n:Ldagger/internal/h;

    .line 167
    .line 168
    iget-object v9, v0, LQK0/a$b;->o:Ldagger/internal/h;

    .line 169
    .line 170
    iget-object v10, v0, LQK0/a$b;->p:Ldagger/internal/h;

    .line 171
    .line 172
    iget-object v11, v0, LQK0/a$b;->q:Ldagger/internal/h;

    .line 173
    .line 174
    iget-object v12, v0, LQK0/a$b;->s:Ldagger/internal/h;

    .line 175
    .line 176
    iget-object v13, v0, LQK0/a$b;->u:Ldagger/internal/h;

    .line 177
    .line 178
    iget-object v14, v0, LQK0/a$b;->v:Ldagger/internal/h;

    .line 179
    .line 180
    invoke-static/range {v2 .. v15}, Lorg/xbet/statistic/races/presentation/viewmodels/m;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/races/presentation/viewmodels/m;

    .line 181
    .line 182
    .line 183
    move-result-object v1

    .line 184
    iput-object v1, v0, LQK0/a$b;->x:Ldagger/internal/h;

    .line 185
    .line 186
    invoke-static/range {p5 .. p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 187
    .line 188
    .line 189
    move-result-object v1

    .line 190
    iput-object v1, v0, LQK0/a$b;->y:Ldagger/internal/h;

    .line 191
    .line 192
    iget-object v2, v0, LQK0/a$b;->j:Ldagger/internal/h;

    .line 193
    .line 194
    iget-object v3, v0, LQK0/a$b;->k:Ldagger/internal/h;

    .line 195
    .line 196
    iget-object v4, v0, LQK0/a$b;->h:Ldagger/internal/h;

    .line 197
    .line 198
    iget-object v5, v0, LQK0/a$b;->b:Ldagger/internal/h;

    .line 199
    .line 200
    iget-object v6, v0, LQK0/a$b;->i:Ldagger/internal/h;

    .line 201
    .line 202
    iget-object v7, v0, LQK0/a$b;->l:Ldagger/internal/h;

    .line 203
    .line 204
    iget-object v8, v0, LQK0/a$b;->n:Ldagger/internal/h;

    .line 205
    .line 206
    iget-object v9, v0, LQK0/a$b;->o:Ldagger/internal/h;

    .line 207
    .line 208
    iget-object v10, v0, LQK0/a$b;->p:Ldagger/internal/h;

    .line 209
    .line 210
    iget-object v11, v0, LQK0/a$b;->s:Ldagger/internal/h;

    .line 211
    .line 212
    iget-object v12, v0, LQK0/a$b;->u:Ldagger/internal/h;

    .line 213
    .line 214
    iget-object v13, v0, LQK0/a$b;->v:Ldagger/internal/h;

    .line 215
    .line 216
    iget-object v14, v0, LQK0/a$b;->w:Ldagger/internal/h;

    .line 217
    .line 218
    move-object/from16 p8, v1

    .line 219
    .line 220
    move-object/from16 p1, v2

    .line 221
    .line 222
    move-object/from16 p2, v3

    .line 223
    .line 224
    move-object/from16 p3, v4

    .line 225
    .line 226
    move-object/from16 p4, v5

    .line 227
    .line 228
    move-object/from16 p5, v6

    .line 229
    .line 230
    move-object/from16 p6, v7

    .line 231
    .line 232
    move-object/from16 p7, v8

    .line 233
    .line 234
    move-object/from16 p9, v9

    .line 235
    .line 236
    move-object/from16 p10, v10

    .line 237
    .line 238
    move-object/from16 p11, v11

    .line 239
    .line 240
    move-object/from16 p12, v12

    .line 241
    .line 242
    move-object/from16 p13, v13

    .line 243
    .line 244
    move-object/from16 p14, v14

    .line 245
    .line 246
    invoke-static/range {p1 .. p14}, Lorg/xbet/statistic/races/presentation/viewmodels/l;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/races/presentation/viewmodels/l;

    .line 247
    .line 248
    .line 249
    move-result-object v1

    .line 250
    iput-object v1, v0, LQK0/a$b;->z:Ldagger/internal/h;

    .line 251
    .line 252
    return-void
.end method

.method public final d(Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;)Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LQK0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/races/presentation/fragments/c;->a(Lorg/xbet/statistic/races/presentation/fragments/RaceStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e(Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;)Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LQK0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/races/presentation/fragments/h;->a(Lorg/xbet/statistic/races/presentation/fragments/RacesStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/races/presentation/viewmodels/RacesStatisticViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LQK0/a$b;->x:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/races/presentation/viewmodels/RaceStatisticViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LQK0/a$b;->z:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LQK0/a$b;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
