.class final Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.promo.domain.usecases.GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1"
    f = "GetPromoGiftsUseCase.kt"
    l = {
        0x20,
        0x57
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lg81/e;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lg81/e;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lg81/e;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $accountId:J

.field final synthetic $onlyActive:Z

.field final synthetic $token:Ljava/lang/String;

.field I$0:I

.field I$1:I

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;",
            "Ljava/lang/String;",
            "JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$token:Ljava/lang/String;

    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$accountId:J

    iput-boolean p5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$onlyActive:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p6}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$token:Ljava/lang/String;

    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$accountId:J

    iget-boolean v5, p0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$onlyActive:Z

    move-object v6, p2

    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;-><init>(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JZLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg81/e;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v6, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v7

    .line 7
    iget v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->label:I

    .line 8
    .line 9
    const/4 v8, 0x2

    .line 10
    const/4 v9, 0x0

    .line 11
    const/4 v10, 0x1

    .line 12
    if-eqz v0, :cond_2

    .line 13
    .line 14
    if-eq v0, v10, :cond_1

    .line 15
    .line 16
    if-ne v0, v8, :cond_0

    .line 17
    .line 18
    iget v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$1:I

    .line 19
    .line 20
    iget-boolean v1, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->Z$0:Z

    .line 21
    .line 22
    iget-wide v2, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->J$0:J

    .line 23
    .line 24
    iget v4, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$0:I

    .line 25
    .line 26
    iget-object v5, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$2:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v5, Ljava/lang/String;

    .line 29
    .line 30
    iget-object v11, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$1:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v11, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 33
    .line 34
    iget-object v12, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast v12, Lkotlinx/coroutines/N;

    .line 37
    .line 38
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    move-object v13, v12

    .line 42
    move v12, v4

    .line 43
    move-wide v3, v2

    .line 44
    move-object v2, v5

    .line 45
    move v5, v1

    .line 46
    move-object v1, v11

    .line 47
    move v11, v0

    .line 48
    goto :goto_1

    .line 49
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw v0

    .line 57
    :cond_1
    iget v1, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$1:I

    .line 58
    .line 59
    iget-boolean v2, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->Z$0:Z

    .line 60
    .line 61
    iget-wide v3, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->J$0:J

    .line 62
    .line 63
    iget v5, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$0:I

    .line 64
    .line 65
    iget-object v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$2:Ljava/lang/Object;

    .line 66
    .line 67
    move-object v11, v0

    .line 68
    check-cast v11, Ljava/lang/String;

    .line 69
    .line 70
    iget-object v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$1:Ljava/lang/Object;

    .line 71
    .line 72
    move-object v12, v0

    .line 73
    check-cast v12, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 74
    .line 75
    iget-object v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    .line 76
    .line 77
    move-object v13, v0

    .line 78
    check-cast v13, Lkotlinx/coroutines/N;

    .line 79
    .line 80
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 81
    .line 82
    .line 83
    move-object/from16 v0, p1

    .line 84
    .line 85
    goto :goto_2

    .line 86
    :catchall_0
    move-exception v0

    .line 87
    move/from16 v16, v5

    .line 88
    .line 89
    move v5, v2

    .line 90
    :goto_0
    move-wide v2, v3

    .line 91
    move/from16 v4, v16

    .line 92
    .line 93
    goto :goto_3

    .line 94
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 95
    .line 96
    .line 97
    iget-object v0, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    .line 98
    .line 99
    check-cast v0, Lkotlinx/coroutines/N;

    .line 100
    .line 101
    iget-object v1, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->this$0:Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;

    .line 102
    .line 103
    iget-object v2, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$token:Ljava/lang/String;

    .line 104
    .line 105
    iget-wide v3, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$accountId:J

    .line 106
    .line 107
    iget-boolean v5, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->$onlyActive:Z

    .line 108
    .line 109
    move-object v13, v0

    .line 110
    const/4 v11, 0x0

    .line 111
    const/4 v12, 0x0

    .line 112
    :goto_1
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 113
    .line 114
    iput-object v13, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    .line 115
    .line 116
    iput-object v1, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$1:Ljava/lang/Object;

    .line 117
    .line 118
    iput-object v2, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$2:Ljava/lang/Object;

    .line 119
    .line 120
    iput v12, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$0:I

    .line 121
    .line 122
    iput-wide v3, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->J$0:J

    .line 123
    .line 124
    iput-boolean v5, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->Z$0:Z

    .line 125
    .line 126
    iput v11, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$1:I

    .line 127
    .line 128
    iput v10, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->label:I

    .line 129
    .line 130
    invoke-static/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;->c(Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase;Ljava/lang/String;JZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 134
    if-ne v0, v7, :cond_3

    .line 135
    .line 136
    goto/16 :goto_7

    .line 137
    .line 138
    :cond_3
    move/from16 v16, v12

    .line 139
    .line 140
    move-object v12, v1

    .line 141
    move v1, v11

    .line 142
    move-object v11, v2

    .line 143
    move v2, v5

    .line 144
    move/from16 v5, v16

    .line 145
    .line 146
    :goto_2
    :try_start_2
    check-cast v0, Lg81/e;

    .line 147
    .line 148
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 152
    goto/16 :goto_9

    .line 153
    .line 154
    :catchall_1
    move-exception v0

    .line 155
    move/from16 v16, v12

    .line 156
    .line 157
    move-object v12, v1

    .line 158
    move v1, v11

    .line 159
    move-object v11, v2

    .line 160
    goto :goto_0

    .line 161
    :goto_3
    if-eqz v4, :cond_4

    .line 162
    .line 163
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 164
    .line 165
    if-eqz v14, :cond_4

    .line 166
    .line 167
    move-object v14, v0

    .line 168
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 169
    .line 170
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 171
    .line 172
    .line 173
    move-result v14

    .line 174
    if-eqz v14, :cond_4

    .line 175
    .line 176
    const/4 v14, 0x1

    .line 177
    goto :goto_4

    .line 178
    :cond_4
    const/4 v14, 0x0

    .line 179
    :goto_4
    instance-of v15, v0, Ljava/util/concurrent/CancellationException;

    .line 180
    .line 181
    if-nez v15, :cond_b

    .line 182
    .line 183
    instance-of v15, v0, Ljava/net/ConnectException;

    .line 184
    .line 185
    if-nez v15, :cond_b

    .line 186
    .line 187
    if-nez v14, :cond_b

    .line 188
    .line 189
    instance-of v14, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 190
    .line 191
    if-eqz v14, :cond_7

    .line 192
    .line 193
    move-object v14, v0

    .line 194
    check-cast v14, Lcom/xbet/onexcore/data/model/ServerException;

    .line 195
    .line 196
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 197
    .line 198
    .line 199
    move-result v15

    .line 200
    if-nez v15, :cond_6

    .line 201
    .line 202
    invoke-virtual {v14}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 203
    .line 204
    .line 205
    move-result v14

    .line 206
    if-eqz v14, :cond_5

    .line 207
    .line 208
    goto :goto_5

    .line 209
    :cond_5
    const/4 v14, 0x0

    .line 210
    goto :goto_6

    .line 211
    :cond_6
    :goto_5
    const/4 v14, 0x1

    .line 212
    goto :goto_6

    .line 213
    :cond_7
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 214
    .line 215
    .line 216
    move-result v14

    .line 217
    if-nez v14, :cond_5

    .line 218
    .line 219
    goto :goto_5

    .line 220
    :goto_6
    add-int/2addr v1, v10

    .line 221
    const/4 v15, 0x3

    .line 222
    if-gt v1, v15, :cond_a

    .line 223
    .line 224
    if-eqz v14, :cond_8

    .line 225
    .line 226
    goto :goto_8

    .line 227
    :cond_8
    new-instance v14, Ljava/lang/StringBuilder;

    .line 228
    .line 229
    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    .line 230
    .line 231
    .line 232
    const-string v15, "error ("

    .line 233
    .line 234
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 235
    .line 236
    .line 237
    invoke-virtual {v14, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 238
    .line 239
    .line 240
    const-string v15, "): "

    .line 241
    .line 242
    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 243
    .line 244
    .line 245
    invoke-virtual {v14, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 246
    .line 247
    .line 248
    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 249
    .line 250
    .line 251
    move-result-object v0

    .line 252
    sget-object v14, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 253
    .line 254
    invoke-virtual {v14, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 255
    .line 256
    .line 257
    iput-object v13, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$0:Ljava/lang/Object;

    .line 258
    .line 259
    iput-object v12, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$1:Ljava/lang/Object;

    .line 260
    .line 261
    iput-object v11, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->L$2:Ljava/lang/Object;

    .line 262
    .line 263
    iput v4, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$0:I

    .line 264
    .line 265
    iput-wide v2, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->J$0:J

    .line 266
    .line 267
    iput-boolean v5, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->Z$0:Z

    .line 268
    .line 269
    iput v1, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->I$1:I

    .line 270
    .line 271
    iput v8, v6, Lorg/xplatform/aggregator/impl/promo/domain/usecases/GetPromoGiftsUseCase$invoke$2$1$freeSpinsDeferred$1;->label:I

    .line 272
    .line 273
    const-wide/16 v14, 0xbb8

    .line 274
    .line 275
    invoke-static {v14, v15, v6}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 276
    .line 277
    .line 278
    move-result-object v0

    .line 279
    if-ne v0, v7, :cond_9

    .line 280
    .line 281
    :goto_7
    return-object v7

    .line 282
    :cond_9
    move-object/from16 v16, v11

    .line 283
    .line 284
    move v11, v1

    .line 285
    move-object v1, v12

    .line 286
    move v12, v4

    .line 287
    move-wide v3, v2

    .line 288
    move-object/from16 v2, v16

    .line 289
    .line 290
    goto/16 :goto_1

    .line 291
    .line 292
    :cond_a
    :goto_8
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 293
    .line 294
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 299
    .line 300
    .line 301
    move-result-object v0

    .line 302
    :goto_9
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 303
    .line 304
    .line 305
    return-object v0

    .line 306
    :cond_b
    throw v0
.end method
