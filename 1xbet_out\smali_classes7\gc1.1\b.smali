.class public final Lgc1/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a;\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0008\u001a\u00020\u0007H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/Game;",
        "",
        "favorite",
        "authorized",
        "LHX0/e;",
        "resourceManager",
        "virtual",
        "Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;",
        "gameCardCollectionStyle",
        "LN21/k;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/Game;ZZLHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)LN21/k;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/Game;ZZLHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)LN21/k;
    .locals 17
    .param p0    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move/from16 v0, p1

    .line 2
    .line 3
    move-object/from16 v1, p3

    .line 4
    .line 5
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getPromo()Z

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    const/4 v3, 0x0

    .line 10
    if-eqz v2, :cond_0

    .line 11
    .line 12
    sget v2, Lpb/k;->casino_promo_game_label:I

    .line 13
    .line 14
    new-array v4, v3, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {v1, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    :goto_0
    move-object v9, v1

    .line 21
    goto :goto_1

    .line 22
    :cond_0
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getNewGame()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_1

    .line 27
    .line 28
    sget v2, Lpb/k;->casino_new_game_label:I

    .line 29
    .line 30
    new-array v4, v3, [Ljava/lang/Object;

    .line 31
    .line 32
    invoke-interface {v1, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    goto :goto_0

    .line 37
    :cond_1
    const-string v1, ""

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :goto_1
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getPromo()Z

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getNewGame()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    move-object/from16 v4, p5

    .line 49
    .line 50
    invoke-static {v4, v1, v2}, Ls81/b;->a(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;ZZ)I

    .line 51
    .line 52
    .line 53
    move-result v13

    .line 54
    if-eqz p4, :cond_2

    .line 55
    .line 56
    sget v1, Lpb/g;->ic_games_placeholder:I

    .line 57
    .line 58
    goto :goto_2

    .line 59
    :cond_2
    sget v1, Lpb/g;->ic_aggregator_placeholder:I

    .line 60
    .line 61
    :goto_2
    new-instance v4, LN21/k;

    .line 62
    .line 63
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v5

    .line 67
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getName()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v7

    .line 71
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getProductName()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v8

    .line 75
    new-instance v10, LN21/m;

    .line 76
    .line 77
    if-nez p2, :cond_3

    .line 78
    .line 79
    goto :goto_3

    .line 80
    :cond_3
    if-eqz v0, :cond_4

    .line 81
    .line 82
    sget v3, Lpb/g;->ic_favorites_slots_checked:I

    .line 83
    .line 84
    goto :goto_3

    .line 85
    :cond_4
    sget v3, Lpb/g;->ic_favorites_slots_unchecked:I

    .line 86
    .line 87
    :goto_3
    invoke-direct {v10, v3, v0}, LN21/m;-><init>(IZ)V

    .line 88
    .line 89
    .line 90
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/api/model/Game;->getLogoUrl()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-static {v0}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-static {v0}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 99
    .line 100
    .line 101
    move-result-object v11

    .line 102
    invoke-static {v1}, LL11/c$c;->d(I)I

    .line 103
    .line 104
    .line 105
    move-result v0

    .line 106
    invoke-static {v0}, LL11/c$c;->c(I)LL11/c$c;

    .line 107
    .line 108
    .line 109
    move-result-object v12

    .line 110
    const/16 v15, 0x100

    .line 111
    .line 112
    const/16 v16, 0x0

    .line 113
    .line 114
    const/4 v14, 0x0

    .line 115
    invoke-direct/range {v4 .. v16}, LN21/k;-><init>(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LN21/m;LL11/c;LL11/c;ILjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 116
    .line 117
    .line 118
    return-object v4
.end method
