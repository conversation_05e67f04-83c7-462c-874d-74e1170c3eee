.class public final synthetic LsS0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroid/content/Context;

.field public final synthetic b:Lcom/google/gson/Gson;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;Lcom/google/gson/Gson;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LsS0/g;->a:Landroid/content/Context;

    iput-object p2, p0, LsS0/g;->b:Lcom/google/gson/Gson;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LsS0/g;->a:Landroid/content/Context;

    iget-object v1, p0, LsS0/g;->b:Lcom/google/gson/Gson;

    invoke-static {v0, v1}, LsS0/i;->a(Landroid/content/Context;Lcom/google/gson/Gson;)LuS0/j;

    move-result-object v0

    return-object v0
.end method
