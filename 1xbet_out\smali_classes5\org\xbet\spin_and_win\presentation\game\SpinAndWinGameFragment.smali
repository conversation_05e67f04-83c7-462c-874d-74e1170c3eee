.class public final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0097\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0006\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0008\u0008*\u0001^\u0018\u0000 c2\u00020\u0001:\u0001dB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001d\u0010\u0010\u001a\u00020\u00062\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J!\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\t2\u0008\u0010\u0014\u001a\u0004\u0018\u00010\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\'\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u001b\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u001d\u0010\u001e\u001a\u00020\u00062\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\rH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0011J\u0017\u0010 \u001a\u00020\u00062\u0006\u0010\u001f\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u0017\u0010#\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008#\u0010!J\u000f\u0010$\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008$\u0010\u0003J\u0017\u0010&\u001a\u00020\u00062\u0006\u0010%\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008&\u0010!J\u000f\u0010\'\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\'\u0010\u0003J-\u0010/\u001a\u0004\u0018\u00010.2\u0006\u0010)\u001a\u00020(2\u0008\u0010+\u001a\u0004\u0018\u00010*2\u0008\u0010-\u001a\u0004\u0018\u00010,H\u0016\u00a2\u0006\u0004\u0008/\u00100J\u0019\u00101\u001a\u00020\u00062\u0008\u0010-\u001a\u0004\u0018\u00010,H\u0014\u00a2\u0006\u0004\u00081\u00102J\u000f\u00103\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00083\u0010\u0003J\u000f\u00104\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00084\u0010\u0003J\u000f\u00105\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00085\u0010\u0003J\u000f\u00106\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00086\u0010\u0003R\"\u0010>\u001a\u0002078\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00088\u00109\u001a\u0004\u0008:\u0010;\"\u0004\u0008<\u0010=R\"\u0010F\u001a\u00020?8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008@\u0010A\u001a\u0004\u0008B\u0010C\"\u0004\u0008D\u0010ER\u001b\u0010L\u001a\u00020G8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008H\u0010I\u001a\u0004\u0008J\u0010KR\u001b\u0010R\u001a\u00020M8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008N\u0010O\u001a\u0004\u0008P\u0010QR\u0018\u0010V\u001a\u0004\u0018\u00010S8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0018\u0010Z\u001a\u0004\u0018\u00010W8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0016\u0010]\u001a\u00020\u00178\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u001b\u0010b\u001a\u00020^8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008_\u0010I\u001a\u0004\u0008`\u0010a\u00a8\u0006e"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "leftMargin",
        "",
        "f3",
        "(I)V",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "type",
        "c3",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V",
        "",
        "Ldz0/a;",
        "bets",
        "d3",
        "(Ljava/util/List;)V",
        "betType",
        "",
        "betSum",
        "Y2",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V",
        "",
        "freeBet",
        "value",
        "",
        "currency",
        "e3",
        "(ZDLjava/lang/String;)V",
        "i3",
        "enable",
        "R2",
        "(Z)V",
        "show",
        "g3",
        "h3",
        "instantBetAllowed",
        "j3",
        "u2",
        "Landroid/view/LayoutInflater;",
        "inflater",
        "Landroid/view/ViewGroup;",
        "container",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "Landroid/view/View;",
        "onCreateView",
        "(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onResume",
        "onPause",
        "onDestroyView",
        "Lcz0/c$b;",
        "i0",
        "Lcz0/c$b;",
        "U2",
        "()Lcz0/c$b;",
        "setSpinAndViewModelFactory",
        "(Lcz0/c$b;)V",
        "spinAndViewModelFactory",
        "LzX0/k;",
        "j0",
        "LzX0/k;",
        "T2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
        "k0",
        "Lkotlin/j;",
        "W2",
        "()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
        "viewModel",
        "Lbz0/c;",
        "l0",
        "LRc/c;",
        "V2",
        "()Lbz0/c;",
        "viewBinding",
        "Lfz0/a;",
        "m0",
        "Lfz0/a;",
        "betAdapter",
        "Ly01/d;",
        "n0",
        "Ly01/d;",
        "snackBar",
        "o0",
        "Z",
        "leftMarginInitialized",
        "org/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b",
        "b1",
        "S2",
        "()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;",
        "globalListener",
        "k1",
        "a",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k1:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:Lcz0/c$b;

.field public j0:LzX0/k;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m0:Lfz0/a;

.field public n0:Ly01/d;

.field public o0:Z


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xbet/spin_and_win/databinding/FragmentSpinAndWinBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->v1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->k1:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LWy0/c;->fragment_spin_and_win:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/g;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/spin_and_win/presentation/game/g;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->k0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$viewBinding$2;->INSTANCE:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->l0:LRc/c;

    .line 57
    .line 58
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/h;

    .line 59
    .line 60
    invoke-direct {v0, p0}, Lorg/xbet/spin_and_win/presentation/game/h;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 61
    .line 62
    .line 63
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->b1:Lkotlin/j;

    .line 68
    .line 69
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->k3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->X2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->b3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->R2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->o0:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic G2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->Y2(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->c3(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->d3(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;ZDLjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->e3(ZDLjava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->o0:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic M2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;I)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->f3(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->g3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->h3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->i3(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->j3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final R2(Z)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->h:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/high16 v1, 0x3f800000    # 1.0f

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const v1, 0x3eb33333

    .line 13
    .line 14
    .line 15
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v0, p1}, Landroid/view/View;->setEnabled(Z)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public static final X2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final Z2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->n0:Ly01/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/google/android/material/snackbar/BaseTransientBottomBar;->dismiss()V

    .line 6
    .line 7
    .line 8
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->s4(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final a3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ldz0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->x4(Ldz0/a;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final b3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;Landroid/view/View;)Lkotlin/Unit;
    .locals 7

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-virtual {p2}, Landroid/app/Activity;->getCurrentFocus()Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    if-eqz p2, :cond_0

    .line 10
    .line 11
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    iget-object v2, p1, Lbz0/c;->d:Landroidx/appcompat/widget/AppCompatButton;

    .line 18
    .line 19
    const/16 v5, 0x8

    .line 20
    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v3, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/g;->s(Lorg/xbet/ui_common/utils/g;Landroid/content/Context;Landroid/view/View;ILkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->N4()V

    .line 32
    .line 33
    .line 34
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 35
    .line 36
    return-object p0
.end method

.method private final e3(ZDLjava/lang/String;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->j:Landroid/widget/TextView;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    sget p1, Lpb/k;->bonus:I

    .line 10
    .line 11
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    sget-object p1, Ll8/j;->a:Ll8/j;

    .line 17
    .line 18
    sget-object v1, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 19
    .line 20
    invoke-virtual {p1, p2, p3, p4, v1}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    :goto_0
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method private final f3(I)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, Lpb/f;->space_22:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    sget v2, Lpb/f;->space_26:I

    .line 16
    .line 17
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    iget-object v2, v2, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 26
    .line 27
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    check-cast v2, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    invoke-virtual {v2, p1, v0, v3, v1}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iget-object p1, p1, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 42
    .line 43
    invoke-virtual {p1}, Landroid/view/View;->requestLayout()V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method private final g3(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->d:Landroidx/appcompat/widget/AppCompatButton;

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x4

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final h3()V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->n0:Ly01/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lcom/google/android/material/snackbar/BaseTransientBottomBar;->isShown()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->T2()LzX0/k;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Ly01/g;

    .line 18
    .line 19
    sget-object v4, Ly01/i$a;->a:Ly01/i$a;

    .line 20
    .line 21
    sget v0, Lpb/k;->games_select_outcome_to_start_game_message:I

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    const/16 v10, 0x3c

    .line 28
    .line 29
    const/4 v11, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x0

    .line 32
    const/4 v8, 0x0

    .line 33
    const/4 v9, 0x0

    .line 34
    invoke-direct/range {v3 .. v11}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    const/16 v12, 0x1fc

    .line 38
    .line 39
    const/4 v13, 0x0

    .line 40
    const/4 v5, 0x0

    .line 41
    const/4 v7, 0x0

    .line 42
    const/4 v8, 0x0

    .line 43
    const/4 v10, 0x0

    .line 44
    move-object v4, p0

    .line 45
    invoke-static/range {v2 .. v13}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    iput-object v0, v4, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->n0:Ly01/d;

    .line 50
    .line 51
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->A4()V

    .line 56
    .line 57
    .line 58
    return-void
.end method

.method private final i3(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->m0:Lfz0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0, p1}, LUX0/h;->B(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method private final j3(Z)V
    .locals 5

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget p1, LIv/b;->multi_choice_play_button_margin_bottom_instant_bet:I

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget p1, LIv/b;->multi_choice_play_button_margin_bottom_bet:I

    .line 7
    .line 8
    :goto_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0, p1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, Lbz0/c;->d:Landroidx/appcompat/widget/AppCompatButton;

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v1, v1, Lbz0/c;->d:Landroidx/appcompat/widget/AppCompatButton;

    .line 27
    .line 28
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    check-cast v1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 33
    .line 34
    iget v2, v1, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 35
    .line 36
    iget v3, v1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 37
    .line 38
    iget v4, v1, Landroid/view/ViewGroup$MarginLayoutParams;->rightMargin:I

    .line 39
    .line 40
    invoke-virtual {v1, v2, v3, v4, p1}, Landroid/view/ViewGroup$MarginLayoutParams;->setMargins(IIII)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public static final k3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->U2()Lcz0/c$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ldz0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->a3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Ldz0/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->Z2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final S2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;

    .line 8
    .line 9
    return-object v0
.end method

.method public final T2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->j0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final U2()Lcz0/c$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->i0:Lcz0/c$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final V2()Lbz0/c;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lbz0/c;

    .line 13
    .line 14
    return-object v0
.end method

.method public final W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final Y2(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 6
    .line 7
    invoke-virtual {v0, p1, p2}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->o(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;Ljava/lang/Double;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final c3(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->l(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final d3(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->n(Ljava/util/List;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public onCreateView(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;
    .locals 12
    .param p1    # Landroid/view/LayoutInflater;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->j4()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onCreateView$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    invoke-super {p0, p1, p2, p3}, Landroidx/fragment/app/Fragment;->onCreateView(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    return-object p1
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    const/4 v1, 0x0

    .line 9
    invoke-virtual {v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->c4(Z)V

    .line 10
    .line 11
    .line 12
    const/4 v0, 0x0

    .line 13
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->m0:Lfz0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iget-object v1, v1, Lbz0/c;->h:Landroidx/recyclerview/widget/RecyclerView;

    .line 20
    .line 21
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 29
    .line 30
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->d()V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->d4()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->S2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->removeOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 26
    .line 27
    .line 28
    :cond_0
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 36
    .line 37
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->f()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->d4()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-nez v0, :cond_0

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->S2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$b;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 29
    .line 30
    .line 31
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 36
    .line 37
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->g()V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->V2()Lbz0/c;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object v0, p1, Lbz0/c;->c:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;

    .line 9
    .line 10
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/d;

    .line 11
    .line 12
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/d;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {v0, v1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinChoiceView;->setOnButtonClickListener$spin_and_win_release(Lkotlin/jvm/functions/Function1;)V

    .line 16
    .line 17
    .line 18
    new-instance v0, Lfz0/a;

    .line 19
    .line 20
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/e;

    .line 21
    .line 22
    invoke-direct {v1, p0}, Lorg/xbet/spin_and_win/presentation/game/e;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 23
    .line 24
    .line 25
    invoke-direct {v0, v1}, Lfz0/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 26
    .line 27
    .line 28
    iput-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->m0:Lfz0/a;

    .line 29
    .line 30
    iget-object v1, p1, Lbz0/c;->h:Landroidx/recyclerview/widget/RecyclerView;

    .line 31
    .line 32
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p1, Lbz0/c;->d:Landroidx/appcompat/widget/AppCompatButton;

    .line 36
    .line 37
    new-instance v1, Lorg/xbet/spin_and_win/presentation/game/f;

    .line 38
    .line 39
    invoke-direct {v1, p0, p1}, Lorg/xbet/spin_and_win/presentation/game/f;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;)V

    .line 40
    .line 41
    .line 42
    const/4 p1, 0x1

    .line 43
    const/4 v2, 0x0

    .line 44
    invoke-static {v0, v2, v1, p1, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public u2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;->h4()Lcz0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-interface {v0, p0}, Lcz0/c;->b(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->k4()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->g4()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$2;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->f4()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$3;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->h4()Lkotlinx/coroutines/flow/e;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    new-instance v11, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$4;

    .line 120
    .line 121
    invoke-direct {v11, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$4;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 122
    .line 123
    .line 124
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 125
    .line 126
    .line 127
    move-result-object v9

    .line 128
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 129
    .line 130
    .line 131
    move-result-object v2

    .line 132
    new-instance v5, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 133
    .line 134
    move-object v7, v5

    .line 135
    invoke-direct/range {v7 .. v12}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 136
    .line 137
    .line 138
    const/4 v7, 0x0

    .line 139
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 140
    .line 141
    .line 142
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->W2()Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 143
    .line 144
    .line 145
    move-result-object v2

    .line 146
    invoke-virtual {v2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->i4()Lkotlinx/coroutines/flow/e;

    .line 147
    .line 148
    .line 149
    move-result-object v8

    .line 150
    new-instance v11, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$5;

    .line 151
    .line 152
    invoke-direct {v11, v0, v1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$5;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    .line 153
    .line 154
    .line 155
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 156
    .line 157
    .line 158
    move-result-object v9

    .line 159
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 160
    .line 161
    .line 162
    move-result-object v1

    .line 163
    new-instance v4, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$5;

    .line 164
    .line 165
    move-object v7, v4

    .line 166
    invoke-direct/range {v7 .. v12}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$$inlined$observeWithLifecycle$default$5;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 167
    .line 168
    .line 169
    const/4 v5, 0x3

    .line 170
    const/4 v6, 0x0

    .line 171
    const/4 v2, 0x0

    .line 172
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 173
    .line 174
    .line 175
    return-void
.end method
