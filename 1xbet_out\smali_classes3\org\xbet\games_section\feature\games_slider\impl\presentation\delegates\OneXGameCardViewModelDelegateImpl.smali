.class public final Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;
.super LG40/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ae\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u001e\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 d2\u00020\u0001:\u0001eBi\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010#\u001a\u00020\u001e2\u0006\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008#\u0010$J&\u0010(\u001a\u00020\u001e2\u000c\u0010\'\u001a\u0008\u0012\u0004\u0012\u00020&0%2\u0006\u0010\"\u001a\u00020!H\u0082@\u00a2\u0006\u0004\u0008(\u0010)J\u0018\u0010*\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u001cH\u0082@\u00a2\u0006\u0004\u0008*\u0010+J\u0017\u0010.\u001a\u00020\u001e2\u0006\u0010-\u001a\u00020,H\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u0015\u00102\u001a\u0008\u0012\u0004\u0012\u00020100H\u0016\u00a2\u0006\u0004\u00082\u00103J\'\u00109\u001a\u00020\u001e2\u0006\u00105\u001a\u0002042\u0006\u00107\u001a\u0002062\u0006\u00108\u001a\u000206H\u0016\u00a2\u0006\u0004\u00089\u0010:J\u001f\u0010;\u001a\u00020\u001e2\u0006\u00107\u001a\u0002062\u0006\u00108\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008;\u0010<J \u0010?\u001a\u00020\u001e2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010>\u001a\u00020=H\u0082@\u00a2\u0006\u0004\u0008?\u0010@J\u0018\u0010B\u001a\u00020\u001e2\u0006\u0010A\u001a\u00020=H\u0082@\u00a2\u0006\u0004\u0008B\u0010CR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u001a\u0010_\u001a\u0008\u0012\u0004\u0012\u0002010\\8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0018\u0010c\u001a\u0004\u0018\u00010`8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008a\u0010b\u00a8\u0006f"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;",
        "LG40/b;",
        "LwX0/a;",
        "appScreensProvider",
        "LDg/c;",
        "oneXGamesAnalytics",
        "LpS/b;",
        "oneXGamesFatmanLogger",
        "LMR/b;",
        "mainMenuTopFatmanLogger",
        "LJT/c;",
        "addOneXGameLastActionUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lw30/o;",
        "getGamesSectionWalletUseCase",
        "LwX0/C;",
        "rootRouterHolder",
        "Lw30/i;",
        "getDemoAvailableForGameScenario",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lw30/q;",
        "getGpResultScenario",
        "<init>",
        "(LwX0/a;LDg/c;LpS/b;LMR/b;LJT/c;Lm8/a;Lorg/xbet/core/domain/usecases/d;Lw30/o;LwX0/C;Lw30/i;Lp9/c;Lw30/q;)V",
        "",
        "gameId",
        "",
        "M",
        "(J)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
        "gameType",
        "S",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "",
        "Lg9/i;",
        "balances",
        "W",
        "(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "V",
        "(JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "throwable",
        "R",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LF40/d;",
        "y0",
        "()Lkotlinx/coroutines/flow/e;",
        "Ln41/m;",
        "model",
        "",
        "screenName",
        "screenType",
        "J2",
        "(Ln41/m;Ljava/lang/String;Ljava/lang/String;)V",
        "B",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
        "item",
        "T",
        "(JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "game",
        "U",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "d",
        "LwX0/a;",
        "e",
        "LDg/c;",
        "f",
        "LpS/b;",
        "g",
        "LMR/b;",
        "h",
        "LJT/c;",
        "i",
        "Lm8/a;",
        "j",
        "Lorg/xbet/core/domain/usecases/d;",
        "k",
        "Lw30/o;",
        "l",
        "LwX0/C;",
        "m",
        "Lw30/i;",
        "n",
        "Lp9/c;",
        "o",
        "Lw30/q;",
        "Lkotlinx/coroutines/flow/U;",
        "p",
        "Lkotlinx/coroutines/flow/U;",
        "singleState",
        "Lkotlinx/coroutines/x0;",
        "q",
        "Lkotlinx/coroutines/x0;",
        "gameOpenJob",
        "r",
        "a",
        "impl_games_section_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final r:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final d:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LDg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LpS/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LMR/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LJT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lw30/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lw30/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lw30/q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "LF40/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public q:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->r:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$a;

    return-void
.end method

.method public constructor <init>(LwX0/a;LDg/c;LpS/b;LMR/b;LJT/c;Lm8/a;Lorg/xbet/core/domain/usecases/d;Lw30/o;LwX0/C;Lw30/i;Lp9/c;Lw30/q;)V
    .locals 0
    .param p1    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LDg/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LpS/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LMR/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LJT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lw30/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lw30/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lw30/q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, LG40/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->d:LwX0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->e:LDg/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->f:LpS/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->g:LMR/b;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->h:LJT/c;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->j:Lorg/xbet/core/domain/usecases/d;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->k:Lw30/o;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->l:LwX0/C;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->m:Lw30/i;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->n:Lp9/c;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->o:Lw30/q;

    .line 27
    .line 28
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iput-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->p:Lkotlinx/coroutines/flow/U;

    .line 33
    .line 34
    return-void
.end method

.method public static final synthetic C(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->R(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->S(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->T(JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic G(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->U(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic K(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->V(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic L(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->W(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final M(J)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    new-instance v2, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$addLastAction$1;

    .line 16
    .line 17
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$addLastAction$1;-><init>(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    new-instance v6, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$addLastAction$2;

    .line 21
    .line 22
    const/4 v0, 0x0

    .line 23
    invoke-direct {v6, p0, p1, p2, v0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$addLastAction$2;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;JLkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v7, 0xa

    .line 27
    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method private final R(Ljava/lang/Throwable;)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    sget-object v2, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$handleGameError$1;->INSTANCE:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$handleGameError$1;

    .line 16
    .line 17
    new-instance v6, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$handleGameError$2;

    .line 18
    .line 19
    const/4 v0, 0x0

    .line 20
    invoke-direct {v6, p0, p1, v0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$handleGameError$2;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    const/16 v7, 0xa

    .line 24
    .line 25
    const/4 v8, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method private final S(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    new-instance v2, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$1;

    .line 16
    .line 17
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$1;-><init>(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    new-instance v6, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;

    .line 21
    .line 22
    const/4 v0, 0x0

    .line 23
    invoke-direct {v6, p0, p1, v0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onWebGameClicked$2;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v7, 0xa

    .line 27
    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method private final V(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide p1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->J$0:J

    .line 39
    .line 40
    iget-object v0, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->L$0:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast v0, LwX0/c;

    .line 43
    .line 44
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    :goto_1
    move-wide v5, p1

    .line 48
    goto :goto_2

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    iget-object p3, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->l:LwX0/C;

    .line 61
    .line 62
    invoke-virtual {p3}, LwX0/D;->a()LwX0/c;

    .line 63
    .line 64
    .line 65
    move-result-object p3

    .line 66
    if-eqz p3, :cond_4

    .line 67
    .line 68
    iget-object v2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->m:Lw30/i;

    .line 69
    .line 70
    iput-object p3, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->L$0:Ljava/lang/Object;

    .line 71
    .line 72
    iput-wide p1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->J$0:J

    .line 73
    .line 74
    iput v3, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 75
    .line 76
    invoke-interface {v2, p1, p2, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    if-ne v0, v1, :cond_3

    .line 81
    .line 82
    return-object v1

    .line 83
    :cond_3
    move-object v5, v0

    .line 84
    move-object v0, p3

    .line 85
    move-object p3, v5

    .line 86
    goto :goto_1

    .line 87
    :goto_2
    check-cast p3, Ljava/lang/Boolean;

    .line 88
    .line 89
    invoke-virtual {p3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 90
    .line 91
    .line 92
    move-result p1

    .line 93
    xor-int/2addr p1, v3

    .line 94
    new-instance v4, Lb30/I;

    .line 95
    .line 96
    const/4 v9, 0x6

    .line 97
    const/4 v10, 0x0

    .line 98
    const/4 v7, 0x0

    .line 99
    const/4 v8, 0x0

    .line 100
    invoke-direct/range {v4 .. v10}, Lb30/I;-><init>(JLorg/xbet/games_section/api/models/GameBonus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v0, p1, v4}, LwX0/c;->o(ZLq4/q;)V

    .line 104
    .line 105
    .line 106
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 107
    .line 108
    return-object p1
.end method

.method private final W(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lg9/i;",
            ">;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-nez p1, :cond_0

    .line 6
    .line 7
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->p:Lkotlinx/coroutines/flow/U;

    .line 8
    .line 9
    sget-object p2, LF40/d$a;->a:LF40/d$a;

    .line 10
    .line 11
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1

    .line 17
    :cond_0
    invoke-static {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 18
    .line 19
    .line 20
    move-result-wide p1

    .line 21
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->V(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    if-ne p1, p2, :cond_1

    .line 30
    .line 31
    return-object p1

    .line 32
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 33
    .line 34
    return-object p1
.end method

.method public static final synthetic k(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LJT/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->h:LJT/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic l(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->j:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic n(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->n:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic o(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->m:Lw30/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->k:Lw30/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)Lw30/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->o:Lw30/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LDg/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->e:LDg/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LpS/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->f:LpS/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->l:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public B(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->e:LDg/c;

    .line 2
    .line 3
    invoke-virtual {v0, p2}, LDg/c;->c(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->g:LMR/b;

    .line 7
    .line 8
    invoke-interface {v0, p1, p2}, LMR/b;->a(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->l:LwX0/C;

    .line 12
    .line 13
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    if-eqz p1, :cond_0

    .line 18
    .line 19
    iget-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->d:LwX0/a;

    .line 20
    .line 21
    const/4 v0, 0x0

    .line 22
    invoke-interface {p2, v0}, LwX0/a;->C(I)Lq4/q;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    invoke-virtual {p1, p2}, LwX0/c;->m(Lq4/q;)V

    .line 27
    .line 28
    .line 29
    :cond_0
    return-void
.end method

.method public J2(Ln41/m;Ljava/lang/String;Ljava/lang/String;)V
    .locals 11
    .param p1    # Ln41/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->q:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v0}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    sget-object v2, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$1;->INSTANCE:Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$1;

    .line 28
    .line 29
    new-instance v5, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;

    .line 30
    .line 31
    const/4 v10, 0x0

    .line 32
    move-object v7, p0

    .line 33
    move-object v6, p1

    .line 34
    move-object v8, p2

    .line 35
    move-object v9, p3

    .line 36
    invoke-direct/range {v5 .. v10}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$onItemClick$2;-><init>(Ln41/m;Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 37
    .line 38
    .line 39
    move-object p1, v7

    .line 40
    const/16 v7, 0xa

    .line 41
    .line 42
    const/4 v8, 0x0

    .line 43
    const/4 v3, 0x0

    .line 44
    move-object v6, v5

    .line 45
    const/4 v5, 0x0

    .line 46
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    .line 49
    move-result-object p2

    .line 50
    iput-object p2, p1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->q:Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    return-void
.end method

.method public final T(JLcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->M(J)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p3}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    instance-of p2, p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeNative;

    .line 9
    .line 10
    if-eqz p2, :cond_1

    .line 11
    .line 12
    invoke-virtual {p0, p3, p4}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->U(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    if-ne p1, p2, :cond_0

    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 24
    .line 25
    return-object p1

    .line 26
    :cond_1
    instance-of p2, p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 27
    .line 28
    if-eqz p2, :cond_2

    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    invoke-static {p2}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    new-instance v1, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$2;

    .line 39
    .line 40
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$2;-><init>(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    iget-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->i:Lm8/a;

    .line 44
    .line 45
    invoke-interface {p2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    new-instance v5, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;

    .line 50
    .line 51
    const/4 p2, 0x0

    .line 52
    invoke-direct {v5, p0, p1, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openGame$3;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    const/16 v6, 0xa

    .line 56
    .line 57
    const/4 v7, 0x0

    .line 58
    const/4 v2, 0x0

    .line 59
    const/4 v4, 0x0

    .line 60
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 61
    .line 62
    .line 63
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 64
    .line 65
    return-object p1

    .line 66
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 67
    .line 68
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 69
    .line 70
    .line 71
    throw p1
.end method

.method public final U(Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;-><init>(Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->J$0:J

    .line 39
    .line 40
    iget-object p1, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->L$1:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast p1, Lb30/L;

    .line 43
    .line 44
    iget-object v0, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v0, LwX0/c;

    .line 47
    .line 48
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    move-object v6, p1

    .line 52
    move-wide v7, v1

    .line 53
    goto :goto_1

    .line 54
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 55
    .line 56
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 57
    .line 58
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw p1

    .line 62
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    iget-object p2, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->l:LwX0/C;

    .line 66
    .line 67
    invoke-virtual {p2}, LwX0/D;->a()LwX0/c;

    .line 68
    .line 69
    .line 70
    move-result-object p2

    .line 71
    if-eqz p2, :cond_4

    .line 72
    .line 73
    sget-object v2, Lb30/L;->a:Lb30/L;

    .line 74
    .line 75
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    invoke-static {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 80
    .line 81
    .line 82
    move-result-wide v4

    .line 83
    iget-object v6, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->m:Lw30/i;

    .line 84
    .line 85
    invoke-virtual {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/GpResult;->getGameType()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 90
    .line 91
    .line 92
    move-result-wide v7

    .line 93
    iput-object p2, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 94
    .line 95
    iput-object v2, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->L$1:Ljava/lang/Object;

    .line 96
    .line 97
    iput-wide v4, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->J$0:J

    .line 98
    .line 99
    iput v3, v0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 100
    .line 101
    invoke-interface {v6, v7, v8, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    if-ne p1, v1, :cond_3

    .line 106
    .line 107
    return-object v1

    .line 108
    :cond_3
    move-object v0, p2

    .line 109
    move-object v6, v2

    .line 110
    move-wide v7, v4

    .line 111
    move-object p2, p1

    .line 112
    :goto_1
    check-cast p2, Ljava/lang/Boolean;

    .line 113
    .line 114
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 115
    .line 116
    .line 117
    move-result p1

    .line 118
    xor-int/lit8 v10, p1, 0x1

    .line 119
    .line 120
    const/4 v11, 0x2

    .line 121
    const/4 v12, 0x0

    .line 122
    const/4 v9, 0x0

    .line 123
    invoke-static/range {v6 .. v12}, Lb30/L;->b(Lb30/L;JLorg/xbet/games_section/api/models/GameBonus;ZILjava/lang/Object;)LwX0/B;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    if-eqz p1, :cond_4

    .line 128
    .line 129
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 130
    .line 131
    .line 132
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 133
    .line 134
    return-object p1
.end method

.method public y0()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LF40/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/games_slider/impl/presentation/delegates/OneXGameCardViewModelDelegateImpl;->p:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    return-object v0
.end method
