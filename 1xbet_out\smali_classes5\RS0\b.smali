.class public final LRS0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u000f\u0008\u0080\u0008\u0018\u00002\u00020\u0001B/\u0012\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ8\u0010\u000c\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008H\u00c6\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0011\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001a\u0010\u0015\u001a\u00020\u00142\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000c\u0010\u0017\u001a\u0004\u0008\u0018\u0010\u0019R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u0012R\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u001fR\u0017\u0010\t\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010 \u001a\u0004\u0008!\u0010\"\u00a8\u0006#"
    }
    d2 = {
        "LRS0/b;",
        "",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;",
        "actionType",
        "",
        "duration",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;",
        "swipeType",
        "Landroid/view/animation/Interpolator;",
        "interpolator",
        "<init>",
        "(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)V",
        "a",
        "(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)LRS0/b;",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;",
        "c",
        "()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;",
        "b",
        "I",
        "d",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;",
        "f",
        "()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;",
        "Landroid/view/animation/Interpolator;",
        "e",
        "()Landroid/view/animation/Interpolator;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/view/animation/Interpolator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 7

    const/16 v5, 0xf

    const/4 v6, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object v0, p0

    .line 1
    invoke-direct/range {v0 .. v6}, LRS0/b;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)V
    .locals 0
    .param p1    # Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Landroid/view/animation/Interpolator;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 4
    iput p2, p0, LRS0/b;->b:I

    .line 5
    iput-object p3, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 6
    iput-object p4, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p6, p5, 0x1

    if-eqz p6, :cond_0

    .line 7
    sget-object p1, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;->Bet:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    :cond_0
    and-int/lit8 p6, p5, 0x2

    if-eqz p6, :cond_1

    .line 8
    sget-object p2, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/Duration;->Slow:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/Duration;

    invoke-virtual {p2}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/Duration;->getDuration()I

    move-result p2

    :cond_1
    and-int/lit8 p6, p5, 0x4

    if-eqz p6, :cond_2

    .line 9
    sget-object p3, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;->MANUAL:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    :cond_2
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_3

    .line 10
    new-instance p4, Landroid/view/animation/AccelerateInterpolator;

    invoke-direct {p4}, Landroid/view/animation/AccelerateInterpolator;-><init>()V

    .line 11
    :cond_3
    invoke-direct {p0, p1, p2, p3, p4}, LRS0/b;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)V

    return-void
.end method

.method public static synthetic b(LRS0/b;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;ILjava/lang/Object;)LRS0/b;
    .locals 0

    .line 1
    and-int/lit8 p6, p5, 0x1

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 6
    .line 7
    :cond_0
    and-int/lit8 p6, p5, 0x2

    .line 8
    .line 9
    if-eqz p6, :cond_1

    .line 10
    .line 11
    iget p2, p0, LRS0/b;->b:I

    .line 12
    .line 13
    :cond_1
    and-int/lit8 p6, p5, 0x4

    .line 14
    .line 15
    if-eqz p6, :cond_2

    .line 16
    .line 17
    iget-object p3, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 18
    .line 19
    :cond_2
    and-int/lit8 p5, p5, 0x8

    .line 20
    .line 21
    if-eqz p5, :cond_3

    .line 22
    .line 23
    iget-object p4, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 24
    .line 25
    :cond_3
    invoke-virtual {p0, p1, p2, p3, p4}, LRS0/b;->a(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)LRS0/b;

    .line 26
    .line 27
    .line 28
    move-result-object p0

    .line 29
    return-object p0
.end method


# virtual methods
.method public final a(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)LRS0/b;
    .locals 1
    .param p1    # Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Landroid/view/animation/Interpolator;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LRS0/b;

    .line 2
    .line 3
    invoke-direct {v0, p1, p2, p3, p4}, LRS0/b;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;ILorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;Landroid/view/animation/Interpolator;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final c()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget v0, p0, LRS0/b;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final e()Landroid/view/animation/Interpolator;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, LRS0/b;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, LRS0/b;

    .line 12
    .line 13
    iget-object v1, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 14
    .line 15
    iget-object v3, p1, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 16
    .line 17
    if-eq v1, v3, :cond_2

    .line 18
    .line 19
    return v2

    .line 20
    :cond_2
    iget v1, p0, LRS0/b;->b:I

    .line 21
    .line 22
    iget v3, p1, LRS0/b;->b:I

    .line 23
    .line 24
    if-eq v1, v3, :cond_3

    .line 25
    .line 26
    return v2

    .line 27
    :cond_3
    iget-object v1, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 28
    .line 29
    iget-object v3, p1, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 30
    .line 31
    if-eq v1, v3, :cond_4

    .line 32
    .line 33
    return v2

    .line 34
    :cond_4
    iget-object v1, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 35
    .line 36
    iget-object p1, p1, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 37
    .line 38
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    if-nez p1, :cond_5

    .line 43
    .line 44
    return v2

    .line 45
    :cond_5
    return v0
.end method

.method public final f()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 2
    .line 3
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    mul-int/lit8 v0, v0, 0x1f

    .line 8
    .line 9
    iget v1, p0, LRS0/b;->b:I

    .line 10
    .line 11
    add-int/2addr v0, v1

    .line 12
    mul-int/lit8 v0, v0, 0x1f

    .line 13
    .line 14
    iget-object v1, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 15
    .line 16
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    add-int/2addr v0, v1

    .line 21
    mul-int/lit8 v0, v0, 0x1f

    .line 22
    .line 23
    iget-object v1, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 24
    .line 25
    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    add-int/2addr v0, v1

    .line 30
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LRS0/b;->a:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 2
    .line 3
    iget v1, p0, LRS0/b;->b:I

    .line 4
    .line 5
    iget-object v2, p0, LRS0/b;->c:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;

    .line 6
    .line 7
    iget-object v3, p0, LRS0/b;->d:Landroid/view/animation/Interpolator;

    .line 8
    .line 9
    new-instance v4, Ljava/lang/StringBuilder;

    .line 10
    .line 11
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 12
    .line 13
    .line 14
    const-string v5, "SwipeAnimationSetting(actionType="

    .line 15
    .line 16
    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 17
    .line 18
    .line 19
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 20
    .line 21
    .line 22
    const-string v0, ", duration="

    .line 23
    .line 24
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 28
    .line 29
    .line 30
    const-string v0, ", swipeType="

    .line 31
    .line 32
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 33
    .line 34
    .line 35
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 36
    .line 37
    .line 38
    const-string v0, ", interpolator="

    .line 39
    .line 40
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 44
    .line 45
    .line 46
    const-string v0, ")"

    .line 47
    .line 48
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 49
    .line 50
    .line 51
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    return-object v0
.end method
