.class public interface abstract LuY0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LIY0/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LuY0/a$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0004\u0008f\u0018\u00002\u00020\u0001R\u0014\u0010\u0005\u001a\u00020\u00028&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0003\u0010\u0004R\u0014\u0010\t\u001a\u00020\u00068&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\r\u001a\u00020\n8&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\u000e"
    }
    d2 = {
        "LuY0/a;",
        "LIY0/b;",
        "Landroid/graphics/RectF;",
        "b",
        "()Landroid/graphics/RectF;",
        "chartBounds",
        "LtY0/a;",
        "j",
        "()LtY0/a;",
        "horizontalDimensions",
        "",
        "g",
        "()F",
        "horizontalScroll",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract b()Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract g()F
.end method

.method public abstract j()LtY0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
