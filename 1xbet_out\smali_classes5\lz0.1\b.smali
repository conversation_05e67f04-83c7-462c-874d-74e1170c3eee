.class public final Llz0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008 \u0008\u0007\u0018\u00002\u00020\u0001B\u0081\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008 \u0010!J/\u0010+\u001a\u00020*2\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$2\u0006\u0010\'\u001a\u00020&2\u0006\u0010)\u001a\u00020(H\u0000\u00a2\u0006\u0004\u0008+\u0010,R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010-R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010I\u00a8\u0006J"
    }
    d2 = {
        "Llz0/b;",
        "LQW0/a;",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LIa0/a;",
        "marketStatisticScreenFactory",
        "LwX0/a;",
        "appScreensProvider",
        "LLD0/a;",
        "statisticFeature",
        "LDg/a;",
        "gamesAnalytics",
        "LRT/c;",
        "favoritesCoreFeature",
        "LDZ/m;",
        "feedFeature",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "isBettingDisabledUseCase",
        "Lse0/a;",
        "playersDuelScreenFactory",
        "LzX0/k;",
        "snackbarManager",
        "LaC0/a;",
        "marketsSettingsDialogFactory",
        "LYB0/a;",
        "marketsSettingsFeature",
        "LHR/a;",
        "gamesFatmanLogger",
        "LHX0/e;",
        "resourceManager",
        "<init>",
        "(LQW0/c;Lorg/xbet/ui_common/utils/M;LIa0/a;LwX0/a;LLD0/a;LDg/a;LRT/c;LDZ/m;Lorg/xbet/remoteconfig/domain/usecases/k;Lse0/a;LzX0/k;LaC0/a;LYB0/a;LHR/a;LHX0/e;)V",
        "Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;",
        "params",
        "LwX0/c;",
        "router",
        "",
        "screenName",
        "LKA0/c$a;",
        "sportGameCoreLibProvider",
        "Llz0/a;",
        "a",
        "(Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;LwX0/c;Ljava/lang/String;LKA0/c$a;)Llz0/a;",
        "LQW0/c;",
        "b",
        "Lorg/xbet/ui_common/utils/M;",
        "c",
        "LIa0/a;",
        "d",
        "LwX0/a;",
        "e",
        "LLD0/a;",
        "f",
        "LDg/a;",
        "g",
        "LRT/c;",
        "h",
        "LDZ/m;",
        "i",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "j",
        "Lse0/a;",
        "k",
        "LzX0/k;",
        "l",
        "LaC0/a;",
        "m",
        "LYB0/a;",
        "n",
        "LHR/a;",
        "o",
        "LHX0/e;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LIa0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LLD0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LDg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LRT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LDZ/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/remoteconfig/domain/usecases/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lse0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:LaC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:LYB0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:LHR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(LQW0/c;Lorg/xbet/ui_common/utils/M;LIa0/a;LwX0/a;LLD0/a;LDg/a;LRT/c;LDZ/m;Lorg/xbet/remoteconfig/domain/usecases/k;Lse0/a;LzX0/k;LaC0/a;LYB0/a;LHR/a;LHX0/e;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LIa0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LLD0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LDg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LRT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LDZ/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/remoteconfig/domain/usecases/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lse0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LaC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LYB0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LHR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Llz0/b;->a:LQW0/c;

    .line 5
    .line 6
    iput-object p2, p0, Llz0/b;->b:Lorg/xbet/ui_common/utils/M;

    .line 7
    .line 8
    iput-object p3, p0, Llz0/b;->c:LIa0/a;

    .line 9
    .line 10
    iput-object p4, p0, Llz0/b;->d:LwX0/a;

    .line 11
    .line 12
    iput-object p5, p0, Llz0/b;->e:LLD0/a;

    .line 13
    .line 14
    iput-object p6, p0, Llz0/b;->f:LDg/a;

    .line 15
    .line 16
    iput-object p7, p0, Llz0/b;->g:LRT/c;

    .line 17
    .line 18
    iput-object p8, p0, Llz0/b;->h:LDZ/m;

    .line 19
    .line 20
    iput-object p9, p0, Llz0/b;->i:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 21
    .line 22
    iput-object p10, p0, Llz0/b;->j:Lse0/a;

    .line 23
    .line 24
    iput-object p11, p0, Llz0/b;->k:LzX0/k;

    .line 25
    .line 26
    iput-object p12, p0, Llz0/b;->l:LaC0/a;

    .line 27
    .line 28
    iput-object p13, p0, Llz0/b;->m:LYB0/a;

    .line 29
    .line 30
    iput-object p14, p0, Llz0/b;->n:LHR/a;

    .line 31
    .line 32
    iput-object p15, p0, Llz0/b;->o:LHX0/e;

    .line 33
    .line 34
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;LwX0/c;Ljava/lang/String;LKA0/c$a;)Llz0/a;
    .locals 21
    .param p1    # Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LKA0/c$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Llz0/d;->a()Llz0/a$a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, Llz0/b;->a:LQW0/c;

    .line 8
    .line 9
    invoke-interface/range {p4 .. p4}, LKA0/c$a;->z0()LKA0/c;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    iget-object v4, v0, Llz0/b;->e:LLD0/a;

    .line 14
    .line 15
    iget-object v11, v0, Llz0/b;->c:LIa0/a;

    .line 16
    .line 17
    iget-object v12, v0, Llz0/b;->d:LwX0/a;

    .line 18
    .line 19
    iget-object v13, v0, Llz0/b;->b:Lorg/xbet/ui_common/utils/M;

    .line 20
    .line 21
    iget-object v14, v0, Llz0/b;->f:LDg/a;

    .line 22
    .line 23
    iget-object v15, v0, Llz0/b;->i:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 24
    .line 25
    iget-object v5, v0, Llz0/b;->g:LRT/c;

    .line 26
    .line 27
    iget-object v6, v0, Llz0/b;->h:LDZ/m;

    .line 28
    .line 29
    iget-object v8, v0, Llz0/b;->j:Lse0/a;

    .line 30
    .line 31
    iget-object v7, v0, Llz0/b;->k:LzX0/k;

    .line 32
    .line 33
    iget-object v9, v0, Llz0/b;->l:LaC0/a;

    .line 34
    .line 35
    move-object/from16 v16, v7

    .line 36
    .line 37
    iget-object v7, v0, Llz0/b;->m:LYB0/a;

    .line 38
    .line 39
    iget-object v10, v0, Llz0/b;->n:LHR/a;

    .line 40
    .line 41
    move-object/from16 v17, v1

    .line 42
    .line 43
    iget-object v1, v0, Llz0/b;->o:LHX0/e;

    .line 44
    .line 45
    move-object/from16 v20, p3

    .line 46
    .line 47
    move-object/from16 v19, v1

    .line 48
    .line 49
    move-object/from16 v18, v10

    .line 50
    .line 51
    move-object/from16 v1, v17

    .line 52
    .line 53
    move-object/from16 v10, p1

    .line 54
    .line 55
    move-object/from16 v17, v9

    .line 56
    .line 57
    move-object/from16 v9, p2

    .line 58
    .line 59
    invoke-interface/range {v1 .. v20}, Llz0/a$a;->a(LQW0/c;LKA0/c;LLD0/a;LRT/c;LDZ/m;LYB0/a;Lse0/a;LwX0/c;Lorg/xbet/sportgame/action_menu/api/ActionMenuDialogParams;LIa0/a;LwX0/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/remoteconfig/domain/usecases/k;LzX0/k;LaC0/a;LHR/a;LHX0/e;Ljava/lang/String;)Llz0/a;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    return-object v1
.end method
