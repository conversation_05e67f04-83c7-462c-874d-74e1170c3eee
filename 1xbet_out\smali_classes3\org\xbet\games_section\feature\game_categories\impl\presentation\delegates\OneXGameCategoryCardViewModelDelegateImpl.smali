.class public final Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;
.super Lx40/c;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0096\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0017\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001BQ\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0015\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00170\u0016H\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001aH\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u001f\u0010!\u001a\u00020\u001c2\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010\u001b\u001a\u00020\u001aH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J \u0010%\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020#2\u0006\u0010 \u001a\u00020\u001fH\u0082@\u00a2\u0006\u0004\u0008%\u0010&J\u0017\u0010\'\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020#H\u0002\u00a2\u0006\u0004\u0008\'\u0010(J\u0018\u0010)\u001a\u00020\u001c2\u0006\u0010 \u001a\u00020\u001fH\u0082@\u00a2\u0006\u0004\u0008)\u0010*J\u0017\u0010-\u001a\u00020\u001c2\u0006\u0010,\u001a\u00020+H\u0002\u00a2\u0006\u0004\u0008-\u0010.J\u0017\u00101\u001a\u00020\u001c2\u0006\u00100\u001a\u00020/H\u0002\u00a2\u0006\u0004\u00081\u00102J\u0013\u00103\u001a\u00020\u001c*\u00020\u0017H\u0002\u00a2\u0006\u0004\u00083\u00104J&\u00108\u001a\u00020\u001c2\u000c\u00107\u001a\u0008\u0012\u0004\u0012\u000206052\u0006\u0010,\u001a\u00020+H\u0082@\u00a2\u0006\u0004\u00088\u00109J\u0018\u0010:\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020#H\u0082@\u00a2\u0006\u0004\u0008:\u0010;R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010Q\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u001a\u0010U\u001a\u0008\u0012\u0004\u0012\u00020\u00170R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010T\u00a8\u0006V"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;",
        "Lx40/c;",
        "LwX0/a;",
        "appScreensProvider",
        "LDg/c;",
        "oneXGamesAnalytics",
        "Lw30/o;",
        "getGamesSectionWalletUseCase",
        "Lorg/xbet/core/domain/usecases/d;",
        "choiceErrorActionScenario",
        "Lm8/a;",
        "coroutineDispatchers",
        "LJT/c;",
        "addOneXGameLastActionUseCase",
        "LwX0/C;",
        "rootRouterHolder",
        "Lw30/i;",
        "getDemoAvailableForGameScenario",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "<init>",
        "(LwX0/a;LDg/c;Lw30/o;Lorg/xbet/core/domain/usecases/d;Lm8/a;LJT/c;LwX0/C;Lw30/i;Lp9/c;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lw40/c;",
        "E2",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "categoryId",
        "",
        "a2",
        "(I)V",
        "Lu40/b;",
        "gameModel",
        "d3",
        "(Lu40/b;I)V",
        "",
        "gameId",
        "R",
        "(JLu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "K",
        "(J)V",
        "T",
        "(Lu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
        "gameType",
        "M",
        "(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V",
        "",
        "throwable",
        "L",
        "(Ljava/lang/Throwable;)V",
        "W",
        "(Lw40/c;)V",
        "",
        "Lg9/i;",
        "balances",
        "V",
        "(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "U",
        "(JLkotlin/coroutines/e;)Ljava/lang/Object;",
        "d",
        "LwX0/a;",
        "e",
        "LDg/c;",
        "f",
        "Lw30/o;",
        "g",
        "Lorg/xbet/core/domain/usecases/d;",
        "h",
        "Lm8/a;",
        "i",
        "LJT/c;",
        "j",
        "LwX0/C;",
        "k",
        "Lw30/i;",
        "l",
        "Lp9/c;",
        "Lkotlinx/coroutines/N;",
        "m",
        "Lkotlinx/coroutines/N;",
        "delegateScope",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "n",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "singleState",
        "impl_games_section_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final d:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LDg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lw30/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/core/domain/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LJT/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lw30/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lw40/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwX0/a;LDg/c;Lw30/o;Lorg/xbet/core/domain/usecases/d;Lm8/a;LJT/c;LwX0/C;Lw30/i;Lp9/c;)V
    .locals 0
    .param p1    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LDg/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lw30/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/core/domain/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LJT/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lw30/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lx40/c;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->d:LwX0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->e:LDg/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->f:Lw30/o;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->g:Lorg/xbet/core/domain/usecases/d;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->i:LJT/c;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->j:LwX0/C;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->k:Lw30/i;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->l:Lp9/c;

    .line 21
    .line 22
    invoke-interface {p5}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    const/4 p2, 0x0

    .line 27
    const/4 p3, 0x1

    .line 28
    invoke-static {p2, p3, p2}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 29
    .line 30
    .line 31
    move-result-object p2

    .line 32
    invoke-virtual {p1, p2}, Lkotlin/coroutines/a;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-static {p1}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->m:Lkotlinx/coroutines/N;

    .line 41
    .line 42
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 43
    .line 44
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 45
    .line 46
    invoke-direct {p1, p3, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 47
    .line 48
    .line 49
    iput-object p1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->n:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 50
    .line 51
    return-void
.end method

.method public static final synthetic C(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;JLu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->R(JLu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic E(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->T(Lu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic F(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->U(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic G(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->V(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final S(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;
    .locals 0

    .line 1
    check-cast p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->M(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method public static final X(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic k(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->S(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->X(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic n(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)LJT/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->i:LJT/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic o(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)Lorg/xbet/core/domain/usecases/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->g:Lorg/xbet/core/domain/usecases/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->l:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)Lw30/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->k:Lw30/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)Lw30/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->f:Lw30/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->n:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->L(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public E2()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lw40/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->n:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final K(J)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    new-instance v2, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$addLastAction$1;

    .line 16
    .line 17
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$addLastAction$1;-><init>(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    new-instance v6, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$addLastAction$2;

    .line 21
    .line 22
    const/4 v0, 0x0

    .line 23
    invoke-direct {v6, p0, p1, p2, v0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$addLastAction$2;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;JLkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v7, 0xa

    .line 27
    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final L(Ljava/lang/Throwable;)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->m:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 4
    .line 5
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 6
    .line 7
    .line 8
    move-result-object v3

    .line 9
    sget-object v1, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;->INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$1;

    .line 10
    .line 11
    new-instance v5, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$2;

    .line 12
    .line 13
    const/4 v2, 0x0

    .line 14
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$handleGameError$2;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Ljava/lang/Throwable;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    const/16 v6, 0xa

    .line 18
    .line 19
    const/4 v7, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final M(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;)V
    .locals 9

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 10
    .line 11
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    new-instance v2, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onWebGameClicked$1;

    .line 16
    .line 17
    invoke-direct {v2, p0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onWebGameClicked$1;-><init>(Ljava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    new-instance v6, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onWebGameClicked$2;

    .line 21
    .line 22
    const/4 v0, 0x0

    .line 23
    invoke-direct {v6, p0, p1, v0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onWebGameClicked$2;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v7, 0xa

    .line 27
    .line 28
    const/4 v8, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    const/4 v5, 0x0

    .line 31
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final R(JLu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lu40/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->K(J)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p3}, Lu40/b;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    instance-of p2, p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeNative;

    .line 9
    .line 10
    if-eqz p2, :cond_1

    .line 11
    .line 12
    invoke-virtual {p0, p3, p4}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->T(Lu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    if-ne p1, p2, :cond_0

    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 24
    .line 25
    return-object p1

    .line 26
    :cond_1
    instance-of p2, p1, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;

    .line 27
    .line 28
    if-eqz p2, :cond_3

    .line 29
    .line 30
    iget-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->j:LwX0/C;

    .line 31
    .line 32
    invoke-virtual {p2}, LwX0/D;->a()LwX0/c;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    if-eqz p2, :cond_2

    .line 37
    .line 38
    new-instance p3, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;

    .line 39
    .line 40
    invoke-direct {p3, p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/b;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p2, p3}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 44
    .line 45
    .line 46
    :cond_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p1

    .line 49
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 50
    .line 51
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 52
    .line 53
    .line 54
    throw p1
.end method

.method public final T(Lu40/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lu40/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide v1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->J$0:J

    .line 39
    .line 40
    iget-object p1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->L$1:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast p1, Lb30/L;

    .line 43
    .line 44
    iget-object v0, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v0, LwX0/c;

    .line 47
    .line 48
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    move-object v6, p1

    .line 52
    move-wide v7, v1

    .line 53
    goto :goto_1

    .line 54
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 55
    .line 56
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 57
    .line 58
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw p1

    .line 62
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    iget-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->j:LwX0/C;

    .line 66
    .line 67
    invoke-virtual {p2}, LwX0/D;->a()LwX0/c;

    .line 68
    .line 69
    .line 70
    move-result-object p2

    .line 71
    if-eqz p2, :cond_4

    .line 72
    .line 73
    sget-object v2, Lb30/L;->a:Lb30/L;

    .line 74
    .line 75
    invoke-virtual {p1}, Lu40/b;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 76
    .line 77
    .line 78
    move-result-object v4

    .line 79
    invoke-static {v4}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 80
    .line 81
    .line 82
    move-result-wide v4

    .line 83
    iget-object v6, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->k:Lw30/i;

    .line 84
    .line 85
    invoke-virtual {p1}, Lu40/b;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-static {p1}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 90
    .line 91
    .line 92
    move-result-wide v7

    .line 93
    iput-object p2, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->L$0:Ljava/lang/Object;

    .line 94
    .line 95
    iput-object v2, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->L$1:Ljava/lang/Object;

    .line 96
    .line 97
    iput-wide v4, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->J$0:J

    .line 98
    .line 99
    iput v3, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openNativeGame$1;->label:I

    .line 100
    .line 101
    invoke-interface {v6, v7, v8, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    if-ne p1, v1, :cond_3

    .line 106
    .line 107
    return-object v1

    .line 108
    :cond_3
    move-object v0, p2

    .line 109
    move-object v6, v2

    .line 110
    move-wide v7, v4

    .line 111
    move-object p2, p1

    .line 112
    :goto_1
    check-cast p2, Ljava/lang/Boolean;

    .line 113
    .line 114
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 115
    .line 116
    .line 117
    move-result p1

    .line 118
    xor-int/lit8 v10, p1, 0x1

    .line 119
    .line 120
    const/4 v11, 0x2

    .line 121
    const/4 v12, 0x0

    .line 122
    const/4 v9, 0x0

    .line 123
    invoke-static/range {v6 .. v12}, Lb30/L;->b(Lb30/L;JLorg/xbet/games_section/api/models/GameBonus;ZILjava/lang/Object;)LwX0/B;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    if-eqz p1, :cond_4

    .line 128
    .line 129
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 130
    .line 131
    .line 132
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 133
    .line 134
    return-object p1
.end method

.method public final U(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-wide p1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->J$0:J

    .line 39
    .line 40
    iget-object v0, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->L$0:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast v0, LwX0/c;

    .line 43
    .line 44
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    :goto_1
    move-wide v5, p1

    .line 48
    goto :goto_2

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    iget-object p3, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->j:LwX0/C;

    .line 61
    .line 62
    invoke-virtual {p3}, LwX0/D;->a()LwX0/c;

    .line 63
    .line 64
    .line 65
    move-result-object p3

    .line 66
    if-eqz p3, :cond_4

    .line 67
    .line 68
    iget-object v2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->k:Lw30/i;

    .line 69
    .line 70
    iput-object p3, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->L$0:Ljava/lang/Object;

    .line 71
    .line 72
    iput-wide p1, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->J$0:J

    .line 73
    .line 74
    iput v3, v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$openWebPage$1;->label:I

    .line 75
    .line 76
    invoke-interface {v2, p1, p2, v0}, Lw30/i;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    if-ne v0, v1, :cond_3

    .line 81
    .line 82
    return-object v1

    .line 83
    :cond_3
    move-object v5, v0

    .line 84
    move-object v0, p3

    .line 85
    move-object p3, v5

    .line 86
    goto :goto_1

    .line 87
    :goto_2
    check-cast p3, Ljava/lang/Boolean;

    .line 88
    .line 89
    invoke-virtual {p3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 90
    .line 91
    .line 92
    move-result p1

    .line 93
    xor-int/2addr p1, v3

    .line 94
    new-instance v4, Lb30/I;

    .line 95
    .line 96
    const/4 v9, 0x6

    .line 97
    const/4 v10, 0x0

    .line 98
    const/4 v7, 0x0

    .line 99
    const/4 v8, 0x0

    .line 100
    invoke-direct/range {v4 .. v10}, Lb30/I;-><init>(JLorg/xbet/games_section/api/models/GameBonus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {v0, p1, v4}, LwX0/c;->o(ZLq4/q;)V

    .line 104
    .line 105
    .line 106
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 107
    .line 108
    return-object p1
.end method

.method public final V(Ljava/util/List;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lg9/i;",
            ">;",
            "Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon$OneXGamesTypeWeb;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-nez p1, :cond_0

    .line 6
    .line 7
    sget-object p1, Lw40/c$a;->a:Lw40/c$a;

    .line 8
    .line 9
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->W(Lw40/c;)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1

    .line 15
    :cond_0
    invoke-static {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 16
    .line 17
    .line 18
    move-result-wide p1

    .line 19
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->U(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    if-ne p1, p2, :cond_1

    .line 28
    .line 29
    return-object p1

    .line 30
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 31
    .line 32
    return-object p1
.end method

.method public final W(Lw40/c;)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->m:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 4
    .line 5
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 6
    .line 7
    .line 8
    move-result-object v3

    .line 9
    new-instance v1, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/c;

    .line 10
    .line 11
    invoke-direct {v1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/c;-><init>()V

    .line 12
    .line 13
    .line 14
    new-instance v5, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$send$2;

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$send$2;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;Lw40/c;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/16 v6, 0xa

    .line 21
    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public a2(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->e:LDg/c;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget-object v2, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewXGames:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 8
    .line 9
    invoke-virtual {v0, v1, v2}, LDg/c;->e(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->j:LwX0/C;

    .line 13
    .line 14
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->d:LwX0/a;

    .line 21
    .line 22
    invoke-interface {v1, p1}, LwX0/a;->C(I)Lq4/q;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 27
    .line 28
    .line 29
    :cond_0
    return-void
.end method

.method public d3(Lu40/b;I)V
    .locals 12
    .param p1    # Lu40/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lu40/b;->c()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-static {p2}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/b;->b(Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesTypeCommon;)J

    .line 6
    .line 7
    .line 8
    move-result-wide v2

    .line 9
    iget-object p2, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->e:LDg/c;

    .line 10
    .line 11
    sget-object v0, Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;->PopularNewTop:Lorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;

    .line 12
    .line 13
    invoke-virtual {p2, v2, v3, v0}, LDg/c;->p(JLorg/xbet/analytics/domain/scope/games/OneXGamePrecedingScreenType;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    invoke-static {p2}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    iget-object v0, p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;->h:Lm8/a;

    .line 25
    .line 26
    invoke-interface {v0}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 27
    .line 28
    .line 29
    move-result-object v7

    .line 30
    sget-object v6, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onGameClick$1;->INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onGameClick$1;

    .line 31
    .line 32
    new-instance v0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onGameClick$2;

    .line 33
    .line 34
    const/4 v5, 0x0

    .line 35
    move-object v1, p0

    .line 36
    move-object v4, p1

    .line 37
    invoke-direct/range {v0 .. v5}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl$onGameClick$2;-><init>(Lorg/xbet/games_section/feature/game_categories/impl/presentation/delegates/OneXGameCategoryCardViewModelDelegateImpl;JLu40/b;Lkotlin/coroutines/e;)V

    .line 38
    .line 39
    .line 40
    const/16 v10, 0xa

    .line 41
    .line 42
    const/4 v11, 0x0

    .line 43
    move-object v5, v6

    .line 44
    const/4 v6, 0x0

    .line 45
    const/4 v8, 0x0

    .line 46
    move-object v4, p2

    .line 47
    move-object v9, v0

    .line 48
    invoke-static/range {v4 .. v11}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    return-void
.end method
