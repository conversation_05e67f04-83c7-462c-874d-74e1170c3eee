.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\n\u0008\u0081\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u001d\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\r\u0010\u000f\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0012\u001a\u00020\u0011H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0010\u0010\u0014\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0014\u0010\u0010J\u001a\u0010\u0018\u001a\u00020\u00172\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u0010R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001d\u0010\u001fR\u0017\u0010\u0006\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008 \u0010\u001b\u001a\u0004\u0008\u001a\u0010\u0010\u00a8\u0006!"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
        "Landroid/os/Parcelable;",
        "",
        "eventId",
        "",
        "sportId",
        "champId",
        "<init>",
        "(IJI)V",
        "Landroid/os/Parcel;",
        "dest",
        "flags",
        "",
        "writeToParcel",
        "(Landroid/os/Parcel;I)V",
        "describeContents",
        "()I",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "I",
        "G",
        "b",
        "J",
        "()J",
        "c",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I

.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:I

.field public final b:J

.field public final c:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams$a;

    invoke-direct {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams$a;-><init>()V

    sput-object v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->CREATOR:Landroid/os/Parcelable$Creator;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->$stable:I

    return-void
.end method

.method public constructor <init>(IJI)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    .line 5
    .line 6
    iput-wide p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    .line 7
    .line 8
    iput p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final G()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 7

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;

    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    iget v3, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-wide v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    iget-wide v5, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    cmp-long v1, v3, v5

    if-eqz v1, :cond_3

    return v2

    :cond_3
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    iget p1, p1, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    if-eq v1, p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 3

    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    mul-int/lit8 v0, v0, 0x1f

    iget-wide v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    invoke-static {v1, v2}, Lu/l;->a(J)I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    iget-wide v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    iget v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "GroupStageScreenParams(eventId="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", sportId="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v0, ", champId="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 2
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iget p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->a:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    iget-wide v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->b:J

    invoke-virtual {p1, v0, v1}, Landroid/os/Parcel;->writeLong(J)V

    iget p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/main/model/GroupStageScreenParams;->c:I

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeInt(I)V

    return-void
.end method
