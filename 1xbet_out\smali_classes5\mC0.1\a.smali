.class public final LmC0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LkC0/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmC0/a$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0017\u0018\u00002\u00020\u0001Ba\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001b\u001a\u00020\u001aH\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0013\u0010 \u001a\u00020\u001f*\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\"R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010#R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105\u00a8\u00066"
    }
    d2 = {
        "LmC0/a;",
        "LkC0/a;",
        "LZD/b;",
        "gameDotaScreenFactory",
        "LnD/b;",
        "cyberGameCs2ScreenFactory",
        "LbE/b;",
        "cyberGameLolScreenFactory",
        "LbH/b;",
        "cyberGameValorantScreenFactory",
        "LgE/b;",
        "cyberRainbowScreenFactory",
        "LhF/b;",
        "cyberUniversalScreenFactory",
        "LGz0/a;",
        "sportGameClassicScreenFactory",
        "Lqz0/a;",
        "sportGameAdvancedScreenFactory",
        "LUn/a;",
        "checkIsCyberGameByIdUseCase",
        "LfX/b;",
        "testRepository",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "<init>",
        "(LZD/b;LnD/b;LbE/b;LbH/b;LgE/b;LhF/b;LGz0/a;Lqz0/a;LUn/a;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V",
        "Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;",
        "gameScreenGeneralParams",
        "Lq4/q;",
        "a",
        "(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Lq4/q;",
        "",
        "b",
        "(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z",
        "LZD/b;",
        "LnD/b;",
        "c",
        "LbE/b;",
        "d",
        "LbH/b;",
        "e",
        "LgE/b;",
        "f",
        "LhF/b;",
        "g",
        "LGz0/a;",
        "h",
        "Lqz0/a;",
        "i",
        "LUn/a;",
        "j",
        "LfX/b;",
        "k",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LZD/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LnD/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LbE/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LbH/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LgE/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LhF/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LGz0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lqz0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LUn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LZD/b;LnD/b;LbE/b;LbH/b;LgE/b;LhF/b;LGz0/a;Lqz0/a;LUn/a;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V
    .locals 0
    .param p1    # LZD/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LnD/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LbE/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LbH/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LgE/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LhF/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LGz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lqz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LUn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LmC0/a;->a:LZD/b;

    .line 5
    .line 6
    iput-object p2, p0, LmC0/a;->b:LnD/b;

    .line 7
    .line 8
    iput-object p3, p0, LmC0/a;->c:LbE/b;

    .line 9
    .line 10
    iput-object p4, p0, LmC0/a;->d:LbH/b;

    .line 11
    .line 12
    iput-object p5, p0, LmC0/a;->e:LgE/b;

    .line 13
    .line 14
    iput-object p6, p0, LmC0/a;->f:LhF/b;

    .line 15
    .line 16
    iput-object p7, p0, LmC0/a;->g:LGz0/a;

    .line 17
    .line 18
    iput-object p8, p0, LmC0/a;->h:Lqz0/a;

    .line 19
    .line 20
    iput-object p9, p0, LmC0/a;->i:LUn/a;

    .line 21
    .line 22
    iput-object p10, p0, LmC0/a;->j:LfX/b;

    .line 23
    .line 24
    iput-object p11, p0, LmC0/a;->k:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 25
    .line 26
    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Lq4/q;
    .locals 19
    .param p1    # Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    const-wide/16 v3, 0x1

    .line 8
    .line 9
    cmp-long v5, v1, v3

    .line 10
    .line 11
    if-nez v5, :cond_0

    .line 12
    .line 13
    iget-object v1, v0, LmC0/a;->a:LZD/b;

    .line 14
    .line 15
    new-instance v2, Lorg/xbet/cyber/game/dota/api/presentation/CyberGameDotaScreenParams;

    .line 16
    .line 17
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 18
    .line 19
    .line 20
    move-result-wide v3

    .line 21
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 26
    .line 27
    .line 28
    move-result-wide v6

    .line 29
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 30
    .line 31
    .line 32
    move-result-wide v8

    .line 33
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 34
    .line 35
    .line 36
    move-result-wide v10

    .line 37
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 38
    .line 39
    .line 40
    move-result v12

    .line 41
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 42
    .line 43
    .line 44
    move-result-object v13

    .line 45
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 46
    .line 47
    .line 48
    move-result-wide v14

    .line 49
    invoke-direct/range {v2 .. v15}, Lorg/xbet/cyber/game/dota/api/presentation/CyberGameDotaScreenParams;-><init>(JZJJJZLjava/lang/String;J)V

    .line 50
    .line 51
    .line 52
    invoke-interface {v1, v2}, LZD/b;->a(Lorg/xbet/cyber/game/dota/api/presentation/CyberGameDotaScreenParams;)Lq4/q;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    return-object v1

    .line 57
    :cond_0
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 58
    .line 59
    .line 60
    move-result-wide v1

    .line 61
    const-wide/16 v3, 0x2e

    .line 62
    .line 63
    cmp-long v5, v1, v3

    .line 64
    .line 65
    if-nez v5, :cond_1

    .line 66
    .line 67
    iget-object v1, v0, LmC0/a;->b:LnD/b;

    .line 68
    .line 69
    new-instance v2, Lorg/xbet/cyber/game/counterstrike/api/CyberGameCs2ScreenParams;

    .line 70
    .line 71
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 72
    .line 73
    .line 74
    move-result-wide v3

    .line 75
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 76
    .line 77
    .line 78
    move-result v5

    .line 79
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 80
    .line 81
    .line 82
    move-result-wide v6

    .line 83
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 84
    .line 85
    .line 86
    move-result-wide v8

    .line 87
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 88
    .line 89
    .line 90
    move-result-wide v10

    .line 91
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 92
    .line 93
    .line 94
    move-result v12

    .line 95
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v13

    .line 99
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 100
    .line 101
    .line 102
    move-result-wide v14

    .line 103
    invoke-direct/range {v2 .. v15}, Lorg/xbet/cyber/game/counterstrike/api/CyberGameCs2ScreenParams;-><init>(JZJJJZLjava/lang/String;J)V

    .line 104
    .line 105
    .line 106
    invoke-interface {v1, v2}, LnD/b;->a(Lorg/xbet/cyber/game/counterstrike/api/CyberGameCs2ScreenParams;)Lq4/q;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    return-object v1

    .line 111
    :cond_1
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 112
    .line 113
    .line 114
    move-result-wide v1

    .line 115
    const-wide/16 v3, 0x2

    .line 116
    .line 117
    cmp-long v5, v1, v3

    .line 118
    .line 119
    if-nez v5, :cond_2

    .line 120
    .line 121
    iget-object v1, v0, LmC0/a;->c:LbE/b;

    .line 122
    .line 123
    new-instance v2, Lorg/xbet/cyber/game/lol/api/CyberGameLolScreenParams;

    .line 124
    .line 125
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 126
    .line 127
    .line 128
    move-result-wide v3

    .line 129
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 130
    .line 131
    .line 132
    move-result v5

    .line 133
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 134
    .line 135
    .line 136
    move-result-wide v6

    .line 137
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 138
    .line 139
    .line 140
    move-result-wide v8

    .line 141
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 142
    .line 143
    .line 144
    move-result-wide v10

    .line 145
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 146
    .line 147
    .line 148
    move-result v12

    .line 149
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object v13

    .line 153
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 154
    .line 155
    .line 156
    move-result-wide v14

    .line 157
    invoke-direct/range {v2 .. v15}, Lorg/xbet/cyber/game/lol/api/CyberGameLolScreenParams;-><init>(JZJJJZLjava/lang/String;J)V

    .line 158
    .line 159
    .line 160
    invoke-interface {v1, v2}, LbE/b;->a(Lorg/xbet/cyber/game/lol/api/CyberGameLolScreenParams;)Lq4/q;

    .line 161
    .line 162
    .line 163
    move-result-object v1

    .line 164
    return-object v1

    .line 165
    :cond_2
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 166
    .line 167
    .line 168
    move-result-wide v1

    .line 169
    const-wide/16 v3, 0x1b

    .line 170
    .line 171
    cmp-long v5, v1, v3

    .line 172
    .line 173
    if-nez v5, :cond_3

    .line 174
    .line 175
    iget-object v1, v0, LmC0/a;->d:LbH/b;

    .line 176
    .line 177
    new-instance v2, Lorg/xbet/cyber/game/valorant/api/CyberGameValorantScreenParams;

    .line 178
    .line 179
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 180
    .line 181
    .line 182
    move-result-wide v3

    .line 183
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 184
    .line 185
    .line 186
    move-result v5

    .line 187
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 188
    .line 189
    .line 190
    move-result-wide v6

    .line 191
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 192
    .line 193
    .line 194
    move-result-wide v8

    .line 195
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 196
    .line 197
    .line 198
    move-result-wide v10

    .line 199
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 200
    .line 201
    .line 202
    move-result v12

    .line 203
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 204
    .line 205
    .line 206
    move-result-object v13

    .line 207
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 208
    .line 209
    .line 210
    move-result-wide v14

    .line 211
    invoke-direct/range {v2 .. v15}, Lorg/xbet/cyber/game/valorant/api/CyberGameValorantScreenParams;-><init>(JZJJJZLjava/lang/String;J)V

    .line 212
    .line 213
    .line 214
    invoke-interface {v1, v2}, LbH/b;->a(Lorg/xbet/cyber/game/valorant/api/CyberGameValorantScreenParams;)Lq4/q;

    .line 215
    .line 216
    .line 217
    move-result-object v1

    .line 218
    return-object v1

    .line 219
    :cond_3
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 220
    .line 221
    .line 222
    move-result-wide v1

    .line 223
    const-wide/16 v3, 0xf

    .line 224
    .line 225
    cmp-long v5, v1, v3

    .line 226
    .line 227
    if-nez v5, :cond_4

    .line 228
    .line 229
    iget-object v1, v0, LmC0/a;->e:LgE/b;

    .line 230
    .line 231
    new-instance v2, Lorg/xbet/cyber/game/rainbow/api/RainbowGameScreenParams;

    .line 232
    .line 233
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 234
    .line 235
    .line 236
    move-result-wide v3

    .line 237
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 238
    .line 239
    .line 240
    move-result v5

    .line 241
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 242
    .line 243
    .line 244
    move-result-wide v6

    .line 245
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 246
    .line 247
    .line 248
    move-result-wide v8

    .line 249
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 250
    .line 251
    .line 252
    move-result-wide v10

    .line 253
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 254
    .line 255
    .line 256
    move-result v12

    .line 257
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 258
    .line 259
    .line 260
    move-result-object v13

    .line 261
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 262
    .line 263
    .line 264
    move-result-wide v14

    .line 265
    invoke-direct/range {v2 .. v15}, Lorg/xbet/cyber/game/rainbow/api/RainbowGameScreenParams;-><init>(JZJJJZLjava/lang/String;J)V

    .line 266
    .line 267
    .line 268
    invoke-interface {v1, v2}, LgE/b;->a(Lorg/xbet/cyber/game/rainbow/api/RainbowGameScreenParams;)Lq4/q;

    .line 269
    .line 270
    .line 271
    move-result-object v1

    .line 272
    return-object v1

    .line 273
    :cond_4
    iget-object v1, v0, LmC0/a;->i:LUn/a;

    .line 274
    .line 275
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 276
    .line 277
    .line 278
    move-result-wide v2

    .line 279
    invoke-interface {v1, v2, v3}, LUn/a;->a(J)Z

    .line 280
    .line 281
    .line 282
    move-result v1

    .line 283
    const/4 v2, 0x1

    .line 284
    if-eqz v1, :cond_6

    .line 285
    .line 286
    iget-object v1, v0, LmC0/a;->f:LhF/b;

    .line 287
    .line 288
    new-instance v3, Lorg/xbet/cyber/game/universal/api/CyberUniversalScreenParams;

    .line 289
    .line 290
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 291
    .line 292
    .line 293
    move-result-wide v4

    .line 294
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 295
    .line 296
    .line 297
    move-result v6

    .line 298
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 299
    .line 300
    .line 301
    move-result-wide v7

    .line 302
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 303
    .line 304
    .line 305
    move-result-wide v9

    .line 306
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->i()J

    .line 307
    .line 308
    .line 309
    move-result-wide v11

    .line 310
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->e()Lorg/xbet/ui_common/sportgame/game_screen/models/GameScreenInitialAction;

    .line 311
    .line 312
    .line 313
    move-result-object v13

    .line 314
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 315
    .line 316
    .line 317
    move-result-wide v14

    .line 318
    const-wide/16 v16, 0x28

    .line 319
    .line 320
    cmp-long v18, v14, v16

    .line 321
    .line 322
    if-eqz v18, :cond_5

    .line 323
    .line 324
    const/4 v14, 0x1

    .line 325
    goto :goto_0

    .line 326
    :cond_5
    const/4 v2, 0x0

    .line 327
    const/4 v14, 0x0

    .line 328
    :goto_0
    invoke-virtual/range {p0 .. p1}, LmC0/a;->b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z

    .line 329
    .line 330
    .line 331
    move-result v15

    .line 332
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->b()Ljava/lang/String;

    .line 333
    .line 334
    .line 335
    move-result-object v16

    .line 336
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->a()J

    .line 337
    .line 338
    .line 339
    move-result-wide v17

    .line 340
    invoke-direct/range {v3 .. v18}, Lorg/xbet/cyber/game/universal/api/CyberUniversalScreenParams;-><init>(JZJJJLorg/xbet/ui_common/sportgame/game_screen/models/GameScreenInitialAction;ZZLjava/lang/String;J)V

    .line 341
    .line 342
    .line 343
    invoke-interface {v1, v3}, LhF/b;->a(Lorg/xbet/cyber/game/universal/api/CyberUniversalScreenParams;)Lq4/q;

    .line 344
    .line 345
    .line 346
    move-result-object v1

    .line 347
    return-object v1

    .line 348
    :cond_6
    iget-object v1, v0, LmC0/a;->j:LfX/b;

    .line 349
    .line 350
    invoke-interface {v1}, LfX/b;->z0()Z

    .line 351
    .line 352
    .line 353
    move-result v1

    .line 354
    if-eqz v1, :cond_9

    .line 355
    .line 356
    iget-object v1, v0, LmC0/a;->k:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 357
    .line 358
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 359
    .line 360
    .line 361
    move-result-object v1

    .line 362
    invoke-virtual {v1}, Lek0/o;->t2()Lorg/xbet/remoteconfig/domain/models/SportGameType;

    .line 363
    .line 364
    .line 365
    move-result-object v1

    .line 366
    sget-object v3, LmC0/a$a;->a:[I

    .line 367
    .line 368
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 369
    .line 370
    .line 371
    move-result v1

    .line 372
    aget v1, v3, v1

    .line 373
    .line 374
    if-eq v1, v2, :cond_8

    .line 375
    .line 376
    const/4 v2, 0x2

    .line 377
    if-eq v1, v2, :cond_8

    .line 378
    .line 379
    const/4 v2, 0x3

    .line 380
    if-eq v1, v2, :cond_8

    .line 381
    .line 382
    const/4 v2, 0x4

    .line 383
    if-ne v1, v2, :cond_7

    .line 384
    .line 385
    iget-object v1, v0, LmC0/a;->h:Lqz0/a;

    .line 386
    .line 387
    new-instance v2, Lorg/xbet/sportgame/advanced/api/SportGameAdvancedScreenParams;

    .line 388
    .line 389
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 390
    .line 391
    .line 392
    move-result-wide v3

    .line 393
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 394
    .line 395
    .line 396
    move-result v5

    .line 397
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 398
    .line 399
    .line 400
    move-result-wide v6

    .line 401
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->c()Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 402
    .line 403
    .line 404
    move-result-object v8

    .line 405
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 406
    .line 407
    .line 408
    move-result-wide v9

    .line 409
    invoke-direct/range {v2 .. v10}, Lorg/xbet/sportgame/advanced/api/SportGameAdvancedScreenParams;-><init>(JZJLorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;J)V

    .line 410
    .line 411
    .line 412
    invoke-interface {v1, v2}, Lqz0/a;->a(Lorg/xbet/sportgame/advanced/api/SportGameAdvancedScreenParams;)Lq4/q;

    .line 413
    .line 414
    .line 415
    move-result-object v1

    .line 416
    return-object v1

    .line 417
    :cond_7
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 418
    .line 419
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 420
    .line 421
    .line 422
    throw v1

    .line 423
    :cond_8
    iget-object v1, v0, LmC0/a;->g:LGz0/a;

    .line 424
    .line 425
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 426
    .line 427
    .line 428
    move-result-wide v3

    .line 429
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 430
    .line 431
    .line 432
    move-result-wide v6

    .line 433
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 434
    .line 435
    .line 436
    move-result v5

    .line 437
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 438
    .line 439
    .line 440
    move-result-wide v8

    .line 441
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->c()Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 442
    .line 443
    .line 444
    move-result-object v10

    .line 445
    new-instance v2, Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;

    .line 446
    .line 447
    invoke-direct/range {v2 .. v10}, Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;-><init>(JZJJLorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;)V

    .line 448
    .line 449
    .line 450
    invoke-interface {v1, v2}, LGz0/a;->a(Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;)Lq4/q;

    .line 451
    .line 452
    .line 453
    move-result-object v1

    .line 454
    return-object v1

    .line 455
    :cond_9
    iget-object v1, v0, LmC0/a;->g:LGz0/a;

    .line 456
    .line 457
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->g()J

    .line 458
    .line 459
    .line 460
    move-result-wide v3

    .line 461
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->d()J

    .line 462
    .line 463
    .line 464
    move-result-wide v6

    .line 465
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->f()Z

    .line 466
    .line 467
    .line 468
    move-result v5

    .line 469
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->h()J

    .line 470
    .line 471
    .line 472
    move-result-wide v8

    .line 473
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->c()Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 474
    .line 475
    .line 476
    move-result-object v10

    .line 477
    new-instance v2, Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;

    .line 478
    .line 479
    invoke-direct/range {v2 .. v10}, Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;-><init>(JZJJLorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;)V

    .line 480
    .line 481
    .line 482
    invoke-interface {v1, v2}, LGz0/a;->a(Lorg/xbet/sportgame/classic/api/navigation/SportGameClassicScreenParams;)Lq4/q;

    .line 483
    .line 484
    .line 485
    move-result-object v1

    .line 486
    return-object v1
.end method

.method public final b(Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;)Z
    .locals 1

    .line 1
    invoke-virtual {p1}, Lorg/xbet/sportgame/navigation/api/presentation/GameScreenGeneralParams;->c()Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    sget-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->VIDEO:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 6
    .line 7
    if-ne p1, v0, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    return p1
.end method
