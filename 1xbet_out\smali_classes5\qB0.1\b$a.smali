.class public final LqB0/b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LqB0/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# static fields
.field public static final a:LqB0/b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LqB0/b;

    .line 2
    .line 3
    invoke-direct {v0}, LqB0/b;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LqB0/b$a;->a:LqB0/b;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static bridge synthetic a()LqB0/b;
    .locals 1

    .line 1
    sget-object v0, LqB0/b$a;->a:LqB0/b;

    return-object v0
.end method
