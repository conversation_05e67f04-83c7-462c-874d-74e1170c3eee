.class public final LTC0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LTC0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LTC0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LfR0/a;

.field public final b:LVC0/a;

.field public final c:LQN0/b;

.field public final d:LGL0/a;

.field public final e:LbL0/a;

.field public final f:LNF0/a;

.field public final g:LAP0/a;

.field public final h:LdM0/a;

.field public final i:LPH0/b;

.field public final j:LfX/b;

.field public final k:Lorg/xbet/remoteconfig/domain/usecases/i;

.field public final l:LEN0/f;

.field public final m:Li8/j;

.field public final n:Lc8/h;

.field public final o:Lz7/a;

.field public final p:Li8/m;

.field public final q:LTC0/a$b;


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LfR0/a;LGL0/a;LVC0/a;LQN0/b;LPH0/b;LbL0/a;LAP0/a;Lz7/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;Li8/m;Li8/j;Lc8/h;LNF0/a;LdM0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LTC0/a$b;->q:LTC0/a$b;

    .line 4
    iput-object p3, p0, LTC0/a$b;->a:LfR0/a;

    .line 5
    iput-object p5, p0, LTC0/a$b;->b:LVC0/a;

    .line 6
    iput-object p6, p0, LTC0/a$b;->c:LQN0/b;

    .line 7
    iput-object p4, p0, LTC0/a$b;->d:LGL0/a;

    .line 8
    iput-object p8, p0, LTC0/a$b;->e:LbL0/a;

    move-object/from16 p1, p17

    .line 9
    iput-object p1, p0, LTC0/a$b;->f:LNF0/a;

    .line 10
    iput-object p9, p0, LTC0/a$b;->g:LAP0/a;

    move-object/from16 p1, p18

    .line 11
    iput-object p1, p0, LTC0/a$b;->h:LdM0/a;

    .line 12
    iput-object p7, p0, LTC0/a$b;->i:LPH0/b;

    .line 13
    iput-object p13, p0, LTC0/a$b;->j:LfX/b;

    .line 14
    iput-object p12, p0, LTC0/a$b;->k:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 15
    iput-object p2, p0, LTC0/a$b;->l:LEN0/f;

    .line 16
    iput-object p15, p0, LTC0/a$b;->m:Li8/j;

    move-object/from16 p1, p16

    .line 17
    iput-object p1, p0, LTC0/a$b;->n:Lc8/h;

    .line 18
    iput-object p10, p0, LTC0/a$b;->o:Lz7/a;

    .line 19
    iput-object p14, p0, LTC0/a$b;->p:Li8/m;

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LfR0/a;LGL0/a;LVC0/a;LQN0/b;LPH0/b;LbL0/a;LAP0/a;Lz7/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;Li8/m;Li8/j;Lc8/h;LNF0/a;LdM0/a;LTC0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p18}, LTC0/a$b;-><init>(LQW0/c;LEN0/f;LfR0/a;LGL0/a;LVC0/a;LQN0/b;LPH0/b;LbL0/a;LAP0/a;Lz7/a;Lf8/g;Lorg/xbet/remoteconfig/domain/usecases/i;LfX/b;Li8/m;Li8/j;Lc8/h;LNF0/a;LdM0/a;)V

    return-void
.end method


# virtual methods
.method public a()LQD0/b;
    .locals 1

    .line 1
    iget-object v0, p0, LTC0/a$b;->l:LEN0/f;

    .line 2
    .line 3
    invoke-interface {v0}, LEN0/f;->f()LQD0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LQD0/b;

    .line 12
    .line 13
    return-object v0
.end method

.method public b()LQD0/d;
    .locals 1

    .line 1
    iget-object v0, p0, LTC0/a$b;->l:LEN0/f;

    .line 2
    .line 3
    invoke-interface {v0}, LEN0/f;->b()LQD0/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LQD0/d;

    .line 12
    .line 13
    return-object v0
.end method

.method public c()LQD0/e;
    .locals 1

    .line 1
    iget-object v0, p0, LTC0/a$b;->l:LEN0/f;

    .line 2
    .line 3
    invoke-interface {v0}, LEN0/f;->c()LQD0/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LQD0/e;

    .line 12
    .line 13
    return-object v0
.end method

.method public d()LQD0/c;
    .locals 1

    .line 1
    invoke-virtual {p0}, LTC0/a$b;->f()LPG0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public e()LDH0/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LTC0/a$b;->i()LUC0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final f()LPG0/a;
    .locals 4

    .line 1
    new-instance v0, LPG0/a;

    .line 2
    .line 3
    iget-object v1, p0, LTC0/a$b;->m:Li8/j;

    .line 4
    .line 5
    invoke-virtual {p0}, LTC0/a$b;->g()LPG0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iget-object v3, p0, LTC0/a$b;->p:Li8/m;

    .line 10
    .line 11
    invoke-direct {v0, v1, v2, v3}, LPG0/a;-><init>(Li8/j;LPG0/c;Li8/m;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public final g()LPG0/c;
    .locals 3

    .line 1
    new-instance v0, LPG0/c;

    .line 2
    .line 3
    invoke-virtual {p0}, LTC0/a$b;->h()LNG0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, LTC0/a$b;->o:Lz7/a;

    .line 8
    .line 9
    invoke-direct {v0, v1, v2}, LPG0/c;-><init>(LPD0/a;Lz7/a;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public final h()LNG0/a;
    .locals 2

    .line 1
    new-instance v0, LNG0/a;

    .line 2
    .line 3
    iget-object v1, p0, LTC0/a$b;->n:Lc8/h;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LNG0/a;-><init>(Lc8/h;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final i()LUC0/a;
    .locals 12

    .line 1
    new-instance v0, LUC0/a;

    .line 2
    .line 3
    iget-object v1, p0, LTC0/a$b;->a:LfR0/a;

    .line 4
    .line 5
    invoke-interface {v1}, LfR0/a;->a()LfR0/b;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    check-cast v1, LfR0/b;

    .line 14
    .line 15
    iget-object v2, p0, LTC0/a$b;->b:LVC0/a;

    .line 16
    .line 17
    invoke-interface {v2}, LVC0/a;->a()LVC0/b;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-static {v2}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    check-cast v2, LVC0/b;

    .line 26
    .line 27
    iget-object v3, p0, LTC0/a$b;->c:LQN0/b;

    .line 28
    .line 29
    invoke-interface {v3}, LQN0/b;->a()LQN0/c;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    invoke-static {v3}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    check-cast v3, LQN0/c;

    .line 38
    .line 39
    iget-object v4, p0, LTC0/a$b;->d:LGL0/a;

    .line 40
    .line 41
    invoke-interface {v4}, LGL0/a;->a()LGL0/b;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    invoke-static {v4}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v4

    .line 49
    check-cast v4, LGL0/b;

    .line 50
    .line 51
    iget-object v5, p0, LTC0/a$b;->e:LbL0/a;

    .line 52
    .line 53
    iget-object v6, p0, LTC0/a$b;->f:LNF0/a;

    .line 54
    .line 55
    iget-object v7, p0, LTC0/a$b;->g:LAP0/a;

    .line 56
    .line 57
    iget-object v8, p0, LTC0/a$b;->h:LdM0/a;

    .line 58
    .line 59
    iget-object v9, p0, LTC0/a$b;->i:LPH0/b;

    .line 60
    .line 61
    iget-object v10, p0, LTC0/a$b;->j:LfX/b;

    .line 62
    .line 63
    iget-object v11, p0, LTC0/a$b;->k:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 64
    .line 65
    invoke-direct/range {v0 .. v11}, LUC0/a;-><init>(LfR0/b;LVC0/b;LQN0/c;LGL0/b;LbL0/a;LNF0/a;LAP0/a;LdM0/a;LPH0/b;LfX/b;Lorg/xbet/remoteconfig/domain/usecases/i;)V

    .line 66
    .line 67
    .line 68
    return-object v0
.end method
