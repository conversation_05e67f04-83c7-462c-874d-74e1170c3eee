.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.CouponMakeBetViewModel$observeAuthState$1"
    f = "CouponMakeBetViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->m4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ln9/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Ln9/b;",
        "loginStateModel",
        "",
        "<anonymous>",
        "(Ln9/b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ln9/b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->invoke(Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln9/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Ln9/b;

    .line 14
    .line 15
    invoke-virtual {p1}, Ln9/b;->c()Z

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    if-eqz p1, :cond_0

    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 22
    .line 23
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->I3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 27
    .line 28
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->M3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 32
    .line 33
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->P3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 37
    .line 38
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->E3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$b;

    .line 43
    .line 44
    const/high16 v1, 0x3f800000    # 1.0f

    .line 45
    .line 46
    invoke-direct {v0, v1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$b;-><init>(F)V

    .line 47
    .line 48
    .line 49
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_0
    iget-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$observeAuthState$1;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;->E3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel;)Lkotlinx/coroutines/flow/V;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    sget-object v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$a;->a:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/CouponMakeBetViewModel$c$a;

    .line 60
    .line 61
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 65
    .line 66
    return-object p1

    .line 67
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 70
    .line 71
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    throw p1
.end method
