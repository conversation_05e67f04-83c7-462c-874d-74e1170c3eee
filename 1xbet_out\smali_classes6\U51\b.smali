.class public final LU51/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LV51/d$a;",
        "LP51/b;",
        "a",
        "(LV51/d$a;)LP51/b;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LV51/d$a;)LP51/b;
    .locals 11
    .param p0    # LV51/d$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LV51/d$a;->c()Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    if-eqz v0, :cond_4

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v4

    .line 13
    invoke-virtual {p0}, LV51/d$a;->b()Ljava/lang/Long;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    if-eqz v0, :cond_3

    .line 18
    .line 19
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 20
    .line 21
    .line 22
    move-result-wide v5

    .line 23
    invoke-virtual {p0}, LV51/d$a;->c()Ljava/lang/Integer;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    sget-object v3, Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;->Companion:Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel$a;

    .line 28
    .line 29
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    invoke-virtual {v3, v0}, Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel$a;->a(I)Lorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;

    .line 34
    .line 35
    .line 36
    move-result-object v7

    .line 37
    invoke-virtual {p0}, LV51/d$a;->a()Ljava/lang/Integer;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    if-eqz v0, :cond_2

    .line 42
    .line 43
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 44
    .line 45
    .line 46
    move-result v8

    .line 47
    invoke-virtual {p0}, LV51/d$a;->d()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    const-string v1, ""

    .line 52
    .line 53
    if-nez v0, :cond_0

    .line 54
    .line 55
    move-object v9, v1

    .line 56
    goto :goto_0

    .line 57
    :cond_0
    move-object v9, v0

    .line 58
    :goto_0
    invoke-virtual {p0}, LV51/d$a;->e()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    if-nez p0, :cond_1

    .line 63
    .line 64
    move-object v10, v1

    .line 65
    goto :goto_1

    .line 66
    :cond_1
    move-object v10, p0

    .line 67
    :goto_1
    new-instance v3, LP51/b;

    .line 68
    .line 69
    invoke-direct/range {v3 .. v10}, LP51/b;-><init>(IJLorg/xbet/vip_cashback/api/domain/models/VipCashbackLevel;ILjava/lang/String;Ljava/lang/String;)V

    .line 70
    .line 71
    .line 72
    return-object v3

    .line 73
    :cond_2
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 74
    .line 75
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    throw p0

    .line 79
    :cond_3
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 80
    .line 81
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 82
    .line 83
    .line 84
    throw p0

    .line 85
    :cond_4
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 86
    .line 87
    invoke-direct {p0, v2, v1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    .line 89
    .line 90
    throw p0
.end method
