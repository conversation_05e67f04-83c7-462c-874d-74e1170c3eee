.class public final Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "Lw40/a;",
        "clickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "g",
        "(LUX0/k;Lw40/a;)LA4/c;",
        "impl_games_section_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->m(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lw40/a;LHZ0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->k(Lw40/a;LHZ0/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LB40/a;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LB40/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->l(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;Lw40/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->j(LB4/a;Lw40/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lw40/a;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt;->i(Lw40/a;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(LUX0/k;Lw40/a;)LA4/c;
    .locals 3
    .param p0    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lw40/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "Lw40/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LD40/a;

    .line 2
    .line 3
    invoke-direct {v0}, LD40/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LD40/b;

    .line 7
    .line 8
    invoke-direct {v1, p1, p0}, LD40/b;-><init>(Lw40/a;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt$getOneXGameCategoryListItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt$getOneXGameCategoryListItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt$getOneXGameCategoryListItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/games_section/feature/game_categories/impl/presentation/adapterdelegates/OneXGameCategoryListItemAdapterDelegateKt$getOneXGameCategoryListItemAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LB40/a;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LB40/a;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LB40/a;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Lw40/a;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LD40/c;

    .line 2
    .line 3
    invoke-direct {v0, p2, p0}, LD40/c;-><init>(LB4/a;Lw40/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p2, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    new-instance p0, LD40/d;

    .line 10
    .line 11
    invoke-direct {p0, p1, p2}, LD40/d;-><init>(LUX0/k;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p2, p0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    new-instance p0, LD40/e;

    .line 18
    .line 19
    invoke-direct {p0, p1, p2}, LD40/e;-><init>(LUX0/k;LB4/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p2, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method

.method public static final j(LB4/a;Lw40/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, LB40/a;

    .line 6
    .line 7
    iget-object p2, p2, LB40/a;->b:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lz40/a$a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lz40/a$a;->d()LHZ0/f;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    const/4 v1, 0x0

    .line 20
    const/4 v2, 0x2

    .line 21
    invoke-static {p2, v0, v1, v2, v1}, Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;->setItems$default(Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;LHZ0/f;Ljava/lang/Runnable;ILjava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    check-cast p0, LB40/a;

    .line 29
    .line 30
    iget-object p0, p0, LB40/a;->b:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 31
    .line 32
    new-instance p2, LD40/f;

    .line 33
    .line 34
    invoke-direct {p2, p1}, LD40/f;-><init>(Lw40/a;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0, p2}, Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 38
    .line 39
    .line 40
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 41
    .line 42
    return-object p0
.end method

.method public static final k(Lw40/a;LHZ0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LHZ0/a;->e()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-interface {p0, p1}, Lw40/a;->a2(I)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final l(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LB40/a;

    .line 14
    .line 15
    iget-object p1, p1, LB40/a;->b:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final m(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LB40/a;

    .line 14
    .line 15
    iget-object p1, p1, LB40/a;->b:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method
