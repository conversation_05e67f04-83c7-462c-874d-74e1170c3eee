.class public final Lorg/xbet/themesettings/impl/presentation/timepicker/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/themesettings/impl/presentation/timepicker/r;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LmT0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LgT0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/m;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/g;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/t;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LhT0/r;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LmT0/c;",
            ">;",
            "LBc/a<",
            "LgT0/a;",
            ">;",
            "LBc/a<",
            "LhT0/a;",
            ">;",
            "LBc/a<",
            "Li8/m;",
            ">;",
            "LBc/a<",
            "LhT0/g;",
            ">;",
            "LBc/a<",
            "LhT0/t;",
            ">;",
            "LBc/a<",
            "LhT0/r;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->h:LBc/a;

    .line 19
    .line 20
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/themesettings/impl/presentation/timepicker/s;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LmT0/c;",
            ">;",
            "LBc/a<",
            "LgT0/a;",
            ">;",
            "LBc/a<",
            "LhT0/a;",
            ">;",
            "LBc/a<",
            "Li8/m;",
            ">;",
            "LBc/a<",
            "LhT0/g;",
            ">;",
            "LBc/a<",
            "LhT0/t;",
            ">;",
            "LBc/a<",
            "LhT0/r;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;)",
            "Lorg/xbet/themesettings/impl/presentation/timepicker/s;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    move-object v7, p6

    .line 10
    move-object/from16 v8, p7

    .line 11
    .line 12
    invoke-direct/range {v0 .. v8}, Lorg/xbet/themesettings/impl/presentation/timepicker/s;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static c(LmT0/c;LgT0/a;LhT0/a;Li8/m;LhT0/g;LhT0/t;LhT0/r;Z)Lorg/xbet/themesettings/impl/presentation/timepicker/r;
    .locals 9

    .line 1
    new-instance v0, Lorg/xbet/themesettings/impl/presentation/timepicker/r;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    move-object v7, p6

    .line 10
    move/from16 v8, p7

    .line 11
    .line 12
    invoke-direct/range {v0 .. v8}, Lorg/xbet/themesettings/impl/presentation/timepicker/r;-><init>(LmT0/c;LgT0/a;LhT0/a;Li8/m;LhT0/g;LhT0/t;LhT0/r;Z)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/themesettings/impl/presentation/timepicker/r;
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, LmT0/c;

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->b:LBc/a;

    .line 11
    .line 12
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    move-object v2, v0

    .line 17
    check-cast v2, LgT0/a;

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->c:LBc/a;

    .line 20
    .line 21
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    move-object v3, v0

    .line 26
    check-cast v3, LhT0/a;

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->d:LBc/a;

    .line 29
    .line 30
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    move-object v4, v0

    .line 35
    check-cast v4, Li8/m;

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->e:LBc/a;

    .line 38
    .line 39
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    move-object v5, v0

    .line 44
    check-cast v5, LhT0/g;

    .line 45
    .line 46
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->f:LBc/a;

    .line 47
    .line 48
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    move-object v6, v0

    .line 53
    check-cast v6, LhT0/t;

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->g:LBc/a;

    .line 56
    .line 57
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    move-object v7, v0

    .line 62
    check-cast v7, LhT0/r;

    .line 63
    .line 64
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->h:LBc/a;

    .line 65
    .line 66
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    check-cast v0, Ljava/lang/Boolean;

    .line 71
    .line 72
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 73
    .line 74
    .line 75
    move-result v8

    .line 76
    invoke-static/range {v1 .. v8}, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->c(LmT0/c;LgT0/a;LhT0/a;Li8/m;LhT0/g;LhT0/t;LhT0/r;Z)Lorg/xbet/themesettings/impl/presentation/timepicker/r;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themesettings/impl/presentation/timepicker/s;->b()Lorg/xbet/themesettings/impl/presentation/timepicker/r;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
