.class public final LtJ0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LtJ0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LtJ0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LtJ0/a$b$a;,
        LtJ0/a$b$b;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/presentation/delegates/TwoTeamHeaderDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/PlayersStatisticViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/i;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/SelectorsBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/e;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/InfoBottomSheetViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LtJ0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqJ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LqJ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/player/impl/player/players_statistic/data/repositories/PlayersStatisticRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/m;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/g;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/o;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/k;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/q;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwJ0/a;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LFN0/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTn/a;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/GetSportUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/statistic/statistic_core/domain/usecases/l;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LtJ0/a$b;->a:LtJ0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p16}, LtJ0/a$b;->d(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V

    .line 5
    invoke-virtual/range {p0 .. p16}, LtJ0/a$b;->e(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;LtJ0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p16}, LtJ0/a$b;-><init>(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtJ0/a$b;->g(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtJ0/a$b;->h(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public c(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LtJ0/a$b;->f(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final d(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V
    .locals 0

    .line 1
    new-instance p6, LtJ0/a$b$a;

    .line 2
    .line 3
    invoke-direct {p6, p1}, LtJ0/a$b$a;-><init>(LQW0/c;)V

    .line 4
    .line 5
    .line 6
    iput-object p6, p0, LtJ0/a$b;->b:Ldagger/internal/h;

    .line 7
    .line 8
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    iput-object p1, p0, LtJ0/a$b;->c:Ldagger/internal/h;

    .line 13
    .line 14
    invoke-static {p1}, LqJ0/d;->a(LBc/a;)LqJ0/d;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, LtJ0/a$b;->d:Ldagger/internal/h;

    .line 19
    .line 20
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iput-object p1, p0, LtJ0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LtJ0/a$b;->f:Ldagger/internal/h;

    .line 31
    .line 32
    iget-object p6, p0, LtJ0/a$b;->b:Ldagger/internal/h;

    .line 33
    .line 34
    iget-object p9, p0, LtJ0/a$b;->d:Ldagger/internal/h;

    .line 35
    .line 36
    iget-object p10, p0, LtJ0/a$b;->e:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {p6, p9, p10, p1}, Lorg/xbet/statistic/player/impl/player/players_statistic/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic/data/repositories/a;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, LwJ0/n;->a(LBc/a;)LwJ0/n;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LtJ0/a$b;->h:Ldagger/internal/h;

    .line 49
    .line 50
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 51
    .line 52
    invoke-static {p1}, LwJ0/h;->a(LBc/a;)LwJ0/h;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iput-object p1, p0, LtJ0/a$b;->i:Ldagger/internal/h;

    .line 57
    .line 58
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 59
    .line 60
    invoke-static {p1}, LwJ0/d;->a(LBc/a;)LwJ0/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, LtJ0/a$b;->j:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p1}, LwJ0/p;->a(LBc/a;)LwJ0/p;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, LtJ0/a$b;->k:Ldagger/internal/h;

    .line 73
    .line 74
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 75
    .line 76
    invoke-static {p1}, LwJ0/l;->a(LBc/a;)LwJ0/l;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    iput-object p1, p0, LtJ0/a$b;->l:Ldagger/internal/h;

    .line 81
    .line 82
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 83
    .line 84
    invoke-static {p1}, LwJ0/r;->a(LBc/a;)LwJ0/r;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LtJ0/a$b;->m:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object p1, p0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static {p1}, LwJ0/b;->a(LBc/a;)LwJ0/b;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iput-object p1, p0, LtJ0/a$b;->n:Ldagger/internal/h;

    .line 97
    .line 98
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    iput-object p1, p0, LtJ0/a$b;->o:Ldagger/internal/h;

    .line 103
    .line 104
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    iput-object p1, p0, LtJ0/a$b;->p:Ldagger/internal/h;

    .line 109
    .line 110
    invoke-static {p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    iput-object p1, p0, LtJ0/a$b;->q:Ldagger/internal/h;

    .line 115
    .line 116
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    iput-object p1, p0, LtJ0/a$b;->r:Ldagger/internal/h;

    .line 121
    .line 122
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    iput-object p1, p0, LtJ0/a$b;->s:Ldagger/internal/h;

    .line 127
    .line 128
    new-instance p1, LtJ0/a$b$b;

    .line 129
    .line 130
    invoke-direct {p1, p2}, LtJ0/a$b$b;-><init>(LEN0/f;)V

    .line 131
    .line 132
    .line 133
    iput-object p1, p0, LtJ0/a$b;->t:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/g;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/g;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    iput-object p1, p0, LtJ0/a$b;->u:Ldagger/internal/h;

    .line 140
    .line 141
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    iput-object p1, p0, LtJ0/a$b;->v:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object p2, p0, LtJ0/a$b;->b:Ldagger/internal/h;

    .line 148
    .line 149
    invoke-static {p2, p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/h;->a(LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/h;

    .line 150
    .line 151
    .line 152
    move-result-object p1

    .line 153
    iput-object p1, p0, LtJ0/a$b;->w:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object p1, p0, LtJ0/a$b;->t:Ldagger/internal/h;

    .line 156
    .line 157
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/j;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/j;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    iput-object p1, p0, LtJ0/a$b;->x:Ldagger/internal/h;

    .line 162
    .line 163
    iget-object p1, p0, LtJ0/a$b;->t:Ldagger/internal/h;

    .line 164
    .line 165
    invoke-static {p1}, Lorg/xbet/statistic/statistic_core/domain/usecases/m;->a(LBc/a;)Lorg/xbet/statistic/statistic_core/domain/usecases/m;

    .line 166
    .line 167
    .line 168
    move-result-object p1

    .line 169
    iput-object p1, p0, LtJ0/a$b;->y:Ldagger/internal/h;

    .line 170
    .line 171
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    iput-object p1, p0, LtJ0/a$b;->z:Ldagger/internal/h;

    .line 176
    .line 177
    return-void
.end method

.method public final e(LQW0/c;LEN0/f;LwX0/c;Lorg/xbet/ui_common/utils/M;LHX0/e;Li8/l;LSX0/a;LTn/a;LqJ0/a;Lorg/xbet/onexdatabase/OnexDatabase;Lf8/g;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lc8/h;)V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LtJ0/a$b;->u:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object v2, v0, LtJ0/a$b;->w:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object v3, v0, LtJ0/a$b;->x:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object v4, v0, LtJ0/a$b;->r:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object v5, v0, LtJ0/a$b;->y:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v6, v0, LtJ0/a$b;->z:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object v7, v0, LtJ0/a$b;->p:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static/range {v1 .. v7}, Lorg/xbet/statistic/statistic_core/presentation/delegates/o;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/statistic_core/presentation/delegates/o;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iput-object v1, v0, LtJ0/a$b;->A:Ldagger/internal/h;

    .line 22
    .line 23
    invoke-static/range {p12 .. p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iput-object v1, v0, LtJ0/a$b;->B:Ldagger/internal/h;

    .line 28
    .line 29
    invoke-static/range {p6 .. p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    iput-object v1, v0, LtJ0/a$b;->C:Ldagger/internal/h;

    .line 34
    .line 35
    iget-object v2, v0, LtJ0/a$b;->h:Ldagger/internal/h;

    .line 36
    .line 37
    iget-object v3, v0, LtJ0/a$b;->i:Ldagger/internal/h;

    .line 38
    .line 39
    iget-object v4, v0, LtJ0/a$b;->j:Ldagger/internal/h;

    .line 40
    .line 41
    iget-object v5, v0, LtJ0/a$b;->k:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v6, v0, LtJ0/a$b;->l:Ldagger/internal/h;

    .line 44
    .line 45
    iget-object v7, v0, LtJ0/a$b;->m:Ldagger/internal/h;

    .line 46
    .line 47
    iget-object v8, v0, LtJ0/a$b;->n:Ldagger/internal/h;

    .line 48
    .line 49
    iget-object v9, v0, LtJ0/a$b;->o:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object v10, v0, LtJ0/a$b;->p:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object v11, v0, LtJ0/a$b;->q:Ldagger/internal/h;

    .line 54
    .line 55
    iget-object v12, v0, LtJ0/a$b;->r:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object v13, v0, LtJ0/a$b;->s:Ldagger/internal/h;

    .line 58
    .line 59
    iget-object v14, v0, LtJ0/a$b;->A:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object v15, v0, LtJ0/a$b;->B:Ldagger/internal/h;

    .line 62
    .line 63
    move-object/from16 v16, v1

    .line 64
    .line 65
    iget-object v1, v0, LtJ0/a$b;->b:Ldagger/internal/h;

    .line 66
    .line 67
    move-object/from16 v17, v1

    .line 68
    .line 69
    invoke-static/range {v2 .. v17}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/d;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/d;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, v0, LtJ0/a$b;->D:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object v1, v0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static {v1}, LwJ0/j;->a(LBc/a;)LwJ0/j;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    iput-object v1, v0, LtJ0/a$b;->E:Ldagger/internal/h;

    .line 82
    .line 83
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    iput-object v1, v0, LtJ0/a$b;->F:Ldagger/internal/h;

    .line 88
    .line 89
    iget-object v2, v0, LtJ0/a$b;->E:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object v3, v0, LtJ0/a$b;->j:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static {v2, v3, v1}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/e;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/e;

    .line 94
    .line 95
    .line 96
    move-result-object v1

    .line 97
    iput-object v1, v0, LtJ0/a$b;->G:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object v1, v0, LtJ0/a$b;->g:Ldagger/internal/h;

    .line 100
    .line 101
    invoke-static {v1}, LwJ0/f;->a(LBc/a;)LwJ0/f;

    .line 102
    .line 103
    .line 104
    move-result-object v1

    .line 105
    iput-object v1, v0, LtJ0/a$b;->H:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v2, v0, LtJ0/a$b;->j:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v3, v0, LtJ0/a$b;->F:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static {v1, v2, v3}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/a;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    iput-object v1, v0, LtJ0/a$b;->I:Ldagger/internal/h;

    .line 116
    .line 117
    return-void
.end method

.method public final f(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtJ0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/c;->a(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/InfoBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final g(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtJ0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/f;->a(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/fragments/PlayersStatisticFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final h(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;)Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LtJ0/a$b;->j()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/f;->a(Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/dialogs/SelectorsBottomSheetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final i()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x3

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/PlayersStatisticViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LtJ0/a$b;->D:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/SelectorsBottomSheetViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LtJ0/a$b;->G:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    const-class v1, Lorg/xbet/statistic/player/impl/player/players_statistic/presentation/viewmodels/InfoBottomSheetViewModel;

    .line 23
    .line 24
    iget-object v2, p0, LtJ0/a$b;->I:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    return-object v0
.end method

.method public final j()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LtJ0/a$b;->i()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
