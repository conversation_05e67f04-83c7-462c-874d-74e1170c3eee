.class public final LhD0/h$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LhD0/h;->b(Ltc1/a;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/n<",
        "Landroidx/compose/foundation/layout/Y;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Landroidx/compose/runtime/r1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/r1<",
            "LjD0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Landroidx/compose/runtime/h0;

.field public final synthetic c:Ltc1/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ltc1/a<",
            "LjD0/c;",
            "LjD0/i;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/compose/runtime/r1;Landroidx/compose/runtime/h0;Ltc1/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/runtime/r1<",
            "LjD0/i;",
            ">;",
            "Landroidx/compose/runtime/h0;",
            "Ltc1/a<",
            "LjD0/c;",
            "LjD0/i;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LhD0/h$b;->a:Landroidx/compose/runtime/r1;

    .line 2
    .line 3
    iput-object p2, p0, LhD0/h$b;->b:Landroidx/compose/runtime/h0;

    .line 4
    .line 5
    iput-object p3, p0, LhD0/h$b;->c:Ltc1/a;

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic a(Ltc1/a;LNN0/h;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LhD0/h$b;->g(Ltc1/a;LNN0/h;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LhD0/h$b;->k(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LhD0/h$b;->f(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LhD0/h$b;->j(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Landroidx/compose/runtime/h0;Lt0/t;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p1}, Lt0/t;->j()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const-wide v2, 0xffffffffL

    .line 6
    .line 7
    .line 8
    .line 9
    .line 10
    and-long/2addr v0, v2

    .line 11
    long-to-int p1, v0

    .line 12
    invoke-static {p0, p1}, LhD0/h;->g(Landroidx/compose/runtime/h0;I)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final g(Ltc1/a;LNN0/h;)Lkotlin/Unit;
    .locals 1

    .line 1
    instance-of v0, p1, LjD0/g;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LjD0/g;

    .line 6
    .line 7
    invoke-static {p1}, LjD0/b;->b(LjD0/g;)LjD0/g;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-static {p1}, LjD0/b;->a(LjD0/g;)LjD0/b;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-virtual {p0, p1}, Ltc1/a;->o3(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final j(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p1

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, LjD0/i;

    .line 16
    .line 17
    invoke-virtual {p0}, LjD0/i;->c()Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$b;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 28
    .line 29
    .line 30
    const/4 p0, 0x0

    .line 31
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method public static final k(Landroidx/compose/runtime/r1;Landroid/content/Context;)Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p1

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, LjD0/i;

    .line 16
    .line 17
    invoke-virtual {p0}, LjD0/i;->c()Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    check-cast p0, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$c;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$c;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    invoke-virtual {v0, p0}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 28
    .line 29
    .line 30
    const/4 p0, 0x0

    .line 31
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method


# virtual methods
.method public final e(Landroidx/compose/foundation/layout/Y;Landroidx/compose/runtime/j;I)V
    .locals 35

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    move-object/from16 v4, p2

    .line 6
    .line 7
    and-int/lit8 v2, p3, 0x6

    .line 8
    .line 9
    if-nez v2, :cond_1

    .line 10
    .line 11
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    if-eqz v2, :cond_0

    .line 16
    .line 17
    const/4 v2, 0x4

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 v2, 0x2

    .line 20
    :goto_0
    or-int v2, p3, v2

    .line 21
    .line 22
    goto :goto_1

    .line 23
    :cond_1
    move/from16 v2, p3

    .line 24
    .line 25
    :goto_1
    and-int/lit8 v3, v2, 0x13

    .line 26
    .line 27
    const/16 v5, 0x12

    .line 28
    .line 29
    if-ne v3, v5, :cond_3

    .line 30
    .line 31
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    if-nez v3, :cond_2

    .line 36
    .line 37
    goto :goto_2

    .line 38
    :cond_2
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 39
    .line 40
    .line 41
    return-void

    .line 42
    :cond_3
    :goto_2
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-eqz v3, :cond_4

    .line 47
    .line 48
    const/4 v3, -0x1

    .line 49
    const-string v5, "org.xbet.statistic.cycling.impl.cycling_menu.presentation.components.TabsWidgetsCyclingMenuComponent.<anonymous> (TabsWidgetsCyclingMenuComponent.kt:81)"

    .line 50
    .line 51
    const v6, -0x7bc0636b

    .line 52
    .line 53
    .line 54
    invoke-static {v6, v2, v3, v5}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 55
    .line 56
    .line 57
    :cond_4
    sget-object v2, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 58
    .line 59
    const/4 v3, 0x0

    .line 60
    const/4 v5, 0x1

    .line 61
    const/4 v6, 0x0

    .line 62
    invoke-static {v2, v3, v5, v6}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 63
    .line 64
    .line 65
    move-result-object v7

    .line 66
    invoke-static {v7, v1}, Landroidx/compose/foundation/layout/WindowInsetsPaddingKt;->a(Landroidx/compose/ui/l;Landroidx/compose/foundation/layout/Y;)Landroidx/compose/ui/l;

    .line 67
    .line 68
    .line 69
    move-result-object v7

    .line 70
    iget-object v8, v0, LhD0/h$b;->a:Landroidx/compose/runtime/r1;

    .line 71
    .line 72
    iget-object v9, v0, LhD0/h$b;->b:Landroidx/compose/runtime/h0;

    .line 73
    .line 74
    iget-object v10, v0, LhD0/h$b;->c:Ltc1/a;

    .line 75
    .line 76
    sget-object v21, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 77
    .line 78
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 79
    .line 80
    .line 81
    move-result-object v11

    .line 82
    const/4 v12, 0x0

    .line 83
    invoke-static {v11, v12}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 84
    .line 85
    .line 86
    move-result-object v11

    .line 87
    invoke-static {v4, v12}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 88
    .line 89
    .line 90
    move-result v13

    .line 91
    invoke-interface {v4}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 92
    .line 93
    .line 94
    move-result-object v14

    .line 95
    invoke-static {v4, v7}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 96
    .line 97
    .line 98
    move-result-object v7

    .line 99
    sget-object v22, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 100
    .line 101
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 102
    .line 103
    .line 104
    move-result-object v15

    .line 105
    invoke-interface {v4}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 106
    .line 107
    .line 108
    move-result-object v16

    .line 109
    invoke-static/range {v16 .. v16}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 110
    .line 111
    .line 112
    move-result v16

    .line 113
    if-nez v16, :cond_5

    .line 114
    .line 115
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 116
    .line 117
    .line 118
    :cond_5
    invoke-interface {v4}, Landroidx/compose/runtime/j;->l()V

    .line 119
    .line 120
    .line 121
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 122
    .line 123
    .line 124
    move-result v16

    .line 125
    if-eqz v16, :cond_6

    .line 126
    .line 127
    invoke-interface {v4, v15}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 128
    .line 129
    .line 130
    goto :goto_3

    .line 131
    :cond_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->h()V

    .line 132
    .line 133
    .line 134
    :goto_3
    invoke-static {v4}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 135
    .line 136
    .line 137
    move-result-object v15

    .line 138
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 139
    .line 140
    .line 141
    move-result-object v12

    .line 142
    invoke-static {v15, v11, v12}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 143
    .line 144
    .line 145
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 146
    .line 147
    .line 148
    move-result-object v11

    .line 149
    invoke-static {v15, v14, v11}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 150
    .line 151
    .line 152
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 153
    .line 154
    .line 155
    move-result-object v11

    .line 156
    invoke-interface {v15}, Landroidx/compose/runtime/j;->B()Z

    .line 157
    .line 158
    .line 159
    move-result v12

    .line 160
    if-nez v12, :cond_7

    .line 161
    .line 162
    invoke-interface {v15}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 163
    .line 164
    .line 165
    move-result-object v12

    .line 166
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 167
    .line 168
    .line 169
    move-result-object v14

    .line 170
    invoke-static {v12, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 171
    .line 172
    .line 173
    move-result v12

    .line 174
    if-nez v12, :cond_8

    .line 175
    .line 176
    :cond_7
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 177
    .line 178
    .line 179
    move-result-object v12

    .line 180
    invoke-interface {v15, v12}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 181
    .line 182
    .line 183
    invoke-static {v13}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 184
    .line 185
    .line 186
    move-result-object v12

    .line 187
    invoke-interface {v15, v12, v11}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 188
    .line 189
    .line 190
    :cond_8
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 191
    .line 192
    .line 193
    move-result-object v11

    .line 194
    invoke-static {v15, v7, v11}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 195
    .line 196
    .line 197
    sget-object v7, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 198
    .line 199
    invoke-static {v2, v3, v5, v6}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 200
    .line 201
    .line 202
    move-result-object v7

    .line 203
    sget-object v23, LA11/a;->a:LA11/a;

    .line 204
    .line 205
    invoke-virtual/range {v23 .. v23}, LA11/a;->p0()F

    .line 206
    .line 207
    .line 208
    move-result v11

    .line 209
    invoke-static {v9}, LhD0/h;->f(Landroidx/compose/runtime/h0;)I

    .line 210
    .line 211
    .line 212
    move-result v12

    .line 213
    invoke-static {v12}, Lorg/xbet/ui_common/utils/ExtensionsKt;->v(I)I

    .line 214
    .line 215
    .line 216
    move-result v12

    .line 217
    int-to-float v12, v12

    .line 218
    invoke-static {v12}, Lt0/i;->k(F)F

    .line 219
    .line 220
    .line 221
    move-result v12

    .line 222
    invoke-interface {v1}, Landroidx/compose/foundation/layout/Y;->d()F

    .line 223
    .line 224
    .line 225
    move-result v13

    .line 226
    add-float/2addr v12, v13

    .line 227
    invoke-static {v12}, Lt0/i;->k(F)F

    .line 228
    .line 229
    .line 230
    move-result v12

    .line 231
    invoke-virtual/range {v23 .. v23}, LA11/a;->D1()F

    .line 232
    .line 233
    .line 234
    move-result v13

    .line 235
    add-float/2addr v12, v13

    .line 236
    invoke-static {v12}, Lt0/i;->k(F)F

    .line 237
    .line 238
    .line 239
    move-result v12

    .line 240
    invoke-static {v7, v11, v12}, Landroidx/compose/foundation/layout/SizeKt;->j(Landroidx/compose/ui/l;FF)Landroidx/compose/ui/l;

    .line 241
    .line 242
    .line 243
    move-result-object v7

    .line 244
    sget-object v11, Landroidx/compose/ui/layout/h;->a:Landroidx/compose/ui/layout/h$a;

    .line 245
    .line 246
    invoke-virtual {v11}, Landroidx/compose/ui/layout/h$a;->a()Landroidx/compose/ui/layout/h;

    .line 247
    .line 248
    .line 249
    move-result-object v12

    .line 250
    new-instance v11, Ln8/a;

    .line 251
    .line 252
    invoke-direct {v11}, Ln8/a;-><init>()V

    .line 253
    .line 254
    .line 255
    invoke-interface {v8}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 256
    .line 257
    .line 258
    move-result-object v13

    .line 259
    check-cast v13, LjD0/i;

    .line 260
    .line 261
    invoke-virtual {v13}, LjD0/i;->a()LNN0/a;

    .line 262
    .line 263
    .line 264
    move-result-object v13

    .line 265
    invoke-static {v13}, LIN0/D;->b(LNN0/a;)Ljava/lang/String;

    .line 266
    .line 267
    .line 268
    move-result-object v13

    .line 269
    invoke-virtual {v11, v13}, Ln8/a;->c(Ljava/lang/String;)Ln8/a;

    .line 270
    .line 271
    .line 272
    move-result-object v11

    .line 273
    invoke-virtual {v11}, Ln8/a;->a()Ljava/lang/String;

    .line 274
    .line 275
    .line 276
    move-result-object v11

    .line 277
    sget-object v13, Landroidx/compose/ui/graphics/w0;->b:Landroidx/compose/ui/graphics/w0$a;

    .line 278
    .line 279
    sget-object v14, Landroidx/compose/ui/graphics/v0;->b:Landroidx/compose/ui/graphics/v0$a;

    .line 280
    .line 281
    invoke-virtual {v14}, Landroidx/compose/ui/graphics/v0$a;->a()J

    .line 282
    .line 283
    .line 284
    move-result-wide v24

    .line 285
    const/16 v30, 0xe

    .line 286
    .line 287
    const/16 v31, 0x0

    .line 288
    .line 289
    const v26, 0x3ecccccd

    .line 290
    .line 291
    .line 292
    const/16 v27, 0x0

    .line 293
    .line 294
    const/16 v28, 0x0

    .line 295
    .line 296
    const/16 v29, 0x0

    .line 297
    .line 298
    invoke-static/range {v24 .. v31}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 299
    .line 300
    .line 301
    move-result-wide v14

    .line 302
    sget-object v16, Landroidx/compose/ui/graphics/e0;->a:Landroidx/compose/ui/graphics/e0$a;

    .line 303
    .line 304
    invoke-virtual/range {v16 .. v16}, Landroidx/compose/ui/graphics/e0$a;->e()I

    .line 305
    .line 306
    .line 307
    move-result v3

    .line 308
    invoke-virtual {v13, v14, v15, v3}, Landroidx/compose/ui/graphics/w0$a;->b(JI)Landroidx/compose/ui/graphics/w0;

    .line 309
    .line 310
    .line 311
    move-result-object v14

    .line 312
    sget v3, Lpb/g;->statistic_back:I

    .line 313
    .line 314
    const/4 v13, 0x0

    .line 315
    invoke-static {v3, v4, v13}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 316
    .line 317
    .line 318
    move-result-object v3

    .line 319
    sget v15, Lpb/g;->statistic_back:I

    .line 320
    .line 321
    invoke-static {v15, v4, v13}, Lm0/d;->c(ILandroidx/compose/runtime/j;I)Landroidx/compose/ui/graphics/painter/Painter;

    .line 322
    .line 323
    .line 324
    move-result-object v15

    .line 325
    const/16 v19, 0x6

    .line 326
    .line 327
    const/16 v20, 0x6be0

    .line 328
    .line 329
    move-object v5, v3

    .line 330
    const/16 v16, 0x1

    .line 331
    .line 332
    const/4 v3, 0x0

    .line 333
    move-object v4, v7

    .line 334
    const/4 v7, 0x0

    .line 335
    move-object/from16 v18, v8

    .line 336
    .line 337
    const/4 v8, 0x0

    .line 338
    move-object/from16 v24, v9

    .line 339
    .line 340
    const/4 v9, 0x0

    .line 341
    move-object/from16 v25, v10

    .line 342
    .line 343
    const/4 v10, 0x0

    .line 344
    move-object/from16 v26, v2

    .line 345
    .line 346
    move-object v2, v11

    .line 347
    const/4 v11, 0x0

    .line 348
    const/16 v27, 0x0

    .line 349
    .line 350
    const/4 v13, 0x0

    .line 351
    move-object/from16 v28, v6

    .line 352
    .line 353
    move-object v6, v15

    .line 354
    const/4 v15, 0x0

    .line 355
    const/16 v29, 0x1

    .line 356
    .line 357
    const/16 v16, 0x0

    .line 358
    .line 359
    move-object/from16 v30, v18

    .line 360
    .line 361
    const/16 v18, 0x30

    .line 362
    .line 363
    move-object/from16 v17, p2

    .line 364
    .line 365
    move-object/from16 v32, v24

    .line 366
    .line 367
    move-object/from16 v33, v25

    .line 368
    .line 369
    move-object/from16 v0, v26

    .line 370
    .line 371
    const/4 v1, 0x0

    .line 372
    invoke-static/range {v2 .. v20}, Lcoil3/compose/r;->b(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/e;Landroidx/compose/ui/layout/h;FLandroidx/compose/ui/graphics/w0;IZLandroidx/compose/runtime/j;III)V

    .line 373
    .line 374
    .line 375
    move-object/from16 v4, v17

    .line 376
    .line 377
    sget-object v2, Landroidx/compose/foundation/layout/Arrangement;->a:Landroidx/compose/foundation/layout/Arrangement;

    .line 378
    .line 379
    invoke-virtual {v2}, Landroidx/compose/foundation/layout/Arrangement;->h()Landroidx/compose/foundation/layout/Arrangement$m;

    .line 380
    .line 381
    .line 382
    move-result-object v2

    .line 383
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/e$a;->k()Landroidx/compose/ui/e$b;

    .line 384
    .line 385
    .line 386
    move-result-object v3

    .line 387
    invoke-static {v2, v3, v4, v1}, Landroidx/compose/foundation/layout/k;->a(Landroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/layout/J;

    .line 388
    .line 389
    .line 390
    move-result-object v2

    .line 391
    invoke-static {v4, v1}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 392
    .line 393
    .line 394
    move-result v3

    .line 395
    invoke-interface {v4}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 396
    .line 397
    .line 398
    move-result-object v5

    .line 399
    invoke-static {v4, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 400
    .line 401
    .line 402
    move-result-object v6

    .line 403
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 404
    .line 405
    .line 406
    move-result-object v7

    .line 407
    invoke-interface {v4}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 408
    .line 409
    .line 410
    move-result-object v8

    .line 411
    invoke-static {v8}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 412
    .line 413
    .line 414
    move-result v8

    .line 415
    if-nez v8, :cond_9

    .line 416
    .line 417
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 418
    .line 419
    .line 420
    :cond_9
    invoke-interface {v4}, Landroidx/compose/runtime/j;->l()V

    .line 421
    .line 422
    .line 423
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 424
    .line 425
    .line 426
    move-result v8

    .line 427
    if-eqz v8, :cond_a

    .line 428
    .line 429
    invoke-interface {v4, v7}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 430
    .line 431
    .line 432
    goto :goto_4

    .line 433
    :cond_a
    invoke-interface {v4}, Landroidx/compose/runtime/j;->h()V

    .line 434
    .line 435
    .line 436
    :goto_4
    invoke-static {v4}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 437
    .line 438
    .line 439
    move-result-object v7

    .line 440
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 441
    .line 442
    .line 443
    move-result-object v8

    .line 444
    invoke-static {v7, v2, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 445
    .line 446
    .line 447
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 448
    .line 449
    .line 450
    move-result-object v2

    .line 451
    invoke-static {v7, v5, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 452
    .line 453
    .line 454
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 455
    .line 456
    .line 457
    move-result-object v2

    .line 458
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 459
    .line 460
    .line 461
    move-result v5

    .line 462
    if-nez v5, :cond_b

    .line 463
    .line 464
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 465
    .line 466
    .line 467
    move-result-object v5

    .line 468
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 469
    .line 470
    .line 471
    move-result-object v8

    .line 472
    invoke-static {v5, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 473
    .line 474
    .line 475
    move-result v5

    .line 476
    if-nez v5, :cond_c

    .line 477
    .line 478
    :cond_b
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 479
    .line 480
    .line 481
    move-result-object v5

    .line 482
    invoke-interface {v7, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 483
    .line 484
    .line 485
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 486
    .line 487
    .line 488
    move-result-object v3

    .line 489
    invoke-interface {v7, v3, v2}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 490
    .line 491
    .line 492
    :cond_c
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 493
    .line 494
    .line 495
    move-result-object v2

    .line 496
    invoke-static {v7, v6, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 497
    .line 498
    .line 499
    sget-object v2, Landroidx/compose/foundation/layout/n;->a:Landroidx/compose/foundation/layout/n;

    .line 500
    .line 501
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 502
    .line 503
    .line 504
    move-result-object v2

    .line 505
    check-cast v2, LjD0/i;

    .line 506
    .line 507
    invoke-virtual {v2}, LjD0/i;->b()LjD0/h;

    .line 508
    .line 509
    .line 510
    move-result-object v2

    .line 511
    invoke-virtual {v2}, LjD0/h;->c()Ljava/lang/String;

    .line 512
    .line 513
    .line 514
    move-result-object v2

    .line 515
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 516
    .line 517
    .line 518
    move-result-object v3

    .line 519
    check-cast v3, LjD0/i;

    .line 520
    .line 521
    invoke-virtual {v3}, LjD0/i;->b()LjD0/h;

    .line 522
    .line 523
    .line 524
    move-result-object v3

    .line 525
    invoke-virtual {v3}, LjD0/h;->b()Ljava/lang/String;

    .line 526
    .line 527
    .line 528
    move-result-object v3

    .line 529
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 530
    .line 531
    .line 532
    move-result-object v5

    .line 533
    check-cast v5, LjD0/i;

    .line 534
    .line 535
    invoke-virtual {v5}, LjD0/i;->b()LjD0/h;

    .line 536
    .line 537
    .line 538
    move-result-object v5

    .line 539
    invoke-virtual {v5}, LjD0/h;->a()Ljava/lang/String;

    .line 540
    .line 541
    .line 542
    move-result-object v5

    .line 543
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 544
    .line 545
    .line 546
    move-result-object v6

    .line 547
    check-cast v6, LjD0/i;

    .line 548
    .line 549
    invoke-virtual {v6}, LjD0/i;->b()LjD0/h;

    .line 550
    .line 551
    .line 552
    move-result-object v6

    .line 553
    invoke-virtual {v6}, LjD0/h;->d()Z

    .line 554
    .line 555
    .line 556
    move-result v6

    .line 557
    const/4 v7, 0x0

    .line 558
    const/4 v10, 0x1

    .line 559
    const/4 v11, 0x0

    .line 560
    invoke-static {v0, v7, v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 561
    .line 562
    .line 563
    move-result-object v8

    .line 564
    invoke-static {v4, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 565
    .line 566
    .line 567
    move-result-object v7

    .line 568
    invoke-virtual {v7}, LA11/b;->a()F

    .line 569
    .line 570
    .line 571
    move-result v7

    .line 572
    invoke-static {v4, v1}, LA11/d;->c(Landroidx/compose/runtime/j;I)LA11/b;

    .line 573
    .line 574
    .line 575
    move-result-object v9

    .line 576
    invoke-virtual {v9}, LA11/b;->a()F

    .line 577
    .line 578
    .line 579
    move-result v9

    .line 580
    invoke-virtual/range {v23 .. v23}, LA11/a;->L1()F

    .line 581
    .line 582
    .line 583
    move-result v12

    .line 584
    invoke-interface/range {p1 .. p1}, Landroidx/compose/foundation/layout/Y;->d()F

    .line 585
    .line 586
    .line 587
    move-result v13

    .line 588
    add-float/2addr v12, v13

    .line 589
    invoke-static {v12}, Lt0/i;->k(F)F

    .line 590
    .line 591
    .line 592
    move-result v12

    .line 593
    invoke-virtual/range {v23 .. v23}, LA11/a;->q1()F

    .line 594
    .line 595
    .line 596
    move-result v13

    .line 597
    invoke-static {v8, v7, v12, v9, v13}, Landroidx/compose/foundation/layout/PaddingKt;->l(Landroidx/compose/ui/l;FFFF)Landroidx/compose/ui/l;

    .line 598
    .line 599
    .line 600
    move-result-object v7

    .line 601
    const v12, 0x4c5de2

    .line 602
    .line 603
    .line 604
    invoke-interface {v4, v12}, Landroidx/compose/runtime/j;->t(I)V

    .line 605
    .line 606
    .line 607
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 608
    .line 609
    .line 610
    move-result-object v8

    .line 611
    sget-object v13, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 612
    .line 613
    invoke-virtual {v13}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 614
    .line 615
    .line 616
    move-result-object v9

    .line 617
    if-ne v8, v9, :cond_d

    .line 618
    .line 619
    new-instance v8, LhD0/i;

    .line 620
    .line 621
    move-object/from16 v9, v32

    .line 622
    .line 623
    invoke-direct {v8, v9}, LhD0/i;-><init>(Landroidx/compose/runtime/h0;)V

    .line 624
    .line 625
    .line 626
    invoke-interface {v4, v8}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 627
    .line 628
    .line 629
    :cond_d
    check-cast v8, Lkotlin/jvm/functions/Function1;

    .line 630
    .line 631
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 632
    .line 633
    .line 634
    invoke-static {v7, v8}, Landroidx/compose/ui/layout/b0;->a(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;)Landroidx/compose/ui/l;

    .line 635
    .line 636
    .line 637
    move-result-object v7

    .line 638
    const/4 v8, 0x0

    .line 639
    const/4 v9, 0x0

    .line 640
    move-object/from16 v34, v7

    .line 641
    .line 642
    move-object v7, v4

    .line 643
    move-object v4, v5

    .line 644
    move v5, v6

    .line 645
    move-object/from16 v6, v34

    .line 646
    .line 647
    invoke-static/range {v2 .. v9}, LIN0/C;->c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 648
    .line 649
    .line 650
    move-object v4, v7

    .line 651
    invoke-static {}, LV01/s;->f()Landroidx/compose/runtime/x0;

    .line 652
    .line 653
    .line 654
    move-result-object v2

    .line 655
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 656
    .line 657
    .line 658
    move-result-object v2

    .line 659
    check-cast v2, Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 660
    .line 661
    invoke-virtual {v2}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackground-0d7_KjU()J

    .line 662
    .line 663
    .line 664
    move-result-wide v2

    .line 665
    invoke-virtual/range {v23 .. v23}, LA11/a;->u1()F

    .line 666
    .line 667
    .line 668
    move-result v15

    .line 669
    invoke-virtual/range {v23 .. v23}, LA11/a;->u1()F

    .line 670
    .line 671
    .line 672
    move-result v14

    .line 673
    const/16 v18, 0xc

    .line 674
    .line 675
    const/16 v19, 0x0

    .line 676
    .line 677
    const/16 v16, 0x0

    .line 678
    .line 679
    const/16 v17, 0x0

    .line 680
    .line 681
    invoke-static/range {v14 .. v19}, LR/i;->h(FFFFILjava/lang/Object;)LR/h;

    .line 682
    .line 683
    .line 684
    move-result-object v5

    .line 685
    invoke-static {v0, v2, v3, v5}, Landroidx/compose/foundation/BackgroundKt;->c(Landroidx/compose/ui/l;JLandroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 686
    .line 687
    .line 688
    move-result-object v2

    .line 689
    invoke-virtual/range {v23 .. v23}, LA11/a;->u1()F

    .line 690
    .line 691
    .line 692
    move-result v15

    .line 693
    invoke-virtual/range {v23 .. v23}, LA11/a;->u1()F

    .line 694
    .line 695
    .line 696
    move-result v14

    .line 697
    invoke-static/range {v14 .. v19}, LR/i;->h(FFFFILjava/lang/Object;)LR/h;

    .line 698
    .line 699
    .line 700
    move-result-object v3

    .line 701
    invoke-static {v2, v3}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 702
    .line 703
    .line 704
    move-result-object v2

    .line 705
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 706
    .line 707
    .line 708
    move-result-object v3

    .line 709
    invoke-static {v3, v1}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 710
    .line 711
    .line 712
    move-result-object v3

    .line 713
    invoke-static {v4, v1}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 714
    .line 715
    .line 716
    move-result v5

    .line 717
    invoke-interface {v4}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 718
    .line 719
    .line 720
    move-result-object v6

    .line 721
    invoke-static {v4, v2}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 722
    .line 723
    .line 724
    move-result-object v2

    .line 725
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 726
    .line 727
    .line 728
    move-result-object v7

    .line 729
    invoke-interface {v4}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 730
    .line 731
    .line 732
    move-result-object v8

    .line 733
    invoke-static {v8}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 734
    .line 735
    .line 736
    move-result v8

    .line 737
    if-nez v8, :cond_e

    .line 738
    .line 739
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 740
    .line 741
    .line 742
    :cond_e
    invoke-interface {v4}, Landroidx/compose/runtime/j;->l()V

    .line 743
    .line 744
    .line 745
    invoke-interface {v4}, Landroidx/compose/runtime/j;->B()Z

    .line 746
    .line 747
    .line 748
    move-result v8

    .line 749
    if-eqz v8, :cond_f

    .line 750
    .line 751
    invoke-interface {v4, v7}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 752
    .line 753
    .line 754
    goto :goto_5

    .line 755
    :cond_f
    invoke-interface {v4}, Landroidx/compose/runtime/j;->h()V

    .line 756
    .line 757
    .line 758
    :goto_5
    invoke-static {v4}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 759
    .line 760
    .line 761
    move-result-object v7

    .line 762
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 763
    .line 764
    .line 765
    move-result-object v8

    .line 766
    invoke-static {v7, v3, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 767
    .line 768
    .line 769
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 770
    .line 771
    .line 772
    move-result-object v3

    .line 773
    invoke-static {v7, v6, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 774
    .line 775
    .line 776
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 777
    .line 778
    .line 779
    move-result-object v3

    .line 780
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 781
    .line 782
    .line 783
    move-result v6

    .line 784
    if-nez v6, :cond_10

    .line 785
    .line 786
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 787
    .line 788
    .line 789
    move-result-object v6

    .line 790
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 791
    .line 792
    .line 793
    move-result-object v8

    .line 794
    invoke-static {v6, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 795
    .line 796
    .line 797
    move-result v6

    .line 798
    if-nez v6, :cond_11

    .line 799
    .line 800
    :cond_10
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 801
    .line 802
    .line 803
    move-result-object v6

    .line 804
    invoke-interface {v7, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 805
    .line 806
    .line 807
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 808
    .line 809
    .line 810
    move-result-object v5

    .line 811
    invoke-interface {v7, v5, v3}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 812
    .line 813
    .line 814
    :cond_11
    invoke-virtual/range {v22 .. v22}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 815
    .line 816
    .line 817
    move-result-object v3

    .line 818
    invoke-static {v7, v2, v3}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 819
    .line 820
    .line 821
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 822
    .line 823
    .line 824
    move-result-object v2

    .line 825
    check-cast v2, LjD0/i;

    .line 826
    .line 827
    invoke-virtual {v2}, LjD0/i;->c()Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l;

    .line 828
    .line 829
    .line 830
    move-result-object v2

    .line 831
    instance-of v3, v2, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$a;

    .line 832
    .line 833
    if-eqz v3, :cond_14

    .line 834
    .line 835
    const v1, 0x29e9ba08

    .line 836
    .line 837
    .line 838
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 839
    .line 840
    .line 841
    invoke-interface/range {v30 .. v30}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 842
    .line 843
    .line 844
    move-result-object v1

    .line 845
    check-cast v1, LjD0/i;

    .line 846
    .line 847
    invoke-virtual {v1}, LjD0/i;->c()Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l;

    .line 848
    .line 849
    .line 850
    move-result-object v1

    .line 851
    check-cast v1, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$a;

    .line 852
    .line 853
    invoke-virtual {v1}, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$a;->a()LHd/c;

    .line 854
    .line 855
    .line 856
    move-result-object v1

    .line 857
    sget v2, Lpb/k;->match_reviewe:I

    .line 858
    .line 859
    const/4 v7, 0x0

    .line 860
    invoke-static {v0, v7, v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 861
    .line 862
    .line 863
    move-result-object v0

    .line 864
    invoke-interface {v4, v12}, Landroidx/compose/runtime/j;->t(I)V

    .line 865
    .line 866
    .line 867
    move-object/from16 v3, v33

    .line 868
    .line 869
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 870
    .line 871
    .line 872
    move-result v5

    .line 873
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 874
    .line 875
    .line 876
    move-result-object v6

    .line 877
    if-nez v5, :cond_12

    .line 878
    .line 879
    invoke-virtual {v13}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 880
    .line 881
    .line 882
    move-result-object v5

    .line 883
    if-ne v6, v5, :cond_13

    .line 884
    .line 885
    :cond_12
    new-instance v6, LhD0/j;

    .line 886
    .line 887
    invoke-direct {v6, v3}, LhD0/j;-><init>(Ltc1/a;)V

    .line 888
    .line 889
    .line 890
    invoke-interface {v4, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 891
    .line 892
    .line 893
    :cond_13
    move-object v3, v6

    .line 894
    check-cast v3, Lkotlin/jvm/functions/Function1;

    .line 895
    .line 896
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 897
    .line 898
    .line 899
    const/16 v6, 0x30

    .line 900
    .line 901
    const/4 v7, 0x0

    .line 902
    move-object v5, v4

    .line 903
    move v4, v2

    .line 904
    move-object v2, v0

    .line 905
    invoke-static/range {v1 .. v7}, Lorg/xbet/statistic/statistic_core/presentation/composable/common/menu/ContentMenuKt;->d(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;ILandroidx/compose/runtime/j;II)V

    .line 906
    .line 907
    .line 908
    move-object v4, v5

    .line 909
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 910
    .line 911
    .line 912
    goto/16 :goto_6

    .line 913
    .line 914
    :cond_14
    instance-of v3, v2, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$b;

    .line 915
    .line 916
    if-eqz v3, :cond_17

    .line 917
    .line 918
    const v1, 0x29f4b938

    .line 919
    .line 920
    .line 921
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 922
    .line 923
    .line 924
    const/4 v7, 0x0

    .line 925
    invoke-static {v0, v7, v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 926
    .line 927
    .line 928
    move-result-object v2

    .line 929
    invoke-interface {v4, v12}, Landroidx/compose/runtime/j;->t(I)V

    .line 930
    .line 931
    .line 932
    move-object/from16 v3, v30

    .line 933
    .line 934
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 935
    .line 936
    .line 937
    move-result v0

    .line 938
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 939
    .line 940
    .line 941
    move-result-object v1

    .line 942
    if-nez v0, :cond_15

    .line 943
    .line 944
    invoke-virtual {v13}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 945
    .line 946
    .line 947
    move-result-object v0

    .line 948
    if-ne v1, v0, :cond_16

    .line 949
    .line 950
    :cond_15
    new-instance v1, LhD0/k;

    .line 951
    .line 952
    invoke-direct {v1, v3}, LhD0/k;-><init>(Landroidx/compose/runtime/r1;)V

    .line 953
    .line 954
    .line 955
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 956
    .line 957
    .line 958
    :cond_16
    check-cast v1, Lkotlin/jvm/functions/Function1;

    .line 959
    .line 960
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 961
    .line 962
    .line 963
    const/16 v5, 0x30

    .line 964
    .line 965
    const/4 v6, 0x4

    .line 966
    const/4 v3, 0x0

    .line 967
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 968
    .line 969
    .line 970
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 971
    .line 972
    .line 973
    goto/16 :goto_6

    .line 974
    .line 975
    :cond_17
    move-object/from16 v3, v30

    .line 976
    .line 977
    instance-of v5, v2, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$c;

    .line 978
    .line 979
    if-eqz v5, :cond_1a

    .line 980
    .line 981
    const v1, 0x29ff7c58

    .line 982
    .line 983
    .line 984
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 985
    .line 986
    .line 987
    const/4 v7, 0x0

    .line 988
    invoke-static {v0, v7, v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->f(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 989
    .line 990
    .line 991
    move-result-object v2

    .line 992
    invoke-interface {v4, v12}, Landroidx/compose/runtime/j;->t(I)V

    .line 993
    .line 994
    .line 995
    invoke-interface {v4, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 996
    .line 997
    .line 998
    move-result v0

    .line 999
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 1000
    .line 1001
    .line 1002
    move-result-object v1

    .line 1003
    if-nez v0, :cond_18

    .line 1004
    .line 1005
    invoke-virtual {v13}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 1006
    .line 1007
    .line 1008
    move-result-object v0

    .line 1009
    if-ne v1, v0, :cond_19

    .line 1010
    .line 1011
    :cond_18
    new-instance v1, LhD0/l;

    .line 1012
    .line 1013
    invoke-direct {v1, v3}, LhD0/l;-><init>(Landroidx/compose/runtime/r1;)V

    .line 1014
    .line 1015
    .line 1016
    invoke-interface {v4, v1}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 1017
    .line 1018
    .line 1019
    :cond_19
    check-cast v1, Lkotlin/jvm/functions/Function1;

    .line 1020
    .line 1021
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 1022
    .line 1023
    .line 1024
    const/16 v5, 0x30

    .line 1025
    .line 1026
    const/4 v6, 0x4

    .line 1027
    const/4 v3, 0x0

    .line 1028
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 1029
    .line 1030
    .line 1031
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 1032
    .line 1033
    .line 1034
    goto :goto_6

    .line 1035
    :cond_1a
    sget-object v3, Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$d;->a:Lorg/xbet/statistic/cycling/impl/cycling_menu/presentation/viewmodel/l$d;

    .line 1036
    .line 1037
    invoke-static {v2, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 1038
    .line 1039
    .line 1040
    move-result v2

    .line 1041
    if-eqz v2, :cond_1c

    .line 1042
    .line 1043
    const v2, 0x2a0a1ded

    .line 1044
    .line 1045
    .line 1046
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->t(I)V

    .line 1047
    .line 1048
    .line 1049
    invoke-static {}, Landroidx/compose/ui/platform/AndroidCompositionLocals_androidKt;->f()Landroidx/compose/runtime/x0;

    .line 1050
    .line 1051
    .line 1052
    move-result-object v2

    .line 1053
    invoke-interface {v4, v2}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 1054
    .line 1055
    .line 1056
    move-result-object v2

    .line 1057
    check-cast v2, Landroid/content/res/Configuration;

    .line 1058
    .line 1059
    iget v2, v2, Landroid/content/res/Configuration;->screenHeightDp:I

    .line 1060
    .line 1061
    int-to-float v2, v2

    .line 1062
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 1063
    .line 1064
    .line 1065
    move-result v2

    .line 1066
    invoke-static {}, LhD0/h;->h()F

    .line 1067
    .line 1068
    .line 1069
    move-result v3

    .line 1070
    sub-float/2addr v2, v3

    .line 1071
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 1072
    .line 1073
    .line 1074
    move-result v2

    .line 1075
    invoke-interface/range {p1 .. p1}, Landroidx/compose/foundation/layout/Y;->d()F

    .line 1076
    .line 1077
    .line 1078
    move-result v3

    .line 1079
    sub-float/2addr v2, v3

    .line 1080
    invoke-static {v2}, Lt0/i;->k(F)F

    .line 1081
    .line 1082
    .line 1083
    move-result v2

    .line 1084
    invoke-static {v2}, LIN0/D;->a(F)I

    .line 1085
    .line 1086
    .line 1087
    move-result v2

    .line 1088
    const/4 v7, 0x0

    .line 1089
    invoke-static {v0, v7, v10, v11}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 1090
    .line 1091
    .line 1092
    move-result-object v0

    .line 1093
    const/16 v3, 0x30

    .line 1094
    .line 1095
    invoke-static {v2, v0, v4, v3, v1}, LIN0/n;->c(ILandroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 1096
    .line 1097
    .line 1098
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 1099
    .line 1100
    .line 1101
    :goto_6
    invoke-interface {v4}, Landroidx/compose/runtime/j;->j()V

    .line 1102
    .line 1103
    .line 1104
    invoke-interface {v4}, Landroidx/compose/runtime/j;->j()V

    .line 1105
    .line 1106
    .line 1107
    invoke-interface {v4}, Landroidx/compose/runtime/j;->j()V

    .line 1108
    .line 1109
    .line 1110
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 1111
    .line 1112
    .line 1113
    move-result v0

    .line 1114
    if-eqz v0, :cond_1b

    .line 1115
    .line 1116
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 1117
    .line 1118
    .line 1119
    :cond_1b
    return-void

    .line 1120
    :cond_1c
    const v0, -0x176c17e9

    .line 1121
    .line 1122
    .line 1123
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 1124
    .line 1125
    .line 1126
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 1127
    .line 1128
    .line 1129
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 1130
    .line 1131
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 1132
    .line 1133
    .line 1134
    throw v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/Y;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/j;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, LhD0/h$b;->e(Landroidx/compose/foundation/layout/Y;Landroidx/compose/runtime/j;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method
