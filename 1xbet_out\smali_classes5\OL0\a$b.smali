.class public final LOL0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOL0/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LOL0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LOL0/a$b;


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LOL0/a$b;->a:LOL0/a$b;

    return-void
.end method

.method public synthetic constructor <init>(LOL0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LOL0/a$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a()LGL0/b;
    .locals 1

    .line 1
    new-instance v0, LJL0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LJL0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
