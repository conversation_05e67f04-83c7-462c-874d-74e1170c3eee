.class public final LSQ0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J&\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\u000c2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0086@\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0012R\u001a\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00138\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016\u00a8\u0006\u0018"
    }
    d2 = {
        "LSQ0/c;",
        "",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lc8/h;Lf8/g;)V",
        "",
        "gameId",
        "",
        "countryId",
        "Le8/b;",
        "LUQ0/b;",
        "b",
        "(Ljava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "Lc8/h;",
        "Lf8/g;",
        "Lkotlin/Function0;",
        "LRQ0/a;",
        "c",
        "Lkotlin/jvm/functions/Function0;",
        "service",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "LRQ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lc8/h;Lf8/g;)V
    .locals 0
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LSQ0/c;->a:Lc8/h;

    .line 5
    .line 6
    iput-object p2, p0, LSQ0/c;->b:Lf8/g;

    .line 7
    .line 8
    new-instance p1, LSQ0/b;

    .line 9
    .line 10
    invoke-direct {p1, p0}, LSQ0/b;-><init>(LSQ0/c;)V

    .line 11
    .line 12
    .line 13
    iput-object p1, p0, LSQ0/c;->c:Lkotlin/jvm/functions/Function0;

    .line 14
    .line 15
    return-void
.end method

.method public static synthetic a(LSQ0/c;)LRQ0/a;
    .locals 0

    .line 1
    invoke-static {p0}, LSQ0/c;->c(LSQ0/c;)LRQ0/a;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LSQ0/c;)LRQ0/a;
    .locals 1

    .line 1
    iget-object p0, p0, LSQ0/c;->b:Lf8/g;

    .line 2
    .line 3
    const-class v0, LRQ0/a;

    .line 4
    .line 5
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LRQ0/a;

    .line 14
    .line 15
    return-object p0
.end method


# virtual methods
.method public final b(Ljava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LUQ0/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LSQ0/c;->c:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, LRQ0/a;

    .line 9
    .line 10
    iget-object v0, p0, LSQ0/c;->a:Lc8/h;

    .line 11
    .line 12
    invoke-interface {v0}, Lc8/h;->c()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    iget-object v0, p0, LSQ0/c;->a:Lc8/h;

    .line 17
    .line 18
    invoke-interface {v0}, Lc8/h;->b()I

    .line 19
    .line 20
    .line 21
    move-result v5

    .line 22
    const/4 v8, 0x1

    .line 23
    const/4 v9, 0x0

    .line 24
    const/4 v2, 0x0

    .line 25
    move-object v4, p1

    .line 26
    move v6, p2

    .line 27
    move-object v7, p3

    .line 28
    invoke-static/range {v1 .. v9}, LRQ0/a$a;->a(LRQ0/a;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method
