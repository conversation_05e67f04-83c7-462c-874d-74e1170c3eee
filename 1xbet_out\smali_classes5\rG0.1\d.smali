.class public final LrG0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LvG0/c;",
        "LvG0/d;",
        "a",
        "(LvG0/c;)LvG0/d;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LvG0/c;)LvG0/d;
    .locals 15
    .param p0    # LvG0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LvG0/c;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual {p0}, LvG0/c;->g()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {p0}, LvG0/c;->h()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual {p0}, LvG0/c;->g()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {p0}, LvG0/c;->h()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-virtual {p0}, LvG0/c;->a()I

    .line 22
    .line 23
    .line 24
    move-result v8

    .line 25
    invoke-virtual {p0}, LvG0/c;->d()I

    .line 26
    .line 27
    .line 28
    move-result v6

    .line 29
    invoke-virtual {p0}, LvG0/c;->e()I

    .line 30
    .line 31
    .line 32
    move-result v7

    .line 33
    invoke-virtual {p0}, LvG0/c;->i()I

    .line 34
    .line 35
    .line 36
    move-result v9

    .line 37
    invoke-virtual {p0}, LvG0/c;->b()J

    .line 38
    .line 39
    .line 40
    move-result-wide v10

    .line 41
    invoke-virtual {p0}, LvG0/c;->f()Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 42
    .line 43
    .line 44
    move-result-object v12

    .line 45
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v13

    .line 49
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v14

    .line 53
    new-instance v0, LvG0/d;

    .line 54
    .line 55
    invoke-direct/range {v0 .. v14}, LvG0/d;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIIJLorg/xbet/statistic/domain/model/shortgame/EventStatusType;Ljava/util/List;Ljava/util/List;)V

    .line 56
    .line 57
    .line 58
    return-object v0
.end method
