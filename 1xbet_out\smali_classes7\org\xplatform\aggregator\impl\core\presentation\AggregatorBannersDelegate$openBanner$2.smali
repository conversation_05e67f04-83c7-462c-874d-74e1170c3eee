.class final Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.presentation.AggregatorBannersDelegate$openBanner$2"
    f = "AggregatorBannersDelegate.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->h(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

.field final synthetic $position:I

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iput p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$position:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    iget v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$position:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->b(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;)Lkotlinx/coroutines/flow/U;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$c;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->this$0:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 20
    .line 21
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->c(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;)LVg0/a;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$banner:Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 26
    .line 27
    iget v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$openBanner$2;->$position:I

    .line 28
    .line 29
    const/4 v4, 0x1

    .line 30
    invoke-interface {v1, v2, v3, v4}, LVg0/a;->c(Lorg/xplatform/banners/api/domain/models/BannerModel;IZ)Landroid/content/Intent;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$c;-><init>(Landroid/content/Intent;)V

    .line 35
    .line 36
    .line 37
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 41
    .line 42
    return-object p1

    .line 43
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 44
    .line 45
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 46
    .line 47
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    throw p1
.end method
