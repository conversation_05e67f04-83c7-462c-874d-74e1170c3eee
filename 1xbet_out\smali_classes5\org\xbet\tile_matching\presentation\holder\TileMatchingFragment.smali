.class public final Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;
.super Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 *2\u00020\u0001:\u0001+B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\r\u0010\u0003R+\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0012\u0010\u0013\"\u0004\u0008\u0014\u0010\u0015R\"\u0010\u001e\u001a\u00020\u00178\u0000@\u0000X\u0081.\u00a2\u0006\u0012\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001b\"\u0004\u0008\u001c\u0010\u001dR\u001b\u0010$\u001a\u00020\u001f8VX\u0096\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008 \u0010!\u001a\u0004\u0008\"\u0010#R\u001b\u0010)\u001a\u00020%8@X\u0080\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008&\u0010!\u001a\u0004\u0008\'\u0010(\u00a8\u0006,"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;",
        "Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;",
        "<init>",
        "()V",
        "",
        "u2",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;",
        "i4",
        "()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "image",
        "l3",
        "(Landroidx/appcompat/widget/AppCompatImageView;)V",
        "Z2",
        "",
        "<set-?>",
        "o0",
        "LeX0/f;",
        "j4",
        "()J",
        "m4",
        "(J)V",
        "gameTypeExtra",
        "LQv/a$s;",
        "b1",
        "LQv/a$s;",
        "l4",
        "()LQv/a$s;",
        "setViewModelFactory$tile_matching_release",
        "(LQv/a$s;)V",
        "viewModelFactory",
        "Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;",
        "k1",
        "Lkotlin/j;",
        "m3",
        "()Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;",
        "viewModel",
        "LyT0/c;",
        "v1",
        "k4",
        "()LyT0/c;",
        "tileMatchingComponent",
        "x1",
        "a",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final x1:Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic y1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public b1:LQv/a$s;

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getGameTypeExtra()J"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 7
    .line 8
    const-string v4, "gameTypeExtra"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->y1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->x1:Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LeX0/f;

    .line 5
    .line 6
    const/4 v4, 0x2

    .line 7
    const/4 v5, 0x0

    .line 8
    const-string v1, "TileMatching.GAME_TYPE_EXTRA"

    .line 9
    .line 10
    const-wide/16 v2, 0x0

    .line 11
    .line 12
    invoke-direct/range {v0 .. v5}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->o0:LeX0/f;

    .line 16
    .line 17
    new-instance v0, Lorg/xbet/tile_matching/presentation/holder/a;

    .line 18
    .line 19
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/holder/a;-><init>(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v1, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$1;

    .line 23
    .line 24
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 25
    .line 26
    .line 27
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 28
    .line 29
    new-instance v3, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$2;

    .line 30
    .line 31
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 32
    .line 33
    .line 34
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    const-class v2, Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;

    .line 39
    .line 40
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 41
    .line 42
    .line 43
    move-result-object v2

    .line 44
    new-instance v3, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$3;

    .line 45
    .line 46
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 47
    .line 48
    .line 49
    new-instance v4, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$4;

    .line 50
    .line 51
    invoke-direct {v4, v5, v1}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 52
    .line 53
    .line 54
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k1:Lkotlin/j;

    .line 59
    .line 60
    new-instance v0, Lorg/xbet/tile_matching/presentation/holder/b;

    .line 61
    .line 62
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/holder/b;-><init>(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)V

    .line 63
    .line 64
    .line 65
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->v1:Lkotlin/j;

    .line 70
    .line 71
    return-void
.end method

.method public static synthetic f4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)LyT0/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->n4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)LyT0/c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->o4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic h4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->m4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final j4()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->o0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method private final m4(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->o0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final n4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)LyT0/c;
    .locals 6

    .line 1
    invoke-static {}, LyT0/a;->a()LyT0/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    instance-of v2, v1, LQW0/f;

    .line 14
    .line 15
    const-string v3, "Can not find dependencies provider for "

    .line 16
    .line 17
    if-eqz v2, :cond_2

    .line 18
    .line 19
    check-cast v1, LQW0/f;

    .line 20
    .line 21
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    instance-of v2, v2, LQv/v;

    .line 26
    .line 27
    if-eqz v2, :cond_1

    .line 28
    .line 29
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    if-eqz v1, :cond_0

    .line 34
    .line 35
    check-cast v1, LQv/v;

    .line 36
    .line 37
    new-instance v2, LyT0/f;

    .line 38
    .line 39
    invoke-direct {v2}, LyT0/f;-><init>()V

    .line 40
    .line 41
    .line 42
    sget-object v3, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;->Companion:Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType$a;

    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->j4()J

    .line 45
    .line 46
    .line 47
    move-result-wide v4

    .line 48
    invoke-virtual {v3, v4, v5}, Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType$a;->a(J)Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 49
    .line 50
    .line 51
    move-result-object p0

    .line 52
    invoke-interface {v0, v1, v2, p0}, LyT0/c$a;->a(LQv/v;LyT0/f;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)LyT0/c;

    .line 53
    .line 54
    .line 55
    move-result-object p0

    .line 56
    return-object p0

    .line 57
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 58
    .line 59
    const-string v0, "null cannot be cast to non-null type org.xbet.core.di.GamesCoreDependencies"

    .line 60
    .line 61
    invoke-direct {p0, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 62
    .line 63
    .line 64
    throw p0

    .line 65
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 66
    .line 67
    new-instance v1, Ljava/lang/StringBuilder;

    .line 68
    .line 69
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 73
    .line 74
    .line 75
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 76
    .line 77
    .line 78
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object p0

    .line 82
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    throw v0

    .line 86
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 87
    .line 88
    new-instance v1, Ljava/lang/StringBuilder;

    .line 89
    .line 90
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 91
    .line 92
    .line 93
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 97
    .line 98
    .line 99
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object p0

    .line 103
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 104
    .line 105
    .line 106
    throw v0
.end method

.method public static final o4(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->l4()LQv/a$s;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method


# virtual methods
.method public Z2()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    sget v1, LIv/d;->onex_holder_end_game_container:I

    .line 7
    .line 8
    invoke-virtual {p0, v0, v1}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->a3(Landroidx/fragment/app/Fragment;I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public i4()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->n0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$a;->a()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public bridge synthetic j3()Landroidx/fragment/app/Fragment;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->i4()Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final k4()LyT0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LyT0/c;

    .line 8
    .line 9
    return-object v0
.end method

.method public l3(Landroidx/appcompat/widget/AppCompatImageView;)V
    .locals 0
    .param p1    # Landroidx/appcompat/widget/AppCompatImageView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public final l4()LQv/a$s;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->b1:LQv/a$s;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public m3()Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/core/presentation/holder/OnexGamesHolderViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k4()LyT0/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-interface {v0, p0}, LyT0/c;->b(Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k4()LyT0/c;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, LyT0/c;->a()LQv/a$a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v0}, LQv/a$a;->a()LQv/a;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {p0, v0}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->M3(LQv/a;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method
