.class public final Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\r\u0008\u0007\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0015\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0015\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0018\u0010\u0018\u001a\u0004\u0018\u00010\u00158\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0018\u0010\u001b\u001a\u0004\u0018\u00010\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR(\u0010!\u001a\u0004\u0018\u00010\r2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\r8\u0006@BX\u0086\u000e\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001e\u001a\u0004\u0008\u001f\u0010 \u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "type",
        "",
        "setType",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V",
        "Lc31/a;",
        "state",
        "setState",
        "(Lc31/a;)V",
        "",
        "showShimmer",
        "setShowShimmer",
        "(Z)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;",
        "tournamentStagesCell",
        "b",
        "Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;",
        "cellType",
        "value",
        "c",
        "Lc31/a;",
        "getUiState",
        "()Lc31/a;",
        "uiState",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

.field public b:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

.field public c:Lc31/a;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    sget-object v0, LS11/h;->TournamentStagesCell:[I

    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    .line 5
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->Companion:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;

    .line 6
    sget v0, LS11/h;->TournamentStagesCell_tournamentStagesCellType:I

    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result v0

    .line 7
    invoke-virtual {p2, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;->a(I)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    move-result-object p2

    .line 8
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 9
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method


# virtual methods
.method public final getUiState()Lc31/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->c:Lc31/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final setShowShimmer(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setShowShimmer(Z)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setState(Lc31/a;)V
    .locals 2
    .param p1    # Lc31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;->Companion:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;

    .line 2
    .line 3
    invoke-interface {p1}, Lc31/a;->d()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType$a;->b(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V

    .line 12
    .line 13
    .line 14
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->c:Lc31/a;

    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 17
    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setState(Lc31/a;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    return-void
.end method

.method public final setType(Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;)V
    .locals 4
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->b:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    goto :goto_1

    .line 6
    :cond_0
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->b:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSTournamentStagesCellType;

    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    const/4 v2, 0x1

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->getShowShimmer()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-ne v0, v2, :cond_1

    .line 19
    .line 20
    const/4 v1, 0x1

    .line 21
    :cond_1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell$a;->a:[I

    .line 22
    .line 23
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    aget p1, v0, p1

    .line 28
    .line 29
    const/4 v0, 0x2

    .line 30
    const/4 v3, 0x0

    .line 31
    if-eq p1, v2, :cond_5

    .line 32
    .line 33
    if-eq p1, v0, :cond_4

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    if-eq p1, v2, :cond_3

    .line 37
    .line 38
    const/4 v2, 0x4

    .line 39
    if-ne p1, v2, :cond_2

    .line 40
    .line 41
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;

    .line 42
    .line 43
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-direct {p1, v2, v3, v0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellProgressLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 52
    .line 53
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_3
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;

    .line 58
    .line 59
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 60
    .line 61
    .line 62
    move-result-object v2

    .line 63
    invoke-direct {p1, v2, v3, v0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellNumber;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 64
    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_4
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;

    .line 68
    .line 69
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-direct {p1, v2, v3, v0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellClock;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 74
    .line 75
    .line 76
    goto :goto_0

    .line 77
    :cond_5
    new-instance p1, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;

    .line 78
    .line 79
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    invoke-direct {p1, v2, v3, v0, v3}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSTournamentStagesCellRadial;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 84
    .line 85
    .line 86
    :goto_0
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 87
    .line 88
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 89
    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 92
    .line 93
    if-eqz p1, :cond_6

    .line 94
    .line 95
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 96
    .line 97
    .line 98
    :cond_6
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->c:Lc31/a;

    .line 99
    .line 100
    if-eqz p1, :cond_7

    .line 101
    .line 102
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->setState(Lc31/a;)V

    .line 103
    .line 104
    .line 105
    :cond_7
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/DSAggregatorTournamentStagesCell;->a:Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;

    .line 106
    .line 107
    if-eqz p1, :cond_8

    .line 108
    .line 109
    invoke-virtual {p1, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamentstagescell/views/DSBaseTournamentStagesCell;->setShowShimmer(Z)V

    .line 110
    .line 111
    .line 112
    :cond_8
    :goto_1
    return-void
.end method
