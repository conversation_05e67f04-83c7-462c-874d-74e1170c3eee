.class public final Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lkotlinx/coroutines/flow/f;

.field public final synthetic b:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/flow/f;Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8

    .line 1
    instance-of v0, p2, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto/16 :goto_5

    .line 42
    .line 43
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 44
    .line 45
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 46
    .line 47
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    throw p1

    .line 51
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    iget-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->a:Lkotlinx/coroutines/flow/f;

    .line 55
    .line 56
    check-cast p1, LKo0/a;

    .line 57
    .line 58
    instance-of v2, p1, LKo0/a$a;

    .line 59
    .line 60
    if-eqz v2, :cond_4

    .line 61
    .line 62
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 63
    .line 64
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->w3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lkotlinx/coroutines/flow/V;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    :cond_3
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    move-object v4, p1

    .line 73
    check-cast v4, LMy0/b;

    .line 74
    .line 75
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    const/4 v6, 0x0

    .line 80
    invoke-virtual {v4, v5, v6, v3}, LMy0/b;->a(Ljava/util/List;LMy0/a;Z)LMy0/b;

    .line 81
    .line 82
    .line 83
    move-result-object v4

    .line 84
    invoke-interface {v2, p1, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    move-result p1

    .line 88
    if-eqz p1, :cond_3

    .line 89
    .line 90
    goto :goto_4

    .line 91
    :cond_4
    instance-of v2, p1, LKo0/a$b;

    .line 92
    .line 93
    if-eqz v2, :cond_a

    .line 94
    .line 95
    check-cast p1, LKo0/a$b;

    .line 96
    .line 97
    invoke-virtual {p1}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    check-cast p1, Ljava/lang/Iterable;

    .line 102
    .line 103
    new-instance v2, Ljava/util/ArrayList;

    .line 104
    .line 105
    const/16 v4, 0xa

    .line 106
    .line 107
    invoke-static {p1, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 108
    .line 109
    .line 110
    move-result v4

    .line 111
    invoke-direct {v2, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 112
    .line 113
    .line 114
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 119
    .line 120
    .line 121
    move-result v4

    .line 122
    if-eqz v4, :cond_5

    .line 123
    .line 124
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 125
    .line 126
    .line 127
    move-result-object v4

    .line 128
    check-cast v4, LDy0/a;

    .line 129
    .line 130
    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 131
    .line 132
    invoke-static {v5}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->v3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)LHX0/e;

    .line 133
    .line 134
    .line 135
    move-result-object v5

    .line 136
    invoke-static {v4, v5}, LKy0/c;->a(LDy0/a;LHX0/e;)LMy0/a;

    .line 137
    .line 138
    .line 139
    move-result-object v4

    .line 140
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 141
    .line 142
    .line 143
    goto :goto_1

    .line 144
    :cond_5
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2;->b:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 145
    .line 146
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->w3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lkotlinx/coroutines/flow/V;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    :cond_6
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v4

    .line 154
    move-object v5, v4

    .line 155
    check-cast v5, LMy0/b;

    .line 156
    .line 157
    invoke-virtual {v5}, LMy0/b;->d()LMy0/a;

    .line 158
    .line 159
    .line 160
    move-result-object v6

    .line 161
    if-eqz v6, :cond_8

    .line 162
    .line 163
    invoke-virtual {v5}, LMy0/b;->d()LMy0/a;

    .line 164
    .line 165
    .line 166
    move-result-object v6

    .line 167
    invoke-interface {v2, v6}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 168
    .line 169
    .line 170
    move-result v6

    .line 171
    if-nez v6, :cond_7

    .line 172
    .line 173
    goto :goto_2

    .line 174
    :cond_7
    invoke-virtual {v5}, LMy0/b;->d()LMy0/a;

    .line 175
    .line 176
    .line 177
    move-result-object v6

    .line 178
    goto :goto_3

    .line 179
    :cond_8
    :goto_2
    invoke-static {v2}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 180
    .line 181
    .line 182
    move-result-object v6

    .line 183
    check-cast v6, LMy0/a;

    .line 184
    .line 185
    :goto_3
    const/4 v7, 0x0

    .line 186
    invoke-virtual {v5, v2, v6, v7}, LMy0/b;->a(Ljava/util/List;LMy0/a;Z)LMy0/b;

    .line 187
    .line 188
    .line 189
    move-result-object v5

    .line 190
    invoke-interface {p1, v4, v5}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 191
    .line 192
    .line 193
    move-result v4

    .line 194
    if-eqz v4, :cond_6

    .line 195
    .line 196
    :goto_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 197
    .line 198
    iput v3, v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1$2$1;->label:I

    .line 199
    .line 200
    invoke-interface {p2, p1, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object p1

    .line 204
    if-ne p1, v1, :cond_9

    .line 205
    .line 206
    return-object v1

    .line 207
    :cond_9
    :goto_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 208
    .line 209
    return-object p1

    .line 210
    :cond_a
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 211
    .line 212
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 213
    .line 214
    .line 215
    throw p1
.end method
