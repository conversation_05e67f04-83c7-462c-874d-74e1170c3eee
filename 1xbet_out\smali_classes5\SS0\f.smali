.class public final synthetic LSS0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Z


# direct methods
.method public synthetic constructor <init>(Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, LSS0/f;->a:Z

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-boolean v0, p0, LSS0/f;->a:Z

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/delegates/card/SwipexCardTwoTeamViewHolderKt;->a(ZLB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
