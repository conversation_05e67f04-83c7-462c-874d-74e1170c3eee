.class public final LoO0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LrO0/a;",
        "",
        "LtO0/a;",
        "a",
        "(LrO0/a;)Ljava/util/List;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LrO0/a;)Ljava/util/List;
    .locals 17
    .param p0    # LrO0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LrO0/a;",
            ")",
            "Ljava/util/List<",
            "LtO0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LrO0/a;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_17

    .line 7
    .line 8
    new-instance v2, Ljava/util/ArrayList;

    .line 9
    .line 10
    const/16 v3, 0xa

    .line 11
    .line 12
    invoke-static {v0, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 13
    .line 14
    .line 15
    move-result v3

    .line 16
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-eqz v3, :cond_16

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    check-cast v3, LCN0/a;

    .line 34
    .line 35
    invoke-virtual {v3}, LCN0/a;->c()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    if-eqz v5, :cond_15

    .line 40
    .line 41
    sget-object v4, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;->Companion:Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;

    .line 42
    .line 43
    invoke-virtual {v3}, LCN0/a;->f()Ljava/lang/Integer;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    const/4 v7, 0x0

    .line 48
    if-eqz v6, :cond_0

    .line 49
    .line 50
    invoke-virtual {v6}, Ljava/lang/Integer;->intValue()I

    .line 51
    .line 52
    .line 53
    move-result v6

    .line 54
    goto :goto_1

    .line 55
    :cond_0
    const/4 v6, 0x0

    .line 56
    :goto_1
    invoke-virtual {v4, v6}, Lorg/xbet/statistic/domain/model/shortgame/EventStatusType$a;->a(I)Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;

    .line 57
    .line 58
    .line 59
    move-result-object v6

    .line 60
    invoke-virtual/range {p0 .. p0}, LrO0/a;->b()Ljava/util/List;

    .line 61
    .line 62
    .line 63
    move-result-object v4

    .line 64
    if-eqz v4, :cond_3

    .line 65
    .line 66
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    :cond_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 71
    .line 72
    .line 73
    move-result v8

    .line 74
    if-eqz v8, :cond_2

    .line 75
    .line 76
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v8

    .line 80
    move-object v9, v8

    .line 81
    check-cast v9, LCN0/p;

    .line 82
    .line 83
    invoke-virtual {v9}, LCN0/p;->a()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v9

    .line 87
    invoke-virtual {v3}, LCN0/a;->g()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v10

    .line 91
    invoke-static {v9, v10}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    move-result v9

    .line 95
    if-eqz v9, :cond_1

    .line 96
    .line 97
    goto :goto_2

    .line 98
    :cond_2
    move-object v8, v1

    .line 99
    :goto_2
    check-cast v8, LCN0/p;

    .line 100
    .line 101
    if-eqz v8, :cond_3

    .line 102
    .line 103
    invoke-virtual {v8}, LCN0/p;->d()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v4

    .line 107
    goto :goto_3

    .line 108
    :cond_3
    move-object v4, v1

    .line 109
    :goto_3
    const-string v8, ""

    .line 110
    .line 111
    if-nez v4, :cond_4

    .line 112
    .line 113
    move-object v4, v8

    .line 114
    :cond_4
    invoke-virtual/range {p0 .. p0}, LrO0/a;->b()Ljava/util/List;

    .line 115
    .line 116
    .line 117
    move-result-object v9

    .line 118
    if-eqz v9, :cond_7

    .line 119
    .line 120
    invoke-interface {v9}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 121
    .line 122
    .line 123
    move-result-object v9

    .line 124
    :cond_5
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    .line 125
    .line 126
    .line 127
    move-result v10

    .line 128
    if-eqz v10, :cond_6

    .line 129
    .line 130
    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object v10

    .line 134
    move-object v11, v10

    .line 135
    check-cast v11, LCN0/p;

    .line 136
    .line 137
    invoke-virtual {v11}, LCN0/p;->a()Ljava/lang/String;

    .line 138
    .line 139
    .line 140
    move-result-object v11

    .line 141
    invoke-virtual {v3}, LCN0/a;->h()Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v12

    .line 145
    invoke-static {v11, v12}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 146
    .line 147
    .line 148
    move-result v11

    .line 149
    if-eqz v11, :cond_5

    .line 150
    .line 151
    goto :goto_4

    .line 152
    :cond_6
    move-object v10, v1

    .line 153
    :goto_4
    check-cast v10, LCN0/p;

    .line 154
    .line 155
    if-eqz v10, :cond_7

    .line 156
    .line 157
    invoke-virtual {v10}, LCN0/p;->d()Ljava/lang/String;

    .line 158
    .line 159
    .line 160
    move-result-object v9

    .line 161
    goto :goto_5

    .line 162
    :cond_7
    move-object v9, v1

    .line 163
    :goto_5
    if-nez v9, :cond_8

    .line 164
    .line 165
    move-object v9, v8

    .line 166
    :cond_8
    sget-object v10, LDX0/e;->a:LDX0/e;

    .line 167
    .line 168
    invoke-virtual/range {p0 .. p0}, LrO0/a;->b()Ljava/util/List;

    .line 169
    .line 170
    .line 171
    move-result-object v11

    .line 172
    if-eqz v11, :cond_b

    .line 173
    .line 174
    invoke-interface {v11}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 175
    .line 176
    .line 177
    move-result-object v11

    .line 178
    :cond_9
    invoke-interface {v11}, Ljava/util/Iterator;->hasNext()Z

    .line 179
    .line 180
    .line 181
    move-result v12

    .line 182
    if-eqz v12, :cond_a

    .line 183
    .line 184
    invoke-interface {v11}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 185
    .line 186
    .line 187
    move-result-object v12

    .line 188
    move-object v13, v12

    .line 189
    check-cast v13, LCN0/p;

    .line 190
    .line 191
    invoke-virtual {v13}, LCN0/p;->a()Ljava/lang/String;

    .line 192
    .line 193
    .line 194
    move-result-object v13

    .line 195
    invoke-virtual {v3}, LCN0/a;->g()Ljava/lang/String;

    .line 196
    .line 197
    .line 198
    move-result-object v14

    .line 199
    invoke-static {v13, v14}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 200
    .line 201
    .line 202
    move-result v13

    .line 203
    if-eqz v13, :cond_9

    .line 204
    .line 205
    goto :goto_6

    .line 206
    :cond_a
    move-object v12, v1

    .line 207
    :goto_6
    check-cast v12, LCN0/p;

    .line 208
    .line 209
    if-eqz v12, :cond_b

    .line 210
    .line 211
    invoke-virtual {v12}, LCN0/p;->b()Ljava/lang/String;

    .line 212
    .line 213
    .line 214
    move-result-object v11

    .line 215
    goto :goto_7

    .line 216
    :cond_b
    move-object v11, v1

    .line 217
    :goto_7
    if-nez v11, :cond_c

    .line 218
    .line 219
    move-object v11, v8

    .line 220
    :cond_c
    invoke-virtual {v10, v11}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 221
    .line 222
    .line 223
    move-result-object v10

    .line 224
    sget-object v11, LDX0/e;->a:LDX0/e;

    .line 225
    .line 226
    invoke-virtual/range {p0 .. p0}, LrO0/a;->b()Ljava/util/List;

    .line 227
    .line 228
    .line 229
    move-result-object v12

    .line 230
    if-eqz v12, :cond_f

    .line 231
    .line 232
    invoke-interface {v12}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 233
    .line 234
    .line 235
    move-result-object v12

    .line 236
    :cond_d
    invoke-interface {v12}, Ljava/util/Iterator;->hasNext()Z

    .line 237
    .line 238
    .line 239
    move-result v13

    .line 240
    if-eqz v13, :cond_e

    .line 241
    .line 242
    invoke-interface {v12}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v13

    .line 246
    move-object v14, v13

    .line 247
    check-cast v14, LCN0/p;

    .line 248
    .line 249
    invoke-virtual {v14}, LCN0/p;->a()Ljava/lang/String;

    .line 250
    .line 251
    .line 252
    move-result-object v14

    .line 253
    invoke-virtual {v3}, LCN0/a;->h()Ljava/lang/String;

    .line 254
    .line 255
    .line 256
    move-result-object v15

    .line 257
    invoke-static {v14, v15}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 258
    .line 259
    .line 260
    move-result v14

    .line 261
    if-eqz v14, :cond_d

    .line 262
    .line 263
    goto :goto_8

    .line 264
    :cond_e
    move-object v13, v1

    .line 265
    :goto_8
    check-cast v13, LCN0/p;

    .line 266
    .line 267
    if-eqz v13, :cond_f

    .line 268
    .line 269
    invoke-virtual {v13}, LCN0/p;->b()Ljava/lang/String;

    .line 270
    .line 271
    .line 272
    move-result-object v12

    .line 273
    goto :goto_9

    .line 274
    :cond_f
    move-object v12, v1

    .line 275
    :goto_9
    if-nez v12, :cond_10

    .line 276
    .line 277
    goto :goto_a

    .line 278
    :cond_10
    move-object v8, v12

    .line 279
    :goto_a
    invoke-virtual {v11, v8}, LDX0/e;->c(Ljava/lang/String;)Ljava/lang/String;

    .line 280
    .line 281
    .line 282
    move-result-object v8

    .line 283
    invoke-virtual {v3}, LCN0/a;->d()Ljava/lang/Integer;

    .line 284
    .line 285
    .line 286
    move-result-object v11

    .line 287
    if-eqz v11, :cond_11

    .line 288
    .line 289
    invoke-virtual {v11}, Ljava/lang/Integer;->intValue()I

    .line 290
    .line 291
    .line 292
    move-result v11

    .line 293
    goto :goto_b

    .line 294
    :cond_11
    const/4 v11, 0x0

    .line 295
    :goto_b
    invoke-virtual {v3}, LCN0/a;->e()Ljava/lang/Integer;

    .line 296
    .line 297
    .line 298
    move-result-object v12

    .line 299
    if-eqz v12, :cond_12

    .line 300
    .line 301
    invoke-virtual {v12}, Ljava/lang/Integer;->intValue()I

    .line 302
    .line 303
    .line 304
    move-result v12

    .line 305
    goto :goto_c

    .line 306
    :cond_12
    const/4 v12, 0x0

    .line 307
    :goto_c
    invoke-virtual {v3}, LCN0/a;->a()Ljava/lang/Integer;

    .line 308
    .line 309
    .line 310
    move-result-object v13

    .line 311
    if-eqz v13, :cond_13

    .line 312
    .line 313
    invoke-virtual {v13}, Ljava/lang/Integer;->intValue()I

    .line 314
    .line 315
    .line 316
    move-result v13

    .line 317
    goto :goto_d

    .line 318
    :cond_13
    const/4 v13, 0x0

    .line 319
    :goto_d
    invoke-virtual {v3}, LCN0/a;->i()Ljava/lang/Integer;

    .line 320
    .line 321
    .line 322
    move-result-object v3

    .line 323
    if-eqz v3, :cond_14

    .line 324
    .line 325
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 326
    .line 327
    .line 328
    move-result v7

    .line 329
    move v14, v7

    .line 330
    :goto_e
    move-object v7, v4

    .line 331
    goto :goto_f

    .line 332
    :cond_14
    const/4 v14, 0x0

    .line 333
    goto :goto_e

    .line 334
    :goto_f
    new-instance v4, LtO0/a;

    .line 335
    .line 336
    move-object/from16 v16, v10

    .line 337
    .line 338
    move-object v10, v8

    .line 339
    move-object v8, v9

    .line 340
    move-object/from16 v9, v16

    .line 341
    .line 342
    invoke-direct/range {v4 .. v14}, LtO0/a;-><init>(Ljava/lang/String;Lorg/xbet/statistic/domain/model/shortgame/EventStatusType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIII)V

    .line 343
    .line 344
    .line 345
    invoke-interface {v2, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 346
    .line 347
    .line 348
    goto/16 :goto_0

    .line 349
    .line 350
    :cond_15
    new-instance v0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 351
    .line 352
    const/4 v2, 0x1

    .line 353
    invoke-direct {v0, v1, v2, v1}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 354
    .line 355
    .line 356
    throw v0

    .line 357
    :cond_16
    move-object v1, v2

    .line 358
    :cond_17
    if-nez v1, :cond_18

    .line 359
    .line 360
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 361
    .line 362
    .line 363
    move-result-object v0

    .line 364
    return-object v0

    .line 365
    :cond_18
    return-object v1
.end method
