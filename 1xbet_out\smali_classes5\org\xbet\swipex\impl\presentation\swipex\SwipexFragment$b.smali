.class public final Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00003\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0007\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\r\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u000c\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u000f\u0010\u000f\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\nJ\u001f\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J!\u0010\u0016\u001a\u00020\u00062\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0015\u00a8\u0006\u0017"
    }
    d2 = {
        "org/xbet/swipex/impl/presentation/swipex/SwipexFragment$b",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;",
        "actionType",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;",
        "swipeType",
        "",
        "a",
        "(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V",
        "d",
        "()V",
        "",
        "ratio",
        "f",
        "(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;F)V",
        "b",
        "Landroid/view/View;",
        "view",
        "",
        "position",
        "e",
        "(Landroid/view/View;I)V",
        "c",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;->b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;->Pass:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;

    .line 2
    .line 3
    if-ne p1, v0, :cond_0

    .line 4
    .line 5
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;->b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 6
    .line 7
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Q2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->y4(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;->b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 15
    .line 16
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Q2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->E4()V

    .line 21
    .line 22
    .line 23
    return-void

    .line 24
    :cond_0
    iget-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;->b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 25
    .line 26
    invoke-static {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Q2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->J4(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipeType;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public b()V
    .locals 0

    .line 1
    return-void
.end method

.method public c(Landroid/view/View;I)V
    .locals 0

    .line 1
    return-void
.end method

.method public d()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;->b:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Q2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->C4()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public e(Landroid/view/View;I)V
    .locals 0

    .line 1
    return-void
.end method

.method public f(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/ActionType;F)V
    .locals 0

    .line 1
    return-void
.end method
