.class public final enum Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/api/domain/model/GameCategory;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "Default"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u000b\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\u0008\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;",
        "",
        "categoryId",
        "",
        "<init>",
        "(Ljava/lang/String;IJ)V",
        "getCategoryId",
        "()J",
        "ONE_X_LIVE_AGGREGATOR",
        "POPULAR",
        "RECOMMENDED",
        "LIVE_AGGREGATOR",
        "SLOTS",
        "UNKNOWN",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum SLOTS:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

.field public static final enum UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;


# instance fields
.field private final categoryId:J


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const-wide/16 v2, 0x4b

    .line 5
    .line 6
    const-string v4, "ONE_X_LIVE_AGGREGATOR"

    .line 7
    .line 8
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 9
    .line 10
    .line 11
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 12
    .line 13
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    const-wide/16 v2, 0x11

    .line 17
    .line 18
    const-string v4, "POPULAR"

    .line 19
    .line 20
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 21
    .line 22
    .line 23
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 24
    .line 25
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 26
    .line 27
    const/4 v1, 0x2

    .line 28
    const-wide/16 v2, -0x1

    .line 29
    .line 30
    const-string v4, "RECOMMENDED"

    .line 31
    .line 32
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 33
    .line 34
    .line 35
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 36
    .line 37
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 38
    .line 39
    const/4 v1, 0x3

    .line 40
    const-wide/16 v2, 0x25

    .line 41
    .line 42
    const-string v4, "LIVE_AGGREGATOR"

    .line 43
    .line 44
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 45
    .line 46
    .line 47
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 48
    .line 49
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 50
    .line 51
    const/4 v1, 0x4

    .line 52
    const-wide/16 v2, 0x1

    .line 53
    .line 54
    const-string v4, "SLOTS"

    .line 55
    .line 56
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->SLOTS:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 62
    .line 63
    const/4 v1, 0x5

    .line 64
    const-wide/16 v2, -0x2

    .line 65
    .line 66
    const-string v4, "UNKNOWN"

    .line 67
    .line 68
    invoke-direct {v0, v4, v1, v2, v3}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;-><init>(Ljava/lang/String;IJ)V

    .line 69
    .line 70
    .line 71
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 72
    .line 73
    invoke-static {}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->a()[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->$VALUES:[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 78
    .line 79
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    sput-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->$ENTRIES:Lkotlin/enums/a;

    .line 84
    .line 85
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;IJ)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput-wide p3, p0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->categoryId:J

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;
    .locals 3

    .line 1
    const/4 v0, 0x6

    new-array v0, v0, [Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->POPULAR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->RECOMMENDED:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->SLOTS:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->UNKNOWN:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->$VALUES:[Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getCategoryId()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->categoryId:J

    .line 2
    .line 3
    return-wide v0
.end method
