.class public final synthetic Lorg/xbet/uikit_web_games/game_card/itemview/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/d;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/d;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    invoke-static {v0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->e(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V

    return-void
.end method
