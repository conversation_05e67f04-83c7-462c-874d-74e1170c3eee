.class public final Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0010\u0003\n\u0002\u0008\n\u0008\u0001\u0018\u0000 \"2\u00020\u0001:\u0001\u001cB!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ+\u0010\u0010\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\u0006\u0010\u000b\u001a\u00020\n2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J@\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u000b\u001a\u00020\n2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u0014H\u0082@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0013\u0010\u001a\u001a\u00020\u0014*\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!\u00a8\u0006#"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
        "",
        "LEy0/a;",
        "stageTableRepository",
        "Lo9/a;",
        "userRepository",
        "LAu/b;",
        "coefViewPrefsRepository",
        "<init>",
        "(LEy0/a;Lo9/a;LAu/b;)V",
        "",
        "eventId",
        "userRegistrationCountryId",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "LDy0/a;",
        "g",
        "(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
        "enCoefView",
        "",
        "hasAuthorized",
        "initRequestInternal",
        "e",
        "(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "f",
        "(Ljava/lang/Throwable;)Z",
        "a",
        "LEy0/a;",
        "b",
        "Lo9/a;",
        "c",
        "LAu/b;",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final e:I


# instance fields
.field public final a:LEy0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lo9/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LAu/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->d:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->e:I

    return-void
.end method

.method public constructor <init>(LEy0/a;Lo9/a;LAu/b;)V
    .locals 0
    .param p1    # LEy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lo9/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LAu/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->a:LEy0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->b:Lo9/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c:LAu/b;

    .line 9
    .line 10
    return-void
.end method

.method public static final synthetic a(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->e(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)LEy0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->a:LEy0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;)Lo9/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->b:Lo9/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Ljava/lang/Throwable;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->f(Ljava/lang/Throwable;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method


# virtual methods
.method public final e(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;ZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/Integer;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "ZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;

    .line 23
    .line 24
    invoke-direct {v1, p0, v0}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object v0, v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v11

    .line 33
    iget v2, v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;->label:I

    .line 34
    .line 35
    const/4 v9, 0x1

    .line 36
    if-eqz v2, :cond_2

    .line 37
    .line 38
    if-ne v2, v9, :cond_1

    .line 39
    .line 40
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    check-cast v0, Lkotlin/Result;

    .line 44
    .line 45
    invoke-virtual {v0}, Lkotlin/Result;->unbox-impl()Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    goto :goto_2

    .line 50
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw v0

    .line 58
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    const-class v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 62
    .line 63
    invoke-virtual {v0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    if-eqz p5, :cond_3

    .line 68
    .line 69
    const/4 v2, 0x4

    .line 70
    const/4 v10, 0x4

    .line 71
    goto :goto_1

    .line 72
    :cond_3
    const/4 v2, 0x0

    .line 73
    const/4 v10, 0x0

    .line 74
    :goto_1
    sget-object v2, Lkotlin/time/d;->b:Lkotlin/time/d$a;

    .line 75
    .line 76
    const-wide/16 v4, 0x3

    .line 77
    .line 78
    sget-object v2, Lkotlin/time/DurationUnit;->SECONDS:Lkotlin/time/DurationUnit;

    .line 79
    .line 80
    invoke-static {v4, v5, v2}, Lkotlin/time/f;->t(JLkotlin/time/DurationUnit;)J

    .line 81
    .line 82
    .line 83
    move-result-wide v12

    .line 84
    new-instance v2, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;

    .line 85
    .line 86
    const/4 v8, 0x0

    .line 87
    move-object v3, p0

    .line 88
    move v5, p1

    .line 89
    move-object/from16 v6, p2

    .line 90
    .line 91
    move-object/from16 v7, p3

    .line 92
    .line 93
    move/from16 v4, p4

    .line 94
    .line 95
    invoke-direct/range {v2 .. v8}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$2;-><init>(Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ZILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Lkotlin/coroutines/e;)V

    .line 96
    .line 97
    .line 98
    iput v9, v1, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$fetchStageTableDataWithRetry$1;->label:I

    .line 99
    .line 100
    const/4 v6, 0x0

    .line 101
    const/16 v9, 0x8

    .line 102
    .line 103
    move v3, v10

    .line 104
    const/4 v10, 0x0

    .line 105
    move-object v8, v1

    .line 106
    move-object v7, v2

    .line 107
    move-wide v4, v12

    .line 108
    move-object v2, v0

    .line 109
    invoke-static/range {v2 .. v10}, Lcom/xbet/onexcore/utils/ext/ResultExtensionKt;->c(Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    if-ne v0, v11, :cond_4

    .line 114
    .line 115
    return-object v11

    .line 116
    :cond_4
    :goto_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    return-object v0
.end method

.method public final f(Ljava/lang/Throwable;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 6
    .line 7
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorCode()Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    sget-object v0, Lcom/xbet/onexcore/data/errors/ErrorsCode;->IncorrectDataError:Lcom/xbet/onexcore/data/errors/ErrorsCode;

    .line 12
    .line 13
    if-ne p1, v0, :cond_0

    .line 14
    .line 15
    const/4 p1, 0x1

    .line 16
    return p1

    .line 17
    :cond_0
    const/4 p1, 0x0

    .line 18
    return p1
.end method

.method public final g(ILjava/lang/Integer;)Lkotlinx/coroutines/flow/e;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/Integer;",
            ")",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v5, Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 2
    .line 3
    invoke-direct {v5}, Lkotlin/jvm/internal/Ref$BooleanRef;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, v5, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;->c:LAu/b;

    .line 10
    .line 11
    invoke-interface {v0}, LAu/b;->d()Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object v6

    .line 15
    new-instance v0, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;

    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    move-object v2, p0

    .line 19
    move v3, p1

    .line 20
    move-object v4, p2

    .line 21
    invoke-direct/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase$invoke$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;ILjava/lang/Integer;Lkotlin/jvm/internal/Ref$BooleanRef;)V

    .line 22
    .line 23
    .line 24
    invoke-static {v6, v0}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    return-object p1
.end method
