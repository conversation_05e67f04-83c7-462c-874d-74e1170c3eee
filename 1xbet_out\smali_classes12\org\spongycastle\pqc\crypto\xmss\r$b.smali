.class public Lorg/spongycastle/pqc/crypto/xmss/r$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/spongycastle/pqc/crypto/xmss/r;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field public final a:Lorg/spongycastle/pqc/crypto/xmss/q;

.field public b:I

.field public c:[B

.field public d:[B

.field public e:[B

.field public f:[B

.field public g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

.field public h:[B

.field public i:Lorg/spongycastle/pqc/crypto/xmss/q;


# direct methods
.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/q;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    iput v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->b:I

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->c:[B

    .line 9
    .line 10
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->d:[B

    .line 11
    .line 12
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->e:[B

    .line 13
    .line 14
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->f:[B

    .line 15
    .line 16
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 17
    .line 18
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->h:[B

    .line 19
    .line 20
    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->i:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 21
    .line 22
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->a:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 23
    .line 24
    return-void
.end method

.method public static synthetic a(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->a:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic b(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->h:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic c(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/q;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->i:Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic d(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->c:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic e(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->d:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic f(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->e:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic g(Lorg/spongycastle/pqc/crypto/xmss/r$b;)[B
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->f:[B

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic h(Lorg/spongycastle/pqc/crypto/xmss/r$b;)Lorg/spongycastle/pqc/crypto/xmss/BDS;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 2
    .line 3
    return-object p0
.end method

.method public static synthetic i(Lorg/spongycastle/pqc/crypto/xmss/r$b;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->b:I

    .line 2
    .line 3
    return p0
.end method


# virtual methods
.method public j()Lorg/spongycastle/pqc/crypto/xmss/r;
    .locals 2

    .line 1
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/r;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, v1}, Lorg/spongycastle/pqc/crypto/xmss/r;-><init>(Lorg/spongycastle/pqc/crypto/xmss/r$b;Lorg/spongycastle/pqc/crypto/xmss/r$a;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method

.method public k(Lorg/spongycastle/pqc/crypto/xmss/BDS;)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->g:Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 2
    .line 3
    return-object p0
.end method

.method public l(I)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    iput p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->b:I

    .line 2
    .line 3
    return-object p0
.end method

.method public m([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->e:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public n([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->f:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public o([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->d:[B

    .line 6
    .line 7
    return-object p0
.end method

.method public p([B)Lorg/spongycastle/pqc/crypto/xmss/r$b;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/spongycastle/pqc/crypto/xmss/t;->c([B)[B

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, Lorg/spongycastle/pqc/crypto/xmss/r$b;->c:[B

    .line 6
    .line 7
    return-object p0
.end method
