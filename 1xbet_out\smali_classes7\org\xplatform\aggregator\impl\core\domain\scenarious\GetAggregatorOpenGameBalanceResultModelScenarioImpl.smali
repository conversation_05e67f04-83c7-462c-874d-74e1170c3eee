.class public final Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Le81/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ \u0010\u0011\u001a\u00020\u00102\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096B\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0010\u0010\u0013\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0015R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0018R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;",
        "Le81/b;",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lfk/k;",
        "getBalancesUseCase",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lfk/n;",
        "getSavedBalanceIdUseCase",
        "<init>",
        "(Lp9/c;Lfk/k;Lfk/l;Lfk/n;)V",
        "",
        "gameId",
        "",
        "needTransfer",
        "Ld81/a;",
        "a",
        "(JZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "c",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lp9/c;",
        "b",
        "Lfk/k;",
        "Lfk/l;",
        "d",
        "Lfk/n;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lfk/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lfk/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lp9/c;Lfk/k;Lfk/l;Lfk/n;)V
    .locals 0
    .param p1    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lfk/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lfk/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->a:Lp9/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->b:Lfk/k;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->c:Lfk/l;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->d:Lfk/n;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public a(JZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Ld81/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of p1, p4, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    move-object p1, p4

    .line 6
    check-cast p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;

    .line 7
    .line 8
    iget p2, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v0, -0x80000000

    .line 11
    .line 12
    and-int v1, p2, v0

    .line 13
    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    sub-int/2addr p2, v0

    .line 17
    iput p2, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;

    .line 21
    .line 22
    invoke-direct {p1, p0, p4}, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object p4

    .line 31
    iget v0, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v1, 0x2

    .line 34
    const/4 v2, 0x1

    .line 35
    if-eqz v0, :cond_3

    .line 36
    .line 37
    if-eq v0, v2, :cond_2

    .line 38
    .line 39
    if-ne v0, v1, :cond_1

    .line 40
    .line 41
    iget-object p1, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 44
    .line 45
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto/16 :goto_4

    .line 49
    .line 50
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw p1

    .line 58
    :cond_2
    iget-boolean p3, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->Z$0:Z

    .line 59
    .line 60
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 65
    .line 66
    .line 67
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->a:Lp9/c;

    .line 68
    .line 69
    invoke-virtual {p2}, Lp9/c;->a()Z

    .line 70
    .line 71
    .line 72
    move-result p2

    .line 73
    if-nez p2, :cond_4

    .line 74
    .line 75
    sget-object p1, Ld81/a$d;->a:Ld81/a$d;

    .line 76
    .line 77
    return-object p1

    .line 78
    :cond_4
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->b:Lfk/k;

    .line 79
    .line 80
    iput-boolean p3, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->Z$0:Z

    .line 81
    .line 82
    iput v2, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->label:I

    .line 83
    .line 84
    const/4 v0, 0x0

    .line 85
    invoke-static {p2, v0, p1, v2, v0}, Lfk/k$a;->a(Lfk/k;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object p2

    .line 89
    if-ne p2, p4, :cond_5

    .line 90
    .line 91
    goto :goto_3

    .line 92
    :cond_5
    :goto_1
    check-cast p2, Ljava/lang/Iterable;

    .line 93
    .line 94
    new-instance v0, Ljava/util/ArrayList;

    .line 95
    .line 96
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 97
    .line 98
    .line 99
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 100
    .line 101
    .line 102
    move-result-object p2

    .line 103
    :cond_6
    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 104
    .line 105
    .line 106
    move-result v3

    .line 107
    if-eqz v3, :cond_7

    .line 108
    .line 109
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    move-object v4, v3

    .line 114
    check-cast v4, Lorg/xbet/balance/model/BalanceModel;

    .line 115
    .line 116
    invoke-virtual {v4}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 117
    .line 118
    .line 119
    move-result-object v4

    .line 120
    invoke-virtual {v4}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isSlotAccount()Z

    .line 121
    .line 122
    .line 123
    move-result v4

    .line 124
    if-eqz v4, :cond_6

    .line 125
    .line 126
    invoke-interface {v0, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 127
    .line 128
    .line 129
    goto :goto_2

    .line 130
    :cond_7
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 131
    .line 132
    .line 133
    move-result p2

    .line 134
    if-eqz p2, :cond_8

    .line 135
    .line 136
    sget-object p1, Ld81/a$b;->a:Ld81/a$b;

    .line 137
    .line 138
    return-object p1

    .line 139
    :cond_8
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 140
    .line 141
    .line 142
    move-result p2

    .line 143
    if-le p2, v2, :cond_9

    .line 144
    .line 145
    sget-object p1, Ld81/a$a;->a:Ld81/a$a;

    .line 146
    .line 147
    return-object p1

    .line 148
    :cond_9
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->x0(Ljava/util/List;)Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object p2

    .line 152
    check-cast p2, Lorg/xbet/balance/model/BalanceModel;

    .line 153
    .line 154
    if-eqz p3, :cond_a

    .line 155
    .line 156
    invoke-virtual {p2}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    invoke-virtual {v0}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isBonus()Z

    .line 161
    .line 162
    .line 163
    move-result v0

    .line 164
    if-eqz v0, :cond_a

    .line 165
    .line 166
    sget-object p1, Ld81/a$e;->a:Ld81/a$e;

    .line 167
    .line 168
    return-object p1

    .line 169
    :cond_a
    if-nez p3, :cond_d

    .line 170
    .line 171
    iput-object p2, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->L$0:Ljava/lang/Object;

    .line 172
    .line 173
    iput v1, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$invoke$1;->label:I

    .line 174
    .line 175
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->c(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object p1

    .line 179
    if-ne p1, p4, :cond_b

    .line 180
    .line 181
    :goto_3
    return-object p4

    .line 182
    :cond_b
    move-object v5, p2

    .line 183
    move-object p2, p1

    .line 184
    move-object p1, v5

    .line 185
    :goto_4
    check-cast p2, Ljava/lang/Boolean;

    .line 186
    .line 187
    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    .line 188
    .line 189
    .line 190
    move-result p2

    .line 191
    if-eqz p2, :cond_c

    .line 192
    .line 193
    sget-object p1, Ld81/a$c;->a:Ld81/a$c;

    .line 194
    .line 195
    return-object p1

    .line 196
    :cond_c
    move-object p2, p1

    .line 197
    :cond_d
    new-instance p1, Ld81/a$f;

    .line 198
    .line 199
    invoke-virtual {p2}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 200
    .line 201
    .line 202
    move-result-wide p2

    .line 203
    invoke-direct {p1, p2, p3}, Ld81/a$f;-><init>(J)V

    .line 204
    .line 205
    .line 206
    return-object p1
.end method

.method public final c(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;-><init>(Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->d:Lfk/n;

    .line 54
    .line 55
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 56
    .line 57
    invoke-interface {p1, v2}, Lfk/n;->a(Lorg/xbet/balance/model/BalanceScreenType;)J

    .line 58
    .line 59
    .line 60
    move-result-wide v4

    .line 61
    const-wide/16 v6, 0x0

    .line 62
    .line 63
    cmp-long p1, v4, v6

    .line 64
    .line 65
    if-gtz p1, :cond_3

    .line 66
    .line 67
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->MULTI:Lorg/xbet/balance/model/BalanceScreenType;

    .line 68
    .line 69
    :cond_3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl;->c:Lfk/l;

    .line 70
    .line 71
    iput v3, v0, Lorg/xplatform/aggregator/impl/core/domain/scenarious/GetAggregatorOpenGameBalanceResultModelScenarioImpl$isInvalidBonusAccount$1;->label:I

    .line 72
    .line 73
    invoke-interface {p1, v2, v0}, Lfk/l;->a(Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    if-ne p1, v1, :cond_4

    .line 78
    .line 79
    return-object v1

    .line 80
    :cond_4
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 81
    .line 82
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    sget-object v1, Lcom/xbet/onexcore/data/configs/TypeAccount;->SPORT_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 87
    .line 88
    if-eq v0, v1, :cond_6

    .line 89
    .line 90
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    sget-object v0, Lcom/xbet/onexcore/data/configs/TypeAccount;->GAME_BONUS:Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 95
    .line 96
    if-ne p1, v0, :cond_5

    .line 97
    .line 98
    goto :goto_2

    .line 99
    :cond_5
    const/4 v3, 0x0

    .line 100
    :cond_6
    :goto_2
    invoke-static {v3}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    return-object p1
.end method
