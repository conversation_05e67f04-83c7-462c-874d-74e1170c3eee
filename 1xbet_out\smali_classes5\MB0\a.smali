.class public final LMB0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LuB0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0007\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u000f\u0010\u0007\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0006J\u000f\u0010\u0008\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0006J\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LMB0/a;",
        "LuB0/a;",
        "<init>",
        "()V",
        "",
        "B",
        "()Ljava/lang/String;",
        "a",
        "getTag",
        "Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;",
        "params",
        "Landroidx/fragment/app/Fragment;",
        "b",
        "(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;)Landroidx/fragment/app/Fragment;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public B()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const-string v0, "PLAYERS_DUEL_TEAM_REQUEST_KEY"

    .line 2
    .line 3
    return-object v0
.end method

.method public a()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const-string v0, "PLAYERS_DUEL_REFRESHED_TEAM_RESULT_KEY"

    .line 2
    .line 3
    return-object v0
.end method

.method public b(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;)Landroidx/fragment/app/Fragment;
    .locals 1
    .param p1    # Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$DuelSubGame;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    sget-object v0, Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;->P1:Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment$a;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment$a;->a(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1

    .line 12
    :cond_0
    instance-of v0, p1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$InsightsSubGame;

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    sget-object v0, Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;->P1:Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment$a;

    .line 17
    .line 18
    check-cast p1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$InsightsSubGame;

    .line 19
    .line 20
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment$a;->a(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$InsightsSubGame;)Lorg/xbet/sportgame/markets/impl/presentation/insights/InsightsMarketsFragment;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    return-object p1

    .line 25
    :cond_1
    instance-of v0, p1, Lorg/xbet/sportgame/markets/api/navigation/MarketsParams$StandardSubGame;

    .line 26
    .line 27
    if-eqz v0, :cond_2

    .line 28
    .line 29
    sget-object v0, Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;->P1:Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment$a;

    .line 30
    .line 31
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment$a;->a(Lorg/xbet/sportgame/markets/api/navigation/MarketsParams;)Lorg/xbet/sportgame/markets/impl/presentation/markets/MarketsFragment;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    return-object p1

    .line 36
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 37
    .line 38
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 39
    .line 40
    .line 41
    throw p1
.end method

.method public getTag()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const-string v0, "MARKETS_FRAGMENT_TAG"

    .line 2
    .line 3
    return-object v0
.end method
