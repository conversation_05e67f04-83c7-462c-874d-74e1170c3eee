.class public interface abstract Lk2/k;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a(I)J
.end method

.method public abstract b()I
.end method

.method public abstract c(J)I
.end method

.method public abstract g(J)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J)",
            "Ljava/util/List<",
            "Ls1/a;",
            ">;"
        }
    .end annotation
.end method
