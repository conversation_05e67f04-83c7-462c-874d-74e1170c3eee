.class public final LSG0/i$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LSG0/i;->e(Lkotlin/jvm/functions/Function1;LWG0/d;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "LOc/n<",
        "Landroidx/compose/foundation/layout/m;",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LWG0/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LWG0/d<",
            "LSG0/j;",
            ">;"
        }
    .end annotation
.end field

.field public final synthetic b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LWG0/d;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LWG0/d<",
            "LSG0/j;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/statistic/main/common/presentation/viewmodel/g0;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LSG0/i$a;->a:LWG0/d;

    .line 2
    .line 3
    iput-object p2, p0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic a(Lkotlin/jvm/functions/Function1;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LSG0/i$a;->k(Lkotlin/jvm/functions/Function1;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LSG0/i$a;->f(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/functions/Function1;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LSG0/i$a;->g(Lkotlin/jvm/functions/Function1;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LSG0/i$a;->j(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private static final f(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;->GAME_EVENT:Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$d;->b(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$d;->a(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$d;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private static final g(Lkotlin/jvm/functions/Function1;J)Lkotlin/Unit;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;->GAME_EVENT:Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 4
    .line 5
    invoke-direct {v0, v1, p1, p2}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$c;-><init>(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;J)V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method private static final j(Lkotlin/jvm/functions/Function1;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;->GAME_EVENT:Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$b;->b(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$b;->a(Lorg/xbet/statistic/main/common/domain/model/StatisticBlockWidget;)Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$a$b;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private static final k(Lkotlin/jvm/functions/Function1;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p1}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$b;->b(Ljava/lang/String;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-static {p1}, Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$b;->a(Ljava/lang/String;)Lorg/xbet/statistic/main/common/presentation/viewmodel/g0$b;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method


# virtual methods
.method public final e(Landroidx/compose/foundation/layout/m;Landroidx/compose/runtime/j;I)V
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v6, p2

    .line 4
    .line 5
    move/from16 v1, p3

    .line 6
    .line 7
    and-int/lit8 v2, v1, 0x11

    .line 8
    .line 9
    const/16 v3, 0x10

    .line 10
    .line 11
    if-ne v2, v3, :cond_1

    .line 12
    .line 13
    invoke-interface {v6}, Landroidx/compose/runtime/j;->c()Z

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    if-nez v2, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    invoke-interface {v6}, Landroidx/compose/runtime/j;->n()V

    .line 21
    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-eqz v2, :cond_2

    .line 29
    .line 30
    const/4 v2, -0x1

    .line 31
    const-string v3, "org.xbet.statistic.main.common.presentation.game_event.GameEventComponent.<anonymous> (GameEventComponent.kt:46)"

    .line 32
    .line 33
    const v4, 0x1519521f

    .line 34
    .line 35
    .line 36
    invoke-static {v4, v1, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 37
    .line 38
    .line 39
    :cond_2
    iget-object v1, v0, LSG0/i$a;->a:LWG0/d;

    .line 40
    .line 41
    invoke-virtual {v1}, LWG0/d;->d()LWG0/c;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    instance-of v2, v1, LWG0/c$a;

    .line 46
    .line 47
    const v3, 0x6e3c21fe

    .line 48
    .line 49
    .line 50
    if-eqz v2, :cond_6

    .line 51
    .line 52
    const v1, 0x4d9d77b1    # 3.30233376E8f

    .line 53
    .line 54
    .line 55
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 56
    .line 57
    .line 58
    iget-object v1, v0, LSG0/i$a;->a:LWG0/d;

    .line 59
    .line 60
    invoke-virtual {v1}, LWG0/d;->d()LWG0/c;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    check-cast v1, LWG0/c$a;

    .line 65
    .line 66
    invoke-virtual {v1}, LWG0/c$a;->b()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 67
    .line 68
    .line 69
    move-result-object v7

    .line 70
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 71
    .line 72
    .line 73
    iget-object v1, v0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 74
    .line 75
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v2

    .line 79
    sget-object v3, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 80
    .line 81
    invoke-virtual {v3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v4

    .line 85
    if-ne v2, v4, :cond_3

    .line 86
    .line 87
    new-instance v2, LSG0/e;

    .line 88
    .line 89
    invoke-direct {v2, v1}, LSG0/e;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 90
    .line 91
    .line 92
    invoke-interface {v6, v2}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 93
    .line 94
    .line 95
    :cond_3
    move-object/from16 v16, v2

    .line 96
    .line 97
    check-cast v16, Lkotlin/jvm/functions/Function0;

    .line 98
    .line 99
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 100
    .line 101
    .line 102
    const/16 v17, 0xff

    .line 103
    .line 104
    const/16 v18, 0x0

    .line 105
    .line 106
    const/4 v8, 0x0

    .line 107
    const/4 v9, 0x0

    .line 108
    const/4 v10, 0x0

    .line 109
    const/4 v11, 0x0

    .line 110
    const/4 v12, 0x0

    .line 111
    const/4 v13, 0x0

    .line 112
    const/4 v14, 0x0

    .line 113
    const/4 v15, 0x0

    .line 114
    invoke-static/range {v7 .. v18}, Lorg/xbet/uikit/components/lottie_empty/n;->b(Lorg/xbet/uikit/components/lottie_empty/n;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 115
    .line 116
    .line 117
    move-result-object v2

    .line 118
    iget-object v1, v0, LSG0/i$a;->a:LWG0/d;

    .line 119
    .line 120
    invoke-virtual {v1}, LWG0/d;->d()LWG0/c;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    check-cast v1, LWG0/c$a;

    .line 125
    .line 126
    invoke-virtual {v1}, LWG0/c$a;->a()J

    .line 127
    .line 128
    .line 129
    move-result-wide v4

    .line 130
    const v1, 0x4c5de2

    .line 131
    .line 132
    .line 133
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 134
    .line 135
    .line 136
    iget-object v1, v0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 137
    .line 138
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 139
    .line 140
    .line 141
    move-result v1

    .line 142
    iget-object v7, v0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 143
    .line 144
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object v8

    .line 148
    if-nez v1, :cond_4

    .line 149
    .line 150
    invoke-virtual {v3}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    move-result-object v1

    .line 154
    if-ne v8, v1, :cond_5

    .line 155
    .line 156
    :cond_4
    new-instance v8, LSG0/f;

    .line 157
    .line 158
    invoke-direct {v8, v7}, LSG0/f;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 159
    .line 160
    .line 161
    invoke-interface {v6, v8}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 162
    .line 163
    .line 164
    :cond_5
    check-cast v8, Lkotlin/jvm/functions/Function1;

    .line 165
    .line 166
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 167
    .line 168
    .line 169
    sget v1, Lorg/xbet/uikit/components/lottie_empty/n;->j:I

    .line 170
    .line 171
    shl-int/lit8 v7, v1, 0x3

    .line 172
    .line 173
    move-wide v3, v4

    .line 174
    move-object v5, v8

    .line 175
    const/4 v8, 0x1

    .line 176
    const/4 v1, 0x0

    .line 177
    invoke-static/range {v1 .. v8}, LJN0/i;->f(Landroidx/compose/ui/l;Lorg/xbet/uikit/components/lottie_empty/n;JLkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 178
    .line 179
    .line 180
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 181
    .line 182
    .line 183
    goto/16 :goto_1

    .line 184
    .line 185
    :cond_6
    sget-object v2, LWG0/c$b;->a:LWG0/c$b;

    .line 186
    .line 187
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 188
    .line 189
    .line 190
    move-result v2

    .line 191
    if-eqz v2, :cond_7

    .line 192
    .line 193
    const v1, 0x340de651

    .line 194
    .line 195
    .line 196
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 197
    .line 198
    .line 199
    const/4 v1, 0x0

    .line 200
    const/4 v2, 0x1

    .line 201
    const/4 v3, 0x0

    .line 202
    invoke-static {v3, v6, v1, v2}, LJN0/c;->c(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 203
    .line 204
    .line 205
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 206
    .line 207
    .line 208
    goto :goto_1

    .line 209
    :cond_7
    instance-of v1, v1, LWG0/c$c;

    .line 210
    .line 211
    if-eqz v1, :cond_b

    .line 212
    .line 213
    const v1, 0x4db0af3f    # 3.70534368E8f

    .line 214
    .line 215
    .line 216
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 217
    .line 218
    .line 219
    iget-object v1, v0, LSG0/i$a;->a:LWG0/d;

    .line 220
    .line 221
    invoke-virtual {v1}, LWG0/d;->d()LWG0/c;

    .line 222
    .line 223
    .line 224
    move-result-object v1

    .line 225
    check-cast v1, LWG0/c$c;

    .line 226
    .line 227
    invoke-virtual {v1}, LWG0/c$c;->a()LWG0/a;

    .line 228
    .line 229
    .line 230
    move-result-object v1

    .line 231
    check-cast v1, LSG0/j;

    .line 232
    .line 233
    invoke-virtual {v1}, LSG0/j;->a()LHd/c;

    .line 234
    .line 235
    .line 236
    move-result-object v1

    .line 237
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 238
    .line 239
    .line 240
    iget-object v2, v0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 241
    .line 242
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 243
    .line 244
    .line 245
    move-result-object v4

    .line 246
    sget-object v5, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 247
    .line 248
    invoke-virtual {v5}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 249
    .line 250
    .line 251
    move-result-object v7

    .line 252
    if-ne v4, v7, :cond_8

    .line 253
    .line 254
    new-instance v4, LSG0/g;

    .line 255
    .line 256
    invoke-direct {v4, v2}, LSG0/g;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 257
    .line 258
    .line 259
    invoke-interface {v6, v4}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 260
    .line 261
    .line 262
    :cond_8
    check-cast v4, Lkotlin/jvm/functions/Function0;

    .line 263
    .line 264
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 265
    .line 266
    .line 267
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->t(I)V

    .line 268
    .line 269
    .line 270
    iget-object v2, v0, LSG0/i$a;->b:Lkotlin/jvm/functions/Function1;

    .line 271
    .line 272
    invoke-interface {v6}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 273
    .line 274
    .line 275
    move-result-object v3

    .line 276
    invoke-virtual {v5}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 277
    .line 278
    .line 279
    move-result-object v5

    .line 280
    if-ne v3, v5, :cond_9

    .line 281
    .line 282
    new-instance v3, LSG0/h;

    .line 283
    .line 284
    invoke-direct {v3, v2}, LSG0/h;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 285
    .line 286
    .line 287
    invoke-interface {v6, v3}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 288
    .line 289
    .line 290
    :cond_9
    check-cast v3, Lkotlin/jvm/functions/Function1;

    .line 291
    .line 292
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 293
    .line 294
    .line 295
    sget v2, Ls31/a;->a:I

    .line 296
    .line 297
    or-int/lit16 v2, v2, 0x1b0

    .line 298
    .line 299
    invoke-static {v1, v4, v3, v6, v2}, LSG0/i;->k(LHd/c;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V

    .line 300
    .line 301
    .line 302
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 303
    .line 304
    .line 305
    :goto_1
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 306
    .line 307
    .line 308
    move-result v1

    .line 309
    if-eqz v1, :cond_a

    .line 310
    .line 311
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 312
    .line 313
    .line 314
    :cond_a
    return-void

    .line 315
    :cond_b
    const v1, 0x340d5179

    .line 316
    .line 317
    .line 318
    invoke-interface {v6, v1}, Landroidx/compose/runtime/j;->t(I)V

    .line 319
    .line 320
    .line 321
    invoke-interface {v6}, Landroidx/compose/runtime/j;->q()V

    .line 322
    .line 323
    .line 324
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 325
    .line 326
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 327
    .line 328
    .line 329
    throw v1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/foundation/layout/m;

    .line 2
    .line 3
    check-cast p2, Landroidx/compose/runtime/j;

    .line 4
    .line 5
    check-cast p3, Ljava/lang/Number;

    .line 6
    .line 7
    invoke-virtual {p3}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    invoke-virtual {p0, p1, p2, p3}, LSG0/i$a;->e(Landroidx/compose/foundation/layout/m;Landroidx/compose/runtime/j;I)V

    .line 12
    .line 13
    .line 14
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method
