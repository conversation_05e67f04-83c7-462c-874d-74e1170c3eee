.class public abstract Lk4/b;
.super Lp4/f$a;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public c:[F

.field public d:Lp4/j;

.field public e:F

.field public f:F

.field public g:Lp4/g;

.field public h:Landroid/view/View;


# direct methods
.method public constructor <init>(Lp4/j;FFLp4/g;Landroid/view/View;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lp4/f$a;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x2

    .line 5
    new-array v0, v0, [F

    .line 6
    .line 7
    iput-object v0, p0, Lk4/b;->c:[F

    .line 8
    .line 9
    iput-object p1, p0, Lk4/b;->d:Lp4/j;

    .line 10
    .line 11
    iput p2, p0, Lk4/b;->e:F

    .line 12
    .line 13
    iput p3, p0, Lk4/b;->f:F

    .line 14
    .line 15
    iput-object p4, p0, Lk4/b;->g:Lp4/g;

    .line 16
    .line 17
    iput-object p5, p0, Lk4/b;->h:Landroid/view/View;

    .line 18
    .line 19
    return-void
.end method
