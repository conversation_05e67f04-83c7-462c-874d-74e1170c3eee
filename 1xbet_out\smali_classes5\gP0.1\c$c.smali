.class public final LgP0/c$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LgP0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\t\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\nR\u0014\u0010\u000b\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\nR\u0014\u0010\u000c\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\nR\u0014\u0010\r\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\nR\u0014\u0010\u000e\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\nR\u0014\u0010\u000f\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\nR\u0014\u0010\u0010\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\n\u00a8\u0006\u0011"
    }
    d2 = {
        "LgP0/c$c;",
        "",
        "<init>",
        "()V",
        "",
        "typeId",
        "LgP0/c;",
        "a",
        "(I)LgP0/c;",
        "TEAM_SQUAD_ID",
        "I",
        "ARENA_ID",
        "PLAYERS_TRANSFERS_ID",
        "PAST_GAMES_ID",
        "FUTURE_GAMES_ID",
        "RATING_ID",
        "RATING_HISTORY_ID",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LgP0/c$c;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(I)LgP0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/16 v0, 0x37

    .line 2
    .line 3
    if-eq p1, v0, :cond_0

    .line 4
    .line 5
    packed-switch p1, :pswitch_data_0

    .line 6
    .line 7
    .line 8
    sget-object p1, LgP0/c$j;->e:LgP0/c$j;

    .line 9
    .line 10
    return-object p1

    .line 11
    :pswitch_0
    sget-object p1, LgP0/c$h;->e:LgP0/c$h;

    .line 12
    .line 13
    return-object p1

    .line 14
    :pswitch_1
    sget-object p1, LgP0/c$d;->e:LgP0/c$d;

    .line 15
    .line 16
    return-object p1

    .line 17
    :pswitch_2
    sget-object p1, LgP0/c$e;->e:LgP0/c$e;

    .line 18
    .line 19
    return-object p1

    .line 20
    :pswitch_3
    sget-object p1, LgP0/c$f;->e:LgP0/c$f;

    .line 21
    .line 22
    return-object p1

    .line 23
    :pswitch_4
    sget-object p1, LgP0/c$a;->e:LgP0/c$a;

    .line 24
    .line 25
    return-object p1

    .line 26
    :pswitch_5
    sget-object p1, LgP0/c$i;->e:LgP0/c$i;

    .line 27
    .line 28
    return-object p1

    .line 29
    :cond_0
    sget-object p1, LgP0/c$g;->e:LgP0/c$g;

    .line 30
    .line 31
    return-object p1

    .line 32
    nop

    .line 33
    :pswitch_data_0
    .packed-switch 0x11
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
