.class public final synthetic LSQ0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LSQ0/c;


# direct methods
.method public synthetic constructor <init>(LSQ0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LSQ0/b;->a:LSQ0/c;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LSQ0/b;->a:LSQ0/c;

    invoke-static {v0}, LSQ0/c;->a(LSQ0/c;)LRQ0/a;

    move-result-object v0

    return-object v0
.end method
