.class public final LSM0/s;
.super LeZ0/a;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0015\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LSM0/s;",
        "LeZ0/a;",
        "LpM0/p;",
        "binding",
        "<init>",
        "(LpM0/p;)V",
        "LRM0/f;",
        "item",
        "",
        "h",
        "(LRM0/f;)V",
        "e",
        "LpM0/p;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:LpM0/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LpM0/p;)V
    .locals 1
    .param p1    # LpM0/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LpM0/p;->b()Landroid/widget/FrameLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, v0}, LeZ0/a;-><init>(Landroid/view/View;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, LSM0/s;->e:LpM0/p;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final h(LRM0/f;)V
    .locals 1
    .param p1    # LRM0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LSM0/s;->e:LpM0/p;

    .line 2
    .line 3
    iget-object v0, v0, LpM0/p;->b:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {p1}, LRM0/f;->getTitle()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, LSM0/s;->e:LpM0/p;

    .line 13
    .line 14
    invoke-virtual {p1}, LpM0/p;->b()Landroid/widget/FrameLayout;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    const/4 v0, -0x2

    .line 23
    iput v0, p1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 24
    .line 25
    return-void
.end method
