.class public final Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u0000 72\u00020\u0001:\u00018B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0006\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0003J\u0017\u0010\t\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J!\u0010\u0010\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000c2\u0008\u0010\u000f\u001a\u0004\u0018\u00010\u000eH\u0016\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0012\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0003JG\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u001f\u0010 \u001a\u00020\u00042\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u000f\u0010\"\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0003R\"\u0010*\u001a\u00020#8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'\"\u0004\u0008(\u0010)R\u001b\u00100\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R\u001b\u00106\u001a\u0002018BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105\u00a8\u00069"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "P2",
        "I2",
        "",
        "clickable",
        "M2",
        "(Z)V",
        "u2",
        "Landroid/view/View;",
        "view",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "x2",
        "Lorg/xbet/core/data/LuckyWheelBonusType;",
        "bonusType",
        "",
        "bonusDescription",
        "",
        "winSum",
        "currencySymbol",
        "returnHalfBonus",
        "showPlayAgain",
        "betSum",
        "O2",
        "(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZZD)V",
        "show",
        "N2",
        "(ZLjava/lang/String;)V",
        "E2",
        "LyT0/c$b;",
        "i0",
        "LyT0/c$b;",
        "G2",
        "()LyT0/c$b;",
        "setTileMatchingGameEndGameViewModelFactory",
        "(LyT0/c$b;)V",
        "tileMatchingGameEndGameViewModelFactory",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
        "j0",
        "Lkotlin/j;",
        "H2",
        "()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;",
        "viewModel",
        "LxT0/b;",
        "k0",
        "LRc/c;",
        "F2",
        "()LxT0/b;",
        "binding",
        "l0",
        "a",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final l0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic m0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:LyT0/c$b;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/tile_matching/databinding/FragmentTileMatchingGameEndedBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->m0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->l0:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LrT0/c;->fragment_tile_matching_game_ended:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/a;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$binding$2;->INSTANCE:Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$binding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->k0:LRc/c;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->Q2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic C2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->M2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZZD)V
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p9}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->O2(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZZD)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final I2()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/tile_matching/presentation/game/d;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/tile_matching/presentation/game/d;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V

    .line 4
    .line 5
    .line 6
    const-string v1, "NOT_ENOUGH_FUNDS"

    .line 7
    .line 8
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->O3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final K2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->N3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final L2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->P3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method private final M2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/b;->i:Landroidx/appcompat/widget/AppCompatButton;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroid/view/View;->setClickable(Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v0, v0, LxT0/b;->e:Landroidx/appcompat/widget/AppCompatButton;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Landroid/view/View;->setClickable(Z)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method private final P2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->H3()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    new-instance v2, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-direct/range {v2 .. v7}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v11, 0x3

    .line 34
    const/4 v12, 0x0

    .line 35
    move-object v7, v8

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    move-object v10, v2

    .line 39
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-virtual {v2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;->I3()Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    new-instance v11, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;

    .line 51
    .line 52
    invoke-direct {v11, v0, v1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$2;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v15, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$$inlined$observeWithLifecycle$default$2;

    .line 64
    .line 65
    move-object v10, v5

    .line 66
    move-object v7, v15

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment$subscribeOnViewActions$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/16 v16, 0x3

    .line 71
    .line 72
    const/16 v17, 0x0

    .line 73
    .line 74
    const/4 v13, 0x0

    .line 75
    const/4 v14, 0x0

    .line 76
    move-object v12, v1

    .line 77
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    return-void
.end method

.method public static final Q2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->G2()LyT0/c$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->K2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->L2(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final E2()V
    .locals 4

    .line 1
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/g;->P(Landroid/content/Context;)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    int-to-double v0, v0

    .line 12
    const-wide v2, 0x3fe3851eb851eb85L    # 0.61

    .line 13
    .line 14
    .line 15
    .line 16
    .line 17
    mul-double v0, v0, v2

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    iget-object v2, v2, LxT0/b;->c:Landroid/widget/ImageView;

    .line 24
    .line 25
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    double-to-int v0, v0

    .line 30
    iput v0, v2, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 31
    .line 32
    return-void
.end method

.method public final F2()LxT0/b;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->k0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->m0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LxT0/b;

    .line 13
    .line 14
    return-object v0
.end method

.method public final G2()LyT0/c$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->i0:LyT0/c$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final H2()Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final N2(ZLjava/lang/String;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LxT0/b;->c:Landroid/widget/ImageView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LxT0/b;->d:Landroidx/appcompat/widget/AppCompatTextView;

    .line 24
    .line 25
    if-eqz p1, :cond_1

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v3, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iget-object v0, v0, LxT0/b;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 39
    .line 40
    if-eqz p1, :cond_2

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    if-eqz p1, :cond_3

    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iget-object p1, p1, LxT0/b;->d:Landroidx/appcompat/widget/AppCompatTextView;

    .line 53
    .line 54
    sget v0, Lpb/k;->bonus:I

    .line 55
    .line 56
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iget-object p1, p1, LxT0/b;->b:Landroidx/appcompat/widget/AppCompatTextView;

    .line 68
    .line 69
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 70
    .line 71
    .line 72
    :cond_3
    return-void
.end method

.method public final O2(Lorg/xbet/core/data/LuckyWheelBonusType;Ljava/lang/String;DLjava/lang/String;ZZD)V
    .locals 6

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x1

    .line 3
    const-wide/16 v2, 0x0

    .line 4
    .line 5
    const/4 v4, 0x0

    .line 6
    cmpl-double v5, p3, v2

    .line 7
    .line 8
    if-lez v5, :cond_0

    .line 9
    .line 10
    const/4 v2, 0x1

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v2, 0x0

    .line 13
    :goto_0
    invoke-virtual {p1}, Lorg/xbet/core/data/LuckyWheelBonusType;->isBonus()Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->N2(ZLjava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    if-eqz v2, :cond_1

    .line 25
    .line 26
    if-eqz p6, :cond_1

    .line 27
    .line 28
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 29
    .line 30
    sget p6, Lpb/k;->win_title:I

    .line 31
    .line 32
    invoke-virtual {p0, p6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p6

    .line 36
    invoke-virtual {p2, p6}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 37
    .line 38
    .line 39
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 40
    .line 41
    sget-object p6, Lub/b;->a:Lub/b;

    .line 42
    .line 43
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {v2}, LxT0/b;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    sget v3, Lpb/e;->end_game_win_title_color:I

    .line 56
    .line 57
    invoke-virtual {p6, v2, v3}, Lub/b;->d(Landroid/content/Context;I)I

    .line 58
    .line 59
    .line 60
    move-result p6

    .line 61
    invoke-virtual {p2, p6}, Landroid/widget/TextView;->setTextColor(I)V

    .line 62
    .line 63
    .line 64
    iget-object p2, p1, LxT0/b;->g:Landroidx/appcompat/widget/AppCompatTextView;

    .line 65
    .line 66
    sget p6, Lpb/k;->games_win_status_return_half_placeholder:I

    .line 67
    .line 68
    sget-object v2, Ll8/j;->a:Ll8/j;

    .line 69
    .line 70
    sget-object v3, Lcom/xbet/onexcore/utils/ValueType;->LIMIT:Lcom/xbet/onexcore/utils/ValueType;

    .line 71
    .line 72
    invoke-virtual {v2, p3, p4, p5, v3}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object p3

    .line 76
    new-array p4, v1, [Ljava/lang/Object;

    .line 77
    .line 78
    aput-object p3, p4, v4

    .line 79
    .line 80
    invoke-virtual {p0, p6, p4}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 81
    .line 82
    .line 83
    move-result-object p3

    .line 84
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 85
    .line 86
    .line 87
    goto :goto_1

    .line 88
    :cond_1
    if-eqz v2, :cond_2

    .line 89
    .line 90
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 91
    .line 92
    sget p6, Lpb/k;->win_title:I

    .line 93
    .line 94
    invoke-virtual {p0, p6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object p6

    .line 98
    invoke-virtual {p2, p6}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 99
    .line 100
    .line 101
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 102
    .line 103
    sget-object p6, Lub/b;->a:Lub/b;

    .line 104
    .line 105
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 106
    .line 107
    .line 108
    move-result-object v2

    .line 109
    invoke-virtual {v2}, LxT0/b;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 110
    .line 111
    .line 112
    move-result-object v2

    .line 113
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 114
    .line 115
    .line 116
    move-result-object v2

    .line 117
    sget v3, Lpb/e;->end_game_win_title_color:I

    .line 118
    .line 119
    invoke-virtual {p6, v2, v3}, Lub/b;->d(Landroid/content/Context;I)I

    .line 120
    .line 121
    .line 122
    move-result p6

    .line 123
    invoke-virtual {p2, p6}, Landroid/widget/TextView;->setTextColor(I)V

    .line 124
    .line 125
    .line 126
    iget-object p2, p1, LxT0/b;->g:Landroidx/appcompat/widget/AppCompatTextView;

    .line 127
    .line 128
    sget p6, Lpb/k;->games_win_status:I

    .line 129
    .line 130
    sget-object v2, Ll8/j;->a:Ll8/j;

    .line 131
    .line 132
    sget-object v3, Lcom/xbet/onexcore/utils/ValueType;->LIMIT:Lcom/xbet/onexcore/utils/ValueType;

    .line 133
    .line 134
    invoke-virtual {v2, p3, p4, v3}, Ll8/j;->d(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 135
    .line 136
    .line 137
    move-result-object p3

    .line 138
    const/4 p4, 0x3

    .line 139
    new-array p4, p4, [Ljava/lang/Object;

    .line 140
    .line 141
    const-string v2, ""

    .line 142
    .line 143
    aput-object v2, p4, v4

    .line 144
    .line 145
    aput-object p3, p4, v1

    .line 146
    .line 147
    aput-object p5, p4, v0

    .line 148
    .line 149
    invoke-virtual {p0, p6, p4}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object p3

    .line 153
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 154
    .line 155
    .line 156
    goto :goto_1

    .line 157
    :cond_2
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 158
    .line 159
    sget p3, Lpb/k;->game_bad_luck:I

    .line 160
    .line 161
    invoke-virtual {p0, p3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 162
    .line 163
    .line 164
    move-result-object p3

    .line 165
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 166
    .line 167
    .line 168
    iget-object p2, p1, LxT0/b;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 169
    .line 170
    sget-object p3, Lub/b;->a:Lub/b;

    .line 171
    .line 172
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 173
    .line 174
    .line 175
    move-result-object p4

    .line 176
    invoke-virtual {p4}, LxT0/b;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 177
    .line 178
    .line 179
    move-result-object p4

    .line 180
    invoke-virtual {p4}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 181
    .line 182
    .line 183
    move-result-object p4

    .line 184
    sget p6, Lpb/e;->text_color_primary_dark:I

    .line 185
    .line 186
    invoke-virtual {p3, p4, p6}, Lub/b;->d(Landroid/content/Context;I)I

    .line 187
    .line 188
    .line 189
    move-result p3

    .line 190
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setTextColor(I)V

    .line 191
    .line 192
    .line 193
    iget-object p2, p1, LxT0/b;->g:Landroidx/appcompat/widget/AppCompatTextView;

    .line 194
    .line 195
    sget p3, Lpb/k;->try_again_new_lottery:I

    .line 196
    .line 197
    invoke-virtual {p0, p3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 198
    .line 199
    .line 200
    move-result-object p3

    .line 201
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 202
    .line 203
    .line 204
    :goto_1
    iget-object p2, p1, LxT0/b;->i:Landroidx/appcompat/widget/AppCompatButton;

    .line 205
    .line 206
    sget p3, Lpb/k;->play_more:I

    .line 207
    .line 208
    sget-object p4, Ll8/j;->a:Ll8/j;

    .line 209
    .line 210
    sget-object p6, Lcom/xbet/onexcore/utils/ValueType;->LIMIT:Lcom/xbet/onexcore/utils/ValueType;

    .line 211
    .line 212
    invoke-virtual {p4, p8, p9, p6}, Ll8/j;->d(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 213
    .line 214
    .line 215
    move-result-object p4

    .line 216
    new-array p6, v0, [Ljava/lang/Object;

    .line 217
    .line 218
    aput-object p4, p6, v4

    .line 219
    .line 220
    aput-object p5, p6, v1

    .line 221
    .line 222
    invoke-virtual {p0, p3, p6}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 223
    .line 224
    .line 225
    move-result-object p3

    .line 226
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 227
    .line 228
    .line 229
    iget-object p1, p1, LxT0/b;->i:Landroidx/appcompat/widget/AppCompatButton;

    .line 230
    .line 231
    if-eqz p7, :cond_3

    .line 232
    .line 233
    goto :goto_2

    .line 234
    :cond_3
    const/16 v4, 0x8

    .line 235
    .line 236
    :goto_2
    invoke-virtual {p1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 237
    .line 238
    .line 239
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 2
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, LXW0/a;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->I2()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iget-object p1, p1, LxT0/b;->i:Landroidx/appcompat/widget/AppCompatButton;

    .line 12
    .line 13
    new-instance p2, Lorg/xbet/tile_matching/presentation/game/b;

    .line 14
    .line 15
    invoke-direct {p2, p0}, Lorg/xbet/tile_matching/presentation/game/b;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V

    .line 16
    .line 17
    .line 18
    const/4 v0, 0x0

    .line 19
    const/4 v1, 0x1

    .line 20
    invoke-static {p1, v0, p2, v1, v0}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->F2()LxT0/b;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    iget-object p1, p1, LxT0/b;->e:Landroidx/appcompat/widget/AppCompatButton;

    .line 28
    .line 29
    new-instance p2, Lorg/xbet/tile_matching/presentation/game/c;

    .line 30
    .line 31
    invoke-direct {p2, p0}, Lorg/xbet/tile_matching/presentation/game/c;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V

    .line 32
    .line 33
    .line 34
    invoke-static {p1, v0, p2, v1, v0}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 35
    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->P2()V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;->E2()V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public u2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/tile_matching/presentation/holder/TileMatchingFragment;->k4()LyT0/c;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    invoke-interface {v0, p0}, LyT0/c;->d(Lorg/xbet/tile_matching/presentation/game/TileMatchingEndGameFragment;)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public x2()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    sget v2, Lpb/c;->black:I

    .line 18
    .line 19
    const v3, 0x1010451

    .line 20
    .line 21
    .line 22
    const/4 v4, 0x1

    .line 23
    invoke-static {v0, v1, v2, v3, v4}, Lorg/xbet/ui_common/utils/I0;->c(Landroid/view/Window;Landroid/content/Context;IIZ)V

    .line 24
    .line 25
    .line 26
    :cond_0
    return-void
.end method
