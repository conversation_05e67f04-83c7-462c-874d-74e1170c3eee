.class final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.my_aggregator.presentation.fragments.MyAggregatorFragment$onObserveData$1"
    f = "MyAggregatorFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "isAuth",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 14
    .line 15
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->x3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J

    .line 16
    .line 17
    .line 18
    move-result-wide v0

    .line 19
    const-wide/16 v2, 0x0

    .line 20
    .line 21
    cmp-long v4, v0, v2

    .line 22
    .line 23
    if-eqz v4, :cond_1

    .line 24
    .line 25
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 26
    .line 27
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object v4

    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 32
    .line 33
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->x3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J

    .line 34
    .line 35
    .line 36
    move-result-wide v5

    .line 37
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 38
    .line 39
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->y3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J

    .line 40
    .line 41
    .line 42
    move-result-wide v7

    .line 43
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 44
    .line 45
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v9

    .line 53
    invoke-virtual/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->F6(JJLjava/lang/String;)V

    .line 54
    .line 55
    .line 56
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 57
    .line 58
    if-eqz p1, :cond_0

    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_0
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->x3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J

    .line 62
    .line 63
    .line 64
    move-result-wide v2

    .line 65
    :goto_0
    invoke-static {v0, v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->E3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;J)V

    .line 66
    .line 67
    .line 68
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 69
    .line 70
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->z3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)LS91/W;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iget-object v0, v0, LS91/W;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 75
    .line 76
    const/16 v1, 0x8

    .line 77
    .line 78
    const/4 v2, 0x0

    .line 79
    if-eqz p1, :cond_2

    .line 80
    .line 81
    const/4 v3, 0x0

    .line 82
    goto :goto_1

    .line 83
    :cond_2
    const/16 v3, 0x8

    .line 84
    .line 85
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 86
    .line 87
    .line 88
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 89
    .line 90
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->z3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)LS91/W;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    iget-object v0, v0, LS91/W;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 95
    .line 96
    if-nez p1, :cond_3

    .line 97
    .line 98
    const/4 v1, 0x0

    .line 99
    :cond_3
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 100
    .line 101
    .line 102
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object p1

    .line 105
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 106
    .line 107
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 108
    .line 109
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    throw p1
.end method
