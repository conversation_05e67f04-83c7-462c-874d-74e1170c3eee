.class final Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.domain.usecases.CheckFavoritesGameUseCase$invoke$2"
    f = "CheckFavoritesGameUseCase.kt"
    l = {
        0xf
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->b(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/lang/Boolean;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Z"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $endPoint:Ljava/lang/String;

.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field private synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$brandsApi:Z

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$endPoint:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$brandsApi:Z

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$endPoint:Ljava/lang/String;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;-><init>(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_1

    .line 10
    .line 11
    if-ne v1, v3, :cond_0

    .line 12
    .line 13
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 14
    .line 15
    .line 16
    goto :goto_0

    .line 17
    :catchall_0
    move-exception p1

    .line 18
    goto :goto_1

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast p1, Lkotlinx/coroutines/N;

    .line 33
    .line 34
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->this$0:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;

    .line 35
    .line 36
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 37
    .line 38
    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$brandsApi:Z

    .line 39
    .line 40
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->$endPoint:Ljava/lang/String;

    .line 41
    .line 42
    :try_start_1
    sget-object v6, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 43
    .line 44
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;->a(Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase;)Lu81/b;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput v3, p0, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/CheckFavoritesGameUseCase$invoke$2;->label:I

    .line 49
    .line 50
    invoke-interface {p1, v1, v4, v5, p0}, Lu81/b;->a(Lorg/xplatform/aggregator/api/model/Game;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    if-ne p1, v0, :cond_2

    .line 55
    .line 56
    return-object v0

    .line 57
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 58
    .line 59
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 60
    .line 61
    .line 62
    move-result p1

    .line 63
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 71
    goto :goto_2

    .line 72
    :goto_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 73
    .line 74
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    :goto_2
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    if-nez v0, :cond_3

    .line 87
    .line 88
    goto :goto_3

    .line 89
    :cond_3
    invoke-static {v2}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    :goto_3
    return-object p1
.end method
