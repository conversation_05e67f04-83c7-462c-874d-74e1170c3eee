.class public abstract LcZ0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LcZ0/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008#\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\'\u0018\u00002\u00020\u0001B!\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ!\u0010\u0012\u001a\u00020\u000c2\u0010\u0010\u0011\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00100\u000fH\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J!\u0010\u0016\u001a\u00020\u000c2\u0010\u0010\u0015\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00140\u000fH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0013J#\u0010\u0018\u001a\u00020\u000c2\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\u000f0\u000fH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0013J!\u0010\u001a\u001a\u00020\u000c2\u0010\u0010\u0019\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00100\u000fH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0013J!\u0010\u001c\u001a\u00020\u000c2\u0010\u0010\u001b\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00140\u000fH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0013J#\u0010\u001e\u001a\u00020\u000c2\u0012\u0010\u001d\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\u000f0\u000fH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0013J)\u0010!\u001a\u00020\u00062\u0010\u0010\u001f\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00140\u000f2\u0006\u0010 \u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008!\u0010\"J5\u0010%\u001a\u00020\u000c2\u0010\u0010#\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00100\u000f2\u0012\u0010$\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\u000f0\u000fH\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0017\u0010(\u001a\u00020\u00062\u0006\u0010\'\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008(\u0010)J\u0017\u0010,\u001a\u00020\u00062\u0006\u0010+\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008,\u0010-J+\u00101\u001a\u00020\u00062\u0008\u0010.\u001a\u0004\u0018\u00010\u00062\u0008\u0010/\u001a\u0004\u0018\u00010\u00062\u0006\u00100\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00081\u00102J\u001f\u00105\u001a\u00020\u000c2\u0006\u00103\u001a\u00020\u00062\u0006\u00104\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00085\u00106J\u0017\u00108\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010:\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008:\u00109J\u001f\u0010=\u001a\u00020\u00062\u0006\u0010;\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008=\u0010>JM\u0010@\u001a\u00020\u000c2\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\u000f0\u000f2\u0010\u0010\u0015\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00140\u000f2\u0010\u0010\u0011\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00100\u000f2\u0006\u0010?\u001a\u00020\n\u00a2\u0006\u0004\u0008@\u0010AJ\u0015\u0010C\u001a\u00020\u000c2\u0006\u0010B\u001a\u00020\u0006\u00a2\u0006\u0004\u0008C\u0010DJ\u0015\u0010F\u001a\u00020\u000c2\u0006\u0010E\u001a\u00020\u0006\u00a2\u0006\u0004\u0008F\u0010DJ\u001d\u0010G\u001a\n\u0018\u00010\nj\u0004\u0018\u0001`\u00102\u0006\u00107\u001a\u00020\u0006\u00a2\u0006\u0004\u0008G\u0010HJ\u001d\u0010I\u001a\n\u0018\u00010\nj\u0004\u0018\u0001`\u00142\u0006\u00107\u001a\u00020\u0006\u00a2\u0006\u0004\u0008I\u0010HJ\u001f\u0010J\u001a\u0004\u0018\u00010\n2\u0006\u0010;\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u0006\u00a2\u0006\u0004\u0008J\u0010KR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\"\u0010T\u001a\u00020N8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008=\u0010O\u001a\u0004\u0008P\u0010Q\"\u0004\u0008R\u0010SR\"\u0010[\u001a\u00020U8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008:\u0010V\u001a\u0004\u0008W\u0010X\"\u0004\u0008Y\u0010ZR\"\u0010c\u001a\u00020\\8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008]\u0010^\u001a\u0004\u0008_\u0010`\"\u0004\u0008a\u0010bR\u0016\u0010e\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008d\u0010MR\u0016\u0010g\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008f\u0010MR \u0010j\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00100\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR \u0010l\u001a\u000c\u0012\u0008\u0012\u00060\nj\u0002`\u00140\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008k\u0010iR\"\u0010n\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\u000f0\u000f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008m\u0010iR\u001c\u0010r\u001a\u0008\u0012\u0004\u0012\u00020p0o8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008q\u0010iR\u0018\u0010 \u001a\u0004\u0018\u00010s8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00088\u0010tR\u0017\u0010z\u001a\u00020u8\u0006\u00a2\u0006\u000c\n\u0004\u0008v\u0010w\u001a\u0004\u0008x\u0010yR\u0014\u0010{\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010M\u00a8\u0006|"
    }
    d2 = {
        "LcZ0/a;",
        "LcZ0/c;",
        "LbZ0/a;",
        "tableView",
        "",
        "hasZebraBackground",
        "",
        "textSize",
        "<init>",
        "(LbZ0/a;ZI)V",
        "LhZ0/a;",
        "cornerCell",
        "",
        "H",
        "(LhZ0/a;)V",
        "",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/model/ColumnHeader;",
        "columnHeaderItems",
        "E",
        "(Ljava/util/List;)V",
        "Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/model/RowHeader;",
        "rowHeaderItems",
        "F",
        "cellItems",
        "C",
        "newColumnHeaderItems",
        "n",
        "newRowHeaderItems",
        "o",
        "newCellItems",
        "m",
        "rowHeaderList",
        "cornerView",
        "u",
        "(Ljava/util/List;LhZ0/a;)I",
        "columnHeader",
        "rowList",
        "A",
        "(Ljava/util/List;Ljava/util/List;)V",
        "cell",
        "y",
        "(LhZ0/a;)I",
        "LhZ0/b;",
        "cellContent",
        "z",
        "(LhZ0/b;)I",
        "minWidth",
        "maxWidth",
        "contentWidth",
        "x",
        "(Ljava/lang/Integer;Ljava/lang/Integer;I)I",
        "unUsedSpace",
        "columnsCount",
        "p",
        "(II)V",
        "position",
        "k",
        "(I)I",
        "c",
        "columnPosition",
        "rowPosition",
        "b",
        "(II)I",
        "firstColumnTitle",
        "B",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;LhZ0/a;)V",
        "rowHeaderWidth",
        "G",
        "(I)V",
        "columnHeaderHeight",
        "D",
        "s",
        "(I)LhZ0/a;",
        "v",
        "q",
        "(II)LhZ0/a;",
        "a",
        "I",
        "LdZ0/d;",
        "LdZ0/d;",
        "t",
        "()LdZ0/d;",
        "setColumnHeaderRecyclerViewAdapter",
        "(LdZ0/d;)V",
        "columnHeaderRecyclerViewAdapter",
        "LdZ0/e;",
        "LdZ0/e;",
        "w",
        "()LdZ0/e;",
        "setRowHeaderRecyclerViewAdapter",
        "(LdZ0/e;)V",
        "rowHeaderRecyclerViewAdapter",
        "LdZ0/b;",
        "d",
        "LdZ0/b;",
        "r",
        "()LdZ0/b;",
        "setCellRecyclerViewAdapter",
        "(LdZ0/b;)V",
        "cellRecyclerViewAdapter",
        "e",
        "rowHeaderWidth1",
        "f",
        "columnHeaderHeight1",
        "g",
        "Ljava/util/List;",
        "columnHeaderItemList",
        "h",
        "rowHeaderItemList",
        "i",
        "cellItemList",
        "",
        "LcZ0/b;",
        "j",
        "dataSetChangedListeners",
        "Landroid/view/View;",
        "Landroid/view/View;",
        "Landroid/graphics/Paint;",
        "l",
        "Landroid/graphics/Paint;",
        "getPaint",
        "()Landroid/graphics/Paint;",
        "paint",
        "padding",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public b:LdZ0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:LdZ0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:LdZ0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:I

.field public f:I

.field public g:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public h:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LcZ0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Landroid/view/View;

.field public final l:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(LbZ0/a;ZI)V
    .locals 2
    .param p1    # LbZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p3, p0, LcZ0/a;->a:I

    .line 3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, LcZ0/a;->g:Ljava/util/List;

    .line 4
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, LcZ0/a;->h:Ljava/util/List;

    .line 5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object v0

    iput-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 6
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, LcZ0/a;->j:Ljava/util/List;

    .line 7
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 8
    invoke-interface {p1}, LbZ0/a;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1, p3}, Landroid/content/res/Resources;->getDimension(I)F

    move-result p3

    invoke-virtual {v0, p3}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 9
    invoke-interface {p1}, LbZ0/a;->getContext()Landroid/content/Context;

    move-result-object p3

    sget v1, Lpb/h;->roboto_regular:I

    invoke-static {p3, v1}, LH0/h;->h(Landroid/content/Context;I)Landroid/graphics/Typeface;

    move-result-object p3

    invoke-virtual {v0, p3}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 10
    iput-object v0, p0, LcZ0/a;->l:Landroid/graphics/Paint;

    .line 11
    invoke-interface {p1}, LbZ0/a;->getContext()Landroid/content/Context;

    move-result-object p3

    invoke-virtual {p3}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, Lpb/f;->size_10:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, LcZ0/a;->m:I

    .line 12
    invoke-interface {p1}, LbZ0/a;->getContext()Landroid/content/Context;

    move-result-object p3

    .line 13
    new-instance v0, LdZ0/d;

    .line 14
    iget-object v1, p0, LcZ0/a;->g:Ljava/util/List;

    .line 15
    invoke-direct {v0, p3, v1, p0}, LdZ0/d;-><init>(Landroid/content/Context;Ljava/util/List;LcZ0/c;)V

    iput-object v0, p0, LcZ0/a;->b:LdZ0/d;

    .line 16
    new-instance v0, LdZ0/e;

    .line 17
    iget-object v1, p0, LcZ0/a;->h:Ljava/util/List;

    .line 18
    invoke-direct {v0, p3, v1, p0, p2}, LdZ0/e;-><init>(Landroid/content/Context;Ljava/util/List;LcZ0/c;Z)V

    iput-object v0, p0, LcZ0/a;->c:LdZ0/e;

    .line 19
    iget-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 20
    new-instance v1, LdZ0/b;

    invoke-direct {v1, v0, p3, p1, p2}, LdZ0/b;-><init>(Ljava/util/List;Landroid/content/Context;LbZ0/a;Z)V

    iput-object v1, p0, LcZ0/a;->d:LdZ0/b;

    return-void
.end method

.method public synthetic constructor <init>(LbZ0/a;ZIILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    .line 21
    sget p3, Lpb/f;->text_12:I

    .line 22
    :cond_0
    invoke-direct {p0, p1, p2, p3}, LcZ0/a;-><init>(LbZ0/a;ZI)V

    return-void
.end method


# virtual methods
.method public final A(Ljava/util/List;Ljava/util/List;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_7

    .line 6
    .line 7
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    goto/16 :goto_3

    .line 14
    .line 15
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 16
    .line 17
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 18
    .line 19
    .line 20
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    const/4 v1, 0x0

    .line 25
    const/4 v2, 0x0

    .line 26
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 27
    .line 28
    .line 29
    move-result v3

    .line 30
    if-eqz v3, :cond_6

    .line 31
    .line 32
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    add-int/lit8 v4, v2, 0x1

    .line 37
    .line 38
    if-gez v2, :cond_1

    .line 39
    .line 40
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 41
    .line 42
    .line 43
    :cond_1
    check-cast v3, LhZ0/a;

    .line 44
    .line 45
    invoke-virtual {p0, v3}, LcZ0/a;->y(LhZ0/a;)I

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    new-instance v5, Ljava/util/ArrayList;

    .line 50
    .line 51
    const/16 v6, 0xa

    .line 52
    .line 53
    invoke-static {p2, v6}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 54
    .line 55
    .line 56
    move-result v6

    .line 57
    invoke-direct {v5, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 58
    .line 59
    .line 60
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 61
    .line 62
    .line 63
    move-result-object v6

    .line 64
    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 65
    .line 66
    .line 67
    move-result v7

    .line 68
    if-eqz v7, :cond_2

    .line 69
    .line 70
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object v7

    .line 74
    check-cast v7, Ljava/util/List;

    .line 75
    .line 76
    invoke-interface {v7, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v7

    .line 80
    check-cast v7, LhZ0/a;

    .line 81
    .line 82
    invoke-interface {v5, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    goto :goto_1

    .line 86
    :cond_2
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 87
    .line 88
    .line 89
    move-result-object v5

    .line 90
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 91
    .line 92
    .line 93
    move-result v6

    .line 94
    if-eqz v6, :cond_5

    .line 95
    .line 96
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v6

    .line 100
    check-cast v6, LhZ0/a;

    .line 101
    .line 102
    invoke-virtual {p0, v6}, LcZ0/a;->y(LhZ0/a;)I

    .line 103
    .line 104
    .line 105
    move-result v6

    .line 106
    :cond_3
    :goto_2
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 107
    .line 108
    .line 109
    move-result v7

    .line 110
    if-eqz v7, :cond_4

    .line 111
    .line 112
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v7

    .line 116
    check-cast v7, LhZ0/a;

    .line 117
    .line 118
    invoke-virtual {p0, v7}, LcZ0/a;->y(LhZ0/a;)I

    .line 119
    .line 120
    .line 121
    move-result v7

    .line 122
    if-ge v6, v7, :cond_3

    .line 123
    .line 124
    move v6, v7

    .line 125
    goto :goto_2

    .line 126
    :cond_4
    invoke-static {v3, v6}, Ljava/lang/Math;->max(II)I

    .line 127
    .line 128
    .line 129
    move-result v3

    .line 130
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 131
    .line 132
    .line 133
    move-result-object v5

    .line 134
    invoke-interface {v5}, LbZ0/a;->getColumnHeaderLayoutManager()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;

    .line 135
    .line 136
    .line 137
    move-result-object v5

    .line 138
    invoke-virtual {v5, v2, v3}, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;->m(II)V

    .line 139
    .line 140
    .line 141
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 142
    .line 143
    .line 144
    move-result-object v2

    .line 145
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 146
    .line 147
    .line 148
    move v2, v4

    .line 149
    goto :goto_0

    .line 150
    :cond_5
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 151
    .line 152
    invoke-direct {p1}, Ljava/util/NoSuchElementException;-><init>()V

    .line 153
    .line 154
    .line 155
    throw p1

    .line 156
    :cond_6
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 157
    .line 158
    .line 159
    move-result-object p1

    .line 160
    invoke-interface {p1}, LbZ0/a;->getContext()Landroid/content/Context;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 165
    .line 166
    .line 167
    move-result-object p1

    .line 168
    invoke-virtual {p1}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    iget p1, p1, Landroid/util/DisplayMetrics;->widthPixels:I

    .line 173
    .line 174
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 175
    .line 176
    .line 177
    move-result-object p2

    .line 178
    invoke-interface {p2}, LbZ0/a;->getRowHeaderWidth()I

    .line 179
    .line 180
    .line 181
    move-result p2

    .line 182
    sub-int/2addr p1, p2

    .line 183
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->o1(Ljava/lang/Iterable;)I

    .line 184
    .line 185
    .line 186
    move-result p2

    .line 187
    if-ge p2, p1, :cond_7

    .line 188
    .line 189
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 190
    .line 191
    .line 192
    move-result-object v2

    .line 193
    invoke-interface {v2, v1, v1}, LbZ0/a;->b(ZZ)V

    .line 194
    .line 195
    .line 196
    sub-int/2addr p1, p2

    .line 197
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 198
    .line 199
    .line 200
    move-result p2

    .line 201
    invoke-virtual {p0, p1, p2}, LcZ0/a;->p(II)V

    .line 202
    .line 203
    .line 204
    :cond_7
    :goto_3
    return-void
.end method

.method public final B(Ljava/util/List;Ljava/util/List;Ljava/util/List;LhZ0/a;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LhZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;>;",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;",
            "LhZ0/a;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, p2, p4}, LcZ0/a;->u(Ljava/util/List;LhZ0/a;)I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-interface {v0, v1}, LbZ0/a;->setRowHeaderWidth(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p4}, LcZ0/a;->H(LhZ0/a;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0, p3, p1}, LcZ0/a;->A(Ljava/util/List;Ljava/util/List;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0, p3}, LcZ0/a;->E(Ljava/util/List;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0, p2}, LcZ0/a;->F(Ljava/util/List;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0, p1}, LcZ0/a;->C(Ljava/util/List;)V

    .line 25
    .line 26
    .line 27
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-interface {p1}, LbZ0/a;->setupHorizontalEdgeFading()V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final C(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LcZ0/a;->i:Ljava/util/List;

    .line 2
    .line 3
    iget-object v0, p0, LcZ0/a;->d:LdZ0/b;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, LdZ0/a;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0, p1}, LcZ0/a;->m(Ljava/util/List;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final D(I)V
    .locals 0

    .line 1
    iput p1, p0, LcZ0/a;->f:I

    .line 2
    .line 3
    return-void
.end method

.method public final E(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LcZ0/a;->g:Ljava/util/List;

    .line 2
    .line 3
    iget-object v0, p0, LcZ0/a;->b:LdZ0/d;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, LdZ0/a;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0, p1}, LcZ0/a;->n(Ljava/util/List;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final F(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LcZ0/a;->h:Ljava/util/List;

    .line 2
    .line 3
    iget-object v0, p0, LcZ0/a;->c:LdZ0/e;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, LdZ0/a;->setItems(Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, LcZ0/a;->h:Ljava/util/List;

    .line 9
    .line 10
    invoke-virtual {p0, p1}, LcZ0/a;->o(Ljava/util/List;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final G(I)V
    .locals 2

    .line 1
    iput p1, p0, LcZ0/a;->e:I

    .line 2
    .line 3
    iget-object v0, p0, LcZ0/a;->k:Landroid/view/View;

    .line 4
    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    iput p1, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    .line 20
    .line 21
    const-string v0, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    return-void
.end method

.method public final H(LhZ0/a;)V
    .locals 3

    .line 1
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Landroid/view/ViewGroup;

    .line 6
    .line 7
    invoke-interface {p0, v0, p1}, LcZ0/c;->l(Landroid/view/ViewGroup;LhZ0/a;)Landroid/view/View;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iput-object p1, p0, LcZ0/a;->k:Landroid/view/View;

    .line 12
    .line 13
    const/4 v0, 0x0

    .line 14
    if-eqz p1, :cond_0

    .line 15
    .line 16
    invoke-virtual {p1, v0, v0}, Landroid/view/View;->measure(II)V

    .line 17
    .line 18
    .line 19
    :cond_0
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iget-object v1, p0, LcZ0/a;->k:Landroid/view/View;

    .line 24
    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    goto :goto_0

    .line 32
    :cond_1
    const/4 v1, 0x0

    .line 33
    :goto_0
    invoke-interface {p1, v1}, LbZ0/a;->setRowHeaderWidth(I)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p0, LcZ0/a;->k:Landroid/view/View;

    .line 37
    .line 38
    if-eqz p1, :cond_3

    .line 39
    .line 40
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    if-eqz v1, :cond_2

    .line 45
    .line 46
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-interface {v2}, LbZ0/a;->getRowHeaderWidth()I

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    iput v2, v1, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 55
    .line 56
    invoke-virtual {p1, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 57
    .line 58
    .line 59
    goto :goto_1

    .line 60
    :cond_2
    new-instance p1, Ljava/lang/NullPointerException;

    .line 61
    .line 62
    const-string v0, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 63
    .line 64
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    throw p1

    .line 68
    :cond_3
    :goto_1
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    instance-of v1, p1, Landroid/view/ViewGroup;

    .line 73
    .line 74
    const/4 v2, 0x0

    .line 75
    if-eqz v1, :cond_4

    .line 76
    .line 77
    check-cast p1, Landroid/view/ViewGroup;

    .line 78
    .line 79
    goto :goto_2

    .line 80
    :cond_4
    move-object p1, v2

    .line 81
    :goto_2
    if-eqz p1, :cond_6

    .line 82
    .line 83
    iget-object v1, p0, LcZ0/a;->k:Landroid/view/View;

    .line 84
    .line 85
    if-eqz v1, :cond_5

    .line 86
    .line 87
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 88
    .line 89
    .line 90
    move-result-object v2

    .line 91
    :cond_5
    invoke-virtual {p1, v1, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 92
    .line 93
    .line 94
    :cond_6
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    iget-object v1, p0, LcZ0/a;->k:Landroid/view/View;

    .line 99
    .line 100
    if-eqz v1, :cond_7

    .line 101
    .line 102
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    if-eqz v1, :cond_7

    .line 107
    .line 108
    iget v0, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 109
    .line 110
    :cond_7
    invoke-interface {p1, v0}, LbZ0/a;->setColumnHeaderHeight(I)V

    .line 111
    .line 112
    .line 113
    return-void
.end method

.method public b(II)I
    .locals 0

    .line 1
    const/4 p1, 0x0

    .line 2
    return p1
.end method

.method public c(I)I
    .locals 0

    .line 1
    const/4 p1, 0x0

    .line 2
    return p1
.end method

.method public k(I)I
    .locals 0

    .line 1
    const/4 p1, 0x0

    .line 2
    return p1
.end method

.method public final m(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->j:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, LcZ0/b;

    .line 18
    .line 19
    invoke-interface {v1, p1}, LcZ0/b;->a(Ljava/util/List;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final n(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->j:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, LcZ0/b;

    .line 18
    .line 19
    invoke-interface {v1, p1}, LcZ0/b;->b(Ljava/util/List;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final o(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->j:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, LcZ0/b;

    .line 18
    .line 19
    invoke-interface {v1, p1}, LcZ0/b;->c(Ljava/util/List;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final p(II)V
    .locals 3

    .line 1
    div-int/2addr p1, p2

    .line 2
    const/4 v0, 0x0

    .line 3
    :goto_0
    if-ge v0, p2, :cond_0

    .line 4
    .line 5
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-interface {v1}, LbZ0/a;->getColumnHeaderLayoutManager()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1, v0}, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;->l(I)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-interface {p0}, LcZ0/c;->d()LbZ0/a;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    invoke-interface {v2}, LbZ0/a;->getColumnHeaderLayoutManager()Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    add-int/2addr v1, p1

    .line 26
    invoke-virtual {v2, v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/scrollable_table/table_view/layout_manager/ColumnHeaderLayoutManager;->m(II)V

    .line 27
    .line 28
    .line 29
    add-int/lit8 v0, v0, 0x1

    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_0
    return-void
.end method

.method public final q(II)LhZ0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    if-ltz p1, :cond_1

    .line 10
    .line 11
    iget-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-ge p2, v0, :cond_1

    .line 18
    .line 19
    if-ltz p2, :cond_1

    .line 20
    .line 21
    iget-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 22
    .line 23
    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    check-cast v0, Ljava/util/List;

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-lt p1, v0, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    iget-object v0, p0, LcZ0/a;->i:Ljava/util/List;

    .line 37
    .line 38
    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p2

    .line 42
    check-cast p2, Ljava/util/List;

    .line 43
    .line 44
    invoke-interface {p2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    check-cast p1, LhZ0/a;

    .line 49
    .line 50
    return-object p1

    .line 51
    :cond_1
    :goto_0
    const/4 p1, 0x0

    .line 52
    return-object p1
.end method

.method public final r()LdZ0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->d:LdZ0/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s(I)LhZ0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LcZ0/a;->g:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    if-ltz p1, :cond_1

    .line 10
    .line 11
    iget-object v0, p0, LcZ0/a;->g:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-lt p1, v0, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    iget-object v0, p0, LcZ0/a;->g:Ljava/util/List;

    .line 21
    .line 22
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LhZ0/a;

    .line 27
    .line 28
    return-object p1

    .line 29
    :cond_1
    :goto_0
    const/4 p1, 0x0

    .line 30
    return-object p1
.end method

.method public final t()LdZ0/d;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->b:LdZ0/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u(Ljava/util/List;LhZ0/a;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LhZ0/a;",
            ">;",
            "LhZ0/a;",
            ")I"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p2}, LcZ0/a;->y(LhZ0/a;)I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    return p2

    .line 12
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, LhZ0/a;

    .line 27
    .line 28
    invoke-virtual {p0, v0}, LcZ0/a;->y(LhZ0/a;)I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    check-cast v1, LhZ0/a;

    .line 43
    .line 44
    invoke-virtual {p0, v1}, LcZ0/a;->y(LhZ0/a;)I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    if-ge v0, v1, :cond_1

    .line 49
    .line 50
    move v0, v1

    .line 51
    goto :goto_0

    .line 52
    :cond_2
    invoke-static {v0, p2}, Ljava/lang/Math;->max(II)I

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    return p1

    .line 57
    :cond_3
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 58
    .line 59
    invoke-direct {p1}, Ljava/util/NoSuchElementException;-><init>()V

    .line 60
    .line 61
    .line 62
    throw p1
.end method

.method public final v(I)LhZ0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LcZ0/a;->h:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    if-ltz p1, :cond_1

    .line 10
    .line 11
    iget-object v0, p0, LcZ0/a;->h:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-lt p1, v0, :cond_0

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    iget-object v0, p0, LcZ0/a;->h:Ljava/util/List;

    .line 21
    .line 22
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LhZ0/a;

    .line 27
    .line 28
    return-object p1

    .line 29
    :cond_1
    :goto_0
    const/4 p1, 0x0

    .line 30
    return-object p1
.end method

.method public final w()LdZ0/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LcZ0/a;->c:LdZ0/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x(Ljava/lang/Integer;Ljava/lang/Integer;I)I
    .locals 1

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-lt p3, v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    return p1

    .line 14
    :cond_0
    if-eqz p1, :cond_1

    .line 15
    .line 16
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    if-gt p3, p2, :cond_1

    .line 21
    .line 22
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    return p1

    .line 27
    :cond_1
    return p3
.end method

.method public final y(LhZ0/a;)I
    .locals 3

    .line 1
    instance-of v0, p1, LhZ0/a$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LhZ0/a$b;

    .line 6
    .line 7
    invoke-virtual {p1}, LhZ0/a$b;->a()LhZ0/b$c;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0}, LcZ0/a;->z(LhZ0/b;)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-virtual {p1}, LhZ0/a$b;->b()Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {p1}, LhZ0/a$b;->c()Ljava/lang/Integer;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-virtual {p0, p1, v1, v0}, LcZ0/a;->x(Ljava/lang/Integer;Ljava/lang/Integer;I)I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1

    .line 28
    :cond_0
    instance-of v0, p1, LhZ0/a$a;

    .line 29
    .line 30
    const/4 v1, 0x0

    .line 31
    if-eqz v0, :cond_2

    .line 32
    .line 33
    check-cast p1, LhZ0/a$a;

    .line 34
    .line 35
    invoke-virtual {p1}, LhZ0/a$a;->a()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    if-eqz v2, :cond_1

    .line 48
    .line 49
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    check-cast v2, LhZ0/b;

    .line 54
    .line 55
    invoke-virtual {p0, v2}, LcZ0/a;->z(LhZ0/b;)I

    .line 56
    .line 57
    .line 58
    move-result v2

    .line 59
    add-int/2addr v1, v2

    .line 60
    goto :goto_0

    .line 61
    :cond_1
    invoke-virtual {p1}, LhZ0/a$a;->b()Ljava/lang/Integer;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-virtual {p1}, LhZ0/a$a;->c()Ljava/lang/Integer;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    invoke-virtual {p0, p1, v0, v1}, LcZ0/a;->x(Ljava/lang/Integer;Ljava/lang/Integer;I)I

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    return p1

    .line 74
    :cond_2
    return v1
.end method

.method public final z(LhZ0/b;)I
    .locals 1

    .line 1
    instance-of v0, p1, LhZ0/b$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LhZ0/b$a;

    .line 6
    .line 7
    invoke-virtual {p1}, LhZ0/b$a;->a()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    iget v0, p0, LcZ0/a;->m:I

    .line 12
    .line 13
    mul-int/lit8 v0, v0, 0x2

    .line 14
    .line 15
    add-int/2addr p1, v0

    .line 16
    return p1

    .line 17
    :cond_0
    instance-of v0, p1, LhZ0/b$b;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    check-cast p1, LhZ0/b$b;

    .line 22
    .line 23
    invoke-virtual {p1}, LhZ0/b$b;->a()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1

    .line 28
    :cond_1
    instance-of v0, p1, LhZ0/b$c;

    .line 29
    .line 30
    if-eqz v0, :cond_2

    .line 31
    .line 32
    iget-object v0, p0, LcZ0/a;->l:Landroid/graphics/Paint;

    .line 33
    .line 34
    check-cast p1, LhZ0/b$c;

    .line 35
    .line 36
    invoke-virtual {p1}, LhZ0/b$c;->a()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 41
    .line 42
    .line 43
    move-result p1

    .line 44
    iget v0, p0, LcZ0/a;->m:I

    .line 45
    .line 46
    mul-int/lit8 v0, v0, 0x2

    .line 47
    .line 48
    int-to-float v0, v0

    .line 49
    add-float/2addr p1, v0

    .line 50
    float-to-int p1, p1

    .line 51
    return p1

    .line 52
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 53
    .line 54
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 55
    .line 56
    .line 57
    throw p1
.end method
