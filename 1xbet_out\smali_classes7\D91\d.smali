.class public final LD91/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0015\u0010\u0002\u001a\u00020\u0001*\u0004\u0018\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "",
        "Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;",
        "a",
        "(Ljava/lang/Integer;)Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/lang/Integer;)Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-nez p0, :cond_0

    .line 2
    .line 3
    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 5
    .line 6
    .line 7
    move-result p0

    .line 8
    const/4 v0, 0x1

    .line 9
    if-ne p0, v0, :cond_1

    .line 10
    .line 11
    sget-object p0, Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;->PROVIDERS:Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 12
    .line 13
    return-object p0

    .line 14
    :cond_1
    :goto_0
    sget-object p0, Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;->FILTERS:Lorg/xplatform/aggregator/impl/category/domain/models/FilterType;

    .line 15
    .line 16
    return-object p0
.end method
