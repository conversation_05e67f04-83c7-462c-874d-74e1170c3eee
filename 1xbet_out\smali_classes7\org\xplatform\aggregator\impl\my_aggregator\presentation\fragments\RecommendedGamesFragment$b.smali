.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;
.super Landroidx/recyclerview/widget/RecyclerView$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->I3()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0019\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\'\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u001f\u0010\n\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001f\u0010\u000c\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\u000b\u00a8\u0006\r"
    }
    d2 = {
        "org/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "",
        "fromPosition",
        "toPosition",
        "itemCount",
        "",
        "onItemRangeMoved",
        "(III)V",
        "positionStart",
        "onItemRangeRemoved",
        "(II)V",
        "onItemRangeInserted",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onItemRangeInserted(II)V
    .locals 0

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    .line 4
    .line 5
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->s3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public onItemRangeMoved(III)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->s3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeRemoved(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment$b;->a:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;->s3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/RecommendedGamesFragment;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
