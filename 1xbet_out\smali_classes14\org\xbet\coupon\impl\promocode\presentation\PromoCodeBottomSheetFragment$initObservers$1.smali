.class final Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.promocode.presentation.PromoCodeBottomSheetFragment$initObservers$1"
    f = "PromoCodeBottomSheetFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->Y2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;

    iget-object v1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->invoke(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v1, 0x0

    .line 22
    const/16 v2, 0x8

    .line 23
    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 27
    .line 28
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LC7/b;->b:Landroidx/constraintlayout/widget/Group;

    .line 33
    .line 34
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 38
    .line 39
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, LC7/b;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 44
    .line 45
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 49
    .line 50
    sget v0, Lpb/k;->select_promo_code_title:I

    .line 51
    .line 52
    invoke-virtual {p1, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->M2(Ljava/lang/String;)V

    .line 57
    .line 58
    .line 59
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 60
    .line 61
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iget-object p1, p1, LC7/b;->d:Lorg/xbet/uikit/components/loader/Loader;

    .line 66
    .line 67
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 68
    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_0
    sget-object v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$b;

    .line 72
    .line 73
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    if-eqz v0, :cond_1

    .line 78
    .line 79
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 80
    .line 81
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iget-object p1, p1, LC7/b;->b:Landroidx/constraintlayout/widget/Group;

    .line 86
    .line 87
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 88
    .line 89
    .line 90
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 91
    .line 92
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    iget-object p1, p1, LC7/b;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 97
    .line 98
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 99
    .line 100
    .line 101
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 102
    .line 103
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iget-object p1, p1, LC7/b;->d:Lorg/xbet/uikit/components/loader/Loader;

    .line 108
    .line 109
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 110
    .line 111
    .line 112
    goto :goto_0

    .line 113
    :cond_1
    instance-of v0, p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;

    .line 114
    .line 115
    if-eqz v0, :cond_2

    .line 116
    .line 117
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 118
    .line 119
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 120
    .line 121
    .line 122
    move-result-object v0

    .line 123
    iget-object v0, v0, LC7/b;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 124
    .line 125
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 126
    .line 127
    .line 128
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 129
    .line 130
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    iget-object v0, v0, LC7/b;->b:Landroidx/constraintlayout/widget/Group;

    .line 135
    .line 136
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 137
    .line 138
    .line 139
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 140
    .line 141
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->T2()LC7/b;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    iget-object v0, v0, LC7/b;->d:Lorg/xbet/uikit/components/loader/Loader;

    .line 146
    .line 147
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 148
    .line 149
    .line 150
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 151
    .line 152
    sget v1, Lpb/k;->select_promo_code_title:I

    .line 153
    .line 154
    invoke-virtual {v0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->M2(Ljava/lang/String;)V

    .line 159
    .line 160
    .line 161
    iget-object v0, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 162
    .line 163
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->P2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;

    .line 164
    .line 165
    .line 166
    move-result-object v0

    .line 167
    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;

    .line 168
    .line 169
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;->a()Ljava/util/List;

    .line 170
    .line 171
    .line 172
    move-result-object p1

    .line 173
    invoke-virtual {v0, p1}, LA4/a;->n(Ljava/lang/Object;)V

    .line 174
    .line 175
    .line 176
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment$initObservers$1;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;

    .line 177
    .line 178
    invoke-static {p1}, Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;->P2(Lorg/xbet/coupon/impl/promocode/presentation/PromoCodeBottomSheetFragment;)LOx/a;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->notifyDataSetChanged()V

    .line 183
    .line 184
    .line 185
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 186
    .line 187
    return-object p1

    .line 188
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 189
    .line 190
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 191
    .line 192
    .line 193
    throw p1

    .line 194
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 195
    .line 196
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 197
    .line 198
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 199
    .line 200
    .line 201
    throw p1
.end method
