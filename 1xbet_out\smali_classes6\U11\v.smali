.class public final synthetic LU11/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LU11/v;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LU11/v;->a:Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;->u(Lorg/xbet/uikit_aggregator/aggregatorBonuses/PictureSStyleBonus;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
