.class public final Lg3/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0017\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "Lee/f;",
        "source",
        "Lg3/b;",
        "a",
        "(Lee/f;)Lg3/b;",
        "coil-svg_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lee/f;)Lg3/b;
    .locals 1
    .param p0    # Lee/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lg3/a;

    .line 2
    .line 3
    invoke-interface {p0}, Lee/f;->J2()Ljava/io/InputStream;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-static {p0}, Lcom/caverock/androidsvg/SVG;->m(Ljava/io/InputStream;)Lcom/caverock/androidsvg/SVG;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, p0}, Lg3/a;-><init>(Lcom/caverock/androidsvg/SVG;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method
