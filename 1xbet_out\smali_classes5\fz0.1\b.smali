.class public final synthetic Lfz0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lfz0/c;

.field public final synthetic b:Ldz0/a;


# direct methods
.method public synthetic constructor <init>(Lfz0/c;Ldz0/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lfz0/b;->a:Lfz0/c;

    iput-object p2, p0, Lfz0/b;->b:Ldz0/a;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lfz0/b;->a:Lfz0/c;

    iget-object v1, p0, Lfz0/b;->b:Ldz0/a;

    invoke-static {v0, v1, p1}, Lfz0/c;->e(Lfz0/c;Ldz0/a;Landroid/view/View;)V

    return-void
.end method
