.class public final synthetic LuZ0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LuZ0/b;->a:Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LuZ0/b;->a:Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;

    invoke-static {v0}, Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;->a(Lorg/xbet/uikit/components/bannercollection/items/rectangleVerticalNoTitle/BannerRectangleVerticalNoTitleItemsView;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
