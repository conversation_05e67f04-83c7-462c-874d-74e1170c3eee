.class public final synthetic Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lpo/a;

.field public final synthetic b:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;


# direct methods
.method public synthetic constructor <init>(Lpo/a;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/l;->a:Lpo/a;

    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/l;->b:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/l;->a:Lpo/a;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/l;->b:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    check-cast p1, LvX0/f;

    invoke-static {v0, v1, p1}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->z3(Lpo/a;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;LvX0/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
