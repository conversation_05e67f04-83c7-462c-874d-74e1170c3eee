.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/c;
.source "SourceFile"

# interfaces
.implements LHy0/b;
.implements Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003B;\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0013\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u0015\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J!\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u001a\u001a\u00020\u00192\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ!\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001a\u001a\u00020\u00192\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u0019H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001dJ2\u0010\"\u001a\u00020\u00122\u0006\u0010\u001a\u001a\u00020\u00192\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u00192\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u0019H\u0096\u0001\u00a2\u0006\u0004\u0008\"\u0010#J2\u0010$\u001a\u00020\u00122\u0006\u0010\u001a\u001a\u00020\u00192\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u00192\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u0019H\u0096\u0001\u00a2\u0006\u0004\u0008$\u0010#J\u0016\u0010&\u001a\u0008\u0012\u0004\u0012\u00020%0\u0015H\u0096\u0001\u00a2\u0006\u0004\u0008&\u0010\u0018J \u0010+\u001a\u00020\u00122\u0006\u0010(\u001a\u00020\'2\u0006\u0010*\u001a\u00020)H\u0096\u0001\u00a2\u0006\u0004\u0008+\u0010,J\u0010\u0010-\u001a\u00020\u0012H\u0096\u0001\u00a2\u0006\u0004\u0008-\u0010\u0014R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u001a\u0010>\u001a\u0008\u0012\u0004\u0012\u00020;0:8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=\u00a8\u0006?"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/c;",
        "LHy0/b;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;",
        "screenParams",
        "Lm8/a;",
        "coroutineDispatchers",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "getStageTableWithExtrasScenario",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "whoWinViewModelDelegate",
        "<init>",
        "(Landroidx/lifecycle/Q;Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;Lm8/a;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;)V",
        "",
        "t3",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "LVy0/b;",
        "E0",
        "()Lkotlinx/coroutines/flow/e;",
        "",
        "opponentId",
        "gameId",
        "g1",
        "(ILjava/lang/Integer;)V",
        "n3",
        "",
        "sportId",
        "champId",
        "c3",
        "(ILjava/lang/Integer;JI)V",
        "J",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
        "A1",
        "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
        "singleBetGame",
        "Lorg/xbet/betting/core/coupon/models/SimpleBetZip;",
        "simpleBetZip",
        "f2",
        "(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V",
        "j1",
        "x1",
        "Landroidx/lifecycle/Q;",
        "y1",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;",
        "F1",
        "Lm8/a;",
        "H1",
        "LHX0/e;",
        "I1",
        "Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;",
        "P1",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "Lkotlinx/coroutines/flow/V;",
        "LVy0/a;",
        "S1",
        "Lkotlinx/coroutines/flow/V;",
        "stateModel",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LVy0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroidx/lifecycle/Q;Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;Lm8/a;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;)V
    .locals 1
    .param p1    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p6}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-direct {p0, p1, v0}, Lorg/xbet/ui_common/viewmodel/core/c;-><init>(Landroidx/lifecycle/Q;Ljava/util/List;)V

    .line 6
    .line 7
    .line 8
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->x1:Landroidx/lifecycle/Q;

    .line 9
    .line 10
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 11
    .line 12
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->F1:Lm8/a;

    .line 13
    .line 14
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->H1:LHX0/e;

    .line 15
    .line 16
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->I1:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 17
    .line 18
    iput-object p6, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    .line 19
    .line 20
    new-instance p1, LVy0/a;

    .line 21
    .line 22
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    invoke-direct {p1, p2, p3}, LVy0/a;-><init>(Ljava/util/List;Ljava/util/List;)V

    .line 31
    .line 32
    .line 33
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->t3()V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public static final synthetic p3(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->H1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->u3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final t3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;->b()Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_1

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-ne v0, v1, :cond_0

    .line 20
    .line 21
    const-class v0, LDy0/a$b$a;

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 25
    .line 26
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 27
    .line 28
    .line 29
    throw v0

    .line 30
    :cond_1
    const-class v0, LDy0/a$b$b;

    .line 31
    .line 32
    :goto_0
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->I1:Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;

    .line 33
    .line 34
    invoke-virtual {v1}, Lorg/xbet/special_event/impl/who_win/domain/scenario/GetStageTableWithExtrasScenario;->d()Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;

    .line 39
    .line 40
    const/4 v3, 0x0

    .line 41
    invoke-direct {v2, p0, v0, v3}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;Ljava/lang/Class;Lkotlin/coroutines/e;)V

    .line 42
    .line 43
    .line 44
    invoke-static {v1, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    sget-object v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$observeStageTable$2;

    .line 49
    .line 50
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->F1:Lm8/a;

    .line 55
    .line 56
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    invoke-static {v0, v2, v1}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 65
    .line 66
    .line 67
    return-void
.end method

.method private static final synthetic u3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public A1()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->A1()Lkotlinx/coroutines/flow/e;

    move-result-object v0

    return-object v0
.end method

.method public final E0()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LVy0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->S1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$getUiState$$inlined$map$1;

    .line 4
    .line 5
    invoke-direct {v1, v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel$getUiState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method

.method public J(ILjava/lang/Integer;JI)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    move v1, p1

    move-object v2, p2

    move-wide v3, p3

    move v5, p5

    invoke-interface/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->J(ILjava/lang/Integer;JI)V

    return-void
.end method

.method public c3(ILjava/lang/Integer;JI)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    move v1, p1

    move-object v2, p2

    move-wide v3, p3

    move v5, p5

    invoke-interface/range {v0 .. v5}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->c3(ILjava/lang/Integer;JI)V

    return-void
.end method

.method public f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
    .locals 1
    .param p1    # Lorg/xbet/betting/core/coupon/models/SingleBetGame;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/betting/core/coupon/models/SimpleBetZip;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V

    return-void
.end method

.method public g1(ILjava/lang/Integer;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;->c()J

    .line 4
    .line 5
    .line 6
    move-result-wide v4

    .line 7
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;->a()I

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    move-object v1, p0

    .line 14
    move v2, p1

    .line 15
    move-object v3, p2

    .line 16
    invoke-virtual/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->c3(ILjava/lang/Integer;JI)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public j1()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->P1:Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;

    invoke-interface {v0}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;->j1()V

    return-void
.end method

.method public n3(ILjava/lang/Integer;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;->c()J

    .line 4
    .line 5
    .line 6
    move-result-wide v4

    .line 7
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->y1:Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/model/SingleStageScreenParams;->a()I

    .line 10
    .line 11
    .line 12
    move-result v6

    .line 13
    move-object v1, p0

    .line 14
    move v2, p1

    .line 15
    move-object v3, p2

    .line 16
    invoke-virtual/range {v1 .. v6}, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageViewModel;->J(ILjava/lang/Integer;JI)V

    .line 17
    .line 18
    .line 19
    return-void
.end method
