.class final Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.data.repository.StageTableRepositoryImpl$getStageTable$2"
    f = "StageTableRepositoryImpl.kt"
    l = {
        0x28
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->b(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Long;ZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LDy0/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "LDy0/a;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $cutCoef:Z

.field final synthetic $enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

.field final synthetic $eventId:I

.field final synthetic $userId:Ljava/lang/Long;

.field final synthetic $userRegistrationCountryId:Ljava/lang/Integer;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Integer;ILjava/lang/Long;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "Ljava/lang/Integer;",
            "I",
            "Ljava/lang/Long;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    iput p4, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$eventId:I

    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userId:Ljava/lang/Long;

    iput-boolean p6, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$cutCoef:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    iget v4, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$eventId:I

    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userId:Ljava/lang/Long;

    iget-boolean v6, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$cutCoef:Z

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;-><init>(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Integer;ILjava/lang/Long;ZLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->e(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Lc8/h;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-interface {p1}, Lc8/h;->j()Lc8/i;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$enCoefView:Lorg/xbet/coef_type/api/domain/models/EnCoefView;

    .line 38
    .line 39
    iget-object v5, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userRegistrationCountryId:Ljava/lang/Integer;

    .line 40
    .line 41
    iget v6, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$eventId:I

    .line 42
    .line 43
    iget-object v7, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$userId:Ljava/lang/Long;

    .line 44
    .line 45
    iget-boolean v8, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->$cutCoef:Z

    .line 46
    .line 47
    invoke-static/range {v3 .. v8}, Lwy0/b;->a(Lc8/i;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Integer;ILjava/lang/Long;Z)Ljava/util/Map;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;

    .line 52
    .line 53
    invoke-static {v1}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->g(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Lvy0/b;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->label:I

    .line 58
    .line 59
    invoke-virtual {v1, p1, p0}, Lvy0/b;->b(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    if-ne p1, v0, :cond_2

    .line 64
    .line 65
    return-object v0

    .line 66
    :cond_2
    :goto_0
    check-cast p1, Le8/b;

    .line 67
    .line 68
    invoke-virtual {p1}, Le8/b;->a()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    check-cast p1, Ljava/util/List;

    .line 73
    .line 74
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;->this$0:Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;

    .line 75
    .line 76
    invoke-static {v0}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->f(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Luy0/a;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-virtual {v0}, Luy0/a;->a()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    new-instance v1, Ljava/util/ArrayList;

    .line 85
    .line 86
    const/16 v2, 0xa

    .line 87
    .line 88
    invoke-static {p1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 89
    .line 90
    .line 91
    move-result v2

    .line 92
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 93
    .line 94
    .line 95
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 100
    .line 101
    .line 102
    move-result v2

    .line 103
    if-eqz v2, :cond_3

    .line 104
    .line 105
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object v2

    .line 109
    check-cast v2, Lxy0/a;

    .line 110
    .line 111
    invoke-static {v2, v0}, Lwy0/a;->a(Lxy0/a;Ljava/util/List;)LDy0/a;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 116
    .line 117
    .line 118
    goto :goto_1

    .line 119
    :cond_3
    return-object v1
.end method
