.class public final Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LHy0/b;",
        "whoWinCardClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(LUX0/k;LHy0/b;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LHy0/b;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt;->g(LHy0/b;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt;->i(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/s1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/s1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt;->h(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(LUX0/k;LHy0/b;)LA4/c;
    .locals 3
    .param p0    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LHy0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "LHy0/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LJy0/g;

    .line 2
    .line 3
    invoke-direct {v0}, LJy0/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LJy0/h;

    .line 7
    .line 8
    invoke-direct {v1, p1, p0}, LJy0/h;-><init>(LHy0/b;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$groupStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$groupStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$groupStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$groupStageAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/s1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/s1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/s1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(LHy0/b;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 13

    .line 1
    new-instance v0, LGy0/b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LGy0/b;-><init>(LHy0/b;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, LGq0/s1;

    .line 11
    .line 12
    invoke-virtual {p0}, LGq0/s1;->b()Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    new-instance v1, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 17
    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    sget v3, Lpb/f;->space_8:I

    .line 23
    .line 24
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    const/16 v11, 0x1de

    .line 29
    .line 30
    const/4 v12, 0x0

    .line 31
    const/4 v3, 0x0

    .line 32
    const/4 v4, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v6, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    const/4 v10, 0x0

    .line 39
    invoke-direct/range {v1 .. v12}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 46
    .line 47
    .line 48
    new-instance v1, LJy0/i;

    .line 49
    .line 50
    invoke-direct {v1, p1, p2, p0}, LJy0/i;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p2, v1}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 54
    .line 55
    .line 56
    new-instance v1, LJy0/j;

    .line 57
    .line 58
    invoke-direct {v1, p1, p2, p0}, LJy0/j;-><init>(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p2, v1}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 62
    .line 63
    .line 64
    new-instance p0, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$a;

    .line 65
    .line 66
    invoke-direct {p0, v0, p2, v0, p2}, Lorg/xbet/special_event/impl/who_win/presentation/adapter/viewholder/GroupStageViewHolderKt$a;-><init>(LGy0/b;LB4/a;LGy0/b;LB4/a;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 70
    .line 71
    .line 72
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 73
    .line 74
    return-object p0
.end method

.method public static final h(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final i(LUX0/k;LB4/a;Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getBindingAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p0, p1, p2}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method
