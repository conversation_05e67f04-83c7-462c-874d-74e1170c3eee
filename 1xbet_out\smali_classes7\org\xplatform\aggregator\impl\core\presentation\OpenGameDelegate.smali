.class public final Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$a;,
        Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009a\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 V2\u00020\u0001:\u000297BY\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0013\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\u00190\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJI\u0010(\u001a\u00020$2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010!\u001a\u00020 2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020$0\"2\u000e\u0008\u0002\u0010\'\u001a\u0008\u0012\u0004\u0012\u00020$0&\u00a2\u0006\u0004\u0008(\u0010)J1\u0010+\u001a\u00020$2\u0006\u0010\u001d\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001e2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020$0\"\u00a2\u0006\u0004\u0008+\u0010,J3\u0010-\u001a\u00020$2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020$0\"2\u0006\u0010\u001d\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008-\u0010.J;\u00100\u001a\u00020$2\u0006\u0010\u001d\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010/\u001a\u00020\u001c2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020$0\"H\u0002\u00a2\u0006\u0004\u00080\u00101J\u0010\u00103\u001a\u000202H\u0082@\u00a2\u0006\u0004\u00083\u00104J\'\u00105\u001a\u00020$2\u0006\u0010\u001d\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010/\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u00085\u00106R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010K\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0018\u0010O\u001a\u0004\u0018\u00010L8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0018\u0010Q\u001a\u0004\u0018\u00010L8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008P\u0010NR\u001a\u0010U\u001a\u0008\u0012\u0004\u0012\u00020\u00190R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010T\u00a8\u0006W"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "",
        "LQW0/c;",
        "coroutinesLib",
        "Le81/d;",
        "getGameToOpenScenario",
        "LJT/a;",
        "addAggregatorLastActionUseCase",
        "LwX0/C;",
        "routerHolder",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lv81/b;",
        "checkBalanceForAggregatorCatalogScenario",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "LwX0/a;",
        "screensProvider",
        "<init>",
        "(LQW0/c;Le81/d;LJT/a;LwX0/C;Lorg/xbet/ui_common/utils/internet/a;Lek/d;Lv81/b;Lfk/m;Lek/f;LwX0/a;)V",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "q",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "game",
        "",
        "subCategoryId",
        "Lkotlinx/coroutines/N;",
        "scope",
        "Lkotlin/Function1;",
        "",
        "",
        "callOnError",
        "Lkotlin/Function0;",
        "callFinally",
        "t",
        "(JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "u",
        "(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V",
        "r",
        "(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V",
        "balanceId",
        "o",
        "(Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V",
        "",
        "p",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "y",
        "(Lorg/xplatform/aggregator/api/model/Game;IJ)V",
        "a",
        "Le81/d;",
        "b",
        "LJT/a;",
        "c",
        "LwX0/C;",
        "d",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "e",
        "Lek/d;",
        "f",
        "Lv81/b;",
        "g",
        "Lfk/m;",
        "h",
        "Lek/f;",
        "i",
        "LwX0/a;",
        "j",
        "Lkotlinx/coroutines/N;",
        "delegateScope",
        "Lkotlinx/coroutines/x0;",
        "k",
        "Lkotlinx/coroutines/x0;",
        "launchGameJob",
        "l",
        "onGameClickJob",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "m",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "event",
        "n",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final n:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Le81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LJT/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lek/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lv81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Lkotlinx/coroutines/x0;

.field public l:Lkotlinx/coroutines/x0;

.field public final m:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->n:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$a;

    return-void
.end method

.method public constructor <init>(LQW0/c;Le81/d;LJT/a;LwX0/C;Lorg/xbet/ui_common/utils/internet/a;Lek/d;Lv81/b;Lfk/m;Lek/f;LwX0/a;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Le81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LJT/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lv81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->a:Le81/d;

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->b:LJT/a;

    .line 7
    .line 8
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->c:LwX0/C;

    .line 9
    .line 10
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->e:Lek/d;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->f:Lv81/b;

    .line 15
    .line 16
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->g:Lfk/m;

    .line 17
    .line 18
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h:Lek/f;

    .line 19
    .line 20
    iput-object p10, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->i:LwX0/a;

    .line 21
    .line 22
    invoke-interface {p1}, LQW0/c;->a()Lm8/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    const/4 p2, 0x1

    .line 31
    const/4 p3, 0x0

    .line 32
    invoke-static {p3, p2, p3}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    invoke-virtual {p1, p2}, Lkotlin/coroutines/a;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-static {p1}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->j:Lkotlinx/coroutines/N;

    .line 45
    .line 46
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 47
    .line 48
    const/4 p2, 0x0

    .line 49
    const/4 p4, 0x3

    .line 50
    invoke-direct {p1, p2, p3, p4, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 51
    .line 52
    .line 53
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->m:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 54
    .line 55
    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->s(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->x(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->w()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->o(Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->p(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)LJT/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->b:LJT/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/internet/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->d:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->m:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Le81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->a:Le81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->g:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic k(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lek/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->e:Lek/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic l(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->h:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic m(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->r(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic n(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJ)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->y(Lorg/xplatform/aggregator/api/model/Game;IJ)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final s(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->k:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->j:Lkotlinx/coroutines/N;

    .line 11
    .line 12
    new-instance v4, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;

    .line 13
    .line 14
    const/4 v9, 0x0

    .line 15
    move-object v5, p0

    .line 16
    move-object v8, p1

    .line 17
    move-object v6, p2

    .line 18
    move v7, p3

    .line 19
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$launchGame$1$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    move-object p0, v8

    .line 23
    move-object v8, v4

    .line 24
    move-object v4, p0

    .line 25
    move-object p0, v5

    .line 26
    const/16 v9, 0xe

    .line 27
    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x0

    .line 32
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->k:Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 39
    .line 40
    return-object p0
.end method

.method public static synthetic v(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V
    .locals 7

    .line 1
    and-int/lit8 p7, p7, 0x10

    .line 2
    .line 3
    if-eqz p7, :cond_0

    .line 4
    .line 5
    new-instance p6, Lorg/xplatform/aggregator/impl/core/presentation/z;

    .line 6
    .line 7
    invoke-direct {p6}, Lorg/xplatform/aggregator/impl/core/presentation/z;-><init>()V

    .line 8
    .line 9
    .line 10
    :cond_0
    move-object v0, p0

    .line 11
    move-wide v1, p1

    .line 12
    move v3, p3

    .line 13
    move-object v4, p4

    .line 14
    move-object v5, p5

    .line 15
    move-object v6, p6

    .line 16
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->t(JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public static final w()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final x(Lkotlin/jvm/functions/Function0;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final o(Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/jvm/functions/Function1;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "IJ",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->j:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$changeBalanceToPrimary$1;

    .line 4
    .line 5
    const/4 v7, 0x0

    .line 6
    move-object v2, p0

    .line 7
    move-object v3, p1

    .line 8
    move v4, p2

    .line 9
    move-wide v5, p3

    .line 10
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$changeBalanceToPrimary$1;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/api/model/Game;IJLkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    const/16 v6, 0xe

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    move-object v5, v1

    .line 19
    move-object v1, p5

    .line 20
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final p(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/Boolean;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->f:Lv81/b;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lv81/b;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public final q()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->m:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final r(Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->c:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/y;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/presentation/y;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final t(JILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V
    .locals 11
    .param p4    # Lkotlinx/coroutines/N;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->l:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->k:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    :cond_1
    new-instance v7, Lorg/xplatform/aggregator/impl/core/presentation/A;

    .line 18
    .line 19
    move-object/from16 v0, p6

    .line 20
    .line 21
    invoke-direct {v7, v0}, Lorg/xplatform/aggregator/impl/core/presentation/A;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 22
    .line 23
    .line 24
    new-instance v0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;

    .line 25
    .line 26
    const/4 v6, 0x0

    .line 27
    move-object v3, p0

    .line 28
    move-wide v1, p1

    .line 29
    move v4, p3

    .line 30
    move-object/from16 v5, p5

    .line 31
    .line 32
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$3;-><init>(JLorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;ILkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;)V

    .line 33
    .line 34
    .line 35
    move-object v8, v0

    .line 36
    const/16 v9, 0xc

    .line 37
    .line 38
    const/4 v10, 0x0

    .line 39
    move-object v5, v7

    .line 40
    const/4 v7, 0x0

    .line 41
    move-object v3, p4

    .line 42
    move-object/from16 v4, p5

    .line 43
    .line 44
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->l:Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    return-void
.end method

.method public final u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V
    .locals 8
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->j:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    move-object v2, p0

    .line 7
    move-object v4, p1

    .line 8
    move v5, p2

    .line 9
    move-object v3, p3

    .line 10
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$onGameClick$4;-><init>(Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lkotlin/jvm/functions/Function1;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    move-object v5, v1

    .line 14
    move-object v1, v3

    .line 15
    const/16 v6, 0xe

    .line 16
    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v2, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final y(Lorg/xplatform/aggregator/api/model/Game;IJ)V
    .locals 13

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->c:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->i:LwX0/a;

    .line 10
    .line 11
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getProviderId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v4

    .line 19
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNeedTransfer()Z

    .line 20
    .line 21
    .line 22
    move-result v6

    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getProductId()J

    .line 24
    .line 25
    .line 26
    move-result-wide v7

    .line 27
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNoLoyalty()Z

    .line 28
    .line 29
    .line 30
    move-result v9

    .line 31
    move v12, p2

    .line 32
    move-wide/from16 v10, p3

    .line 33
    .line 34
    invoke-interface/range {v1 .. v12}, LwX0/a;->v(JJZJZJI)Lq4/q;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 39
    .line 40
    .line 41
    :cond_0
    return-void
.end method
