.class public final Lorg/xbet/special_event/impl/who_win/presentation/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->j:LBc/a;

    .line 23
    .line 24
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/special_event/impl/who_win/presentation/g;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Integer;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;)",
            "Lorg/xbet/special_event/impl/who_win/presentation/g;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/g;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object/from16 v6, p5

    .line 9
    .line 10
    move-object/from16 v7, p6

    .line 11
    .line 12
    move-object/from16 v8, p7

    .line 13
    .line 14
    move-object/from16 v9, p8

    .line 15
    .line 16
    move-object/from16 v10, p9

    .line 17
    .line 18
    invoke-direct/range {v0 .. v10}, Lorg/xbet/special_event/impl/who_win/presentation/g;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method

.method public static c(ILwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;
    .locals 11

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 2
    .line 3
    move v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object/from16 v6, p5

    .line 9
    .line 10
    move-object/from16 v7, p6

    .line 11
    .line 12
    move-object/from16 v8, p7

    .line 13
    .line 14
    move-object/from16 v9, p8

    .line 15
    .line 16
    move-object/from16 v10, p9

    .line 17
    .line 18
    invoke-direct/range {v0 .. v10}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;-><init>(ILwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/ui_common/utils/internet/a;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Integer;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->b:LBc/a;

    .line 14
    .line 15
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    move-object v2, v0

    .line 20
    check-cast v2, LwX0/c;

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->c:LBc/a;

    .line 23
    .line 24
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    move-object v3, v0

    .line 29
    check-cast v3, Lm8/a;

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->d:LBc/a;

    .line 32
    .line 33
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    move-object v4, v0

    .line 38
    check-cast v4, Lorg/xbet/ui_common/utils/M;

    .line 39
    .line 40
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->e:LBc/a;

    .line 41
    .line 42
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    move-object v5, v0

    .line 47
    check-cast v5, LHX0/e;

    .line 48
    .line 49
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->f:LBc/a;

    .line 50
    .line 51
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    move-object v6, v0

    .line 56
    check-cast v6, Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 57
    .line 58
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->g:LBc/a;

    .line 59
    .line 60
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    move-object v7, v0

    .line 65
    check-cast v7, Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 66
    .line 67
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->h:LBc/a;

    .line 68
    .line 69
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    move-object v8, v0

    .line 74
    check-cast v8, Lp9/c;

    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->i:LBc/a;

    .line 77
    .line 78
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    move-object v9, v0

    .line 83
    check-cast v9, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 84
    .line 85
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/g;->j:LBc/a;

    .line 86
    .line 87
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    move-object v10, v0

    .line 92
    check-cast v10, Lorg/xbet/ui_common/utils/internet/a;

    .line 93
    .line 94
    invoke-static/range {v1 .. v10}, Lorg/xbet/special_event/impl/who_win/presentation/g;->c(ILwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/ui_common/utils/internet/a;)Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/g;->b()Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
