.class public final synthetic Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/b;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/b;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;

    invoke-static {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;->z2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
