.class public final LOB0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "Lorg/xbet/sportgame/core/domain/models/markets/a;",
        "",
        "marketId",
        "",
        "marketIdEnable",
        "LPB0/a;",
        "a",
        "(Lorg/xbet/sportgame/core/domain/models/markets/a;JZ)LPB0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/sportgame/core/domain/models/markets/a;JZ)LPB0/a;
    .locals 16
    .param p0    # Lorg/xbet/sportgame/core/domain/models/markets/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->k()I

    .line 2
    .line 3
    .line 4
    move-result v1

    .line 5
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->o()J

    .line 6
    .line 7
    .line 8
    move-result-wide v2

    .line 9
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->p()D

    .line 10
    .line 11
    .line 12
    move-result-wide v4

    .line 13
    sget-object v0, Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;->Companion:Lorg/xbet/betting/core/zip/model/bet/KindEnumModel$a;

    .line 14
    .line 15
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->l()Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;

    .line 16
    .line 17
    .line 18
    move-result-object v6

    .line 19
    invoke-virtual {v6}, Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;->getId()I

    .line 20
    .line 21
    .line 22
    move-result v6

    .line 23
    invoke-virtual {v0, v6}, Lorg/xbet/betting/core/zip/model/bet/KindEnumModel$a;->a(I)Lorg/xbet/betting/core/zip/model/bet/KindEnumModel;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    new-instance v9, Lu31/a$b;

    .line 28
    .line 29
    if-eqz p3, :cond_0

    .line 30
    .line 31
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->w()I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->o()J

    .line 36
    .line 37
    .line 38
    move-result-wide v6

    .line 39
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->i()Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v10

    .line 43
    new-instance v11, Ljava/lang/StringBuilder;

    .line 44
    .line 45
    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    .line 46
    .line 47
    .line 48
    const-string v12, "[\u0420 :"

    .line 49
    .line 50
    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 54
    .line 55
    .line 56
    const-string v0, "][\u041c:"

    .line 57
    .line 58
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 59
    .line 60
    .line 61
    invoke-virtual {v11, v6, v7}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    .line 62
    .line 63
    .line 64
    const-string v0, "] "

    .line 65
    .line 66
    invoke-virtual {v11, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 70
    .line 71
    .line 72
    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    :goto_0
    move-object v10, v0

    .line 77
    goto :goto_1

    .line 78
    :cond_0
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->i()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    goto :goto_0

    .line 83
    :goto_1
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->h()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v11

    .line 87
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->d()Z

    .line 88
    .line 89
    .line 90
    move-result v0

    .line 91
    if-eqz v0, :cond_1

    .line 92
    .line 93
    sget-object v0, Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;->BLOCKED:Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;

    .line 94
    .line 95
    :goto_2
    move-object v12, v0

    .line 96
    goto :goto_3

    .line 97
    :cond_1
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->f()Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    sget-object v6, Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;->INCREASED:Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;

    .line 102
    .line 103
    if-ne v0, v6, :cond_2

    .line 104
    .line 105
    sget-object v0, Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;->HIGHER:Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;

    .line 106
    .line 107
    goto :goto_2

    .line 108
    :cond_2
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->f()Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;

    .line 109
    .line 110
    .line 111
    move-result-object v0

    .line 112
    sget-object v6, Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;->DECREASED:Lorg/xbet/sportgame/core/domain/models/markets/MarketBetModel$CoefType;

    .line 113
    .line 114
    if-ne v0, v6, :cond_3

    .line 115
    .line 116
    sget-object v0, Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;->LOWER:Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;

    .line 117
    .line 118
    goto :goto_2

    .line 119
    :cond_3
    sget-object v0, Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;->DEFAULT:Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;

    .line 120
    .line 121
    goto :goto_2

    .line 122
    :goto_3
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->c()Z

    .line 123
    .line 124
    .line 125
    move-result v13

    .line 126
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->v()Z

    .line 127
    .line 128
    .line 129
    move-result v14

    .line 130
    invoke-virtual/range {p0 .. p0}, Lorg/xbet/sportgame/core/domain/models/markets/a;->t()Z

    .line 131
    .line 132
    .line 133
    move-result v15

    .line 134
    invoke-direct/range {v9 .. v15}, Lu31/a$b;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;ZZZ)V

    .line 135
    .line 136
    .line 137
    new-instance v0, LPB0/a$a;

    .line 138
    .line 139
    move-wide/from16 v6, p1

    .line 140
    .line 141
    invoke-direct/range {v0 .. v9}, LPB0/a$a;-><init>(IJDJLorg/xbet/betting/core/zip/model/bet/KindEnumModel;Lu31/a;)V

    .line 142
    .line 143
    .line 144
    return-object v0
.end method
