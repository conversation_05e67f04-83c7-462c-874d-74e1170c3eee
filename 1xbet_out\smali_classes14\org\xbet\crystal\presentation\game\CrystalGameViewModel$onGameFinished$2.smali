.class final Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.crystal.presentation.game.CrystalGameViewModel$onGameFinished$2"
    f = "CrystalGameViewModel.kt"
    l = {
        0x49
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->F3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;

    iget-object v0, p0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;-><init>(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    iget-object v1, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->L$1:Ljava/lang/Object;

    .line 15
    .line 16
    check-cast v1, LZx/b;

    .line 17
    .line 18
    iget-object v2, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->L$0:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v2, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 21
    .line 22
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 27
    .line 28
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 29
    .line 30
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    throw v1

    .line 34
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    iget-object v2, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 38
    .line 39
    invoke-static {v2}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->v3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lay/f;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-virtual {v2}, Lay/f;->a()LZx/b;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    iget-object v4, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->this$0:Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;

    .line 48
    .line 49
    invoke-static {v4}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->r3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;)Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 50
    .line 51
    .line 52
    move-result-object v5

    .line 53
    new-instance v6, LTv/a$j;

    .line 54
    .line 55
    invoke-virtual {v2}, LZx/b;->g()D

    .line 56
    .line 57
    .line 58
    move-result-wide v7

    .line 59
    invoke-virtual {v2}, LZx/b;->d()Lorg/xbet/core/domain/StatusBetEnum;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-virtual {v2}, LZx/b;->e()D

    .line 64
    .line 65
    .line 66
    move-result-wide v11

    .line 67
    invoke-virtual {v2}, LZx/b;->c()D

    .line 68
    .line 69
    .line 70
    move-result-wide v13

    .line 71
    invoke-virtual {v2}, LZx/b;->b()Lorg/xbet/games_section/api/models/GameBonus;

    .line 72
    .line 73
    .line 74
    move-result-object v10

    .line 75
    invoke-virtual {v10}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 76
    .line 77
    .line 78
    move-result-object v15

    .line 79
    invoke-virtual {v2}, LZx/b;->a()J

    .line 80
    .line 81
    .line 82
    move-result-wide v16

    .line 83
    const/4 v10, 0x0

    .line 84
    invoke-direct/range {v6 .. v17}, LTv/a$j;-><init>(DLorg/xbet/core/domain/StatusBetEnum;ZDDLorg/xbet/games_section/api/models/GameBonusType;J)V

    .line 85
    .line 86
    .line 87
    iput-object v4, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->L$0:Ljava/lang/Object;

    .line 88
    .line 89
    iput-object v2, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->L$1:Ljava/lang/Object;

    .line 90
    .line 91
    iput v3, v0, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$onGameFinished$2;->label:I

    .line 92
    .line 93
    invoke-virtual {v5, v6, v0}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    if-ne v3, v1, :cond_2

    .line 98
    .line 99
    return-object v1

    .line 100
    :cond_2
    move-object v1, v2

    .line 101
    move-object v2, v4

    .line 102
    :goto_0
    new-instance v3, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$d;

    .line 103
    .line 104
    invoke-virtual {v1}, LZx/b;->g()D

    .line 105
    .line 106
    .line 107
    move-result-wide v4

    .line 108
    invoke-direct {v3, v4, v5}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a$d;-><init>(D)V

    .line 109
    .line 110
    .line 111
    invoke-static {v2, v3}, Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;->z3(Lorg/xbet/crystal/presentation/game/CrystalGameViewModel;Lorg/xbet/crystal/presentation/game/CrystalGameViewModel$a;)V

    .line 112
    .line 113
    .line 114
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 115
    .line 116
    return-object v1
.end method
