.class public final LUW0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LUW0/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LUW0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:LTW0/a;

.field public final b:LUW0/a$b;


# direct methods
.method public constructor <init>(LTW0/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LUW0/a$b;->b:LUW0/a$b;

    .line 4
    iput-object p1, p0, LUW0/a$b;->a:LTW0/a;

    return-void
.end method

.method public synthetic constructor <init>(LTW0/a;LUW0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LUW0/a$b;-><init>(LTW0/a;)V

    return-void
.end method


# virtual methods
.method public a()LWW0/c;
    .locals 1

    .line 1
    invoke-virtual {p0}, LUW0/a$b;->e()LWW0/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public b()LWW0/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LUW0/a$b;->d()LWW0/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final c()LTW0/b;
    .locals 2

    .line 1
    new-instance v0, LTW0/b;

    .line 2
    .line 3
    iget-object v1, p0, LUW0/a$b;->a:LTW0/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LTW0/b;-><init>(LTW0/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final d()LWW0/b;
    .locals 2

    .line 1
    new-instance v0, LWW0/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LUW0/a$b;->c()LTW0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LWW0/b;-><init>(LVW0/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final e()LWW0/d;
    .locals 2

    .line 1
    new-instance v0, LWW0/d;

    .line 2
    .line 3
    invoke-virtual {p0}, LUW0/a$b;->c()LTW0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LWW0/d;-><init>(LVW0/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
