.class public final Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0013\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0015\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ-\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\t\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u001b\u001a\u00020\r2\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u001b\u0010&\u001a\u00020!8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%R\u001a\u0010)\u001a\u0008\u0012\u0004\u0012\u00020\t0\'8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010(R\u0018\u0010,\u001a\u0004\u0018\u00010*8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\n\u0010+\u00a8\u0006-"
    }
    d2 = {
        "Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;",
        "",
        "Landroid/content/Context;",
        "context",
        "Lm8/a;",
        "coroutineDispatchers",
        "<init>",
        "(Landroid/content/Context;Lm8/a;)V",
        "Lkotlinx/coroutines/flow/e;",
        "Lcom/xbet/onexcore/themes/Theme;",
        "e",
        "()Lkotlinx/coroutines/flow/e;",
        "oldTheme",
        "",
        "h",
        "(Lcom/xbet/onexcore/themes/Theme;)V",
        "",
        "name",
        "",
        "initialDelay",
        "",
        "timeTableEnabled",
        "theme",
        "i",
        "(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V",
        "Ljava/util/UUID;",
        "id",
        "f",
        "(Ljava/util/UUID;)V",
        "a",
        "Landroid/content/Context;",
        "b",
        "Lm8/a;",
        "Lkotlinx/coroutines/N;",
        "c",
        "Lkotlin/j;",
        "d",
        "()Lkotlinx/coroutines/N;",
        "scope",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "themeSwitchStream",
        "Lkotlinx/coroutines/x0;",
        "Lkotlinx/coroutines/x0;",
        "timeTableJob",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lcom/xbet/onexcore/themes/Theme;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Lkotlinx/coroutines/x0;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lm8/a;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->a:Landroid/content/Context;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->b:Lm8/a;

    .line 7
    .line 8
    new-instance p1, Lorg/xbet/themeswitch/impl/data/a;

    .line 9
    .line 10
    invoke-direct {p1, p0}, Lorg/xbet/themeswitch/impl/data/a;-><init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)V

    .line 11
    .line 12
    .line 13
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->c:Lkotlin/j;

    .line 18
    .line 19
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 20
    .line 21
    const/4 p2, 0x1

    .line 22
    sget-object v0, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 23
    .line 24
    invoke-direct {p1, p2, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 25
    .line 26
    .line 27
    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->d:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 28
    .line 29
    return-void
.end method

.method public static synthetic a(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lkotlinx/coroutines/N;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->g(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lkotlinx/coroutines/N;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Landroid/content/Context;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->a:Landroid/content/Context;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->d:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final g(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;)Lkotlinx/coroutines/N;
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {v0, v1, v0}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object p0, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->b:Lm8/a;

    .line 8
    .line 9
    invoke-interface {p0}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-interface {v0, p0}, Lkotlin/coroutines/CoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-static {p0}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    return-object p0
.end method


# virtual methods
.method public final d()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlinx/coroutines/N;

    .line 8
    .line 9
    return-object v0
.end method

.method public final e()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lcom/xbet/onexcore/themes/Theme;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->d:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f(Ljava/util/UUID;)V
    .locals 8

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->d()Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$1;->INSTANCE:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$1;

    .line 6
    .line 7
    new-instance v5, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$observeWork$2;-><init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Ljava/util/UUID;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    const/16 v6, 0xe

    .line 14
    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    iput-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->e:Lkotlinx/coroutines/x0;

    .line 23
    .line 24
    return-void
.end method

.method public final h(Lcom/xbet/onexcore/themes/Theme;)V
    .locals 8
    .param p1    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->d()Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$updateThemeSwitchStream$1;->INSTANCE:Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$updateThemeSwitchStream$1;

    .line 6
    .line 7
    new-instance v5, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$updateThemeSwitchStream$2;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource$updateThemeSwitchStream$2;-><init>(Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;Lcom/xbet/onexcore/themes/Theme;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    const/16 v6, 0xe

    .line 14
    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public final i(Ljava/lang/String;JZLcom/xbet/onexcore/themes/Theme;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lcom/xbet/onexcore/themes/Theme;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    sget-object v2, Landroidx/work/WorkManager;->a:Landroidx/work/WorkManager$a;

    .line 4
    .line 5
    iget-object v3, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->a:Landroid/content/Context;

    .line 6
    .line 7
    invoke-virtual {v3}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    invoke-virtual {v2, v3}, Landroidx/work/WorkManager$a;->a(Landroid/content/Context;)Landroidx/work/WorkManager;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    if-eqz p4, :cond_0

    .line 16
    .line 17
    new-instance p4, Landroidx/work/u$a;

    .line 18
    .line 19
    const-class v3, Lorg/xbet/themeswitch/impl/ThemeSwitchTimeWorker;

    .line 20
    .line 21
    invoke-direct {p4, v3}, Landroidx/work/u$a;-><init>(Ljava/lang/Class;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p5}, Ljava/lang/Enum;->ordinal()I

    .line 25
    .line 26
    .line 27
    move-result p5

    .line 28
    invoke-static {p5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object p5

    .line 32
    const-string v3, "theme"

    .line 33
    .line 34
    invoke-static {v3, p5}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 35
    .line 36
    .line 37
    move-result-object p5

    .line 38
    new-array v1, v1, [Lkotlin/Pair;

    .line 39
    .line 40
    aput-object p5, v1, v0

    .line 41
    .line 42
    new-instance p5, Landroidx/work/Data$a;

    .line 43
    .line 44
    invoke-direct {p5}, Landroidx/work/Data$a;-><init>()V

    .line 45
    .line 46
    .line 47
    aget-object v0, v1, v0

    .line 48
    .line 49
    invoke-virtual {v0}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    check-cast v1, Ljava/lang/String;

    .line 54
    .line 55
    invoke-virtual {v0}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {p5, v1, v0}, Landroidx/work/Data$a;->b(Ljava/lang/String;Ljava/lang/Object;)Landroidx/work/Data$a;

    .line 60
    .line 61
    .line 62
    invoke-virtual {p5}, Landroidx/work/Data$a;->a()Landroidx/work/Data;

    .line 63
    .line 64
    .line 65
    move-result-object p5

    .line 66
    invoke-virtual {p4, p5}, Landroidx/work/I$a;->l(Landroidx/work/Data;)Landroidx/work/I$a;

    .line 67
    .line 68
    .line 69
    move-result-object p4

    .line 70
    check-cast p4, Landroidx/work/u$a;

    .line 71
    .line 72
    sget-object p5, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 73
    .line 74
    invoke-virtual {p4, p2, p3, p5}, Landroidx/work/I$a;->k(JLjava/util/concurrent/TimeUnit;)Landroidx/work/I$a;

    .line 75
    .line 76
    .line 77
    move-result-object p2

    .line 78
    check-cast p2, Landroidx/work/u$a;

    .line 79
    .line 80
    invoke-virtual {p2}, Landroidx/work/I$a;->b()Landroidx/work/I;

    .line 81
    .line 82
    .line 83
    move-result-object p2

    .line 84
    check-cast p2, Landroidx/work/u;

    .line 85
    .line 86
    sget-object p3, Landroidx/work/ExistingWorkPolicy;->REPLACE:Landroidx/work/ExistingWorkPolicy;

    .line 87
    .line 88
    invoke-virtual {v2, p1, p3, p2}, Landroidx/work/WorkManager;->g(Ljava/lang/String;Landroidx/work/ExistingWorkPolicy;Landroidx/work/u;)Landroidx/work/v;

    .line 89
    .line 90
    .line 91
    invoke-virtual {p2}, Landroidx/work/I;->a()Ljava/util/UUID;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    invoke-virtual {p0, p1}, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->f(Ljava/util/UUID;)V

    .line 96
    .line 97
    .line 98
    return-void

    .line 99
    :cond_0
    invoke-virtual {v2, p1}, Landroidx/work/WorkManager;->c(Ljava/lang/String;)Landroidx/work/v;

    .line 100
    .line 101
    .line 102
    iget-object p1, p0, Lorg/xbet/themeswitch/impl/data/ThemeSwitchDataSource;->e:Lkotlinx/coroutines/x0;

    .line 103
    .line 104
    if-eqz p1, :cond_1

    .line 105
    .line 106
    const/4 p2, 0x0

    .line 107
    invoke-static {p1, p2, v1, p2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 108
    .line 109
    .line 110
    :cond_1
    return-void
.end method
