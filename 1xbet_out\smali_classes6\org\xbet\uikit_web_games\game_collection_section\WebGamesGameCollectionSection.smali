.class public final Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0003\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0015\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\'\u0010\u0015\u001a\u00020\u000c2\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000c0\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0017\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0017\u0010\u0012J\u0017\u0010\u001a\u001a\u00020\u000c2\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001d\u001a\u00020\u000c2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\u0017\u0010\u001e\u001a\u00020\u000c2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001e\u0010\u001bJ\u001b\u0010!\u001a\u00020\u000c2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u00140\u001f\u00a2\u0006\u0004\u0008!\u0010\"J\r\u0010$\u001a\u00020#\u00a2\u0006\u0004\u0008$\u0010%J\r\u0010\'\u001a\u00020&\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010)\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008)\u0010*R\u0014\u0010.\u001a\u00020+8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0016\u00102\u001a\u00020/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0018\u00106\u001a\u0004\u0018\u0001038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u00105\u00a8\u00067"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "gamesType",
        "",
        "s",
        "(Ljava/lang/String;)V",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setOnButtonClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "Lkotlin/Function2;",
        "Ln41/m;",
        "setOnItemClickListener",
        "(Lkotlin/jvm/functions/Function2;)V",
        "setOnTagClickListener",
        "",
        "title",
        "setHeaderTitle",
        "(Ljava/lang/CharSequence;)V",
        "text",
        "setButtonText",
        "setTagText",
        "",
        "items",
        "setItems",
        "(Ljava/util/List;)V",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "getHeader",
        "()Lorg/xbet/uikit/components/header/DSHeader;",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "getCollectionRecycler",
        "()Landroidx/recyclerview/widget/RecyclerView;",
        "t",
        "()V",
        "Lk41/a;",
        "a",
        "Lk41/a;",
        "binding",
        "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;",
        "b",
        "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;",
        "collectionType",
        "Ln41/c;",
        "c",
        "Ln41/c;",
        "gameCollectionAdapter",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lk41/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ln41/c;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, Lk41/a;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lk41/a;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 6
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    iput-object v1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 7
    sget-object v1, Lj41/f;->GameCollectionStyle:[I

    const/4 v2, 0x0

    .line 8
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 9
    sget p3, Lj41/f;->GameCollectionStyle_collectionTagText:I

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p3

    .line 10
    sget v1, Lj41/f;->GameCollectionStyle_collectionButtonText:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-static {p2, p1, v1}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v1

    .line 11
    sget v2, Lj41/f;->GameCollectionStyle_collectionTitle:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {p2, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    .line 12
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->setHeaderTitle(Ljava/lang/CharSequence;)V

    .line 13
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->setButtonText(Ljava/lang/CharSequence;)V

    .line 14
    invoke-virtual {p0, p3}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->setTagText(Ljava/lang/CharSequence;)V

    .line 15
    iget-object p3, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    sget v0, LlZ0/d;->uikitStaticTeal:I

    const/4 v1, 0x0

    const/4 v2, 0x2

    invoke-static {p1, v0, v1, v2, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p3, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setTagColor(Ljava/lang/Integer;)V

    .line 16
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final getCollectionRecycler()Landroidx/recyclerview/widget/RecyclerView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 4
    .line 5
    return-object v0
.end method

.method public final getHeader()Lorg/xbet/uikit/components/header/DSHeader;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    return-object v0
.end method

.method public final s(Ljava/lang/String;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/4 v2, 0x0

    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    move-object v3, v1

    .line 21
    check-cast v3, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 22
    .line 23
    invoke-virtual {v3}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getConfigType()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-static {v3, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    if-eqz v3, :cond_0

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_1
    move-object v1, v2

    .line 35
    :goto_0
    check-cast v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 36
    .line 37
    if-nez v1, :cond_2

    .line 38
    .line 39
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 40
    .line 41
    :cond_2
    iput-object v1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 42
    .line 43
    new-instance v3, Ln41/c;

    .line 44
    .line 45
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 46
    .line 47
    invoke-virtual {p1}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 56
    .line 57
    invoke-virtual {v0}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    sget v1, Lj41/c;->ic_games_placeholder_icon:I

    .line 62
    .line 63
    sget v5, LlZ0/h;->banner_item_placeholder:I

    .line 64
    .line 65
    invoke-static {p1, v0, v1, v5}, Lj41/a;->b(Landroid/content/Context;Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;II)LL11/c;

    .line 66
    .line 67
    .line 68
    move-result-object v5

    .line 69
    const/4 v7, 0x4

    .line 70
    const/4 v8, 0x0

    .line 71
    const/4 v6, 0x0

    .line 72
    invoke-direct/range {v3 .. v8}, Ln41/c;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 73
    .line 74
    .line 75
    iput-object v3, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->c:Ln41/c;

    .line 76
    .line 77
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 78
    .line 79
    iget-object p1, p1, Lk41/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 80
    .line 81
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 82
    .line 83
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    const/4 v3, 0x0

    .line 88
    invoke-direct {v0, v1, v3, v3}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 92
    .line 93
    .line 94
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->c:Ln41/c;

    .line 95
    .line 96
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->t()V

    .line 100
    .line 101
    .line 102
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 103
    .line 104
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection$a;->a:[I

    .line 105
    .line 106
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 107
    .line 108
    .line 109
    move-result p1

    .line 110
    aget p1, v0, p1

    .line 111
    .line 112
    const/4 v0, 0x1

    .line 113
    const/16 v1, 0x8

    .line 114
    .line 115
    if-eq p1, v0, :cond_5

    .line 116
    .line 117
    const/4 v0, 0x2

    .line 118
    if-eq p1, v0, :cond_4

    .line 119
    .line 120
    const/4 v0, 0x3

    .line 121
    if-eq p1, v0, :cond_3

    .line 122
    .line 123
    return-void

    .line 124
    :cond_3
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 125
    .line 126
    iget-object p1, p1, Lk41/a;->b:Landroid/widget/FrameLayout;

    .line 127
    .line 128
    invoke-virtual {p1, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 129
    .line 130
    .line 131
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 132
    .line 133
    iget-object p1, p1, Lk41/a;->b:Landroid/widget/FrameLayout;

    .line 134
    .line 135
    invoke-virtual {p1, v3, v3, v3, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 136
    .line 137
    .line 138
    return-void

    .line 139
    :cond_4
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 140
    .line 141
    iget-object p1, p1, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 142
    .line 143
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 144
    .line 145
    .line 146
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 147
    .line 148
    iget-object p1, p1, Lk41/a;->b:Landroid/widget/FrameLayout;

    .line 149
    .line 150
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    check-cast p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 155
    .line 156
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    sget v1, LlZ0/g;->space_8:I

    .line 161
    .line 162
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 163
    .line 164
    .line 165
    move-result v0

    .line 166
    iput v0, p1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 167
    .line 168
    return-void

    .line 169
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 170
    .line 171
    iget-object p1, p1, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 172
    .line 173
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 174
    .line 175
    .line 176
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 177
    .line 178
    iget-object p1, p1, Lk41/a;->b:Landroid/widget/FrameLayout;

    .line 179
    .line 180
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    check-cast p1, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 185
    .line 186
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 187
    .line 188
    .line 189
    move-result-object v0

    .line 190
    sget v1, LlZ0/g;->space_8:I

    .line 191
    .line 192
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 193
    .line 194
    .line 195
    move-result v0

    .line 196
    iput v0, p1, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 197
    .line 198
    return-void
.end method

.method public final setButtonText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonLabel(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setHeaderTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setItems(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ln41/m;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->c:Ln41/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-virtual {v0, p1, v1}, Landroidx/recyclerview/widget/s;->r(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 7
    .line 8
    .line 9
    :cond_0
    return-void
.end method

.method public final setOnButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ln41/m;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->c:Ln41/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Ln41/c;->z(Lkotlin/jvm/functions/Function2;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final setOnTagClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setTagClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->c:Lorg/xbet/uikit/components/header/DSHeader;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/header/DSHeader;->setTagLabel(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final t()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 2
    .line 3
    iget-object v0, v0, Lk41/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getItemDecorationCount()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-lt v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 14
    .line 15
    invoke-virtual {v0}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    sget-object v2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 20
    .line 21
    if-ne v0, v2, :cond_1

    .line 22
    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    sget v3, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 28
    .line 29
    invoke-virtual {v0, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    :goto_0
    move v5, v0

    .line 34
    goto :goto_1

    .line 35
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    sget v3, LlZ0/g;->space_8:I

    .line 40
    .line 41
    invoke-virtual {v0, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result v0

    .line 45
    goto :goto_0

    .line 46
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    sget v3, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 51
    .line 52
    invoke-virtual {v0, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    iget-object v3, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 57
    .line 58
    invoke-virtual {v3}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    sget-object v4, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection$a;->b:[I

    .line 63
    .line 64
    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    .line 65
    .line 66
    .line 67
    move-result v3

    .line 68
    aget v3, v4, v3

    .line 69
    .line 70
    if-ne v3, v1, :cond_2

    .line 71
    .line 72
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 73
    .line 74
    .line 75
    move-result-object v3

    .line 76
    sget v4, LlZ0/g;->space_12:I

    .line 77
    .line 78
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 79
    .line 80
    .line 81
    move-result v3

    .line 82
    :goto_2
    move v4, v3

    .line 83
    goto :goto_3

    .line 84
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 85
    .line 86
    .line 87
    move-result-object v3

    .line 88
    sget v4, LlZ0/g;->space_8:I

    .line 89
    .line 90
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 91
    .line 92
    .line 93
    move-result v3

    .line 94
    goto :goto_2

    .line 95
    :goto_3
    iget-object v3, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->b:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 96
    .line 97
    invoke-virtual {v3}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    if-eq v3, v2, :cond_3

    .line 102
    .line 103
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 104
    .line 105
    iget-object v2, v2, Lk41/a;->b:Landroid/widget/FrameLayout;

    .line 106
    .line 107
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    check-cast v2, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 112
    .line 113
    iput v0, v2, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 114
    .line 115
    iput v0, v2, Landroid/view/ViewGroup$MarginLayoutParams;->rightMargin:I

    .line 116
    .line 117
    :cond_3
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 118
    .line 119
    iget-object v0, v0, Lk41/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 120
    .line 121
    invoke-virtual {v0, v1}, Landroid/view/View;->setClipToOutline(Z)V

    .line 122
    .line 123
    .line 124
    new-instance v1, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection$b;

    .line 125
    .line 126
    invoke-direct {v1, p0}, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection$b;-><init>(Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;)V

    .line 127
    .line 128
    .line 129
    invoke-virtual {v0, v1}, Landroid/view/View;->setOutlineProvider(Landroid/view/ViewOutlineProvider;)V

    .line 130
    .line 131
    .line 132
    new-instance v3, LR11/c;

    .line 133
    .line 134
    const/16 v9, 0x14

    .line 135
    .line 136
    const/4 v10, 0x0

    .line 137
    const/4 v6, 0x0

    .line 138
    const/4 v7, 0x0

    .line 139
    const/4 v8, 0x0

    .line 140
    invoke-direct/range {v3 .. v10}, LR11/c;-><init>(IIIIZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 141
    .line 142
    .line 143
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/WebGamesGameCollectionSection;->a:Lk41/a;

    .line 144
    .line 145
    iget-object v0, v0, Lk41/a;->d:Lorg/xbet/uikit/components/views/OptimizedScrollRecyclerView;

    .line 146
    .line 147
    invoke-virtual {v0, v3}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 148
    .line 149
    .line 150
    return-void
.end method
