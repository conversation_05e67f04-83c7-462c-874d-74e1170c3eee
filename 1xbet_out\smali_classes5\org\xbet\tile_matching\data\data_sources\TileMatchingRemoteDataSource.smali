.class public final Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J \u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0080@\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ \u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\rH\u0080@\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J \u0010\u0012\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0011H\u0080@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J \u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0014H\u0080@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0018\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u0006H\u0080@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0018\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u0007\u001a\u00020\u0006H\u0080@\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\u0018\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u0007\u001a\u00020\u0006H\u0080@\u00a2\u0006\u0004\u0008\u001b\u0010\u0018J\u0018\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u0007\u001a\u00020\u0006H\u0080@\u00a2\u0006\u0004\u0008\u001d\u0010\u0018R\u001a\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u001a\u0010%\u001a\u0008\u0012\u0004\u0012\u00020#0\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010!\u00a8\u0006&"
    }
    d2 = {
        "Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lf8/g;)V",
        "",
        "auth",
        "LuT0/b;",
        "request",
        "LvT0/d;",
        "i",
        "(Ljava/lang/String;LuT0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LuT0/d;",
        "LwT0/d;",
        "j",
        "(Ljava/lang/String;LuT0/d;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LuT0/a;",
        "k",
        "(Ljava/lang/String;LuT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LuT0/c;",
        "l",
        "(Ljava/lang/String;LuT0/c;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "e",
        "(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "g",
        "LvT0/c;",
        "f",
        "LwT0/a;",
        "h",
        "Lkotlin/Function0;",
        "LsT0/a;",
        "a",
        "Lkotlin/jvm/functions/Function0;",
        "fruitBlastApi",
        "LsT0/b;",
        "b",
        "gemsOdysseyApi",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "LsT0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "LsT0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;)V
    .locals 1
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/b;

    .line 5
    .line 6
    invoke-direct {v0, p1}, Lorg/xbet/tile_matching/data/data_sources/b;-><init>(Lf8/g;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a:Lkotlin/jvm/functions/Function0;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/c;

    .line 12
    .line 13
    invoke-direct {v0, p1}, Lorg/xbet/tile_matching/data/data_sources/c;-><init>(Lf8/g;)V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 17
    .line 18
    return-void
.end method

.method public static synthetic a(Lf8/g;)LsT0/b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->d(Lf8/g;)LsT0/b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lf8/g;)LsT0/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->c(Lf8/g;)LsT0/a;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lf8/g;)LsT0/a;
    .locals 1

    .line 1
    const-class v0, LsT0/a;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, LsT0/a;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final d(Lf8/g;)LsT0/b;
    .locals 1

    .line 1
    const-class v0, LsT0/b;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, LsT0/b;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public final e(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p2, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    check-cast p2, LsT0/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastActiveGame$1;->label:I

    .line 62
    .line 63
    invoke-interface {p2, p1, v0}, LsT0/a;->a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    if-ne p2, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p2, Lorg/xbet/core/data/j;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final f(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p2, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    check-cast p2, LsT0/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getFruitBlastCoef$1;->label:I

    .line 62
    .line 63
    invoke-interface {p2, p1, v0}, LsT0/a;->c(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p2

    .line 67
    if-ne p2, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p2, Lorg/xbet/core/data/j;

    .line 71
    .line 72
    invoke-virtual {p2}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final g(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v4, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object p2, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 56
    .line 57
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    move-object v1, p2

    .line 62
    check-cast v1, LsT0/b;

    .line 63
    .line 64
    iput v2, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyActiveGame$1;->label:I

    .line 65
    .line 66
    const/4 v3, 0x0

    .line 67
    const/4 v5, 0x2

    .line 68
    const/4 v6, 0x0

    .line 69
    move-object v2, p1

    .line 70
    invoke-static/range {v1 .. v6}, LsT0/b$a;->a(LsT0/b;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p2

    .line 74
    if-ne p2, v0, :cond_3

    .line 75
    .line 76
    return-object v0

    .line 77
    :cond_3
    :goto_2
    check-cast p2, Le8/d;

    .line 78
    .line 79
    invoke-virtual {p2}, Le8/d;->a()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    return-object p1
.end method

.method public final h(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v4, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    iget-object p2, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 56
    .line 57
    invoke-interface {p2}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p2

    .line 61
    move-object v1, p2

    .line 62
    check-cast v1, LsT0/b;

    .line 63
    .line 64
    iput v2, v4, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$getGemsOdysseyCoef$1;->label:I

    .line 65
    .line 66
    const/4 v3, 0x0

    .line 67
    const/4 v5, 0x2

    .line 68
    const/4 v6, 0x0

    .line 69
    move-object v2, p1

    .line 70
    invoke-static/range {v1 .. v6}, LsT0/b$a;->b(LsT0/b;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p2

    .line 74
    if-ne p2, v0, :cond_3

    .line 75
    .line 76
    return-object v0

    .line 77
    :cond_3
    :goto_2
    check-cast p2, Le8/d;

    .line 78
    .line 79
    invoke-virtual {p2}, Le8/d;->a()Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    return-object p1
.end method

.method public final i(Ljava/lang/String;LuT0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LuT0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LuT0/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p3, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p3}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p3

    .line 59
    check-cast p3, LsT0/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetFruitBlastGame$1;->label:I

    .line 62
    .line 63
    invoke-interface {p3, p1, p2, v0}, LsT0/a;->b(Ljava/lang/String;LuT0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    if-ne p3, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p3, Lorg/xbet/core/data/j;

    .line 71
    .line 72
    invoke-virtual {p3}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final j(Ljava/lang/String;LuT0/d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LuT0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LuT0/d;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p3, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p3}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p3

    .line 59
    check-cast p3, LsT0/b;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeBetGemsOdysseyGame$1;->label:I

    .line 62
    .line 63
    invoke-interface {p3, p1, p2, v0}, LsT0/b;->d(Ljava/lang/String;LuT0/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    if-ne p3, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p3, Le8/d;

    .line 71
    .line 72
    invoke-virtual {p3}, Le8/d;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final k(Ljava/lang/String;LuT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LuT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LuT0/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "LvT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p3, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->a:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p3}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p3

    .line 59
    check-cast p3, LsT0/a;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeFruitBlastAction$1;->label:I

    .line 62
    .line 63
    invoke-interface {p3, p1, p2, v0}, LsT0/a;->d(Ljava/lang/String;LuT0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    if-ne p3, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p3, Lorg/xbet/core/data/j;

    .line 71
    .line 72
    invoke-virtual {p3}, Lorg/xbet/core/data/a;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method

.method public final l(Ljava/lang/String;LuT0/c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LuT0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LuT0/c;",
            "Lkotlin/coroutines/e<",
            "-",
            "LwT0/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p3, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p3

    .line 6
    check-cast v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p3}, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;-><init>(Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p3}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p3, p0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource;->b:Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    invoke-interface {p3}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object p3

    .line 59
    check-cast p3, LsT0/b;

    .line 60
    .line 61
    iput v3, v0, Lorg/xbet/tile_matching/data/data_sources/TileMatchingRemoteDataSource$makeGemsOdysseyAction$1;->label:I

    .line 62
    .line 63
    invoke-interface {p3, p1, p2, v0}, LsT0/b;->c(Ljava/lang/String;LuT0/c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p3

    .line 67
    if-ne p3, v1, :cond_3

    .line 68
    .line 69
    return-object v1

    .line 70
    :cond_3
    :goto_1
    check-cast p3, Le8/d;

    .line 71
    .line 72
    invoke-virtual {p3}, Le8/d;->a()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    return-object p1
.end method
