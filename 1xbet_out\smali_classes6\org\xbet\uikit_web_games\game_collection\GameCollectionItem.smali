.class public final Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008a\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u00002\u00020\u0001B;\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008\u0012\u0008\u0008\u0002\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001f\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0019\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJI\u0010#\u001a\u00020\u00102\u0006\u0010\u001b\u001a\u00020\n2\u001a\u0008\u0002\u0010\u001f\u001a\u0014\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u001e0\u001c2\u0016\u0008\u0002\u0010\"\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010!\u0012\u0004\u0012\u00020\u001e0 \u00a2\u0006\u0004\u0008#\u0010$J\u0015\u0010\'\u001a\u00020\u00102\u0006\u0010&\u001a\u00020%\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010*\u001a\u00020)H\u0002\u00a2\u0006\u0004\u0008*\u0010+R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u001b\u00105\u001a\u0002008FX\u0086\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104R\u001b\u0010:\u001a\u0002068FX\u0086\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00102\u001a\u0004\u00088\u00109R\u001d\u0010?\u001a\u0004\u0018\u00010;8FX\u0086\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008<\u00102\u001a\u0004\u0008=\u0010>R\u001b\u0010D\u001a\u00020@8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008A\u00102\u001a\u0004\u0008B\u0010C\u00a8\u0006E"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "gameType",
        "LL11/c;",
        "placeholder",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Ln41/m;",
        "model",
        "setModel",
        "(Ln41/m;)V",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;",
        "type",
        "setType",
        "(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;)V",
        "picture",
        "Lkotlin/Function2;",
        "Landroid/graphics/drawable/Drawable;",
        "",
        "onLoaded",
        "Lkotlin/Function1;",
        "Lcom/bumptech/glide/load/engine/GlideException;",
        "onError",
        "setPicture",
        "(LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V",
        "",
        "label",
        "setLabel",
        "(Ljava/lang/String;)V",
        "Landroid/graphics/drawable/BitmapDrawable;",
        "h",
        "()Landroid/graphics/drawable/BitmapDrawable;",
        "a",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "b",
        "LL11/c;",
        "Lorg/xbet/uikit/components/views/LoadableShapeableImageView;",
        "c",
        "Lkotlin/j;",
        "getPictureImageView",
        "()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;",
        "pictureImageView",
        "Landroid/widget/TextView;",
        "d",
        "getLabelTextView",
        "()Landroid/widget/TextView;",
        "labelTextView",
        "Landroid/view/View;",
        "e",
        "getGradientView",
        "()Landroid/view/View;",
        "gradientView",
        "Lcom/google/android/material/imageview/ShapeableImageView;",
        "f",
        "getTechnicalWorksImageView",
        "()Lcom/google/android/material/imageview/ShapeableImageView;",
        "technicalWorksImageView",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LL11/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/16 v6, 0x1e

    const/4 v7, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/16 v6, 0x1c

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 8
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    const/16 v6, 0x18

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move v3, p3

    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 7
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 8
    iput-object p4, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 9
    iput-object p5, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->b:LL11/c;

    .line 10
    new-instance p2, Ln41/d;

    invoke-direct {p2, p0}, Ln41/d;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->c:Lkotlin/j;

    .line 11
    new-instance p2, Ln41/e;

    invoke-direct {p2, p0}, Ln41/e;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->d:Lkotlin/j;

    .line 12
    new-instance p2, Ln41/f;

    invoke-direct {p2, p0}, Ln41/f;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->e:Lkotlin/j;

    .line 13
    new-instance p2, Ln41/g;

    invoke-direct {p2, p0}, Ln41/g;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->f:Lkotlin/j;

    .line 14
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getLayoutResId()I

    move-result p3

    const/4 p5, 0x1

    invoke-virtual {p2, p3, p0, p5}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 15
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    move-result-object p2

    invoke-virtual {p2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p2

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureWidthRes()I

    move-result p5

    invoke-virtual {p3, p5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p2, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureHeightRes()I

    move-result p5

    invoke-virtual {p3, p5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 18
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    move-result-object p2

    .line 19
    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getShapeAppearanceRes()I

    move-result p3

    const/4 p5, 0x0

    invoke-static {p1, p3, p5}, Lcom/google/android/material/shape/ShapeAppearanceModel;->builder(Landroid/content/Context;II)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object p3

    .line 20
    invoke-virtual {p3}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->build()Lcom/google/android/material/shape/ShapeAppearanceModel;

    move-result-object p3

    .line 21
    invoke-virtual {p2, p3}, Lcom/google/android/material/imageview/ShapeableImageView;->setShapeAppearanceModel(Lcom/google/android/material/shape/ShapeAppearanceModel;)V

    .line 22
    invoke-direct {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getTechnicalWorksImageView()Lcom/google/android/material/imageview/ShapeableImageView;

    move-result-object p2

    invoke-virtual {p2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object p2

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureWidthRes()I

    move-result v0

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p2, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureHeightRes()I

    move-result v0

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getTechnicalWorksImageView()Lcom/google/android/material/imageview/ShapeableImageView;

    move-result-object p2

    .line 26
    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getShapeAppearanceRes()I

    move-result p3

    invoke-static {p1, p3, p5}, Lcom/google/android/material/shape/ShapeAppearanceModel;->builder(Landroid/content/Context;II)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object p1

    .line 27
    invoke-virtual {p1}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->build()Lcom/google/android/material/shape/ShapeAppearanceModel;

    move-result-object p1

    .line 28
    invoke-virtual {p2, p1}, Lcom/google/android/material/imageview/ShapeableImageView;->setShapeAppearanceModel(Lcom/google/android/material/shape/ShapeAppearanceModel;)V

    .line 29
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getLabelTextView()Landroid/widget/TextView;

    move-result-object p1

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getTextStyleRes()I

    move-result p2

    invoke-static {p1, p2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 30
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    invoke-virtual {p4}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureWidthRes()I

    move-result p2

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    .line 31
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getLabelTextView()Landroid/widget/TextView;

    move-result-object p2

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setWidth(I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 1

    and-int/lit8 p7, p6, 0x2

    const/4 v0, 0x0

    if-eqz p7, :cond_0

    move-object p2, v0

    :cond_0
    and-int/lit8 p7, p6, 0x4

    if-eqz p7, :cond_1

    const/4 p3, 0x0

    :cond_1
    and-int/lit8 p7, p6, 0x8

    if-eqz p7, :cond_2

    .line 4
    sget-object p4, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    :cond_2
    and-int/lit8 p6, p6, 0x10

    if-eqz p6, :cond_3

    .line 5
    new-instance p5, LL11/c$b;

    invoke-direct {p5, v0}, LL11/c$b;-><init>(Landroid/graphics/drawable/Drawable;)V

    :cond_3
    move-object p6, p5

    move-object p5, p4

    move p4, p3

    move-object p3, p2

    move-object p2, p1

    move-object p1, p0

    .line 6
    invoke-direct/range {p1 .. p6}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lorg/xbet/uikit/components/views/LoadableShapeableImageView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->k(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->l(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Z

    move-result p0

    return p0
.end method

.method public static synthetic c(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lcom/google/android/material/imageview/ShapeableImageView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->o(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lcom/google/android/material/imageview/ShapeableImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->i(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/view/View;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->m(Lcom/bumptech/glide/load/engine/GlideException;)Z

    move-result p0

    return p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/widget/TextView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->j(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/widget/TextView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lkotlin/jvm/functions/Function2;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;Landroid/graphics/drawable/Drawable;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->n(Lkotlin/jvm/functions/Function2;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;Landroid/graphics/drawable/Drawable;)Z

    move-result p0

    return p0
.end method

.method private final getTechnicalWorksImageView()Lcom/google/android/material/imageview/ShapeableImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->f:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/imageview/ShapeableImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final i(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/view/View;
    .locals 1

    .line 1
    sget v0, Lj41/d;->uikitBannerGradientView:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    return-object p0
.end method

.method public static final j(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/widget/TextView;
    .locals 1

    .line 1
    sget v0, Lj41/d;->uikitBannerLabelTextView:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Landroid/widget/TextView;

    .line 8
    .line 9
    return-object p0
.end method

.method public static final k(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lorg/xbet/uikit/components/views/LoadableShapeableImageView;
    .locals 1

    .line 1
    sget v0, Lj41/d;->uikitBannerPictureImageView:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 8
    .line 9
    return-object p0
.end method

.method public static final l(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method public static final m(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method public static final n(Lkotlin/jvm/functions/Function2;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;Landroid/graphics/drawable/Drawable;)Z
    .locals 0

    .line 1
    invoke-interface {p0, p2, p1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, Ljava/lang/Boolean;

    .line 6
    .line 7
    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    return p0
.end method

.method public static final o(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Lcom/google/android/material/imageview/ShapeableImageView;
    .locals 1

    .line 1
    sget v0, Lj41/d;->uikitBannerTechicalWorksImageView:I

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lcom/google/android/material/imageview/ShapeableImageView;

    .line 8
    .line 9
    return-object p0
.end method

.method public static synthetic setPicture$default(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p5, p4, 0x2

    .line 2
    .line 3
    if-eqz p5, :cond_0

    .line 4
    .line 5
    new-instance p2, Ln41/i;

    .line 6
    .line 7
    invoke-direct {p2}, Ln41/i;-><init>()V

    .line 8
    .line 9
    .line 10
    :cond_0
    and-int/lit8 p4, p4, 0x4

    .line 11
    .line 12
    if-eqz p4, :cond_1

    .line 13
    .line 14
    new-instance p3, Ln41/j;

    .line 15
    .line 16
    invoke-direct {p3}, Ln41/j;-><init>()V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setPicture(LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method


# virtual methods
.method public final getGradientView()Landroid/view/View;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/view/View;

    .line 8
    .line 9
    return-object v0
.end method

.method public final getLabelTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/widget/TextView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final h()Landroid/graphics/drawable/BitmapDrawable;
    .locals 8

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 6
    .line 7
    invoke-virtual {v1}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureHeightRes()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 20
    .line 21
    invoke-virtual {v2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getPictureWidthRes()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    new-instance v2, Landroid/graphics/drawable/ColorDrawable;

    .line 30
    .line 31
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    sget v4, LlZ0/d;->uikitBackgroundLight60:I

    .line 36
    .line 37
    const/4 v5, 0x0

    .line 38
    const/4 v6, 0x2

    .line 39
    invoke-static {v3, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 40
    .line 41
    .line 42
    move-result v3

    .line 43
    invoke-direct {v2, v3}, Landroid/graphics/drawable/ColorDrawable;-><init>(I)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    sget v4, LlZ0/h;->ic_glyph_technical_works:I

    .line 51
    .line 52
    invoke-static {v3, v4}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 53
    .line 54
    .line 55
    move-result-object v3

    .line 56
    sget-object v4, Landroid/graphics/Bitmap$Config;->ARGB_8888:Landroid/graphics/Bitmap$Config;

    .line 57
    .line 58
    invoke-static {v1, v0, v4}, Landroid/graphics/Bitmap;->createBitmap(IILandroid/graphics/Bitmap$Config;)Landroid/graphics/Bitmap;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    new-instance v5, Landroid/graphics/Canvas;

    .line 63
    .line 64
    invoke-direct {v5, v4}, Landroid/graphics/Canvas;-><init>(Landroid/graphics/Bitmap;)V

    .line 65
    .line 66
    .line 67
    const/4 v7, 0x0

    .line 68
    invoke-virtual {v2, v7, v7, v1, v0}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {v2, v5}, Landroid/graphics/drawable/ColorDrawable;->draw(Landroid/graphics/Canvas;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    iget-object v7, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 79
    .line 80
    invoke-virtual {v7}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getIconSizeRes()I

    .line 81
    .line 82
    .line 83
    move-result v7

    .line 84
    invoke-virtual {v2, v7}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 85
    .line 86
    .line 87
    move-result v2

    .line 88
    sub-int/2addr v1, v2

    .line 89
    div-int/2addr v1, v6

    .line 90
    sub-int/2addr v0, v2

    .line 91
    div-int/2addr v0, v6

    .line 92
    if-eqz v3, :cond_0

    .line 93
    .line 94
    add-int v6, v1, v2

    .line 95
    .line 96
    add-int/2addr v2, v0

    .line 97
    invoke-virtual {v3, v1, v0, v6, v2}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 98
    .line 99
    .line 100
    :cond_0
    if-eqz v3, :cond_1

    .line 101
    .line 102
    invoke-virtual {v3, v5}, Landroid/graphics/drawable/Drawable;->draw(Landroid/graphics/Canvas;)V

    .line 103
    .line 104
    .line 105
    :cond_1
    new-instance v0, Landroid/graphics/drawable/BitmapDrawable;

    .line 106
    .line 107
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    invoke-direct {v0, v1, v4}, Landroid/graphics/drawable/BitmapDrawable;-><init>(Landroid/content/res/Resources;Landroid/graphics/Bitmap;)V

    .line 112
    .line 113
    .line 114
    return-object v0
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object p2, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 6
    .line 7
    invoke-virtual {p2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getWidthRes()I

    .line 8
    .line 9
    .line 10
    move-result p2

    .line 11
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    const/high16 p2, 0x40000000    # 2.0f

    .line 16
    .line 17
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 26
    .line 27
    invoke-virtual {v1}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getHeightRes()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 36
    .line 37
    .line 38
    move-result p2

    .line 39
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public final setLabel(Ljava/lang/String;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getLabelTextView()Landroid/widget/TextView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getGradientView()Landroid/view/View;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    if-eqz v0, :cond_2

    .line 13
    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 15
    .line 16
    invoke-virtual {v1}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->getHasGradient()Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    const/4 v2, 0x0

    .line 21
    if-eqz v1, :cond_0

    .line 22
    .line 23
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    if-lez p1, :cond_0

    .line 28
    .line 29
    const/4 p1, 0x1

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const/4 p1, 0x0

    .line 32
    :goto_0
    if-eqz p1, :cond_1

    .line 33
    .line 34
    goto :goto_1

    .line 35
    :cond_1
    const/16 v2, 0x8

    .line 36
    .line 37
    :goto_1
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 38
    .line 39
    .line 40
    :cond_2
    return-void
.end method

.method public final setModel(Ln41/m;)V
    .locals 3
    .param p1    # Ln41/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Ln41/m;->j()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setType(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, Ln41/m;->i()LL11/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p1}, Ln41/m;->h()Lkotlin/jvm/functions/Function2;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {p1}, Ln41/m;->g()Lkotlin/jvm/functions/Function1;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setPicture(LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p1}, Ln41/m;->f()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setLabel(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final setPicture(LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V
    .locals 3
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL11/c;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "-",
            "Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/bumptech/glide/load/engine/GlideException;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    instance-of v0, p1, LL11/c$d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->b:LL11/c;

    .line 10
    .line 11
    new-instance v2, Ln41/h;

    .line 12
    .line 13
    invoke-direct {v2, p2, p0}, Ln41/h;-><init>(Lkotlin/jvm/functions/Function2;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, p1, v1, v2, p3}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->G(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 17
    .line 18
    .line 19
    return-void

    .line 20
    :cond_0
    instance-of p2, p1, LL11/c$c;

    .line 21
    .line 22
    if-eqz p2, :cond_1

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 29
    .line 30
    .line 31
    move-result-object p3

    .line 32
    check-cast p1, LL11/c$c;

    .line 33
    .line 34
    invoke-virtual {p1}, LL11/c$c;->h()I

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    invoke-static {p3, p1}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-virtual {p2, p1}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 43
    .line 44
    .line 45
    :cond_1
    return-void
.end method

.method public final setType(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;)V
    .locals 7
    .param p1    # Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getTechnicalWorksImageView()Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;->TechnicalWorks:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x1

    .line 9
    if-ne p1, v1, :cond_0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v1, 0x0

    .line 14
    :goto_0
    if-eqz v1, :cond_1

    .line 15
    .line 16
    goto :goto_1

    .line 17
    :cond_1
    const/16 v2, 0x8

    .line 18
    .line 19
    :goto_1
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem$a;->a:[I

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    aget p1, v0, p1

    .line 29
    .line 30
    if-eq p1, v3, :cond_3

    .line 31
    .line 32
    const/4 v0, 0x2

    .line 33
    if-eq p1, v0, :cond_2

    .line 34
    .line 35
    return-void

    .line 36
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getPictureImageView()Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    .line 41
    .line 42
    sget-object v2, Landroid/graphics/drawable/GradientDrawable$Orientation;->TL_BR:Landroid/graphics/drawable/GradientDrawable$Orientation;

    .line 43
    .line 44
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    sget v4, LlZ0/d;->uikitPromoCardGradientEnd:I

    .line 49
    .line 50
    const/4 v5, 0x0

    .line 51
    invoke-static {v3, v4, v5, v0, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 52
    .line 53
    .line 54
    move-result v3

    .line 55
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 56
    .line 57
    .line 58
    move-result-object v4

    .line 59
    sget v6, LlZ0/d;->uikitPromoCardGradientStart:I

    .line 60
    .line 61
    invoke-static {v4, v6, v5, v0, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 62
    .line 63
    .line 64
    move-result v0

    .line 65
    filled-new-array {v3, v0}, [I

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    invoke-direct {v1, v2, v0}, Landroid/graphics/drawable/GradientDrawable;-><init>(Landroid/graphics/drawable/GradientDrawable$Orientation;[I)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {p1, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 73
    .line 74
    .line 75
    return-void

    .line 76
    :cond_3
    invoke-direct {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->getTechnicalWorksImageView()Lcom/google/android/material/imageview/ShapeableImageView;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->h()Landroid/graphics/drawable/BitmapDrawable;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 85
    .line 86
    .line 87
    return-void
.end method
