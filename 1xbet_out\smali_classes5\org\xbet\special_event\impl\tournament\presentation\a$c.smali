.class public final Lorg/xbet/special_event/impl/tournament/presentation/a$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/special_event/impl/tournament/presentation/a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/special_event/impl/tournament/presentation/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0007\u0008\u0087@\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0010\u0010\n\u001a\u00020\tH\u00d6\u0001\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001a\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u00d6\u0003\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0011\u0010\u0012\u001a\u0004\u0008\u0013\u0010\u0014\u0088\u0001\u0003\u0092\u0001\u00020\u0002\u00a8\u0006\u0015"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/tournament/presentation/a$c;",
        "Lorg/xbet/special_event/impl/tournament/presentation/a;",
        "Landroid/content/Intent;",
        "value",
        "b",
        "(Landroid/content/Intent;)Landroid/content/Intent;",
        "",
        "e",
        "(Landroid/content/Intent;)Ljava/lang/String;",
        "",
        "d",
        "(Landroid/content/Intent;)I",
        "",
        "other",
        "",
        "c",
        "(Landroid/content/Intent;Ljava/lang/Object;)Z",
        "a",
        "Landroid/content/Intent;",
        "getValue",
        "()Landroid/content/Intent;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/content/Intent;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public synthetic constructor <init>(Landroid/content/Intent;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->a:Landroid/content/Intent;

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a(Landroid/content/Intent;)Lorg/xbet/special_event/impl/tournament/presentation/a$c;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;

    invoke-direct {v0, p0}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;-><init>(Landroid/content/Intent;)V

    return-object v0
.end method

.method public static b(Landroid/content/Intent;)Landroid/content/Intent;
    .locals 0
    .param p0    # Landroid/content/Intent;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    return-object p0
.end method

.method public static c(Landroid/content/Intent;Ljava/lang/Object;)Z
    .locals 2

    .line 1
    instance-of v0, p1, Lorg/xbet/special_event/impl/tournament/presentation/a$c;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/a$c;

    invoke-virtual {p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->f()Landroid/content/Intent;

    move-result-object p1

    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_1

    return v1

    :cond_1
    const/4 p0, 0x1

    return p0
.end method

.method public static d(Landroid/content/Intent;)I
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Object;->hashCode()I

    move-result p0

    return p0
.end method

.method public static e(Landroid/content/Intent;)Ljava/lang/String;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "OpenIntent(value="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p0, ")"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 1

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->a:Landroid/content/Intent;

    invoke-static {v0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->c(Landroid/content/Intent;Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public final synthetic f()Landroid/content/Intent;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->a:Landroid/content/Intent;

    return-object v0
.end method

.method public hashCode()I
    .locals 1

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->a:Landroid/content/Intent;

    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->d(Landroid/content/Intent;)I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->a:Landroid/content/Intent;

    invoke-static {v0}, Lorg/xbet/special_event/impl/tournament/presentation/a$c;->e(Landroid/content/Intent;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
