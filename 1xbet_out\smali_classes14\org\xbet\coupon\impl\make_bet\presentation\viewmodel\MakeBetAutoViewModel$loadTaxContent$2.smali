.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$loadTaxContent$2"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x32e,
        0x33b,
        0x338
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->y5(Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

.field final synthetic $coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

.field final synthetic $couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

.field D$0:D

.field D$1:D

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;",
            "Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;",
            "Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iput-object p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    iput-object p3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    iput-object p4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;

    iget-object v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iget-object v2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    iget-object v3, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    iget-object v4, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 23

    .line 1
    move-object/from16 v8, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v9

    .line 7
    iget v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->label:I

    .line 8
    .line 9
    const/4 v10, 0x3

    .line 10
    const/4 v11, 0x2

    .line 11
    const/4 v12, 0x1

    .line 12
    if-eqz v0, :cond_3

    .line 13
    .line 14
    if-eq v0, v12, :cond_2

    .line 15
    .line 16
    if-eq v0, v11, :cond_1

    .line 17
    .line 18
    if-ne v0, v10, :cond_0

    .line 19
    .line 20
    iget-wide v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$0:D

    .line 21
    .line 22
    iget-object v2, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v2, LV50/a;

    .line 25
    .line 26
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    move-wide v13, v0

    .line 30
    move-object/from16 v0, p1

    .line 31
    .line 32
    goto/16 :goto_4

    .line 33
    .line 34
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 35
    .line 36
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 37
    .line 38
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v0

    .line 42
    :cond_1
    iget-wide v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$1:D

    .line 43
    .line 44
    iget-wide v2, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$0:D

    .line 45
    .line 46
    iget-object v4, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$2:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v4, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 49
    .line 50
    iget-object v5, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$1:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v5, Lorg/xbet/coupon/impl/make_bet/domain/scenario/GetTaxModelScenario;

    .line 53
    .line 54
    iget-object v6, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast v6, LV50/a;

    .line 57
    .line 58
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    move-wide v13, v2

    .line 62
    move-object v11, v6

    .line 63
    move-wide v1, v0

    .line 64
    move-object/from16 v0, p1

    .line 65
    .line 66
    :goto_0
    move-object v3, v4

    .line 67
    goto/16 :goto_2

    .line 68
    .line 69
    :cond_2
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$1:Ljava/lang/Object;

    .line 70
    .line 71
    check-cast v0, LW50/a;

    .line 72
    .line 73
    iget-object v1, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 74
    .line 75
    check-cast v1, LV50/a;

    .line 76
    .line 77
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 78
    .line 79
    .line 80
    move-object v13, v0

    .line 81
    move-object/from16 v0, p1

    .line 82
    .line 83
    goto :goto_1

    .line 84
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 85
    .line 86
    .line 87
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 88
    .line 89
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->J4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)V

    .line 90
    .line 91
    .line 92
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 93
    .line 94
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->Z3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)LW50/b;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-interface {v0}, LW50/b;->invoke()LV50/a;

    .line 99
    .line 100
    .line 101
    move-result-object v13

    .line 102
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 103
    .line 104
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->L3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)LW50/a;

    .line 105
    .line 106
    .line 107
    move-result-object v14

    .line 108
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 109
    .line 110
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->M3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/CalculatePossiblePayoutUseCase;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    iget-object v1, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 115
    .line 116
    invoke-virtual {v1}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->e()D

    .line 117
    .line 118
    .line 119
    move-result-wide v1

    .line 120
    iget-object v3, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 121
    .line 122
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->W3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;

    .line 123
    .line 124
    .line 125
    move-result-object v3

    .line 126
    invoke-virtual {v3}, Lorg/xbet/coupon/impl/coupon/domain/usecases/y0;->a()D

    .line 127
    .line 128
    .line 129
    move-result-wide v3

    .line 130
    iget-object v5, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 131
    .line 132
    invoke-static {v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->b4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/m1;

    .line 133
    .line 134
    .line 135
    move-result-object v5

    .line 136
    invoke-virtual {v5}, Lorg/xbet/coupon/impl/coupon/domain/usecases/m1;->a()D

    .line 137
    .line 138
    .line 139
    move-result-wide v5

    .line 140
    iget-object v7, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 141
    .line 142
    invoke-static {v7}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->X3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/A0;

    .line 143
    .line 144
    .line 145
    move-result-object v7

    .line 146
    invoke-virtual {v7}, Lorg/xbet/coupon/impl/coupon/domain/usecases/A0;->a()LSw/h;

    .line 147
    .line 148
    .line 149
    move-result-object v7

    .line 150
    invoke-virtual {v7}, LSw/h;->o()Z

    .line 151
    .line 152
    .line 153
    move-result v7

    .line 154
    iput-object v13, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 155
    .line 156
    iput-object v14, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$1:Ljava/lang/Object;

    .line 157
    .line 158
    iput v12, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->label:I

    .line 159
    .line 160
    invoke-virtual/range {v0 .. v8}, Lorg/xbet/coupon/impl/coupon/domain/usecases/CalculatePossiblePayoutUseCase;->f(DDDZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    if-ne v0, v9, :cond_4

    .line 165
    .line 166
    goto/16 :goto_3

    .line 167
    .line 168
    :cond_4
    move-object v1, v13

    .line 169
    move-object v13, v14

    .line 170
    :goto_1
    check-cast v0, Ljava/lang/Number;

    .line 171
    .line 172
    invoke-virtual {v0}, Ljava/lang/Number;->doubleValue()D

    .line 173
    .line 174
    .line 175
    move-result-wide v14

    .line 176
    invoke-virtual {v1}, LV50/a;->d()I

    .line 177
    .line 178
    .line 179
    move-result v16

    .line 180
    invoke-virtual {v1}, LV50/a;->c()D

    .line 181
    .line 182
    .line 183
    move-result-wide v17

    .line 184
    invoke-virtual {v1}, LV50/a;->b()D

    .line 185
    .line 186
    .line 187
    move-result-wide v19

    .line 188
    invoke-interface/range {v13 .. v20}, LW50/a;->a(DIDD)D

    .line 189
    .line 190
    .line 191
    move-result-wide v2

    .line 192
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 193
    .line 194
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->d4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/make_bet/domain/scenario/GetTaxModelScenario;

    .line 195
    .line 196
    .line 197
    move-result-object v5

    .line 198
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 199
    .line 200
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->e()D

    .line 201
    .line 202
    .line 203
    move-result-wide v6

    .line 204
    iget-object v4, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 205
    .line 206
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 207
    .line 208
    iput-object v1, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 209
    .line 210
    iput-object v5, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$1:Ljava/lang/Object;

    .line 211
    .line 212
    iput-object v4, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$2:Ljava/lang/Object;

    .line 213
    .line 214
    iput-wide v2, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$0:D

    .line 215
    .line 216
    iput-wide v6, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$1:D

    .line 217
    .line 218
    iput v11, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->label:I

    .line 219
    .line 220
    invoke-static {v0, v8}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->o4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 221
    .line 222
    .line 223
    move-result-object v0

    .line 224
    if-ne v0, v9, :cond_5

    .line 225
    .line 226
    goto :goto_3

    .line 227
    :cond_5
    move-object v11, v1

    .line 228
    move-wide v13, v2

    .line 229
    move-wide v1, v6

    .line 230
    goto/16 :goto_0

    .line 231
    .line 232
    :goto_2
    check-cast v0, Lorg/xbet/balance/model/BalanceModel;

    .line 233
    .line 234
    invoke-virtual {v0}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyId()J

    .line 235
    .line 236
    .line 237
    move-result-wide v6

    .line 238
    iget-object v0, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 239
    .line 240
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;->c()Ljava/math/BigDecimal;

    .line 241
    .line 242
    .line 243
    move-result-object v0

    .line 244
    invoke-virtual {v0}, Ljava/math/BigDecimal;->doubleValue()D

    .line 245
    .line 246
    .line 247
    move-result-wide v15

    .line 248
    invoke-static/range {v15 .. v16}, LHc/a;->c(D)Ljava/lang/Double;

    .line 249
    .line 250
    .line 251
    move-result-object v0

    .line 252
    move-wide/from16 v21, v6

    .line 253
    .line 254
    move-object v6, v0

    .line 255
    move-object v0, v5

    .line 256
    move-wide/from16 v4, v21

    .line 257
    .line 258
    sget-object v7, Lorg/xbet/betting/core/coupon/models/CoefTypeModel;->POSSIBLE_PAYOUT:Lorg/xbet/betting/core/coupon/models/CoefTypeModel;

    .line 259
    .line 260
    iput-object v11, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$0:Ljava/lang/Object;

    .line 261
    .line 262
    const/4 v15, 0x0

    .line 263
    iput-object v15, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$1:Ljava/lang/Object;

    .line 264
    .line 265
    iput-object v15, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->L$2:Ljava/lang/Object;

    .line 266
    .line 267
    iput-wide v13, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->D$0:D

    .line 268
    .line 269
    iput v10, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->label:I

    .line 270
    .line 271
    invoke-virtual/range {v0 .. v8}, Lorg/xbet/coupon/impl/make_bet/domain/scenario/GetTaxModelScenario;->b(DLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;JLjava/lang/Double;Lorg/xbet/betting/core/coupon/models/CoefTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 272
    .line 273
    .line 274
    move-result-object v0

    .line 275
    if-ne v0, v9, :cond_6

    .line 276
    .line 277
    :goto_3
    return-object v9

    .line 278
    :cond_6
    move-object v2, v11

    .line 279
    :goto_4
    check-cast v0, Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;

    .line 280
    .line 281
    invoke-virtual {v2}, LV50/a;->d()I

    .line 282
    .line 283
    .line 284
    move-result v1

    .line 285
    invoke-static {v0, v13, v14, v1, v12}, Loo/a;->c(Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;DIZ)Lpo/a;

    .line 286
    .line 287
    .line 288
    move-result-object v1

    .line 289
    iget-object v2, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 290
    .line 291
    iget-object v3, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 292
    .line 293
    invoke-static {v2, v1, v0, v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->I4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lpo/a;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;)V

    .line 294
    .line 295
    .line 296
    iget-object v1, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 297
    .line 298
    iget-object v2, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$betSumStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 299
    .line 300
    iget-object v3, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$coefStepInputUiModel:Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 301
    .line 302
    iget-object v4, v8, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$loadTaxContent$2;->$couponTypeModel:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 303
    .line 304
    invoke-static {v1, v2, v3, v0, v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->D3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;Lorg/xbet/betting/core/tax/domain/models/GetTaxModel;Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;)V

    .line 305
    .line 306
    .line 307
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 308
    .line 309
    return-object v0
.end method
