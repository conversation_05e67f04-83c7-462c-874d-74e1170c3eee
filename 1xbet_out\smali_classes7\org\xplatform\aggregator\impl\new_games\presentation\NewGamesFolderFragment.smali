.class public final Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0099\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0010%\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007*\u0001\u001c\u0008\u0000\u0018\u0000 o2\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u00020\u0003:\u0001pB\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0017\u0010\t\u001a\u00020\u00082\u0006\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0005J\u0017\u0010\u0019\u001a\u00020\u00082\u0006\u0010\u0018\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u000eJ\u0017\u0010\u001a\u001a\u00020\u00082\u0006\u0010\u0018\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u000eJ\u000f\u0010\u001b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0005J\u000f\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0019\u0010!\u001a\u00020\u00082\u0008\u0010 \u001a\u0004\u0018\u00010\u001fH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008#\u0010\u0005J\u000f\u0010%\u001a\u00020$H\u0010\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010(\u001a\u00020\'H\u0014\u00a2\u0006\u0004\u0008(\u0010)J\u000f\u0010*\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0005J\u000f\u0010+\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008+\u0010\u0005J\u0019\u0010,\u001a\u00020\u00082\u0008\u0010 \u001a\u0004\u0018\u00010\u001fH\u0014\u00a2\u0006\u0004\u0008,\u0010\"J\u000f\u0010-\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008-\u0010\u0005J\u000f\u0010.\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008.\u0010\u0005J\u000f\u0010/\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008/\u0010\u0005R\u001b\u00105\u001a\u0002008BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u00104R\"\u0010=\u001a\u0002068\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<R+\u0010F\u001a\u00020>2\u0006\u0010?\u001a\u00020>8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008@\u0010A\u001a\u0004\u0008B\u0010C\"\u0004\u0008D\u0010ER+\u0010L\u001a\u00020\u000b2\u0006\u0010?\u001a\u00020\u000b8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008G\u0010H\u001a\u0004\u0008I\u0010J\"\u0004\u0008K\u0010\u000eR+\u0010P\u001a\u00020\u000b2\u0006\u0010?\u001a\u00020\u000b8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008M\u0010H\u001a\u0004\u0008N\u0010J\"\u0004\u0008O\u0010\u000eR\"\u0010V\u001a\u0010\u0012\u0004\u0012\u00020R\u0012\u0006\u0012\u0004\u0018\u00010S0Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u001b\u0010\\\u001a\u00020W8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008X\u0010Y\u001a\u0004\u0008Z\u0010[R\u001b\u0010`\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008]\u0010Y\u001a\u0004\u0008^\u0010_R\u0014\u0010c\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u001a\u0010i\u001a\u00020d8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008e\u0010f\u001a\u0004\u0008g\u0010hR\u001b\u0010n\u001a\u00020j8VX\u0096\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008k\u0010Y\u001a\u0004\u0008l\u0010m\u00a8\u0006q"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
        "",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;",
        "state",
        "",
        "N3",
        "(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;)V",
        "",
        "set",
        "Z3",
        "(Z)V",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
        "event",
        "R3",
        "(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;)V",
        "",
        "deeplink",
        "X3",
        "(Ljava/lang/String;)V",
        "c4",
        "show",
        "e4",
        "d4",
        "O3",
        "org/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b",
        "E3",
        "()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "u2",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "onResume",
        "onPause",
        "t2",
        "U2",
        "v2",
        "onDestroyView",
        "LS91/U;",
        "o0",
        "LRc/c;",
        "J3",
        "()LS91/U;",
        "binding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "b1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "M3",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "<set-?>",
        "k1",
        "LeX0/h;",
        "H3",
        "()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;",
        "Y3",
        "(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V",
        "aggregatorScreenModel",
        "v1",
        "LeX0/a;",
        "K3",
        "()Z",
        "b4",
        "fromSearch",
        "x1",
        "getBundleVirtual",
        "a4",
        "bundleVirtual",
        "",
        "",
        "Landroid/os/Parcelable;",
        "y1",
        "Ljava/util/Map;",
        "bannerStates",
        "Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;",
        "F1",
        "Lkotlin/j;",
        "I3",
        "()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;",
        "balanceViewModel",
        "H1",
        "L3",
        "()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;",
        "viewModel",
        "I1",
        "Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;",
        "gamesPagerAdapterObserver",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "P1",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "S1",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "V1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final V1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic b2:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final F1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b1:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final k1:LeX0/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Landroid/os/Parcelable;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 8

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 4
    .line 5
    const-string v2, "binding"

    .line 6
    .line 7
    const-string v3, "getBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentGamesFolderBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "aggregatorScreenModel"

    .line 20
    .line 21
    const-string v5, "getAggregatorScreenModel()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "fromSearch"

    .line 33
    .line 34
    const-string v6, "getFromSearch()Z"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "bundleVirtual"

    .line 46
    .line 47
    const-string v7, "getBundleVirtual()Z"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    const/4 v5, 0x4

    .line 57
    new-array v5, v5, [Lkotlin/reflect/m;

    .line 58
    .line 59
    aput-object v0, v5, v4

    .line 60
    .line 61
    const/4 v0, 0x1

    .line 62
    aput-object v2, v5, v0

    .line 63
    .line 64
    const/4 v0, 0x2

    .line 65
    aput-object v3, v5, v0

    .line 66
    .line 67
    const/4 v0, 0x3

    .line 68
    aput-object v1, v5, v0

    .line 69
    .line 70
    sput-object v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 71
    .line 72
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    sput-object v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->V1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$a;

    .line 79
    .line 80
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lu91/c;->fragment_games_folder:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$binding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->o0:LRc/c;

    .line 13
    .line 14
    new-instance v0, LeX0/h;

    .line 15
    .line 16
    const-string v1, "AGGREGATOR_SCREEN_ITEM"

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    const/4 v3, 0x2

    .line 20
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/h;-><init>(Ljava/lang/String;Landroid/os/Parcelable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->k1:LeX0/h;

    .line 24
    .line 25
    new-instance v0, LeX0/a;

    .line 26
    .line 27
    const-string v1, "FROM_AGGREGATOR_SEARCH_ITEM"

    .line 28
    .line 29
    const/4 v4, 0x0

    .line 30
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->v1:LeX0/a;

    .line 34
    .line 35
    new-instance v0, LeX0/a;

    .line 36
    .line 37
    const-string v1, "VIRTUAL_CATEGORY"

    .line 38
    .line 39
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->x1:LeX0/a;

    .line 43
    .line 44
    new-instance v0, Ljava/util/LinkedHashMap;

    .line 45
    .line 46
    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->y1:Ljava/util/Map;

    .line 50
    .line 51
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/e;

    .line 52
    .line 53
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/e;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 54
    .line 55
    .line 56
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$1;

    .line 57
    .line 58
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 59
    .line 60
    .line 61
    sget-object v3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 62
    .line 63
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$2;

    .line 64
    .line 65
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 66
    .line 67
    .line 68
    invoke-static {v3, v4}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    const-class v4, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 73
    .line 74
    invoke-static {v4}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 75
    .line 76
    .line 77
    move-result-object v4

    .line 78
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$3;

    .line 79
    .line 80
    invoke-direct {v5, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 81
    .line 82
    .line 83
    new-instance v6, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$4;

    .line 84
    .line 85
    invoke-direct {v6, v2, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 86
    .line 87
    .line 88
    invoke-static {p0, v4, v5, v6, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->F1:Lkotlin/j;

    .line 93
    .line 94
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/f;

    .line 95
    .line 96
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/f;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 97
    .line 98
    .line 99
    new-instance v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$6;

    .line 100
    .line 101
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$6;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 102
    .line 103
    .line 104
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$7;

    .line 105
    .line 106
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$7;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 107
    .line 108
    .line 109
    invoke-static {v3, v4}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    const-class v3, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 114
    .line 115
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 116
    .line 117
    .line 118
    move-result-object v3

    .line 119
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$8;

    .line 120
    .line 121
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$8;-><init>(Lkotlin/j;)V

    .line 122
    .line 123
    .line 124
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$9;

    .line 125
    .line 126
    invoke-direct {v5, v2, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$special$$inlined$viewModels$default$9;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 127
    .line 128
    .line 129
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->H1:Lkotlin/j;

    .line 134
    .line 135
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->E3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->I1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;

    .line 140
    .line 141
    sget-object v0, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->AGGREGATOR_LIVE:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 142
    .line 143
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->P1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 144
    .line 145
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/g;

    .line 146
    .line 147
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/g;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 148
    .line 149
    .line 150
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 151
    .line 152
    .line 153
    move-result-object v0

    .line 154
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->S1:Lkotlin/j;

    .line 155
    .line 156
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->e3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->e4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final F3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->M3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final G3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 4

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->H3()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->d()J

    .line 6
    .line 7
    .line 8
    move-result-wide v0

    .line 9
    sget-object p0, Lorg/xplatform/aggregator/api/model/PartitionType;->TV_BET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    cmp-long p0, v0, v2

    .line 16
    .line 17
    if-nez p0, :cond_0

    .line 18
    .line 19
    sget-object p0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->AggregatorTvBets:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 20
    .line 21
    return-object p0

    .line 22
    :cond_0
    sget-object p0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->UNKNOWN:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 23
    .line 24
    return-object p0
.end method

.method private final H3()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->k1:LeX0/h;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/h;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Landroid/os/Parcelable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 13
    .line 14
    return-object v0
.end method

.method private final I3()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->F1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method private final O3()V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->X4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iget-object v1, v1, LS91/U;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 14
    .line 15
    sget v2, Lpb/k;->update_again_after:I

    .line 16
    .line 17
    const-wide/16 v3, 0x2710

    .line 18
    .line 19
    invoke-virtual {v1, v0, v2, v3, v4}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final P3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    invoke-virtual {v0, p1, p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->T3(Ljava/lang/String;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final Q3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, LXW0/d;->h(Landroidx/fragment/app/Fragment;)Ljava/lang/Boolean;

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final R3(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$c;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$c;->a()Landroid/content/Intent;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->startActivity(Landroid/content/Intent;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$a;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    check-cast p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$a;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$a;->a()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->X3(Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_1
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b$b;

    .line 30
    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->c4()V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 38
    .line 39
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 40
    .line 41
    .line 42
    throw p1
.end method

.method public static final S3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->p5(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final T3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 6
    .line 7
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Landroidx/paging/u;->d()Landroidx/paging/s;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 16
    .line 17
    const/4 v2, 0x0

    .line 18
    if-eqz v1, :cond_0

    .line 19
    .line 20
    check-cast v0, Landroidx/paging/s$a;

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    move-object v0, v2

    .line 24
    :goto_0
    if-nez v0, :cond_5

    .line 25
    .line 26
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {v0}, Landroidx/paging/u;->e()Landroidx/paging/s;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 35
    .line 36
    if-eqz v1, :cond_1

    .line 37
    .line 38
    check-cast v0, Landroidx/paging/s$a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    if-nez v0, :cond_5

    .line 43
    .line 44
    invoke-virtual {p2}, Landroidx/paging/f;->e()Landroidx/paging/u;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-virtual {v0}, Landroidx/paging/u;->f()Landroidx/paging/s;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 53
    .line 54
    if-eqz v1, :cond_2

    .line 55
    .line 56
    check-cast v0, Landroidx/paging/s$a;

    .line 57
    .line 58
    goto :goto_2

    .line 59
    :cond_2
    move-object v0, v2

    .line 60
    :goto_2
    if-nez v0, :cond_5

    .line 61
    .line 62
    invoke-virtual {p2}, Landroidx/paging/f;->a()Landroidx/paging/s;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 67
    .line 68
    if-eqz v1, :cond_3

    .line 69
    .line 70
    check-cast v0, Landroidx/paging/s$a;

    .line 71
    .line 72
    goto :goto_3

    .line 73
    :cond_3
    move-object v0, v2

    .line 74
    :goto_3
    if-nez v0, :cond_5

    .line 75
    .line 76
    invoke-virtual {p2}, Landroidx/paging/f;->c()Landroidx/paging/s;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 81
    .line 82
    if-eqz v1, :cond_4

    .line 83
    .line 84
    check-cast v0, Landroidx/paging/s$a;

    .line 85
    .line 86
    goto :goto_4

    .line 87
    :cond_4
    move-object v0, v2

    .line 88
    :goto_4
    if-nez v0, :cond_5

    .line 89
    .line 90
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    instance-of v1, v0, Landroidx/paging/s$a;

    .line 95
    .line 96
    if-eqz v1, :cond_6

    .line 97
    .line 98
    move-object v2, v0

    .line 99
    check-cast v2, Landroidx/paging/s$a;

    .line 100
    .line 101
    goto :goto_5

    .line 102
    :cond_5
    move-object v2, v0

    .line 103
    :cond_6
    :goto_5
    if-eqz v2, :cond_7

    .line 104
    .line 105
    invoke-virtual {v2}, Landroidx/paging/s$a;->b()Ljava/lang/Throwable;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-virtual {v1, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->e5(Ljava/lang/Throwable;)V

    .line 114
    .line 115
    .line 116
    :cond_7
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    instance-of v0, v0, Landroidx/paging/s$b;

    .line 121
    .line 122
    if-nez v0, :cond_8

    .line 123
    .line 124
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 125
    .line 126
    .line 127
    move-result v0

    .line 128
    if-nez v0, :cond_8

    .line 129
    .line 130
    if-nez v2, :cond_8

    .line 131
    .line 132
    const/4 v0, 0x1

    .line 133
    goto :goto_6

    .line 134
    :cond_8
    const/4 v0, 0x0

    .line 135
    :goto_6
    invoke-direct {p1, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->d4(Z)V

    .line 136
    .line 137
    .line 138
    invoke-virtual {p2}, Landroidx/paging/f;->d()Landroidx/paging/s;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    instance-of p1, p1, Landroidx/paging/s$b;

    .line 143
    .line 144
    if-nez p1, :cond_9

    .line 145
    .line 146
    if-nez v2, :cond_9

    .line 147
    .line 148
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 149
    .line 150
    .line 151
    :cond_9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 152
    .line 153
    return-object p0
.end method

.method public static final U3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LN21/k;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, LN21/k;->e()J

    .line 12
    .line 13
    .line 14
    move-result-wide v1

    .line 15
    invoke-virtual {p0, v0, v1, v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->o5(Ljava/lang/String;J)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final V3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LrZ0/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, LrZ0/b;->a()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->k5(II)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final synthetic W3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->N3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final X3(Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lorg/xbet/ui_common/utils/h;->l(Landroid/content/Context;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final Y3(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->k1:LeX0/h;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/h;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Landroid/os/Parcelable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final Z3(Z)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p1, :cond_0

    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget v1, LlZ0/g;->space_8:I

    .line 9
    .line 10
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    :goto_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    iget-object v1, v1, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 21
    .line 22
    invoke-virtual {v1, v0, p1, v0, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method private final c4()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final d4(Z)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v4, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    if-eqz p1, :cond_1

    .line 20
    .line 21
    iget-object v1, v0, LS91/U;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-virtual {v4}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->V4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 28
    .line 29
    .line 30
    move-result-object v4

    .line 31
    invoke-virtual {v1, v4}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 32
    .line 33
    .line 34
    :cond_1
    iget-object v1, v0, LS91/U;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 35
    .line 36
    if-eqz p1, :cond_2

    .line 37
    .line 38
    const/4 v2, 0x0

    .line 39
    :cond_2
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    iget-object v0, v0, LS91/U;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 43
    .line 44
    xor-int/lit8 p1, p1, 0x1

    .line 45
    .line 46
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method public static final f4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->M3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->Q3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->U3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->P3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->S3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->G3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Landroidx/paging/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->T3(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Landroidx/paging/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LrZ0/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->V3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;LrZ0/b;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->F3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->f4(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)LS91/U;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->R3(Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->W3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->Y3(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->d3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final E3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final J3()LS91/U;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/U;

    .line 13
    .line 14
    return-object v0
.end method

.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/U;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public final K3()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->v1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->H1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->S1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 8
    .line 9
    return-object v0
.end method

.method public final M3()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/U;->h:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public final N3(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a;)V
    .locals 4

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-eqz v0, :cond_1

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iget-object v0, v0, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 13
    .line 14
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;

    .line 15
    .line 16
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;->a()Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-virtual {v0, v3}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setItems(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$a;->a()Lorg/xbet/uikit/components/bannercollection/a$a;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p1}, Lorg/xbet/uikit/components/bannercollection/a$a;->a()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    xor-int/lit8 v3, p1, 0x1

    .line 36
    .line 37
    invoke-direct {p0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->Z3(Z)V

    .line 38
    .line 39
    .line 40
    if-nez p1, :cond_0

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    :cond_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    return-void

    .line 47
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$c;

    .line 48
    .line 49
    if-eqz v0, :cond_2

    .line 50
    .line 51
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    iget-object v0, v0, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 56
    .line 57
    check-cast p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$c;

    .line 58
    .line 59
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$c;->a()Lorg/xbet/uikit/components/bannercollection/a$b;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setItems(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 64
    .line 65
    .line 66
    const/4 p1, 0x1

    .line 67
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->Z3(Z)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iget-object p1, p1, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 75
    .line 76
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 77
    .line 78
    .line 79
    return-void

    .line 80
    :cond_2
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel$a$b;

    .line 81
    .line 82
    if-eqz p1, :cond_3

    .line 83
    .line 84
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iget-object p1, p1, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 89
    .line 90
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 91
    .line 92
    .line 93
    return-void

    .line 94
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 95
    .line 96
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 97
    .line 98
    .line 99
    throw p1
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->P1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public U2()V
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    .line 8
    move-result-object v3

    .line 9
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->K3()Z

    .line 10
    .line 11
    .line 12
    move-result v4

    .line 13
    if-nez v4, :cond_0

    .line 14
    .line 15
    sget-object v7, Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;->ACTIVE:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;

    .line 16
    .line 17
    sget v8, Lpb/g;->ic_search_new:I

    .line 18
    .line 19
    new-instance v5, LM01/c;

    .line 20
    .line 21
    new-instance v9, Lorg/xplatform/aggregator/impl/new_games/presentation/c;

    .line 22
    .line 23
    invoke-direct {v9, v0, v3}, Lorg/xplatform/aggregator/impl/new_games/presentation/c;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;)V

    .line 24
    .line 25
    .line 26
    const/16 v17, 0x3f0

    .line 27
    .line 28
    const/16 v18, 0x0

    .line 29
    .line 30
    const-string v6, "ic_search_new"

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v11, 0x0

    .line 34
    const/4 v12, 0x0

    .line 35
    const/4 v13, 0x0

    .line 36
    const/4 v14, 0x0

    .line 37
    const/4 v15, 0x0

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    invoke-direct/range {v5 .. v18}, LM01/c;-><init>(Ljava/lang/String;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonType;ILkotlin/jvm/functions/Function0;ZZLorg/xbet/uikit/components/badges/BadgeType;Lorg/xbet/uikit/components/counter/CounterType;Ljava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 41
    .line 42
    .line 43
    new-array v4, v2, [LM01/c;

    .line 44
    .line 45
    aput-object v5, v4, v1

    .line 46
    .line 47
    invoke-static {v4}, Lkotlin/collections/v;->h([Ljava/lang/Object;)Ljava/util/ArrayList;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    invoke-virtual {v3, v4}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtons(Ljava/util/ArrayList;)V

    .line 52
    .line 53
    .line 54
    :cond_0
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 55
    .line 56
    .line 57
    move-result-object v4

    .line 58
    sget v5, LlZ0/d;->uikitSecondary:I

    .line 59
    .line 60
    const/4 v6, 0x2

    .line 61
    const/4 v7, 0x0

    .line 62
    invoke-static {v4, v5, v7, v6, v7}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 63
    .line 64
    .line 65
    move-result v4

    .line 66
    invoke-static {v4}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 67
    .line 68
    .line 69
    move-result-object v4

    .line 70
    invoke-virtual {v3, v4}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setNavigationBarButtonsColorStateList(Landroid/content/res/ColorStateList;)V

    .line 71
    .line 72
    .line 73
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/d;

    .line 74
    .line 75
    invoke-direct {v4, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/d;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 76
    .line 77
    .line 78
    invoke-static {v3, v1, v4, v2, v7}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->H3()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 82
    .line 83
    .line 84
    move-result-object v1

    .line 85
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;->h()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    invoke-virtual {v3, v1}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setTitle(Ljava/lang/CharSequence;)V

    .line 90
    .line 91
    .line 92
    return-void
.end method

.method public final a4(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->x1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final b4(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->v1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->b2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e4(Z)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 6
    .line 7
    const/16 v2, 0x8

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v4, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->O3()V

    .line 20
    .line 21
    .line 22
    iget-object v1, v0, LS91/U;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 23
    .line 24
    if-eqz p1, :cond_1

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    :cond_1
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    iget-object v0, v0, LS91/U;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 31
    .line 32
    xor-int/lit8 p1, p1, 0x1

    .line 33
    .line 34
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/new_games/presentation/k;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/k;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public onDestroyView()V
    .locals 4

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    new-instance v0, Landroid/os/Bundle;

    .line 15
    .line 16
    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    .line 17
    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->y1:Ljava/util/Map;

    .line 20
    .line 21
    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    if-eqz v2, :cond_0

    .line 34
    .line 35
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    check-cast v2, Ljava/util/Map$Entry;

    .line 40
    .line 41
    invoke-interface {v2}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    check-cast v3, Ljava/lang/Number;

    .line 46
    .line 47
    invoke-virtual {v3}, Ljava/lang/Number;->intValue()I

    .line 48
    .line 49
    .line 50
    move-result v3

    .line 51
    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    check-cast v2, Landroid/os/Parcelable;

    .line 56
    .line 57
    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    invoke-virtual {v0, v3, v2}, Landroid/os/Bundle;->putParcelable(Ljava/lang/String;Landroid/os/Parcelable;)V

    .line 62
    .line 63
    .line 64
    goto :goto_0

    .line 65
    :cond_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    if-nez v1, :cond_1

    .line 70
    .line 71
    new-instance v1, Landroid/os/Bundle;

    .line 72
    .line 73
    invoke-direct {v1}, Landroid/os/Bundle;-><init>()V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 77
    .line 78
    .line 79
    :cond_1
    const-string v2, "BANNER_STATE_KEY"

    .line 80
    .line 81
    invoke-virtual {v1, v2, v0}, Landroid/os/Bundle;->putBundle(Ljava/lang/String;Landroid/os/Bundle;)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method public onPause()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->I1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public onResume()V
    .locals 2

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 9
    .line 10
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->I1:Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$b;

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->I3()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;->x3()V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 4

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->R2()Z

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->a4(Z)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->b5()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 33
    .line 34
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    sget v1, LlZ0/g;->space_12:I

    .line 39
    .line 40
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    const/4 v1, 0x0

    .line 45
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->q(II)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 53
    .line 54
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->getPagingAdapter()Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    if-eqz p1, :cond_0

    .line 59
    .line 60
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/h;

    .line 61
    .line 62
    invoke-direct {v0, p1, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/h;-><init>(Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/r;Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p1, v0}, Landroidx/paging/PagingDataAdapter;->p(Lkotlin/jvm/functions/Function1;)V

    .line 66
    .line 67
    .line 68
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 73
    .line 74
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/i;

    .line 75
    .line 76
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/i;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iget-object p1, p1, LS91/U;->i:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 87
    .line 88
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onInitView$3;

    .line 89
    .line 90
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 91
    .line 92
    .line 93
    move-result-object v1

    .line 94
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onInitView$3;-><init>(Ljava/lang/Object;)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 98
    .line 99
    .line 100
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->J3()LS91/U;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    iget-object p1, p1, LS91/U;->d:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 105
    .line 106
    new-instance v0, Lorg/xplatform/aggregator/impl/new_games/presentation/j;

    .line 107
    .line 108
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/j;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 109
    .line 110
    .line 111
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 112
    .line 113
    .line 114
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    if-eqz p1, :cond_1

    .line 119
    .line 120
    const-string v0, "BANNER_STATE_KEY"

    .line 121
    .line 122
    invoke-virtual {p1, v0}, Landroid/os/Bundle;->getBundle(Ljava/lang/String;)Landroid/os/Bundle;

    .line 123
    .line 124
    .line 125
    move-result-object p1

    .line 126
    goto :goto_0

    .line 127
    :cond_1
    const/4 p1, 0x0

    .line 128
    :goto_0
    if-eqz p1, :cond_3

    .line 129
    .line 130
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->y1:Ljava/util/Map;

    .line 131
    .line 132
    invoke-interface {v0}, Ljava/util/Map;->clear()V

    .line 133
    .line 134
    .line 135
    invoke-virtual {p1}, Landroid/os/BaseBundle;->keySet()Ljava/util/Set;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    :cond_2
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 144
    .line 145
    .line 146
    move-result v1

    .line 147
    if-eqz v1, :cond_3

    .line 148
    .line 149
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    move-result-object v1

    .line 153
    check-cast v1, Ljava/lang/String;

    .line 154
    .line 155
    invoke-virtual {p1, v1}, Landroid/os/Bundle;->getParcelable(Ljava/lang/String;)Landroid/os/Parcelable;

    .line 156
    .line 157
    .line 158
    move-result-object v2

    .line 159
    if-eqz v1, :cond_2

    .line 160
    .line 161
    if-eqz v2, :cond_2

    .line 162
    .line 163
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->y1:Ljava/util/Map;

    .line 164
    .line 165
    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    .line 166
    .line 167
    .line 168
    move-result v1

    .line 169
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 170
    .line 171
    .line 172
    move-result-object v1

    .line 173
    invoke-interface {v3, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 174
    .line 175
    .line 176
    goto :goto_1

    .line 177
    :cond_3
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, LN91/l;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, LN91/l;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, LN91/l;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->H3()Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-virtual {v2, v0}, LN91/l;->a(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)LN91/k;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {v0, p0}, LN91/k;->g(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;)V

    .line 61
    .line 62
    .line 63
    return-void

    .line 64
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 65
    .line 66
    new-instance v2, Ljava/lang/StringBuilder;

    .line 67
    .line 68
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 69
    .line 70
    .line 71
    const-string v3, "Cannot create dependency "

    .line 72
    .line 73
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 88
    .line 89
    .line 90
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->Q4()Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v11

    .line 27
    new-instance v2, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    move-object v5, v10

    .line 31
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v15, 0x3

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v12, 0x0

    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v14, v2

    .line 40
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->S4()Lkotlinx/coroutines/flow/Z;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$2;

    .line 52
    .line 53
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 65
    .line 66
    move-object v7, v5

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/4 v6, 0x3

    .line 71
    const/4 v7, 0x0

    .line 72
    const/4 v3, 0x0

    .line 73
    const/4 v4, 0x0

    .line 74
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->a5()Lkotlinx/coroutines/flow/e;

    .line 82
    .line 83
    .line 84
    move-result-object v8

    .line 85
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;

    .line 86
    .line 87
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    invoke-interface {v2}, Landroidx/lifecycle/w;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    .line 95
    .line 96
    .line 97
    move-result-object v9

    .line 98
    invoke-static {v9}, Landroidx/lifecycle/u;->a(Landroidx/lifecycle/Lifecycle;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycleLatest$default$1;

    .line 103
    .line 104
    move-object v7, v5

    .line 105
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycleLatest$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/Lifecycle;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 106
    .line 107
    .line 108
    const/4 v7, 0x0

    .line 109
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 110
    .line 111
    .line 112
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 113
    .line 114
    .line 115
    move-result-object v2

    .line 116
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->T4()Lkotlinx/coroutines/flow/e;

    .line 117
    .line 118
    .line 119
    move-result-object v8

    .line 120
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$4;

    .line 121
    .line 122
    invoke-direct {v11, v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$4;-><init>(Ljava/lang/Object;)V

    .line 123
    .line 124
    .line 125
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 126
    .line 127
    .line 128
    move-result-object v9

    .line 129
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 134
    .line 135
    move-object v7, v5

    .line 136
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 137
    .line 138
    .line 139
    const/4 v7, 0x0

    .line 140
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 141
    .line 142
    .line 143
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 144
    .line 145
    .line 146
    move-result-object v2

    .line 147
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->P4()Lkotlinx/coroutines/flow/Z;

    .line 148
    .line 149
    .line 150
    move-result-object v8

    .line 151
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 152
    .line 153
    .line 154
    move-result-object v9

    .line 155
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;

    .line 156
    .line 157
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 158
    .line 159
    .line 160
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 165
    .line 166
    move-object v7, v5

    .line 167
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 168
    .line 169
    .line 170
    const/4 v7, 0x0

    .line 171
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 172
    .line 173
    .line 174
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->L3()Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;

    .line 175
    .line 176
    .line 177
    move-result-object v2

    .line 178
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderViewModel;->W4()Lkotlinx/coroutines/flow/e;

    .line 179
    .line 180
    .line 181
    move-result-object v8

    .line 182
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 183
    .line 184
    .line 185
    move-result-object v9

    .line 186
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$6;

    .line 187
    .line 188
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$6;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 189
    .line 190
    .line 191
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 192
    .line 193
    .line 194
    move-result-object v2

    .line 195
    new-instance v5, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$5;

    .line 196
    .line 197
    move-object v7, v5

    .line 198
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$5;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 199
    .line 200
    .line 201
    const/4 v7, 0x0

    .line 202
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 203
    .line 204
    .line 205
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;->I3()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 206
    .line 207
    .line 208
    move-result-object v2

    .line 209
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;->y3()Lkotlinx/coroutines/flow/e;

    .line 210
    .line 211
    .line 212
    move-result-object v8

    .line 213
    new-instance v11, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$7;

    .line 214
    .line 215
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$7;-><init>(Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment;Lkotlin/coroutines/e;)V

    .line 216
    .line 217
    .line 218
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 219
    .line 220
    .line 221
    move-result-object v9

    .line 222
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    new-instance v4, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$6;

    .line 227
    .line 228
    move-object v7, v4

    .line 229
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/new_games/presentation/NewGamesFolderFragment$onObserveData$$inlined$observeWithLifecycle$default$6;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 230
    .line 231
    .line 232
    const/4 v5, 0x3

    .line 233
    const/4 v6, 0x0

    .line 234
    const/4 v2, 0x0

    .line 235
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 236
    .line 237
    .line 238
    return-void
.end method
