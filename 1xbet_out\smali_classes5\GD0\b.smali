.class public final LGD0/b;
.super LZY0/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LGD0/b$a;,
        LGD0/b$b;,
        LGD0/b$c;,
        LGD0/b$d;,
        LGD0/b$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010%\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u0000 )2\u00020\u0001:\u000573\u001c1\u001eB+\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00080\u0006\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0015\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\'\u0010\u001c\u001a\u00020\u00082\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u001f\u0010\u001e\u001a\u00020\u000c2\u0006\u0010\u001b\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010\"\u001a\u00020\u00082\u0006\u0010!\u001a\u00020 H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u001f\u0010&\u001a\u00020\u00082\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u001f\u0010)\u001a\u00020\u00082\u0006\u0010\u001b\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008)\u0010*J\'\u0010,\u001a\u00020\u00082\u0006\u0010\u001b\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020+H\u0002\u00a2\u0006\u0004\u0008,\u0010-J\u001f\u0010/\u001a\u00020\u00082\u0006\u0010.\u001a\u00020\u000c2\u0006\u0010%\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008/\u00100R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R \u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00080\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u00105R\u001c\u00109\u001a\u0008\u0012\u0004\u0012\u00020\u0007068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u001c\u0010;\u001a\u0008\u0012\u0004\u0012\u00020:068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001c\u00108R\"\u0010<\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u000706068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0018\u00108R\u0014\u0010?\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010@\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u0010>R\u0014\u0010A\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010>R \u0010D\u001a\u000e\u0012\u0004\u0012\u00020\u000c\u0012\u0004\u0012\u00020\u000c0B8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010CR\u0014\u0010G\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010FR\u0014\u0010I\u001a\u00020\u000c8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u00083\u0010HR\u0014\u0010J\u001a\u00020\u000c8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u00087\u0010HR\u0014\u0010K\u001a\u00020\u000c8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u00081\u0010H\u00a8\u0006L"
    }
    d2 = {
        "LGD0/b;",
        "LZY0/b;",
        "Landroid/content/Context;",
        "context",
        "LHX0/e;",
        "resourceManager",
        "Lkotlin/Function1;",
        "",
        "",
        "onPlayerClick",
        "<init>",
        "(Landroid/content/Context;LHX0/e;Lkotlin/jvm/functions/Function1;)V",
        "",
        "column",
        "m",
        "(I)I",
        "LID0/a;",
        "racesResults",
        "n",
        "(LID0/a;)V",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "f",
        "(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;",
        "holder",
        "row",
        "e",
        "(Landroidx/recyclerview/widget/RecyclerView$D;II)V",
        "c",
        "(II)I",
        "LGD0/b$d;",
        "firstColumnTitleViewHolder",
        "k",
        "(LGD0/b$d;)V",
        "LGD0/b$b;",
        "viewHolder",
        "i",
        "(ILGD0/b$b;)V",
        "LGD0/b$e;",
        "l",
        "(ILGD0/b$e;)V",
        "LGD0/b$a;",
        "j",
        "(IILGD0/b$a;)V",
        "position",
        "h",
        "(ILandroidx/recyclerview/widget/RecyclerView$D;)V",
        "a",
        "Landroid/content/Context;",
        "b",
        "LHX0/e;",
        "Lkotlin/jvm/functions/Function1;",
        "",
        "d",
        "Ljava/util/List;",
        "columnTitles",
        "LID0/c;",
        "rowTitles",
        "cells",
        "g",
        "I",
        "minColumnWidth",
        "maxColumnWidth",
        "padding",
        "",
        "Ljava/util/Map;",
        "cachedColumnWidthMap",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "titlePaint",
        "()I",
        "firstColumnWidth",
        "rowCount",
        "columnCount",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final l:LGD0/b$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final m:I


# instance fields
.field public final a:Landroid/content/Context;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LID0/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "+",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LGD0/b$c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LGD0/b$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LGD0/b;->l:LGD0/b$c;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LGD0/b;->m:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;LHX0/e;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "LHX0/e;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, LZY0/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LGD0/b;->a:Landroid/content/Context;

    .line 5
    .line 6
    iput-object p2, p0, LGD0/b;->b:LHX0/e;

    .line 7
    .line 8
    iput-object p3, p0, LGD0/b;->c:Lkotlin/jvm/functions/Function1;

    .line 9
    .line 10
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object p3

    .line 14
    iput-object p3, p0, LGD0/b;->d:Ljava/util/List;

    .line 15
    .line 16
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 17
    .line 18
    .line 19
    move-result-object p3

    .line 20
    iput-object p3, p0, LGD0/b;->e:Ljava/util/List;

    .line 21
    .line 22
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object p3

    .line 26
    iput-object p3, p0, LGD0/b;->f:Ljava/util/List;

    .line 27
    .line 28
    sget p3, Lpb/f;->size_70:I

    .line 29
    .line 30
    invoke-interface {p2, p3}, LHX0/e;->i(I)I

    .line 31
    .line 32
    .line 33
    move-result p3

    .line 34
    iput p3, p0, LGD0/b;->g:I

    .line 35
    .line 36
    sget p3, Lpb/f;->size_100:I

    .line 37
    .line 38
    invoke-interface {p2, p3}, LHX0/e;->i(I)I

    .line 39
    .line 40
    .line 41
    move-result p3

    .line 42
    iput p3, p0, LGD0/b;->h:I

    .line 43
    .line 44
    sget p3, Lpb/f;->space_8:I

    .line 45
    .line 46
    invoke-interface {p2, p3}, LHX0/e;->i(I)I

    .line 47
    .line 48
    .line 49
    move-result p3

    .line 50
    iput p3, p0, LGD0/b;->i:I

    .line 51
    .line 52
    new-instance p3, Ljava/util/LinkedHashMap;

    .line 53
    .line 54
    invoke-direct {p3}, Ljava/util/LinkedHashMap;-><init>()V

    .line 55
    .line 56
    .line 57
    iput-object p3, p0, LGD0/b;->j:Ljava/util/Map;

    .line 58
    .line 59
    new-instance p3, Landroid/graphics/Paint;

    .line 60
    .line 61
    invoke-direct {p3}, Landroid/graphics/Paint;-><init>()V

    .line 62
    .line 63
    .line 64
    sget v0, Lpb/h;->roboto_regular:I

    .line 65
    .line 66
    invoke-static {p1, v0}, LH0/h;->h(Landroid/content/Context;I)Landroid/graphics/Typeface;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    sget v0, Lpb/f;->text_16:I

    .line 71
    .line 72
    invoke-interface {p2, v0}, LHX0/e;->j(I)I

    .line 73
    .line 74
    .line 75
    move-result p2

    .line 76
    int-to-float p2, p2

    .line 77
    invoke-virtual {p3, p2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p3, p1}, Landroid/graphics/Paint;->setTypeface(Landroid/graphics/Typeface;)Landroid/graphics/Typeface;

    .line 81
    .line 82
    .line 83
    iput-object p3, p0, LGD0/b;->k:Landroid/graphics/Paint;

    .line 84
    .line 85
    return-void
.end method

.method private final m(I)I
    .locals 3

    .line 1
    iget-object v0, p0, LGD0/b;->j:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    if-nez v2, :cond_2

    .line 12
    .line 13
    iget-object v2, p0, LGD0/b;->d:Ljava/util/List;

    .line 14
    .line 15
    invoke-interface {v2, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    check-cast p1, Ljava/lang/String;

    .line 20
    .line 21
    iget-object v2, p0, LGD0/b;->k:Landroid/graphics/Paint;

    .line 22
    .line 23
    invoke-virtual {v2, p1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    float-to-int p1, p1

    .line 28
    iget v2, p0, LGD0/b;->g:I

    .line 29
    .line 30
    if-ge p1, v2, :cond_0

    .line 31
    .line 32
    :goto_0
    move p1, v2

    .line 33
    goto :goto_1

    .line 34
    :cond_0
    iget v2, p0, LGD0/b;->h:I

    .line 35
    .line 36
    if-le p1, v2, :cond_1

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    :goto_1
    iget v2, p0, LGD0/b;->i:I

    .line 40
    .line 41
    mul-int/lit8 v2, v2, 0x2

    .line 42
    .line 43
    add-int/2addr p1, v2

    .line 44
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 45
    .line 46
    .line 47
    move-result-object v2

    .line 48
    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    :cond_2
    check-cast v2, Ljava/lang/Number;

    .line 52
    .line 53
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    return p1
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 1
    iget-object v0, p0, LGD0/b;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public b()I
    .locals 2

    .line 1
    iget-object v0, p0, LGD0/b;->b:LHX0/e;

    .line 2
    .line 3
    sget v1, Lpb/f;->size_180:I

    .line 4
    .line 5
    invoke-interface {v0, v1}, LHX0/e;->i(I)I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    return v0
.end method

.method public c(II)I
    .locals 0

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x0

    .line 6
    return p1

    .line 7
    :cond_0
    if-nez p1, :cond_1

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_1
    if-nez p2, :cond_2

    .line 12
    .line 13
    const/4 p1, 0x2

    .line 14
    return p1

    .line 15
    :cond_2
    const/4 p1, 0x3

    .line 16
    return p1
.end method

.method public d()I
    .locals 1

    .line 1
    iget-object v0, p0, LGD0/b;->e:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v0, v0, 0x1

    .line 8
    .line 9
    return v0
.end method

.method public e(Landroidx/recyclerview/widget/RecyclerView$D;II)V
    .locals 2
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2, p3}, LGD0/b;->c(II)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_2

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    if-eq v0, v1, :cond_1

    .line 9
    .line 10
    const/4 v1, 0x2

    .line 11
    if-eq v0, v1, :cond_0

    .line 12
    .line 13
    check-cast p1, LGD0/b$a;

    .line 14
    .line 15
    invoke-virtual {p0, p2, p3, p1}, LGD0/b;->j(IILGD0/b$a;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    check-cast p1, LGD0/b$e;

    .line 20
    .line 21
    invoke-virtual {p0, p2, p1}, LGD0/b;->l(ILGD0/b$e;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_1
    check-cast p1, LGD0/b$b;

    .line 26
    .line 27
    invoke-virtual {p0, p3, p1}, LGD0/b;->i(ILGD0/b$b;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_2
    check-cast p1, LGD0/b$d;

    .line 32
    .line 33
    invoke-virtual {p0, p1}, LGD0/b;->k(LGD0/b$d;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public f(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 3
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-eqz p2, :cond_2

    .line 11
    .line 12
    const/4 v2, 0x1

    .line 13
    if-eq p2, v2, :cond_1

    .line 14
    .line 15
    const/4 v2, 0x2

    .line 16
    if-eq p2, v2, :cond_0

    .line 17
    .line 18
    new-instance p2, LGD0/b$a;

    .line 19
    .line 20
    invoke-static {v0, p1, v1}, LDN0/v;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/v;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    invoke-direct {p2, p1}, LGD0/b$a;-><init>(LDN0/v;)V

    .line 25
    .line 26
    .line 27
    return-object p2

    .line 28
    :cond_0
    new-instance p2, LGD0/b$e;

    .line 29
    .line 30
    invoke-static {v0, p1, v1}, LJD0/f;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LJD0/f;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object v0, p0, LGD0/b;->c:Lkotlin/jvm/functions/Function1;

    .line 35
    .line 36
    invoke-direct {p2, p1, v0}, LGD0/b$e;-><init>(LJD0/f;Lkotlin/jvm/functions/Function1;)V

    .line 37
    .line 38
    .line 39
    return-object p2

    .line 40
    :cond_1
    new-instance p2, LGD0/b$b;

    .line 41
    .line 42
    invoke-static {v0, p1, v1}, LDN0/w;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LDN0/w;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    invoke-direct {p2, p1}, LGD0/b$b;-><init>(LDN0/w;)V

    .line 47
    .line 48
    .line 49
    return-object p2

    .line 50
    :cond_2
    new-instance p2, LGD0/b$d;

    .line 51
    .line 52
    invoke-static {v0, p1, v1}, LJD0/e;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LJD0/e;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-direct {p2, p1}, LGD0/b$d;-><init>(LJD0/e;)V

    .line 57
    .line 58
    .line 59
    return-object p2
.end method

.method public final h(ILandroidx/recyclerview/widget/RecyclerView$D;)V
    .locals 6

    .line 1
    rem-int/lit8 p1, p1, 0x2

    .line 2
    .line 3
    if-nez p1, :cond_0

    .line 4
    .line 5
    sget-object v0, Lub/b;->a:Lub/b;

    .line 6
    .line 7
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 8
    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    sget v2, Lpb/c;->background:I

    .line 14
    .line 15
    const/4 v4, 0x4

    .line 16
    const/4 v5, 0x0

    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    sget-object v0, Lub/b;->a:Lub/b;

    .line 24
    .line 25
    iget-object p1, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 26
    .line 27
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    sget v2, Lpb/c;->contentBackground:I

    .line 32
    .line 33
    const/4 v4, 0x4

    .line 34
    const/4 v5, 0x0

    .line 35
    const/4 v3, 0x0

    .line 36
    invoke-static/range {v0 .. v5}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    :goto_0
    iget-object p2, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 41
    .line 42
    invoke-virtual {p2, p1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final i(ILGD0/b$b;)V
    .locals 1

    .line 1
    iget-object v0, p0, LGD0/b;->d:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 p1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, Ljava/lang/String;

    .line 10
    .line 11
    invoke-direct {p0, p1}, LGD0/b;->m(I)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p2, v0, p1}, LGD0/b$b;->d(Ljava/lang/String;I)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final j(IILGD0/b$a;)V
    .locals 3

    .line 1
    iget-object v0, p0, LGD0/b;->f:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    add-int/lit8 v1, p1, -0x1

    .line 8
    .line 9
    if-le v0, v1, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, LGD0/b;->f:Ljava/util/List;

    .line 12
    .line 13
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    check-cast v0, Ljava/util/List;

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    add-int/lit8 v2, p2, -0x1

    .line 24
    .line 25
    if-le v0, v2, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, LGD0/b;->f:Ljava/util/List;

    .line 28
    .line 29
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, Ljava/util/List;

    .line 34
    .line 35
    invoke-interface {v0, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    check-cast v0, Ljava/lang/String;

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const-string v0, ""

    .line 43
    .line 44
    :goto_0
    add-int/lit8 p2, p2, -0x1

    .line 45
    .line 46
    invoke-direct {p0, p2}, LGD0/b;->m(I)I

    .line 47
    .line 48
    .line 49
    move-result p2

    .line 50
    invoke-virtual {p3, v0, p2}, LGD0/b$a;->d(Ljava/lang/String;I)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0, p1, p3}, LGD0/b;->h(ILandroidx/recyclerview/widget/RecyclerView$D;)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final k(LGD0/b$d;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LGD0/b$d;->e()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final l(ILGD0/b$e;)V
    .locals 2

    .line 1
    iget-object v0, p0, LGD0/b;->e:Ljava/util/List;

    .line 2
    .line 3
    add-int/lit8 v1, p1, -0x1

    .line 4
    .line 5
    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, LID0/c;

    .line 10
    .line 11
    invoke-virtual {p2, v0}, LGD0/b$e;->e(LID0/c;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, p1, p2}, LGD0/b;->h(ILandroidx/recyclerview/widget/RecyclerView$D;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final n(LID0/a;)V
    .locals 1
    .param p1    # LID0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LID0/a;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iput-object v0, p0, LGD0/b;->d:Ljava/util/List;

    .line 6
    .line 7
    invoke-virtual {p1}, LID0/a;->c()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, LGD0/b;->e:Ljava/util/List;

    .line 12
    .line 13
    invoke-virtual {p1}, LID0/a;->a()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    iput-object p1, p0, LGD0/b;->f:Ljava/util/List;

    .line 18
    .line 19
    return-void
.end method
