.class public final synthetic Lu01/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lu01/f;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lu01/f;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    check-cast p1, Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;

    invoke-static {v0, p1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->d(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
