.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\r\u0008\u0082\u0008\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0004\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0010\u0010\r\u001a\u00020\u000cH\u00d6\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0010\u0010\u000f\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001a\u0010\u0013\u001a\u00020\u00122\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u0019\u0010\u0010R\u0017\u0010\u0006\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u001a\u001a\u0004\u0008\u001b\u0010\u0010R\u0017\u0010\u0007\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u001a\u001a\u0004\u0008\u0015\u0010\u0010R\u0017\u0010\t\u001a\u00020\u00088\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001c\u0010\u001e\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
        "",
        "Landroid/view/View;",
        "view",
        "",
        "rowIndex",
        "viewIndex",
        "headerCount",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "viewType",
        "<init>",
        "(Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Landroid/view/View;",
        "c",
        "()Landroid/view/View;",
        "b",
        "I",
        "d",
        "e",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    .line 5
    .line 6
    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    .line 7
    .line 8
    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    .line 9
    .line 10
    iput p4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final c()Landroid/view/View;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final e()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    iget-object p1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    if-eq v1, p1, :cond_6

    return v2

    :cond_6
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a:Landroid/view/View;

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b:I

    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c:I

    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d:I

    iget-object v4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "MeasureView(view="

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", rowIndex="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", viewIndex="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", headerCount="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", viewType="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
