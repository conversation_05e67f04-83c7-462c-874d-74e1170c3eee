.class public final synthetic LL1/D;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/video/f$a;

.field public final synthetic b:Landroidx/media3/common/r;

.field public final synthetic c:Landroidx/media3/exoplayer/k;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/video/f$a;Landroidx/media3/common/r;Landroidx/media3/exoplayer/k;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LL1/D;->a:Landroidx/media3/exoplayer/video/f$a;

    iput-object p2, p0, LL1/D;->b:Landroidx/media3/common/r;

    iput-object p3, p0, LL1/D;->c:Landroidx/media3/exoplayer/k;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, LL1/D;->a:Landroidx/media3/exoplayer/video/f$a;

    iget-object v1, p0, LL1/D;->b:Landroidx/media3/common/r;

    iget-object v2, p0, LL1/D;->c:Landroidx/media3/exoplayer/k;

    invoke-static {v0, v1, v2}, Landroidx/media3/exoplayer/video/f$a;->h(Landroidx/media3/exoplayer/video/f$a;Landroidx/media3/common/r;Landroidx/media3/exoplayer/k;)V

    return-void
.end method
