.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0088\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0017\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u0000 \u0081\u00012\u00020\u0001:\u0002\u0082\u0001B\u00e9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u00a2\u0006\u0004\u0008:\u0010;J\u000f\u0010=\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008=\u0010>J\u000f\u0010?\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008?\u0010>J\u000f\u0010@\u001a\u00020<H\u0016\u00a2\u0006\u0004\u0008@\u0010>J\u000f\u0010A\u001a\u00020<H\u0016\u00a2\u0006\u0004\u0008A\u0010>J\u0017\u0010D\u001a\u00020<2\u0006\u0010C\u001a\u00020BH\u0016\u00a2\u0006\u0004\u0008D\u0010EJ\u0013\u0010H\u001a\u0008\u0012\u0004\u0012\u00020G0F\u00a2\u0006\u0004\u0008H\u0010IJ\u0013\u0010L\u001a\u0008\u0012\u0004\u0012\u00020K0J\u00a2\u0006\u0004\u0008L\u0010MJ\u0013\u0010O\u001a\u0008\u0012\u0004\u0012\u00020N0F\u00a2\u0006\u0004\u0008O\u0010IJ\r\u0010P\u001a\u00020<\u00a2\u0006\u0004\u0008P\u0010>J\r\u0010Q\u001a\u00020<\u00a2\u0006\u0004\u0008Q\u0010>J\u0015\u0010T\u001a\u00020<2\u0006\u0010S\u001a\u00020R\u00a2\u0006\u0004\u0008T\u0010UJ\u0015\u0010V\u001a\u00020<2\u0006\u0010S\u001a\u00020R\u00a2\u0006\u0004\u0008V\u0010UJ%\u0010[\u001a\u00020<2\u0006\u0010S\u001a\u00020R2\u0006\u0010X\u001a\u00020W2\u0006\u0010Z\u001a\u00020Y\u00a2\u0006\u0004\u0008[\u0010\\R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010lR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010nR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u0014\u0010t\u001a\u00020q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008r\u0010sR\u001a\u0010w\u001a\u0008\u0012\u0004\u0012\u00020K0J8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u001a\u0010{\u001a\u0008\u0012\u0004\u0012\u00020N0x8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u001a\u0010}\u001a\u0008\u0012\u0004\u0012\u00020G0x8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008|\u0010zR\u0015\u0010\u0080\u0001\u001a\u00020R8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008~\u0010\u007f\u00a8\u0006\u0083\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lkc1/d;",
        "getAggregatorTournamentBannerListScenario",
        "LwX0/a;",
        "appScreensProvider",
        "Lorg/xbet/analytics/domain/scope/g;",
        "aggregatorTournamentsAnalytics",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "openBannersDelegate",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "LwX0/C;",
        "routerHolder",
        "LpR/a;",
        "authFatmanLogger",
        "Lp9/g;",
        "observeLoginStateUseCase",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "LHX0/e;",
        "resourceManager",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lm8/a;",
        "dispatchers",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "<init>",
        "(Lcom/xbet/onexuser/domain/user/c;Lorg/xbet/ui_common/utils/M;Lkc1/d;LwX0/a;Lorg/xbet/analytics/domain/scope/g;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LSX0/c;LwX0/C;LpR/a;Lp9/g;LGg/a;Lorg/xbet/analytics/domain/scope/I;LHX0/e;LxX0/a;Lm8/a;Lek/d;LAR/a;LZR/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lfk/s;Lgk0/a;Lek/f;Lfk/l;LC81/f;LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lp9/c;)V",
        "",
        "H4",
        "()V",
        "G4",
        "R3",
        "d4",
        "",
        "throwable",
        "e4",
        "(Ljava/lang/Throwable;)V",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
        "y4",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/Z;",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
        "x4",
        "()Lkotlinx/coroutines/flow/Z;",
        "",
        "w4",
        "B4",
        "A4",
        "",
        "screenName",
        "E4",
        "(Ljava/lang/String;)V",
        "F4",
        "Lkb1/b;",
        "banner",
        "",
        "position",
        "C4",
        "(Ljava/lang/String;Lkb1/b;I)V",
        "y5",
        "Lcom/xbet/onexuser/domain/user/c;",
        "z5",
        "Lorg/xbet/ui_common/utils/M;",
        "A5",
        "Lkc1/d;",
        "B5",
        "LwX0/a;",
        "C5",
        "Lorg/xbet/analytics/domain/scope/g;",
        "D5",
        "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
        "E5",
        "LSX0/c;",
        "F5",
        "LwX0/C;",
        "G5",
        "LpR/a;",
        "H5",
        "Lp9/g;",
        "Lek0/o;",
        "I5",
        "Lek0/o;",
        "remoteConfigModel",
        "J5",
        "Lkotlinx/coroutines/flow/Z;",
        "bannersEventsFlow",
        "Lkotlinx/coroutines/flow/V;",
        "K5",
        "Lkotlinx/coroutines/flow/V;",
        "authStateFlow",
        "L5",
        "bannersMutableStateFlow",
        "z4",
        "()Ljava/lang/String;",
        "remoteAggregatorTournamentCardsOldStyle",
        "M5",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final M5:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lkc1/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lorg/xbet/analytics/domain/scope/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:LpR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lp9/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lkotlinx/coroutines/flow/Z;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->M5:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a;

    return-void
.end method

.method public constructor <init>(Lcom/xbet/onexuser/domain/user/c;Lorg/xbet/ui_common/utils/M;Lkc1/d;LwX0/a;Lorg/xbet/analytics/domain/scope/g;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LSX0/c;LwX0/C;LpR/a;Lp9/g;LGg/a;Lorg/xbet/analytics/domain/scope/I;LHX0/e;LxX0/a;Lm8/a;Lek/d;LAR/a;LZR/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lfk/s;Lgk0/a;Lek/f;Lfk/l;LC81/f;LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lp9/c;)V
    .locals 20
    .param p1    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkc1/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/analytics/domain/scope/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LpR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lp9/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v3, p2

    .line 4
    .line 5
    move-object/from16 v8, p8

    .line 6
    .line 7
    move-object/from16 v6, p11

    .line 8
    .line 9
    move-object/from16 v7, p12

    .line 10
    .line 11
    move-object/from16 v13, p13

    .line 12
    .line 13
    move-object/from16 v4, p14

    .line 14
    .line 15
    move-object/from16 v9, p15

    .line 16
    .line 17
    move-object/from16 v12, p16

    .line 18
    .line 19
    move-object/from16 v14, p17

    .line 20
    .line 21
    move-object/from16 v15, p18

    .line 22
    .line 23
    move-object/from16 v18, p20

    .line 24
    .line 25
    move-object/from16 v17, p21

    .line 26
    .line 27
    move-object/from16 v16, p22

    .line 28
    .line 29
    move-object/from16 v10, p23

    .line 30
    .line 31
    move-object/from16 v11, p24

    .line 32
    .line 33
    move-object/from16 v19, p25

    .line 34
    .line 35
    move-object/from16 v1, p26

    .line 36
    .line 37
    move-object/from16 v2, p27

    .line 38
    .line 39
    move-object/from16 v5, p28

    .line 40
    .line 41
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V

    .line 42
    .line 43
    .line 44
    move-object/from16 v1, p1

    .line 45
    .line 46
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->y5:Lcom/xbet/onexuser/domain/user/c;

    .line 47
    .line 48
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->z5:Lorg/xbet/ui_common/utils/M;

    .line 49
    .line 50
    move-object/from16 v1, p3

    .line 51
    .line 52
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->A5:Lkc1/d;

    .line 53
    .line 54
    move-object/from16 v1, p4

    .line 55
    .line 56
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->B5:LwX0/a;

    .line 57
    .line 58
    move-object/from16 v1, p5

    .line 59
    .line 60
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->C5:Lorg/xbet/analytics/domain/scope/g;

    .line 61
    .line 62
    move-object/from16 v1, p6

    .line 63
    .line 64
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->D5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 65
    .line 66
    move-object/from16 v2, p7

    .line 67
    .line 68
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->E5:LSX0/c;

    .line 69
    .line 70
    iput-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->F5:LwX0/C;

    .line 71
    .line 72
    move-object/from16 v2, p9

    .line 73
    .line 74
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->G5:LpR/a;

    .line 75
    .line 76
    move-object/from16 v2, p10

    .line 77
    .line 78
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->H5:Lp9/g;

    .line 79
    .line 80
    invoke-interface/range {p19 .. p19}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->I5:Lek0/o;

    .line 85
    .line 86
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->d()Lkotlinx/coroutines/flow/Z;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->J5:Lkotlinx/coroutines/flow/Z;

    .line 91
    .line 92
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 93
    .line 94
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 99
    .line 100
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;

    .line 101
    .line 102
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->z4()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    invoke-static {v2}, Ljb1/l;->a(Ljava/lang/String;)Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;-><init>(Ljava/util/List;)V

    .line 111
    .line 112
    .line 113
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 118
    .line 119
    return-void
.end method

.method public static final D4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-interface {p0}, Lkotlinx/coroutines/N;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-interface {v0, p0, p1}, Lkotlinx/coroutines/CoroutineExceptionHandler;->handleException(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Throwable;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method private final G4()V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$a;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->E5:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->AGGREGATOR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    sget v8, Lpb/k;->tournaments_placeholder:I

    .line 10
    .line 11
    const/16 v12, 0x1de

    .line 12
    .line 13
    const/4 v13, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v9, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method private final H4()V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$b;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->E5:LSX0/c;

    .line 6
    .line 7
    sget-object v3, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 8
    .line 9
    sget v8, Lpb/k;->data_retrieval_error:I

    .line 10
    .line 11
    const/16 v12, 0x1de

    .line 12
    .line 13
    const/4 v13, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v6, 0x0

    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v9, 0x0

    .line 19
    const/4 v10, 0x0

    .line 20
    const/4 v11, 0x0

    .line 21
    invoke-static/range {v2 .. v13}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 26
    .line 27
    .line 28
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->D4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic m4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)LwX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->B5:LwX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic n4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic o4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->z4()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic q4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->F5:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->a4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->G4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->H4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->f4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final A4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->A5:Lkc1/d;

    .line 2
    .line 3
    invoke-interface {v0}, Lkc1/d;->invoke()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$2;

    .line 18
    .line 19
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$getTournaments$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final B4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->H5:Lp9/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/g;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$observeLoginState$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$observeLoginState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final C4(Ljava/lang/String;Lkb1/b;I)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkb1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->C5:Lorg/xbet/analytics/domain/scope/g;

    .line 2
    .line 3
    invoke-virtual {p2}, Lkb1/b;->f()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-virtual {p1, v0}, Lorg/xbet/analytics/domain/scope/g;->g(I)V

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->D5:Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    .line 11
    .line 12
    invoke-virtual {p2}, Lkb1/b;->j()Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/l;

    .line 21
    .line 22
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/l;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p1, p2, p3, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;->f(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final E4(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->C5:Lorg/xbet/analytics/domain/scope/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/g;->b()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->G5:LpR/a;

    .line 7
    .line 8
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->AGGREGATOR_TOURNAMENTS:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 9
    .line 10
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-interface {v0, p1, v1}, LpR/a;->f(Ljava/lang/String;Ljava/lang/String;)V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->F5:LwX0/C;

    .line 18
    .line 19
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    if-eqz p1, :cond_0

    .line 24
    .line 25
    invoke-virtual {p1}, LwX0/c;->w()V

    .line 26
    .line 27
    .line 28
    :cond_0
    return-void
.end method

.method public final F4(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->G5:LpR/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->AGGREGATOR_TOURNAMENTS:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 4
    .line 5
    invoke-interface {v0, p1, v1}, LpR/a;->j(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 6
    .line 7
    .line 8
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    sget-object v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$onClickRegistration$1;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$onClickRegistration$1;

    .line 13
    .line 14
    new-instance v7, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$onClickRegistration$2;

    .line 15
    .line 16
    const/4 p1, 0x0

    .line 17
    invoke-direct {v7, p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$onClickRegistration$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/16 v8, 0xe

    .line 21
    .line 22
    const/4 v9, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public R3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->A4()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->z4()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    invoke-static {v2}, Ljb1/l;->a(Ljava/lang/String;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a$c;-><init>(Ljava/util/List;)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public d4()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->H4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->z5:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$showCustomError$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$showCustomError$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final w4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x4()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->J5:Lkotlinx/coroutines/flow/Z;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel$a$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final z4()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/deprecated/AggregatorTournamentsDeprecatedViewModel;->I5:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->y()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
