.class public final LEa1/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u001a1\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a/\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;",
        "aggregatorTournamentCardsCollectionType",
        "",
        "currencySymbol",
        "LHX0/e;",
        "resourceManager",
        "Ljava/util/Locale;",
        "locale",
        "Lk21/b;",
        "b",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;Ljava/lang/String;LHX0/e;Ljava/util/Locale;)Lk21/b;",
        "",
        "show24format",
        "Ljava/util/Date;",
        "start",
        "end",
        "a",
        "(Ljava/util/Locale;ZLjava/util/Date;Ljava/util/Date;)Ljava/lang/String;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/util/Locale;ZLjava/util/Date;Ljava/util/Date;)Ljava/lang/String;
    .locals 7

    .line 1
    sget-object v0, Ll8/b;->a:Ll8/b;

    .line 2
    .line 3
    const/4 v5, 0x4

    .line 4
    const/4 v6, 0x0

    .line 5
    const/4 v3, 0x0

    .line 6
    move-object v4, p0

    .line 7
    move v1, p1

    .line 8
    move-object v2, p2

    .line 9
    invoke-static/range {v0 .. v6}, Ll8/b;->m0(Ll8/b;ZLjava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    move-object v2, p3

    .line 14
    invoke-static/range {v0 .. v6}, Ll8/b;->m0(Ll8/b;ZLjava/util/Date;Ljava/lang/String;Ljava/util/Locale;ILjava/lang/Object;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    new-instance p2, Ljava/lang/StringBuilder;

    .line 19
    .line 20
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    const-string p0, " \u2014 "

    .line 27
    .line 28
    invoke-virtual {p2, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    return-object p0
.end method

.method public static final b(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;Ljava/lang/String;LHX0/e;Ljava/util/Locale;)Lk21/b;
    .locals 13
    .param p0    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/Locale;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move-object/from16 v0, p3

    .line 2
    .line 3
    sget-object v1, Ll8/j;->a:Ll8/j;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->f()J

    .line 10
    .line 11
    .line 12
    move-result-wide v2

    .line 13
    long-to-double v2, v2

    .line 14
    sget-object v4, Lcom/xbet/onexcore/utils/ValueType;->PRIZE:Lcom/xbet/onexcore/utils/ValueType;

    .line 15
    .line 16
    invoke-virtual {v1, v2, v3, v4}, Ll8/j;->n(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    new-instance v2, Lk21/b;

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 23
    .line 24
    .line 25
    move-result-wide v3

    .line 26
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    invoke-virtual {v5}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    invoke-interface {v0}, LHX0/e;->c()Z

    .line 35
    .line 36
    .line 37
    move-result v6

    .line 38
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 39
    .line 40
    .line 41
    move-result-object v7

    .line 42
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->e()Ljava/util/Date;

    .line 43
    .line 44
    .line 45
    move-result-object v7

    .line 46
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    invoke-virtual {v8}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->c()Ljava/util/Date;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    move-object/from16 v9, p4

    .line 55
    .line 56
    invoke-static {v9, v6, v7, v8}, LEa1/d;->a(Ljava/util/Locale;ZLjava/util/Date;Ljava/util/Date;)Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v6

    .line 60
    new-instance v7, Ljava/lang/StringBuilder;

    .line 61
    .line 62
    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    .line 63
    .line 64
    .line 65
    invoke-virtual {v7, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 66
    .line 67
    .line 68
    const-string p2, " "

    .line 69
    .line 70
    invoke-virtual {v7, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 71
    .line 72
    .line 73
    invoke-virtual {v7, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v7

    .line 80
    sget-object p2, LCX0/l;->a:LCX0/l;

    .line 81
    .line 82
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->d()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$c;->a()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-virtual {p2, v1}, LCX0/l;->K(Ljava/lang/String;)Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object p2

    .line 94
    invoke-static {p2}, LL11/c$d;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object p2

    .line 98
    invoke-static {p2}, LL11/c$d;->c(Ljava/lang/String;)LL11/c$d;

    .line 99
    .line 100
    .line 101
    move-result-object v8

    .line 102
    sget p2, Lpb/g;->ic_tournament_banner:I

    .line 103
    .line 104
    invoke-static {p2}, LL11/c$c;->d(I)I

    .line 105
    .line 106
    .line 107
    move-result p2

    .line 108
    invoke-static {p2}, LL11/c$c;->c(I)LL11/c$c;

    .line 109
    .line 110
    .line 111
    move-result-object v9

    .line 112
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->g()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;

    .line 113
    .line 114
    .line 115
    move-result-object p2

    .line 116
    invoke-static {p2, v0}, LEa1/g;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$d;LHX0/e;)Lk21/m;

    .line 117
    .line 118
    .line 119
    move-result-object v10

    .line 120
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->l()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;

    .line 121
    .line 122
    .line 123
    move-result-object p2

    .line 124
    invoke-static {p2, v0}, LEa1/c;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$e;LHX0/e;)Lk21/a;

    .line 125
    .line 126
    .line 127
    move-result-object v11

    .line 128
    invoke-virtual {p0}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 129
    .line 130
    .line 131
    move-result-object p0

    .line 132
    invoke-static {p0, p1, v0}, LEa1/e;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;Lorg/xbet/uikit_aggregator/aggregatorTournamentCardsCollection/models/AggregatorTournamentCardsCollectionType;LHX0/e;)Lk21/f;

    .line 133
    .line 134
    .line 135
    move-result-object v12

    .line 136
    invoke-direct/range {v2 .. v12}, Lk21/b;-><init>(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LL11/c;LL11/c;Lk21/m;Lk21/a;Lk21/f;)V

    .line 137
    .line 138
    .line 139
    return-object v2
.end method
