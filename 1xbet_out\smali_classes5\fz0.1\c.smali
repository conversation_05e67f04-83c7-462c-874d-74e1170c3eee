.class public final Lfz0/c;
.super LUX0/i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lfz0/c$a;,
        Lfz0/c$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LUX0/i<",
        "Ldz0/a;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u0000 \u00102\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0017B#\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R \u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u0012R\u0014\u0010\u0016\u001a\u00020\u00138\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0018"
    }
    d2 = {
        "Lfz0/c;",
        "LUX0/i;",
        "Ldz0/a;",
        "Landroid/view/View;",
        "itemView",
        "Lkotlin/Function1;",
        "",
        "onDeleteBetClickListener",
        "<init>",
        "(Landroid/view/View;Lkotlin/jvm/functions/Function1;)V",
        "item",
        "f",
        "(Ldz0/a;)V",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "cellType",
        "",
        "h",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)I",
        "Lkotlin/jvm/functions/Function1;",
        "Lbz0/d;",
        "g",
        "Lbz0/d;",
        "viewBinding",
        "a",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:Lfz0/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ldz0/a;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lbz0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lfz0/c$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lfz0/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lfz0/c;->h:Lfz0/c$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Landroid/view/View;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/view/View;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ldz0/a;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1}, LUX0/i;-><init>(Landroid/view/View;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lfz0/c;->f:Lkotlin/jvm/functions/Function1;

    .line 5
    .line 6
    invoke-static {p1}, Lbz0/d;->a(Landroid/view/View;)Lbz0/d;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    iput-object p1, p0, Lfz0/c;->g:Lbz0/d;

    .line 11
    .line 12
    return-void
.end method

.method public static synthetic e(Lfz0/c;Ldz0/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lfz0/c;->g(Lfz0/c;Ldz0/a;Landroid/view/View;)V

    return-void
.end method

.method public static final g(Lfz0/c;Ldz0/a;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, Lfz0/c;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic d(Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p1, Ldz0/a;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lfz0/c;->f(Ldz0/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public f(Ldz0/a;)V
    .locals 8
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lfz0/c;->g:Lbz0/d;

    .line 2
    .line 3
    iget-object v1, v0, Lbz0/d;->b:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {p1}, Ldz0/a;->d()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {v2}, Lorg/xbet/games_section/api/models/GameBonusType;->isFreeBetBonus()Z

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    if-eqz v2, :cond_0

    .line 14
    .line 15
    iget-object v2, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    sget v3, Lpb/k;->bonus:I

    .line 22
    .line 23
    invoke-virtual {v2, v3}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    goto :goto_0

    .line 28
    :cond_0
    iget-object v2, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 29
    .line 30
    invoke-virtual {v2}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    sget v3, Lpb/k;->cell_bet:I

    .line 35
    .line 36
    sget-object v4, Ll8/j;->a:Ll8/j;

    .line 37
    .line 38
    invoke-virtual {p1}, Ldz0/a;->c()D

    .line 39
    .line 40
    .line 41
    move-result-wide v5

    .line 42
    sget-object v7, Lcom/xbet/onexcore/utils/ValueType;->LIMIT:Lcom/xbet/onexcore/utils/ValueType;

    .line 43
    .line 44
    invoke-virtual {v4, v5, v6, v7}, Ll8/j;->d(DLcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    invoke-virtual {p1}, Ldz0/a;->e()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v5

    .line 52
    const/4 v6, 0x2

    .line 53
    new-array v6, v6, [Ljava/lang/Object;

    .line 54
    .line 55
    const/4 v7, 0x0

    .line 56
    aput-object v4, v6, v7

    .line 57
    .line 58
    const/4 v4, 0x1

    .line 59
    aput-object v5, v6, v4

    .line 60
    .line 61
    invoke-virtual {v2, v3, v6}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    :goto_0
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 66
    .line 67
    .line 68
    iget-object v1, v0, Lbz0/d;->c:Landroid/widget/TextView;

    .line 69
    .line 70
    invoke-virtual {p1}, Ldz0/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    invoke-virtual {p0, v2}, Lfz0/c;->h(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)I

    .line 75
    .line 76
    .line 77
    move-result v2

    .line 78
    invoke-virtual {v1, v2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 79
    .line 80
    .line 81
    iget-object v1, v0, Lbz0/d;->d:Landroid/widget/ImageView;

    .line 82
    .line 83
    new-instance v2, Lfz0/b;

    .line 84
    .line 85
    invoke-direct {v2, p0, p1}, Lfz0/b;-><init>(Lfz0/c;Ldz0/a;)V

    .line 86
    .line 87
    .line 88
    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p1}, Ldz0/a;->f()Z

    .line 92
    .line 93
    .line 94
    move-result p1

    .line 95
    if-eqz p1, :cond_1

    .line 96
    .line 97
    iget-object p1, v0, Lbz0/d;->c:Landroid/widget/TextView;

    .line 98
    .line 99
    const/high16 v0, 0x3f800000    # 1.0f

    .line 100
    .line 101
    invoke-virtual {p1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 102
    .line 103
    .line 104
    return-void

    .line 105
    :cond_1
    iget-object p1, v0, Lbz0/d;->c:Landroid/widget/TextView;

    .line 106
    .line 107
    const v0, 0x3e4ccccd

    .line 108
    .line 109
    .line 110
    invoke-virtual {p1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 111
    .line 112
    .line 113
    return-void
.end method

.method public final h(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)I
    .locals 1

    .line 1
    sget-object v0, Lfz0/c$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    packed-switch p1, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    sget p1, LWy0/a;->default_bet_background:I

    .line 13
    .line 14
    return p1

    .line 15
    :pswitch_0
    sget p1, LWy0/a;->x20_bet_background:I

    .line 16
    .line 17
    return p1

    .line 18
    :pswitch_1
    sget p1, LWy0/a;->x10_bet_background:I

    .line 19
    .line 20
    return p1

    .line 21
    :pswitch_2
    sget p1, LWy0/a;->x7_bet_background:I

    .line 22
    .line 23
    return p1

    .line 24
    :pswitch_3
    sget p1, LWy0/a;->x5_bet_background:I

    .line 25
    .line 26
    return p1

    .line 27
    :pswitch_4
    sget p1, LWy0/a;->x4_bet_background:I

    .line 28
    .line 29
    return p1

    .line 30
    :pswitch_5
    sget p1, LWy0/a;->x2_bet_background:I

    .line 31
    .line 32
    return p1

    .line 33
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
