.class public final synthetic Lorg/xbet/themesettings/impl/presentation/timepicker/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/e;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/timepicker/e;->a:Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;->S2(Lorg/xbet/themesettings/impl/presentation/timepicker/TimePickerBottomDialog;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
