.class public final synthetic Lorg/xbet/ui_common/moxy/activities/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/c;->a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/c;->a:Lorg/xbet/ui_common/moxy/activities/IntellijActivity;

    invoke-static {v0}, Lorg/xbet/ui_common/moxy/activities/IntellijActivity;->M(Lorg/xbet/ui_common/moxy/activities/IntellijActivity;)Lorg/xbet/ui_common/moxy/activities/h;

    move-result-object v0

    return-object v0
.end method
