.class public final synthetic LqE0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LqE0/b;


# direct methods
.method public synthetic constructor <init>(LqE0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LqE0/a;->a:LqE0/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LqE0/a;->a:LqE0/b;

    invoke-static {v0}, LqE0/b;->a(LqE0/b;)LpE0/a;

    move-result-object v0

    return-object v0
.end method
