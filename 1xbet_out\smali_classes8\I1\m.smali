.class public final synthetic LI1/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/common/base/Predicate;


# instance fields
.field public final synthetic a:LI1/n;

.field public final synthetic b:LI1/n$e;


# direct methods
.method public synthetic constructor <init>(LI1/n;LI1/n$e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LI1/m;->a:LI1/n;

    iput-object p2, p0, LI1/m;->b:LI1/n$e;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Z
    .locals 2

    .line 1
    iget-object v0, p0, LI1/m;->a:LI1/n;

    iget-object v1, p0, LI1/m;->b:LI1/n$e;

    check-cast p1, Landroidx/media3/common/r;

    invoke-static {v0, v1, p1}, LI1/n;->t(LI1/n;LI1/n$e;Landroidx/media3/common/r;)Z

    move-result p1

    return p1
.end method
