.class public final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;
.super Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;",
        "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "betType",
        "<init>",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "a",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 1
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 3
    .line 4
    .line 5
    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;->a:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$b$b;->a:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 2
    .line 3
    return-object v0
.end method
