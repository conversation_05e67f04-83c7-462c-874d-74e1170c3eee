.class public final LQP0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQP0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LQP0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LQP0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LQP0/a$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;)LQP0/c;
    .locals 11

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static {p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static {p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    new-instance v0, LQP0/a$a;

    .line 29
    .line 30
    const/4 v10, 0x0

    .line 31
    move-object v1, p1

    .line 32
    move-object v2, p2

    .line 33
    move-object v3, p3

    .line 34
    move-object v4, p4

    .line 35
    move-object/from16 v5, p5

    .line 36
    .line 37
    move-object/from16 v6, p6

    .line 38
    .line 39
    move-object/from16 v7, p7

    .line 40
    .line 41
    move-object/from16 v8, p8

    .line 42
    .line 43
    move-object/from16 v9, p9

    .line 44
    .line 45
    invoke-direct/range {v0 .. v10}, LQP0/a$a;-><init>(LQW0/c;Lf8/g;Lorg/xbet/ui_common/utils/M;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;LHX0/e;Ljava/lang/String;Lc8/h;LQP0/b;)V

    .line 46
    .line 47
    .line 48
    return-object v0
.end method
