.class public final Lorg/xbet/ui_common/dialogs/c$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/core/view/K;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/dialogs/c;->t2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/ui_common/dialogs/c;


# direct methods
.method public constructor <init>(ZLorg/xbet/ui_common/dialogs/c;)V
    .locals 0

    iput-boolean p1, p0, Lorg/xbet/ui_common/dialogs/c$b;->a:Z

    iput-object p2, p0, Lorg/xbet/ui_common/dialogs/c$b;->b:Lorg/xbet/ui_common/dialogs/c;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onApplyWindowInsets(Landroid/view/View;Landroidx/core/view/F0;)Landroidx/core/view/F0;
    .locals 7

    .line 1
    iget-object p1, p0, Lorg/xbet/ui_common/dialogs/c$b;->b:Lorg/xbet/ui_common/dialogs/c;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->requireView()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {}, Landroidx/core/view/F0$o;->h()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget v2, p1, LI0/d;->b:I

    .line 16
    .line 17
    const/16 v5, 0xd

    .line 18
    .line 19
    const/4 v6, 0x0

    .line 20
    const/4 v1, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/ui_common/dialogs/c$b;->b:Lorg/xbet/ui_common/dialogs/c;

    .line 27
    .line 28
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->requireView()Landroid/view/View;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-static {}, Landroidx/core/view/F0$o;->g()I

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    invoke-virtual {p2, v0}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iget v0, v0, LI0/d;->d:I

    .line 41
    .line 42
    invoke-virtual {p1}, Landroid/view/View;->getPaddingLeft()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-virtual {p1}, Landroid/view/View;->getPaddingTop()I

    .line 47
    .line 48
    .line 49
    move-result v2

    .line 50
    invoke-virtual {p1}, Landroid/view/View;->getPaddingRight()I

    .line 51
    .line 52
    .line 53
    move-result v3

    .line 54
    invoke-virtual {p1, v1, v2, v3, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 55
    .line 56
    .line 57
    iget-boolean p1, p0, Lorg/xbet/ui_common/dialogs/c$b;->a:Z

    .line 58
    .line 59
    if-eqz p1, :cond_0

    .line 60
    .line 61
    sget-object p1, Landroidx/core/view/F0;->b:Landroidx/core/view/F0;

    .line 62
    .line 63
    return-object p1

    .line 64
    :cond_0
    return-object p2
.end method
