.class public final LPx0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LPx0/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0018\u0008\u0001\u0018\u0000 \u00102\u00020\u0001:\u0001%B#\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001d\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0015\u0010\u0010\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J%\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u001d\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0015\u0010\u001a\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u001a\u0010\u0011J\u0015\u0010\u001b\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u001b\u0010\u0011J%\u0010\u001e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0015\u0010 \u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008 \u0010\u0011J\u0015\u0010!\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008!\u0010\u0011J\u001d\u0010#\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\u0012\u00a2\u0006\u0004\u0008#\u0010$J\u0015\u0010%\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008%\u0010\u0011J\u0015\u0010&\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008&\u0010\u0011J\u0015\u0010\'\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\'\u0010\u0011J\u0015\u0010(\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008(\u0010\u0011R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010)R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010*R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010+\u00a8\u0006,"
    }
    d2 = {
        "LPx0/a;",
        "",
        "LHg/d;",
        "specialEventAnalytics",
        "LfS/a;",
        "specialEventFatmanLogger",
        "",
        "screenName",
        "<init>",
        "(LHg/d;LfS/a;Ljava/lang/String;)V",
        "",
        "eventId",
        "teamClId",
        "",
        "e",
        "(II)V",
        "d",
        "(I)V",
        "",
        "sportId",
        "",
        "isLive",
        "i",
        "(JIZ)V",
        "g",
        "(JI)V",
        "m",
        "h",
        "bannerId",
        "translateId",
        "l",
        "(IILjava/lang/String;)V",
        "b",
        "c",
        "stadiumId",
        "n",
        "(IJ)V",
        "a",
        "j",
        "k",
        "f",
        "LHg/d;",
        "LfS/a;",
        "Ljava/lang/String;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:LPx0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final e:I


# instance fields
.field public final a:LHg/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LfS/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LPx0/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LPx0/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LPx0/a;->d:LPx0/a$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LPx0/a;->e:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>(LHg/d;LfS/a;Ljava/lang/String;)V
    .locals 0
    .param p1    # LHg/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LfS/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LPx0/a;->a:LHg/d;

    .line 5
    .line 6
    iput-object p2, p0, LPx0/a;->b:LfS/a;

    .line 7
    .line 8
    iput-object p3, p0, LPx0/a;->c:Ljava/lang/String;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LHg/d;->a(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-interface {v0, v2, p1, v1}, LfS/a;->z(Ljava/lang/String;ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final b(I)V
    .locals 4

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    const-string v2, "leaderboard"

    .line 6
    .line 7
    const-string v3, "tournament"

    .line 8
    .line 9
    invoke-interface {v0, v1, p1, v2, v3}, LfS/a;->d(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 13
    .line 14
    sget-object v1, Lorg/xbet/analytics/domain/scope/special_event/TourEvent;->LEADERBOARD:Lorg/xbet/analytics/domain/scope/special_event/TourEvent;

    .line 15
    .line 16
    invoke-virtual {v0, p1, v1, v3}, LHg/d;->r(ILorg/xbet/analytics/domain/scope/special_event/TourEvent;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final c(I)V
    .locals 4

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    const-string v2, "tournament_bracket"

    .line 6
    .line 7
    const-string v3, "tournament"

    .line 8
    .line 9
    invoke-interface {v0, v1, p1, v2, v3}, LfS/a;->d(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 13
    .line 14
    sget-object v1, Lorg/xbet/analytics/domain/scope/special_event/TourEvent;->TOURNAMENT_BRACKET:Lorg/xbet/analytics/domain/scope/special_event/TourEvent;

    .line 15
    .line 16
    invoke-virtual {v0, p1, v1, v3}, LHg/d;->r(ILorg/xbet/analytics/domain/scope/special_event/TourEvent;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final d(I)V
    .locals 4

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    const-string v2, "set"

    .line 6
    .line 7
    const-string v3, "tournament"

    .line 8
    .line 9
    invoke-interface {v0, v1, p1, v2, v3}, LfS/a;->s(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 13
    .line 14
    sget-object v1, LHg/a$b;->b:LHg/a$b;

    .line 15
    .line 16
    invoke-virtual {v0, p1, v1, v3}, LHg/d;->z(ILHg/a;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final e(II)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    new-instance v1, LHg/a$a;

    .line 4
    .line 5
    invoke-direct {v1, p2}, LHg/a$a;-><init>(I)V

    .line 6
    .line 7
    .line 8
    const-string v2, "tournament"

    .line 9
    .line 10
    invoke-virtual {v0, p1, v1, v2}, LHg/d;->z(ILHg/a;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 14
    .line 15
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 16
    .line 17
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    invoke-interface {v0, v1, p1, p2, v2}, LfS/a;->s(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final f(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LHg/d;->k(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-interface {v0, v2, p1, v1}, LfS/a;->i(Ljava/lang/String;ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final g(JI)V
    .locals 13

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    sget-object v5, LgS/a$g;->a:LgS/a$g;

    .line 6
    .line 7
    const-string v6, "results"

    .line 8
    .line 9
    move-wide v2, p1

    .line 10
    move/from16 v4, p3

    .line 11
    .line 12
    invoke-interface/range {v0 .. v6}, LfS/a;->y(Ljava/lang/String;JILgS/a;Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    iget-object v7, p0, LPx0/a;->a:LHg/d;

    .line 16
    .line 17
    new-instance v11, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;

    .line 18
    .line 19
    invoke-direct {v11, v4}, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;-><init>(I)V

    .line 20
    .line 21
    .line 22
    const-string v12, "results"

    .line 23
    .line 24
    move-wide v8, p1

    .line 25
    move v10, v4

    .line 26
    invoke-virtual/range {v7 .. v12}, LHg/d;->p(JILorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final h(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    const-string v2, "tournament"

    .line 6
    .line 7
    invoke-interface {v0, v1, p1, v2}, LfS/a;->m(Ljava/lang/String;ILjava/lang/String;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 11
    .line 12
    invoke-virtual {v0, p1, v2}, LHg/d;->n(ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final i(JIZ)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LPx0/a;->b:LfS/a;

    .line 4
    .line 5
    iget-object v2, v0, LPx0/a;->c:Ljava/lang/String;

    .line 6
    .line 7
    sget-object v6, LgS/a$g;->a:LgS/a$g;

    .line 8
    .line 9
    const-string v8, "line"

    .line 10
    .line 11
    const-string v9, "live"

    .line 12
    .line 13
    if-eqz p4, :cond_0

    .line 14
    .line 15
    move-object v7, v9

    .line 16
    :goto_0
    move-wide/from16 v3, p1

    .line 17
    .line 18
    move/from16 v5, p3

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_0
    move-object v7, v8

    .line 22
    goto :goto_0

    .line 23
    :goto_1
    invoke-interface/range {v1 .. v7}, LfS/a;->y(Ljava/lang/String;JILgS/a;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    iget-object v10, v0, LPx0/a;->a:LHg/d;

    .line 27
    .line 28
    new-instance v14, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;

    .line 29
    .line 30
    move/from16 v13, p3

    .line 31
    .line 32
    invoke-direct {v14, v13}, Lorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint$TournamentScreen;-><init>(I)V

    .line 33
    .line 34
    .line 35
    if-eqz p4, :cond_1

    .line 36
    .line 37
    move-object v15, v9

    .line 38
    :goto_2
    move-wide/from16 v11, p1

    .line 39
    .line 40
    goto :goto_3

    .line 41
    :cond_1
    move-object v15, v8

    .line 42
    goto :goto_2

    .line 43
    :goto_3
    invoke-virtual/range {v10 .. v15}, LHg/d;->p(JILorg/xbet/analytics/domain/AnalyticsEventModel$EntryPointType$SpecialEventScreenSection$SportGamesEntryPoint;Ljava/lang/String;)V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public final j(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LHg/d;->q(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-interface {v0, v2, p1, v1}, LfS/a;->f(Ljava/lang/String;ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final k(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LHg/d;->u(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-interface {v0, v2, p1, v1}, LfS/a;->c(Ljava/lang/String;ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final l(IILjava/lang/String;)V
    .locals 6
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 2
    .line 3
    iget-object v1, p0, LPx0/a;->c:Ljava/lang/String;

    .line 4
    .line 5
    const-string v4, "tournament"

    .line 6
    .line 7
    move v2, p1

    .line 8
    move v3, p2

    .line 9
    move-object v5, p3

    .line 10
    invoke-interface/range {v0 .. v5}, LfS/a;->D(Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, LPx0/a;->a:LHg/d;

    .line 14
    .line 15
    const-string p2, "tournament"

    .line 16
    .line 17
    invoke-virtual {p1, v2, v3, p2, v5}, LHg/d;->w(IILjava/lang/String;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final m(I)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LHg/d;->x(ILjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-interface {v0, v2, p1, v1}, LfS/a;->t(Ljava/lang/String;ILjava/lang/String;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final n(IJ)V
    .locals 3

    .line 1
    iget-object v0, p0, LPx0/a;->a:LHg/d;

    .line 2
    .line 3
    const-string v1, "tournament"

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2, p3, v1}, LHg/d;->E(IJLjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LPx0/a;->b:LfS/a;

    .line 9
    .line 10
    iget-object v2, p0, LPx0/a;->c:Ljava/lang/String;

    .line 11
    .line 12
    invoke-static {p2, p3}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    invoke-interface {v0, v2, p1, p2, v1}, LfS/a;->x(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method
