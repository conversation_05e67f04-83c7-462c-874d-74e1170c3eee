.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Le31/f;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$a;,
        Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0018\u0008\u0007\u0018\u0000 22\u00020\u00012\u00020\u0002:\u0001\"B\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001d\u0010\u0011\u001a\u00020\u000b2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000eH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001d\u0010\u0015\u001a\u00020\u000b2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0015\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010\u001f\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u001f\u0010\rJ\u001d\u0010!\u001a\u00020\u000b2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u0013H\u0016\u00a2\u0006\u0004\u0008!\u0010\u0016J+\u0010\"\u001a\u00020\u000b2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u0013H\u0016\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\u000bH\u0016\u00a2\u0006\u0004\u0008$\u0010%J\u000f\u0010&\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008&\u0010%R\u0016\u0010(\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\"\u0010\'R\u0016\u0010+\u001a\u00020\t8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0018\u0010-\u001a\u0004\u0018\u00010\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010,R\u001c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u001c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u00101\u00a8\u00063"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;",
        "Landroid/widget/FrameLayout;",
        "Le31/f;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Le31/b;",
        "model",
        "",
        "setModelInternal",
        "(Le31/b;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "stopTimerFlow",
        "setStopTimerFlow",
        "(Lkotlinx/coroutines/flow/e;)V",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "setTimeOutCallback",
        "(Lkotlin/jvm/functions/Function0;)V",
        "",
        "style",
        "setStyleName",
        "(Ljava/lang/String;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "styleType",
        "setStyleType",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V",
        "setModel",
        "callback",
        "setOnTimerExpiredListener",
        "a",
        "(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V",
        "clear",
        "()V",
        "c",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "currentStyle",
        "b",
        "Le31/b;",
        "currentModel",
        "Le31/f;",
        "currentStyledView",
        "d",
        "Lkotlinx/coroutines/flow/e;",
        "e",
        "Lkotlin/jvm/functions/Function0;",
        "f",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final f:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final g:I


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Le31/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Le31/f;

.field public d:Lkotlinx/coroutines/flow/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->f:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->g:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->CARDS_L:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 5
    new-instance v0, Le31/d;

    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    invoke-direct {v0, v1}, Le31/d;-><init>(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->b:Le31/b;

    const/4 v0, 0x0

    .line 6
    new-array v1, v0, [Ljava/lang/Boolean;

    invoke-static {v1}, Lkotlinx/coroutines/flow/g;->Y([Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    move-result-object v1

    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->d:Lkotlinx/coroutines/flow/e;

    .line 7
    new-instance v1, Le31/g;

    invoke-direct {v1}, Le31/g;-><init>()V

    iput-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->e:Lkotlin/jvm/functions/Function0;

    .line 8
    sget-object v1, LS11/h;->DSAggregatorTournamentTimer:[I

    .line 9
    invoke-virtual {p1, p2, v1, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    .line 10
    sget p2, LS11/h;->DSAggregatorTournamentTimer_aggregatorTournamentTimerType:I

    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    move-result v0

    .line 12
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p2

    .line 13
    invoke-static {p2}, Le31/e;->a(I)Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    move-result-object p2

    .line 14
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 15
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 16
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c()V

    .line 17
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->b:Le31/b;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->setModel(Le31/b;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic b()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->d()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static final d()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method private final setModelInternal(Le31/b;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1}, Le31/f;->setModel(Le31/b;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    instance-of v0, p1, Le31/a;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    check-cast p1, Le31/a;

    .line 13
    .line 14
    invoke-virtual {p1}, Le31/a;->b()Z

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    if-eqz p1, :cond_1

    .line 19
    .line 20
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 21
    .line 22
    if-eqz p1, :cond_1

    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->d:Lkotlinx/coroutines/flow/e;

    .line 25
    .line 26
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->e:Lkotlin/jvm/functions/Function0;

    .line 27
    .line 28
    invoke-interface {p1, v0, v1}, Le31/f;->a(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    :cond_1
    return-void
.end method

.method private final setStopTimerFlow(Lkotlinx/coroutines/flow/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->d:Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    return-void
.end method

.method private final setTimeOutCallback(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method


# virtual methods
.method public a(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlinx/coroutines/flow/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p1, p2}, Le31/f;->a(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final c()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_3

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_2

    .line 16
    .line 17
    const/4 v1, 0x3

    .line 18
    if-eq v0, v1, :cond_1

    .line 19
    .line 20
    const/4 v1, 0x4

    .line 21
    if-ne v0, v1, :cond_0

    .line 22
    .line 23
    new-instance v2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    const/4 v6, 0x6

    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 38
    .line 39
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 40
    .line 41
    new-instance v2, Ljava/lang/StringBuilder;

    .line 42
    .line 43
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 44
    .line 45
    .line 46
    const-string v3, "Unknown view type "

    .line 47
    .line 48
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 49
    .line 50
    .line 51
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 52
    .line 53
    .line 54
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw v0

    .line 62
    :cond_1
    new-instance v2, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentVertical;

    .line 63
    .line 64
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 65
    .line 66
    .line 67
    move-result-object v3

    .line 68
    const/4 v6, 0x6

    .line 69
    const/4 v7, 0x0

    .line 70
    const/4 v4, 0x0

    .line 71
    const/4 v5, 0x0

    .line 72
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentVertical;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 73
    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_2
    new-instance v3, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerCardsL;

    .line 77
    .line 78
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 79
    .line 80
    .line 81
    move-result-object v4

    .line 82
    const/4 v7, 0x6

    .line 83
    const/4 v8, 0x0

    .line 84
    const/4 v5, 0x0

    .line 85
    const/4 v6, 0x0

    .line 86
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerCardsL;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 87
    .line 88
    .line 89
    move-object v2, v3

    .line 90
    goto :goto_0

    .line 91
    :cond_3
    new-instance v4, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerCardsS;

    .line 92
    .line 93
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 94
    .line 95
    .line 96
    move-result-object v5

    .line 97
    const/4 v8, 0x6

    .line 98
    const/4 v9, 0x0

    .line 99
    const/4 v6, 0x0

    .line 100
    const/4 v7, 0x0

    .line 101
    invoke-direct/range {v4 .. v9}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerCardsS;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 102
    .line 103
    .line 104
    move-object v2, v4

    .line 105
    :goto_0
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 106
    .line 107
    .line 108
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 109
    .line 110
    .line 111
    iput-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 112
    .line 113
    return-void
.end method

.method public clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Le31/f;->clear()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public setModel(Le31/b;)V
    .locals 0
    .param p1    # Le31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->b:Le31/b;

    .line 2
    .line 3
    invoke-interface {p1}, Le31/b;->a()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->setStyleType(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c:Le31/f;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {v0, p1}, Le31/f;->setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final setStyleName(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->Companion:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType$a;->a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->setStyleType(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final setStyleType(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 2
    .line 3
    if-eq v0, p1, :cond_0

    .line 4
    .line 5
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->c()V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->b:Le31/b;

    .line 11
    .line 12
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/DSAggregatorTournamentTimer;->setModelInternal(Le31/b;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method
