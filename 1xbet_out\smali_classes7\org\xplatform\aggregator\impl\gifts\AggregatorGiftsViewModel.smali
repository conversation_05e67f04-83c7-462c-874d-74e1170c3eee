.class public final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$a;,
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$b;,
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;,
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;,
        Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00e8\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008+\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0008\u0000\u0018\u0000 \u00ae\u00022\u00020\u0001:\u0008\u00af\u0002\u00b0\u0002\u00b1\u0002\u00b2\u0002B\u00c9\u0002\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u0012\u0006\u0010A\u001a\u00020@\u0012\u0006\u0010C\u001a\u00020B\u0012\u0006\u0010E\u001a\u00020D\u0012\u0006\u0010G\u001a\u00020F\u0012\u0006\u0010I\u001a\u00020H\u0012\u0006\u0010K\u001a\u00020J\u0012\u0006\u0010M\u001a\u00020L\u0012\u0006\u0010O\u001a\u00020N\u0012\u0006\u0010Q\u001a\u00020P\u00a2\u0006\u0004\u0008R\u0010SJ\u000f\u0010U\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008U\u0010VJ\u0017\u0010Y\u001a\u00020T2\u0006\u0010X\u001a\u00020WH\u0002\u00a2\u0006\u0004\u0008Y\u0010ZJ\u000f\u0010\\\u001a\u00020[H\u0002\u00a2\u0006\u0004\u0008\\\u0010]J\u000f\u0010^\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008^\u0010VJ\u001f\u0010c\u001a\u00020T2\u0006\u0010`\u001a\u00020_2\u0006\u0010b\u001a\u00020aH\u0002\u00a2\u0006\u0004\u0008c\u0010dJ\u001f\u0010i\u001a\u00020T2\u0006\u0010f\u001a\u00020e2\u0006\u0010h\u001a\u00020gH\u0002\u00a2\u0006\u0004\u0008i\u0010jJ\u0010\u0010k\u001a\u00020TH\u0082@\u00a2\u0006\u0004\u0008k\u0010lJ\u000f\u0010m\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008m\u0010VJ\u000f\u0010n\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008n\u0010VJ\u001f\u0010r\u001a\u00020T2\u0006\u0010o\u001a\u00020W2\u0006\u0010q\u001a\u00020pH\u0002\u00a2\u0006\u0004\u0008r\u0010sJ\u000f\u0010t\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008t\u0010VJ\u0017\u0010u\u001a\u00020T2\u0006\u0010X\u001a\u00020WH\u0002\u00a2\u0006\u0004\u0008u\u0010ZJ\u001f\u0010v\u001a\u00020T2\u0006\u0010X\u001a\u00020W2\u0006\u0010q\u001a\u00020pH\u0002\u00a2\u0006\u0004\u0008v\u0010sJ\u000f\u0010w\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008w\u0010VJ\u000f\u0010x\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008x\u0010VJ\u000f\u0010y\u001a\u00020TH\u0002\u00a2\u0006\u0004\u0008y\u0010VJ+\u0010~\u001a\u00020T2\u000c\u0010|\u001a\u0008\u0012\u0004\u0012\u00020{0z2\u000c\u0010}\u001a\u0008\u0012\u0004\u0012\u00020{0zH\u0002\u00a2\u0006\u0004\u0008~\u0010\u007fJ\u0011\u0010\u0080\u0001\u001a\u00020TH\u0002\u00a2\u0006\u0005\u0008\u0080\u0001\u0010VJ;\u0010\u0084\u0001\u001a\u00020T2\u0007\u0010\u0081\u0001\u001a\u00020g2\u0007\u0010\u0082\u0001\u001a\u00020g2\u0007\u0010\u0083\u0001\u001a\u00020g2\u000c\u0010}\u001a\u0008\u0012\u0004\u0012\u00020{0zH\u0002\u00a2\u0006\u0006\u0008\u0084\u0001\u0010\u0085\u0001J\u0011\u0010\u0086\u0001\u001a\u00020TH\u0002\u00a2\u0006\u0005\u0008\u0086\u0001\u0010VJ>\u0010\u008e\u0001\u001a\t\u0012\u0005\u0012\u00030\u008d\u00010z2\u000e\u0010\u0088\u0001\u001a\t\u0012\u0005\u0012\u00030\u0087\u00010z2\u0008\u0010\u008a\u0001\u001a\u00030\u0089\u00012\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u0001H\u0082@\u00a2\u0006\u0006\u0008\u008e\u0001\u0010\u008f\u0001J&\u0010\u0092\u0001\u001a\u0004\u0018\u00010g2\u0007\u0010\u0090\u0001\u001a\u00020a2\u0007\u0010\u0091\u0001\u001a\u00020aH\u0003\u00a2\u0006\u0006\u0008\u0092\u0001\u0010\u0093\u0001J+\u0010\u0095\u0001\u001a\u00020T2\r\u0010\u0094\u0001\u001a\u0008\u0012\u0004\u0012\u00020a0z2\u0008\u0010\u008a\u0001\u001a\u00030\u0089\u0001H\u0002\u00a2\u0006\u0006\u0008\u0095\u0001\u0010\u0096\u0001J\u0013\u0010\u0098\u0001\u001a\u00030\u0097\u0001H\u0002\u00a2\u0006\u0006\u0008\u0098\u0001\u0010\u0099\u0001J\u001c\u0010\u009c\u0001\u001a\u00020T2\u0008\u0010\u009b\u0001\u001a\u00030\u009a\u0001H\u0002\u00a2\u0006\u0006\u0008\u009c\u0001\u0010\u009d\u0001J\u0018\u0010\u009e\u0001\u001a\u0008\u0012\u0004\u0012\u00020{0zH\u0002\u00a2\u0006\u0006\u0008\u009e\u0001\u0010\u009f\u0001J\u0011\u0010\u00a0\u0001\u001a\u00020TH\u0002\u00a2\u0006\u0005\u0008\u00a0\u0001\u0010VJ\"\u0010\u00a2\u0001\u001a\u00030\u0089\u00012\r\u0010\u00a1\u0001\u001a\u0008\u0012\u0004\u0012\u00020{0zH\u0002\u00a2\u0006\u0006\u0008\u00a2\u0001\u0010\u00a3\u0001J\u0011\u0010\u00a4\u0001\u001a\u00020TH\u0016\u00a2\u0006\u0005\u0008\u00a4\u0001\u0010VJ\u0011\u0010\u00a5\u0001\u001a\u00020TH\u0016\u00a2\u0006\u0005\u0008\u00a5\u0001\u0010VJ\u001c\u0010\u00a6\u0001\u001a\u00020T2\u0008\u0010\u009b\u0001\u001a\u00030\u009a\u0001H\u0016\u00a2\u0006\u0006\u0008\u00a6\u0001\u0010\u009d\u0001J\u000f\u0010\u00a7\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00a7\u0001\u0010VJ\u0011\u0010\u00a8\u0001\u001a\u00020TH\u0014\u00a2\u0006\u0005\u0008\u00a8\u0001\u0010VJ\u0018\u0010\u00ab\u0001\u001a\n\u0012\u0005\u0012\u00030\u00aa\u00010\u00a9\u0001\u00a2\u0006\u0006\u0008\u00ab\u0001\u0010\u00ac\u0001J\u0018\u0010\u00ae\u0001\u001a\n\u0012\u0005\u0012\u00030\u00ad\u00010\u00a9\u0001\u00a2\u0006\u0006\u0008\u00ae\u0001\u0010\u00ac\u0001J\u0018\u0010\u00af\u0001\u001a\n\u0012\u0005\u0012\u00030\u0089\u00010\u00a9\u0001\u00a2\u0006\u0006\u0008\u00af\u0001\u0010\u00ac\u0001J\u0018\u0010\u00b1\u0001\u001a\n\u0012\u0005\u0012\u00030\u00b0\u00010\u00a9\u0001\u00a2\u0006\u0006\u0008\u00b1\u0001\u0010\u00ac\u0001J\u000f\u0010\u00b2\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00b2\u0001\u0010VJ\u000f\u0010\u00b3\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00b3\u0001\u0010VJ\u001a\u0010\u00b6\u0001\u001a\u00020T2\u0008\u0010\u00b5\u0001\u001a\u00030\u00b4\u0001\u00a2\u0006\u0006\u0008\u00b6\u0001\u0010\u00b7\u0001J\u0010\u0010\u00b8\u0001\u001a\u00020g\u00a2\u0006\u0006\u0008\u00b8\u0001\u0010\u00b9\u0001J\u0019\u0010\u00bb\u0001\u001a\u00020T2\u0007\u0010\u00ba\u0001\u001a\u00020g\u00a2\u0006\u0006\u0008\u00bb\u0001\u0010\u00bc\u0001J!\u0010\u00be\u0001\u001a\u00020T2\u0007\u0010`\u001a\u00030\u00bd\u00012\u0006\u0010b\u001a\u00020a\u00a2\u0006\u0006\u0008\u00be\u0001\u0010\u00bf\u0001J\u000f\u0010\u00c0\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00c0\u0001\u0010VJ\u000f\u0010\u00c1\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00c1\u0001\u0010VJ\u000f\u0010\u00c2\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00c2\u0001\u0010VJ\u000f\u0010\u00c3\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00c3\u0001\u0010VJ\u0019\u0010\u00c5\u0001\u001a\u00020T2\u0007\u0010`\u001a\u00030\u00c4\u0001\u00a2\u0006\u0006\u0008\u00c5\u0001\u0010\u00c6\u0001J)\u0010\u00c8\u0001\u001a\u00020T2\u0006\u0010q\u001a\u00020p2\u0007\u0010f\u001a\u00030\u00c7\u00012\u0006\u0010X\u001a\u00020W\u00a2\u0006\u0006\u0008\u00c8\u0001\u0010\u00c9\u0001J\u000f\u0010\u00ca\u0001\u001a\u00020T\u00a2\u0006\u0005\u0008\u00ca\u0001\u0010VJ,\u0010\u00ce\u0001\u001a\u00020T2\u0007\u0010\u00ba\u0001\u001a\u00020a2\u0007\u0010\u00cb\u0001\u001a\u00020a2\u0008\u0010\u00cd\u0001\u001a\u00030\u00cc\u0001\u00a2\u0006\u0006\u0008\u00ce\u0001\u0010\u00cf\u0001J\"\u0010\u00d1\u0001\u001a\u00020T2\u0007\u0010\u00d0\u0001\u001a\u00020a2\u0007\u0010\u0090\u0001\u001a\u00020a\u00a2\u0006\u0006\u0008\u00d1\u0001\u0010\u00d2\u0001J,\u0010\u00d4\u0001\u001a\u00020T2\u0007\u0010\u00d0\u0001\u001a\u00020a2\u0007\u0010\u0090\u0001\u001a\u00020a2\u0008\u0010\u00d3\u0001\u001a\u00030\u0089\u0001\u00a2\u0006\u0006\u0008\u00d4\u0001\u0010\u00d5\u0001R\u0016\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d6\u0001\u0010\u00d7\u0001R\u0016\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0001\u0010\u00d8\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u00d9\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a2\u0001\u0010\u00da\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ce\u0001\u0010\u00db\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d4\u0001\u0010\u00dc\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d1\u0001\u0010\u00dd\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0001\u0010\u00de\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ca\u0001\u0010\u00df\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00e0\u0001R\u0015\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008t\u0010\u00e1\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00be\u0001\u0010\u00e2\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c5\u0001\u0010\u00e3\u0001R\u0015\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008c\u0010\u00e4\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e5\u0001\u0010\u00e6\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00e7\u0001R\u0015\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008u\u0010\u00e8\u0001R\u0015\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008v\u0010\u00e9\u0001R\u0015\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008r\u0010\u00ea\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a0\u0001\u0010\u00eb\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bb\u0001\u0010\u00ec\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c3\u0001\u0010\u00ed\u0001R\u0015\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008k\u0010\u00ee\u0001R\u0015\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008x\u0010\u00ef\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0080\u0001\u0010\u00f0\u0001R\u0016\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c8\u0001\u0010\u00f1\u0001R\u0015\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008y\u0010\u00f2\u0001R\u0016\u00109\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00f3\u0001R\u0016\u0010;\u001a\u00020:8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u00f4\u0001R\u0016\u0010=\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0001\u0010\u00f5\u0001R\u0018\u0010\u008c\u0001\u001a\u00030\u008b\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f6\u0001\u0010\u00f7\u0001R\u0018\u0010\u00fa\u0001\u001a\u00030\u00f8\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00f9\u0001R\u0018\u0010\u00fc\u0001\u001a\u00020a8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008n\u0010\u00fb\u0001R\u001a\u0010\u00fe\u0001\u001a\u00030\u00b4\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c1\u0001\u0010\u00fd\u0001R\u001a\u0010\u00ff\u0001\u001a\u00030\u00b4\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00b2\u0001\u0010\u00fd\u0001R\u001a\u0010\u0081\u0002\u001a\u00030\u0089\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0080\u0002R\u001a\u0010\u0083\u0002\u001a\u0004\u0018\u00010g8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008U\u0010\u0082\u0002R\u001c\u0010\u0087\u0002\u001a\u0005\u0018\u00010\u0084\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0002\u0010\u0086\u0002R\u001c\u0010\u008b\u0002\u001a\u0005\u0018\u00010\u0088\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0002\u0010\u008a\u0002R\u001c\u0010\u008d\u0002\u001a\u0005\u0018\u00010\u0088\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008c\u0002\u0010\u008a\u0002R\u001c\u0010\u008f\u0002\u001a\u0005\u0018\u00010\u0088\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0002\u0010\u008a\u0002R\u001c\u0010\u0091\u0002\u001a\u0005\u0018\u00010\u0088\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0002\u0010\u008a\u0002R\u001c\u0010\u0095\u0002\u001a\u0005\u0018\u00010\u0092\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0002\u0010\u0094\u0002R\u001a\u0010\u0097\u0002\u001a\u00030\u0089\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0002\u0010\u0080\u0002R$\u0010\u009b\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020{0z0\u0098\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0002\u0010\u009a\u0002R$\u0010\u009d\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020{0z0\u0098\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009c\u0002\u0010\u009a\u0002R\u001f\u0010\u009f\u0002\u001a\n\u0012\u0005\u0012\u00030\u0089\u00010\u0098\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0002\u0010\u009a\u0002R\u001f\u0010\u00a3\u0002\u001a\n\u0012\u0005\u0012\u00030\u00aa\u00010\u00a0\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a1\u0002\u0010\u00a2\u0002R\u001f\u0010\u00a5\u0002\u001a\n\u0012\u0005\u0012\u00030\u0089\u00010\u0098\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a4\u0002\u0010\u009a\u0002R\u001f\u0010\u00a7\u0002\u001a\n\u0012\u0005\u0012\u00030\u00b0\u00010\u0098\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a6\u0002\u0010\u009a\u0002R)\u0010\u00ad\u0002\u001a\u000f\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020{0z0\u00a8\u00028\u0006\u00a2\u0006\u0010\n\u0006\u0008\u00a9\u0002\u0010\u00aa\u0002\u001a\u0006\u0008\u00ab\u0002\u0010\u00ac\u0002\u00a8\u0006\u00b3\u0002"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;",
        "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;",
        "giftsDelegate",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "setNeedFavoritesReUpdateUseCase",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/h;",
        "editBonusesStateScenario",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/a;",
        "addAggregatorLastActionScenario",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/f;",
        "configureActiveBonusChipIdScenario",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/d;",
        "clearActiveBonusChipIdScenario",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/l;",
        "removeTimeOutBonusUseCase",
        "Lia1/f;",
        "giftsInfo",
        "LwX0/C;",
        "routerHolder",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lf81/a;",
        "addFavoriteUseCase",
        "Lfk/j;",
        "getBalanceByIdUseCase",
        "Lf81/d;",
        "removeFavoriteUseCase",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "openGameDelegate",
        "Lf81/c;",
        "getGamesForNonAuthUseCase",
        "Lv81/g;",
        "getAggregatorGameUseCase",
        "Lorg/xbet/analytics/domain/scope/T;",
        "giftAnalytics",
        "Lfk/o;",
        "observeScreenBalanceUseCase",
        "Le81/c;",
        "getFavoriteGamesFlowScenario",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/n;",
        "updateLocalLeftTimeUseCase",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lm8/a;",
        "dispatchers",
        "LP91/b;",
        "aggregatorNavigator",
        "LwX0/a;",
        "screensProvider",
        "LHX0/e;",
        "resourceManager",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LUR/a;",
        "promoFatmanLogger",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LGg/a;",
        "searchAnalytics",
        "Lorg/xbet/analytics/domain/scope/I;",
        "depositAnalytics",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LAR/a;",
        "depositFatmanLogger",
        "LZR/a;",
        "searchFatmanLogger",
        "Lfk/s;",
        "hasUserScreenBalanceUseCase",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "LC81/f;",
        "setDailyTaskRefreshScenario",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/gifts/usecases/h;Lorg/xplatform/aggregator/impl/gifts/usecases/a;Lorg/xplatform/aggregator/impl/gifts/usecases/f;Lorg/xplatform/aggregator/impl/gifts/usecases/d;Lorg/xplatform/aggregator/impl/gifts/usecases/l;Lia1/f;LwX0/C;Lorg/xbet/ui_common/utils/M;Lf81/a;Lfk/j;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/c;Lv81/g;Lorg/xbet/analytics/domain/scope/T;Lfk/o;Le81/c;Lorg/xplatform/aggregator/impl/gifts/usecases/n;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;LP91/b;LwX0/a;LHX0/e;Lek/d;Lek/f;Lp9/c;LUR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LAR/a;LZR/a;Lfk/s;Lgk0/a;Lfk/l;LC81/f;)V",
        "",
        "i6",
        "()V",
        "Lha1/b;",
        "callbackClickModelContainer",
        "k5",
        "(Lha1/b;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "s5",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "w5",
        "Lorg/xplatform/aggregator/core/AggregatorGame;",
        "game",
        "",
        "balanceId",
        "L5",
        "(Lorg/xplatform/aggregator/core/AggregatorGame;J)V",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "state",
        "",
        "bonusId",
        "l5",
        "(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V",
        "U5",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "v5",
        "e6",
        "clickModel",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "partitionType",
        "Q5",
        "(Lha1/b;Lorg/xplatform/aggregator/api/model/PartitionType;)V",
        "I5",
        "O5",
        "P5",
        "x5",
        "V5",
        "Y5",
        "",
        "LVX0/i;",
        "giftsByAccount",
        "giftsByType",
        "i5",
        "(Ljava/util/List;Ljava/util/List;)V",
        "W5",
        "allGiftsCount",
        "bonusesCount",
        "freeSpinsCount",
        "b6",
        "(IIILjava/util/List;)V",
        "u5",
        "Ld81/b;",
        "items",
        "",
        "isLoggedIn",
        "Lek0/o;",
        "remoteConfigModel",
        "Lma1/b;",
        "A5",
        "(Ljava/util/List;ZLek0/o;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "categoryId",
        "categoryPartId",
        "o5",
        "(JJ)Ljava/lang/Integer;",
        "favoriteIdList",
        "h6",
        "(Ljava/util/List;Z)V",
        "Lma1/f;",
        "a6",
        "()Lma1/f;",
        "",
        "throwable",
        "F5",
        "(Ljava/lang/Throwable;)V",
        "q5",
        "()Ljava/util/List;",
        "R5",
        "gifts",
        "B5",
        "(Ljava/util/List;)Z",
        "R3",
        "d4",
        "e4",
        "H5",
        "onCleared",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
        "m5",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
        "N5",
        "z5",
        "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;",
        "d6",
        "g6",
        "n5",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "type",
        "h5",
        "(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)V",
        "p5",
        "()I",
        "id",
        "S5",
        "(I)V",
        "Lma1/c;",
        "J5",
        "(Lma1/c;J)V",
        "Z5",
        "f6",
        "j5",
        "T5",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "K5",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;",
        "X5",
        "(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lha1/b;)V",
        "G5",
        "partId",
        "",
        "title",
        "C5",
        "(JJLjava/lang/String;)V",
        "gameId",
        "E5",
        "(JJ)V",
        "favorite",
        "D5",
        "(JJZ)V",
        "y5",
        "Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;",
        "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/h;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/a;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/f;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/d;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/l;",
        "Lia1/f;",
        "LwX0/C;",
        "Lorg/xbet/ui_common/utils/M;",
        "Lf81/a;",
        "Lfk/j;",
        "Lf81/d;",
        "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
        "M5",
        "Lf81/c;",
        "Lv81/g;",
        "Lorg/xbet/analytics/domain/scope/T;",
        "Lfk/o;",
        "Le81/c;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/n;",
        "LSX0/c;",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lm8/a;",
        "LP91/b;",
        "LwX0/a;",
        "LHX0/e;",
        "Lek/d;",
        "Lek/f;",
        "Lp9/c;",
        "LUR/a;",
        "c6",
        "Lek0/o;",
        "Lek0/a;",
        "Lek0/a;",
        "aggregatorModel",
        "J",
        "currentAccountId",
        "Lorg/xplatform/aggregator/api/navigation/GiftsChipType;",
        "currentActiveChipType",
        "primaryBalanceChipType",
        "Z",
        "topGamesIsLoaded",
        "Ljava/lang/Integer;",
        "bonusIdForDelete",
        "Lha1/c;",
        "j6",
        "Lha1/c;",
        "listOfChips",
        "Lkotlinx/coroutines/x0;",
        "k6",
        "Lkotlinx/coroutines/x0;",
        "connectionJob",
        "l6",
        "balanceChangesJob",
        "m6",
        "loadingGamesJob",
        "n6",
        "giftsConfigureJob",
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
        "o6",
        "Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;",
        "flowTimer",
        "p6",
        "giftsIsUpdate",
        "Lkotlinx/coroutines/flow/V;",
        "q6",
        "Lkotlinx/coroutines/flow/V;",
        "updateGiftsAdapterFlow",
        "r6",
        "updateTopGamesAdapterFlow",
        "s6",
        "showTopGamesFlow",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "t6",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "eventsStateFlow",
        "u6",
        "loadingFlow",
        "v6",
        "showNoConnectionErrorFlow",
        "Lkotlinx/coroutines/flow/f0;",
        "w6",
        "Lkotlinx/coroutines/flow/f0;",
        "r5",
        "()Lkotlinx/coroutines/flow/f0;",
        "giftsListFlow",
        "x6",
        "a",
        "c",
        "d",
        "b",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final x6:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lorg/xplatform/aggregator/impl/gifts/usecases/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lorg/xplatform/aggregator/impl/gifts/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lorg/xplatform/aggregator/impl/gifts/usecases/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D5:Lorg/xplatform/aggregator/impl/gifts/usecases/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E5:Lorg/xplatform/aggregator/impl/gifts/usecases/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F5:Lia1/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G5:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Lf81/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lfk/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Lf81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:Lf81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final N5:Lv81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final O5:Lorg/xbet/analytics/domain/scope/T;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P5:Lfk/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Q5:Le81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final R5:Lorg/xplatform/aggregator/impl/gifts/usecases/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S5:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final T5:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final U5:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V5:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final W5:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X5:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Y5:Lek/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final Z5:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a6:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b6:LUR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c6:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d6:Lek0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e6:J

.field public f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public volatile h6:Z

.field public i6:Ljava/lang/Integer;

.field public j6:Lha1/c;

.field public k6:Lkotlinx/coroutines/x0;

.field public l6:Lkotlinx/coroutines/x0;

.field public m6:Lkotlinx/coroutines/x0;

.field public n6:Lkotlinx/coroutines/x0;

.field public o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

.field public p6:Z

.field public final q6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t6:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final u6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v6:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w6:Lkotlinx/coroutines/flow/f0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x6:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$b;

    return-void
.end method

.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/gifts/usecases/h;Lorg/xplatform/aggregator/impl/gifts/usecases/a;Lorg/xplatform/aggregator/impl/gifts/usecases/f;Lorg/xplatform/aggregator/impl/gifts/usecases/d;Lorg/xplatform/aggregator/impl/gifts/usecases/l;Lia1/f;LwX0/C;Lorg/xbet/ui_common/utils/M;Lf81/a;Lfk/j;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/c;Lv81/g;Lorg/xbet/analytics/domain/scope/T;Lfk/o;Le81/c;Lorg/xplatform/aggregator/impl/gifts/usecases/n;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;LP91/b;LwX0/a;LHX0/e;Lek/d;Lek/f;Lp9/c;LUR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LAR/a;LZR/a;Lfk/s;Lgk0/a;Lfk/l;LC81/f;)V
    .locals 20
    .param p1    # Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/impl/gifts/usecases/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/impl/gifts/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/impl/gifts/usecases/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/impl/gifts/usecases/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/impl/gifts/usecases/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lia1/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lf81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lfk/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lf81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lf81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lv81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/analytics/domain/scope/T;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Le81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lorg/xplatform/aggregator/impl/gifts/usecases/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # LUR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p32    # LGg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p33    # Lorg/xbet/analytics/domain/scope/I;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p34    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p35    # LAR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p36    # LZR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p37    # Lfk/s;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p38    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p39    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p40    # LC81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v8, p9

    .line 4
    .line 5
    move-object/from16 v3, p10

    .line 6
    .line 7
    move-object/from16 v18, p18

    .line 8
    .line 9
    move-object/from16 v2, p22

    .line 10
    .line 11
    move-object/from16 v9, p23

    .line 12
    .line 13
    move-object/from16 v1, p24

    .line 14
    .line 15
    move-object/from16 v13, p26

    .line 16
    .line 17
    move-object/from16 v12, p27

    .line 18
    .line 19
    move-object/from16 v10, p28

    .line 20
    .line 21
    move-object/from16 v5, p29

    .line 22
    .line 23
    move-object/from16 v6, p32

    .line 24
    .line 25
    move-object/from16 v7, p33

    .line 26
    .line 27
    move-object/from16 v4, p34

    .line 28
    .line 29
    move-object/from16 v14, p35

    .line 30
    .line 31
    move-object/from16 v15, p36

    .line 32
    .line 33
    move-object/from16 v17, p37

    .line 34
    .line 35
    move-object/from16 v16, p38

    .line 36
    .line 37
    move-object/from16 v11, p39

    .line 38
    .line 39
    move-object/from16 v19, p40

    .line 40
    .line 41
    invoke-direct/range {v0 .. v19}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;-><init>(LP91/b;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lp9/c;LGg/a;Lorg/xbet/analytics/domain/scope/I;LwX0/C;Lm8/a;Lek/f;Lfk/l;Lek/d;LHX0/e;LAR/a;LZR/a;Lgk0/a;Lfk/s;Lfk/o;LC81/f;)V

    .line 42
    .line 43
    .line 44
    move-object/from16 v1, p1

    .line 45
    .line 46
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->y5:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 47
    .line 48
    move-object/from16 v1, p2

    .line 49
    .line 50
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 51
    .line 52
    move-object/from16 v1, p3

    .line 53
    .line 54
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->A5:Lorg/xplatform/aggregator/impl/gifts/usecases/h;

    .line 55
    .line 56
    move-object/from16 v1, p4

    .line 57
    .line 58
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->B5:Lorg/xplatform/aggregator/impl/gifts/usecases/a;

    .line 59
    .line 60
    move-object/from16 v1, p5

    .line 61
    .line 62
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->C5:Lorg/xplatform/aggregator/impl/gifts/usecases/f;

    .line 63
    .line 64
    move-object/from16 v1, p6

    .line 65
    .line 66
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->D5:Lorg/xplatform/aggregator/impl/gifts/usecases/d;

    .line 67
    .line 68
    move-object/from16 v1, p7

    .line 69
    .line 70
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->E5:Lorg/xplatform/aggregator/impl/gifts/usecases/l;

    .line 71
    .line 72
    move-object/from16 v1, p8

    .line 73
    .line 74
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->F5:Lia1/f;

    .line 75
    .line 76
    iput-object v8, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G5:LwX0/C;

    .line 77
    .line 78
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->H5:Lorg/xbet/ui_common/utils/M;

    .line 79
    .line 80
    move-object/from16 v2, p11

    .line 81
    .line 82
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->I5:Lf81/a;

    .line 83
    .line 84
    move-object/from16 v2, p12

    .line 85
    .line 86
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->J5:Lfk/j;

    .line 87
    .line 88
    move-object/from16 v2, p13

    .line 89
    .line 90
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->K5:Lf81/d;

    .line 91
    .line 92
    move-object/from16 v2, p14

    .line 93
    .line 94
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 95
    .line 96
    move-object/from16 v2, p15

    .line 97
    .line 98
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->M5:Lf81/c;

    .line 99
    .line 100
    move-object/from16 v2, p16

    .line 101
    .line 102
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->N5:Lv81/g;

    .line 103
    .line 104
    move-object/from16 v2, p17

    .line 105
    .line 106
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 107
    .line 108
    move-object/from16 v2, p18

    .line 109
    .line 110
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->P5:Lfk/o;

    .line 111
    .line 112
    move-object/from16 v2, p19

    .line 113
    .line 114
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Q5:Le81/c;

    .line 115
    .line 116
    move-object/from16 v2, p20

    .line 117
    .line 118
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R5:Lorg/xplatform/aggregator/impl/gifts/usecases/n;

    .line 119
    .line 120
    move-object/from16 v2, p21

    .line 121
    .line 122
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->S5:LSX0/c;

    .line 123
    .line 124
    move-object/from16 v2, p22

    .line 125
    .line 126
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->T5:Lorg/xbet/ui_common/utils/internet/a;

    .line 127
    .line 128
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->U5:Lm8/a;

    .line 129
    .line 130
    move-object/from16 v2, p24

    .line 131
    .line 132
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->V5:LP91/b;

    .line 133
    .line 134
    move-object/from16 v2, p25

    .line 135
    .line 136
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->W5:LwX0/a;

    .line 137
    .line 138
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5:LHX0/e;

    .line 139
    .line 140
    iput-object v12, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Y5:Lek/d;

    .line 141
    .line 142
    iput-object v10, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5:Lek/f;

    .line 143
    .line 144
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->a6:Lp9/c;

    .line 145
    .line 146
    move-object/from16 v2, p30

    .line 147
    .line 148
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 149
    .line 150
    invoke-interface/range {p31 .. p31}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 151
    .line 152
    .line 153
    move-result-object v2

    .line 154
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->c6:Lek0/o;

    .line 155
    .line 156
    invoke-virtual {v2}, Lek0/o;->o()Lek0/a;

    .line 157
    .line 158
    .line 159
    move-result-object v2

    .line 160
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d6:Lek0/a;

    .line 161
    .line 162
    sget-object v2, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->ALL:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 163
    .line 164
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 165
    .line 166
    sget-object v2, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->Companion:Lorg/xplatform/aggregator/api/navigation/GiftsChipType$a;

    .line 167
    .line 168
    invoke-virtual {v1}, Lia1/f;->a()I

    .line 169
    .line 170
    .line 171
    move-result v1

    .line 172
    invoke-virtual {v2, v1}, Lorg/xplatform/aggregator/api/navigation/GiftsChipType$a;->a(I)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 177
    .line 178
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 179
    .line 180
    .line 181
    move-result-object v1

    .line 182
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 187
    .line 188
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 189
    .line 190
    .line 191
    move-result-object v2

    .line 192
    invoke-static {v2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 193
    .line 194
    .line 195
    move-result-object v2

    .line 196
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 197
    .line 198
    sget-object v3, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 199
    .line 200
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 201
    .line 202
    .line 203
    move-result-object v4

    .line 204
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 205
    .line 206
    new-instance v5, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 207
    .line 208
    const/4 v6, 0x3

    .line 209
    const/4 v7, 0x0

    .line 210
    const/4 v8, 0x0

    .line 211
    invoke-direct {v5, v7, v8, v6, v8}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 212
    .line 213
    .line 214
    iput-object v5, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->t6:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 215
    .line 216
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 217
    .line 218
    .line 219
    move-result-object v3

    .line 220
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 221
    .line 222
    sget-object v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 223
    .line 224
    invoke-static {v3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 225
    .line 226
    .line 227
    move-result-object v3

    .line 228
    iput-object v3, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 229
    .line 230
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->w5()V

    .line 231
    .line 232
    .line 233
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;

    .line 234
    .line 235
    invoke-direct {v3, v0, v8}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 236
    .line 237
    .line 238
    invoke-static {v4, v2, v1, v3}, Lkotlinx/coroutines/flow/g;->p(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/o;)Lkotlinx/coroutines/flow/e;

    .line 239
    .line 240
    .line 241
    move-result-object v1

    .line 242
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 243
    .line 244
    .line 245
    move-result-object v2

    .line 246
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 247
    .line 248
    .line 249
    move-result-object v3

    .line 250
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 251
    .line 252
    .line 253
    move-result-object v2

    .line 254
    sget-object v3, Lkotlinx/coroutines/flow/d0;->a:Lkotlinx/coroutines/flow/d0$a;

    .line 255
    .line 256
    invoke-virtual {v3}, Lkotlinx/coroutines/flow/d0$a;->d()Lkotlinx/coroutines/flow/d0;

    .line 257
    .line 258
    .line 259
    move-result-object v3

    .line 260
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 261
    .line 262
    .line 263
    move-result-object v4

    .line 264
    invoke-static {v1, v2, v3, v4}, Lkotlinx/coroutines/flow/g;->v0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlinx/coroutines/flow/d0;Ljava/lang/Object;)Lkotlinx/coroutines/flow/f0;

    .line 265
    .line 266
    .line 267
    move-result-object v1

    .line 268
    iput-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->w6:Lkotlinx/coroutines/flow/f0;

    .line 269
    .line 270
    return-void
.end method

.method public static final synthetic A4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->a6:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Le81/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Q5:Le81/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->M5:Lf81/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Y5:Lek/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/analytics/domain/scope/T;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->y5:Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;

    .line 2
    .line 3
    return-object p0
.end method

.method private final F5(Ljava/lang/Throwable;)V
    .locals 3

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;

    .line 7
    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;->getErrorModel()Lwa1/a;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, Lwa1/a;->b()I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    sget-object v2, Lcom/xbet/onexcore/data/errors/ErrorsCode;->PromoBonusTryActivateLater:Lcom/xbet/onexcore/data/errors/ErrorsCode;

    .line 17
    .line 18
    invoke-virtual {v2}, Lcom/xbet/onexcore/data/errors/ErrorsCode;->getErrorCode()I

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    if-ne v1, v2, :cond_0

    .line 23
    .line 24
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 25
    .line 26
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;->getErrorModel()Lwa1/a;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    invoke-virtual {v2}, Lwa1/a;->a()I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    invoke-virtual {v1, v2}, Lorg/xbet/analytics/domain/scope/T;->f(I)V

    .line 35
    .line 36
    .line 37
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 38
    .line 39
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/promo/domain/exceptions/AggregatorGiftException;->getErrorModel()Lwa1/a;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-virtual {v0}, Lwa1/a;->a()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    const-string v2, "CasinoGiftsFragment"

    .line 48
    .line 49
    invoke-interface {v1, v2, v0}, LUR/a;->w(Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e4(Ljava/lang/Throwable;)V

    .line 53
    .line 54
    .line 55
    return-void

    .line 56
    :cond_1
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 57
    .line 58
    if-nez v0, :cond_3

    .line 59
    .line 60
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 61
    .line 62
    if-nez v0, :cond_3

    .line 63
    .line 64
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 65
    .line 66
    if-eqz v0, :cond_2

    .line 67
    .line 68
    goto :goto_0

    .line 69
    :cond_2
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e4(Ljava/lang/Throwable;)V

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :cond_3
    :goto_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d4()V

    .line 74
    .line 75
    .line 76
    return-void
.end method

.method public static final synthetic G4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lha1/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->j6:Lha1/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)LUR/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->c6:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->K5:Lf81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final M5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->F5(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic N4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/usecases/n;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R5:Lorg/xplatform/aggregator/impl/gifts/usecases/n;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final R5()V
    .locals 6

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$refresh$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v3, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$refresh$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x2

    .line 16
    const/4 v5, 0x0

    .line 17
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final synthetic S4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic T4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Throwable;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->Q3(Ljava/lang/Throwable;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic U4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;ZLek0/o;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->A5(Ljava/util/List;ZLek0/o;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic V4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->B5(Ljava/util/List;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic W4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->F5(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic X4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->U5(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Y4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Integer;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6:Ljava/lang/Integer;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic Z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic a5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic b5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lha1/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->j6:Lha1/c;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic c5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h6:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final c6(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic d5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lma1/f;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->a6()Lma1/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic e5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h6(Ljava/util/List;Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic l4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->M5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->c6(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->t5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->y5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i5(Ljava/util/List;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic q4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/usecases/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->B5:Lorg/xplatform/aggregator/impl/gifts/usecases/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->I5:Lf81/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d6:Lek0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final s5()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->S5:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v8, Lpb/k;->try_again_text:I

    .line 6
    .line 7
    sget v6, Lpb/k;->data_retrieval_error:I

    .line 8
    .line 9
    new-instance v9, Lorg/xplatform/aggregator/impl/gifts/t;

    .line 10
    .line 11
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/impl/gifts/t;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 12
    .line 13
    .line 14
    const/16 v10, 0x5e

    .line 15
    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public static final synthetic t4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JJ)Ljava/lang/Integer;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o5(JJ)Ljava/lang/Integer;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final t5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R5()V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic u4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic v4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/impl/gifts/usecases/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->A5:Lorg/xplatform/aggregator/impl/gifts/usecases/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->t6:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->q5()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final y5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lv81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->N5:Lv81/g;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final A5(Ljava/util/List;ZLek0/o;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 28
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ld81/b;",
            ">;Z",
            "Lek0/o;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lma1/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p4

    .line 4
    .line 5
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;

    .line 25
    .line 26
    invoke-direct {v2, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v1, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 36
    .line 37
    const/16 v5, 0xa

    .line 38
    .line 39
    const/4 v6, 0x1

    .line 40
    if-eqz v4, :cond_2

    .line 41
    .line 42
    if-ne v4, v6, :cond_1

    .line 43
    .line 44
    iget-boolean v4, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->Z$0:Z

    .line 45
    .line 46
    iget-object v7, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$4:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v7, Ljava/util/Collection;

    .line 49
    .line 50
    iget-object v8, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$3:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v8, Ld81/b;

    .line 53
    .line 54
    iget-object v9, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$2:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast v9, Ljava/util/Iterator;

    .line 57
    .line 58
    iget-object v10, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$1:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast v10, Ljava/util/Collection;

    .line 61
    .line 62
    iget-object v11, v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$0:Ljava/lang/Object;

    .line 63
    .line 64
    check-cast v11, Lek0/o;

    .line 65
    .line 66
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 67
    .line 68
    .line 69
    goto :goto_2

    .line 70
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 71
    .line 72
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 73
    .line 74
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 75
    .line 76
    .line 77
    throw v1

    .line 78
    :cond_2
    invoke-static {v1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    new-instance v1, Ljava/util/ArrayList;

    .line 82
    .line 83
    move-object/from16 v4, p1

    .line 84
    .line 85
    invoke-static {v4, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 86
    .line 87
    .line 88
    move-result v7

    .line 89
    invoke-direct {v1, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 90
    .line 91
    .line 92
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 93
    .line 94
    .line 95
    move-result-object v4

    .line 96
    move-object v7, v1

    .line 97
    move-object v9, v4

    .line 98
    move/from16 v1, p2

    .line 99
    .line 100
    move-object v4, v2

    .line 101
    move-object/from16 v2, p3

    .line 102
    .line 103
    :goto_1
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    .line 104
    .line 105
    .line 106
    move-result v8

    .line 107
    if-eqz v8, :cond_7

    .line 108
    .line 109
    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v8

    .line 113
    check-cast v8, Ld81/b;

    .line 114
    .line 115
    if-eqz v1, :cond_5

    .line 116
    .line 117
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Q5:Le81/c;

    .line 118
    .line 119
    invoke-interface {v10}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 120
    .line 121
    .line 122
    move-result-object v10

    .line 123
    iput-object v2, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$0:Ljava/lang/Object;

    .line 124
    .line 125
    iput-object v7, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$1:Ljava/lang/Object;

    .line 126
    .line 127
    iput-object v9, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$2:Ljava/lang/Object;

    .line 128
    .line 129
    iput-object v8, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$3:Ljava/lang/Object;

    .line 130
    .line 131
    iput-object v7, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->L$4:Ljava/lang/Object;

    .line 132
    .line 133
    iput-boolean v1, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->Z$0:Z

    .line 134
    .line 135
    iput v6, v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$mapCategoryGamesToAdapterItems$1;->label:I

    .line 136
    .line 137
    invoke-static {v10, v4}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 138
    .line 139
    .line 140
    move-result-object v10

    .line 141
    if-ne v10, v3, :cond_3

    .line 142
    .line 143
    return-object v3

    .line 144
    :cond_3
    move-object v11, v2

    .line 145
    move-object v2, v4

    .line 146
    move v4, v1

    .line 147
    move-object v1, v10

    .line 148
    move-object v10, v7

    .line 149
    :goto_2
    check-cast v1, Ljava/lang/Iterable;

    .line 150
    .line 151
    new-instance v12, Ljava/util/ArrayList;

    .line 152
    .line 153
    invoke-static {v1, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 154
    .line 155
    .line 156
    move-result v13

    .line 157
    invoke-direct {v12, v13}, Ljava/util/ArrayList;-><init>(I)V

    .line 158
    .line 159
    .line 160
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 161
    .line 162
    .line 163
    move-result-object v1

    .line 164
    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 165
    .line 166
    .line 167
    move-result v13

    .line 168
    if-eqz v13, :cond_4

    .line 169
    .line 170
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object v13

    .line 174
    check-cast v13, Lorg/xplatform/aggregator/api/model/Game;

    .line 175
    .line 176
    invoke-virtual {v13}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 177
    .line 178
    .line 179
    move-result-wide v13

    .line 180
    invoke-static {v13, v14}, LHc/a;->f(J)Ljava/lang/Long;

    .line 181
    .line 182
    .line 183
    move-result-object v13

    .line 184
    invoke-interface {v12, v13}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 185
    .line 186
    .line 187
    goto :goto_3

    .line 188
    :cond_4
    move v15, v4

    .line 189
    move-object v1, v7

    .line 190
    move-object v7, v10

    .line 191
    move-object v4, v2

    .line 192
    move-object v2, v11

    .line 193
    goto :goto_4

    .line 194
    :cond_5
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 195
    .line 196
    .line 197
    move-result-object v12

    .line 198
    move v15, v1

    .line 199
    move-object v1, v7

    .line 200
    :goto_4
    invoke-virtual {v8}, Ld81/b;->e()J

    .line 201
    .line 202
    .line 203
    move-result-wide v10

    .line 204
    invoke-virtual {v2}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 205
    .line 206
    .line 207
    move-result-object v13

    .line 208
    invoke-static {v13, v6}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 209
    .line 210
    .line 211
    move-result v20

    .line 212
    move/from16 v21, v20

    .line 213
    .line 214
    invoke-virtual {v8}, Ld81/b;->h()Ljava/lang/String;

    .line 215
    .line 216
    .line 217
    move-result-object v20

    .line 218
    invoke-virtual {v8}, Ld81/b;->c()Ljava/util/List;

    .line 219
    .line 220
    .line 221
    move-result-object v13

    .line 222
    new-instance v14, Ljava/util/ArrayList;

    .line 223
    .line 224
    invoke-static {v13, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 225
    .line 226
    .line 227
    move-result v6

    .line 228
    invoke-direct {v14, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 229
    .line 230
    .line 231
    invoke-interface {v13}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 232
    .line 233
    .line 234
    move-result-object v6

    .line 235
    :goto_5
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 236
    .line 237
    .line 238
    move-result v13

    .line 239
    if-eqz v13, :cond_6

    .line 240
    .line 241
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 242
    .line 243
    .line 244
    move-result-object v13

    .line 245
    check-cast v13, Lorg/xplatform/aggregator/api/model/Game;

    .line 246
    .line 247
    invoke-virtual {v13}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 248
    .line 249
    .line 250
    move-result-wide v16

    .line 251
    invoke-static/range {v16 .. v17}, LHc/a;->f(J)Ljava/lang/Long;

    .line 252
    .line 253
    .line 254
    move-result-object v5

    .line 255
    invoke-interface {v12, v5}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 256
    .line 257
    .line 258
    move-result v17

    .line 259
    move-object v5, v14

    .line 260
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5:LHX0/e;

    .line 261
    .line 262
    move-object/from16 p1, v2

    .line 263
    .line 264
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->d6:Lek0/a;

    .line 265
    .line 266
    invoke-virtual {v2}, Lek0/a;->m()Z

    .line 267
    .line 268
    .line 269
    move-result v16

    .line 270
    invoke-virtual/range {p1 .. p1}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 271
    .line 272
    .line 273
    move-result-object v18

    .line 274
    move-object/from16 v27, v3

    .line 275
    .line 276
    invoke-virtual {v8}, Ld81/b;->e()J

    .line 277
    .line 278
    .line 279
    move-result-wide v2

    .line 280
    long-to-int v3, v2

    .line 281
    invoke-static {v3}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 282
    .line 283
    .line 284
    move-result-object v19

    .line 285
    invoke-static/range {v13 .. v19}, LQ91/c;->a(Lorg/xplatform/aggregator/api/model/Game;LHX0/e;ZZZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Ljava/lang/Integer;)LN21/k;

    .line 286
    .line 287
    .line 288
    move-result-object v2

    .line 289
    invoke-interface {v5, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 290
    .line 291
    .line 292
    move-object/from16 v2, p1

    .line 293
    .line 294
    move-object v14, v5

    .line 295
    move-object/from16 v3, v27

    .line 296
    .line 297
    const/16 v5, 0xa

    .line 298
    .line 299
    goto :goto_5

    .line 300
    :cond_6
    move-object/from16 p1, v2

    .line 301
    .line 302
    move-object/from16 v27, v3

    .line 303
    .line 304
    move-object v5, v14

    .line 305
    invoke-virtual {v8}, Ld81/b;->f()J

    .line 306
    .line 307
    .line 308
    move-result-wide v22

    .line 309
    invoke-virtual {v8}, Ld81/b;->g()J

    .line 310
    .line 311
    .line 312
    move-result-wide v24

    .line 313
    new-instance v16, Lma1/b;

    .line 314
    .line 315
    const/16 v26, 0x0

    .line 316
    .line 317
    move-wide/from16 v17, v10

    .line 318
    .line 319
    move/from16 v19, v21

    .line 320
    .line 321
    move-object/from16 v21, v5

    .line 322
    .line 323
    invoke-direct/range {v16 .. v26}, Lma1/b;-><init>(JILjava/lang/String;Ljava/util/List;JJZ)V

    .line 324
    .line 325
    .line 326
    move-object/from16 v2, v16

    .line 327
    .line 328
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 329
    .line 330
    .line 331
    move-object/from16 v2, p1

    .line 332
    .line 333
    move v1, v15

    .line 334
    const/16 v5, 0xa

    .line 335
    .line 336
    const/4 v6, 0x1

    .line 337
    goto/16 :goto_1

    .line 338
    .line 339
    :cond_7
    check-cast v7, Ljava/util/List;

    .line 340
    .line 341
    return-object v7
.end method

.method public final B5(Ljava/util/List;)Z
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)Z"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    return v1

    .line 15
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    :cond_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    check-cast v0, LVX0/i;

    .line 30
    .line 31
    instance-of v2, v0, Lma1/e;

    .line 32
    .line 33
    if-nez v2, :cond_2

    .line 34
    .line 35
    instance-of v0, v0, Lma1/d;

    .line 36
    .line 37
    if-eqz v0, :cond_1

    .line 38
    .line 39
    :cond_2
    const/4 p1, 0x0

    .line 40
    return p1

    .line 41
    :cond_3
    return v1
.end method

.method public final C5(JJLjava/lang/String;)V
    .locals 10
    .param p5    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;

    .line 11
    .line 12
    const/4 v9, 0x0

    .line 13
    move-object v3, p0

    .line 14
    move-wide v4, p1

    .line 15
    move-wide v6, p3

    .line 16
    move-object v8, p5

    .line 17
    invoke-direct/range {v2 .. v9}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryAllClicked$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JJLjava/lang/String;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    const/16 v6, 0xe

    .line 21
    .line 22
    const/4 v7, 0x0

    .line 23
    move-object v5, v2

    .line 24
    const/4 v2, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final D5(JJZ)V
    .locals 10

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;

    .line 10
    .line 11
    const/4 v9, 0x0

    .line 12
    move-object v3, p0

    .line 13
    move-wide v4, p1

    .line 14
    move-wide v7, p3

    .line 15
    move v6, p5

    .line 16
    invoke-direct/range {v2 .. v9}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JZJLkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/4 v4, 0x2

    .line 20
    const/4 v5, 0x0

    .line 21
    move-object v3, v2

    .line 22
    const/4 v2, 0x0

    .line 23
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final E5(JJ)V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->N5:Lv81/g;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, Lv81/g;->a(J)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {p1}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result p2

    .line 11
    if-eqz p2, :cond_0

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/api/model/Game;

    .line 14
    .line 15
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->K5(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 19
    .line 20
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 21
    .line 22
    .line 23
    move-result-wide v2

    .line 24
    const-string v4, "open"

    .line 25
    .line 26
    const-string v1, "CasinoGiftsFragment"

    .line 27
    .line 28
    move-wide v5, p3

    .line 29
    invoke-interface/range {v0 .. v6}, LUR/a;->r(Ljava/lang/String;JLjava/lang/String;J)V

    .line 30
    .line 31
    .line 32
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 33
    .line 34
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 35
    .line 36
    .line 37
    move-result-wide p3

    .line 38
    const-string v8, "open"

    .line 39
    .line 40
    move-wide v9, v5

    .line 41
    move-object v5, p2

    .line 42
    move-wide v6, p3

    .line 43
    invoke-virtual/range {v5 .. v10}, Lorg/xbet/analytics/domain/scope/T;->e(JLjava/lang/String;J)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 47
    .line 48
    .line 49
    :cond_0
    return-void
.end method

.method public final G5()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->n6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onLoadGiftsIfTimeOut$1;

    .line 18
    .line 19
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onLoadGiftsIfTimeOut$1;-><init>(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onLoadGiftsIfTimeOut$2;

    .line 23
    .line 24
    const/4 v0, 0x0

    .line 25
    invoke-direct {v7, p0, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onLoadGiftsIfTimeOut$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    const/16 v8, 0xe

    .line 29
    .line 30
    const/4 v9, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    const/4 v6, 0x0

    .line 34
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->n6:Lkotlinx/coroutines/x0;

    .line 39
    .line 40
    return-void
.end method

.method public final H5()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 2
    .line 3
    const-string v1, "CasinoGiftsFragment"

    .line 4
    .line 5
    invoke-interface {v0, v1}, LUR/a;->h(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 9
    .line 10
    invoke-virtual {v0}, Lorg/xbet/analytics/domain/scope/T;->k()V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->V5:LP91/b;

    .line 14
    .line 15
    new-instance v1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 16
    .line 17
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$Rules;

    .line 18
    .line 19
    const-string v2, "rule_casino"

    .line 20
    .line 21
    invoke-direct {v6, v2}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$Rules;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    const/16 v13, 0xf7

    .line 25
    .line 26
    const/4 v14, 0x0

    .line 27
    const/4 v2, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const-wide/16 v4, 0x0

    .line 30
    .line 31
    const/4 v7, 0x0

    .line 32
    const-wide/16 v8, 0x0

    .line 33
    .line 34
    const-wide/16 v10, 0x0

    .line 35
    .line 36
    const/4 v12, 0x0

    .line 37
    invoke-direct/range {v1 .. v14}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v0, v1}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final I5()V
    .locals 15

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->V5:LP91/b;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5:LHX0/e;

    .line 6
    .line 7
    sget v3, Lpb/k;->array_slots:I

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    new-array v5, v4, [Ljava/lang/Object;

    .line 11
    .line 12
    invoke-interface {v2, v3, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X5:LHX0/e;

    .line 17
    .line 18
    sget v5, Lpb/k;->casino_category_folder_and_section_description:I

    .line 19
    .line 20
    new-array v4, v4, [Ljava/lang/Object;

    .line 21
    .line 22
    invoke-interface {v3, v5, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v3

    .line 26
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;

    .line 27
    .line 28
    const-wide v4, 0x7fffffffffffffffL

    .line 29
    .line 30
    .line 31
    .line 32
    .line 33
    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 34
    .line 35
    .line 36
    move-result-object v4

    .line 37
    invoke-static {v4}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    const/4 v5, 0x0

    .line 42
    const/4 v7, 0x2

    .line 43
    invoke-direct {v6, v4, v5, v7, v5}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$AggregatorCategoryItemScreen;-><init>(Ljava/util/List;Ljava/util/List;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    const/16 v13, 0xf0

    .line 47
    .line 48
    const/4 v14, 0x0

    .line 49
    const-wide/16 v4, 0x1

    .line 50
    .line 51
    const/4 v7, 0x0

    .line 52
    const-wide/16 v8, 0x0

    .line 53
    .line 54
    const-wide/16 v10, 0x0

    .line 55
    .line 56
    const/4 v12, 0x0

    .line 57
    invoke-direct/range {v1 .. v14}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v0, v1}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method public final J5(Lma1/c;J)V
    .locals 13
    .param p1    # Lma1/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G5:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->W5:LwX0/a;

    .line 10
    .line 11
    invoke-virtual {p1}, Lma1/c;->a()Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v2}, Lorg/xplatform/aggregator/core/AggregatorGame;->getId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v2

    .line 19
    invoke-virtual {p1}, Lma1/c;->a()Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-virtual {v4}, Lorg/xplatform/aggregator/core/AggregatorGame;->getProviderId()J

    .line 24
    .line 25
    .line 26
    move-result-wide v4

    .line 27
    invoke-virtual {p1}, Lma1/c;->a()Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 28
    .line 29
    .line 30
    move-result-object v6

    .line 31
    invoke-virtual {v6}, Lorg/xplatform/aggregator/core/AggregatorGame;->getNeedTransfer()Z

    .line 32
    .line 33
    .line 34
    move-result v6

    .line 35
    invoke-virtual {p1}, Lma1/c;->a()Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 36
    .line 37
    .line 38
    move-result-object v7

    .line 39
    invoke-virtual {v7}, Lorg/xplatform/aggregator/core/AggregatorGame;->getProductId()J

    .line 40
    .line 41
    .line 42
    move-result-wide v7

    .line 43
    invoke-virtual {p1}, Lma1/c;->a()Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-virtual {p1}, Lorg/xplatform/aggregator/core/AggregatorGame;->getNoLoyalty()Z

    .line 48
    .line 49
    .line 50
    move-result v9

    .line 51
    const/4 v12, 0x0

    .line 52
    move-wide v10, p2

    .line 53
    invoke-interface/range {v1 .. v12}, LwX0/a;->v(JJZJZJI)Lq4/q;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 58
    .line 59
    .line 60
    :cond_0
    return-void
.end method

.method public final K5(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 3
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/u;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/u;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 6
    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-virtual {v0, p1, v2, v1}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final L5(Lorg/xplatform/aggregator/core/AggregatorGame;J)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$2;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$2;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;

    .line 11
    .line 12
    const/4 v7, 0x0

    .line 13
    move-object v3, p0

    .line 14
    move-object v4, p1

    .line 15
    move-wide v5, p2

    .line 16
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$openGame$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/core/AggregatorGame;JLkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    const/16 v6, 0xe

    .line 20
    .line 21
    move-object v5, v2

    .line 22
    const/4 v2, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v4, 0x0

    .line 25
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final N5()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L5:Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->q()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final O5(Lha1/b;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 2
    .line 3
    invoke-virtual {p1}, Lha1/b;->d()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/T;->d(I)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G5:LwX0/C;

    .line 11
    .line 12
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    new-instance v1, LP91/v;

    .line 19
    .line 20
    invoke-virtual {p1}, Lha1/b;->d()I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 25
    .line 26
    invoke-direct {v1, p1, v2, v3}, LP91/v;-><init>(IJ)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 30
    .line 31
    .line 32
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final P5(Lha1/b;Lorg/xplatform/aggregator/api/model/PartitionType;)V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G5:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    new-instance v1, LP91/q;

    .line 10
    .line 11
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    invoke-virtual {p1}, Lha1/b;->d()I

    .line 16
    .line 17
    .line 18
    move-result v4

    .line 19
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 20
    .line 21
    sget-object v8, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->GIFTS:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 22
    .line 23
    const/16 v9, 0x8

    .line 24
    .line 25
    const/4 v10, 0x0

    .line 26
    const/4 v7, 0x0

    .line 27
    invoke-direct/range {v1 .. v10}, LP91/q;-><init>(JIJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 31
    .line 32
    .line 33
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final Q5(Lha1/b;Lorg/xplatform/aggregator/api/model/PartitionType;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->G5:LwX0/C;

    .line 4
    .line 5
    invoke-virtual {v1}, LwX0/D;->a()LwX0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    new-instance v2, LP91/p;

    .line 12
    .line 13
    invoke-virtual/range {p2 .. p2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 14
    .line 15
    .line 16
    move-result-wide v3

    .line 17
    invoke-virtual/range {p1 .. p1}, Lha1/b;->d()I

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    int-to-long v5, v5

    .line 22
    invoke-virtual/range {p1 .. p1}, Lha1/b;->e()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v7

    .line 26
    sget-object v14, Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;->GIFTS:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 27
    .line 28
    const/16 v15, 0x70

    .line 29
    .line 30
    const/16 v16, 0x0

    .line 31
    .line 32
    const/4 v8, 0x0

    .line 33
    const-wide/16 v9, 0x0

    .line 34
    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v12, 0x0

    .line 37
    const/4 v13, 0x0

    .line 38
    invoke-direct/range {v2 .. v16}, LP91/p;-><init>(JJLjava/lang/String;ZJIZILorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    .line 40
    .line 41
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 42
    .line 43
    .line 44
    :cond_0
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z5()V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public R3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->R5()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final S5(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->E5:Lorg/xplatform/aggregator/impl/gifts/usecases/l;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/usecases/l;->a(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x5()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final T5()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6:Ljava/lang/Integer;

    .line 3
    .line 4
    return-void
.end method

.method public final U5(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v5, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :catchall_0
    move-exception v0

    .line 45
    move-object p1, v0

    .line 46
    goto :goto_3

    .line 47
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw p1

    .line 55
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    :try_start_1
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 59
    .line 60
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->J5:Lfk/j;

    .line 61
    .line 62
    const/4 p1, 0x1

    .line 63
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 64
    .line 65
    iput p1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$savePrimaryBalanceChipType$1;->label:I

    .line 66
    .line 67
    const/4 v4, 0x0

    .line 68
    const/4 v6, 0x2

    .line 69
    const/4 v7, 0x0

    .line 70
    invoke-static/range {v1 .. v7}, Lfk/j$a;->a(Lfk/j;JLorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    if-ne p1, v0, :cond_3

    .line 75
    .line 76
    return-object v0

    .line 77
    :cond_3
    :goto_2
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 78
    .line 79
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isPrimary()Z

    .line 84
    .line 85
    .line 86
    move-result p1

    .line 87
    invoke-static {p1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 95
    goto :goto_4

    .line 96
    :goto_3
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 97
    .line 98
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    :goto_4
    const/4 v0, 0x0

    .line 107
    invoke-static {v0}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 112
    .line 113
    .line 114
    move-result v1

    .line 115
    if-eqz v1, :cond_4

    .line 116
    .line 117
    move-object p1, v0

    .line 118
    :cond_4
    check-cast p1, Ljava/lang/Boolean;

    .line 119
    .line 120
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 121
    .line 122
    .line 123
    move-result p1

    .line 124
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 125
    .line 126
    const-wide/16 v2, 0x0

    .line 127
    .line 128
    cmp-long v4, v0, v2

    .line 129
    .line 130
    if-eqz v4, :cond_5

    .line 131
    .line 132
    if-eqz p1, :cond_5

    .line 133
    .line 134
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 135
    .line 136
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 137
    .line 138
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 139
    .line 140
    return-object p1
.end method

.method public final V5()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->T5:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setConnectionObserver$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setConnectionObserver$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->U5:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->k6:Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    return-void
.end method

.method public final W5()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v5()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final X5(Lorg/xplatform/aggregator/api/model/PartitionType;Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;Lha1/b;)V
    .locals 2
    .param p1    # Lorg/xplatform/aggregator/api/model/PartitionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lha1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;->a:[I

    .line 2
    .line 3
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    aget p2, v0, p2

    .line 8
    .line 9
    const-string v0, "CasinoGiftsFragment"

    .line 10
    .line 11
    packed-switch p2, :pswitch_data_0

    .line 12
    .line 13
    .line 14
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 15
    .line 16
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 17
    .line 18
    .line 19
    throw p1

    .line 20
    :pswitch_0
    return-void

    .line 21
    :pswitch_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 22
    .line 23
    invoke-virtual {p3}, Lha1/b;->c()Ljava/lang/Integer;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    if-eqz p2, :cond_0

    .line 28
    .line 29
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    goto :goto_0

    .line 34
    :cond_0
    const/4 p2, 0x0

    .line 35
    :goto_0
    invoke-interface {p1, v0, p2}, LUR/a;->p(Ljava/lang/String;I)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 39
    .line 40
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 41
    .line 42
    .line 43
    move-result p2

    .line 44
    invoke-virtual {p1, p2}, Lorg/xbet/analytics/domain/scope/T;->i(I)V

    .line 45
    .line 46
    .line 47
    new-instance p1, Lorg/xplatform/aggregator/core/AggregatorGame;

    .line 48
    .line 49
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 50
    .line 51
    .line 52
    move-result p2

    .line 53
    int-to-long v0, p2

    .line 54
    invoke-virtual {p3}, Lha1/b;->e()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object p2

    .line 58
    invoke-direct {p1, v0, v1, p2}, Lorg/xplatform/aggregator/core/AggregatorGame;-><init>(JLjava/lang/String;)V

    .line 59
    .line 60
    .line 61
    iget-wide p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 62
    .line 63
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L5(Lorg/xplatform/aggregator/core/AggregatorGame;J)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :pswitch_2
    invoke-virtual {p0, p3, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->P5(Lha1/b;Lorg/xplatform/aggregator/api/model/PartitionType;)V

    .line 68
    .line 69
    .line 70
    return-void

    .line 71
    :pswitch_3
    invoke-virtual {p0, p3, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Q5(Lha1/b;Lorg/xplatform/aggregator/api/model/PartitionType;)V

    .line 72
    .line 73
    .line 74
    return-void

    .line 75
    :pswitch_4
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 76
    .line 77
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 78
    .line 79
    .line 80
    move-result p2

    .line 81
    invoke-interface {p1, v0, p2}, LUR/a;->j(Ljava/lang/String;I)V

    .line 82
    .line 83
    .line 84
    invoke-virtual {p0, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5(Lha1/b;)V

    .line 85
    .line 86
    .line 87
    return-void

    .line 88
    :pswitch_5
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 89
    .line 90
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 91
    .line 92
    .line 93
    move-result p2

    .line 94
    invoke-interface {p1, v0, p2}, LUR/a;->s(Ljava/lang/String;I)V

    .line 95
    .line 96
    .line 97
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 98
    .line 99
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 100
    .line 101
    .line 102
    move-result p2

    .line 103
    invoke-virtual {p1, p2}, Lorg/xbet/analytics/domain/scope/T;->h(I)V

    .line 104
    .line 105
    .line 106
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->INTERRUPT:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 107
    .line 108
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 109
    .line 110
    .line 111
    move-result p2

    .line 112
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V

    .line 113
    .line 114
    .line 115
    return-void

    .line 116
    :pswitch_6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 117
    .line 118
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 119
    .line 120
    .line 121
    move-result p2

    .line 122
    invoke-interface {p1, v0, p2}, LUR/a;->j(Ljava/lang/String;I)V

    .line 123
    .line 124
    .line 125
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->I5()V

    .line 126
    .line 127
    .line 128
    return-void

    .line 129
    :pswitch_7
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 130
    .line 131
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 132
    .line 133
    .line 134
    move-result p2

    .line 135
    invoke-interface {p1, v0, p2}, LUR/a;->v(Ljava/lang/String;I)V

    .line 136
    .line 137
    .line 138
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 139
    .line 140
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 141
    .line 142
    .line 143
    move-result p2

    .line 144
    invoke-virtual {p1, p2}, Lorg/xbet/analytics/domain/scope/T;->i(I)V

    .line 145
    .line 146
    .line 147
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 148
    .line 149
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 150
    .line 151
    .line 152
    move-result p2

    .line 153
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V

    .line 154
    .line 155
    .line 156
    return-void

    .line 157
    :pswitch_8
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 158
    .line 159
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 160
    .line 161
    .line 162
    move-result p2

    .line 163
    invoke-interface {p1, v0, p2}, LUR/a;->t(Ljava/lang/String;I)V

    .line 164
    .line 165
    .line 166
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 167
    .line 168
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 169
    .line 170
    .line 171
    move-result p2

    .line 172
    invoke-virtual {p1, p2}, Lorg/xbet/analytics/domain/scope/T;->g(I)V

    .line 173
    .line 174
    .line 175
    sget-object p1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->ACTIVE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 176
    .line 177
    invoke-virtual {p3}, Lha1/b;->d()I

    .line 178
    .line 179
    .line 180
    move-result p2

    .line 181
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V

    .line 182
    .line 183
    .line 184
    return-void

    .line 185
    :pswitch_9
    invoke-virtual {p0, p3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->k5(Lha1/b;)V

    .line 186
    .line 187
    .line 188
    return-void

    .line 189
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final Y5()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const-wide/16 v2, 0x0

    .line 7
    .line 8
    const/4 v4, 0x1

    .line 9
    invoke-static {v0, v2, v3, v4, v1}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->m(Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;JILjava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->j()Lkotlinx/coroutines/flow/f0;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setTimer$1;

    .line 23
    .line 24
    invoke-direct {v2, p0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setTimer$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setTimer$2;

    .line 34
    .line 35
    invoke-direct {v2, p0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$setTimer$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 36
    .line 37
    .line 38
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    if-eqz v0, :cond_1

    .line 43
    .line 44
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    .line 51
    :cond_1
    return-void
.end method

.method public final Z5()V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->p6:Z

    .line 3
    .line 4
    return-void
.end method

.method public final a6()Lma1/f;
    .locals 3

    .line 1
    new-instance v0, Lma1/f;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 4
    .line 5
    sget-object v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;->b:[I

    .line 6
    .line 7
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    aget v1, v2, v1

    .line 12
    .line 13
    const/4 v2, 0x1

    .line 14
    if-eq v1, v2, :cond_2

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    if-eq v1, v2, :cond_1

    .line 18
    .line 19
    const/4 v2, 0x3

    .line 20
    if-ne v1, v2, :cond_0

    .line 21
    .line 22
    sget v1, Lpb/k;->no_free_spins_title:I

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 26
    .line 27
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 28
    .line 29
    .line 30
    throw v0

    .line 31
    :cond_1
    sget v1, Lpb/k;->no_bonuses_title:I

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_2
    sget v1, Lpb/k;->no_gifts_title:I

    .line 35
    .line 36
    :goto_0
    invoke-direct {v0, v1}, Lma1/f;-><init>(I)V

    .line 37
    .line 38
    .line 39
    return-object v0
.end method

.method public final b6(IIILjava/util/List;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(III",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/v;

    .line 11
    .line 12
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/gifts/v;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 13
    .line 14
    .line 15
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    move-object v4, p0

    .line 19
    move v5, p1

    .line 20
    move v6, p2

    .line 21
    move v7, p3

    .line 22
    move-object v8, p4

    .line 23
    invoke-direct/range {v3 .. v9}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showGiftsWithChips$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;IIILjava/util/List;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v6, 0xc

    .line 27
    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v5, v3

    .line 30
    const/4 v3, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public d4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;

    .line 11
    .line 12
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s5()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final d6()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public e4(Ljava/lang/Throwable;)V
    .locals 2
    .param p1    # Ljava/lang/Throwable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->H5:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showCustomError$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$showCustomError$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e6()V
    .locals 6

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h6:Z

    .line 2
    .line 3
    if-eqz v0, :cond_3

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 8
    .line 9
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v2, v1

    .line 19
    check-cast v2, Ljava/util/List;

    .line 20
    .line 21
    new-instance v3, Ljava/util/ArrayList;

    .line 22
    .line 23
    const/16 v4, 0xa

    .line 24
    .line 25
    invoke-static {v2, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 26
    .line 27
    .line 28
    move-result v4

    .line 29
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 30
    .line 31
    .line 32
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 37
    .line 38
    .line 39
    move-result v4

    .line 40
    if-eqz v4, :cond_2

    .line 41
    .line 42
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    check-cast v4, LVX0/i;

    .line 47
    .line 48
    instance-of v5, v4, Lma1/f;

    .line 49
    .line 50
    if-eqz v5, :cond_1

    .line 51
    .line 52
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->a6()Lma1/f;

    .line 53
    .line 54
    .line 55
    move-result-object v4

    .line 56
    :cond_1
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_2
    invoke-interface {v0, v1, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 61
    .line 62
    .line 63
    move-result v1

    .line 64
    if-eqz v1, :cond_0

    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u5()V

    .line 68
    .line 69
    .line 70
    return-void
.end method

.method public final f6()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;->n()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public final g6()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->z5:Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l6:Lkotlinx/coroutines/x0;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->P5:Lfk/o;

    .line 16
    .line 17
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 18
    .line 19
    invoke-interface {v0, v2}, Lfk/o;->a(Lorg/xbet/balance/model/BalanceScreenType;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v2, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;

    .line 24
    .line 25
    invoke-direct {v2, p0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l6:Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    return-void
.end method

.method public final h5(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)V
    .locals 10
    .param p1    # Lorg/xplatform/aggregator/api/navigation/GiftsChipType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6:LUR/a;

    .line 4
    .line 5
    const-string v1, "CasinoGiftsFragment"

    .line 6
    .line 7
    invoke-static {p1}, Lka1/a;->a(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-interface {v0, v1, v2}, LUR/a;->k(Ljava/lang/String;I)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->O5:Lorg/xbet/analytics/domain/scope/T;

    .line 15
    .line 16
    invoke-static {p1}, Lka1/a;->a(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/T;->j(I)V

    .line 21
    .line 22
    .line 23
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$1;

    .line 28
    .line 29
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$1;-><init>(Ljava/lang/Object;)V

    .line 30
    .line 31
    .line 32
    new-instance v7, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;

    .line 33
    .line 34
    const/4 v0, 0x0

    .line 35
    invoke-direct {v7, p0, p1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$checkGiftsByType$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V

    .line 36
    .line 37
    .line 38
    const/16 v8, 0xe

    .line 39
    .line 40
    const/4 v9, 0x0

    .line 41
    const/4 v4, 0x0

    .line 42
    const/4 v5, 0x0

    .line 43
    const/4 v6, 0x0

    .line 44
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public final h6(Ljava/util/List;Z)V
    .locals 27
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Long;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r6:Lkotlinx/coroutines/flow/V;

    .line 4
    .line 5
    :cond_0
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v3, v2

    .line 10
    check-cast v3, Ljava/util/List;

    .line 11
    .line 12
    new-instance v4, Ljava/util/ArrayList;

    .line 13
    .line 14
    const/16 v5, 0xa

    .line 15
    .line 16
    invoke-static {v3, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 17
    .line 18
    .line 19
    move-result v6

    .line 20
    invoke-direct {v4, v6}, Ljava/util/ArrayList;-><init>(I)V

    .line 21
    .line 22
    .line 23
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 28
    .line 29
    .line 30
    move-result v6

    .line 31
    if-eqz v6, :cond_3

    .line 32
    .line 33
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    check-cast v6, LVX0/i;

    .line 38
    .line 39
    instance-of v7, v6, Lma1/b;

    .line 40
    .line 41
    if-eqz v7, :cond_2

    .line 42
    .line 43
    move-object v8, v6

    .line 44
    check-cast v8, Lma1/b;

    .line 45
    .line 46
    invoke-virtual {v8}, Lma1/b;->f()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v6

    .line 50
    new-instance v13, Ljava/util/ArrayList;

    .line 51
    .line 52
    invoke-static {v6, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 53
    .line 54
    .line 55
    move-result v7

    .line 56
    invoke-direct {v13, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 57
    .line 58
    .line 59
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 60
    .line 61
    .line 62
    move-result-object v6

    .line 63
    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 64
    .line 65
    .line 66
    move-result v7

    .line 67
    if-eqz v7, :cond_1

    .line 68
    .line 69
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v7

    .line 73
    move-object v14, v7

    .line 74
    check-cast v14, LN21/k;

    .line 75
    .line 76
    invoke-virtual {v14}, LN21/k;->e()J

    .line 77
    .line 78
    .line 79
    move-result-wide v9

    .line 80
    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 81
    .line 82
    .line 83
    move-result-object v7

    .line 84
    move-object/from16 v9, p1

    .line 85
    .line 86
    invoke-interface {v9, v7}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 87
    .line 88
    .line 89
    move-result v7

    .line 90
    move/from16 v10, p2

    .line 91
    .line 92
    invoke-static {v10, v7}, LQ91/b;->a(ZZ)LN21/m;

    .line 93
    .line 94
    .line 95
    move-result-object v20

    .line 96
    const/16 v25, 0x1ef

    .line 97
    .line 98
    const/16 v26, 0x0

    .line 99
    .line 100
    const-wide/16 v15, 0x0

    .line 101
    .line 102
    const/16 v17, 0x0

    .line 103
    .line 104
    const/16 v18, 0x0

    .line 105
    .line 106
    const/16 v19, 0x0

    .line 107
    .line 108
    const/16 v21, 0x0

    .line 109
    .line 110
    const/16 v22, 0x0

    .line 111
    .line 112
    const/16 v23, 0x0

    .line 113
    .line 114
    const/16 v24, 0x0

    .line 115
    .line 116
    invoke-static/range {v14 .. v26}, LN21/k;->b(LN21/k;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;LN21/m;LL11/c;LL11/c;ILjava/lang/Integer;ILjava/lang/Object;)LN21/k;

    .line 117
    .line 118
    .line 119
    move-result-object v7

    .line 120
    invoke-interface {v13, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 121
    .line 122
    .line 123
    goto :goto_1

    .line 124
    :cond_1
    move-object/from16 v9, p1

    .line 125
    .line 126
    move/from16 v10, p2

    .line 127
    .line 128
    const/16 v19, 0x77

    .line 129
    .line 130
    const/16 v20, 0x0

    .line 131
    .line 132
    const-wide/16 v9, 0x0

    .line 133
    .line 134
    const/4 v11, 0x0

    .line 135
    const/4 v12, 0x0

    .line 136
    const-wide/16 v14, 0x0

    .line 137
    .line 138
    const-wide/16 v16, 0x0

    .line 139
    .line 140
    const/16 v18, 0x0

    .line 141
    .line 142
    invoke-static/range {v8 .. v20}, Lma1/b;->e(Lma1/b;JILjava/lang/String;Ljava/util/List;JJZILjava/lang/Object;)Lma1/b;

    .line 143
    .line 144
    .line 145
    move-result-object v6

    .line 146
    :cond_2
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 147
    .line 148
    .line 149
    goto :goto_0

    .line 150
    :cond_3
    invoke-interface {v1, v2, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 151
    .line 152
    .line 153
    move-result v2

    .line 154
    if-eqz v2, :cond_0

    .line 155
    .line 156
    return-void
.end method

.method public final i5(Ljava/util/List;Ljava/util/List;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    new-instance v1, Ljava/util/ArrayList;

    .line 6
    .line 7
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 8
    .line 9
    .line 10
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    :cond_0
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 15
    .line 16
    .line 17
    move-result v3

    .line 18
    if-eqz v3, :cond_1

    .line 19
    .line 20
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    instance-of v4, v3, Lma1/d;

    .line 25
    .line 26
    if-eqz v4, :cond_0

    .line 27
    .line 28
    invoke-interface {v1, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    goto :goto_0

    .line 32
    :cond_1
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    new-instance v2, Ljava/util/ArrayList;

    .line 37
    .line 38
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 39
    .line 40
    .line 41
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    :cond_2
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 46
    .line 47
    .line 48
    move-result v3

    .line 49
    if-eqz v3, :cond_3

    .line 50
    .line 51
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v3

    .line 55
    instance-of v4, v3, Lma1/e;

    .line 56
    .line 57
    if-eqz v4, :cond_2

    .line 58
    .line 59
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    goto :goto_1

    .line 63
    :cond_3
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 64
    .line 65
    .line 66
    move-result p1

    .line 67
    if-nez v0, :cond_4

    .line 68
    .line 69
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->W5()V

    .line 70
    .line 71
    .line 72
    return-void

    .line 73
    :cond_4
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    .line 74
    .line 75
    .line 76
    move-result v2

    .line 77
    if-eqz v2, :cond_5

    .line 78
    .line 79
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 80
    .line 81
    .line 82
    move-result-object p2

    .line 83
    invoke-virtual {p0, v0, v1, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6(IIILjava/util/List;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6()V

    .line 87
    .line 88
    .line 89
    return-void

    .line 90
    :cond_5
    invoke-virtual {p0, v0, v1, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->b6(IIILjava/util/List;)V

    .line 91
    .line 92
    .line 93
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 94
    .line 95
    sget-object p2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 96
    .line 97
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 98
    .line 99
    .line 100
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 101
    .line 102
    if-nez p1, :cond_6

    .line 103
    .line 104
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 105
    .line 106
    const/4 v5, 0x5

    .line 107
    const/4 v6, 0x0

    .line 108
    const-wide/16 v1, 0x0

    .line 109
    .line 110
    const/4 v3, 0x0

    .line 111
    const/4 v4, 0x0

    .line 112
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;-><init>(JZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 113
    .line 114
    .line 115
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->o6:Lorg/xplatform/aggregator/impl/gifts/timer/FlowTimer;

    .line 116
    .line 117
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Y5()V

    .line 118
    .line 119
    .line 120
    :cond_6
    return-void
.end method

.method public final i6()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h6:Z

    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->x5()V

    .line 5
    .line 6
    .line 7
    iput-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->p6:Z

    .line 8
    .line 9
    return-void
.end method

.method public final j5()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6:Ljava/lang/Integer;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    sget-object v1, Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;

    .line 10
    .line 11
    invoke-virtual {p0, v1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V

    .line 12
    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6:Ljava/lang/Integer;

    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final k5(Lha1/b;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$deleteBonus$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$deleteBonus$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 11
    .line 12
    .line 13
    move-result-object v2

    .line 14
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->U5:Lm8/a;

    .line 15
    .line 16
    invoke-interface {v3}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-interface {v2, v3}, Lkotlin/coroutines/CoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$deleteBonus$2;

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    invoke-direct {v5, p0, p1, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$deleteBonus$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lha1/b;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/16 v6, 0xa

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    const/4 v4, 0x0

    .line 34
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final l5(Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;I)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$editStateBonuses$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final m5()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->t6:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n5()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->p6:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->i6()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final o5(JJ)Ljava/lang/Integer;
    .locals 6

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    sget-object v2, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->SLOTS:Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;

    .line 12
    .line 13
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 14
    .line 15
    .line 16
    move-result-wide v3

    .line 17
    invoke-static {v3, v4}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    const/4 v4, 0x2

    .line 22
    new-array v4, v4, [Ljava/lang/Long;

    .line 23
    .line 24
    const/4 v5, 0x0

    .line 25
    aput-object v1, v4, v5

    .line 26
    .line 27
    const/4 v1, 0x1

    .line 28
    aput-object v3, v4, v1

    .line 29
    .line 30
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 35
    .line 36
    .line 37
    move-result-object v3

    .line 38
    invoke-interface {v1, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    if-eqz v1, :cond_0

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_0
    move-wide p1, p3

    .line 46
    :goto_0
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 47
    .line 48
    .line 49
    move-result-wide p3

    .line 50
    cmp-long v0, p1, p3

    .line 51
    .line 52
    if-nez v0, :cond_1

    .line 53
    .line 54
    sget p1, Lpb/k;->live_casino_title:I

    .line 55
    .line 56
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    return-object p1

    .line 61
    :cond_1
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/domain/model/GameCategory$Default;->getCategoryId()J

    .line 62
    .line 63
    .line 64
    move-result-wide p3

    .line 65
    cmp-long v0, p1, p3

    .line 66
    .line 67
    if-nez v0, :cond_2

    .line 68
    .line 69
    sget p1, Lpb/k;->array_slots:I

    .line 70
    .line 71
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    return-object p1

    .line 76
    :cond_2
    const/4 p1, 0x0

    .line 77
    return-object p1
.end method

.method public onCleared()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->D5:Lorg/xplatform/aggregator/impl/gifts/usecases/d;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/usecases/d;->a()V

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Lorg/xbet/ui_common/viewmodel/core/b;->onCleared()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final p5()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    invoke-static {v0}, Lka1/a;->a(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public final q5()Ljava/util/List;
    .locals 14
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 5
    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    :goto_0
    if-ge v2, v1, :cond_0

    .line 9
    .line 10
    new-instance v3, Lma1/b;

    .line 11
    .line 12
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->c6:Lek0/o;

    .line 13
    .line 14
    invoke-virtual {v4}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 15
    .line 16
    .line 17
    move-result-object v4

    .line 18
    const/4 v5, 0x1

    .line 19
    invoke-static {v4, v5}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 20
    .line 21
    .line 22
    move-result v6

    .line 23
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    const-wide/high16 v11, -0x8000000000000000L

    .line 28
    .line 29
    const/4 v13, 0x1

    .line 30
    const-wide/high16 v4, -0x8000000000000000L

    .line 31
    .line 32
    const-string v7, ""

    .line 33
    .line 34
    const-wide/high16 v9, -0x8000000000000000L

    .line 35
    .line 36
    invoke-direct/range {v3 .. v13}, Lma1/b;-><init>(JILjava/lang/String;Ljava/util/List;JJZ)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v0, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    add-int/lit8 v2, v2, 0x1

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_0
    return-object v0
.end method

.method public final r5()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->w6:Lkotlinx/coroutines/flow/f0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u5()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->m6:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-interface {v1}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    const/4 v2, 0x1

    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 16
    .line 17
    .line 18
    move-result-object v3

    .line 19
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$1;

    .line 20
    .line 21
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$1;-><init>(Ljava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    new-instance v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v8, p0, v0, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$getTopGames$2;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/16 v9, 0xe

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v6, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->m6:Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    return-void
.end method

.method public final v5()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->h6:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s6:Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 8
    .line 9
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u5()V

    .line 14
    .line 15
    .line 16
    :goto_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->q6:Lkotlinx/coroutines/flow/V;

    .line 17
    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public final w5()V
    .locals 6

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;->M3()Lkotlinx/coroutines/CoroutineExceptionHandler;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$initScreenData$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v3, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$initScreenData$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x2

    .line 16
    const/4 v5, 0x0

    .line 17
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final x5()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->k6:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->e6:J

    .line 11
    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    cmp-long v0, v3, v5

    .line 15
    .line 16
    if-eqz v0, :cond_2

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    instance-of v0, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$b;

    .line 25
    .line 26
    if-nez v0, :cond_2

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    sget-object v3, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 31
    .line 32
    invoke-interface {v0, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->v6:Lkotlinx/coroutines/flow/V;

    .line 36
    .line 37
    sget-object v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$c$a;

    .line 38
    .line 39
    invoke-interface {v0, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->C5:Lorg/xplatform/aggregator/impl/gifts/usecases/f;

    .line 43
    .line 44
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 45
    .line 46
    invoke-static {v3}, Lka1/a;->a(Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->F5:Lia1/f;

    .line 51
    .line 52
    invoke-virtual {v4}, Lia1/f;->a()I

    .line 53
    .line 54
    .line 55
    move-result v4

    .line 56
    invoke-virtual {v0, v3, v4}, Lorg/xplatform/aggregator/impl/gifts/usecases/f;->a(II)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f6:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 61
    .line 62
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->n6:Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    if-eqz v0, :cond_1

    .line 65
    .line 66
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 67
    .line 68
    .line 69
    move-result v0

    .line 70
    if-ne v0, v1, :cond_1

    .line 71
    .line 72
    return-void

    .line 73
    :cond_1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    new-instance v4, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$loadCurrentGifts$1;

    .line 78
    .line 79
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$loadCurrentGifts$1;-><init>(Ljava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    new-instance v5, Lorg/xplatform/aggregator/impl/gifts/w;

    .line 83
    .line 84
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/gifts/w;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 85
    .line 86
    .line 87
    new-instance v8, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$loadCurrentGifts$3;

    .line 88
    .line 89
    invoke-direct {v8, p0, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$loadCurrentGifts$3;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    .line 90
    .line 91
    .line 92
    const/16 v9, 0xc

    .line 93
    .line 94
    const/4 v10, 0x0

    .line 95
    const/4 v6, 0x0

    .line 96
    const/4 v7, 0x0

    .line 97
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->n6:Lkotlinx/coroutines/x0;

    .line 102
    .line 103
    return-void

    .line 104
    :cond_2
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->V5()V

    .line 105
    .line 106
    .line 107
    return-void
.end method

.method public final z5()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u6:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method
