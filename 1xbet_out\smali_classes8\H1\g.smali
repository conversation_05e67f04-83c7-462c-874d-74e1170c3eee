.class public interface abstract LH1/g;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:LH1/g;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LH1/g$a;

    .line 2
    .line 3
    invoke-direct {v0}, LH1/g$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LH1/g;->a:LH1/g;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public abstract a(Landroidx/media3/common/r;)Z
.end method

.method public abstract b(Landroidx/media3/common/r;)Lk2/l;
.end method
