.class public final LDc1/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LDc1/h;",
        ">;"
    }
.end annotation


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltk0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ltk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXa0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lnl/q;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LD81/a;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHn0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/a;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LX8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lxg/h;",
            ">;"
        }
    .end annotation
.end field

.field public final J:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public final K:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lyg/c;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LpR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LqX0/b;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ldk0/p;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lmo/f;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lk8/b;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw30/e;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/domain/a;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/c;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/consultantchat/domain/usecases/y0;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LiP/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexcore/domain/usecase/a;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHt/a;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Leu/a;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ly20/a;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQl0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LJT/d;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LVT/g;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lxc1/a;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/e;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lyg/c;",
            ">;",
            "LBc/a<",
            "LpR/a;",
            ">;",
            "LBc/a<",
            "LqX0/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Ldk0/p;",
            ">;",
            "LBc/a<",
            "Lmo/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lk8/b;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "Lw30/e;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/domain/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/consultantchat/domain/usecases/y0;",
            ">;",
            "LBc/a<",
            "LiP/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexcore/domain/usecase/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/b;",
            ">;",
            "LBc/a<",
            "LHt/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;",
            ">;",
            "LBc/a<",
            "Leu/a;",
            ">;",
            "LBc/a<",
            "Ly20/a;",
            ">;",
            "LBc/a<",
            "LQl0/a;",
            ">;",
            "LBc/a<",
            "LJT/d;",
            ">;",
            "LBc/a<",
            "LVT/g;",
            ">;",
            "LBc/a<",
            "Lxc1/a;",
            ">;",
            "LBc/a<",
            "Lv81/e;",
            ">;",
            "LBc/a<",
            "Ltk0/b;",
            ">;",
            "LBc/a<",
            "Ltk0/a;",
            ">;",
            "LBc/a<",
            "LXa0/c;",
            ">;",
            "LBc/a<",
            "Lnl/q;",
            ">;",
            "LBc/a<",
            "LD81/a;",
            ">;",
            "LBc/a<",
            "LHn0/a;",
            ">;",
            "LBc/a<",
            "Lp9/a;",
            ">;",
            "LBc/a<",
            "LX8/a;",
            ">;",
            "LBc/a<",
            "Lxg/h;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LDc1/i;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LDc1/i;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, LDc1/i;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, LDc1/i;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, LDc1/i;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, LDc1/i;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, LDc1/i;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, LDc1/i;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, LDc1/i;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, LDc1/i;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, LDc1/i;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, LDc1/i;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, LDc1/i;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, LDc1/i;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, LDc1/i;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, LDc1/i;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, LDc1/i;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, LDc1/i;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, LDc1/i;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, LDc1/i;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, LDc1/i;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, LDc1/i;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, LDc1/i;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, LDc1/i;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, LDc1/i;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, LDc1/i;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, LDc1/i;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, LDc1/i;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, LDc1/i;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, LDc1/i;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, LDc1/i;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, LDc1/i;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, LDc1/i;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, LDc1/i;->H:LBc/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, LDc1/i;->I:LBc/a;

    .line 113
    .line 114
    move-object/from16 p1, p36

    .line 115
    .line 116
    iput-object p1, p0, LDc1/i;->J:LBc/a;

    .line 117
    .line 118
    move-object/from16 p1, p37

    .line 119
    .line 120
    iput-object p1, p0, LDc1/i;->K:LBc/a;

    .line 121
    .line 122
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LDc1/i;
    .locals 38
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lyg/c;",
            ">;",
            "LBc/a<",
            "LpR/a;",
            ">;",
            "LBc/a<",
            "LqX0/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Ldk0/p;",
            ">;",
            "LBc/a<",
            "Lmo/f;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lk8/b;",
            ">;",
            "LBc/a<",
            "Lf8/g;",
            ">;",
            "LBc/a<",
            "Lw30/e;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/domain/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/feed/subscriptions/domain/usecases/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/consultantchat/domain/usecases/y0;",
            ">;",
            "LBc/a<",
            "LiP/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexcore/domain/usecase/a;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/b;",
            ">;",
            "LBc/a<",
            "LHt/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;",
            ">;",
            "LBc/a<",
            "Leu/a;",
            ">;",
            "LBc/a<",
            "Ly20/a;",
            ">;",
            "LBc/a<",
            "LQl0/a;",
            ">;",
            "LBc/a<",
            "LJT/d;",
            ">;",
            "LBc/a<",
            "LVT/g;",
            ">;",
            "LBc/a<",
            "Lxc1/a;",
            ">;",
            "LBc/a<",
            "Lv81/e;",
            ">;",
            "LBc/a<",
            "Ltk0/b;",
            ">;",
            "LBc/a<",
            "Ltk0/a;",
            ">;",
            "LBc/a<",
            "LXa0/c;",
            ">;",
            "LBc/a<",
            "Lnl/q;",
            ">;",
            "LBc/a<",
            "LD81/a;",
            ">;",
            "LBc/a<",
            "LHn0/a;",
            ">;",
            "LBc/a<",
            "Lp9/a;",
            ">;",
            "LBc/a<",
            "LX8/a;",
            ">;",
            "LBc/a<",
            "Lxg/h;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/data/profile/b;",
            ">;)",
            "LDc1/i;"
        }
    .end annotation

    .line 1
    new-instance v0, LDc1/i;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    invoke-direct/range {v0 .. v37}, LDc1/i;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 78
    .line 79
    .line 80
    return-object v0
.end method

.method public static c(Lyg/c;LpR/a;LqX0/b;Lm8/a;Ldk0/p;Lmo/f;Lorg/xbet/ui_common/utils/M;Lk8/b;Lf8/g;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LiP/a;Lcom/xbet/onexcore/domain/usecase/a;LHX0/e;Lorg/xbet/analytics/domain/b;LHt/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;Leu/a;Ly20/a;LQl0/a;LJT/d;LVT/g;Lxc1/a;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;)LDc1/h;
    .locals 38

    .line 1
    new-instance v0, LDc1/h;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    invoke-direct/range {v0 .. v37}, LDc1/h;-><init>(Lyg/c;LpR/a;LqX0/b;Lm8/a;Ldk0/p;Lmo/f;Lorg/xbet/ui_common/utils/M;Lk8/b;Lf8/g;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LiP/a;Lcom/xbet/onexcore/domain/usecase/a;LHX0/e;Lorg/xbet/analytics/domain/b;LHt/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;Leu/a;Ly20/a;LQl0/a;LJT/d;LVT/g;Lxc1/a;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;)V

    .line 78
    .line 79
    .line 80
    return-object v0
.end method


# virtual methods
.method public b()LDc1/h;
    .locals 39

    move-object/from16 v0, p0

    .line 1
    iget-object v1, v0, LDc1/i;->a:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Lyg/c;

    iget-object v1, v0, LDc1/i;->b:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v3, v1

    check-cast v3, LpR/a;

    iget-object v1, v0, LDc1/i;->c:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, LqX0/b;

    iget-object v1, v0, LDc1/i;->d:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lm8/a;

    iget-object v1, v0, LDc1/i;->e:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, Ldk0/p;

    iget-object v1, v0, LDc1/i;->f:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v7, v1

    check-cast v7, Lmo/f;

    iget-object v1, v0, LDc1/i;->g:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v8, v1

    check-cast v8, Lorg/xbet/ui_common/utils/M;

    iget-object v1, v0, LDc1/i;->h:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, Lk8/b;

    iget-object v1, v0, LDc1/i;->i:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v10, v1

    check-cast v10, Lf8/g;

    iget-object v1, v0, LDc1/i;->j:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v11, v1

    check-cast v11, Lw30/e;

    iget-object v1, v0, LDc1/i;->k:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v12, v1

    check-cast v12, Lorg/xplatform/aggregator/api/domain/a;

    iget-object v1, v0, LDc1/i;->l:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v13, v1

    check-cast v13, Lorg/xbet/feed/subscriptions/domain/usecases/c;

    iget-object v1, v0, LDc1/i;->m:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v14, v1

    check-cast v14, Lorg/xbet/consultantchat/domain/usecases/y0;

    iget-object v1, v0, LDc1/i;->n:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v15, v1

    check-cast v15, LiP/a;

    iget-object v1, v0, LDc1/i;->o:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v16, v1

    check-cast v16, Lcom/xbet/onexcore/domain/usecase/a;

    iget-object v1, v0, LDc1/i;->p:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v17, v1

    check-cast v17, LHX0/e;

    iget-object v1, v0, LDc1/i;->q:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v18, v1

    check-cast v18, Lorg/xbet/analytics/domain/b;

    iget-object v1, v0, LDc1/i;->r:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v19, v1

    check-cast v19, LHt/a;

    iget-object v1, v0, LDc1/i;->s:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v20, v1

    check-cast v20, Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;

    iget-object v1, v0, LDc1/i;->t:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v21, v1

    check-cast v21, Leu/a;

    iget-object v1, v0, LDc1/i;->u:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v22, v1

    check-cast v22, Ly20/a;

    iget-object v1, v0, LDc1/i;->v:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v23, v1

    check-cast v23, LQl0/a;

    iget-object v1, v0, LDc1/i;->w:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v24, v1

    check-cast v24, LJT/d;

    iget-object v1, v0, LDc1/i;->x:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v25, v1

    check-cast v25, LVT/g;

    iget-object v1, v0, LDc1/i;->y:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v26, v1

    check-cast v26, Lxc1/a;

    iget-object v1, v0, LDc1/i;->z:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v27, v1

    check-cast v27, Lv81/e;

    iget-object v1, v0, LDc1/i;->A:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v28, v1

    check-cast v28, Ltk0/b;

    iget-object v1, v0, LDc1/i;->B:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v29, v1

    check-cast v29, Ltk0/a;

    iget-object v1, v0, LDc1/i;->C:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v30, v1

    check-cast v30, LXa0/c;

    iget-object v1, v0, LDc1/i;->D:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v31, v1

    check-cast v31, Lnl/q;

    iget-object v1, v0, LDc1/i;->E:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v32, v1

    check-cast v32, LD81/a;

    iget-object v1, v0, LDc1/i;->F:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v33, v1

    check-cast v33, LHn0/a;

    iget-object v1, v0, LDc1/i;->G:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v34, v1

    check-cast v34, Lp9/a;

    iget-object v1, v0, LDc1/i;->H:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v35, v1

    check-cast v35, LX8/a;

    iget-object v1, v0, LDc1/i;->I:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v36, v1

    check-cast v36, Lxg/h;

    iget-object v1, v0, LDc1/i;->J:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v37, v1

    check-cast v37, Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    iget-object v1, v0, LDc1/i;->K:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v38, v1

    check-cast v38, Lcom/xbet/onexuser/data/profile/b;

    invoke-static/range {v2 .. v38}, LDc1/i;->c(Lyg/c;LpR/a;LqX0/b;Lm8/a;Ldk0/p;Lmo/f;Lorg/xbet/ui_common/utils/M;Lk8/b;Lf8/g;Lw30/e;Lorg/xplatform/aggregator/api/domain/a;Lorg/xbet/feed/subscriptions/domain/usecases/c;Lorg/xbet/consultantchat/domain/usecases/y0;LiP/a;Lcom/xbet/onexcore/domain/usecase/a;LHX0/e;Lorg/xbet/analytics/domain/b;LHt/a;Lorg/xbet/analytics/domain/trackers/AppsFlyerLogger;Leu/a;Ly20/a;LQl0/a;LJT/d;LVT/g;Lxc1/a;Lv81/e;Ltk0/b;Ltk0/a;LXa0/c;Lnl/q;LD81/a;LHn0/a;Lp9/a;LX8/a;Lxg/h;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lcom/xbet/onexuser/data/profile/b;)LDc1/h;

    move-result-object v1

    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LDc1/i;->b()LDc1/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
