.class public final synthetic LKZ0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKZ0/a;->a:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LKZ0/a;->a:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    invoke-static {v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->J(Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;)Landroid/view/View;

    move-result-object v0

    return-object v0
.end method
