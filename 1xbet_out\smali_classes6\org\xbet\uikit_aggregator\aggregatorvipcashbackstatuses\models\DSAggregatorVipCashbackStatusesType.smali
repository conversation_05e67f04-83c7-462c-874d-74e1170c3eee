.class public final enum Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\n\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\u0008\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;",
        "",
        "id",
        "",
        "<init>",
        "(Ljava/lang/String;II)V",
        "getId",
        "()I",
        "LARGE_ICON",
        "INDICATOR",
        "SMALL_ICON",
        "PICTURE",
        "COMPACT",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

.field public static final enum COMPACT:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

.field public static final enum INDICATOR:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

.field public static final enum LARGE_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

.field public static final enum PICTURE:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

.field public static final enum SMALL_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;


# instance fields
.field private final id:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 2
    .line 3
    const-string v1, "LARGE_ICON"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;-><init>(Ljava/lang/String;II)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->LARGE_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 12
    .line 13
    const-string v1, "INDICATOR"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;-><init>(Ljava/lang/String;II)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->INDICATOR:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 22
    .line 23
    const-string v1, "SMALL_ICON"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;-><init>(Ljava/lang/String;II)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->SMALL_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 32
    .line 33
    const-string v1, "PICTURE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;-><init>(Ljava/lang/String;II)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->PICTURE:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 42
    .line 43
    const-string v1, "COMPACT"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;-><init>(Ljava/lang/String;II)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->COMPACT:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 50
    .line 51
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->a()[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->$VALUES:[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 56
    .line 57
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->$ENTRIES:Lkotlin/enums/a;

    .line 62
    .line 63
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->id:I

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;
    .locals 3

    .line 1
    const/4 v0, 0x5

    new-array v0, v0, [Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->LARGE_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->INDICATOR:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->SMALL_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->PICTURE:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->COMPACT:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->$VALUES:[Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getId()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->id:I

    .line 2
    .line 3
    return v0
.end method
