.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "c",
        "()LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/Z0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt;->d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/Z0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt;->e(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LZw0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LZw0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LZw0/b;

    .line 7
    .line 8
    invoke-direct {v1}, LZw0/b;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt$tournamentEmptyGamesAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt$tournamentEmptyGamesAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt$tournamentEmptyGamesAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/games/emptygames/viewholder/TournamentEmptyGamesViewHolderKt$tournamentEmptyGamesAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/Z0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/Z0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/Z0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final e(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method
