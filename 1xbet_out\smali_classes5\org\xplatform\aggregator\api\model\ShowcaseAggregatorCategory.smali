.class public final enum Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;,
        Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0010\u0008\u0086\u0081\u0002\u0018\u0000 \u00072\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0008B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\r\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0005\u0010\u0006j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "",
        "getCategoryId",
        "()J",
        "Companion",
        "a",
        "ONE_X_LIVE_AGGREGATOR",
        "LIVE_AGGREGATOR",
        "SLOTS",
        "NONE",
        "POPULAR",
        "SLOTS_POPULAR",
        "LIVE_AGGREGATOR_POPULAR",
        "TOP_CHOICE",
        "NEW_SLOTS",
        "EXCLUSIVE",
        "RECOMMENDATION",
        "api_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field public static final Companion:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum EXCLUSIVE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final EXCLUSIVE_CATEGORY_ID:J = 0x59L

.field public static final enum LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final LIVE_AGGREGATOR_CATEGORY_ID:J = 0x0L

.field public static final enum LIVE_AGGREGATOR_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field public static final enum NEW_SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final NEW_SLOTS_CATEGORY_ID:J = 0x15L

.field public static final enum NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final NONE_CATEGORY_ID:J = 0x0L

.field public static final enum ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final ONE_X_LIVE_AGGREGATOR_CATEGORY_ID:J = 0x4bL

.field public static final enum POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final POPULAR_CATEGORY_ID:J = 0x11L

.field public static final enum RECOMMENDATION:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final RECOMMENDATION_CATEGORY_ID:J = 0x0L

.field public static final enum SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final SLOTS_CATEGORY_ID:J = 0x0L

.field public static final enum SLOTS_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field public static final enum TOP_CHOICE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

.field private static final TOP_CHOICE_CATEGORY_ID:J = 0x5eL


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 2
    .line 3
    const-string v1, "ONE_X_LIVE_AGGREGATOR"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 10
    .line 11
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 12
    .line 13
    const-string v1, "LIVE_AGGREGATOR"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 20
    .line 21
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 22
    .line 23
    const-string v1, "SLOTS"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 30
    .line 31
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 32
    .line 33
    const-string v1, "NONE"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 40
    .line 41
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 42
    .line 43
    const-string v1, "POPULAR"

    .line 44
    .line 45
    const/4 v2, 0x4

    .line 46
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 47
    .line 48
    .line 49
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 50
    .line 51
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 52
    .line 53
    const-string v1, "SLOTS_POPULAR"

    .line 54
    .line 55
    const/4 v2, 0x5

    .line 56
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 60
    .line 61
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 62
    .line 63
    const-string v1, "LIVE_AGGREGATOR_POPULAR"

    .line 64
    .line 65
    const/4 v2, 0x6

    .line 66
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 67
    .line 68
    .line 69
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 70
    .line 71
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 72
    .line 73
    const-string v1, "TOP_CHOICE"

    .line 74
    .line 75
    const/4 v2, 0x7

    .line 76
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 77
    .line 78
    .line 79
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->TOP_CHOICE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 82
    .line 83
    const-string v1, "NEW_SLOTS"

    .line 84
    .line 85
    const/16 v2, 0x8

    .line 86
    .line 87
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 88
    .line 89
    .line 90
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NEW_SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 91
    .line 92
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 93
    .line 94
    const-string v1, "EXCLUSIVE"

    .line 95
    .line 96
    const/16 v2, 0x9

    .line 97
    .line 98
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 99
    .line 100
    .line 101
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->EXCLUSIVE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 102
    .line 103
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 104
    .line 105
    const-string v1, "RECOMMENDATION"

    .line 106
    .line 107
    const/16 v2, 0xa

    .line 108
    .line 109
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;-><init>(Ljava/lang/String;I)V

    .line 110
    .line 111
    .line 112
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->RECOMMENDATION:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 113
    .line 114
    invoke-static {}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->a()[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->$VALUES:[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 119
    .line 120
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->$ENTRIES:Lkotlin/enums/a;

    .line 125
    .line 126
    new-instance v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;

    .line 127
    .line 128
    const/4 v1, 0x0

    .line 129
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 130
    .line 131
    .line 132
    sput-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->Companion:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;

    .line 133
    .line 134
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
    .locals 3

    .line 1
    const/16 v0, 0xb

    new-array v0, v0, [Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->TOP_CHOICE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/4 v2, 0x7

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NEW_SLOTS:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/16 v2, 0x8

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->EXCLUSIVE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/16 v2, 0x9

    aput-object v1, v0, v2

    sget-object v1, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->RECOMMENDATION:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    const/16 v2, 0xa

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
    .locals 1

    .line 1
    const-class v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->$VALUES:[Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getCategoryId()J
    .locals 5

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    aget v0, v0, v1

    .line 8
    .line 9
    const-wide/16 v1, 0x11

    .line 10
    .line 11
    const-wide/16 v3, 0x0

    .line 12
    .line 13
    packed-switch v0, :pswitch_data_0

    .line 14
    .line 15
    .line 16
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 17
    .line 18
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 19
    .line 20
    .line 21
    throw v0

    .line 22
    :pswitch_0
    return-wide v1

    .line 23
    :pswitch_1
    return-wide v3

    .line 24
    :pswitch_2
    const-wide/16 v0, 0x59

    .line 25
    .line 26
    return-wide v0

    .line 27
    :pswitch_3
    const-wide/16 v0, 0x15

    .line 28
    .line 29
    return-wide v0

    .line 30
    :pswitch_4
    const-wide/16 v0, 0x5e

    .line 31
    .line 32
    return-wide v0

    .line 33
    :pswitch_5
    return-wide v1

    .line 34
    :pswitch_6
    return-wide v3

    .line 35
    :pswitch_7
    const-wide/16 v0, 0x4b

    .line 36
    .line 37
    return-wide v0

    .line 38
    nop

    .line 39
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_7
        :pswitch_6
        :pswitch_6
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method
