.class public final LI1/n$j;
.super LI1/n$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LI1/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "j"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LI1/n$i<",
        "LI1/n$j;",
        ">;"
    }
.end annotation


# instance fields
.field public final e:Z

.field public final f:LI1/n$e;

.field public final g:Z

.field public final h:Z

.field public final i:Z

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:Z

.field public final q:I

.field public final r:Z

.field public final s:I

.field public final t:Z

.field public final u:Z

.field public final v:I


# direct methods
.method public constructor <init>(ILandroidx/media3/common/G;ILI1/n$e;ILjava/lang/String;IZ)V
    .locals 4

    .line 1
    invoke-direct {p0, p1, p2, p3}, LI1/n$i;-><init>(ILandroidx/media3/common/G;I)V

    .line 2
    .line 3
    .line 4
    iput-object p4, p0, LI1/n$j;->f:LI1/n$e;

    .line 5
    .line 6
    iget-boolean p1, p4, LI1/n$e;->r0:Z

    .line 7
    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    const/16 p1, 0x18

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/16 p1, 0x10

    .line 14
    .line 15
    :goto_0
    iget-boolean p2, p4, LI1/n$e;->q0:Z

    .line 16
    .line 17
    const/4 p3, 0x1

    .line 18
    const/4 v0, 0x0

    .line 19
    if-eqz p2, :cond_1

    .line 20
    .line 21
    and-int p2, p7, p1

    .line 22
    .line 23
    if-eqz p2, :cond_1

    .line 24
    .line 25
    const/4 p2, 0x1

    .line 26
    goto :goto_1

    .line 27
    :cond_1
    const/4 p2, 0x0

    .line 28
    :goto_1
    iput-boolean p2, p0, LI1/n$j;->r:Z

    .line 29
    .line 30
    const/high16 p2, -0x40800000    # -1.0f

    .line 31
    .line 32
    const/4 p7, -0x1

    .line 33
    if-eqz p8, :cond_6

    .line 34
    .line 35
    iget-object v1, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 36
    .line 37
    iget v2, v1, Landroidx/media3/common/r;->v:I

    .line 38
    .line 39
    if-eq v2, p7, :cond_2

    .line 40
    .line 41
    iget v3, p4, Landroidx/media3/common/I;->a:I

    .line 42
    .line 43
    if-gt v2, v3, :cond_6

    .line 44
    .line 45
    :cond_2
    iget v2, v1, Landroidx/media3/common/r;->w:I

    .line 46
    .line 47
    if-eq v2, p7, :cond_3

    .line 48
    .line 49
    iget v3, p4, Landroidx/media3/common/I;->b:I

    .line 50
    .line 51
    if-gt v2, v3, :cond_6

    .line 52
    .line 53
    :cond_3
    iget v2, v1, Landroidx/media3/common/r;->x:F

    .line 54
    .line 55
    cmpl-float v3, v2, p2

    .line 56
    .line 57
    if-eqz v3, :cond_4

    .line 58
    .line 59
    iget v3, p4, Landroidx/media3/common/I;->c:I

    .line 60
    .line 61
    int-to-float v3, v3

    .line 62
    cmpg-float v2, v2, v3

    .line 63
    .line 64
    if-gtz v2, :cond_6

    .line 65
    .line 66
    :cond_4
    iget v1, v1, Landroidx/media3/common/r;->j:I

    .line 67
    .line 68
    if-eq v1, p7, :cond_5

    .line 69
    .line 70
    iget v2, p4, Landroidx/media3/common/I;->d:I

    .line 71
    .line 72
    if-gt v1, v2, :cond_6

    .line 73
    .line 74
    :cond_5
    const/4 v1, 0x1

    .line 75
    goto :goto_2

    .line 76
    :cond_6
    const/4 v1, 0x0

    .line 77
    :goto_2
    iput-boolean v1, p0, LI1/n$j;->e:Z

    .line 78
    .line 79
    if-eqz p8, :cond_b

    .line 80
    .line 81
    iget-object p8, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 82
    .line 83
    iget v1, p8, Landroidx/media3/common/r;->v:I

    .line 84
    .line 85
    if-eq v1, p7, :cond_7

    .line 86
    .line 87
    iget v2, p4, Landroidx/media3/common/I;->e:I

    .line 88
    .line 89
    if-lt v1, v2, :cond_b

    .line 90
    .line 91
    :cond_7
    iget v1, p8, Landroidx/media3/common/r;->w:I

    .line 92
    .line 93
    if-eq v1, p7, :cond_8

    .line 94
    .line 95
    iget v2, p4, Landroidx/media3/common/I;->f:I

    .line 96
    .line 97
    if-lt v1, v2, :cond_b

    .line 98
    .line 99
    :cond_8
    iget v1, p8, Landroidx/media3/common/r;->x:F

    .line 100
    .line 101
    cmpl-float v2, v1, p2

    .line 102
    .line 103
    if-eqz v2, :cond_9

    .line 104
    .line 105
    iget v2, p4, Landroidx/media3/common/I;->g:I

    .line 106
    .line 107
    int-to-float v2, v2

    .line 108
    cmpl-float v1, v1, v2

    .line 109
    .line 110
    if-ltz v1, :cond_b

    .line 111
    .line 112
    :cond_9
    iget p8, p8, Landroidx/media3/common/r;->j:I

    .line 113
    .line 114
    if-eq p8, p7, :cond_a

    .line 115
    .line 116
    iget p7, p4, Landroidx/media3/common/I;->h:I

    .line 117
    .line 118
    if-lt p8, p7, :cond_b

    .line 119
    .line 120
    :cond_a
    const/4 p7, 0x1

    .line 121
    goto :goto_3

    .line 122
    :cond_b
    const/4 p7, 0x0

    .line 123
    :goto_3
    iput-boolean p7, p0, LI1/n$j;->g:Z

    .line 124
    .line 125
    invoke-static {p5, v0}, Landroidx/media3/exoplayer/A1;->k(IZ)Z

    .line 126
    .line 127
    .line 128
    move-result p7

    .line 129
    iput-boolean p7, p0, LI1/n$j;->h:Z

    .line 130
    .line 131
    iget-object p7, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 132
    .line 133
    iget p8, p7, Landroidx/media3/common/r;->x:F

    .line 134
    .line 135
    cmpl-float p2, p8, p2

    .line 136
    .line 137
    if-eqz p2, :cond_c

    .line 138
    .line 139
    const/high16 p2, 0x41200000    # 10.0f

    .line 140
    .line 141
    cmpl-float p2, p8, p2

    .line 142
    .line 143
    if-ltz p2, :cond_c

    .line 144
    .line 145
    const/4 p2, 0x1

    .line 146
    goto :goto_4

    .line 147
    :cond_c
    const/4 p2, 0x0

    .line 148
    :goto_4
    iput-boolean p2, p0, LI1/n$j;->i:Z

    .line 149
    .line 150
    iget p2, p7, Landroidx/media3/common/r;->j:I

    .line 151
    .line 152
    iput p2, p0, LI1/n$j;->j:I

    .line 153
    .line 154
    invoke-virtual {p7}, Landroidx/media3/common/r;->e()I

    .line 155
    .line 156
    .line 157
    move-result p2

    .line 158
    iput p2, p0, LI1/n$j;->k:I

    .line 159
    .line 160
    const/4 p2, 0x0

    .line 161
    :goto_5
    iget-object p7, p4, Landroidx/media3/common/I;->n:Lcom/google/common/collect/ImmutableList;

    .line 162
    .line 163
    invoke-virtual {p7}, Ljava/util/AbstractCollection;->size()I

    .line 164
    .line 165
    .line 166
    move-result p7

    .line 167
    const p8, 0x7fffffff

    .line 168
    .line 169
    .line 170
    if-ge p2, p7, :cond_e

    .line 171
    .line 172
    iget-object p7, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 173
    .line 174
    iget-object v1, p4, Landroidx/media3/common/I;->n:Lcom/google/common/collect/ImmutableList;

    .line 175
    .line 176
    invoke-interface {v1, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    check-cast v1, Ljava/lang/String;

    .line 181
    .line 182
    invoke-static {p7, v1, v0}, LI1/n;->H(Landroidx/media3/common/r;Ljava/lang/String;Z)I

    .line 183
    .line 184
    .line 185
    move-result p7

    .line 186
    if-lez p7, :cond_d

    .line 187
    .line 188
    goto :goto_6

    .line 189
    :cond_d
    add-int/lit8 p2, p2, 0x1

    .line 190
    .line 191
    goto :goto_5

    .line 192
    :cond_e
    const p2, 0x7fffffff

    .line 193
    .line 194
    .line 195
    const/4 p7, 0x0

    .line 196
    :goto_6
    iput p2, p0, LI1/n$j;->m:I

    .line 197
    .line 198
    iput p7, p0, LI1/n$j;->n:I

    .line 199
    .line 200
    iget-object p2, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 201
    .line 202
    iget p2, p2, Landroidx/media3/common/r;->f:I

    .line 203
    .line 204
    iget p7, p4, Landroidx/media3/common/I;->o:I

    .line 205
    .line 206
    invoke-static {p2, p7}, LI1/n;->y(II)I

    .line 207
    .line 208
    .line 209
    move-result p2

    .line 210
    iput p2, p0, LI1/n$j;->o:I

    .line 211
    .line 212
    iget-object p2, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 213
    .line 214
    iget p2, p2, Landroidx/media3/common/r;->f:I

    .line 215
    .line 216
    if-eqz p2, :cond_10

    .line 217
    .line 218
    and-int/2addr p2, p3

    .line 219
    if-eqz p2, :cond_f

    .line 220
    .line 221
    goto :goto_7

    .line 222
    :cond_f
    const/4 p2, 0x0

    .line 223
    goto :goto_8

    .line 224
    :cond_10
    :goto_7
    const/4 p2, 0x1

    .line 225
    :goto_8
    iput-boolean p2, p0, LI1/n$j;->p:Z

    .line 226
    .line 227
    invoke-static {p6}, LI1/n;->U(Ljava/lang/String;)Ljava/lang/String;

    .line 228
    .line 229
    .line 230
    move-result-object p2

    .line 231
    if-nez p2, :cond_11

    .line 232
    .line 233
    const/4 p2, 0x1

    .line 234
    goto :goto_9

    .line 235
    :cond_11
    const/4 p2, 0x0

    .line 236
    :goto_9
    iget-object p7, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 237
    .line 238
    invoke-static {p7, p6, p2}, LI1/n;->H(Landroidx/media3/common/r;Ljava/lang/String;Z)I

    .line 239
    .line 240
    .line 241
    move-result p2

    .line 242
    iput p2, p0, LI1/n$j;->q:I

    .line 243
    .line 244
    const/4 p2, 0x0

    .line 245
    :goto_a
    iget-object p6, p4, Landroidx/media3/common/I;->m:Lcom/google/common/collect/ImmutableList;

    .line 246
    .line 247
    invoke-virtual {p6}, Ljava/util/AbstractCollection;->size()I

    .line 248
    .line 249
    .line 250
    move-result p6

    .line 251
    if-ge p2, p6, :cond_13

    .line 252
    .line 253
    iget-object p6, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 254
    .line 255
    iget-object p6, p6, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 256
    .line 257
    if-eqz p6, :cond_12

    .line 258
    .line 259
    iget-object p7, p4, Landroidx/media3/common/I;->m:Lcom/google/common/collect/ImmutableList;

    .line 260
    .line 261
    invoke-interface {p7, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 262
    .line 263
    .line 264
    move-result-object p7

    .line 265
    invoke-virtual {p6, p7}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 266
    .line 267
    .line 268
    move-result p6

    .line 269
    if-eqz p6, :cond_12

    .line 270
    .line 271
    move p8, p2

    .line 272
    goto :goto_b

    .line 273
    :cond_12
    add-int/lit8 p2, p2, 0x1

    .line 274
    .line 275
    goto :goto_a

    .line 276
    :cond_13
    :goto_b
    iput p8, p0, LI1/n$j;->l:I

    .line 277
    .line 278
    invoke-static {p5}, Landroidx/media3/exoplayer/A1;->g(I)I

    .line 279
    .line 280
    .line 281
    move-result p2

    .line 282
    const/16 p4, 0x80

    .line 283
    .line 284
    if-ne p2, p4, :cond_14

    .line 285
    .line 286
    const/4 p2, 0x1

    .line 287
    goto :goto_c

    .line 288
    :cond_14
    const/4 p2, 0x0

    .line 289
    :goto_c
    iput-boolean p2, p0, LI1/n$j;->t:Z

    .line 290
    .line 291
    invoke-static {p5}, Landroidx/media3/exoplayer/A1;->i(I)I

    .line 292
    .line 293
    .line 294
    move-result p2

    .line 295
    const/16 p4, 0x40

    .line 296
    .line 297
    if-ne p2, p4, :cond_15

    .line 298
    .line 299
    goto :goto_d

    .line 300
    :cond_15
    const/4 p3, 0x0

    .line 301
    :goto_d
    iput-boolean p3, p0, LI1/n$j;->u:Z

    .line 302
    .line 303
    iget-object p2, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 304
    .line 305
    iget-object p2, p2, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 306
    .line 307
    invoke-static {p2}, LI1/n;->z(Ljava/lang/String;)I

    .line 308
    .line 309
    .line 310
    move-result p2

    .line 311
    iput p2, p0, LI1/n$j;->v:I

    .line 312
    .line 313
    invoke-virtual {p0, p5, p1}, LI1/n$j;->m(II)I

    .line 314
    .line 315
    .line 316
    move-result p1

    .line 317
    iput p1, p0, LI1/n$j;->s:I

    .line 318
    .line 319
    return-void
.end method

.method public static synthetic c(LI1/n$j;LI1/n$j;)I
    .locals 0

    .line 1
    invoke-static {p0, p1}, LI1/n$j;->i(LI1/n$j;LI1/n$j;)I

    move-result p0

    return p0
.end method

.method public static synthetic g(LI1/n$j;LI1/n$j;)I
    .locals 0

    .line 1
    invoke-static {p0, p1}, LI1/n$j;->h(LI1/n$j;LI1/n$j;)I

    move-result p0

    return p0
.end method

.method public static h(LI1/n$j;LI1/n$j;)I
    .locals 4

    .line 1
    invoke-static {}, Lcom/google/common/collect/ComparisonChain;->k()Lcom/google/common/collect/ComparisonChain;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-boolean v1, p0, LI1/n$j;->h:Z

    .line 6
    .line 7
    iget-boolean v2, p1, LI1/n$j;->h:Z

    .line 8
    .line 9
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget v1, p0, LI1/n$j;->m:I

    .line 14
    .line 15
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iget v2, p1, LI1/n$j;->m:I

    .line 20
    .line 21
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    invoke-static {}, Lcom/google/common/collect/Ordering;->natural()Lcom/google/common/collect/Ordering;

    .line 26
    .line 27
    .line 28
    move-result-object v3

    .line 29
    invoke-virtual {v3}, Lcom/google/common/collect/Ordering;->reverse()Lcom/google/common/collect/Ordering;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    invoke-virtual {v0, v1, v2, v3}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iget v1, p0, LI1/n$j;->n:I

    .line 38
    .line 39
    iget v2, p1, LI1/n$j;->n:I

    .line 40
    .line 41
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->d(II)Lcom/google/common/collect/ComparisonChain;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iget v1, p0, LI1/n$j;->o:I

    .line 46
    .line 47
    iget v2, p1, LI1/n$j;->o:I

    .line 48
    .line 49
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->d(II)Lcom/google/common/collect/ComparisonChain;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-boolean v1, p0, LI1/n$j;->p:Z

    .line 54
    .line 55
    iget-boolean v2, p1, LI1/n$j;->p:Z

    .line 56
    .line 57
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iget v1, p0, LI1/n$j;->q:I

    .line 62
    .line 63
    iget v2, p1, LI1/n$j;->q:I

    .line 64
    .line 65
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->d(II)Lcom/google/common/collect/ComparisonChain;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-boolean v1, p0, LI1/n$j;->i:Z

    .line 70
    .line 71
    iget-boolean v2, p1, LI1/n$j;->i:Z

    .line 72
    .line 73
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iget-boolean v1, p0, LI1/n$j;->e:Z

    .line 78
    .line 79
    iget-boolean v2, p1, LI1/n$j;->e:Z

    .line 80
    .line 81
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    iget-boolean v1, p0, LI1/n$j;->g:Z

    .line 86
    .line 87
    iget-boolean v2, p1, LI1/n$j;->g:Z

    .line 88
    .line 89
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    iget v1, p0, LI1/n$j;->l:I

    .line 94
    .line 95
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    iget v2, p1, LI1/n$j;->l:I

    .line 100
    .line 101
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 102
    .line 103
    .line 104
    move-result-object v2

    .line 105
    invoke-static {}, Lcom/google/common/collect/Ordering;->natural()Lcom/google/common/collect/Ordering;

    .line 106
    .line 107
    .line 108
    move-result-object v3

    .line 109
    invoke-virtual {v3}, Lcom/google/common/collect/Ordering;->reverse()Lcom/google/common/collect/Ordering;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    invoke-virtual {v0, v1, v2, v3}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    iget-boolean v1, p0, LI1/n$j;->t:Z

    .line 118
    .line 119
    iget-boolean v2, p1, LI1/n$j;->t:Z

    .line 120
    .line 121
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    iget-boolean v1, p0, LI1/n$j;->u:Z

    .line 126
    .line 127
    iget-boolean v2, p1, LI1/n$j;->u:Z

    .line 128
    .line 129
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->h(ZZ)Lcom/google/common/collect/ComparisonChain;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    iget-boolean v1, p0, LI1/n$j;->t:Z

    .line 134
    .line 135
    if-eqz v1, :cond_0

    .line 136
    .line 137
    iget-boolean v1, p0, LI1/n$j;->u:Z

    .line 138
    .line 139
    if-eqz v1, :cond_0

    .line 140
    .line 141
    iget p0, p0, LI1/n$j;->v:I

    .line 142
    .line 143
    iget p1, p1, LI1/n$j;->v:I

    .line 144
    .line 145
    invoke-virtual {v0, p0, p1}, Lcom/google/common/collect/ComparisonChain;->d(II)Lcom/google/common/collect/ComparisonChain;

    .line 146
    .line 147
    .line 148
    move-result-object v0

    .line 149
    :cond_0
    invoke-virtual {v0}, Lcom/google/common/collect/ComparisonChain;->j()I

    .line 150
    .line 151
    .line 152
    move-result p0

    .line 153
    return p0
.end method

.method public static i(LI1/n$j;LI1/n$j;)I
    .locals 5

    .line 1
    iget-boolean v0, p0, LI1/n$j;->e:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-boolean v0, p0, LI1/n$j;->h:Z

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-static {}, LI1/n;->A()Lcom/google/common/collect/Ordering;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-static {}, LI1/n;->A()Lcom/google/common/collect/Ordering;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lcom/google/common/collect/Ordering;->reverse()Lcom/google/common/collect/Ordering;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :goto_0
    invoke-static {}, Lcom/google/common/collect/ComparisonChain;->k()Lcom/google/common/collect/ComparisonChain;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, LI1/n$j;->f:LI1/n$e;

    .line 27
    .line 28
    iget-boolean v2, v2, Landroidx/media3/common/I;->B:Z

    .line 29
    .line 30
    if-eqz v2, :cond_1

    .line 31
    .line 32
    iget v2, p0, LI1/n$j;->j:I

    .line 33
    .line 34
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    iget v3, p1, LI1/n$j;->j:I

    .line 39
    .line 40
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    invoke-static {}, LI1/n;->A()Lcom/google/common/collect/Ordering;

    .line 45
    .line 46
    .line 47
    move-result-object v4

    .line 48
    invoke-virtual {v4}, Lcom/google/common/collect/Ordering;->reverse()Lcom/google/common/collect/Ordering;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    invoke-virtual {v1, v2, v3, v4}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    :cond_1
    iget v2, p0, LI1/n$j;->k:I

    .line 57
    .line 58
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v2

    .line 62
    iget v3, p1, LI1/n$j;->k:I

    .line 63
    .line 64
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 65
    .line 66
    .line 67
    move-result-object v3

    .line 68
    invoke-virtual {v1, v2, v3, v0}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    iget p0, p0, LI1/n$j;->j:I

    .line 73
    .line 74
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 75
    .line 76
    .line 77
    move-result-object p0

    .line 78
    iget p1, p1, LI1/n$j;->j:I

    .line 79
    .line 80
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-virtual {v1, p0, p1, v0}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 85
    .line 86
    .line 87
    move-result-object p0

    .line 88
    invoke-virtual {p0}, Lcom/google/common/collect/ComparisonChain;->j()I

    .line 89
    .line 90
    .line 91
    move-result p0

    .line 92
    return p0
.end method

.method public static k(Ljava/util/List;Ljava/util/List;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LI1/n$j;",
            ">;",
            "Ljava/util/List<",
            "LI1/n$j;",
            ">;)I"
        }
    .end annotation

    .line 1
    invoke-static {}, Lcom/google/common/collect/ComparisonChain;->k()Lcom/google/common/collect/ComparisonChain;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, LI1/w;

    .line 6
    .line 7
    invoke-direct {v1}, LI1/w;-><init>()V

    .line 8
    .line 9
    .line 10
    invoke-static {p0, v1}, Ljava/util/Collections;->max(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    check-cast v1, LI1/n$j;

    .line 15
    .line 16
    new-instance v2, LI1/w;

    .line 17
    .line 18
    invoke-direct {v2}, LI1/w;-><init>()V

    .line 19
    .line 20
    .line 21
    invoke-static {p1, v2}, Ljava/util/Collections;->max(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    check-cast v2, LI1/n$j;

    .line 26
    .line 27
    new-instance v3, LI1/w;

    .line 28
    .line 29
    invoke-direct {v3}, LI1/w;-><init>()V

    .line 30
    .line 31
    .line 32
    invoke-virtual {v0, v1, v2, v3}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 41
    .line 42
    .line 43
    move-result v2

    .line 44
    invoke-virtual {v0, v1, v2}, Lcom/google/common/collect/ComparisonChain;->d(II)Lcom/google/common/collect/ComparisonChain;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    new-instance v1, LI1/x;

    .line 49
    .line 50
    invoke-direct {v1}, LI1/x;-><init>()V

    .line 51
    .line 52
    .line 53
    invoke-static {p0, v1}, Ljava/util/Collections;->max(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    check-cast p0, LI1/n$j;

    .line 58
    .line 59
    new-instance v1, LI1/x;

    .line 60
    .line 61
    invoke-direct {v1}, LI1/x;-><init>()V

    .line 62
    .line 63
    .line 64
    invoke-static {p1, v1}, Ljava/util/Collections;->max(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, LI1/n$j;

    .line 69
    .line 70
    new-instance v1, LI1/x;

    .line 71
    .line 72
    invoke-direct {v1}, LI1/x;-><init>()V

    .line 73
    .line 74
    .line 75
    invoke-virtual {v0, p0, p1, v1}, Lcom/google/common/collect/ComparisonChain;->g(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)Lcom/google/common/collect/ComparisonChain;

    .line 76
    .line 77
    .line 78
    move-result-object p0

    .line 79
    invoke-virtual {p0}, Lcom/google/common/collect/ComparisonChain;->j()I

    .line 80
    .line 81
    .line 82
    move-result p0

    .line 83
    return p0
.end method

.method public static l(ILandroidx/media3/common/G;LI1/n$e;[ILjava/lang/String;ILandroid/graphics/Point;)Lcom/google/common/collect/ImmutableList;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroidx/media3/common/G;",
            "LI1/n$e;",
            "[I",
            "Ljava/lang/String;",
            "I",
            "Landroid/graphics/Point;",
            ")",
            "Lcom/google/common/collect/ImmutableList<",
            "LI1/n$j;",
            ">;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p6

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget v1, v0, Landroid/graphics/Point;->x:I

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    iget v1, p2, Landroidx/media3/common/I;->i:I

    .line 9
    .line 10
    :goto_0
    if-eqz v0, :cond_1

    .line 11
    .line 12
    iget v0, v0, Landroid/graphics/Point;->y:I

    .line 13
    .line 14
    goto :goto_1

    .line 15
    :cond_1
    iget v0, p2, Landroidx/media3/common/I;->j:I

    .line 16
    .line 17
    :goto_1
    iget-boolean v2, p2, Landroidx/media3/common/I;->l:Z

    .line 18
    .line 19
    invoke-static {p1, v1, v0, v2}, LI1/n;->x(Landroidx/media3/common/G;IIZ)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->builder()Lcom/google/common/collect/ImmutableList$Builder;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const/4 v2, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    :goto_2
    iget v3, p1, Landroidx/media3/common/G;->a:I

    .line 30
    .line 31
    if-ge v6, v3, :cond_4

    .line 32
    .line 33
    invoke-virtual {p1, v6}, Landroidx/media3/common/G;->a(I)Landroidx/media3/common/r;

    .line 34
    .line 35
    .line 36
    move-result-object v3

    .line 37
    invoke-virtual {v3}, Landroidx/media3/common/r;->e()I

    .line 38
    .line 39
    .line 40
    move-result v3

    .line 41
    const v4, 0x7fffffff

    .line 42
    .line 43
    .line 44
    if-eq v0, v4, :cond_3

    .line 45
    .line 46
    const/4 v4, -0x1

    .line 47
    if-eq v3, v4, :cond_2

    .line 48
    .line 49
    if-gt v3, v0, :cond_2

    .line 50
    .line 51
    goto :goto_3

    .line 52
    :cond_2
    const/4 v11, 0x0

    .line 53
    goto :goto_4

    .line 54
    :cond_3
    :goto_3
    const/4 v3, 0x1

    .line 55
    const/4 v11, 0x1

    .line 56
    :goto_4
    new-instance v3, LI1/n$j;

    .line 57
    .line 58
    aget v8, p3, v6

    .line 59
    .line 60
    move v4, p0

    .line 61
    move-object v5, p1

    .line 62
    move-object v7, p2

    .line 63
    move-object/from16 v9, p4

    .line 64
    .line 65
    move/from16 v10, p5

    .line 66
    .line 67
    invoke-direct/range {v3 .. v11}, LI1/n$j;-><init>(ILandroidx/media3/common/G;ILI1/n$e;ILjava/lang/String;IZ)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {v1, v3}, Lcom/google/common/collect/ImmutableList$Builder;->j(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList$Builder;

    .line 71
    .line 72
    .line 73
    add-int/lit8 v6, v6, 0x1

    .line 74
    .line 75
    goto :goto_2

    .line 76
    :cond_4
    invoke-virtual {v1}, Lcom/google/common/collect/ImmutableList$Builder;->n()Lcom/google/common/collect/ImmutableList;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    return-object p0
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 1
    iget v0, p0, LI1/n$j;->s:I

    .line 2
    .line 3
    return v0
.end method

.method public bridge synthetic b(LI1/n$i;)Z
    .locals 0

    .line 1
    check-cast p1, LI1/n$j;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LI1/n$j;->n(LI1/n$j;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public final m(II)I
    .locals 2

    .line 1
    iget-object v0, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 2
    .line 3
    iget v0, v0, Landroidx/media3/common/r;->f:I

    .line 4
    .line 5
    and-int/lit16 v0, v0, 0x4000

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    return v1

    .line 11
    :cond_0
    iget-object v0, p0, LI1/n$j;->f:LI1/n$e;

    .line 12
    .line 13
    iget-boolean v0, v0, LI1/n$e;->A0:Z

    .line 14
    .line 15
    invoke-static {p1, v0}, Landroidx/media3/exoplayer/A1;->k(IZ)Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-nez v0, :cond_1

    .line 20
    .line 21
    return v1

    .line 22
    :cond_1
    iget-boolean v0, p0, LI1/n$j;->e:Z

    .line 23
    .line 24
    if-nez v0, :cond_2

    .line 25
    .line 26
    iget-object v0, p0, LI1/n$j;->f:LI1/n$e;

    .line 27
    .line 28
    iget-boolean v0, v0, LI1/n$e;->p0:Z

    .line 29
    .line 30
    if-nez v0, :cond_2

    .line 31
    .line 32
    return v1

    .line 33
    :cond_2
    invoke-static {p1, v1}, Landroidx/media3/exoplayer/A1;->k(IZ)Z

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    if-eqz v0, :cond_3

    .line 38
    .line 39
    iget-boolean v0, p0, LI1/n$j;->g:Z

    .line 40
    .line 41
    if-eqz v0, :cond_3

    .line 42
    .line 43
    iget-boolean v0, p0, LI1/n$j;->e:Z

    .line 44
    .line 45
    if-eqz v0, :cond_3

    .line 46
    .line 47
    iget-object v0, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 48
    .line 49
    iget v0, v0, Landroidx/media3/common/r;->j:I

    .line 50
    .line 51
    const/4 v1, -0x1

    .line 52
    if-eq v0, v1, :cond_3

    .line 53
    .line 54
    iget-object v0, p0, LI1/n$j;->f:LI1/n$e;

    .line 55
    .line 56
    iget-boolean v1, v0, Landroidx/media3/common/I;->C:Z

    .line 57
    .line 58
    if-nez v1, :cond_3

    .line 59
    .line 60
    iget-boolean v0, v0, Landroidx/media3/common/I;->B:Z

    .line 61
    .line 62
    if-nez v0, :cond_3

    .line 63
    .line 64
    and-int/2addr p1, p2

    .line 65
    if-eqz p1, :cond_3

    .line 66
    .line 67
    const/4 p1, 0x2

    .line 68
    return p1

    .line 69
    :cond_3
    const/4 p1, 0x1

    .line 70
    return p1
.end method

.method public n(LI1/n$j;)Z
    .locals 2

    .line 1
    iget-boolean v0, p0, LI1/n$j;->r:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 6
    .line 7
    iget-object v0, v0, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 8
    .line 9
    iget-object v1, p1, LI1/n$i;->d:Landroidx/media3/common/r;

    .line 10
    .line 11
    iget-object v1, v1, Landroidx/media3/common/r;->o:Ljava/lang/String;

    .line 12
    .line 13
    invoke-static {v0, v1}, Lj$/util/Objects;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    :cond_0
    iget-object v0, p0, LI1/n$j;->f:LI1/n$e;

    .line 20
    .line 21
    iget-boolean v0, v0, LI1/n$e;->s0:Z

    .line 22
    .line 23
    if-nez v0, :cond_2

    .line 24
    .line 25
    iget-boolean v0, p0, LI1/n$j;->t:Z

    .line 26
    .line 27
    iget-boolean v1, p1, LI1/n$j;->t:Z

    .line 28
    .line 29
    if-ne v0, v1, :cond_1

    .line 30
    .line 31
    iget-boolean v0, p0, LI1/n$j;->u:Z

    .line 32
    .line 33
    iget-boolean p1, p1, LI1/n$j;->u:Z

    .line 34
    .line 35
    if-ne v0, p1, :cond_1

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_1
    const/4 p1, 0x0

    .line 39
    return p1

    .line 40
    :cond_2
    :goto_0
    const/4 p1, 0x1

    .line 41
    return p1
.end method
