.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\t\n\u0002\u0008\u0016\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 u2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001vB\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J%\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0006\u001a\u00020\u00052\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u0007H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ%\u0010\u000f\u001a\u00020\n2\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u00072\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0019\u0010\u0017\u001a\u00020\n2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u0019\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0004J\u0017\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0019\u0010\u001f\u001a\u00020\n2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0016\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010!\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008!\u0010\u0004J\u000f\u0010#\u001a\u00020\"H\u0010\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010&\u001a\u00020%H\u0014\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010(\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008(\u0010\u0004J\u0019\u0010)\u001a\u00020\n2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0014\u00a2\u0006\u0004\u0008)\u0010 J\u000f\u0010*\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008*\u0010\u0004J\u000f\u0010+\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008+\u0010\u0004J\u000f\u0010,\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008,\u0010\u0004R+\u00105\u001a\u00020-2\u0006\u0010.\u001a\u00020-8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102\"\u0004\u00083\u00104R+\u00109\u001a\u00020-2\u0006\u0010.\u001a\u00020-8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00086\u00100\u001a\u0004\u00087\u00102\"\u0004\u00088\u00104R+\u0010=\u001a\u00020-2\u0006\u0010.\u001a\u00020-8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008:\u00100\u001a\u0004\u0008;\u00102\"\u0004\u0008<\u00104R+\u0010C\u001a\u00020\r2\u0006\u0010.\u001a\u00020\r8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010\u001cR\"\u0010K\u001a\u00020D8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008E\u0010F\u001a\u0004\u0008G\u0010H\"\u0004\u0008I\u0010JR\"\u0010S\u001a\u00020L8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008M\u0010N\u001a\u0004\u0008O\u0010P\"\u0004\u0008Q\u0010RR\u001b\u0010Y\u001a\u00020T8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008U\u0010V\u001a\u0004\u0008W\u0010XR\u001b\u0010_\u001a\u00020Z8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008[\u0010\\\u001a\u0004\u0008]\u0010^R\u001b\u0010d\u001a\u00020`8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008a\u0010V\u001a\u0004\u0008b\u0010cR\u001b\u0010h\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008e\u0010V\u001a\u0004\u0008f\u0010gR\u001a\u0010n\u001a\u00020i8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008j\u0010k\u001a\u0004\u0008l\u0010mR\u001a\u0010t\u001a\u00020o8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008p\u0010q\u001a\u0004\u0008r\u0010s\u00a8\u0006w"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;",
        "action",
        "",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "bannerList",
        "",
        "h4",
        "(Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;Ljava/util/List;)V",
        "",
        "openAfterDialog",
        "i4",
        "(Ljava/util/List;Z)V",
        "",
        "deeplink",
        "k4",
        "(Ljava/lang/String;)V",
        "",
        "error",
        "q4",
        "(I)V",
        "p4",
        "visibility",
        "r4",
        "(Z)V",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "u2",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "onDestroyView",
        "t2",
        "v2",
        "onResume",
        "onPause",
        "",
        "<set-?>",
        "o0",
        "LeX0/f;",
        "X3",
        "()J",
        "m4",
        "(J)V",
        "idToOpen",
        "b1",
        "U3",
        "l4",
        "bannerToOpen",
        "k1",
        "Y3",
        "n4",
        "partitionId",
        "v1",
        "LeX0/a;",
        "c4",
        "()Z",
        "o4",
        "isVirtual",
        "LF81/a;",
        "x1",
        "LF81/a;",
        "V3",
        "()LF81/a;",
        "setDailyTaskWidgetAdapterDelegates",
        "(LF81/a;)V",
        "dailyTaskWidgetAdapterDelegates",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "y1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "b4",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lpa1/j;",
        "F1",
        "Lkotlin/j;",
        "W3",
        "()Lpa1/j;",
        "gamesAdapter",
        "LS91/W;",
        "H1",
        "LRc/c;",
        "Z3",
        "()LS91/W;",
        "viewBinding",
        "Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;",
        "I1",
        "T3",
        "()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;",
        "balanceViewModel",
        "P1",
        "a4",
        "()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
        "viewModel",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "S1",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "V1",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "b2",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b2:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v2:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final F1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public x1:LF81/a;

.field public y1:Lorg/xbet/ui_common/viewmodel/core/l;


# direct methods
.method static constructor <clinit>()V
    .locals 9

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 4
    .line 5
    const-string v2, "idToOpen"

    .line 6
    .line 7
    const-string v3, "getIdToOpen()J"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bannerToOpen"

    .line 20
    .line 21
    const-string v5, "getBannerToOpen()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "partitionId"

    .line 33
    .line 34
    const-string v6, "getPartitionId()J"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "isVirtual"

    .line 46
    .line 47
    const-string v7, "isVirtual()Z"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    new-instance v6, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 57
    .line 58
    const-string v7, "viewBinding"

    .line 59
    .line 60
    const-string v8, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentMyAggregatorBinding;"

    .line 61
    .line 62
    invoke-direct {v6, v1, v7, v8, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 63
    .line 64
    .line 65
    invoke-static {v6}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v6, 0x5

    .line 70
    new-array v6, v6, [Lkotlin/reflect/m;

    .line 71
    .line 72
    aput-object v0, v6, v4

    .line 73
    .line 74
    const/4 v0, 0x1

    .line 75
    aput-object v2, v6, v0

    .line 76
    .line 77
    const/4 v0, 0x2

    .line 78
    aput-object v3, v6, v0

    .line 79
    .line 80
    const/4 v0, 0x3

    .line 81
    aput-object v5, v6, v0

    .line 82
    .line 83
    const/4 v0, 0x4

    .line 84
    aput-object v1, v6, v0

    .line 85
    .line 86
    sput-object v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 87
    .line 88
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$a;

    .line 89
    .line 90
    const/4 v1, 0x0

    .line 91
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    sput-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->b2:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$a;

    .line 95
    .line 96
    return-void
.end method

.method public constructor <init>()V
    .locals 9

    .line 1
    sget v0, Lu91/c;->fragment_my_aggregator:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v1, LeX0/f;

    .line 7
    .line 8
    const/4 v5, 0x2

    .line 9
    const/4 v6, 0x0

    .line 10
    const-string v2, "GAME_TO_OPEN_ITEM"

    .line 11
    .line 12
    const-wide/16 v3, 0x0

    .line 13
    .line 14
    invoke-direct/range {v1 .. v6}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->o0:LeX0/f;

    .line 18
    .line 19
    new-instance v2, LeX0/f;

    .line 20
    .line 21
    const/4 v6, 0x2

    .line 22
    const/4 v7, 0x0

    .line 23
    const-string v3, "BANNER_TO_OPEN_ITEM"

    .line 24
    .line 25
    const-wide/16 v4, 0x0

    .line 26
    .line 27
    invoke-direct/range {v2 .. v7}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    iput-object v2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->b1:LeX0/f;

    .line 31
    .line 32
    new-instance v3, LeX0/f;

    .line 33
    .line 34
    const/4 v7, 0x2

    .line 35
    const/4 v8, 0x0

    .line 36
    const-string v4, "PARTITION_ID"

    .line 37
    .line 38
    const-wide/16 v5, 0x0

    .line 39
    .line 40
    invoke-direct/range {v3 .. v8}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 41
    .line 42
    .line 43
    iput-object v3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->k1:LeX0/f;

    .line 44
    .line 45
    new-instance v0, LeX0/a;

    .line 46
    .line 47
    const-string v1, "IS_VIRTUAL"

    .line 48
    .line 49
    const/4 v2, 0x0

    .line 50
    const/4 v3, 0x2

    .line 51
    const/4 v4, 0x0

    .line 52
    invoke-direct {v0, v1, v2, v3, v4}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v1:LeX0/a;

    .line 56
    .line 57
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/e;

    .line 58
    .line 59
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/e;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 60
    .line 61
    .line 62
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->F1:Lkotlin/j;

    .line 67
    .line 68
    sget-object v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$viewBinding$2;

    .line 69
    .line 70
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->H1:LRc/c;

    .line 75
    .line 76
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$balanceViewModel$2;

    .line 77
    .line 78
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$balanceViewModel$2;-><init>(Ljava/lang/Object;)V

    .line 79
    .line 80
    .line 81
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 82
    .line 83
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$1;

    .line 84
    .line 85
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 86
    .line 87
    .line 88
    invoke-static {v1, v2}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    const-class v2, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 93
    .line 94
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 95
    .line 96
    .line 97
    move-result-object v2

    .line 98
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$2;

    .line 99
    .line 100
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    .line 101
    .line 102
    .line 103
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$3;

    .line 104
    .line 105
    invoke-direct {v5, v4, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 106
    .line 107
    .line 108
    new-instance v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$4;

    .line 109
    .line 110
    invoke-direct {v6, p0, v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$4;-><init>(Landroidx/fragment/app/Fragment;Lkotlin/j;)V

    .line 111
    .line 112
    .line 113
    invoke-static {p0, v2, v3, v5, v6}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->I1:Lkotlin/j;

    .line 118
    .line 119
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/f;

    .line 120
    .line 121
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/f;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 122
    .line 123
    .line 124
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$5;

    .line 125
    .line 126
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$5;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 127
    .line 128
    .line 129
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$6;

    .line 130
    .line 131
    invoke-direct {v3, v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$6;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 132
    .line 133
    .line 134
    invoke-static {v1, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    const-class v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 139
    .line 140
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 141
    .line 142
    .line 143
    move-result-object v2

    .line 144
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$7;

    .line 145
    .line 146
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$7;-><init>(Lkotlin/j;)V

    .line 147
    .line 148
    .line 149
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$8;

    .line 150
    .line 151
    invoke-direct {v5, v4, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$special$$inlined$viewModels$default$8;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 152
    .line 153
    .line 154
    invoke-static {p0, v2, v3, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 155
    .line 156
    .line 157
    move-result-object v0

    .line 158
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->P1:Lkotlin/j;

    .line 159
    .line 160
    sget-object v0, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->MY_AGGREGATOR:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 161
    .line 162
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->S1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 163
    .line 164
    sget-object v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->MyAggregator:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 165
    .line 166
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->V1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 167
    .line 168
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Z
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->c4()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->h4(Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->k4(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->l4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->m4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->n4(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->o4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->p4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->d3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->e3(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->q4(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->r4(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->i3(Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->k3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final O3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lpa1/j;
    .locals 6

    .line 1
    new-instance v0, Lpa1/j;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->V3()LF81/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-interface {v1, v2}, LF81/a;->b(LF81/b;)LA4/c;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/i;

    .line 16
    .line 17
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/i;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 18
    .line 19
    .line 20
    new-instance v3, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/j;

    .line 21
    .line 22
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/j;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 23
    .line 24
    .line 25
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/k;

    .line 26
    .line 27
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/k;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 28
    .line 29
    .line 30
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/b;

    .line 31
    .line 32
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/b;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 33
    .line 34
    .line 35
    invoke-direct/range {v0 .. v5}, Lpa1/j;-><init>(LA4/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function0;)V

    .line 36
    .line 37
    .line 38
    return-object v0
.end method

.method public static final P3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->A6(Ljava/lang/String;Lra1/c;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final Q3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;J)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->E6(Ljava/lang/String;Lra1/c;J)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final R3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;JZ)Lkotlin/Unit;
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-class p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {p0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    move-object v5, p1

    .line 12
    move-wide v2, p2

    .line 13
    move v4, p4

    .line 14
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->z6(Ljava/lang/String;JZLra1/c;)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final S3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-virtual {p0, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->O5(Ljava/lang/String;Z)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method private final T3()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->I1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method private final Y3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->k1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method private final c4()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public static final d4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->D6(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final e4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;LrZ0/b;I)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, LrZ0/b;->a()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p0, v0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y6(Ljava/lang/String;II)V

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final f4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->I6(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final g4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->J6(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final j4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-interface {p2, p1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    invoke-virtual {p0, v0, v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y6(Ljava/lang/String;II)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method private final k4(Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lorg/xbet/ui_common/utils/h;->l(Landroid/content/Context;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->s4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->j4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;JZ)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->R3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;JZ)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final n4(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->k1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->g4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final o4(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v1:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->P3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    sget v3, Lpb/k;->access_denied_with_bonus_currency_message:I

    .line 10
    .line 11
    invoke-virtual {p0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    const/16 v8, 0x3c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    const/16 v10, 0x1fc

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    move-object v2, p0

    .line 33
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->S3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->d4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->f4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final s4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->b4()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Q3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lra1/c;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;LrZ0/b;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->e4(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;LrZ0/b;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic v3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lpa1/j;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->O3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lpa1/j;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)Lpa1/j;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->W3()Lpa1/j;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->X3()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    return-wide v0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)J
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Y3()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    return-wide v0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)LS91/W;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/W;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->V1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/W;->j:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->S1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final U3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final V3()LF81/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->x1:LF81/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final W3()Lpa1/j;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->F1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lpa1/j;

    .line 8
    .line 9
    return-object v0
.end method

.method public final X3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->o0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final Z3()LS91/W;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->H1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/W;

    .line 13
    .line 14
    return-object v0
.end method

.method public a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->P1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final b4()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->y1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final h4(Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b;",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$a;->a:Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    invoke-virtual {p0, p2, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->i4(Ljava/util/List;Z)V

    .line 11
    .line 12
    .line 13
    return-void

    .line 14
    :cond_0
    sget-object v0, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$b;->a:Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$b;

    .line 15
    .line 16
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    const/4 p1, 0x0

    .line 23
    invoke-virtual {p0, p2, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->i4(Ljava/util/List;Z)V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_1
    sget-object p2, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$c;->a:Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel$b$c;

    .line 28
    .line 29
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_2

    .line 34
    .line 35
    return-void

    .line 36
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 37
    .line 38
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 39
    .line 40
    .line 41
    throw p1
.end method

.method public final i4(Ljava/util/List;Z)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_7

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->U3()J

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    const-wide/16 v2, 0x0

    .line 12
    .line 13
    cmp-long v4, v0, v2

    .line 14
    .line 15
    if-nez v4, :cond_0

    .line 16
    .line 17
    goto :goto_2

    .line 18
    :cond_0
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    const/4 v4, 0x0

    .line 27
    if-eqz v1, :cond_2

    .line 28
    .line 29
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    move-object v5, v1

    .line 34
    check-cast v5, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 35
    .line 36
    invoke-virtual {v5}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 37
    .line 38
    .line 39
    move-result v5

    .line 40
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->U3()J

    .line 41
    .line 42
    .line 43
    move-result-wide v6

    .line 44
    long-to-int v7, v6

    .line 45
    if-ne v5, v7, :cond_1

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_2
    move-object v1, v4

    .line 49
    :goto_0
    check-cast v1, Lorg/xplatform/banners/api/domain/models/BannerModel;

    .line 50
    .line 51
    if-nez v1, :cond_3

    .line 52
    .line 53
    invoke-virtual {p0, v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->l4(J)V

    .line 54
    .line 55
    .line 56
    return-void

    .line 57
    :cond_3
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->c4()Z

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    if-nez v0, :cond_5

    .line 62
    .line 63
    invoke-static {v1}, Lq81/a;->a(Lorg/xplatform/banners/api/domain/models/BannerModel;)Z

    .line 64
    .line 65
    .line 66
    move-result v0

    .line 67
    if-eqz v0, :cond_5

    .line 68
    .line 69
    if-eqz p2, :cond_5

    .line 70
    .line 71
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 72
    .line 73
    .line 74
    move-result-object p2

    .line 75
    instance-of v0, p2, Landroidx/appcompat/app/AppCompatActivity;

    .line 76
    .line 77
    if-eqz v0, :cond_4

    .line 78
    .line 79
    move-object v4, p2

    .line 80
    check-cast v4, Landroidx/appcompat/app/AppCompatActivity;

    .line 81
    .line 82
    :cond_4
    if-eqz v4, :cond_6

    .line 83
    .line 84
    new-instance p2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;

    .line 85
    .line 86
    invoke-direct {p2, p0, v1, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/h;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lorg/xplatform/banners/api/domain/models/BannerModel;Ljava/util/List;)V

    .line 87
    .line 88
    .line 89
    const-string p1, "REQUEST_ATTENTION_DIALOG_KEY"

    .line 90
    .line 91
    invoke-static {v4, p1, p2}, LVZ0/c;->d(Landroidx/appcompat/app/AppCompatActivity;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 92
    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 96
    .line 97
    .line 98
    move-result-object p2

    .line 99
    const-class v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;

    .line 100
    .line 101
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    invoke-virtual {v1}, Lorg/xplatform/banners/api/domain/models/BannerModel;->getBannerId()I

    .line 106
    .line 107
    .line 108
    move-result v4

    .line 109
    invoke-interface {p1, v1}, Ljava/util/List;->indexOf(Ljava/lang/Object;)I

    .line 110
    .line 111
    .line 112
    move-result p1

    .line 113
    invoke-virtual {p2, v0, v4, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->y6(Ljava/lang/String;II)V

    .line 114
    .line 115
    .line 116
    :cond_6
    :goto_1
    invoke-virtual {p0, v2, v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->l4(J)V

    .line 117
    .line 118
    .line 119
    :cond_7
    :goto_2
    return-void
.end method

.method public final l4(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final m4(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->o0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->v2:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    new-instance p1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/g;

    .line 5
    .line 6
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/g;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 7
    .line 8
    .line 9
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/i;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v0, v0, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->K6()V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->B6()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->C6()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final q4(I)V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->P2()LzX0/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Ly01/g;

    .line 6
    .line 7
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 8
    .line 9
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    const/16 v8, 0x3c

    .line 14
    .line 15
    const/4 v9, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    const/4 v6, 0x0

    .line 19
    const/4 v7, 0x0

    .line 20
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    const/16 v10, 0x1fc

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    move-object v2, p0

    .line 31
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final r4(Z)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->c6()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iget-object v1, v1, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 14
    .line 15
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$b;

    .line 16
    .line 17
    invoke-direct {v2, p0, p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$b;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Z)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v1, v2}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget-object v1, v1, LS91/W;->i:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 28
    .line 29
    sget v2, Lpb/k;->update_again_after:I

    .line 30
    .line 31
    const-wide/16 v3, 0x2710

    .line 32
    .line 33
    invoke-virtual {v1, v0, v2, v3, v4}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iget-object v0, v0, LS91/W;->i:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    if-eqz p1, :cond_0

    .line 44
    .line 45
    const/4 p1, 0x0

    .line 46
    goto :goto_0

    .line 47
    :cond_0
    const/16 p1, 0x8

    .line 48
    .line 49
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    iget-object p1, p1, LS91/W;->g:Landroidx/core/widget/NestedScrollView;

    .line 57
    .line 58
    invoke-virtual {p1, v1, v1}, Landroidx/core/widget/NestedScrollView;->scrollTo(II)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 12

    .line 1
    invoke-super {p0, p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->P5()V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->c4()Z

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    sget v0, Lpb/k;->my_virtual:I

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;->setTitle(Ljava/lang/CharSequence;)V

    .line 28
    .line 29
    .line 30
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object p1, p1, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 35
    .line 36
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 37
    .line 38
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    sget v2, Lpb/f;->space_8:I

    .line 43
    .line 44
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    sget v3, Lpb/f;->space_8:I

    .line 53
    .line 54
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    const/16 v10, 0x1da

    .line 59
    .line 60
    const/4 v11, 0x0

    .line 61
    const/4 v2, 0x0

    .line 62
    const/4 v4, 0x0

    .line 63
    const/4 v5, 0x0

    .line 64
    const/4 v6, 0x1

    .line 65
    const/4 v7, 0x0

    .line 66
    const/4 v8, 0x0

    .line 67
    const/4 v9, 0x0

    .line 68
    invoke-direct/range {v0 .. v11}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iget-object p1, p1, LS91/W;->e:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 79
    .line 80
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/a;

    .line 81
    .line 82
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/a;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function2;)V

    .line 86
    .line 87
    .line 88
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iget-object p1, p1, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 93
    .line 94
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->W3()Lpa1/j;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 99
    .line 100
    .line 101
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    iget-object p1, p1, LS91/W;->k:Landroidx/recyclerview/widget/RecyclerView;

    .line 106
    .line 107
    const/4 v0, 0x0

    .line 108
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 109
    .line 110
    .line 111
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    iget-object p1, p1, LS91/W;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 116
    .line 117
    invoke-virtual {p1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getAuthorizationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/c;

    .line 122
    .line 123
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/c;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 124
    .line 125
    .line 126
    const/4 v2, 0x1

    .line 127
    invoke-static {p1, v0, v1, v2, v0}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 128
    .line 129
    .line 130
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->Z3()LS91/W;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    iget-object p1, p1, LS91/W;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 135
    .line 136
    invoke-virtual {p1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getRegistrationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 137
    .line 138
    .line 139
    move-result-object p1

    .line 140
    new-instance v1, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/d;

    .line 141
    .line 142
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/d;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 143
    .line 144
    .line 145
    invoke-static {p1, v0, v1, v2, v0}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 146
    .line 147
    .line 148
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lna1/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lna1/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lna1/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->c4()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    invoke-virtual {v2, v0}, Lna1/d;->a(Z)Lna1/c;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {v0, p0}, Lna1/c;->a(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;)V

    .line 61
    .line 62
    .line 63
    return-void

    .line 64
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 65
    .line 66
    new-instance v2, Ljava/lang/StringBuilder;

    .line 67
    .line 68
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 69
    .line 70
    .line 71
    const-string v3, "Cannot create dependency "

    .line 72
    .line 73
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 88
    .line 89
    .line 90
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->Y5()Lkotlinx/coroutines/flow/f0;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->R5()Lkotlinx/coroutines/flow/f0;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 55
    .line 56
    .line 57
    move-result-object v9

    .line 58
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$2;

    .line 59
    .line 60
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 61
    .line 62
    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->w0()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->T3()Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/base/presentation/AggregatorBalanceViewModel;->z3()Lkotlinx/coroutines/flow/e;

    .line 116
    .line 117
    .line 118
    move-result-object v2

    .line 119
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 120
    .line 121
    .line 122
    move-result-object v3

    .line 123
    invoke-virtual {v3}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->h6()Lkotlinx/coroutines/flow/f0;

    .line 124
    .line 125
    .line 126
    move-result-object v3

    .line 127
    new-instance v4, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$4;

    .line 128
    .line 129
    invoke-direct {v4, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 130
    .line 131
    .line 132
    invoke-static {v2, v3, v4}, Lkotlinx/coroutines/flow/g;->W(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 133
    .line 134
    .line 135
    move-result-object v8

    .line 136
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$5;

    .line 137
    .line 138
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$5;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 139
    .line 140
    .line 141
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 142
    .line 143
    .line 144
    move-result-object v9

    .line 145
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 146
    .line 147
    .line 148
    move-result-object v2

    .line 149
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 150
    .line 151
    move-object v7, v5

    .line 152
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 153
    .line 154
    .line 155
    const/4 v7, 0x0

    .line 156
    const/4 v3, 0x0

    .line 157
    const/4 v4, 0x0

    .line 158
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 159
    .line 160
    .line 161
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 162
    .line 163
    .line 164
    move-result-object v2

    .line 165
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->a6()Lkotlinx/coroutines/flow/Z;

    .line 166
    .line 167
    .line 168
    move-result-object v8

    .line 169
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 170
    .line 171
    .line 172
    move-result-object v9

    .line 173
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$6;

    .line 174
    .line 175
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$6;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 176
    .line 177
    .line 178
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 179
    .line 180
    .line 181
    move-result-object v2

    .line 182
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$5;

    .line 183
    .line 184
    move-object v7, v5

    .line 185
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$5;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 186
    .line 187
    .line 188
    const/4 v7, 0x0

    .line 189
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 190
    .line 191
    .line 192
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 193
    .line 194
    .line 195
    move-result-object v2

    .line 196
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->f6()Lkotlinx/coroutines/flow/Z;

    .line 197
    .line 198
    .line 199
    move-result-object v8

    .line 200
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 201
    .line 202
    .line 203
    move-result-object v9

    .line 204
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$7;

    .line 205
    .line 206
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$7;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 207
    .line 208
    .line 209
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 210
    .line 211
    .line 212
    move-result-object v2

    .line 213
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$6;

    .line 214
    .line 215
    move-object v7, v5

    .line 216
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$6;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 217
    .line 218
    .line 219
    const/4 v7, 0x0

    .line 220
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 221
    .line 222
    .line 223
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 224
    .line 225
    .line 226
    move-result-object v2

    .line 227
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->X5()Lkotlinx/coroutines/flow/f0;

    .line 228
    .line 229
    .line 230
    move-result-object v8

    .line 231
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$8;

    .line 232
    .line 233
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$8;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 234
    .line 235
    .line 236
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 237
    .line 238
    .line 239
    move-result-object v9

    .line 240
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 241
    .line 242
    .line 243
    move-result-object v2

    .line 244
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$7;

    .line 245
    .line 246
    move-object v7, v5

    .line 247
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$7;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 248
    .line 249
    .line 250
    const/4 v7, 0x0

    .line 251
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 252
    .line 253
    .line 254
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;->a4()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 255
    .line 256
    .line 257
    move-result-object v2

    .line 258
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;->T5()Lkotlinx/coroutines/flow/f0;

    .line 259
    .line 260
    .line 261
    move-result-object v8

    .line 262
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 263
    .line 264
    .line 265
    move-result-object v9

    .line 266
    new-instance v11, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;

    .line 267
    .line 268
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$9;-><init>(Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment;Lkotlin/coroutines/e;)V

    .line 269
    .line 270
    .line 271
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 272
    .line 273
    .line 274
    move-result-object v2

    .line 275
    new-instance v5, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$8;

    .line 276
    .line 277
    move-object v7, v5

    .line 278
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/fragments/MyAggregatorFragment$onObserveData$$inlined$observeWithLifecycle$default$8;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 279
    .line 280
    .line 281
    const/4 v7, 0x0

    .line 282
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 283
    .line 284
    .line 285
    return-void
.end method
