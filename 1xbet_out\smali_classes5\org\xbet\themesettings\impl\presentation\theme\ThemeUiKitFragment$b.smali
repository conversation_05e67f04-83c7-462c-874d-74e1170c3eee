.class public final Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->K2(ZLjava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0010\u0003\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0001\u0010\u0002"
    }
    d2 = {
        "",
        "run",
        "()V",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

.field public final synthetic b:Z


# direct methods
.method public constructor <init>(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;Z)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    iput-boolean p2, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;->b:Z

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;->a:Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;->H2(Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment;)Lorg/xbet/themesettings/impl/presentation/theme/m;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-boolean v1, p0, Lorg/xbet/themesettings/impl/presentation/theme/ThemeUiKitFragment$b;->b:Z

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Lorg/xbet/themesettings/impl/presentation/theme/m;->B3(Z)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
