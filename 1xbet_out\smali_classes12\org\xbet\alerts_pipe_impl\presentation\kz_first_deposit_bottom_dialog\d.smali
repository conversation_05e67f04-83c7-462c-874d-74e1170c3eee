.class public final synthetic Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/d;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    return-void
.end method


# virtual methods
.method public final invoke(<PERSON><PERSON><PERSON>/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/d;->a:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;

    check-cast p1, Landroid/graphics/drawable/Drawable;

    invoke-static {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet$onObserveData$1;->a(Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositBottomSheet;Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
