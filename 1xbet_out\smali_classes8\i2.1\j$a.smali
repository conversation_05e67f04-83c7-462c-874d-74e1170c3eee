.class public final Li2/j$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Li2/j;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:LN1/W$c;

.field public final b:LN1/W$a;

.field public final c:[B

.field public final d:[LN1/W$b;

.field public final e:I


# direct methods
.method public constructor <init>(LN1/W$c;LN1/W$a;[B[LN1/W$b;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Li2/j$a;->a:LN1/W$c;

    .line 5
    .line 6
    iput-object p2, p0, Li2/j$a;->b:LN1/W$a;

    .line 7
    .line 8
    iput-object p3, p0, Li2/j$a;->c:[B

    .line 9
    .line 10
    iput-object p4, p0, Li2/j$a;->d:[LN1/W$b;

    .line 11
    .line 12
    iput p5, p0, Li2/j$a;->e:I

    .line 13
    .line 14
    return-void
.end method
