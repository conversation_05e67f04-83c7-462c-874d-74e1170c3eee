.class public final synthetic Leb1/X;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:LB4/a;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Leb1/X;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, Leb1/X;->b:LB4/a;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Leb1/X;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, Leb1/X;->b:LB4/a;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTitleDelegateKt;->a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)V

    return-void
.end method
