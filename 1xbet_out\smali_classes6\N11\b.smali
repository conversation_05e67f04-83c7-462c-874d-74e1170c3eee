.class public final LN11/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a7\u0010\u0008\u001a\u00020\u0007*\u00020\u00002\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u00012\u001a\u0010\u0006\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u0000\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Landroid/widget/CompoundButton;",
        "Lorg/xbet/uikit/utils/debounce/Interval;",
        "minimumInterval",
        "Lkotlin/Function2;",
        "",
        "",
        "function",
        "Landroid/widget/CompoundButton$OnCheckedChangeListener;",
        "a",
        "(Landroid/widget/CompoundButton;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function2;)Landroid/widget/CompoundButton$OnCheckedChangeListener;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Landroid/widget/CompoundButton;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function2;)Landroid/widget/CompoundButton$OnCheckedChangeListener;
    .locals 6
    .param p0    # Landroid/widget/CompoundButton;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit/utils/debounce/Interval;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/CompoundButton;",
            "Lorg/xbet/uikit/utils/debounce/Interval;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroid/widget/CompoundButton;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)",
            "Landroid/widget/CompoundButton$OnCheckedChangeListener;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LN11/a;

    .line 2
    .line 3
    const/4 v4, 0x2

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    move-object v1, p1

    .line 7
    move-object v3, p2

    .line 8
    invoke-direct/range {v0 .. v5}, LN11/a;-><init>(Lorg/xbet/uikit/utils/debounce/Interval;ZLkotlin/jvm/functions/Function2;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic b(Landroid/widget/CompoundButton;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Landroid/widget/CompoundButton$OnCheckedChangeListener;
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit/utils/E;->b:Lorg/xbet/uikit/utils/E$a;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xbet/uikit/utils/E$a;->a()Lorg/xbet/uikit/utils/debounce/Interval;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    :cond_0
    invoke-static {p0, p1, p2}, LN11/b;->a(Landroid/widget/CompoundButton;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function2;)Landroid/widget/CompoundButton$OnCheckedChangeListener;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    return-object p0
.end method
