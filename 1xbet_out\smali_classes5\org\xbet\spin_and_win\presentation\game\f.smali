.class public final synthetic Lorg/xbet/spin_and_win/presentation/game/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

.field public final synthetic b:Lbz0/c;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/f;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    iput-object p2, p0, Lorg/xbet/spin_and_win/presentation/game/f;->b:Lbz0/c;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/f;->a:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/f;->b:Lbz0/c;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->C2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lbz0/c;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
