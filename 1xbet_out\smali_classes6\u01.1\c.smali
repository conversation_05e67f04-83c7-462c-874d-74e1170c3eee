.class public final synthetic Lu01/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/animation/ValueAnimator$AnimatorUpdateListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lu01/c;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;

    return-void
.end method


# virtual methods
.method public final onAnimationUpdate(Landroid/animation/ValueAnimator;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lu01/c;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;

    invoke-static {v0, p1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;->i(Lorg/xbet/uikit/components/segmentedcontrol/SegmentItem;Landroid/animation/ValueAnimator;)V

    return-void
.end method
