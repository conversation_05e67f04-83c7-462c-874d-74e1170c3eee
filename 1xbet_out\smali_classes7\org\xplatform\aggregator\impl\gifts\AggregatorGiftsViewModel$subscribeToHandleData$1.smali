.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$subscribeToHandleData$1"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x109
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g6()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/balance/model/BalanceModel;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "balance",
        "Lorg/xbet/balance/model/BalanceModel;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->invoke(Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/balance/model/BalanceModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->L$0:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Lorg/xbet/balance/model/BalanceModel;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->L$0:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 36
    .line 37
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->u4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)J

    .line 38
    .line 39
    .line 40
    move-result-wide v3

    .line 41
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 42
    .line 43
    .line 44
    move-result-wide v5

    .line 45
    cmp-long v1, v3, v5

    .line 46
    .line 47
    if-eqz v1, :cond_4

    .line 48
    .line 49
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 50
    .line 51
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->L$0:Ljava/lang/Object;

    .line 52
    .line 53
    iput v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->label:I

    .line 54
    .line 55
    invoke-static {v1, p0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->X4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    if-ne v1, v0, :cond_2

    .line 60
    .line 61
    return-object v0

    .line 62
    :cond_2
    move-object v0, p1

    .line 63
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 64
    .line 65
    invoke-virtual {v0}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v1

    .line 69
    invoke-static {p1, v1, v2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->Z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;J)V

    .line 70
    .line 71
    .line 72
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 73
    .line 74
    invoke-virtual {v0}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-virtual {v0}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isPrimary()Z

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    if-eqz v0, :cond_3

    .line 83
    .line 84
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 85
    .line 86
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->I4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    goto :goto_1

    .line 91
    :cond_3
    sget-object v0, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->ALL:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    .line 92
    .line 93
    :goto_1
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->a5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lorg/xplatform/aggregator/api/navigation/GiftsChipType;)V

    .line 94
    .line 95
    .line 96
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$subscribeToHandleData$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 97
    .line 98
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->g5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)V

    .line 99
    .line 100
    .line 101
    :cond_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 102
    .line 103
    return-object p1
.end method
