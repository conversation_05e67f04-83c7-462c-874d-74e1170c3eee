.class public final synthetic Lc11/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Landroidx/compose/ui/l;

.field public final synthetic c:Z

.field public final synthetic d:L<PERSON><PERSON>/jvm/functions/Function1;

.field public final synthetic e:Landroidx/compose/foundation/interaction/i;

.field public final synthetic f:I

.field public final synthetic g:I


# direct methods
.method public synthetic constructor <init>(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lc11/a;->a:Z

    iput-object p2, p0, Lc11/a;->b:Landroidx/compose/ui/l;

    iput-boolean p3, p0, Lc11/a;->c:Z

    iput-object p4, p0, Lc11/a;->d:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    iput-object p5, p0, Lc11/a;->e:Landroidx/compose/foundation/interaction/i;

    iput p6, p0, Lc11/a;->f:I

    iput p7, p0, Lc11/a;->g:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    iget-boolean v0, p0, Lc11/a;->a:Z

    iget-object v1, p0, Lc11/a;->b:Landroidx/compose/ui/l;

    iget-boolean v2, p0, Lc11/a;->c:Z

    iget-object v3, p0, Lc11/a;->d:Lkotlin/jvm/functions/Function1;

    iget-object v4, p0, Lc11/a;->e:Landroidx/compose/foundation/interaction/i;

    iget v5, p0, Lc11/a;->f:I

    iget v6, p0, Lc11/a;->g:I

    move-object v7, p1

    check-cast v7, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v8

    invoke-static/range {v0 .. v8}, Lc11/b;->a(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
