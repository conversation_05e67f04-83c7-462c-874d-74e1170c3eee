.class final synthetic Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$lottieConfig$2$1;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;-><init>(Landroidx/lifecycle/Q;Lorg/xbet/analytics/domain/scope/f1;Lorg/xbet/swipex/impl/domain/scenario/LoadSwipexCardListScenario;Lorg/xbet/swipex/impl/domain/usecases/GetSportsFromLocaleUseCase;Lorg/xbet/swipex/impl/domain/usecases/h;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelGroupByIdUseCase;Lorg/xbet/swipex/impl/domain/usecases/j;LGS0/a;Lorg/xbet/swipex/impl/domain/scenario/GetCurrencyModelScenario;Lorg/xbet/swipex/impl/domain/usecases/GetEventModelByIdUseCase;Li8/m;Lorg/xbet/swipex/impl/domain/scenario/MakeBetScenario;LwX0/c;ZLm8/a;Lorg/xbet/swipex/impl/domain/usecases/UpdateLineCardUseCase;Lorg/xbet/swipex/impl/domain/usecases/UpdateLiveCardUseCase;Lorg/xbet/swipex/impl/domain/scenario/LoadAllFilterSportsAndChampsScenario;Lorg/xbet/swipex/impl/domain/scenario/GetSwipexBetSettingsScenario;LxX0/a;LSX0/a;Ljo/a;LHX0/e;Lorg/xbet/swipex/impl/domain/usecases/n;Lorg/xbet/swipex/impl/domain/usecases/N;LkS/a;Lorg/xbet/swipex/impl/domain/usecases/L;Lfk/i;Lfk/j;Ljava/lang/String;Lh9/a;Lorg/xbet/feed/subscriptions/domain/usecases/r;Lek/c;Lorg/xbet/swipex/impl/domain/scenario/b;Lorg/xbet/swipex/impl/domain/usecases/P;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function0<",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/Object;)V
    .locals 7

    const-string v5, "onLottieButtonClick()V"

    const/4 v6, 0x0

    const/4 v1, 0x0

    const-class v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    const-string v4, "onLottieButtonClick"

    move-object v0, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v6}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$lottieConfig$2$1;->invoke()V

    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object v0
.end method

.method public final invoke()V
    .locals 1

    .line 2
    iget-object v0, p0, Lkotlin/jvm/internal/CallableReference;->receiver:Ljava/lang/Object;

    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    invoke-static {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->X3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)V

    return-void
.end method
