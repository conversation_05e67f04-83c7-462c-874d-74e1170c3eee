.class public final Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/uikit_sport/express_card/SportExpressCard;->setMarketDescription(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function2<",
        "Landroidx/compose/runtime/j;",
        "Ljava/lang/Integer;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/express_card/SportExpressCard;


# direct methods
.method public constructor <init>(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;->a:Lorg/xbet/uikit_sport/express_card/SportExpressCard;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/compose/runtime/j;I)V
    .locals 9

    .line 1
    and-int/lit8 v0, p2, 0x3

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    if-ne v0, v1, :cond_1

    .line 5
    .line 6
    invoke-interface {p1}, Landroidx/compose/runtime/j;->c()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-interface {p1}, Landroidx/compose/runtime/j;->n()V

    .line 14
    .line 15
    .line 16
    return-void

    .line 17
    :cond_1
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_2

    .line 22
    .line 23
    const/4 v0, -0x1

    .line 24
    const-string v1, "org.xbet.uikit_sport.express_card.SportExpressCard.setMarketDescription.<anonymous> (SportExpressCard.kt:177)"

    .line 25
    .line 26
    const v2, -0x5d36e296

    .line 27
    .line 28
    .line 29
    invoke-static {v2, p2, v0, v1}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 30
    .line 31
    .line 32
    :cond_2
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;->a:Lorg/xbet/uikit_sport/express_card/SportExpressCard;

    .line 33
    .line 34
    invoke-static {p2}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lu31/a$b;

    .line 35
    .line 36
    .line 37
    move-result-object p2

    .line 38
    const/4 v0, 0x0

    .line 39
    invoke-static {p2, p1, v0}, Landroidx/compose/runtime/i1;->p(Ljava/lang/Object;Landroidx/compose/runtime/j;I)Landroidx/compose/runtime/r1;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;->a:Lorg/xbet/uikit_sport/express_card/SportExpressCard;

    .line 44
    .line 45
    invoke-static {p2}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lu31/b;

    .line 46
    .line 47
    .line 48
    move-result-object v3

    .line 49
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;->a:Lorg/xbet/uikit_sport/express_card/SportExpressCard;

    .line 50
    .line 51
    invoke-static {p2}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lkotlin/jvm/functions/Function0;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    const/4 v7, 0x0

    .line 56
    const/16 v8, 0x11

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    const/4 v5, 0x0

    .line 60
    move-object v6, p1

    .line 61
    invoke-static/range {v1 .. v8}, Lt31/a;->a(Landroidx/compose/ui/l;Landroidx/compose/runtime/r1;Lu31/b;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Landroidx/compose/runtime/j;II)V

    .line 62
    .line 63
    .line 64
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_3

    .line 69
    .line 70
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 71
    .line 72
    .line 73
    :cond_3
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/compose/runtime/j;

    .line 2
    .line 3
    check-cast p2, Ljava/lang/Number;

    .line 4
    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->intValue()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;->a(Landroidx/compose/runtime/j;I)V

    .line 10
    .line 11
    .line 12
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p1
.end method
