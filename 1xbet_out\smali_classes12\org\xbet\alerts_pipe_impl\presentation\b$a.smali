.class public final Lorg/xbet/alerts_pipe_impl/presentation/b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/alerts_pipe_impl/presentation/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001d\u0010\t\u001a\u00020\u00082\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u000c\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_impl/presentation/b$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/uikit/components/dialog/DialogFields;",
        "dialogFields",
        "Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;",
        "alertsPipeType",
        "Lorg/xbet/alerts_pipe_impl/presentation/b;",
        "a",
        "(Lorg/xbet/uikit/components/dialog/DialogFields;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Lorg/xbet/alerts_pipe_impl/presentation/b;",
        "",
        "TYPE_KEY",
        "Ljava/lang/String;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/alerts_pipe_impl/presentation/b$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit/components/dialog/DialogFields;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)Lorg/xbet/alerts_pipe_impl/presentation/b;
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/dialog/DialogFields;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/b;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/alerts_pipe_impl/presentation/b;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1}, Lorg/xbet/alerts_pipe_impl/presentation/b;->O2(Lorg/xbet/alerts_pipe_impl/presentation/b;Lorg/xbet/uikit/components/dialog/DialogFields;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p2}, Lorg/xbet/alerts_pipe_impl/presentation/b;->N2(Lorg/xbet/alerts_pipe_impl/presentation/b;Lorg/xbet/alerts_pipe_api/presentation/AlertsPipeType;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method
