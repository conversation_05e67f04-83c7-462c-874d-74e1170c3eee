.class public final synthetic LI1/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/Comparator;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON><PERSON><PERSON>/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 1
    check-cast p1, <PERSON><PERSON><PERSON>/lang/Integer;

    check-cast p2, L<PERSON><PERSON>/lang/Integer;

    invoke-static {p1, p2}, LI1/n;->v(<PERSON><PERSON><PERSON>/lang/Integer;L<PERSON><PERSON>/lang/Integer;)I

    move-result p1

    return p1
.end method
