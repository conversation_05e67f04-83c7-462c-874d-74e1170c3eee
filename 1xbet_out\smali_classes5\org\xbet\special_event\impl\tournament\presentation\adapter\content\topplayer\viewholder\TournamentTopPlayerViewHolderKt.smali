.class public final Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0007\u001a#\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a#\u0010\u000c\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\r\u001a#\u0010\u000e\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\r\u001a#\u0010\u000f\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\r\u001a#\u0010\u0010\u001a\u00020\u000b*\u0012\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007j\u0002`\nH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\r*$\u0008\u0000\u0010\u0011\"\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00072\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007\u00a8\u0006\u0012"
    }
    d2 = {
        "LEx0/b;",
        "tournamentTopPlayerClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "m",
        "(LEx0/b;)LA4/c;",
        "LB4/a;",
        "LIx0/a;",
        "LGq0/j1;",
        "Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolder;",
        "",
        "l",
        "(LB4/a;)V",
        "k",
        "i",
        "j",
        "TournamentTopPlayerViewHolder",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LEx0/b;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->p(LEx0/b;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LGq0/j1;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->q(LGq0/j1;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LEx0/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->o(LEx0/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/j1;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->n(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/j1;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic e(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->i(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->j(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->k(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h(LB4/a;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt;->l(LB4/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final i(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIx0/a;",
            "LGq0/j1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/j1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/j1;->b:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LIx0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, LIx0/a;->e()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final j(LB4/a;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIx0/a;",
            "LGq0/j1;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    check-cast v1, LGq0/j1;

    .line 8
    .line 9
    iget-object v1, v1, LGq0/j1;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, LIx0/a;

    .line 16
    .line 17
    invoke-virtual {v2}, LIx0/a;->j()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    check-cast p0, LIx0/a;

    .line 26
    .line 27
    invoke-virtual {p0}, LIx0/a;->d()I

    .line 28
    .line 29
    .line 30
    move-result v5

    .line 31
    const/4 v6, 0x3

    .line 32
    const/4 v7, 0x0

    .line 33
    const/4 v2, 0x0

    .line 34
    const/4 v3, 0x0

    .line 35
    invoke-static/range {v0 .. v7}, LCX0/l;->F(LCX0/l;Landroid/widget/ImageView;Lorg/xbet/ui_common/utils/image/ImageCropType;ZLjava/lang/String;IILjava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public static final k(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIx0/a;",
            "LGq0/j1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/j1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/j1;->g:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LIx0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, LIx0/a;->o()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final l(LB4/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LB4/a<",
            "LIx0/a;",
            "LGq0/j1;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LGq0/j1;

    .line 6
    .line 7
    iget-object v0, v0, LGq0/j1;->h:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LIx0/a;

    .line 14
    .line 15
    invoke-virtual {p0}, LIx0/a;->s()I

    .line 16
    .line 17
    .line 18
    move-result p0

    .line 19
    invoke-static {p0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p0

    .line 23
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final m(LEx0/b;)LA4/c;
    .locals 4
    .param p0    # LEx0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LEx0/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LLx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LLx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LLx0/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LLx0/b;-><init>(LEx0/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$tournamentTopPlayerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$tournamentTopPlayerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$tournamentTopPlayerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$tournamentTopPlayerAdapterDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final n(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LGq0/j1;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LGq0/j1;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LGq0/j1;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final o(LEx0/b;LB4/a;)Lkotlin/Unit;
    .locals 5

    .line 1
    new-instance v0, LEx0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LEx0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    check-cast v1, LGq0/j1;

    .line 11
    .line 12
    iget-object v2, v1, LGq0/j1;->i:Lorg/xbet/uikit/components/cells/SettingsCell;

    .line 13
    .line 14
    new-instance v3, LLx0/c;

    .line 15
    .line 16
    invoke-direct {v3, p0, p1}, LLx0/c;-><init>(LEx0/b;LB4/a;)V

    .line 17
    .line 18
    .line 19
    const/4 p0, 0x1

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static {v2, v4, v3, p0, v4}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 22
    .line 23
    .line 24
    iget-object p0, v1, LGq0/j1;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 25
    .line 26
    new-instance v2, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 27
    .line 28
    iget-object v3, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 29
    .line 30
    invoke-virtual {v3}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    const/4 v4, 0x0

    .line 35
    invoke-direct {v2, v3, v4, v4}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0, v2}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 39
    .line 40
    .line 41
    iget-object p0, v1, LGq0/j1;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 42
    .line 43
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 44
    .line 45
    .line 46
    new-instance p0, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;

    .line 47
    .line 48
    invoke-direct {p0, p1, v0, p1, v0}, Lorg/xbet/special_event/impl/tournament/presentation/adapter/content/topplayer/viewholder/TournamentTopPlayerViewHolderKt$a;-><init>(LB4/a;LEx0/a;LB4/a;LEx0/a;)V

    .line 49
    .line 50
    .line 51
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 52
    .line 53
    .line 54
    new-instance p0, LLx0/d;

    .line 55
    .line 56
    invoke-direct {p0, v1}, LLx0/d;-><init>(LGq0/j1;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p1, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 60
    .line 61
    .line 62
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 63
    .line 64
    return-object p0
.end method

.method public static final p(LEx0/b;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LIx0/a;

    .line 6
    .line 7
    invoke-virtual {p1}, LIx0/a;->f()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-interface {p0, p1}, LEx0/b;->g(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final q(LGq0/j1;)Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 2
    .line 3
    iget-object p0, p0, LGq0/j1;->c:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    .line 4
    .line 5
    invoke-virtual {v0, p0}, LCX0/l;->j(Landroid/widget/ImageView;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method
