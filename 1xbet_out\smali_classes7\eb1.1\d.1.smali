.class public final Leb1/d;
.super LVX0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Leb1/d$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0000\u0018\u00002\u00020\u0001:\u0001\u0010BS\u0012\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00040\u0002\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00040\u0002\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00040\u0002\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0011"
    }
    d2 = {
        "Leb1/d;",
        "LVX0/a;",
        "Lkotlin/Function1;",
        "Llb1/q;",
        "",
        "onShowAllClick",
        "",
        "onGameClick",
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "Lkb1/z$c;",
        "onPrizeClick",
        "Landroidx/lifecycle/LifecycleCoroutineScope;",
        "lifecycleCoroutineScope",
        "<init>",
        "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LUX0/k;Lkotlin/jvm/functions/Function1;Landroidx/lifecycle/LifecycleCoroutineScope;)V",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LUX0/k;Lkotlin/jvm/functions/Function1;Landroidx/lifecycle/LifecycleCoroutineScope;)V
    .locals 2
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Landroidx/lifecycle/LifecycleCoroutineScope;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Llb1/q;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Long;",
            "Lkotlin/Unit;",
            ">;",
            "LUX0/k;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lkb1/z$c;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/lifecycle/LifecycleCoroutineScope;",
            ")V"
        }
    .end annotation

    .line 1
    sget-object v0, Leb1/d$a;->a:Leb1/d$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LVX0/a;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentBannerDelegateKt;->d()LA4/c;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTimerDelegateKt;->d()LA4/c;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTitleDelegateKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/result/TournamentResultProgressDelegateKt;->e()LA4/c;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p1, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/result/TournamentResultBannerDelegateKt;->d()LA4/c;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {p1, v0}, LA4/d;->c(LA4/c;)LA4/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-static {p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentTopGamesDelegateKt;->h(Lkotlin/jvm/functions/Function1;LUX0/k;)LA4/c;

    .line 49
    .line 50
    .line 51
    move-result-object p2

    .line 52
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentStagesDelegateKt;->d()LA4/c;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentRulesDelegateKt;->d()LA4/c;

    .line 65
    .line 66
    .line 67
    move-result-object p2

    .line 68
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-static {p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentProvidersDelegateKt;->d(Landroidx/lifecycle/LifecycleCoroutineScope;)LA4/c;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-static {p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/adapters/main_info/TournamentPrizesDelegateKt;->d(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 81
    .line 82
    .line 83
    move-result-object p2

    .line 84
    invoke-virtual {p1, p2}, LA4/d;->c(LA4/c;)LA4/d;

    .line 85
    .line 86
    .line 87
    return-void
.end method
