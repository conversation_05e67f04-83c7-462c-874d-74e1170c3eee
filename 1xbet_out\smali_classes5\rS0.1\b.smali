.class public final LrS0/b;
.super Ljava/lang/Object;


# static fields
.field public static allCardsSwipedAlert:I = 0x7f0a00e5

.field public static apply:I = 0x7f0a010c

.field public static backgroundImage:I = 0x7f0a0154

.field public static badge:I = 0x7f0a0161

.field public static balanceTitle:I = 0x7f0a0171

.field public static balanceTitleColon:I = 0x7f0a0172

.field public static balanceValue:I = 0x7f0a0174

.field public static balanceView:I = 0x7f0a0175

.field public static betResult:I = 0x7f0a01c5

.field public static betResultValue:I = 0x7f0a01c6

.field public static betSumTitle:I = 0x7f0a01d3

.field public static betSumTitleColon:I = 0x7f0a01d4

.field public static betSumValue:I = 0x7f0a01d6

.field public static btnConfirm:I = 0x7f0a02a9

.field public static btnNext:I = 0x7f0a02c7

.field public static btnSkip:I = 0x7f0a02ea

.field public static btn_next:I = 0x7f0a030d

.field public static cardGradient:I = 0x7f0a0372

.field public static cardStack:I = 0x7f0a0376

.field public static centerVertical:I = 0x7f0a03fa

.field public static changeFiltersText:I = 0x7f0a0424

.field public static coefficient:I = 0x7f0a0525

.field public static coefficientValue:I = 0x7f0a0529

.field public static currencySymbol:I = 0x7f0a05dc

.field public static dislikeButton:I = 0x7f0a0671

.field public static editor:I = 0x7f0a06bf

.field public static emptyCardWithLoader:I = 0x7f0a06d4

.field public static emptyFilterItemEight:I = 0x7f0a06ee

.field public static emptyFilterItemEighteen:I = 0x7f0a06ef

.field public static emptyFilterItemEleven:I = 0x7f0a06f0

.field public static emptyFilterItemFifteen:I = 0x7f0a06f1

.field public static emptyFilterItemFive:I = 0x7f0a06f2

.field public static emptyFilterItemFour:I = 0x7f0a06f3

.field public static emptyFilterItemFourteen:I = 0x7f0a06f4

.field public static emptyFilterItemNine:I = 0x7f0a06f5

.field public static emptyFilterItemOne:I = 0x7f0a06f6

.field public static emptyFilterItemSeven:I = 0x7f0a06f7

.field public static emptyFilterItemSeventeen:I = 0x7f0a06f8

.field public static emptyFilterItemSix:I = 0x7f0a06f9

.field public static emptyFilterItemSixteen:I = 0x7f0a06fa

.field public static emptyFilterItemThirteen:I = 0x7f0a06fb

.field public static emptyFilterItemThree:I = 0x7f0a06fc

.field public static emptyFilterItemTwelve:I = 0x7f0a06fd

.field public static emptyFilterItemTwo:I = 0x7f0a06fe

.field public static errorView:I = 0x7f0a0742

.field public static eventName:I = 0x7f0a0757

.field public static firstTeamFirstIcon:I = 0x7f0a083d

.field public static firstTeamIcon:I = 0x7f0a083f

.field public static firstTeamName:I = 0x7f0a0842

.field public static firstTeamSecondIcon:I = 0x7f0a0844

.field public static fixedBetButton1:I = 0x7f0a0864

.field public static fixedBetButton2:I = 0x7f0a0865

.field public static fixedBetButton3:I = 0x7f0a0866

.field public static fixedBetButton4:I = 0x7f0a0867

.field public static fixedBetLabel:I = 0x7f0a0868

.field public static glyphIcon:I = 0x7f0a096e

.field public static goToFilterButton:I = 0x7f0a096f

.field public static guidelineNameBottom:I = 0x7f0a0a1d

.field public static guidelineNameTop:I = 0x7f0a0a1e

.field public static header:I = 0x7f0a0a67

.field public static icon:I = 0x7f0a0ac8

.field public static imageViewArrow:I = 0x7f0a0af7

.field public static imageViewChampSelected:I = 0x7f0a0af8

.field public static imageViewPreview:I = 0x7f0a0b07

.field public static imageViewSelected:I = 0x7f0a0b0a

.field public static imageViewSport:I = 0x7f0a0b0c

.field public static indicator:I = 0x7f0a0b8e

.field public static infoText:I = 0x7f0a0b9f

.field public static ivClose:I = 0x7f0a0bfd

.field public static ivDescription:I = 0x7f0a0c32

.field public static ivDescriptionForEditor:I = 0x7f0a0c33

.field public static likeButton:I = 0x7f0a0e04

.field public static loading:I = 0x7f0a0ecc

.field public static lottieEmptyView:I = 0x7f0a0eef

.field public static market:I = 0x7f0a0f11

.field public static marketContainer:I = 0x7f0a0f14

.field public static marketName:I = 0x7f0a0f18

.field public static name:I = 0x7f0a0fac

.field public static navigationBar:I = 0x7f0a0faf

.field public static onBoardingCardPlaceholder:I = 0x7f0a1002

.field public static parent:I = 0x7f0a104c

.field public static possibleWin:I = 0x7f0a10e5

.field public static possibleWinValue:I = 0x7f0a10ef

.field public static recyclerFilter:I = 0x7f0a11a1

.field public static reset_filters:I = 0x7f0a11ef

.field public static root:I = 0x7f0a1215

.field public static secondTeamFirstIcon:I = 0x7f0a1375

.field public static secondTeamIcon:I = 0x7f0a1377

.field public static secondTeamName:I = 0x7f0a137a

.field public static secondTeamSecondIcon:I = 0x7f0a137c

.field public static separator:I = 0x7f0a13c7

.field public static sportIcon:I = 0x7f0a15b0

.field public static sportImageGradient:I = 0x7f0a15b3

.field public static sportRecycler:I = 0x7f0a15b5

.field public static staticNavigationBar:I = 0x7f0a15f5

.field public static subGameName:I = 0x7f0a1624

.field public static subtitle:I = 0x7f0a163b

.field public static sumStepperField:I = 0x7f0a1642

.field public static teamName:I = 0x7f0a16de

.field public static textViewCountChamps:I = 0x7f0a1737

.field public static textViewDescription:I = 0x7f0a1739

.field public static textViewSportName:I = 0x7f0a174a

.field public static textViewTitle:I = 0x7f0a1751

.field public static timer:I = 0x7f0a17f3

.field public static title:I = 0x7f0a1808

.field public static tvDescription:I = 0x7f0a1a20

.field public static tvDescriptionForEditor:I = 0x7f0a1a21

.field public static tvTitle:I = 0x7f0a1cac

.field public static viewPagerPreview:I = 0x7f0a1f62

.field public static viewRounded:I = 0x7f0a1f6b

.field public static vs:I = 0x7f0a1fbd


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
