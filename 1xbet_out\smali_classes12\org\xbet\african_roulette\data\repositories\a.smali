.class public final synthetic Lorg/xbet/african_roulette/data/repositories/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/african_roulette/data/repositories/a;->a:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/data/repositories/a;->a:Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;

    invoke-static {v0}, Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;->a(Lorg/xbet/african_roulette/data/repositories/AfricanRouletteRepository;)Lcg/a;

    move-result-object v0

    return-object v0
.end method
