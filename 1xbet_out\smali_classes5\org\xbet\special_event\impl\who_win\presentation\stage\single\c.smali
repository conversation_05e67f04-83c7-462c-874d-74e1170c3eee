.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/single/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;LCy0/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->i0:LCy0/f;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/single/SingleStageFragment;->j0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 2
    .line 3
    return-void
.end method
