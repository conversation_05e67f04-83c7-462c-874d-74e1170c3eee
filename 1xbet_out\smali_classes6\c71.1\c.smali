.class public interface abstract Lc71/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lc71/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001:\u0001\u000cJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0017\u0010\t\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u0007H&\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH&\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lc71/c;",
        "",
        "Lorg/xbet/west_gold/presentation/holder/WestGoldHolderFragment;",
        "holderFragment",
        "",
        "c",
        "(Lorg/xbet/west_gold/presentation/holder/WestGoldHolderFragment;)V",
        "Lorg/xbet/west_gold/presentation/game/WestGoldGameFragment;",
        "gameFragment",
        "b",
        "(Lorg/xbet/west_gold/presentation/game/WestGoldGameFragment;)V",
        "LQv/a$a;",
        "a",
        "()LQv/a$a;",
        "west_gold_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a()LQv/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract b(Lorg/xbet/west_gold/presentation/game/WestGoldGameFragment;)V
    .param p1    # Lorg/xbet/west_gold/presentation/game/WestGoldGameFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public abstract c(Lorg/xbet/west_gold/presentation/holder/WestGoldHolderFragment;)V
    .param p1    # Lorg/xbet/west_gold/presentation/holder/WestGoldHolderFragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method
