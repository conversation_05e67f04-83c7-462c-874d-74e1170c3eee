.class public final Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a/\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a#\u0010\u000e\u001a\u00020\u0002*\u00020\t2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "Lkotlin/Function1;",
        "LN80/c;",
        "",
        "onItemClickListener",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lkotlin/jvm/functions/Function1;)LA4/c;",
        "Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;",
        "Landroid/content/Context;",
        "context",
        "LN80/c$a;",
        "item",
        "j",
        "(Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;Landroid/content/Context;LN80/c$a;)V",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;->h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;->g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/J;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/J;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lkotlin/jvm/functions/Function1;)LA4/c;
    .locals 4
    .param p0    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LN80/c;",
            "Lkotlin/Unit;",
            ">;)",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LS80/a;

    .line 2
    .line 3
    invoke-direct {v0}, LS80/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LS80/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, LS80/b;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt$getAuthenticatorCompactLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt$getAuthenticatorCompactLineItemDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt$getAuthenticatorCompactLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt$getAuthenticatorCompactLineItemDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lv80/J;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, Lv80/J;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lv80/J;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function1;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lv80/J;

    .line 6
    .line 7
    invoke-virtual {v0}, Lv80/J;->b()Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    new-instance v1, LS80/c;

    .line 12
    .line 13
    invoke-direct {v1, p0, p1}, LS80/c;-><init>(Lkotlin/jvm/functions/Function1;LB4/a;)V

    .line 14
    .line 15
    .line 16
    const/4 p0, 0x1

    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v2, v1, p0, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 19
    .line 20
    .line 21
    new-instance p0, LS80/d;

    .line 22
    .line 23
    invoke-direct {p0, p1}, LS80/d;-><init>(LB4/a;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final h(Lkotlin/jvm/functions/Function1;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lv80/J;

    .line 6
    .line 7
    iget-object p1, p1, Lv80/J;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, LN80/c$a;

    .line 14
    .line 15
    invoke-virtual {v0}, LN80/c$a;->getIcon()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setIconResource(I)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, Lv80/J;

    .line 27
    .line 28
    iget-object p1, p1, Lv80/J;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, LN80/c$a;

    .line 35
    .line 36
    invoke-virtual {v0}, LN80/c$a;->getTitle()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setTitle(Ljava/lang/String;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, Lv80/J;

    .line 48
    .line 49
    iget-object p1, p1, Lv80/J;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 50
    .line 51
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, LN80/c$a;

    .line 56
    .line 57
    invoke-virtual {v0}, LN80/c$a;->u()Z

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setBadgeVisible(Z)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, LN80/c$a;

    .line 69
    .line 70
    invoke-virtual {p1}, LN80/c$a;->o()Z

    .line 71
    .line 72
    .line 73
    move-result p1

    .line 74
    if-eqz p1, :cond_0

    .line 75
    .line 76
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    check-cast p1, Lv80/J;

    .line 81
    .line 82
    iget-object p1, p1, Lv80/J;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 83
    .line 84
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    move-result-object p0

    .line 92
    check-cast p0, LN80/c$a;

    .line 93
    .line 94
    invoke-static {p1, v0, p0}, Lorg/xbet/main_menu/impl/presentation/common/viewholders/compact/tall/AuthenticatorCompactLineItemDelegateKt;->j(Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;Landroid/content/Context;LN80/c$a;)V

    .line 95
    .line 96
    .line 97
    goto :goto_0

    .line 98
    :cond_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    check-cast p1, Lv80/J;

    .line 103
    .line 104
    iget-object p1, p1, Lv80/J;->b:Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;

    .line 105
    .line 106
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object p0

    .line 110
    check-cast p0, LN80/c$a;

    .line 111
    .line 112
    invoke-virtual {p0}, LN80/c$a;->f()Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object p0

    .line 116
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 117
    .line 118
    .line 119
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 120
    .line 121
    return-object p0
.end method

.method public static final j(Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;Landroid/content/Context;LN80/c$a;)V
    .locals 5

    .line 1
    invoke-virtual {p2}, LN80/c$a;->j()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {p1, v0}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    sget v1, LlZ0/g;->space_14:I

    .line 14
    .line 15
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    const/4 v1, 0x0

    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    invoke-virtual {v0, v1, v1, p1, p1}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 23
    .line 24
    .line 25
    :cond_0
    new-instance p1, Landroid/text/SpannableStringBuilder;

    .line 26
    .line 27
    const-string v2, " "

    .line 28
    .line 29
    invoke-direct {p1, v2}, Landroid/text/SpannableStringBuilder;-><init>(Ljava/lang/CharSequence;)V

    .line 30
    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    new-instance v3, Lorg/xbet/ui_common/utils/t;

    .line 35
    .line 36
    invoke-direct {v3, v0}, Lorg/xbet/ui_common/utils/t;-><init>(Landroid/graphics/drawable/Drawable;)V

    .line 37
    .line 38
    .line 39
    const/4 v0, 0x1

    .line 40
    const/16 v4, 0x11

    .line 41
    .line 42
    invoke-virtual {p1, v3, v1, v0, v4}, Landroid/text/SpannableStringBuilder;->setSpan(Ljava/lang/Object;III)V

    .line 43
    .line 44
    .line 45
    :cond_1
    invoke-virtual {p1, v2}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 46
    .line 47
    .line 48
    invoke-virtual {p2}, LN80/c$a;->f()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p2

    .line 52
    invoke-virtual {p1, p2}, Landroid/text/SpannableStringBuilder;->append(Ljava/lang/CharSequence;)Landroid/text/SpannableStringBuilder;

    .line 53
    .line 54
    .line 55
    invoke-virtual {p0, p1}, Lorg/xbet/uikit/components/cells/menu/MenuCompactCell;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 56
    .line 57
    .line 58
    return-void
.end method
