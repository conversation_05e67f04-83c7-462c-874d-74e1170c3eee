.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/o;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$giftsListFlow$1"
    f = "AggregatorGiftsViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;-><init>(Lorg/xplatform/aggregator/impl/gifts/presentation/delegates/GiftsDelegate;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lorg/xplatform/aggregator/impl/gifts/usecases/h;Lorg/xplatform/aggregator/impl/gifts/usecases/a;Lorg/xplatform/aggregator/impl/gifts/usecases/f;Lorg/xplatform/aggregator/impl/gifts/usecases/d;Lorg/xplatform/aggregator/impl/gifts/usecases/l;Lia1/f;LwX0/C;Lorg/xbet/ui_common/utils/M;Lf81/a;Lfk/j;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lf81/c;Lv81/g;Lorg/xbet/analytics/domain/scope/T;Lfk/o;Le81/c;Lorg/xplatform/aggregator/impl/gifts/usecases/n;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lm8/a;LP91/b;LwX0/a;LHX0/e;Lek/d;Lek/f;Lp9/c;LUR/a;Lorg/xbet/remoteconfig/domain/usecases/i;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LAR/a;LZR/a;Lfk/s;Lgk0/a;Lfk/l;LC81/f;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/o<",
        "Ljava/lang/Boolean;",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LVX0/i;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00022\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "",
        "show",
        "",
        "LVX0/i;",
        "topGames",
        "gifts",
        "<anonymous>",
        "(ZLjava/util/List;Ljava/util/List;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    const/4 p1, 0x4

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Ljava/util/List;

    check-cast p3, Ljava/util/List;

    check-cast p4, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->invoke(ZLjava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLjava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    invoke-direct {v0, v1, p4}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Lkotlin/coroutines/e;)V

    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->Z$0:Z

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->L$0:Ljava/lang/Object;

    iput-object p3, v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->Z$0:Z

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v0, Ljava/util/List;

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->L$1:Ljava/lang/Object;

    .line 18
    .line 19
    check-cast v1, Ljava/util/List;

    .line 20
    .line 21
    if-eqz p1, :cond_0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$giftsListFlow$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 29
    .line 30
    invoke-static {p1, v1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->V4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;)Z

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    if-eqz p1, :cond_1

    .line 35
    .line 36
    goto :goto_1

    .line 37
    :cond_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    :goto_1
    invoke-static {v1, v0}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    return-object p1

    .line 46
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1
.end method
