.class public Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/security/PublicKey;


# instance fields
.field private final keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

.field private final treeDigest:LSe/m;


# direct methods
.method public constructor <init>(LSe/m;Lorg/spongycastle/pqc/crypto/xmss/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 3
    iput-object p2, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    return-void
.end method

.method public constructor <init>(Lkf/z;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 4
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 5
    invoke-virtual {p1}, Lkf/z;->o()Lkf/a;

    move-result-object v0

    invoke-virtual {v0}, Lkf/a;->t()LSe/e;

    move-result-object v0

    invoke-static {v0}, LIf/k;->r(Ljava/lang/Object;)LIf/k;

    move-result-object v0

    .line 6
    invoke-virtual {v0}, LIf/k;->t()Lkf/a;

    move-result-object v1

    invoke-virtual {v1}, Lkf/a;->o()LSe/m;

    move-result-object v1

    iput-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 7
    invoke-virtual {p1}, Lkf/z;->w()LSe/q;

    move-result-object p1

    invoke-static {p1}, LIf/n;->o(Ljava/lang/Object;)LIf/n;

    move-result-object p1

    .line 8
    new-instance v2, Lorg/spongycastle/pqc/crypto/xmss/n$b;

    new-instance v3, Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 9
    invoke-virtual {v0}, LIf/k;->o()I

    move-result v4

    invoke-virtual {v0}, LIf/k;->s()I

    move-result v0

    invoke-static {v1}, LUf/a;->a(LSe/m;)Lorg/spongycastle/crypto/e;

    move-result-object v1

    invoke-direct {v3, v4, v0, v1}, Lorg/spongycastle/pqc/crypto/xmss/l;-><init>(IILorg/spongycastle/crypto/e;)V

    invoke-direct {v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/n$b;-><init>(Lorg/spongycastle/pqc/crypto/xmss/l;)V

    .line 10
    invoke-virtual {p1}, LIf/n;->r()[B

    move-result-object v0

    invoke-virtual {v2, v0}, Lorg/spongycastle/pqc/crypto/xmss/n$b;->f([B)Lorg/spongycastle/pqc/crypto/xmss/n$b;

    move-result-object v0

    .line 11
    invoke-virtual {p1}, LIf/n;->s()[B

    move-result-object p1

    invoke-virtual {v0, p1}, Lorg/spongycastle/pqc/crypto/xmss/n$b;->g([B)Lorg/spongycastle/pqc/crypto/xmss/n$b;

    move-result-object p1

    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/n$b;->e()Lorg/spongycastle/pqc/crypto/xmss/n;

    move-result-object p1

    iput-object p1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p1, p0, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    check-cast p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;

    .line 11
    .line 12
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 13
    .line 14
    iget-object v3, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 15
    .line 16
    invoke-virtual {v1, v3}, LSe/q;->equals(Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-eqz v1, :cond_1

    .line 21
    .line 22
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 23
    .line 24
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/n;->e()[B

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    iget-object p1, p1, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 29
    .line 30
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/n;->e()[B

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    invoke-static {v1, p1}, Lorg/spongycastle/util/a;->a([B[B)Z

    .line 35
    .line 36
    .line 37
    move-result p1

    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    return v0

    .line 41
    :cond_1
    return v2
.end method

.method public final getAlgorithm()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "XMSSMT"

    .line 2
    .line 3
    return-object v0
.end method

.method public getEncoded()[B
    .locals 7

    .line 1
    :try_start_0
    new-instance v0, Lkf/a;

    .line 2
    .line 3
    sget-object v1, LIf/e;->B:LSe/m;

    .line 4
    .line 5
    new-instance v2, LIf/k;

    .line 6
    .line 7
    iget-object v3, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 8
    .line 9
    invoke-virtual {v3}, Lorg/spongycastle/pqc/crypto/xmss/n;->b()Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual {v3}, Lorg/spongycastle/pqc/crypto/xmss/l;->c()I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    iget-object v4, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 18
    .line 19
    invoke-virtual {v4}, Lorg/spongycastle/pqc/crypto/xmss/n;->b()Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-virtual {v4}, Lorg/spongycastle/pqc/crypto/xmss/l;->d()I

    .line 24
    .line 25
    .line 26
    move-result v4

    .line 27
    new-instance v5, Lkf/a;

    .line 28
    .line 29
    iget-object v6, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 30
    .line 31
    invoke-direct {v5, v6}, Lkf/a;-><init>(LSe/m;)V

    .line 32
    .line 33
    .line 34
    invoke-direct {v2, v3, v4, v5}, LIf/k;-><init>(IILkf/a;)V

    .line 35
    .line 36
    .line 37
    invoke-direct {v0, v1, v2}, Lkf/a;-><init>(LSe/m;LSe/e;)V

    .line 38
    .line 39
    .line 40
    new-instance v1, Lkf/z;

    .line 41
    .line 42
    new-instance v2, LIf/n;

    .line 43
    .line 44
    iget-object v3, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 45
    .line 46
    invoke-virtual {v3}, Lorg/spongycastle/pqc/crypto/xmss/n;->c()[B

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    iget-object v4, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 51
    .line 52
    invoke-virtual {v4}, Lorg/spongycastle/pqc/crypto/xmss/n;->d()[B

    .line 53
    .line 54
    .line 55
    move-result-object v4

    .line 56
    invoke-direct {v2, v3, v4}, LIf/n;-><init>([B[B)V

    .line 57
    .line 58
    .line 59
    invoke-direct {v1, v0, v2}, Lkf/z;-><init>(Lkf/a;LSe/e;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v1}, LSe/l;->i()[B

    .line 63
    .line 64
    .line 65
    move-result-object v0
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    .line 66
    return-object v0

    .line 67
    :catch_0
    const/4 v0, 0x0

    .line 68
    return-object v0
.end method

.method public getFormat()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "X.509"

    .line 2
    .line 3
    return-object v0
.end method

.method public getHeight()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/n;->b()Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/l;->c()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public getKeyParams()Lorg/spongycastle/crypto/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 2
    .line 3
    return-object v0
.end method

.method public getLayers()I
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/n;->b()Lorg/spongycastle/pqc/crypto/xmss/l;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/l;->d()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    return v0
.end method

.method public getTreeDigest()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    invoke-static {v0}, LUf/a;->b(LSe/m;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public hashCode()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->treeDigest:LSe/m;

    .line 2
    .line 3
    invoke-virtual {v0}, LSe/m;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/spongycastle/pqc/jcajce/provider/xmss/BCXMSSMTPublicKey;->keyParams:Lorg/spongycastle/pqc/crypto/xmss/n;

    .line 8
    .line 9
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/n;->e()[B

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-static {v1}, Lorg/spongycastle/util/a;->p([B)I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    mul-int/lit8 v1, v1, 0x25

    .line 18
    .line 19
    add-int/2addr v0, v1

    .line 20
    return v0
.end method
