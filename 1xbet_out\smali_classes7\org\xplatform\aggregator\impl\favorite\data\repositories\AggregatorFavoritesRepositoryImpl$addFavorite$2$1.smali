.class final Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.favorite.data.repositories.AggregatorFavoritesRepositoryImpl$addFavorite$2$1"
    f = "AggregatorFavoritesRepositoryImpl.kt"
    l = {
        0x141,
        0x1fa
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $subcategoryId:I

.field I$0:I

.field I$1:I

.field I$2:I

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;",
            "Z",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iput-boolean p2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$brandsApi:Z

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput p4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$subcategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$brandsApi:Z

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget v4, p0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$subcategoryId:I

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;-><init>(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;ZLorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 16

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    iget v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v0, :cond_3

    .line 13
    .line 14
    if-eq v0, v5, :cond_2

    .line 15
    .line 16
    if-ne v0, v3, :cond_1

    .line 17
    .line 18
    iget v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$2:I

    .line 19
    .line 20
    iget v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$1:I

    .line 21
    .line 22
    iget-boolean v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->Z$0:Z

    .line 23
    .line 24
    iget v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$0:I

    .line 25
    .line 26
    iget-object v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$2:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v9, Lorg/xplatform/aggregator/api/model/Game;

    .line 29
    .line 30
    iget-object v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v10, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 33
    .line 34
    iget-object v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast v11, Lkotlinx/coroutines/N;

    .line 37
    .line 38
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    move v14, v6

    .line 42
    move v6, v0

    .line 43
    :cond_0
    move-object v12, v11

    .line 44
    move v11, v7

    .line 45
    move-object v7, v12

    .line 46
    move-object v13, v9

    .line 47
    move-object v12, v10

    .line 48
    move v9, v8

    .line 49
    goto :goto_0

    .line 50
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw v0

    .line 58
    :cond_2
    iget v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$2:I

    .line 59
    .line 60
    iget v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$1:I

    .line 61
    .line 62
    iget-boolean v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->Z$0:Z

    .line 63
    .line 64
    iget v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$0:I

    .line 65
    .line 66
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$2:Ljava/lang/Object;

    .line 67
    .line 68
    move-object v10, v0

    .line 69
    check-cast v10, Lorg/xplatform/aggregator/api/model/Game;

    .line 70
    .line 71
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 72
    .line 73
    move-object v11, v0

    .line 74
    check-cast v11, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 75
    .line 76
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 77
    .line 78
    move-object v12, v0

    .line 79
    check-cast v12, Lkotlinx/coroutines/N;

    .line 80
    .line 81
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 82
    .line 83
    .line 84
    goto :goto_1

    .line 85
    :catchall_0
    move-exception v0

    .line 86
    move v14, v7

    .line 87
    move v7, v8

    .line 88
    move v8, v9

    .line 89
    move-object v9, v10

    .line 90
    move-object v10, v11

    .line 91
    move-object v11, v12

    .line 92
    goto :goto_2

    .line 93
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 94
    .line 95
    .line 96
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 97
    .line 98
    check-cast v0, Lkotlinx/coroutines/N;

    .line 99
    .line 100
    iget-object v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 101
    .line 102
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->s(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)LT91/a;

    .line 103
    .line 104
    .line 105
    move-result-object v6

    .line 106
    invoke-virtual {v6, v5}, LT91/a;->o(Z)V

    .line 107
    .line 108
    .line 109
    iget-object v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 110
    .line 111
    iget-boolean v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$brandsApi:Z

    .line 112
    .line 113
    iget-object v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 114
    .line 115
    iget v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$subcategoryId:I

    .line 116
    .line 117
    move-object v12, v6

    .line 118
    move v11, v7

    .line 119
    move-object v13, v8

    .line 120
    move v14, v9

    .line 121
    const/4 v6, 0x0

    .line 122
    const/4 v9, 0x0

    .line 123
    move-object v7, v0

    .line 124
    :goto_0
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 125
    .line 126
    invoke-static {v12}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->v(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    new-instance v10, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1$1$1;

    .line 131
    .line 132
    const/4 v15, 0x0

    .line 133
    invoke-direct/range {v10 .. v15}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1$1$1;-><init>(ZLorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    .line 134
    .line 135
    .line 136
    iput-object v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 137
    .line 138
    iput-object v12, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 139
    .line 140
    iput-object v13, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$2:Ljava/lang/Object;

    .line 141
    .line 142
    iput v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$0:I

    .line 143
    .line 144
    iput-boolean v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->Z$0:Z

    .line 145
    .line 146
    iput v14, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$1:I

    .line 147
    .line 148
    iput v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$2:I

    .line 149
    .line 150
    iput v5, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->label:I

    .line 151
    .line 152
    invoke-virtual {v0, v10, v1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 153
    .line 154
    .line 155
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 156
    if-ne v0, v2, :cond_4

    .line 157
    .line 158
    goto/16 :goto_6

    .line 159
    .line 160
    :cond_4
    move v8, v11

    .line 161
    move-object v11, v12

    .line 162
    move-object v10, v13

    .line 163
    move-object v12, v7

    .line 164
    move v7, v14

    .line 165
    :goto_1
    :try_start_2
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 166
    .line 167
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 168
    .line 169
    .line 170
    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 171
    goto/16 :goto_8

    .line 172
    .line 173
    :catchall_1
    move-exception v0

    .line 174
    move v8, v11

    .line 175
    move-object v11, v7

    .line 176
    move v7, v8

    .line 177
    move v8, v9

    .line 178
    move-object v10, v12

    .line 179
    move-object v9, v13

    .line 180
    :goto_2
    if-eqz v8, :cond_5

    .line 181
    .line 182
    instance-of v12, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 183
    .line 184
    if-eqz v12, :cond_5

    .line 185
    .line 186
    move-object v12, v0

    .line 187
    check-cast v12, Lcom/xbet/onexcore/data/model/ServerException;

    .line 188
    .line 189
    invoke-virtual {v12}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 190
    .line 191
    .line 192
    move-result v12

    .line 193
    if-eqz v12, :cond_5

    .line 194
    .line 195
    const/4 v12, 0x1

    .line 196
    goto :goto_3

    .line 197
    :cond_5
    const/4 v12, 0x0

    .line 198
    :goto_3
    instance-of v13, v0, Ljava/util/concurrent/CancellationException;

    .line 199
    .line 200
    if-nez v13, :cond_e

    .line 201
    .line 202
    instance-of v13, v0, Ljava/net/ConnectException;

    .line 203
    .line 204
    if-nez v13, :cond_e

    .line 205
    .line 206
    if-nez v12, :cond_e

    .line 207
    .line 208
    instance-of v12, v0, Lcom/xbet/onexcore/data/model/ServerException;

    .line 209
    .line 210
    if-eqz v12, :cond_8

    .line 211
    .line 212
    move-object v12, v0

    .line 213
    check-cast v12, Lcom/xbet/onexcore/data/model/ServerException;

    .line 214
    .line 215
    invoke-virtual {v12}, Lcom/xbet/onexcore/data/model/ServerException;->isRedirectCode()Z

    .line 216
    .line 217
    .line 218
    move-result v13

    .line 219
    if-nez v13, :cond_7

    .line 220
    .line 221
    invoke-virtual {v12}, Lcom/xbet/onexcore/data/model/ServerException;->getClientError()Z

    .line 222
    .line 223
    .line 224
    move-result v12

    .line 225
    if-eqz v12, :cond_6

    .line 226
    .line 227
    goto :goto_4

    .line 228
    :cond_6
    const/4 v12, 0x0

    .line 229
    goto :goto_5

    .line 230
    :cond_7
    :goto_4
    const/4 v12, 0x1

    .line 231
    goto :goto_5

    .line 232
    :cond_8
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/b;->a(Ljava/lang/Throwable;)Z

    .line 233
    .line 234
    .line 235
    move-result v12

    .line 236
    if-nez v12, :cond_6

    .line 237
    .line 238
    goto :goto_4

    .line 239
    :goto_5
    add-int/2addr v6, v5

    .line 240
    const/4 v13, 0x3

    .line 241
    if-gt v6, v13, :cond_a

    .line 242
    .line 243
    if-eqz v12, :cond_9

    .line 244
    .line 245
    goto :goto_7

    .line 246
    :cond_9
    new-instance v12, Ljava/lang/StringBuilder;

    .line 247
    .line 248
    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    .line 249
    .line 250
    .line 251
    const-string v13, "error ("

    .line 252
    .line 253
    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 254
    .line 255
    .line 256
    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 257
    .line 258
    .line 259
    const-string v13, "): "

    .line 260
    .line 261
    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 262
    .line 263
    .line 264
    invoke-virtual {v12, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 265
    .line 266
    .line 267
    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 268
    .line 269
    .line 270
    move-result-object v0

    .line 271
    sget-object v12, Ljava/lang/System;->out:Ljava/io/PrintStream;

    .line 272
    .line 273
    invoke-virtual {v12, v0}, Ljava/io/PrintStream;->println(Ljava/lang/Object;)V

    .line 274
    .line 275
    .line 276
    iput-object v11, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$0:Ljava/lang/Object;

    .line 277
    .line 278
    iput-object v10, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$1:Ljava/lang/Object;

    .line 279
    .line 280
    iput-object v9, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->L$2:Ljava/lang/Object;

    .line 281
    .line 282
    iput v8, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$0:I

    .line 283
    .line 284
    iput-boolean v7, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->Z$0:Z

    .line 285
    .line 286
    iput v14, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$1:I

    .line 287
    .line 288
    iput v6, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->I$2:I

    .line 289
    .line 290
    iput v3, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->label:I

    .line 291
    .line 292
    const-wide/16 v12, 0xbb8

    .line 293
    .line 294
    invoke-static {v12, v13, v1}, Lkotlinx/coroutines/DelayKt;->b(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    if-ne v0, v2, :cond_0

    .line 299
    .line 300
    :goto_6
    return-object v2

    .line 301
    :cond_a
    :goto_7
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 302
    .line 303
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 304
    .line 305
    .line 306
    move-result-object v0

    .line 307
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 308
    .line 309
    .line 310
    move-result-object v0

    .line 311
    :goto_8
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->this$0:Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;

    .line 312
    .line 313
    iget-object v3, v1, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl$addFavorite$2$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 314
    .line 315
    invoke-static {v0}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 316
    .line 317
    .line 318
    move-result v4

    .line 319
    if-eqz v4, :cond_b

    .line 320
    .line 321
    move-object v4, v0

    .line 322
    check-cast v4, Lkotlin/Unit;

    .line 323
    .line 324
    invoke-static {v2, v3}, Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;->p(Lorg/xplatform/aggregator/impl/favorite/data/repositories/AggregatorFavoritesRepositoryImpl;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 325
    .line 326
    .line 327
    :cond_b
    invoke-static {v0}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 328
    .line 329
    .line 330
    move-result-object v2

    .line 331
    if-eqz v2, :cond_d

    .line 332
    .line 333
    instance-of v3, v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 334
    .line 335
    if-eqz v3, :cond_d

    .line 336
    .line 337
    check-cast v2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 338
    .line 339
    invoke-virtual {v2}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorCode()Lcom/xbet/onexcore/data/errors/IErrorCode;

    .line 340
    .line 341
    .line 342
    move-result-object v2

    .line 343
    sget-object v3, Lcom/xbet/onexcore/data/errors/ErrorsCode;->AggregatorFavoritesLimit:Lcom/xbet/onexcore/data/errors/ErrorsCode;

    .line 344
    .line 345
    if-eq v2, v3, :cond_c

    .line 346
    .line 347
    goto :goto_9

    .line 348
    :cond_c
    new-instance v0, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;

    .line 349
    .line 350
    invoke-direct {v0}, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;-><init>()V

    .line 351
    .line 352
    .line 353
    throw v0

    .line 354
    :cond_d
    :goto_9
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 355
    .line 356
    .line 357
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 358
    .line 359
    return-object v0

    .line 360
    :cond_e
    throw v0
.end method
