.class public final Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 G2\u00020\u0001:\u0001HB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003B\u0011\u0008\u0016\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0002\u0010\u0006J\u0017\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ!\u0010\u000f\u001a\u00020\t2\u0006\u0010\u000c\u001a\u00020\u00072\u0008\u0010\u000e\u001a\u0004\u0018\u00010\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0003J\u001d\u0010\u0015\u001a\u00020\t2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u0012H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u000f\u0010\u0017\u001a\u00020\tH\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0003J\u0019\u0010\u001a\u001a\u00020\t2\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0018H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001c\u001a\u00020\tH\u0014\u00a2\u0006\u0004\u0008\u001c\u0010\u0003J\u000f\u0010\u001d\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u0003J\u000f\u0010\u001e\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u0003R\"\u0010&\u001a\u00020\u001f8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008 \u0010!\u001a\u0004\u0008\"\u0010#\"\u0004\u0008$\u0010%R\"\u0010.\u001a\u00020\'8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+\"\u0004\u0008,\u0010-R\u001b\u00104\u001a\u00020/8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00080\u00101\u001a\u0004\u00082\u00103R\u001b\u0010:\u001a\u0002058BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109R+\u0010A\u001a\u00020\u00042\u0006\u0010;\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?\"\u0004\u0008@\u0010\u0006R\u001b\u0010F\u001a\u00020B8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008C\u00101\u001a\u0004\u0008D\u0010E\u00a8\u0006I"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/games_section/api/models/OneXGamesPromoType;",
        "promoScreenToOpen",
        "(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V",
        "",
        "isShown",
        "",
        "R2",
        "(Z)V",
        "show",
        "Lorg/xbet/uikit/components/lottie/a;",
        "config",
        "b3",
        "(ZLorg/xbet/uikit/components/lottie/a;)V",
        "a3",
        "",
        "Lq50/a;",
        "promoList",
        "c3",
        "(Ljava/util/List;)V",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "onResume",
        "onPause",
        "Ln50/c$b;",
        "i0",
        "Ln50/c$b;",
        "Q2",
        "()Ln50/c$b;",
        "setViewModelFactory",
        "(Ln50/c$b;)V",
        "viewModelFactory",
        "Lak/b;",
        "j0",
        "Lak/b;",
        "M2",
        "()Lak/b;",
        "setChangeBalanceFeature",
        "(Lak/b;)V",
        "changeBalanceFeature",
        "Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
        "k0",
        "Lkotlin/j;",
        "P2",
        "()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;",
        "viewModel",
        "Lm50/a;",
        "l0",
        "LRc/c;",
        "L2",
        "()Lm50/a;",
        "binding",
        "<set-?>",
        "m0",
        "LeX0/j;",
        "O2",
        "()Lorg/xbet/games_section/api/models/OneXGamesPromoType;",
        "Z2",
        "promoScreenBundle",
        "Lo50/a;",
        "n0",
        "N2",
        "()Lo50/a;",
        "promoAdapter",
        "o0",
        "a",
        "promo_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final o0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public i0:Ln50/c$b;

.field public j0:Lak/b;

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 4
    .line 5
    const-string v2, "binding"

    .line 6
    .line 7
    const-string v3, "getBinding()Lorg/xbet/games_section/feature/promo/databinding/FragmentPromoFgBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "promoScreenBundle"

    .line 20
    .line 21
    const-string v5, "getPromoScreenBundle()Lorg/xbet/games_section/api/models/OneXGamesPromoType;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    const/4 v2, 0x2

    .line 31
    new-array v2, v2, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v2, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v1, v2, v0

    .line 37
    .line 38
    sput-object v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;

    .line 41
    .line 42
    const/4 v1, 0x0

    .line 43
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->o0:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$a;

    .line 47
    .line 48
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Ll50/c;->fragment_promo_fg:I

    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 2
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/b;

    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/b;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$1;

    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 4
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance v3, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$2;

    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v1

    .line 5
    const-class v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    move-result-object v2

    new-instance v3, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$3;

    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    new-instance v4, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$4;

    const/4 v5, 0x0

    invoke-direct {v4, v5, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 6
    iput-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->k0:Lkotlin/j;

    .line 7
    sget-object v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$binding$2;->INSTANCE:Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$binding$2;

    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->l0:LRc/c;

    .line 8
    new-instance v0, LeX0/j;

    const-string v1, "OPEN_PROMO_KEY"

    invoke-direct {v0, v1}, LeX0/j;-><init>(Ljava/lang/String;)V

    iput-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->m0:LeX0/j;

    .line 9
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/c;

    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/c;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->n0:Lkotlin/j;

    return-void
.end method

.method public constructor <init>(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/api/models/OneXGamesPromoType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 10
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;-><init>()V

    .line 11
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->Z2(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    return-void
.end method

.method public static synthetic A2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->U2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->W2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic C2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->S2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static synthetic D2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->V2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lo50/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->X2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lo50/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->T2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic G2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lm50/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->R2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->a3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;ZLorg/xbet/uikit/components/lottie/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b3(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->c3(Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final R2(Z)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm50/a;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/16 v1, 0x8

    .line 12
    .line 13
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 14
    .line 15
    .line 16
    if-eqz p1, :cond_1

    .line 17
    .line 18
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/d;

    .line 23
    .line 24
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/d;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 25
    .line 26
    .line 27
    const-string v1, "SELECT_BALANCE_REQUEST_KEY"

    .line 28
    .line 29
    invoke-virtual {p1, v1, p0, v0}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iget-object p1, p1, Lm50/a;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 37
    .line 38
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/e;

    .line 39
    .line 40
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/e;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 41
    .line 42
    .line 43
    const/4 v1, 0x0

    .line 44
    const/4 v2, 0x1

    .line 45
    invoke-static {p1, v1, v0, v2, v1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setUpdateClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/f;

    .line 49
    .line 50
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/f;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 51
    .line 52
    .line 53
    invoke-static {p1, v1, v0, v2, v1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setAccountClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/g;

    .line 57
    .line 58
    invoke-direct {v0, p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/g;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)V

    .line 59
    .line 60
    .line 61
    invoke-static {p1, v1, v0, v2, v1}, Lorg/xbet/uikit/components/accountselection/AccountSelection;->setTopUpAccountClickListener$default(Lorg/xbet/uikit/components/accountselection/AccountSelection;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    :cond_1
    return-void
.end method

.method public static final S2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 3

    .line 1
    const-string v0, "SELECT_BALANCE_REQUEST_KEY"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    const-string p1, "RESULT_ON_ITEM_SELECTED_LISTENER_KEY"

    .line 11
    .line 12
    invoke-virtual {p2, p1}, Landroid/os/BaseBundle;->containsKey(Ljava/lang/String;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_5

    .line 17
    .line 18
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 19
    .line 20
    const/16 v1, 0x21

    .line 21
    .line 22
    const/4 v2, 0x0

    .line 23
    if-lt v0, v1, :cond_1

    .line 24
    .line 25
    const-class v0, Lorg/xbet/balance/model/BalanceModel;

    .line 26
    .line 27
    invoke-static {p2, p1, v0}, Lcom/xbet/security/impl/presentation/phone/confirm/check/a;->a(Landroid/os/Bundle;Ljava/lang/String;Ljava/lang/Class;)Ljava/io/Serializable;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    goto :goto_0

    .line 32
    :cond_1
    invoke-virtual {p2, p1}, Landroid/os/Bundle;->getSerializable(Ljava/lang/String;)Ljava/io/Serializable;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    instance-of p2, p1, Lorg/xbet/balance/model/BalanceModel;

    .line 37
    .line 38
    if-nez p2, :cond_2

    .line 39
    .line 40
    move-object p1, v2

    .line 41
    :cond_2
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 42
    .line 43
    :goto_0
    instance-of p2, p1, Lorg/xbet/balance/model/BalanceModel;

    .line 44
    .line 45
    if-eqz p2, :cond_3

    .line 46
    .line 47
    move-object v2, p1

    .line 48
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 49
    .line 50
    :cond_3
    if-nez v2, :cond_4

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    invoke-virtual {p0, v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->r4(Lorg/xbet/balance/model/BalanceModel;)V

    .line 58
    .line 59
    .line 60
    :cond_5
    :goto_1
    return-void
.end method

.method public static final T2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->w4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final U2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->X3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final V2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lorg/xbet/uikit/components/accountselection/AccountSelection;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->o4(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final W2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->l4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static final X2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Lo50/a;
    .locals 2

    .line 1
    new-instance v0, Lo50/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/games_section/feature/promo/presentation/h;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/promo/presentation/h;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, Lo50/a;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final Y2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lq50/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->p4(Ljava/lang/String;Lq50/a;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private final a3()V
    .locals 14

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->M2()Lak/b;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->GAMES:Lorg/xbet/balance/model/BalanceScreenType;

    .line 10
    .line 11
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 12
    .line 13
    .line 14
    move-result-object v6

    .line 15
    const/16 v12, 0x2ee

    .line 16
    .line 17
    const/4 v13, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    const-string v10, "SELECT_BALANCE_REQUEST_KEY"

    .line 25
    .line 26
    const/4 v11, 0x0

    .line 27
    invoke-static/range {v1 .. v13}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static final d3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->Q2()Ln50/c$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->d3(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lq50/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->Y2(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lq50/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final L2()Lm50/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lm50/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final M2()Lak/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->j0:Lak/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final N2()Lo50/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lo50/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final O2()Lorg/xbet/games_section/api/models/OneXGamesPromoType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->m0:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/j;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/io/Serializable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 13
    .line 14
    return-object v0
.end method

.method public final P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final Q2()Ln50/c$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->i0:Ln50/c$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Z2(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->m0:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/j;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/io/Serializable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final b3(ZLorg/xbet/uikit/components/lottie/a;)V
    .locals 1

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, Lm50/a;->e:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 8
    .line 9
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    iget-object p2, p2, Lm50/a;->e:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 17
    .line 18
    if-eqz p1, :cond_1

    .line 19
    .line 20
    const/4 p1, 0x0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    const/16 p1, 0x8

    .line 23
    .line 24
    :goto_0
    invoke-virtual {p2, p1}, Landroid/view/View;->setVisibility(I)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final c3(Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lq50/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x0

    .line 3
    invoke-virtual {p0, v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->b3(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->N2()Lo50/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-virtual {v0, p1}, LUX0/h;->B(Ljava/util/List;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->m4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->n4()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 5

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->k4()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget-object p1, p1, Lm50/a;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 16
    .line 17
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 18
    .line 19
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-direct {v0, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->N2()Lo50/a;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 34
    .line 35
    .line 36
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/m;

    .line 37
    .line 38
    sget v1, Lpb/f;->space_8:I

    .line 39
    .line 40
    const/4 v2, 0x2

    .line 41
    const/4 v3, 0x0

    .line 42
    const/4 v4, 0x0

    .line 43
    invoke-direct {v0, v1, v4, v2, v3}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/m;-><init>(IZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->L2()Lm50/a;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iget-object p1, p1, Lm50/a;->h:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 54
    .line 55
    new-instance v0, Lorg/xbet/games_section/feature/promo/presentation/a;

    .line 56
    .line 57
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/promo/presentation/a;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-static {}, Ln50/a;->a()Ln50/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    instance-of v2, v1, LQW0/f;

    .line 14
    .line 15
    const-string v3, "Can not find dependencies provider for "

    .line 16
    .line 17
    if-eqz v2, :cond_2

    .line 18
    .line 19
    check-cast v1, LQW0/f;

    .line 20
    .line 21
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    instance-of v2, v2, LSv/g;

    .line 26
    .line 27
    if-eqz v2, :cond_1

    .line 28
    .line 29
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    if-eqz v1, :cond_0

    .line 34
    .line 35
    check-cast v1, LSv/g;

    .line 36
    .line 37
    new-instance v2, Ln50/e;

    .line 38
    .line 39
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->O2()Lorg/xbet/games_section/api/models/OneXGamesPromoType;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-direct {v2, v3}, Ln50/e;-><init>(Lorg/xbet/games_section/api/models/OneXGamesPromoType;)V

    .line 44
    .line 45
    .line 46
    invoke-interface {v0, v1, v2}, Ln50/c$a;->a(LSv/g;Ln50/e;)Ln50/c;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-interface {v0, p0}, Ln50/c;->a(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;)V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_0
    new-instance v0, Ljava/lang/NullPointerException;

    .line 55
    .line 56
    const-string v1, "null cannot be cast to non-null type org.xbet.core.di.dependencies.OneXGamesPromoDependencies"

    .line 57
    .line 58
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    throw v0

    .line 62
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 63
    .line 64
    new-instance v1, Ljava/lang/StringBuilder;

    .line 65
    .line 66
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 67
    .line 68
    .line 69
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 70
    .line 71
    .line 72
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 73
    .line 74
    .line 75
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    throw v0

    .line 83
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 84
    .line 85
    new-instance v1, Ljava/lang/StringBuilder;

    .line 86
    .line 87
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 101
    .line 102
    .line 103
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->V3()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->d4()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$2;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->e4()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$3;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;->P2()Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoViewModel;->f4()Lkotlinx/coroutines/flow/e;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    new-instance v11, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$4;

    .line 120
    .line 121
    invoke-direct {v11, v0, v1}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$4;-><init>(Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment;Lkotlin/coroutines/e;)V

    .line 122
    .line 123
    .line 124
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 125
    .line 126
    .line 127
    move-result-object v9

    .line 128
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    new-instance v4, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 133
    .line 134
    move-object v7, v4

    .line 135
    invoke-direct/range {v7 .. v12}, Lorg/xbet/games_section/feature/promo/presentation/OneXGamesPromoFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 136
    .line 137
    .line 138
    const/4 v5, 0x3

    .line 139
    const/4 v6, 0x0

    .line 140
    const/4 v2, 0x0

    .line 141
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 142
    .line 143
    .line 144
    return-void
.end method
