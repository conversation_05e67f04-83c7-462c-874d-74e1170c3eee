.class public final LGB0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0016\u0008\u0001\u0018\u00002\u00020\u0001BY\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J%\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001f\u0010 R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010!R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103\u00a8\u00064"
    }
    d2 = {
        "LGB0/e;",
        "LQW0/a;",
        "Lm8/a;",
        "coroutineDispatchers",
        "LQn/a;",
        "eventGroupRepository",
        "LQn/b;",
        "eventRepository",
        "Ljo/a;",
        "marketParser",
        "Lk8/c;",
        "coefViewPrefsRepositoryProvider",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lc8/a;",
        "applicationSettingsDataSource",
        "LEP/b;",
        "betEventRepository",
        "LKB0/a;",
        "trackCoefRepositoryProvider",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lm8/a;LQn/a;LQn/b;Ljo/a;Lk8/c;Lc8/h;Lc8/a;LEP/b;LKB0/a;Lf8/g;)V",
        "Lorg/xbet/sportgame/core/data/datasource/local/l;",
        "scoreLocalDataSource",
        "Lorg/xbet/sportgame/core/data/datasource/local/j;",
        "marketsLocalDataSource",
        "Lorg/xbet/sportgame/core/data/datasource/local/r;",
        "totalExactLocalDataSource",
        "LGB0/d;",
        "a",
        "(Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;Lorg/xbet/sportgame/core/data/datasource/local/r;)LGB0/d;",
        "Lm8/a;",
        "b",
        "LQn/a;",
        "c",
        "LQn/b;",
        "d",
        "Ljo/a;",
        "e",
        "Lk8/c;",
        "f",
        "Lc8/h;",
        "g",
        "Lc8/a;",
        "h",
        "LEP/b;",
        "i",
        "LKB0/a;",
        "j",
        "Lf8/g;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LQn/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LQn/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljo/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lk8/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lc8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LEP/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:LKB0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lm8/a;LQn/a;LQn/b;Ljo/a;Lk8/c;Lc8/h;Lc8/a;LEP/b;LKB0/a;Lf8/g;)V
    .locals 0
    .param p1    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LQn/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LQn/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljo/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lk8/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lc8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LEP/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LKB0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LGB0/e;->a:Lm8/a;

    .line 5
    .line 6
    iput-object p2, p0, LGB0/e;->b:LQn/a;

    .line 7
    .line 8
    iput-object p3, p0, LGB0/e;->c:LQn/b;

    .line 9
    .line 10
    iput-object p4, p0, LGB0/e;->d:Ljo/a;

    .line 11
    .line 12
    iput-object p5, p0, LGB0/e;->e:Lk8/c;

    .line 13
    .line 14
    iput-object p6, p0, LGB0/e;->f:Lc8/h;

    .line 15
    .line 16
    iput-object p7, p0, LGB0/e;->g:Lc8/a;

    .line 17
    .line 18
    iput-object p8, p0, LGB0/e;->h:LEP/b;

    .line 19
    .line 20
    iput-object p9, p0, LGB0/e;->i:LKB0/a;

    .line 21
    .line 22
    iput-object p10, p0, LGB0/e;->j:Lf8/g;

    .line 23
    .line 24
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;Lorg/xbet/sportgame/core/data/datasource/local/r;)LGB0/d;
    .locals 14
    .param p1    # Lorg/xbet/sportgame/core/data/datasource/local/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/sportgame/core/data/datasource/local/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/sportgame/core/data/datasource/local/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LGB0/a;->a()LGB0/d$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LGB0/e;->a:Lm8/a;

    .line 6
    .line 7
    iget-object v2, p0, LGB0/e;->e:Lk8/c;

    .line 8
    .line 9
    iget-object v3, p0, LGB0/e;->b:LQn/a;

    .line 10
    .line 11
    iget-object v4, p0, LGB0/e;->c:LQn/b;

    .line 12
    .line 13
    iget-object v5, p0, LGB0/e;->d:Ljo/a;

    .line 14
    .line 15
    iget-object v6, p0, LGB0/e;->g:Lc8/a;

    .line 16
    .line 17
    iget-object v7, p0, LGB0/e;->f:Lc8/h;

    .line 18
    .line 19
    iget-object v8, p0, LGB0/e;->j:Lf8/g;

    .line 20
    .line 21
    iget-object v11, p0, LGB0/e;->h:LEP/b;

    .line 22
    .line 23
    iget-object v12, p0, LGB0/e;->i:LKB0/a;

    .line 24
    .line 25
    move-object v9, p1

    .line 26
    move-object/from16 v10, p2

    .line 27
    .line 28
    move-object/from16 v13, p3

    .line 29
    .line 30
    invoke-interface/range {v0 .. v13}, LGB0/d$a;->a(Lm8/a;Lk8/c;LQn/a;LQn/b;Ljo/a;Lc8/a;Lc8/h;Lf8/g;Lorg/xbet/sportgame/core/data/datasource/local/l;Lorg/xbet/sportgame/core/data/datasource/local/j;LEP/b;LKB0/a;Lorg/xbet/sportgame/core/data/datasource/local/r;)LGB0/d;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    return-object p1
.end method
