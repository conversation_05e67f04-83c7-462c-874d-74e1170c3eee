.class public final Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0019\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "f",
        "()LA4/c;",
        "impl_aggregator_implRelease"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/y0;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;->g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/y0;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;->j(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;->h(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt;->k(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LBa1/g;

    .line 2
    .line 3
    invoke-direct {v0}, LBa1/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LBa1/h;

    .line 7
    .line 8
    invoke-direct {v1}, LBa1/h;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt$promoShimmerBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt$promoShimmerBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt$promoShimmerBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/impl/promo/presentation/adapters/delegates/banners/PromoShimmerBannerContainerViewHolderKt$promoShimmerBannerContainerViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final g(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LS91/y0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LS91/y0;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LS91/y0;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final h(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, LBa1/i;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LBa1/i;-><init>(LB4/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 7
    .line 8
    .line 9
    new-instance v0, LBa1/j;

    .line 10
    .line 11
    invoke-direct {v0, p0}, LBa1/j;-><init>(LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    new-instance v0, LBa1/k;

    .line 18
    .line 19
    invoke-direct {v0, p0}, LBa1/k;-><init>(LB4/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0, v0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LS91/y0;

    .line 6
    .line 7
    iget-object p1, p1, LS91/y0;->b:Lorg/xbet/uikit/components/bannercollection/BannerCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, Lza1/a$a$c;

    .line 14
    .line 15
    invoke-virtual {p0}, Lza1/a$a$c;->d()Lorg/xbet/uikit/components/bannercollection/a;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p1, p0}, Lorg/xbet/uikit/components/bannercollection/BannerCollection;->setItems(Lorg/xbet/uikit/components/bannercollection/a;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method public static final j(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LS91/y0;

    .line 6
    .line 7
    invoke-virtual {p0}, LS91/y0;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final k(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LS91/y0;

    .line 6
    .line 7
    invoke-virtual {p0}, LS91/y0;->b()Landroid/widget/LinearLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
