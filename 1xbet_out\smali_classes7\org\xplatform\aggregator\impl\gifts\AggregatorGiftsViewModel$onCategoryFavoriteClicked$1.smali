.class final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.gifts.AggregatorGiftsViewModel$onCategoryFavoriteClicked$1"
    f = "AggregatorGiftsViewModel.kt"
    l = {
        0x31b,
        0x321,
        0x32e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->D5(JJZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $categoryId:J

.field final synthetic $favorite:Z

.field final synthetic $gameId:J

.field J$0:J

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JZJLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;",
            "JZJ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$gameId:J

    iput-boolean p4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$favorite:Z

    iput-wide p5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$categoryId:J

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$gameId:J

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$favorite:Z

    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$categoryId:J

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;-><init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;JZJLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v5, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v6

    .line 7
    iget v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->label:I

    .line 8
    .line 9
    const/4 v7, 0x3

    .line 10
    const/4 v1, 0x2

    .line 11
    const/4 v8, 0x1

    .line 12
    if-eqz v0, :cond_3

    .line 13
    .line 14
    if-eq v0, v8, :cond_2

    .line 15
    .line 16
    if-eq v0, v1, :cond_1

    .line 17
    .line 18
    if-ne v0, v7, :cond_0

    .line 19
    .line 20
    iget-object v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 21
    .line 22
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 23
    .line 24
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    move-object/from16 v1, p1

    .line 28
    .line 29
    goto/16 :goto_3

    .line 30
    .line 31
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 32
    .line 33
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 34
    .line 35
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    throw v0

    .line 39
    :cond_1
    iget-wide v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->J$0:J

    .line 40
    .line 41
    iget-object v2, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$2:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast v2, Lorg/xplatform/aggregator/api/model/Game;

    .line 44
    .line 45
    iget-object v3, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 46
    .line 47
    check-cast v3, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 48
    .line 49
    iget-object v4, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$0:Ljava/lang/Object;

    .line 50
    .line 51
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    move-wide v15, v0

    .line 55
    goto/16 :goto_0

    .line 56
    .line 57
    :cond_2
    iget-object v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 60
    .line 61
    iget-object v1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 64
    .line 65
    .line 66
    goto/16 :goto_1

    .line 67
    .line 68
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    iget-object v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 72
    .line 73
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->z4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lv81/g;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iget-wide v2, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$gameId:J

    .line 78
    .line 79
    invoke-interface {v0, v2, v3}, Lv81/g;->a(J)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object v9

    .line 83
    iget-boolean v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$favorite:Z

    .line 84
    .line 85
    iget-object v10, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->this$0:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;

    .line 86
    .line 87
    iget-wide v2, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->$categoryId:J

    .line 88
    .line 89
    invoke-static {v9}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 90
    .line 91
    .line 92
    move-result v4

    .line 93
    if-eqz v4, :cond_9

    .line 94
    .line 95
    move-object v4, v9

    .line 96
    check-cast v4, Lorg/xplatform/aggregator/api/model/Game;

    .line 97
    .line 98
    if-eqz v0, :cond_5

    .line 99
    .line 100
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->L4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/d;

    .line 101
    .line 102
    .line 103
    move-result-object v0

    .line 104
    invoke-virtual {v4}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 105
    .line 106
    .line 107
    move-result-wide v1

    .line 108
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek0/a;

    .line 109
    .line 110
    .line 111
    move-result-object v3

    .line 112
    invoke-virtual {v3}, Lek0/a;->c()Z

    .line 113
    .line 114
    .line 115
    move-result v3

    .line 116
    iput-object v9, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$0:Ljava/lang/Object;

    .line 117
    .line 118
    iput-object v10, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 119
    .line 120
    iput v8, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->label:I

    .line 121
    .line 122
    const/4 v4, 0x0

    .line 123
    invoke-interface/range {v0 .. v5}, Lf81/d;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    if-ne v0, v6, :cond_4

    .line 128
    .line 129
    goto :goto_2

    .line 130
    :cond_4
    move-object v1, v9

    .line 131
    move-object v0, v10

    .line 132
    goto :goto_1

    .line 133
    :cond_5
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->r4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lf81/a;

    .line 134
    .line 135
    .line 136
    move-result-object v0

    .line 137
    invoke-static {v10}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->s4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lek0/a;

    .line 138
    .line 139
    .line 140
    move-result-object v11

    .line 141
    invoke-virtual {v11}, Lek0/a;->c()Z

    .line 142
    .line 143
    .line 144
    move-result v11

    .line 145
    iput-object v9, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$0:Ljava/lang/Object;

    .line 146
    .line 147
    iput-object v10, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 148
    .line 149
    iput-object v4, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$2:Ljava/lang/Object;

    .line 150
    .line 151
    iput-wide v2, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->J$0:J

    .line 152
    .line 153
    iput v1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->label:I

    .line 154
    .line 155
    const/4 v1, 0x0

    .line 156
    invoke-interface {v0, v4, v11, v1, v5}, Lf81/a;->a(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    if-ne v0, v6, :cond_6

    .line 161
    .line 162
    goto :goto_2

    .line 163
    :cond_6
    move-wide v15, v2

    .line 164
    move-object v2, v4

    .line 165
    move-object v4, v9

    .line 166
    move-object v3, v10

    .line 167
    :goto_0
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->J4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)LUR/a;

    .line 168
    .line 169
    .line 170
    move-result-object v11

    .line 171
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 172
    .line 173
    .line 174
    move-result-wide v13

    .line 175
    move-wide/from16 v16, v15

    .line 176
    .line 177
    const-string v15, "add_favor"

    .line 178
    .line 179
    const-string v12, "CasinoGiftsFragment"

    .line 180
    .line 181
    invoke-interface/range {v11 .. v17}, LUR/a;->r(Ljava/lang/String;JLjava/lang/String;J)V

    .line 182
    .line 183
    .line 184
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->E4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Lorg/xbet/analytics/domain/scope/T;

    .line 185
    .line 186
    .line 187
    move-result-object v11

    .line 188
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 189
    .line 190
    .line 191
    move-result-wide v12

    .line 192
    const-string v14, "add_favor"

    .line 193
    .line 194
    move-wide/from16 v15, v16

    .line 195
    .line 196
    invoke-virtual/range {v11 .. v16}, Lorg/xbet/analytics/domain/scope/T;->e(JLjava/lang/String;J)V

    .line 197
    .line 198
    .line 199
    move-object v0, v3

    .line 200
    move-object v1, v4

    .line 201
    :goto_1
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->B4(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;)Le81/c;

    .line 202
    .line 203
    .line 204
    move-result-object v2

    .line 205
    invoke-interface {v2}, Le81/c;->invoke()Lkotlinx/coroutines/flow/e;

    .line 206
    .line 207
    .line 208
    move-result-object v2

    .line 209
    iput-object v1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$0:Ljava/lang/Object;

    .line 210
    .line 211
    iput-object v0, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$1:Ljava/lang/Object;

    .line 212
    .line 213
    const/4 v1, 0x0

    .line 214
    iput-object v1, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->L$2:Ljava/lang/Object;

    .line 215
    .line 216
    iput v7, v5, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$onCategoryFavoriteClicked$1;->label:I

    .line 217
    .line 218
    invoke-static {v2, v5}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v1

    .line 222
    if-ne v1, v6, :cond_7

    .line 223
    .line 224
    :goto_2
    return-object v6

    .line 225
    :cond_7
    :goto_3
    check-cast v1, Ljava/lang/Iterable;

    .line 226
    .line 227
    new-instance v2, Ljava/util/ArrayList;

    .line 228
    .line 229
    const/16 v3, 0xa

    .line 230
    .line 231
    invoke-static {v1, v3}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 232
    .line 233
    .line 234
    move-result v3

    .line 235
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 236
    .line 237
    .line 238
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 239
    .line 240
    .line 241
    move-result-object v1

    .line 242
    :goto_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 243
    .line 244
    .line 245
    move-result v3

    .line 246
    if-eqz v3, :cond_8

    .line 247
    .line 248
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 249
    .line 250
    .line 251
    move-result-object v3

    .line 252
    check-cast v3, Lorg/xplatform/aggregator/api/model/Game;

    .line 253
    .line 254
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 255
    .line 256
    .line 257
    move-result-wide v3

    .line 258
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 259
    .line 260
    .line 261
    move-result-object v3

    .line 262
    invoke-interface {v2, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 263
    .line 264
    .line 265
    goto :goto_4

    .line 266
    :cond_8
    invoke-static {v0, v2, v8}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;->f5(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;Ljava/util/List;Z)V

    .line 267
    .line 268
    .line 269
    :cond_9
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 270
    .line 271
    return-object v0
.end method
