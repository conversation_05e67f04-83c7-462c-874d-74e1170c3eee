.class public final Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXa0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LG81/c;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final E:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final F:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public final G:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/j;",
            ">;"
        }
    .end annotation
.end field

.field public final H:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/q;",
            ">;"
        }
    .end annotation
.end field

.field public final I:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public final J:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LfX/b;",
            ">;"
        }
    .end annotation
.end field

.field public final K:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LGg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final L:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;"
        }
    .end annotation
.end field

.field public final M:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public final N:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final O:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LAR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final P:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LZR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final Q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/s;",
            ">;"
        }
    .end annotation
.end field

.field public final R:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final S:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/o;",
            ">;"
        }
    .end annotation
.end field

.field public final T:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final U:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public final V:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public final W:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LC81/f;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lkc1/b;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/s;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf81/a;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lf81/d;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Le81/d;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LOR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/xbet/onexcore/utils/ext/c;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LpR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Le81/c;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQ51/a;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LQ51/b;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
            ">;",
            "LBc/a<",
            "Lkc1/b;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
            ">;",
            "LBc/a<",
            "Lv81/s;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
            ">;",
            "LBc/a<",
            "Lf81/a;",
            ">;",
            "LBc/a<",
            "Lf81/d;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Le81/d;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LOR/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexcore/utils/ext/c;",
            ">;",
            "LBc/a<",
            "LpR/a;",
            ">;",
            "LBc/a<",
            "Le81/c;",
            ">;",
            "LBc/a<",
            "LQ51/a;",
            ">;",
            "LBc/a<",
            "LQ51/b;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "LXa0/i;",
            ">;",
            "LBc/a<",
            "LG81/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lv81/j;",
            ">;",
            "LBc/a<",
            "Lv81/q;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LGg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;",
            "LBc/a<",
            "Lek/d;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "LAR/a;",
            ">;",
            "LBc/a<",
            "LZR/a;",
            ">;",
            "LBc/a<",
            "Lfk/s;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lfk/o;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "Lek/f;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "LC81/f;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->D:LBc/a;

    .line 93
    .line 94
    move-object/from16 p1, p31

    .line 95
    .line 96
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->E:LBc/a;

    .line 97
    .line 98
    move-object/from16 p1, p32

    .line 99
    .line 100
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->F:LBc/a;

    .line 101
    .line 102
    move-object/from16 p1, p33

    .line 103
    .line 104
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->G:LBc/a;

    .line 105
    .line 106
    move-object/from16 p1, p34

    .line 107
    .line 108
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->H:LBc/a;

    .line 109
    .line 110
    move-object/from16 p1, p35

    .line 111
    .line 112
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->I:LBc/a;

    .line 113
    .line 114
    move-object/from16 p1, p36

    .line 115
    .line 116
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->J:LBc/a;

    .line 117
    .line 118
    move-object/from16 p1, p37

    .line 119
    .line 120
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->K:LBc/a;

    .line 121
    .line 122
    move-object/from16 p1, p38

    .line 123
    .line 124
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->L:LBc/a;

    .line 125
    .line 126
    move-object/from16 p1, p39

    .line 127
    .line 128
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->M:LBc/a;

    .line 129
    .line 130
    move-object/from16 p1, p40

    .line 131
    .line 132
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->N:LBc/a;

    .line 133
    .line 134
    move-object/from16 p1, p41

    .line 135
    .line 136
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->O:LBc/a;

    .line 137
    .line 138
    move-object/from16 p1, p42

    .line 139
    .line 140
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->P:LBc/a;

    .line 141
    .line 142
    move-object/from16 p1, p43

    .line 143
    .line 144
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->Q:LBc/a;

    .line 145
    .line 146
    move-object/from16 p1, p44

    .line 147
    .line 148
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->R:LBc/a;

    .line 149
    .line 150
    move-object/from16 p1, p45

    .line 151
    .line 152
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->S:LBc/a;

    .line 153
    .line 154
    move-object/from16 p1, p46

    .line 155
    .line 156
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->T:LBc/a;

    .line 157
    .line 158
    move-object/from16 p1, p47

    .line 159
    .line 160
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->U:LBc/a;

    .line 161
    .line 162
    move-object/from16 p1, p48

    .line 163
    .line 164
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->V:LBc/a;

    .line 165
    .line 166
    move-object/from16 p1, p49

    .line 167
    .line 168
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->W:LBc/a;

    .line 169
    .line 170
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;
    .locals 50
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;",
            ">;",
            "LBc/a<",
            "Lkc1/b;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;",
            ">;",
            "LBc/a<",
            "Lv81/s;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;",
            ">;",
            "LBc/a<",
            "Lf81/a;",
            ">;",
            "LBc/a<",
            "Lf81/d;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;",
            ">;",
            "LBc/a<",
            "LwX0/a;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Le81/d;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LOR/a;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexcore/utils/ext/c;",
            ">;",
            "LBc/a<",
            "LpR/a;",
            ">;",
            "LBc/a<",
            "Le81/c;",
            ">;",
            "LBc/a<",
            "LQ51/a;",
            ">;",
            "LBc/a<",
            "LQ51/b;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "LXa0/i;",
            ">;",
            "LBc/a<",
            "LG81/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
            ">;",
            "LBc/a<",
            "Lv81/j;",
            ">;",
            "LBc/a<",
            "Lv81/q;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LfX/b;",
            ">;",
            "LBc/a<",
            "LGg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/I;",
            ">;",
            "LBc/a<",
            "Lek/d;",
            ">;",
            "LBc/a<",
            "LxX0/a;",
            ">;",
            "LBc/a<",
            "LAR/a;",
            ">;",
            "LBc/a<",
            "LZR/a;",
            ">;",
            "LBc/a<",
            "Lfk/s;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lfk/o;",
            ">;",
            "LBc/a<",
            "Lgk0/a;",
            ">;",
            "LBc/a<",
            "Lek/f;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "LC81/f;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    move-object/from16 v40, p39

    .line 82
    .line 83
    move-object/from16 v41, p40

    .line 84
    .line 85
    move-object/from16 v42, p41

    .line 86
    .line 87
    move-object/from16 v43, p42

    .line 88
    .line 89
    move-object/from16 v44, p43

    .line 90
    .line 91
    move-object/from16 v45, p44

    .line 92
    .line 93
    move-object/from16 v46, p45

    .line 94
    .line 95
    move-object/from16 v47, p46

    .line 96
    .line 97
    move-object/from16 v48, p47

    .line 98
    .line 99
    move-object/from16 v49, p48

    .line 100
    .line 101
    invoke-direct/range {v0 .. v49}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 102
    .line 103
    .line 104
    return-object v0
.end method

.method public static c(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;
    .locals 50

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    move/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    move-object/from16 v31, p30

    .line 64
    .line 65
    move-object/from16 v32, p31

    .line 66
    .line 67
    move-object/from16 v33, p32

    .line 68
    .line 69
    move-object/from16 v34, p33

    .line 70
    .line 71
    move-object/from16 v35, p34

    .line 72
    .line 73
    move-object/from16 v36, p35

    .line 74
    .line 75
    move-object/from16 v37, p36

    .line 76
    .line 77
    move-object/from16 v38, p37

    .line 78
    .line 79
    move-object/from16 v39, p38

    .line 80
    .line 81
    move-object/from16 v40, p39

    .line 82
    .line 83
    move-object/from16 v41, p40

    .line 84
    .line 85
    move-object/from16 v42, p41

    .line 86
    .line 87
    move-object/from16 v43, p42

    .line 88
    .line 89
    move-object/from16 v44, p43

    .line 90
    .line 91
    move-object/from16 v45, p44

    .line 92
    .line 93
    move-object/from16 v46, p45

    .line 94
    .line 95
    move-object/from16 v47, p46

    .line 96
    .line 97
    move-object/from16 v48, p47

    .line 98
    .line 99
    move-object/from16 v49, p48

    .line 100
    .line 101
    invoke-direct/range {v0 .. v49}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;-><init>(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V

    .line 102
    .line 103
    .line 104
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;
    .locals 51

    move-object/from16 v0, p0

    .line 1
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->a:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Boolean;

    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v2

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->b:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v3, v1

    check-cast v3, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/j;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->c:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v4, v1

    check-cast v4, Lkc1/b;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->d:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v5, v1

    check-cast v5, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->e:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v6, v1

    check-cast v6, Lv81/s;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->f:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v7, v1

    check-cast v7, Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->g:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v8, v1

    check-cast v8, Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->h:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v9, v1

    check-cast v9, Lf81/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->i:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v10, v1

    check-cast v10, Lf81/d;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->j:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v11, v1

    check-cast v11, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->k:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v12, v1

    check-cast v12, Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->l:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v13, v1

    check-cast v13, LwX0/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->m:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v14, v1

    check-cast v14, LP91/b;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->n:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object v15, v1

    check-cast v15, Le81/d;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->o:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v16, v1

    check-cast v16, LwX0/C;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->p:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v17, v1

    check-cast v17, Lorg/xbet/analytics/domain/scope/g0;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->q:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v18, v1

    check-cast v18, LOR/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->r:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v19, v1

    check-cast v19, Lm8/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->s:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v20, v1

    check-cast v20, LSX0/c;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->t:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v21, v1

    check-cast v21, LHX0/e;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->u:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v22, v1

    check-cast v22, Lcom/xbet/onexcore/utils/ext/c;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->v:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v23, v1

    check-cast v23, LpR/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->w:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v24, v1

    check-cast v24, Le81/c;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->x:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v25, v1

    check-cast v25, LQ51/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->y:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v26, v1

    check-cast v26, LQ51/b;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->z:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v27, v1

    check-cast v27, Lp9/c;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->A:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v28, v1

    check-cast v28, LXa0/i;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->B:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v29, v1

    check-cast v29, LG81/c;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->C:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v30, v1

    check-cast v30, Lorg/xbet/ui_common/utils/internet/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->D:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v31, v1

    check-cast v31, Lorg/xbet/ui_common/utils/M;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->E:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v32, v1

    check-cast v32, LnR/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->F:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v33, v1

    check-cast v33, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->G:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v34, v1

    check-cast v34, Lv81/j;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->H:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v35, v1

    check-cast v35, Lv81/q;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->I:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v36, v1

    check-cast v36, Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->J:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v37, v1

    check-cast v37, LfX/b;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->K:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v38, v1

    check-cast v38, LGg/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->L:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v39, v1

    check-cast v39, Lorg/xbet/analytics/domain/scope/I;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->M:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v40, v1

    check-cast v40, Lek/d;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->N:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v41, v1

    check-cast v41, LxX0/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->O:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v42, v1

    check-cast v42, LAR/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->P:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v43, v1

    check-cast v43, LZR/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->Q:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v44, v1

    check-cast v44, Lfk/s;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->R:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v45, v1

    check-cast v45, Lorg/xbet/remoteconfig/domain/usecases/i;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->S:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v46, v1

    check-cast v46, Lfk/o;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->T:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v47, v1

    check-cast v47, Lgk0/a;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->U:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v48, v1

    check-cast v48, Lek/f;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->V:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v49, v1

    check-cast v49, Lfk/l;

    iget-object v1, v0, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->W:LBc/a;

    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    move-result-object v1

    move-object/from16 v50, v1

    check-cast v50, LC81/f;

    invoke-static/range {v2 .. v50}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->c(ZLorg/xplatform/aggregator/impl/favorite/domain/usecases/j;Lkc1/b;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/AggregatorGamesScenario;Lv81/s;Lorg/xplatform/aggregator/impl/my_aggregator/domain/scenario/SlotsGamesScenario;Lorg/xplatform/aggregator/impl/favorite/domain/usecases/GetViewedGamesScenario;Lf81/a;Lf81/d;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;Lorg/xplatform/aggregator/impl/core/presentation/AggregatorBannersDelegate;LwX0/a;LP91/b;Le81/d;LwX0/C;Lorg/xbet/analytics/domain/scope/g0;LOR/a;Lm8/a;LSX0/c;LHX0/e;Lcom/xbet/onexcore/utils/ext/c;LpR/a;Le81/c;LQ51/a;LQ51/b;Lp9/c;LXa0/i;LG81/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LnR/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lv81/j;Lv81/q;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LfX/b;LGg/a;Lorg/xbet/analytics/domain/scope/I;Lek/d;LxX0/a;LAR/a;LZR/a;Lfk/s;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    move-result-object v1

    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/k;->b()Lorg/xplatform/aggregator/impl/my_aggregator/presentation/viewmodels/MyAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
