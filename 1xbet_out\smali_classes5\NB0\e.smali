.class public final synthetic LNB0/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Landroidx/compose/runtime/r1;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNB0/e;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, LNB0/e;->b:Landroidx/compose/runtime/r1;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LNB0/e;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, LNB0/e;->b:Landroidx/compose/runtime/r1;

    invoke-static {v0, v1}, LNB0/g$a;->b(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/r1;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
