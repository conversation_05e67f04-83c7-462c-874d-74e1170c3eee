.class public final LsC0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LnC0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LsC0/a;",
        "LnC0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/sportgame/subgames/api/SubGamesParams;",
        "params",
        "Landroidx/fragment/app/Fragment;",
        "a",
        "(Lorg/xbet/sportgame/subgames/api/SubGamesParams;)Landroidx/fragment/app/Fragment;",
        "",
        "getTag",
        "()Ljava/lang/String;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/sportgame/subgames/api/SubGamesParams;)Landroidx/fragment/app/Fragment;
    .locals 1
    .param p1    # Lorg/xbet/sportgame/subgames/api/SubGamesParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    sget-object v0, Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;->b1:Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment$a;->a(Lorg/xbet/sportgame/subgames/api/SubGamesParams;)Lorg/xbet/sportgame/subgames/impl/presentation/SubGamesFragment;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public getTag()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const-string v0, "SUB_GAME_FRAGMENT_TAG"

    .line 2
    .line 3
    return-object v0
.end method
