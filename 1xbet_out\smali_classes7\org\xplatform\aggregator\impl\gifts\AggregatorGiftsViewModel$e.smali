.class public final synthetic Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = "e"
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic a:[I

.field public static final synthetic b:[I


# direct methods
.method static constructor <clinit>()V
    .locals 6

    invoke-static {}, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->values()[Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    const/4 v1, 0x1

    :try_start_0
    sget-object v2, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->DELETE:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v2}, Ljava/lang/Enum;->ordinal()I

    move-result v2

    aput v1, v0, v2
    :try_end_0
    .catch Ljava/lang/NoSuchFieldError; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    const/4 v2, 0x2

    :try_start_1
    sget-object v3, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->BONUS_ACTIVATE:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v3}, Ljava/lang/Enum;->ordinal()I

    move-result v3

    aput v2, v0, v3
    :try_end_1
    .catch Ljava/lang/NoSuchFieldError; {:try_start_1 .. :try_end_1} :catch_1

    :catch_1
    const/4 v3, 0x3

    :try_start_2
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->BONUS_RESUME:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    aput v3, v0, v4
    :try_end_2
    .catch Ljava/lang/NoSuchFieldError; {:try_start_2 .. :try_end_2} :catch_2

    :catch_2
    :try_start_3
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->ALL_GAMES_SLOTS:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/4 v5, 0x4

    aput v5, v0, v4
    :try_end_3
    .catch Ljava/lang/NoSuchFieldError; {:try_start_3 .. :try_end_3} :catch_3

    :catch_3
    :try_start_4
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->BONUS_PAUSE:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/4 v5, 0x5

    aput v5, v0, v4
    :try_end_4
    .catch Ljava/lang/NoSuchFieldError; {:try_start_4 .. :try_end_4} :catch_4

    :catch_4
    :try_start_5
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->OPEN_GAMES_BY_BONUS:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/4 v5, 0x6

    aput v5, v0, v4
    :try_end_5
    .catch Ljava/lang/NoSuchFieldError; {:try_start_5 .. :try_end_5} :catch_5

    :catch_5
    :try_start_6
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->OPEN_GAMES_BY_PRODUCT:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/4 v5, 0x7

    aput v5, v0, v4
    :try_end_6
    .catch Ljava/lang/NoSuchFieldError; {:try_start_6 .. :try_end_6} :catch_6

    :catch_6
    :try_start_7
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->OPEN_PRODUCTS_BY_BONUS:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/16 v5, 0x8

    aput v5, v0, v4
    :try_end_7
    .catch Ljava/lang/NoSuchFieldError; {:try_start_7 .. :try_end_7} :catch_7

    :catch_7
    :try_start_8
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->PLAY_GAME:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/16 v5, 0x9

    aput v5, v0, v4
    :try_end_8
    .catch Ljava/lang/NoSuchFieldError; {:try_start_8 .. :try_end_8} :catch_8

    :catch_8
    :try_start_9
    sget-object v4, Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;->UNKNOWN:Lorg/xplatform/aggregator/impl/promo/presentation/models/StateBonus;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    const/16 v5, 0xa

    aput v5, v0, v4
    :try_end_9
    .catch Ljava/lang/NoSuchFieldError; {:try_start_9 .. :try_end_9} :catch_9

    :catch_9
    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;->a:[I

    invoke-static {}, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->values()[Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    move-result-object v0

    array-length v0, v0

    new-array v0, v0, [I

    :try_start_a
    sget-object v4, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->ALL:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-virtual {v4}, Ljava/lang/Enum;->ordinal()I

    move-result v4

    aput v1, v0, v4
    :try_end_a
    .catch Ljava/lang/NoSuchFieldError; {:try_start_a .. :try_end_a} :catch_a

    :catch_a
    :try_start_b
    sget-object v1, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->BONUSES:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aput v2, v0, v1
    :try_end_b
    .catch Ljava/lang/NoSuchFieldError; {:try_start_b .. :try_end_b} :catch_b

    :catch_b
    :try_start_c
    sget-object v1, Lorg/xplatform/aggregator/api/navigation/GiftsChipType;->FREE_SPINS:Lorg/xplatform/aggregator/api/navigation/GiftsChipType;

    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    move-result v1

    aput v3, v0, v1
    :try_end_c
    .catch Ljava/lang/NoSuchFieldError; {:try_start_c .. :try_end_c} :catch_c

    :catch_c
    sput-object v0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsViewModel$e;->b:[I

    return-void
.end method
