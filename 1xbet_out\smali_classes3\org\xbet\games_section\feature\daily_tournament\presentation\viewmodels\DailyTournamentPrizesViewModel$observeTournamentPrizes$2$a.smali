.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlinx/coroutines/flow/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/f;"
    }
.end annotation

.annotation runtime Lkot<PERSON>/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lp40/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lp40/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Lp40/a;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-interface {p2}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    if-eqz p2, :cond_0

    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 12
    .line 13
    new-instance p2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$a;

    .line 14
    .line 15
    invoke-static {p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-direct {p2, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$a;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 20
    .line 21
    .line 22
    invoke-static {p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->v3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V

    .line 23
    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    iget-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 27
    .line 28
    const/4 v0, 0x1

    .line 29
    invoke-static {p2, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->w3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Z)V

    .line 30
    .line 31
    .line 32
    iget-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;

    .line 33
    .line 34
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$b;

    .line 35
    .line 36
    invoke-virtual {p1}, Lp40/a;->b()Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-direct {v0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a$b;-><init>(Ljava/util/List;)V

    .line 41
    .line 42
    .line 43
    invoke-static {p2, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;->v3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$a;)V

    .line 44
    .line 45
    .line 46
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p1
.end method

.method public bridge synthetic emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lp40/a;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPrizesViewModel$observeTournamentPrizes$2$a;->a(Lp40/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
