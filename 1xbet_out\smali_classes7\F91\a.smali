.class public final LF91/a;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LF91/a$a;,
        LF91/a$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u0000 \u000e2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u000fBE\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0012\u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u0012\u000c\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\u00070\t\u0012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u0010"
    }
    d2 = {
        "LF91/a;",
        "LA4/e;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterCategoryUiModel;",
        "Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;",
        "filterType",
        "Lkotlin/Function1;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
        "",
        "changeCheckedState",
        "Lkotlin/Function0;",
        "openProvidersListener",
        "onProviderRemoved",
        "<init>",
        "(Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V",
        "f",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LF91/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LF91/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LF91/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LF91/a;->f:LF91/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .param p1    # Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/remoteconfig/domain/models/AggregatorFilterScreenStyleType;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xplatform/aggregator/impl/category/presentation/models/FilterItemUi;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    sget-object v0, LF91/a;->f:LF91/a$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    sget-object v1, LF91/a$b;->a:[I

    .line 9
    .line 10
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    aget p1, v1, p1

    .line 15
    .line 16
    const/4 v1, 0x1

    .line 17
    if-eq p1, v1, :cond_2

    .line 18
    .line 19
    const/4 v1, 0x2

    .line 20
    if-eq p1, v1, :cond_1

    .line 21
    .line 22
    const/4 v1, 0x3

    .line 23
    if-ne p1, v1, :cond_0

    .line 24
    .line 25
    invoke-static {p2}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/FiltersChipsTypeLargeHeaderAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 30
    .line 31
    .line 32
    invoke-static {p3, p4}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeLargeHeaderAdapterDelegateKt;->f(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 37
    .line 38
    .line 39
    invoke-static {}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ShimmersTypeLargeHeaderAdapterDelegateKt;->f()LA4/c;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 44
    .line 45
    .line 46
    return-void

    .line 47
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 48
    .line 49
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 50
    .line 51
    .line 52
    throw p1

    .line 53
    :cond_1
    invoke-static {p2}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/FiltersChipsTypeProviderChipsAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 58
    .line 59
    .line 60
    invoke-static {p3, p4}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeProviderChipsAdapterDelegateKt;->i(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 65
    .line 66
    .line 67
    invoke-static {}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ShimmersTypeProviderChipsAdapterDelegateKt;->f()LA4/c;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 72
    .line 73
    .line 74
    return-void

    .line 75
    :cond_2
    invoke-static {p2}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/FiltersChipsTypeSmallHeaderAdapterDelegateKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 80
    .line 81
    .line 82
    invoke-static {p3, p4}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ProvidersTypeSmallHeaderAdapterDelegateKt;->f(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 87
    .line 88
    .line 89
    invoke-static {}, Lorg/xplatform/aggregator/impl/category/presentation/filters/delegates/ShimmersTypeSmallHeaderAdapterDelegateKt;->f()LA4/c;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method public static final synthetic o()LF91/a$a;
    .locals 1

    .line 1
    sget-object v0, LF91/a;->f:LF91/a$a;

    .line 2
    .line 3
    return-object v0
.end method
