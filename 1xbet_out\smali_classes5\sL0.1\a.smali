.class public interface abstract LsL0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build LNc/c;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LsL0/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008a\u0018\u00002\u00020\u0001J\u00b4\u0001\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u00122\u0008\u0008\u0003\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0004\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0001\u0010\u0008\u001a\u00020\u00062\n\u0008\u0001\u0010\t\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\n\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u000b\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u000c\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\r\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u000e\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u000f\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u0010\u001a\u0004\u0018\u00010\u00022\n\u0008\u0001\u0010\u0011\u001a\u0004\u0018\u00010\u0002H\u00a7@\u00a2\u0006\u0004\u0008\u0014\u0010\u0015\u00a8\u0006\u0016"
    }
    d2 = {
        "LsL0/a;",
        "",
        "",
        "header",
        "lang",
        "gameId",
        "",
        "countryId",
        "ref",
        "tournamentId",
        "competitorType",
        "season",
        "date",
        "age",
        "discipline",
        "tournamentType",
        "recLimit",
        "startRec",
        "Le8/b;",
        "LvL0/a;",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "id"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # I
        .annotation runtime Lbd1/t;
            value = "fcountry"
        .end annotation
    .end param
    .param p5    # I
        .annotation runtime Lbd1/t;
            value = "ref"
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "selTourn"
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "competitorType"
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "season"
        .end annotation
    .end param
    .param p9    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "date"
        .end annotation
    .end param
    .param p10    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "age"
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "discipline"
        .end annotation
    .end param
    .param p12    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "tournamentType"
        .end annotation
    .end param
    .param p13    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "recLimit"
        .end annotation
    .end param
    .param p14    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "startRec"
        .end annotation
    .end param
    .param p15    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/statisticGame/v2/Rating/Table"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "II",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LvL0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
