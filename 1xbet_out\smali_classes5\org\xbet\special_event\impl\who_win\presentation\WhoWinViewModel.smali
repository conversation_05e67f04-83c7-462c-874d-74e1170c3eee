.class public final Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;
.super Landroidx/lifecycle/b0;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u001c\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001B[\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0019\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u0013\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001d0\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\r\u0010 \u001a\u00020\u0018\u00a2\u0006\u0004\u0008 \u0010\u001aJ\r\u0010!\u001a\u00020\u0018\u00a2\u0006\u0004\u0008!\u0010\u001aJ\u0015\u0010#\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008%\u0010\u001aR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0018\u0010=\u001a\u0004\u0018\u00010:8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u001a\u0010B\u001a\u0008\u0012\u0004\u0012\u00020?0>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010A\u00a8\u0006C"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;",
        "Landroidx/lifecycle/b0;",
        "",
        "eventId",
        "LwX0/c;",
        "router",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "getStageTableResultStreamUseCase",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
        "getStageTableUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "getProfileUseCase",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "<init>",
        "(ILwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/ui_common/utils/internet/a;)V",
        "",
        "E3",
        "()V",
        "G3",
        "Lkotlinx/coroutines/flow/e;",
        "LMy0/c;",
        "E0",
        "()Lkotlinx/coroutines/flow/e;",
        "onBackPressed",
        "J3",
        "tabPosition",
        "K3",
        "(I)V",
        "B3",
        "b1",
        "I",
        "k1",
        "LwX0/c;",
        "v1",
        "Lm8/a;",
        "x1",
        "Lorg/xbet/ui_common/utils/M;",
        "y1",
        "LHX0/e;",
        "F1",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/a;",
        "H1",
        "Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;",
        "I1",
        "Lp9/c;",
        "P1",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "S1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lkotlinx/coroutines/x0;",
        "V1",
        "Lkotlinx/coroutines/x0;",
        "whoWinJob",
        "Lkotlinx/coroutines/flow/V;",
        "LMy0/b;",
        "b2",
        "Lkotlinx/coroutines/flow/V;",
        "stateModel",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V1:Lkotlinx/coroutines/x0;

.field public final b1:I

.field public final b2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LMy0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(ILwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/special_event/impl/who_win/domain/usecase/a;Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;Lp9/c;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 0
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/special_event/impl/who_win/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Landroidx/lifecycle/b0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b1:I

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->k1:LwX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->v1:Lm8/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->x1:Lorg/xbet/ui_common/utils/M;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->y1:LHX0/e;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->F1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->H1:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->I1:Lp9/c;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->P1:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->S1:Lorg/xbet/ui_common/utils/internet/a;

    .line 23
    .line 24
    new-instance p1, LMy0/b;

    .line 25
    .line 26
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    const/4 p3, 0x0

    .line 31
    const/4 p4, 0x0

    .line 32
    invoke-direct {p1, p2, p3, p4}, LMy0/b;-><init>(Ljava/util/List;LMy0/a;Z)V

    .line 33
    .line 34
    .line 35
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 40
    .line 41
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->E3()V

    .line 42
    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->G3()V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public static final synthetic A3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->I3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final C3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->x1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/special_event/impl/who_win/presentation/f;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/special_event/impl/who_win/presentation/f;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final D3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method private final E3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->S1:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeConnection$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeConnection$1;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    sget-object v2, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeConnection$2;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeConnection$2;

    .line 22
    .line 23
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method private static final synthetic F3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final G3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->F1:Lorg/xbet/special_event/impl/who_win/domain/usecase/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/who_win/domain/usecase/a;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/d;

    .line 8
    .line 9
    invoke-direct {v1}, Lorg/xbet/special_event/impl/who_win/presentation/d;-><init>()V

    .line 10
    .line 11
    .line 12
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->D(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1;

    .line 17
    .line 18
    invoke-direct {v1, v0, p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)V

    .line 19
    .line 20
    .line 21
    sget-object v0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$3;->INSTANCE:Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$observeStageTable$3;

    .line 22
    .line 23
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iget-object v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->v1:Lm8/a;

    .line 28
    .line 29
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    invoke-static {v2, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-static {v1, v2, v0}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public static final H3(LKo0/a;)Ljava/lang/Object;
    .locals 2

    .line 1
    instance-of v0, p0, LKo0/a$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object p0

    .line 6
    :cond_0
    instance-of v0, p0, LKo0/a$b;

    .line 7
    .line 8
    if-eqz v0, :cond_2

    .line 9
    .line 10
    check-cast p0, LKo0/a$b;

    .line 11
    .line 12
    invoke-virtual {p0}, LKo0/a$b;->b()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    check-cast p0, Ljava/lang/Iterable;

    .line 17
    .line 18
    new-instance v0, Ljava/util/ArrayList;

    .line 19
    .line 20
    const/16 v1, 0xa

    .line 21
    .line 22
    invoke-static {p0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 27
    .line 28
    .line 29
    invoke-interface {p0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_1

    .line 38
    .line 39
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    check-cast v1, LDy0/a;

    .line 44
    .line 45
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_1
    return-object v0

    .line 54
    :cond_2
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p0
.end method

.method public static final synthetic I3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic o3(LKo0/a;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->H3(LKo0/a;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->C3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->D3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b1:I

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic s3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->I1:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->P1:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->H1:Lorg/xbet/special_event/impl/who_win/domain/usecase/GetStageTableUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->B3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->F3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final B3()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    new-instance v2, Lorg/xbet/special_event/impl/who_win/presentation/e;

    .line 11
    .line 12
    invoke-direct {v2, p0}, Lorg/xbet/special_event/impl/who_win/presentation/e;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;)V

    .line 13
    .line 14
    .line 15
    new-instance v6, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;

    .line 16
    .line 17
    const/4 v0, 0x0

    .line 18
    invoke-direct {v6, p0, v0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$loadWhoWinContent$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    const/16 v7, 0xe

    .line 22
    .line 23
    const/4 v8, 0x0

    .line 24
    const/4 v3, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    const/4 v5, 0x0

    .line 27
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iput-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    return-void
.end method

.method public final E0()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LMy0/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel$getUiState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method

.method public final J3()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->B3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final K3(I)V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, LMy0/b;

    .line 9
    .line 10
    invoke-virtual {v2}, LMy0/b;->c()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    invoke-static {v3, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    move-object v4, v3

    .line 19
    check-cast v4, LMy0/a;

    .line 20
    .line 21
    const/4 v6, 0x5

    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    invoke-static/range {v2 .. v7}, LMy0/b;->b(LMy0/b;Ljava/util/List;LMy0/a;ZILjava/lang/Object;)LMy0/b;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    if-eqz v1, :cond_0

    .line 34
    .line 35
    return-void
.end method

.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/WhoWinViewModel;->k1:LwX0/c;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method
