.class public final Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;LBy0/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->i0:LBy0/f;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/stage/group/opponents/OpponentsFragment;->j0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/b;

    .line 2
    .line 3
    return-void
.end method
