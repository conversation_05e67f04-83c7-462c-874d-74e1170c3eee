.class public final LTN0/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Landroid/view/View;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;Landroid/view/View;)V
    .locals 0
    .param p1    # Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LTN0/r;->a:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LTN0/r;->b:Landroid/view/View;

    .line 7
    .line 8
    return-void
.end method

.method public static a(Landroid/view/View;)LTN0/r;
    .locals 2
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    sget v0, LRN0/a;->viewEmpty1:I

    .line 2
    .line 3
    invoke-static {p0, v0}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    new-instance v0, LTN0/r;

    .line 10
    .line 11
    check-cast p0, Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 12
    .line 13
    invoke-direct {v0, p0, v1}, LTN0/r;-><init>(Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;Landroid/view/View;)V

    .line 14
    .line 15
    .line 16
    return-object v0

    .line 17
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    new-instance v0, Ljava/lang/NullPointerException;

    .line 26
    .line 27
    const-string v1, "Missing required view with ID: "

    .line 28
    .line 29
    invoke-virtual {v1, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-direct {v0, p0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    throw v0
.end method


# virtual methods
.method public b()Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LTN0/r;->a:Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTN0/r;->b()Lorg/xbet/ui_common/viewcomponents/layouts/constraint/ShimmerConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
