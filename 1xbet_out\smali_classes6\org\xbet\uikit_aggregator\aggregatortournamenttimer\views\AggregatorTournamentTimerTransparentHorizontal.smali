.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Le31/f;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u000e\n\u0002\u0010\r\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001f\u0010\u0010\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0012J\u000f\u0010\u0014\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0012J\u0017\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0017J\u0017\u0010\u0019\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0017J\u000f\u0010\u001a\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0012J\u000f\u0010\u001b\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0012J\u0017\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010\"\u001a\u00020\r2\u0006\u0010!\u001a\u00020 H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008$\u0010%J\u000f\u0010&\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008&\u0010%J\u000f\u0010\'\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\'\u0010%J\u001f\u0010(\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008(\u0010\u000fJ7\u0010/\u001a\u00020\r2\u0006\u0010*\u001a\u00020)2\u0006\u0010+\u001a\u00020\u00072\u0006\u0010,\u001a\u00020\u00072\u0006\u0010-\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008/\u00100J\u0017\u00103\u001a\u00020\r2\u0006\u00102\u001a\u000201H\u0016\u00a2\u0006\u0004\u00083\u00104J+\u00109\u001a\u00020\r2\u000c\u00106\u001a\u0008\u0012\u0004\u0012\u00020)052\u000c\u00108\u001a\u0008\u0012\u0004\u0012\u00020\r07H\u0016\u00a2\u0006\u0004\u00089\u0010:J\u000f\u0010;\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008;\u0010\u0012J\u001d\u0010=\u001a\u00020\r2\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020\r07H\u0016\u00a2\u0006\u0004\u0008=\u0010>R\u0014\u0010@\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010?R\u0014\u0010A\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010?R\u0014\u0010B\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010?R\u0014\u0010C\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010?R\u0014\u0010D\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010?R\u0014\u0010G\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010FR\u0014\u0010J\u001a\u00020H8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010IR\u0014\u0010M\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010LR\u0014\u0010N\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010L\u00a8\u0006O"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;",
        "Landroid/widget/FrameLayout;",
        "Le31/f;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "g",
        "(II)V",
        "e",
        "f",
        "()V",
        "d",
        "k",
        "centerX",
        "i",
        "(I)V",
        "j",
        "h",
        "c",
        "b",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "Le31/c;",
        "timerModel",
        "setTimer",
        "(Le31/c;)V",
        "getAllHeight",
        "()I",
        "getTitleViewHeightWithBottomMargin",
        "getTitleShimmerHeightWithBottomMargin",
        "onMeasure",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Le31/b;",
        "model",
        "setModel",
        "(Le31/b;)V",
        "Lkotlinx/coroutines/flow/e;",
        "stopTimerFlow",
        "Lkotlin/Function0;",
        "timeOutCallback",
        "a",
        "(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V",
        "clear",
        "callback",
        "setOnTimerExpiredListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "I",
        "titleTimerSpace",
        "titleShimmerHeight",
        "titleShimmerWidth",
        "timerShimmerHeight",
        "timerShimmerWidth",
        "Landroid/widget/TextView;",
        "Landroid/widget/TextView;",
        "titleView",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;",
        "timerView",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "titleShimmer",
        "timerShimmer",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 12
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_10:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->a:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_12:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->b:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_128:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->c:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_40:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->d:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_304:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->e:I

    .line 10
    new-instance p2, Landroid/widget/TextView;

    invoke-direct {p2, p1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 11
    const-string p3, "tag_title_view_tournament_timer"

    invoke-virtual {p2, p3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 12
    sget p3, LlZ0/n;->TextStyle_Caption_Regular_L_TextPrimary:I

    invoke-static {p2, p3}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/4 p3, 0x4

    .line 13
    invoke-virtual {p2, p3}, Landroid/view/View;->setTextAlignment(I)V

    .line 14
    iput-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 15
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object v7, v1

    .line 16
    const-string p1, "tag_timer_view_tournament_timer"

    invoke-virtual {v0, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 17
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 18
    new-instance v6, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v10, 0x6

    const/4 v11, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 19
    const-string p1, "tag_title_shimmer_tournament_timer"

    invoke-virtual {v6, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 20
    new-instance p1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {p1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 21
    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->radius_full:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimension(I)F

    move-result p2

    invoke-virtual {p1, p2}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 22
    sget p2, LlZ0/d;->uikitSecondary20:I

    const/4 p3, 0x0

    const/4 v0, 0x2

    invoke-static {v7, p2, p3, v0, p3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p2

    invoke-static {p2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 23
    invoke-virtual {v6, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 24
    iput-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 25
    new-instance v6, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    const-string p1, "tag_timer_shimmer_tournament_timer"

    invoke-virtual {v6, p1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 27
    new-instance p1, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {p1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 28
    invoke-virtual {v7}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v1, LlZ0/g;->radius_10:I

    invoke-virtual {p2, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result p2

    invoke-virtual {p1, p2}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 29
    sget p2, LlZ0/d;->uikitSecondary20:I

    invoke-static {v7, p2, p3, v0, p3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p2

    invoke-static {p2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 30
    invoke-virtual {v6, p1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 31
    iput-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 32
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->b()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final b()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 18
    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 23
    .line 24
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    if-nez v0, :cond_1

    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 31
    .line 32
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 33
    .line 34
    .line 35
    :cond_1
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method private final c()V
    .locals 1

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 5
    .line 6
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_1

    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 26
    .line 27
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 28
    .line 29
    .line 30
    :cond_1
    return-void
.end method

.method private final d()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 12
    .line 13
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->e:I

    .line 14
    .line 15
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->d:I

    .line 20
    .line 21
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    const/4 v0, 0x0

    .line 30
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 35
    .line 36
    invoke-virtual {v1, v0, v0}, Landroid/view/View;->measure(II)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method private final e(II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 10
    .line 11
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    const/high16 p2, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 23
    .line 24
    invoke-virtual {p2, p1, p1}, Landroid/view/View;->measure(II)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method private final f()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/high16 v1, 0x40000000    # 2.0f

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 12
    .line 13
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->c:I

    .line 14
    .line 15
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->b:I

    .line 20
    .line 21
    invoke-static {v3, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    const/4 v0, 0x0

    .line 30
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 35
    .line 36
    invoke-virtual {v1, v0, v0}, Landroid/view/View;->measure(II)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method private final g(II)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 10
    .line 11
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    const/high16 p2, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 23
    .line 24
    invoke-virtual {p2, p1, p1}, Landroid/view/View;->measure(II)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method private final getAllHeight()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    goto :goto_1

    .line 18
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->getTitleViewHeightWithBottomMargin()I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 23
    .line 24
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    :goto_0
    add-int/2addr v0, v1

    .line 29
    return v0

    .line 30
    :cond_1
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->getTitleShimmerHeightWithBottomMargin()I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 35
    .line 36
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    goto :goto_0
.end method

.method private final getTitleShimmerHeightWithBottomMargin()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->a:I

    .line 8
    .line 9
    add-int/2addr v0, v1

    .line 10
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->a:I

    .line 19
    .line 20
    if-le v1, v2, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 v0, 0x0

    .line 24
    :goto_0
    if-eqz v0, :cond_1

    .line 25
    .line 26
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    return v0

    .line 31
    :cond_1
    const/4 v0, 0x0

    .line 32
    return v0
.end method

.method private final getTitleViewHeightWithBottomMargin()I
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->a:I

    .line 8
    .line 9
    add-int/2addr v0, v1

    .line 10
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->a:I

    .line 19
    .line 20
    if-le v1, v2, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 v0, 0x0

    .line 24
    :goto_0
    if-eqz v0, :cond_1

    .line 25
    .line 26
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    return v0

    .line 31
    :cond_1
    const/4 v0, 0x0

    .line 32
    return v0
.end method

.method private final h(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    div-int/lit8 v0, v0, 0x2

    .line 16
    .line 17
    sub-int/2addr p1, v0

    .line 18
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->getTitleShimmerHeightWithBottomMargin()I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 23
    .line 24
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    add-int/2addr v2, p1

    .line 29
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 30
    .line 31
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    add-int/2addr v3, v0

    .line 36
    invoke-virtual {v1, p1, v0, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 37
    .line 38
    .line 39
    :cond_0
    return-void
.end method

.method private final i(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    div-int/lit8 v0, v0, 0x2

    .line 16
    .line 17
    sub-int/2addr p1, v0

    .line 18
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->getTitleViewHeightWithBottomMargin()I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 23
    .line 24
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    add-int/2addr v2, p1

    .line 29
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 30
    .line 31
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    add-int/2addr v3, v0

    .line 36
    invoke-virtual {v1, p1, v0, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 37
    .line 38
    .line 39
    :cond_0
    return-void
.end method

.method private final j(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    div-int/lit8 v0, v0, 0x2

    .line 16
    .line 17
    sub-int/2addr p1, v0

    .line 18
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    add-int/2addr v1, p1

    .line 25
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 26
    .line 27
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    const/4 v3, 0x0

    .line 32
    invoke-virtual {v0, p1, v3, v1, v2}, Landroid/view/View;->layout(IIII)V

    .line 33
    .line 34
    .line 35
    :cond_0
    return-void
.end method

.method private final k()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 16
    .line 17
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    const/4 v3, 0x0

    .line 22
    invoke-virtual {v0, v3, v3, v1, v2}, Landroid/view/View;->layout(IIII)V

    .line 23
    .line 24
    .line 25
    :cond_0
    return-void
.end method

.method private final setTimer(Le31/c;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->setModel(Le31/c;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method private final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/uikit/utils/S;->h(Landroid/view/View;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f:Landroid/widget/TextView;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public a(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlinx/coroutines/flow/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->y(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public clear()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->o()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    div-int/lit8 p1, p1, 0x2

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->k()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->i(I)V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->j(I)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->h(I)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onMeasure(II)V
    .locals 0

    .line 1
    const/4 p2, 0x0

    .line 2
    invoke-static {p2, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 3
    .line 4
    .line 5
    move-result p2

    .line 6
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g(II)V

    .line 7
    .line 8
    .line 9
    invoke-direct {p0, p2, p2}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->e(II)V

    .line 10
    .line 11
    .line 12
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->f()V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->d()V

    .line 16
    .line 17
    .line 18
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->getAllHeight()I

    .line 23
    .line 24
    .line 25
    move-result p2

    .line 26
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public setModel(Le31/b;)V
    .locals 1
    .param p1    # Le31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Le31/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Le31/a;

    .line 6
    .line 7
    invoke-virtual {p1}, Le31/a;->c()Le31/c;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Le31/c;->g()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-direct {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->setTitle(Ljava/lang/CharSequence;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1}, Le31/a;->c()Le31/c;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->setTimer(Le31/c;)V

    .line 23
    .line 24
    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->c()V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_0
    instance-of p1, p1, Le31/d;

    .line 30
    .line 31
    if-eqz p1, :cond_1

    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->b()V

    .line 34
    .line 35
    .line 36
    :cond_1
    return-void
.end method

.method public setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregatorTournamentTimerTransparentHorizontal;->g:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerTransparentHorizontalTimerView;->setOnTimerExpiredListener(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
