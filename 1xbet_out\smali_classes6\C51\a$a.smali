.class public final LC51/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LC51/e$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LC51/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LC51/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LC51/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a()LC51/e;
    .locals 2

    .line 1
    new-instance v0, LC51/a$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LC51/a$b;-><init>(LC51/b;)V

    .line 5
    .line 6
    .line 7
    return-object v0
.end method
