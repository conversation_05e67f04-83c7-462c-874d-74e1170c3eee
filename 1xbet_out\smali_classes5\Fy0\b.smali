.class public final LFy0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LFy0/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0001\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LFy0/b;",
        "LFy0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;",
        "params",
        "Lq4/q;",
        "a",
        "(Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;)Lq4/q;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;)Lq4/q;
    .locals 1
    .param p1    # Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LFy0/b$a;

    .line 2
    .line 3
    invoke-direct {v0, p1}, LFy0/b$a;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/model/WhoWinScreenParams;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
