.class public final LLV0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "LMV0/a;",
        "LPV0/a;",
        "a",
        "(LMV0/a;)LPV0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LMV0/a;)LPV0/a;
    .locals 14
    .param p0    # LMV0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LPV0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LMV0/a;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const-string v2, ""

    .line 8
    .line 9
    if-nez v1, :cond_0

    .line 10
    .line 11
    move-object v1, v2

    .line 12
    :cond_0
    invoke-virtual {p0}, LMV0/a;->b()Ljava/lang/Integer;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    const/4 v4, -0x1

    .line 17
    if-eqz v3, :cond_1

    .line 18
    .line 19
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    goto :goto_0

    .line 24
    :cond_1
    const/4 v3, -0x1

    .line 25
    :goto_0
    invoke-virtual {p0}, LMV0/a;->c()Ljava/lang/Double;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    const-wide/high16 v6, -0x4010000000000000L    # -1.0

    .line 30
    .line 31
    if-eqz v5, :cond_2

    .line 32
    .line 33
    invoke-virtual {v5}, Ljava/lang/Double;->doubleValue()D

    .line 34
    .line 35
    .line 36
    move-result-wide v8

    .line 37
    goto :goto_1

    .line 38
    :cond_2
    move-wide v8, v6

    .line 39
    :goto_1
    invoke-virtual {p0}, LMV0/a;->d()Ljava/lang/Double;

    .line 40
    .line 41
    .line 42
    move-result-object v5

    .line 43
    if-eqz v5, :cond_3

    .line 44
    .line 45
    invoke-virtual {v5}, Ljava/lang/Double;->doubleValue()D

    .line 46
    .line 47
    .line 48
    move-result-wide v10

    .line 49
    goto :goto_2

    .line 50
    :cond_3
    move-wide v10, v6

    .line 51
    :goto_2
    invoke-virtual {p0}, LMV0/a;->e()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v5

    .line 55
    if-nez v5, :cond_4

    .line 56
    .line 57
    goto :goto_3

    .line 58
    :cond_4
    move-object v2, v5

    .line 59
    :goto_3
    invoke-virtual {p0}, LMV0/a;->g()Ljava/lang/Double;

    .line 60
    .line 61
    .line 62
    move-result-object v5

    .line 63
    if-eqz v5, :cond_5

    .line 64
    .line 65
    invoke-virtual {v5}, Ljava/lang/Double;->doubleValue()D

    .line 66
    .line 67
    .line 68
    move-result-wide v6

    .line 69
    :cond_5
    invoke-virtual {p0}, LMV0/a;->f()Ljava/lang/Integer;

    .line 70
    .line 71
    .line 72
    move-result-object p0

    .line 73
    if-eqz p0, :cond_6

    .line 74
    .line 75
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 76
    .line 77
    .line 78
    move-result v4

    .line 79
    move-wide v12, v6

    .line 80
    move-wide v5, v10

    .line 81
    move v10, v4

    .line 82
    :goto_4
    move-object v7, v2

    .line 83
    move v2, v3

    .line 84
    move-wide v3, v8

    .line 85
    move-wide v8, v12

    .line 86
    goto :goto_5

    .line 87
    :cond_6
    move-wide v12, v6

    .line 88
    move-wide v5, v10

    .line 89
    const/4 v10, -0x1

    .line 90
    goto :goto_4

    .line 91
    :goto_5
    invoke-direct/range {v0 .. v10}, LPV0/a;-><init>(Ljava/lang/String;IDDLjava/lang/String;DI)V

    .line 92
    .line 93
    .line 94
    return-object v0
.end method
