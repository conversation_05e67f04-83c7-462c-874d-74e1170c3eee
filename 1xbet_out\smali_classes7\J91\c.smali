.class public interface abstract LJ91/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build LNc/c;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LJ91/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000~\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008g\u0018\u00002\u00020\u0001J,\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0014\u0008\u0001\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0002H\u00a7@\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J,\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00052\u0014\u0008\u0001\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00030\u0002H\u00a7@\u00a2\u0006\u0004\u0008\n\u0010\u0008J6\u0010\u000c\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0014\u0008\u0001\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00022\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008\u000c\u0010\rJ>\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u00032\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J>\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u00142\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u00032\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J>\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u00172\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u00032\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J*\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u00172\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ$\u0010\u001f\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0004\u0008\u001f\u0010 J$\u0010!\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0004\u0008!\u0010 J$\u0010#\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\"H\u00a7@\u00a2\u0006\u0004\u0008#\u0010$J$\u0010%\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0004\u0008%\u0010 J$\u0010&\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0004\u0008&\u0010 J$\u0010\'\u001a\u00020\u001e2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010\u000f\u001a\u00020\"H\u00a7@\u00a2\u0006\u0004\u0008\'\u0010$J6\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00052\u0014\u0008\u0001\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00022\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008(\u0010\rJB\u0010/\u001a\u00020.2\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010*\u001a\u00020)2\u0008\u0008\u0001\u0010+\u001a\u00020\u00032\u0008\u0008\u0001\u0010-\u001a\u00020,2\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u0008/\u00100JL\u00105\u001a\u0002042\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0001\u0010*\u001a\u00020)2\u0008\u0008\u0001\u0010-\u001a\u00020,2\u0008\u0008\u0001\u00101\u001a\u00020\u00032\u0008\u0008\u0001\u00103\u001a\u0002022\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0004\u00085\u00106JB\u00108\u001a\u0002072\u0008\u0008\u0001\u0010\u001c\u001a\u00020\u00032\u0008\u0008\u0003\u0010\u000b\u001a\u00020\u00032\u0008\u0008\u0001\u0010*\u001a\u00020)2\u0008\u0008\u0001\u0010+\u001a\u00020\u00032\u0008\u0008\u0001\u0010-\u001a\u00020,H\u00a7@\u00a2\u0006\u0004\u00088\u00109\u00a8\u0006:"
    }
    d2 = {
        "LJ91/c;",
        "",
        "",
        "",
        "paramsMap",
        "Le8/b;",
        "LL91/e;",
        "c",
        "(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LL91/b;",
        "j",
        "acceptHeader",
        "q",
        "(Ljava/util/Map;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LL91/n;",
        "request",
        "appGuid",
        "token",
        "p",
        "(LL91/n;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LL91/i;",
        "i",
        "(LL91/i;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "LL91/h;",
        "g",
        "(LL91/h;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "l",
        "(LL91/h;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "auth",
        "LU91/b;",
        "LU91/c;",
        "n",
        "(Ljava/lang/String;LU91/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "e",
        "LU91/a;",
        "m",
        "(Ljava/lang/String;LU91/a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "f",
        "k",
        "d",
        "o",
        "",
        "userId",
        "language",
        "",
        "whence",
        "Lua1/g;",
        "b",
        "(Ljava/lang/String;JLjava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "country",
        "",
        "onlyActive",
        "Lua1/i;",
        "a",
        "(Ljava/lang/String;JILjava/lang/String;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lua1/a;",
        "h",
        "(Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;JILjava/lang/String;ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # J
        .annotation runtime Lbd1/t;
            value = "accId"
        .end annotation
    .end param
    .param p4    # I
        .annotation runtime Lbd1/t;
            value = "Whence"
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "fcountry"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Z
        .annotation runtime Lbd1/t;
            value = "onlyActive"
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "MobileF2/CntAvailableOffers"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "JI",
            "Ljava/lang/String;",
            "Z",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Lua1/i;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract b(Ljava/lang/String;JLjava/lang/String;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # J
        .annotation runtime Lbd1/t;
            value = "AccId"
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # I
        .annotation runtime Lbd1/t;
            value = "Whence"
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "MobileB2/CountBonusesAvailable"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "J",
            "Ljava/lang/String;",
            "I",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Lua1/g;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract c(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/GetGames"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract d(Ljava/lang/String;LU91/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/a;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/ClearFavoriteGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/a;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract e(Ljava/lang/String;LU91/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/b;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/FavoriteGames/RemoveFavoriteGame"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/b;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract f(Ljava/lang/String;LU91/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/b;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/AddFavoriteGame"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/b;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract g(LL91/h;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # LL91/h;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "AppGuid"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/GetFavoriteGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL91/h;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract h(Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # J
        .annotation runtime Lbd1/t;
            value = "AccId"
        .end annotation
    .end param
    .param p5    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # I
        .annotation runtime Lbd1/t;
            value = "Whence"
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "MobileB2/ActiveBonusAmount"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "J",
            "Ljava/lang/String;",
            "I",
            "Lkotlin/coroutines/e<",
            "Lua1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract i(LL91/i;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # LL91/i;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "AppGuid"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/GetRecommendedGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL91/i;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract j(Ljava/util/Map;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/Games/Get"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/b;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract k(Ljava/lang/String;LU91/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/b;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/RemoveFavoriteGame"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/b;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract l(LL91/h;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # LL91/h;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/FavoriteGames/GetFavoriteGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL91/h;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract m(Ljava/lang/String;LU91/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/a;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/FavoriteGames/ClearFavoriteGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/a;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract n(Ljava/lang/String;LU91/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LU91/b;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/FavoriteGames/AddFavoriteGame"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "LU91/b;",
            "Lkotlin/coroutines/e<",
            "LU91/c;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract o(Ljava/util/Map;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/GetGamesByCharsMobile"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract p(LL91/n;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # LL91/n;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "AppGuid"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/o;
        value = "/Aggregator_v3/v2/GetRecommendedGames"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL91/n;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract q(Ljava/util/Map;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/util/Map;
        .annotation runtime Lbd1/u;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "Accept"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/Aggregator_v3/v2/GetGamesForCategory"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "Le8/b<",
            "LL91/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
