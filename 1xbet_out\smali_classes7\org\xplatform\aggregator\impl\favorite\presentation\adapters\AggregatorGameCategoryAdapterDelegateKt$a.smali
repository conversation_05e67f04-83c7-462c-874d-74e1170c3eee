.class public final Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->f(Lkotlin/jvm/functions/Function2;LOc/n;)LA4/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lkotlin/jvm/functions/Function1<",
        "Ljava/util/List<",
        "+",
        "Ljava/lang/Object;",
        ">;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LB4/a;


# direct methods
.method public constructor <init>(LB4/a;LB4/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 9
    .line 10
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    check-cast p1, LS91/m0;

    .line 15
    .line 16
    iget-object p1, p1, LS91/m0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 19
    .line 20
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    check-cast v0, Lt81/b;

    .line 25
    .line 26
    invoke-virtual {v0}, Lt81/b;->B()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 31
    .line 32
    .line 33
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 34
    .line 35
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    check-cast p1, Lt81/b;

    .line 40
    .line 41
    invoke-virtual {p1}, Lt81/b;->j()Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 46
    .line 47
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    check-cast v0, LS91/m0;

    .line 52
    .line 53
    iget-object v0, v0, LS91/m0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 54
    .line 55
    invoke-static {p1, v0, v1}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->l(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;Z)V

    .line 56
    .line 57
    .line 58
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 59
    .line 60
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    check-cast p1, LS91/m0;

    .line 65
    .line 66
    iget-object p1, p1, LS91/m0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 67
    .line 68
    new-instance v0, Lorg/xbet/uikit/components/header/a$a;

    .line 69
    .line 70
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 71
    .line 72
    invoke-virtual {v1}, LB4/a;->i()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    check-cast v1, Lt81/b;

    .line 77
    .line 78
    invoke-virtual {v1}, Lt81/b;->getTitle()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a:LB4/a;

    .line 83
    .line 84
    sget v3, Lpb/k;->all:I

    .line 85
    .line 86
    invoke-virtual {v2, v3}, LB4/a;->j(I)Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v4

    .line 90
    const/16 v10, 0x1f6

    .line 91
    .line 92
    const/4 v11, 0x0

    .line 93
    const/4 v2, 0x0

    .line 94
    const/4 v3, 0x0

    .line 95
    const/4 v5, 0x0

    .line 96
    const/4 v6, 0x0

    .line 97
    const/4 v7, 0x0

    .line 98
    const/4 v8, 0x0

    .line 99
    const/4 v9, 0x0

    .line 100
    invoke-direct/range {v0 .. v11}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 101
    .line 102
    .line 103
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    .line 104
    .line 105
    .line 106
    return-void

    .line 107
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 108
    .line 109
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 110
    .line 111
    .line 112
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v2

    .line 120
    if-eqz v2, :cond_1

    .line 121
    .line 122
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v2

    .line 126
    check-cast v2, Ljava/util/Collection;

    .line 127
    .line 128
    check-cast v2, Ljava/lang/Iterable;

    .line 129
    .line 130
    invoke-static {v0, v2}, Lkotlin/collections/A;->D(Ljava/util/Collection;Ljava/lang/Iterable;)Z

    .line 131
    .line 132
    .line 133
    goto :goto_0

    .line 134
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 135
    .line 136
    .line 137
    move-result-object p1

    .line 138
    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 139
    .line 140
    .line 141
    move-result v0

    .line 142
    if-eqz v0, :cond_4

    .line 143
    .line 144
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object v0

    .line 148
    check-cast v0, Lt81/b$b;

    .line 149
    .line 150
    instance-of v2, v0, Lt81/b$b$a;

    .line 151
    .line 152
    if-eqz v2, :cond_2

    .line 153
    .line 154
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 155
    .line 156
    invoke-virtual {v0}, LB4/a;->i()Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    check-cast v0, Lt81/b;

    .line 161
    .line 162
    invoke-virtual {v0}, Lt81/b;->j()Ljava/util/List;

    .line 163
    .line 164
    .line 165
    move-result-object v0

    .line 166
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 167
    .line 168
    invoke-virtual {v2}, LB4/a;->e()LL2/a;

    .line 169
    .line 170
    .line 171
    move-result-object v2

    .line 172
    check-cast v2, LS91/m0;

    .line 173
    .line 174
    iget-object v2, v2, LS91/m0;->c:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 175
    .line 176
    const/4 v3, 0x2

    .line 177
    const/4 v4, 0x0

    .line 178
    invoke-static {v0, v2, v1, v3, v4}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt;->m(Ljava/util/List;Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;ZILjava/lang/Object;)V

    .line 179
    .line 180
    .line 181
    goto :goto_1

    .line 182
    :cond_2
    instance-of v0, v0, Lt81/b$b$b;

    .line 183
    .line 184
    if-eqz v0, :cond_3

    .line 185
    .line 186
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 187
    .line 188
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    check-cast v0, LS91/m0;

    .line 193
    .line 194
    iget-object v0, v0, LS91/m0;->b:Lorg/xbet/uikit/components/header/DSHeader;

    .line 195
    .line 196
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->b:LB4/a;

    .line 197
    .line 198
    invoke-virtual {v2}, LB4/a;->i()Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object v2

    .line 202
    check-cast v2, Lt81/b;

    .line 203
    .line 204
    invoke-virtual {v2}, Lt81/b;->getTitle()Ljava/lang/String;

    .line 205
    .line 206
    .line 207
    move-result-object v2

    .line 208
    invoke-virtual {v0, v2}, Lorg/xbet/uikit/components/header/DSHeader;->setLabel(Ljava/lang/CharSequence;)V

    .line 209
    .line 210
    .line 211
    goto :goto_1

    .line 212
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 213
    .line 214
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 215
    .line 216
    .line 217
    throw p1

    .line 218
    :cond_4
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/favorite/presentation/adapters/AggregatorGameCategoryAdapterDelegateKt$a;->a(Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p1
.end method
