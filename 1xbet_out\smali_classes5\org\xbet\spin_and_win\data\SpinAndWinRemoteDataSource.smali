.class public final Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007JN\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0008\u0010\r\u001a\u0004\u0018\u00010\u000c2\u000c\u0010\u0010\u001a\u0008\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0013H\u0086@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u001aR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u001a\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u001f\u00a8\u0006!"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;",
        "",
        "Lf8/g;",
        "serviceGenerator",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "<init>",
        "(Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V",
        "",
        "betSum",
        "",
        "activeId",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "gameBonus",
        "",
        "LZy0/a;",
        "betUser",
        "",
        "whence",
        "",
        "language",
        "Lg9/d;",
        "Laz0/a;",
        "c",
        "(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "Lf8/g;",
        "b",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "Lkotlin/Function0;",
        "LXy0/a;",
        "Lkotlin/jvm/functions/Function0;",
        "spinAndWinApiService",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "LXy0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->a:Lf8/g;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 7
    .line 8
    new-instance p1, Lorg/xbet/spin_and_win/data/b;

    .line 9
    .line 10
    invoke-direct {p1, p0}, Lorg/xbet/spin_and_win/data/b;-><init>(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)V

    .line 11
    .line 12
    .line 13
    iput-object p1, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->c:Lkotlin/jvm/functions/Function0;

    .line 14
    .line 15
    return-void
.end method

.method public static synthetic a(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)LXy0/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->d(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)LXy0/a;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic b(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)Lkotlin/jvm/functions/Function0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->c:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final d(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;)LXy0/a;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->a:Lf8/g;

    .line 2
    .line 3
    const-class v0, LXy0/a;

    .line 4
    .line 5
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    check-cast p0, LXy0/a;

    .line 14
    .line 15
    return-object p0
.end method


# virtual methods
.method public final c(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .param p6    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(DJ",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Ljava/util/List<",
            "LZy0/a;",
            ">;I",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lg9/d<",
            "Laz0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->b:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;

    .line 4
    .line 5
    const/4 v11, 0x0

    .line 6
    move-object v2, p0

    .line 7
    move-wide v5, p1

    .line 8
    move-wide v7, p3

    .line 9
    move-object/from16 v3, p5

    .line 10
    .line 11
    move-object/from16 v4, p6

    .line 12
    .line 13
    move/from16 v10, p7

    .line 14
    .line 15
    move-object/from16 v9, p8

    .line 16
    .line 17
    invoke-direct/range {v1 .. v11}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource$play$2;-><init>(Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;Lorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;DJLjava/lang/String;ILkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    move-object/from16 p1, p9

    .line 21
    .line 22
    invoke-virtual {v0, v1, p1}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    return-object p1
.end method
