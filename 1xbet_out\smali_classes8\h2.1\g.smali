.class public final synthetic Lh2/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/media3/container/i$b;


# instance fields
.field public final synthetic a:Lh2/h;


# direct methods
.method public synthetic constructor <init>(Lh2/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lh2/g;->a:Lh2/h;

    return-void
.end method


# virtual methods
.method public final a(JLt1/G;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lh2/g;->a:Lh2/h;

    invoke-static {v0, p1, p2, p3}, Lh2/h;->c(Lh2/h;JLt1/G;)V

    return-void
.end method
