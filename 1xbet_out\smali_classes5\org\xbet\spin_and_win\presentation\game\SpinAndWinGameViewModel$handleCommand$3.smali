.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameViewModel$handleCommand$3"
    f = "SpinAndWinGameViewModel.kt"
    l = {
        0x110
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->l4(LTv/d;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;

    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->G3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/f;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p1}, Lez0/f;->a()Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput v2, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->label:I

    .line 38
    .line 39
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-ne p1, v0, :cond_2

    .line 44
    .line 45
    return-object v0

    .line 46
    :cond_2
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 49
    .line 50
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->F3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;)Lez0/e;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {v0}, Lez0/e;->a()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;->isEmpty()Z

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    if-eqz v0, :cond_3

    .line 63
    .line 64
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_3

    .line 69
    .line 70
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel$handleCommand$3;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;

    .line 71
    .line 72
    sget-object v0, Lorg/xbet/spin_and_win/presentation/game/c$a;->a:Lorg/xbet/spin_and_win/presentation/game/c$a;

    .line 73
    .line 74
    invoke-static {p1, v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;->Y3(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameViewModel;Lorg/xbet/spin_and_win/presentation/game/c;)V

    .line 75
    .line 76
    .line 77
    :cond_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 78
    .line 79
    return-object p1
.end method
