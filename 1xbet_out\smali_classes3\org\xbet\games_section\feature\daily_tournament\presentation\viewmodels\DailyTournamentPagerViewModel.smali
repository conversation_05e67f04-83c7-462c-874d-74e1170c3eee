.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001:\u0001BBK\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0008\u0008\u0001\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017H\u0000\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0014H\u0000\u00a2\u0006\u0004\u0008\u001b\u0010\u0016J\u0017\u0010\u001e\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u001cH\u0000\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010!\u001a\u00020\u00142\u0006\u0010 \u001a\u00020\u001cH\u0000\u00a2\u0006\u0004\u0008!\u0010\u001fJ\u000f\u0010\"\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0016J\u000f\u0010#\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0016J\u0013\u0010$\u001a\u00020\u0014*\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0018\u00109\u001a\u0004\u0018\u0001068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u0018\u0010;\u001a\u0004\u0018\u0001068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008:\u00108R\u001a\u0010?\u001a\u0008\u0012\u0004\u0012\u00020\u001c0<8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u001a\u0010A\u001a\u0008\u0012\u0004\u0012\u00020\u00180<8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010>\u00a8\u0006C"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;",
        "loadUserPlaceModelUseCase",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;",
        "getUserPlaceModelFlowUseCase",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;",
        "getRuleIdWithRefUseCase",
        "LwX0/c;",
        "router",
        "Lm8/a;",
        "dispatchers",
        "LXv/a;",
        "gamesSectionRulesScreenFactory",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "<init>",
        "(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;LwX0/c;Lm8/a;LXv/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;)V",
        "",
        "w3",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;",
        "u3",
        "()Lkotlinx/coroutines/flow/e;",
        "y3",
        "",
        "fromGames",
        "z3",
        "(Z)V",
        "show",
        "C3",
        "x3",
        "v3",
        "A3",
        "(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;)V",
        "v1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;",
        "x1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;",
        "y1",
        "Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;",
        "F1",
        "LwX0/c;",
        "H1",
        "Lm8/a;",
        "I1",
        "LXv/a;",
        "P1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "S1",
        "Lorg/xbet/ui_common/utils/M;",
        "Lkotlinx/coroutines/x0;",
        "V1",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "b2",
        "loadTournamentJob",
        "Lkotlinx/coroutines/flow/V;",
        "v2",
        "Lkotlinx/coroutines/flow/V;",
        "userResultFlow",
        "x2",
        "state",
        "a",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LXv/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V1:Lkotlinx/coroutines/x0;

.field public b2:Lkotlinx/coroutines/x0;

.field public final v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;LwX0/c;Lm8/a;LXv/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;)V
    .locals 0
    .param p1    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LXv/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->y1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->F1:LwX0/c;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->H1:Lm8/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->I1:LXv/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 19
    .line 20
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 21
    .line 22
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v2:Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    sget-object p1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a$a;->a:Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a$a;

    .line 29
    .line 30
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->w3()V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x3()V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method private static final B3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic p3(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->B3(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;)Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/LoadUserPlaceModelUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic t3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->A3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final w3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeConnection$1;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v1, p0, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeConnection$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->V1:Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    return-void
.end method


# virtual methods
.method public final A3(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;)V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->H1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/a;

    .line 12
    .line 13
    invoke-direct {v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/a;-><init>()V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$send$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$send$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final C3(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public final u3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v3()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->b2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->H1:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$loadTournamentItem$1;

    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 26
    .line 27
    invoke-direct {v3, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$loadTournamentItem$1;-><init>(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    new-instance v7, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$loadTournamentItem$2;

    .line 31
    .line 32
    const/4 v0, 0x0

    .line 33
    invoke-direct {v7, p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$loadTournamentItem$2;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/16 v8, 0xa

    .line 37
    .line 38
    const/4 v9, 0x0

    .line 39
    const/4 v4, 0x0

    .line 40
    const/4 v6, 0x0

    .line 41
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->b2:Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    return-void
.end method

.method public final x3()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->x1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/i;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->v2:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    new-instance v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;

    .line 10
    .line 11
    const/4 v3, 0x0

    .line 12
    invoke-direct {v2, p0, v3}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel$observeUserPlace$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    iget-object v2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->H1:Lm8/a;

    .line 24
    .line 25
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final y3()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->F1:LwX0/c;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final z3(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->F1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->I1:LXv/a;

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->y1:Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;

    .line 6
    .line 7
    invoke-virtual {v2}, Lorg/xbet/games_section/feature/daily_tournament/domain/usecase/c;->a()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {v1, v2, p1}, LXv/a;->d(Ljava/lang/String;Z)Lr4/d;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method
