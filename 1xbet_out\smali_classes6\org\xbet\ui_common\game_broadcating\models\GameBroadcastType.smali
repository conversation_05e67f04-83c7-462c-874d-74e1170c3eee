.class public final enum Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;
.super Ljava/lang/Enum;
.source "SourceFile"

# interfaces
.implements Landroid/os/Parcelable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;",
        ">;",
        "Landroid/os/Parcelable;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008\u0006\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0008\u0087\u0081\u0002\u0018\u00002\u00020\u00012\u0008\u0012\u0004\u0012\u00020\u00000\u0002B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0006\u0010\u0008\u001a\u00020\tJ\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\tj\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;",
        "Landroid/os/Parcelable;",
        "",
        "<init>",
        "(Ljava/lang/String;I)V",
        "VIDEO",
        "ZONE",
        "NONE",
        "describeContents",
        "",
        "writeToParcel",
        "",
        "dest",
        "Landroid/os/Parcel;",
        "flags",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

.field public static final CREATOR:Landroid/os/Parcelable$Creator;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroid/os/Parcelable$Creator<",
            "Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum NONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

.field public static final enum VIDEO:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

.field public static final enum ZONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 2
    .line 3
    const-string v1, "VIDEO"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;-><init>(Ljava/lang/String;I)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->VIDEO:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 12
    .line 13
    const-string v1, "ZONE"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;-><init>(Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->ZONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 22
    .line 23
    const-string v1, "NONE"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;-><init>(Ljava/lang/String;I)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->NONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 30
    .line 31
    invoke-static {}, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->a()[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->$VALUES:[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 36
    .line 37
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->$ENTRIES:Lkotlin/enums/a;

    .line 42
    .line 43
    new-instance v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType$a;

    .line 44
    .line 45
    invoke-direct {v0}, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType$a;-><init>()V

    .line 46
    .line 47
    .line 48
    sput-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->CREATOR:Landroid/os/Parcelable$Creator;

    .line 49
    .line 50
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;
    .locals 3

    .line 1
    const/4 v0, 0x3

    new-array v0, v0, [Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    sget-object v1, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->VIDEO:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->ZONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->NONE:Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;->$VALUES:[Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/ui_common/game_broadcating/models/GameBroadcastType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final describeContents()I
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public final writeToParcel(Landroid/os/Parcel;I)V
    .locals 0
    .param p1    # Landroid/os/Parcel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    invoke-virtual {p0}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    return-void
.end method
