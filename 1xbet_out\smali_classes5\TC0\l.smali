.class public final LTC0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LDH0/a;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LLD0/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LLD0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LTC0/l;->a:LBc/a;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LBc/a;)LTC0/l;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LLD0/a;",
            ">;)",
            "LTC0/l;"
        }
    .end annotation

    .line 1
    new-instance v0, LTC0/l;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LTC0/l;-><init>(LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LLD0/a;)LDH0/a;
    .locals 1

    .line 1
    sget-object v0, LTC0/f;->a:LTC0/f$a;

    .line 2
    .line 3
    invoke-virtual {v0, p0}, LTC0/f$a;->f(LLD0/a;)LDH0/a;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-static {p0}, Ldagger/internal/g;->e(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, LDH0/a;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public b()LDH0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LTC0/l;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LLD0/a;

    .line 8
    .line 9
    invoke-static {v0}, LTC0/l;->c(LLD0/a;)LDH0/a;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LTC0/l;->b()LDH0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
