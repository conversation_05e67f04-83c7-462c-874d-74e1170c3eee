.class public final LTN0/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LL2/a;


# instance fields
.field public final a:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/lottie/LottieView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final c:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final e:Landroidx/recyclerview/widget/RecyclerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final g:Landroid/widget/FrameLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final h:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final i:Lorg/xbet/uikit/components/separator/Separator;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final j:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final k:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field

.field public final m:Landroidx/constraintlayout/widget/ConstraintLayout;
    .annotation build Landroidx/annotation/NonNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;Landroidx/recyclerview/widget/RecyclerView;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/FrameLayout;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Landroidx/constraintlayout/widget/ConstraintLayout;)V
    .locals 0
    .param p1    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit/components/lottie/LottieView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p3    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p5    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p7    # Landroid/widget/FrameLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/uikit/components/separator/Separator;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p11    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/uikit/components/shimmer/ShimmerView;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .param p13    # Landroidx/constraintlayout/widget/ConstraintLayout;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LTN0/m;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 5
    .line 6
    iput-object p2, p0, LTN0/m;->b:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 7
    .line 8
    iput-object p3, p0, LTN0/m;->c:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 9
    .line 10
    iput-object p4, p0, LTN0/m;->d:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 11
    .line 12
    iput-object p5, p0, LTN0/m;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 13
    .line 14
    iput-object p6, p0, LTN0/m;->f:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 15
    .line 16
    iput-object p7, p0, LTN0/m;->g:Landroid/widget/FrameLayout;

    .line 17
    .line 18
    iput-object p8, p0, LTN0/m;->h:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 19
    .line 20
    iput-object p9, p0, LTN0/m;->i:Lorg/xbet/uikit/components/separator/Separator;

    .line 21
    .line 22
    iput-object p10, p0, LTN0/m;->j:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 23
    .line 24
    iput-object p11, p0, LTN0/m;->k:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 25
    .line 26
    iput-object p12, p0, LTN0/m;->l:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 27
    .line 28
    iput-object p13, p0, LTN0/m;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 29
    .line 30
    return-void
.end method

.method public static a(Landroid/view/View;)LTN0/m;
    .locals 17
    .param p0    # Landroid/view/View;
        .annotation build Landroidx/annotation/NonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    sget v1, LRN0/a;->emptyView:I

    .line 4
    .line 5
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    move-object v5, v2

    .line 10
    check-cast v5, Lorg/xbet/uikit/components/lottie/LottieView;

    .line 11
    .line 12
    if-eqz v5, :cond_0

    .line 13
    .line 14
    move-object v4, v0

    .line 15
    check-cast v4, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 16
    .line 17
    sget v1, LRN0/a;->navigationBar:I

    .line 18
    .line 19
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    move-object v7, v2

    .line 24
    check-cast v7, Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 25
    .line 26
    if-eqz v7, :cond_0

    .line 27
    .line 28
    sget v1, LRN0/a;->rvContent:I

    .line 29
    .line 30
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    move-object v8, v2

    .line 35
    check-cast v8, Landroidx/recyclerview/widget/RecyclerView;

    .line 36
    .line 37
    if-eqz v8, :cond_0

    .line 38
    .line 39
    sget v1, LRN0/a;->segmentedGroup:I

    .line 40
    .line 41
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    move-object v9, v2

    .line 46
    check-cast v9, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 47
    .line 48
    if-eqz v9, :cond_0

    .line 49
    .line 50
    sget v1, LRN0/a;->segmentedGroupContainer:I

    .line 51
    .line 52
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 53
    .line 54
    .line 55
    move-result-object v2

    .line 56
    move-object v10, v2

    .line 57
    check-cast v10, Landroid/widget/FrameLayout;

    .line 58
    .line 59
    if-eqz v10, :cond_0

    .line 60
    .line 61
    sget v1, LRN0/a;->segmentsShimmer:I

    .line 62
    .line 63
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    move-object v11, v2

    .line 68
    check-cast v11, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 69
    .line 70
    if-eqz v11, :cond_0

    .line 71
    .line 72
    sget v1, LRN0/a;->shadow:I

    .line 73
    .line 74
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 75
    .line 76
    .line 77
    move-result-object v2

    .line 78
    move-object v12, v2

    .line 79
    check-cast v12, Lorg/xbet/uikit/components/separator/Separator;

    .line 80
    .line 81
    if-eqz v12, :cond_0

    .line 82
    .line 83
    sget v1, LRN0/a;->shimmerView1:I

    .line 84
    .line 85
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 86
    .line 87
    .line 88
    move-result-object v2

    .line 89
    move-object v13, v2

    .line 90
    check-cast v13, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 91
    .line 92
    if-eqz v13, :cond_0

    .line 93
    .line 94
    sget v1, LRN0/a;->shimmerView2:I

    .line 95
    .line 96
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    move-object v14, v2

    .line 101
    check-cast v14, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 102
    .line 103
    if-eqz v14, :cond_0

    .line 104
    .line 105
    sget v1, LRN0/a;->shimmerView3:I

    .line 106
    .line 107
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    move-object v15, v2

    .line 112
    check-cast v15, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 113
    .line 114
    if-eqz v15, :cond_0

    .line 115
    .line 116
    sget v1, LRN0/a;->shimmers:I

    .line 117
    .line 118
    invoke-static {v0, v1}, LL2/b;->a(Landroid/view/View;I)Landroid/view/View;

    .line 119
    .line 120
    .line 121
    move-result-object v2

    .line 122
    move-object/from16 v16, v2

    .line 123
    .line 124
    check-cast v16, Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 125
    .line 126
    if-eqz v16, :cond_0

    .line 127
    .line 128
    new-instance v3, LTN0/m;

    .line 129
    .line 130
    move-object v6, v4

    .line 131
    invoke-direct/range {v3 .. v16}, LTN0/m;-><init>(Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/lottie/LottieView;Landroidx/constraintlayout/widget/ConstraintLayout;Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;Landroidx/recyclerview/widget/RecyclerView;Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroid/widget/FrameLayout;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/separator/Separator;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Lorg/xbet/uikit/components/shimmer/ShimmerView;Landroidx/constraintlayout/widget/ConstraintLayout;)V

    .line 132
    .line 133
    .line 134
    return-object v3

    .line 135
    :cond_0
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    new-instance v1, Ljava/lang/NullPointerException;

    .line 144
    .line 145
    const-string v2, "Missing required view with ID: "

    .line 146
    .line 147
    invoke-virtual {v2, v0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v0

    .line 151
    invoke-direct {v1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 152
    .line 153
    .line 154
    throw v1
.end method


# virtual methods
.method public b()Landroidx/constraintlayout/widget/ConstraintLayout;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    iget-object v0, p0, LTN0/m;->a:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic getRoot()Landroid/view/View;
    .locals 1
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LTN0/m;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
