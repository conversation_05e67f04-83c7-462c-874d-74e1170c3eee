.class public final LRE0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRE0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LRE0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LRE0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LRE0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;JLc8/h;)LRE0/c;
    .locals 13

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    new-instance v1, LRE0/a$b;

    .line 36
    .line 37
    invoke-static/range {p9 .. p10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 38
    .line 39
    .line 40
    move-result-object v10

    .line 41
    const/4 v12, 0x0

    .line 42
    move-object v2, p1

    .line 43
    move-object v3, p2

    .line 44
    move-object/from16 v4, p3

    .line 45
    .line 46
    move-object/from16 v5, p4

    .line 47
    .line 48
    move-object/from16 v6, p5

    .line 49
    .line 50
    move-object/from16 v7, p6

    .line 51
    .line 52
    move-object/from16 v8, p7

    .line 53
    .line 54
    move-object/from16 v9, p8

    .line 55
    .line 56
    move-object/from16 v11, p11

    .line 57
    .line 58
    invoke-direct/range {v1 .. v12}, LRE0/a$b;-><init>(LQW0/c;LwX0/c;Lorg/xbet/ui_common/utils/M;Lf8/g;LOE0/a;LSX0/a;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;Ljava/lang/Long;Lc8/h;LRE0/b;)V

    .line 59
    .line 60
    .line 61
    return-object v1
.end method
