.class final Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.tournament.presentation.TournamentViewModel$onTeamClicked$2"
    f = "TournamentViewModel.kt"
    l = {
        0x1db
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->c(I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $teamClId:I

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    iput p2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->$teamClId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->$teamClId:I

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;-><init>(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->P3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)Lorg/xbet/special_event/impl/teams/domain/usecase/d;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iget-object v1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 34
    .line 35
    invoke-static {v1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-virtual {v1}, LZx0/h;->d()I

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    iput v2, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->label:I

    .line 44
    .line 45
    invoke-virtual {p1, v1, p0}, Lorg/xbet/special_event/impl/teams/domain/usecase/d;->a(ILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    if-ne p1, v0, :cond_2

    .line 50
    .line 51
    return-object v0

    .line 52
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Iterable;

    .line 53
    .line 54
    iget v0, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->$teamClId:I

    .line 55
    .line 56
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 61
    .line 62
    .line 63
    move-result v1

    .line 64
    if-eqz v1, :cond_4

    .line 65
    .line 66
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    move-object v2, v1

    .line 71
    check-cast v2, Ltw0/a;

    .line 72
    .line 73
    invoke-virtual {v2}, Ltw0/a;->c()I

    .line 74
    .line 75
    .line 76
    move-result v2

    .line 77
    if-ne v2, v0, :cond_3

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_4
    const/4 v1, 0x0

    .line 81
    :goto_1
    check-cast v1, Ltw0/a;

    .line 82
    .line 83
    if-eqz v1, :cond_5

    .line 84
    .line 85
    iget-object p1, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->this$0:Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;

    .line 86
    .line 87
    iget v4, p0, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel$onTeamClicked$2;->$teamClId:I

    .line 88
    .line 89
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->Y3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LPx0/a;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    invoke-virtual {v2}, LZx0/h;->d()I

    .line 98
    .line 99
    .line 100
    move-result v2

    .line 101
    invoke-virtual {v0, v2, v4}, LPx0/a;->e(II)V

    .line 102
    .line 103
    .line 104
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->F3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LWo0/a;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    new-instance v2, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$ParticipantGames;

    .line 109
    .line 110
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 111
    .line 112
    .line 113
    move-result-object v3

    .line 114
    invoke-virtual {v3}, LZx0/h;->d()I

    .line 115
    .line 116
    .line 117
    move-result v3

    .line 118
    invoke-virtual {v1}, Ltw0/a;->d()Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object v5

    .line 122
    invoke-virtual {v1}, Ltw0/a;->a()Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v6

    .line 126
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->a4(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LZx0/h;

    .line 127
    .line 128
    .line 129
    move-result-object v1

    .line 130
    invoke-virtual {v1}, LZx0/h;->i()J

    .line 131
    .line 132
    .line 133
    move-result-wide v7

    .line 134
    invoke-direct/range {v2 .. v8}, Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams$ParticipantGames;-><init>(IILjava/lang/String;Ljava/lang/String;J)V

    .line 135
    .line 136
    .line 137
    invoke-interface {v0, v2}, LWo0/a;->a(Lorg/xbet/special_event/impl/alleventsgames/presentation/AllEventGamesScreenParams;)Lq4/q;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    invoke-static {p1}, Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;->X3(Lorg/xbet/special_event/impl/tournament/presentation/TournamentViewModel;)LwX0/c;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 146
    .line 147
    .line 148
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 149
    .line 150
    return-object p1
.end method
