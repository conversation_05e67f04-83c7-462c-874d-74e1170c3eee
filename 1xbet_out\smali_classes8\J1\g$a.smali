.class public final LJ1/g$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LJ1/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# virtual methods
.method public a()LJ1/g;
    .locals 0

    const/4 p0, 0x0

    throw p0
.end method

.method public b(Ljava/lang/String;)LJ1/g$a;
    .locals 0
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    const/4 p0, 0x0

    throw p0
.end method
