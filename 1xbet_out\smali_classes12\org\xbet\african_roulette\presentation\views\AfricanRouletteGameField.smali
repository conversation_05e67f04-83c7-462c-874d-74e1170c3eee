.class public final Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0010 \n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ#\u0010\u0010\u001a\u00020\u000c2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000c0\nH\u0000\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\u000c2\u0006\u0010\u0011\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u000cH\u0000\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u001d\u0010\u0018\u001a\u00020\u000c2\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u000b0\u0016H\u0000\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0015R\u0014\u0010\u001e\u001a\u00020\u001b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u001a\u0010#\u001a\u0008\u0012\u0004\u0012\u00020 0\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"\u00a8\u0006$"
    }
    d2 = {
        "Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lkotlin/Function1;",
        "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
        "",
        "action",
        "setCellClickListeners$african_roulette_release",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setCellClickListeners",
        "bet",
        "u",
        "(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V",
        "t",
        "()V",
        "",
        "betsList",
        "v",
        "(Ljava/util/List;)V",
        "s",
        "Lgg/c;",
        "a",
        "Lgg/c;",
        "viewBinding",
        "",
        "Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;",
        "b",
        "Ljava/util/List;",
        "cellList",
        "african_roulette_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lgg/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    const/4 p2, 0x1

    invoke-static {p1, p0, p2}, Lgg/c;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)Lgg/c;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->a:Lgg/c;

    .line 6
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    iput-object p2, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 7
    new-instance p2, Landroid/view/ViewGroup$LayoutParams;

    const/4 p3, -0x1

    invoke-direct {p2, p3, p3}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {p0, p2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 8
    iget-object p2, p1, Lgg/c;->b:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ZERO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 9
    iget-object p2, p1, Lgg/c;->c:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ONE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 10
    iget-object p2, p1, Lgg/c;->h:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWO:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 11
    iget-object p2, p1, Lgg/c;->i:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->THREE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 12
    iget-object p2, p1, Lgg/c;->j:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FOUR:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 13
    iget-object p2, p1, Lgg/c;->k:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 14
    iget-object p2, p1, Lgg/c;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SIX:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 15
    iget-object p2, p1, Lgg/c;->m:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->SEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 16
    iget-object p2, p1, Lgg/c;->o:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->EIGHT:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 17
    iget-object p2, p1, Lgg/c;->p:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->NINE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 18
    iget-object p2, p1, Lgg/c;->d:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 19
    iget-object p2, p1, Lgg/c;->e:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->ELEVEN:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 20
    iget-object p2, p1, Lgg/c;->f:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->TWELVE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 21
    iget-object p2, p1, Lgg/c;->g:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->FIRST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 22
    iget-object p2, p1, Lgg/c;->n:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LAST_HALF:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 23
    iget-object p2, p1, Lgg/c;->s:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->LOW:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 24
    iget-object p2, p1, Lgg/c;->t:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->MIDDLE:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 25
    iget-object p2, p1, Lgg/c;->r:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->HIGH:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 26
    iget-object p2, p1, Lgg/c;->u:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p3, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->RED:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 27
    iget-object p1, p1, Lgg/c;->q:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    sget-object p2, Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;->BLACK:Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    invoke-virtual {p1, p2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->e(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V

    .line 28
    invoke-virtual {p0}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->s()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final s()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->a:Lgg/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 4
    .line 5
    iget-object v2, v0, Lgg/c;->b:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 6
    .line 7
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 11
    .line 12
    iget-object v2, v0, Lgg/c;->c:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 13
    .line 14
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 18
    .line 19
    iget-object v2, v0, Lgg/c;->h:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 20
    .line 21
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 25
    .line 26
    iget-object v2, v0, Lgg/c;->i:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 27
    .line 28
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 32
    .line 33
    iget-object v2, v0, Lgg/c;->j:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 34
    .line 35
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 39
    .line 40
    iget-object v2, v0, Lgg/c;->k:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 41
    .line 42
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 46
    .line 47
    iget-object v2, v0, Lgg/c;->l:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 48
    .line 49
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 53
    .line 54
    iget-object v2, v0, Lgg/c;->m:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 55
    .line 56
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 57
    .line 58
    .line 59
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 60
    .line 61
    iget-object v2, v0, Lgg/c;->o:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 62
    .line 63
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 64
    .line 65
    .line 66
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 67
    .line 68
    iget-object v2, v0, Lgg/c;->p:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 69
    .line 70
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 71
    .line 72
    .line 73
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 74
    .line 75
    iget-object v2, v0, Lgg/c;->d:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 76
    .line 77
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 78
    .line 79
    .line 80
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 81
    .line 82
    iget-object v2, v0, Lgg/c;->e:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 83
    .line 84
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 88
    .line 89
    iget-object v2, v0, Lgg/c;->f:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 90
    .line 91
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 92
    .line 93
    .line 94
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 95
    .line 96
    iget-object v2, v0, Lgg/c;->g:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 97
    .line 98
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 99
    .line 100
    .line 101
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 102
    .line 103
    iget-object v2, v0, Lgg/c;->n:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 104
    .line 105
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 106
    .line 107
    .line 108
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 109
    .line 110
    iget-object v2, v0, Lgg/c;->s:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 111
    .line 112
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 113
    .line 114
    .line 115
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 116
    .line 117
    iget-object v2, v0, Lgg/c;->t:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 118
    .line 119
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 120
    .line 121
    .line 122
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 123
    .line 124
    iget-object v2, v0, Lgg/c;->r:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 125
    .line 126
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 127
    .line 128
    .line 129
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 130
    .line 131
    iget-object v2, v0, Lgg/c;->u:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 132
    .line 133
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    iget-object v1, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 137
    .line 138
    iget-object v0, v0, Lgg/c;->q:Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 139
    .line 140
    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 141
    .line 142
    .line 143
    return-void
.end method

.method public final setCellClickListeners$african_roulette_release(Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .param p1    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 18
    .line 19
    invoke-virtual {v1, p1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->setCellClickListener$african_roulette_release(Lkotlin/jvm/functions/Function1;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final t()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 18
    .line 19
    const/4 v2, 0x1

    .line 20
    invoke-virtual {v1, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->i(Z)V

    .line 21
    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    return-void
.end method

.method public final u(Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;)V
    .locals 3
    .param p1    # Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 18
    .line 19
    invoke-virtual {v1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->getBetType$african_roulette_release()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    if-ne v2, p1, :cond_0

    .line 24
    .line 25
    const/4 v2, 0x1

    .line 26
    invoke-virtual {v1, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->i(Z)V

    .line 27
    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const/4 v2, 0x0

    .line 31
    invoke-virtual {v1, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->i(Z)V

    .line 32
    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_1
    return-void
.end method

.method public final v(Ljava/util/List;)V
    .locals 3
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteGameField;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_1

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;

    .line 18
    .line 19
    invoke-virtual {v1}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->getBetType$african_roulette_release()Lorg/xbet/african_roulette/domain/models/AfricanRouletteBetType;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-interface {p1, v2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    if-eqz v2, :cond_0

    .line 28
    .line 29
    const/4 v2, 0x1

    .line 30
    invoke-virtual {v1, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->h(Z)V

    .line 31
    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    const/4 v2, 0x0

    .line 35
    invoke-virtual {v1, v2}, Lorg/xbet/african_roulette/presentation/views/AfricanRouletteCell;->h(Z)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_1
    return-void
.end method
