.class public final Lu01/j;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\r\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0008\u0003\n\u0002\u0008\u0005*\u0002\u0019\u001c\u0008\u0007\u0018\u00002\u00020\u0001B+\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00080\u0006\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\r\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\r\u0010\u000f\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000f\u0010\u000eJ\u000f\u0010\u0010\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014R \u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00080\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R$\u0010\u0018\u001a\u0012\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u000c0\u0006j\u0002`\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u0016R\u0014\u0010\u001b\u001a\u00020\u00198\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u001aR\u0014\u0010\u001f\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001e\u00a8\u0006 "
    }
    d2 = {
        "Lu01/j;",
        "",
        "Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;",
        "segmentedGroup",
        "Landroidx/viewpager2/widget/ViewPager2;",
        "viewPager",
        "Lkotlin/Function1;",
        "",
        "",
        "onSegmentConfigure",
        "<init>",
        "(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroidx/viewpager2/widget/ViewPager2;Lkotlin/jvm/functions/Function1;)V",
        "",
        "d",
        "()V",
        "e",
        "g",
        "a",
        "Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;",
        "b",
        "Landroidx/viewpager2/widget/ViewPager2;",
        "c",
        "Lkotlin/jvm/functions/Function1;",
        "Lorg/xbet/uikit/components/segmentedcontrol/OnSegmentSelectedListener;",
        "onSegmentChanged",
        "u01/j$b",
        "Lu01/j$b;",
        "onPageChangeCallback",
        "u01/j$a",
        "f",
        "Lu01/j$a;",
        "adapterDataObserver",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroidx/viewpager2/widget/ViewPager2;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Integer;",
            "Ljava/lang/CharSequence;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lu01/j$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lu01/j$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroidx/viewpager2/widget/ViewPager2;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/viewpager2/widget/ViewPager2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;",
            "Landroidx/viewpager2/widget/ViewPager2;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Integer;",
            "+",
            "Ljava/lang/CharSequence;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 5
    .line 6
    iput-object p2, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 7
    .line 8
    iput-object p3, p0, Lu01/j;->c:Lkotlin/jvm/functions/Function1;

    .line 9
    .line 10
    new-instance p1, Lu01/i;

    .line 11
    .line 12
    invoke-direct {p1, p0}, Lu01/i;-><init>(Lu01/j;)V

    .line 13
    .line 14
    .line 15
    iput-object p1, p0, Lu01/j;->d:Lkotlin/jvm/functions/Function1;

    .line 16
    .line 17
    new-instance p1, Lu01/j$b;

    .line 18
    .line 19
    invoke-direct {p1, p0}, Lu01/j$b;-><init>(Lu01/j;)V

    .line 20
    .line 21
    .line 22
    iput-object p1, p0, Lu01/j;->e:Lu01/j$b;

    .line 23
    .line 24
    new-instance p1, Lu01/j$a;

    .line 25
    .line 26
    invoke-direct {p1, p0}, Lu01/j$a;-><init>(Lu01/j;)V

    .line 27
    .line 28
    .line 29
    iput-object p1, p0, Lu01/j;->f:Lu01/j$a;

    .line 30
    .line 31
    return-void
.end method

.method public static synthetic a(Lu01/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lu01/j;->f(Lu01/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic b(Lu01/j;)Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;
    .locals 0

    .line 1
    iget-object p0, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lu01/j;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lu01/j;->g()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final f(Lu01/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(I)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public final d()V
    .locals 4

    .line 1
    iget-object v0, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 2
    .line 3
    iget-object v1, p0, Lu01/j;->d:Lkotlin/jvm/functions/Function1;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    const/4 v3, 0x0

    .line 7
    invoke-static {v0, v3, v1, v2, v3}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 11
    .line 12
    iget-object v1, p0, Lu01/j;->e:Lu01/j$b;

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 18
    .line 19
    invoke-virtual {v0}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    if-eqz v0, :cond_0

    .line 24
    .line 25
    iget-object v1, p0, Lu01/j;->f:Lu01/j$a;

    .line 26
    .line 27
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 28
    .line 29
    .line 30
    :cond_0
    invoke-virtual {p0}, Lu01/j;->g()V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final e()V
    .locals 3

    .line 1
    iget-object v0, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v1, v1, v2, v1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 9
    .line 10
    iget-object v1, p0, Lu01/j;->e:Lu01/j$b;

    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->o(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object v1, p0, Lu01/j;->f:Lu01/j$a;

    .line 24
    .line 25
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 26
    .line 27
    .line 28
    :cond_0
    return-void
.end method

.method public final g()V
    .locals 9

    .line 1
    iget-object v0, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->r()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 7
    .line 8
    invoke-virtual {v0}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    const/4 v1, 0x0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    const/4 v0, 0x0

    .line 21
    :goto_0
    const/4 v2, 0x0

    .line 22
    :goto_1
    if-ge v2, v0, :cond_2

    .line 23
    .line 24
    iget-object v3, p0, Lu01/j;->a:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 25
    .line 26
    new-instance v4, Lu01/a;

    .line 27
    .line 28
    invoke-direct {v4}, Lu01/a;-><init>()V

    .line 29
    .line 30
    .line 31
    iget-object v5, p0, Lu01/j;->c:Lkotlin/jvm/functions/Function1;

    .line 32
    .line 33
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 34
    .line 35
    .line 36
    move-result-object v6

    .line 37
    invoke-interface {v5, v6}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v5

    .line 41
    check-cast v5, Ljava/lang/CharSequence;

    .line 42
    .line 43
    invoke-virtual {v4, v5}, Lu01/a;->d(Ljava/lang/CharSequence;)V

    .line 44
    .line 45
    .line 46
    iget-object v5, p0, Lu01/j;->b:Landroidx/viewpager2/widget/ViewPager2;

    .line 47
    .line 48
    invoke-virtual {v5}, Landroidx/viewpager2/widget/ViewPager2;->getCurrentItem()I

    .line 49
    .line 50
    .line 51
    move-result v5

    .line 52
    if-ne v2, v5, :cond_1

    .line 53
    .line 54
    const/4 v5, 0x1

    .line 55
    const/4 v6, 0x1

    .line 56
    goto :goto_2

    .line 57
    :cond_1
    const/4 v6, 0x0

    .line 58
    :goto_2
    const/4 v7, 0x2

    .line 59
    const/4 v8, 0x0

    .line 60
    const/4 v5, 0x0

    .line 61
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->h(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lu01/a;IZILjava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    add-int/lit8 v2, v2, 0x1

    .line 65
    .line 66
    goto :goto_1

    .line 67
    :cond_2
    return-void
.end method
