.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info.TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1"
    f = "TournamentsFullInfoSharedViewModel.kt"
    l = {
        0xe3
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;-><init>(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
        "Lkb1/E;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkb1/F<",
        "+",
        "Lkb1/n;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
        "fullInfoState",
        "Lkb1/E;",
        "error",
        "Lkb1/F;",
        "Lkb1/n;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;)Lkb1/F;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    check-cast p2, Lkb1/E;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;",
            "Lkb1/E;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkb1/F<",
            "Lkb1/n;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 25

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$4:Ljava/lang/Object;

    .line 15
    .line 16
    check-cast v0, Li81/a;

    .line 17
    .line 18
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$3:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 21
    .line 22
    iget-object v3, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$2:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 25
    .line 26
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 29
    .line 30
    iget-object v5, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v5, Lkb1/E;

    .line 33
    .line 34
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 35
    .line 36
    .line 37
    move-object v12, v4

    .line 38
    move-object v4, v0

    .line 39
    move-object v0, v3

    .line 40
    move-object/from16 v3, p1

    .line 41
    .line 42
    :goto_0
    move-object v13, v5

    .line 43
    goto :goto_1

    .line 44
    :catchall_0
    move-exception v0

    .line 45
    goto/16 :goto_5

    .line 46
    .line 47
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 48
    .line 49
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 50
    .line 51
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    throw v0

    .line 55
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c;

    .line 61
    .line 62
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 63
    .line 64
    move-object v5, v4

    .line 65
    check-cast v5, Lkb1/E;

    .line 66
    .line 67
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 68
    .line 69
    instance-of v6, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 70
    .line 71
    if-eqz v6, :cond_8

    .line 72
    .line 73
    :try_start_1
    sget-object v6, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 74
    .line 75
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;

    .line 76
    .line 77
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$a;->a()Li81/a;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lgk/b;

    .line 82
    .line 83
    .line 84
    move-result-object v6

    .line 85
    invoke-virtual {v2}, Li81/a;->d()Lj81/a;

    .line 86
    .line 87
    .line 88
    move-result-object v7

    .line 89
    invoke-virtual {v7}, Lj81/a;->b()I

    .line 90
    .line 91
    .line 92
    move-result v7

    .line 93
    int-to-long v7, v7

    .line 94
    iput-object v5, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 95
    .line 96
    iput-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 97
    .line 98
    iput-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$2:Ljava/lang/Object;

    .line 99
    .line 100
    iput-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$3:Ljava/lang/Object;

    .line 101
    .line 102
    iput-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->L$4:Ljava/lang/Object;

    .line 103
    .line 104
    iput v3, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$tournamentMainInfoState$1;->label:I

    .line 105
    .line 106
    invoke-interface {v6, v7, v8, v1}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 110
    if-ne v3, v0, :cond_2

    .line 111
    .line 112
    return-object v0

    .line 113
    :cond_2
    move-object v0, v4

    .line 114
    move-object v12, v0

    .line 115
    move-object v4, v2

    .line 116
    move-object v2, v12

    .line 117
    goto :goto_0

    .line 118
    :goto_1
    :try_start_2
    check-cast v3, Lbk/a;

    .line 119
    .line 120
    invoke-virtual {v3}, Lbk/a;->o()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v5

    .line 124
    invoke-virtual {v4}, Li81/a;->e()Lk81/a;

    .line 125
    .line 126
    .line 127
    move-result-object v3

    .line 128
    invoke-virtual {v4}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 129
    .line 130
    .line 131
    move-result-object v6

    .line 132
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 133
    .line 134
    .line 135
    move-result-object v7

    .line 136
    invoke-static {v3, v6, v5, v7}, Ljb1/w;->f(Lk81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;

    .line 137
    .line 138
    .line 139
    move-result-object v9

    .line 140
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 141
    .line 142
    .line 143
    move-result-object v3

    .line 144
    invoke-static {v4, v3}, Ljb1/B;->g(Li81/a;LHX0/e;)Ljava/util/List;

    .line 145
    .line 146
    .line 147
    move-result-object v3

    .line 148
    invoke-virtual {v4}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 149
    .line 150
    .line 151
    move-result-object v6

    .line 152
    invoke-virtual {v4}, Li81/a;->p()Z

    .line 153
    .line 154
    .line 155
    move-result v7

    .line 156
    invoke-virtual {v4}, Li81/a;->d()Lj81/a;

    .line 157
    .line 158
    .line 159
    move-result-object v8

    .line 160
    invoke-virtual {v8}, Lj81/a;->i()Lorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;

    .line 161
    .line 162
    .line 163
    move-result-object v8

    .line 164
    invoke-static {v3, v6, v7, v8}, Ljb1/s;->g(Ljava/util/List;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;ZLorg/xplatform/aggregator/api/model/tournaments/header/TournamentStatus;)Ljava/util/List;

    .line 165
    .line 166
    .line 167
    move-result-object v6

    .line 168
    new-instance v8, Lkotlin/Pair;

    .line 169
    .line 170
    invoke-interface {v3}, Ljava/util/List;->size()I

    .line 171
    .line 172
    .line 173
    move-result v3

    .line 174
    invoke-static {v3}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 175
    .line 176
    .line 177
    move-result-object v3

    .line 178
    invoke-direct {v8, v6, v3}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 179
    .line 180
    .line 181
    invoke-virtual {v4}, Li81/a;->m()Ljava/util/List;

    .line 182
    .line 183
    .line 184
    move-result-object v3

    .line 185
    new-instance v6, Ljava/util/ArrayList;

    .line 186
    .line 187
    const/16 v7, 0xa

    .line 188
    .line 189
    invoke-static {v3, v7}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 190
    .line 191
    .line 192
    move-result v7

    .line 193
    invoke-direct {v6, v7}, Ljava/util/ArrayList;-><init>(I)V

    .line 194
    .line 195
    .line 196
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 197
    .line 198
    .line 199
    move-result-object v3

    .line 200
    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 201
    .line 202
    .line 203
    move-result v7

    .line 204
    if-eqz v7, :cond_3

    .line 205
    .line 206
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 207
    .line 208
    .line 209
    move-result-object v7

    .line 210
    check-cast v7, Lorg/xplatform/aggregator/api/model/Game;

    .line 211
    .line 212
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Ljava/util/Map;

    .line 213
    .line 214
    .line 215
    move-result-object v10

    .line 216
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 217
    .line 218
    .line 219
    move-result-wide v14

    .line 220
    invoke-static {v14, v15}, LHc/a;->f(J)Ljava/lang/Long;

    .line 221
    .line 222
    .line 223
    move-result-object v11

    .line 224
    invoke-interface {v10, v11, v7}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    sget-object v7, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 228
    .line 229
    invoke-interface {v6, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 230
    .line 231
    .line 232
    goto :goto_2

    .line 233
    :catchall_1
    move-exception v0

    .line 234
    move-object v4, v12

    .line 235
    goto :goto_5

    .line 236
    :cond_3
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 237
    .line 238
    .line 239
    move-result-object v6

    .line 240
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LHX0/e;

    .line 241
    .line 242
    .line 243
    move-result-object v3

    .line 244
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->l4()Z

    .line 245
    .line 246
    .line 247
    move-result v7

    .line 248
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->M3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lek0/o;

    .line 249
    .line 250
    .line 251
    move-result-object v10

    .line 252
    invoke-virtual {v10}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 253
    .line 254
    .line 255
    move-result-object v10

    .line 256
    invoke-static {v4, v3, v7, v10}, Ljb1/r;->h(Li81/a;LHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)Lkb1/m;

    .line 257
    .line 258
    .line 259
    move-result-object v7

    .line 260
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->M3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lek0/o;

    .line 261
    .line 262
    .line 263
    move-result-object v10

    .line 264
    const/4 v11, 0x1

    .line 265
    invoke-static/range {v4 .. v11}, Ljb1/v;->t(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;Lkotlin/Pair;Ljava/util/List;Lek0/o;Z)Lkb1/n;

    .line 266
    .line 267
    .line 268
    move-result-object v0

    .line 269
    instance-of v3, v13, Lkb1/E$c;

    .line 270
    .line 271
    if-eqz v3, :cond_4

    .line 272
    .line 273
    new-instance v2, Lkb1/F$d;

    .line 274
    .line 275
    invoke-direct {v2, v0}, Lkb1/F$d;-><init>(Ljava/lang/Object;)V

    .line 276
    .line 277
    .line 278
    goto :goto_4

    .line 279
    :cond_4
    instance-of v0, v13, Lkb1/E$b;

    .line 280
    .line 281
    if-eqz v0, :cond_5

    .line 282
    .line 283
    new-instance v0, Lkb1/F$b;

    .line 284
    .line 285
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 286
    .line 287
    .line 288
    move-result-object v2

    .line 289
    invoke-direct {v0, v2}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 290
    .line 291
    .line 292
    :goto_3
    move-object v2, v0

    .line 293
    goto :goto_4

    .line 294
    :cond_5
    instance-of v0, v13, Lkb1/E$a;

    .line 295
    .line 296
    if-eqz v0, :cond_6

    .line 297
    .line 298
    new-instance v0, Lkb1/F$a;

    .line 299
    .line 300
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 301
    .line 302
    .line 303
    move-result-object v2

    .line 304
    invoke-direct {v0, v2}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 305
    .line 306
    .line 307
    goto :goto_3

    .line 308
    :goto_4
    invoke-static {v2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 309
    .line 310
    .line 311
    move-result-object v0

    .line 312
    goto :goto_6

    .line 313
    :cond_6
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 314
    .line 315
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 316
    .line 317
    .line 318
    throw v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 319
    :goto_5
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 320
    .line 321
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 322
    .line 323
    .line 324
    move-result-object v0

    .line 325
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 326
    .line 327
    .line 328
    move-result-object v0

    .line 329
    move-object v12, v4

    .line 330
    :goto_6
    invoke-static {v0}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 331
    .line 332
    .line 333
    move-result-object v2

    .line 334
    if-nez v2, :cond_7

    .line 335
    .line 336
    goto :goto_7

    .line 337
    :cond_7
    invoke-static {v12, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->W3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;Ljava/lang/Throwable;)V

    .line 338
    .line 339
    .line 340
    new-instance v0, Lkb1/F$c;

    .line 341
    .line 342
    invoke-static {v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)LSX0/c;

    .line 343
    .line 344
    .line 345
    move-result-object v13

    .line 346
    sget-object v14, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 347
    .line 348
    sget v19, Lpb/k;->data_retrieval_error:I

    .line 349
    .line 350
    const/16 v23, 0x1de

    .line 351
    .line 352
    const/16 v24, 0x0

    .line 353
    .line 354
    const/4 v15, 0x0

    .line 355
    const/16 v16, 0x0

    .line 356
    .line 357
    const/16 v17, 0x0

    .line 358
    .line 359
    const/16 v18, 0x0

    .line 360
    .line 361
    const/16 v20, 0x0

    .line 362
    .line 363
    const/16 v21, 0x0

    .line 364
    .line 365
    const/16 v22, 0x0

    .line 366
    .line 367
    invoke-static/range {v13 .. v24}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 368
    .line 369
    .line 370
    move-result-object v2

    .line 371
    invoke-direct {v0, v2}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 372
    .line 373
    .line 374
    :goto_7
    check-cast v0, Lkb1/F;

    .line 375
    .line 376
    return-object v0

    .line 377
    :cond_8
    instance-of v0, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel$c$b;

    .line 378
    .line 379
    if-eqz v0, :cond_9

    .line 380
    .line 381
    instance-of v2, v5, Lkb1/E$a;

    .line 382
    .line 383
    if-eqz v2, :cond_9

    .line 384
    .line 385
    new-instance v0, Lkb1/F$a;

    .line 386
    .line 387
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 388
    .line 389
    .line 390
    move-result-object v2

    .line 391
    invoke-direct {v0, v2}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 392
    .line 393
    .line 394
    return-object v0

    .line 395
    :cond_9
    if-eqz v0, :cond_a

    .line 396
    .line 397
    instance-of v0, v5, Lkb1/E$b;

    .line 398
    .line 399
    if-eqz v0, :cond_a

    .line 400
    .line 401
    new-instance v0, Lkb1/F$b;

    .line 402
    .line 403
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 404
    .line 405
    .line 406
    move-result-object v2

    .line 407
    invoke-direct {v0, v2}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 408
    .line 409
    .line 410
    return-object v0

    .line 411
    :cond_a
    sget-object v0, Lkb1/F$e;->a:Lkb1/F$e;

    .line 412
    .line 413
    return-object v0
.end method
