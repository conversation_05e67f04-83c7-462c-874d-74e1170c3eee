.class public final Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;Lmg/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->o0:Lmg/b;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_not_identified_alert/NeedIdentificationDialog;->m0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
