.class public final Lorg/xbet/tile_matching/domain/usecases/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u0080B\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/tile_matching/domain/usecases/a;",
        "",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "addCommandScenario",
        "LAT0/a;",
        "tileMatchingRepository",
        "<init>",
        "(Lorg/xbet/core/domain/usecases/AddCommandScenario;LAT0/a;)V",
        "",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
        "b",
        "LAT0/a;",
        "tile_matching_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/core/domain/usecases/AddCommandScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LAT0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/core/domain/usecases/AddCommandScenario;LAT0/a;)V
    .locals 0
    .param p1    # Lorg/xbet/core/domain/usecases/AddCommandScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LAT0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/tile_matching/domain/usecases/a;->a:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/tile_matching/domain/usecases/a;->b:LAT0/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 14
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/domain/usecases/a;->b:LAT0/a;

    .line 2
    .line 3
    invoke-interface {v0}, LAT0/a;->c()LzT0/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/tile_matching/domain/usecases/a;->a:Lorg/xbet/core/domain/usecases/AddCommandScenario;

    .line 8
    .line 9
    new-instance v2, LTv/a$j;

    .line 10
    .line 11
    invoke-virtual {v0}, LzT0/e;->h()D

    .line 12
    .line 13
    .line 14
    move-result-wide v3

    .line 15
    sget-object v5, Lorg/xbet/core/domain/StatusBetEnum;->UNDEFINED:Lorg/xbet/core/domain/StatusBetEnum;

    .line 16
    .line 17
    invoke-virtual {v0}, LzT0/e;->c()D

    .line 18
    .line 19
    .line 20
    move-result-wide v7

    .line 21
    invoke-virtual {v0}, LzT0/e;->e()Lorg/xbet/games_section/api/models/GameBonus;

    .line 22
    .line 23
    .line 24
    move-result-object v6

    .line 25
    invoke-virtual {v6}, Lorg/xbet/games_section/api/models/GameBonus;->getBonusType()Lorg/xbet/games_section/api/models/GameBonusType;

    .line 26
    .line 27
    .line 28
    move-result-object v11

    .line 29
    invoke-virtual {v0}, LzT0/e;->a()J

    .line 30
    .line 31
    .line 32
    move-result-wide v12

    .line 33
    const/4 v6, 0x0

    .line 34
    const-wide/high16 v9, 0x3ff0000000000000L    # 1.0

    .line 35
    .line 36
    invoke-direct/range {v2 .. v13}, LTv/a$j;-><init>(DLorg/xbet/core/domain/StatusBetEnum;ZDDLorg/xbet/games_section/api/models/GameBonusType;J)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1, v2, p1}, Lorg/xbet/core/domain/usecases/AddCommandScenario;->l(LTv/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    if-ne p1, v0, :cond_0

    .line 48
    .line 49
    return-object p1

    .line 50
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 51
    .line 52
    return-object p1
.end method
