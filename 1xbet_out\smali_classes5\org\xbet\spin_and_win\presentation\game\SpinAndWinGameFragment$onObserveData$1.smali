.class final Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.spin_and_win.presentation.game.SpinAndWinGameFragment$onObserveData$1"
    f = "SpinAndWinGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/spin_and_win/presentation/game/s;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/game/s;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/spin_and_win/presentation/game/s;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;-><init>(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/s;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->invoke(Lorg/xbet/spin_and_win/presentation/game/s;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/spin_and_win/presentation/game/s;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/spin_and_win/presentation/game/s;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/s;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 26
    .line 27
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/s$a;

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/s$a;->a()Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_0

    .line 34
    .line 35
    const/4 p1, 0x0

    .line 36
    goto :goto_0

    .line 37
    :cond_0
    const/16 p1, 0x8

    .line 38
    .line 39
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    goto :goto_1

    .line 43
    :cond_1
    instance-of v0, p1, Lorg/xbet/spin_and_win/presentation/game/s$b;

    .line 44
    .line 45
    if-eqz v0, :cond_3

    .line 46
    .line 47
    check-cast p1, Lorg/xbet/spin_and_win/presentation/game/s$b;

    .line 48
    .line 49
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/s$b;->b()Z

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz v0, :cond_2

    .line 54
    .line 55
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 56
    .line 57
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 62
    .line 63
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/s$b;->a()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->j(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 68
    .line 69
    .line 70
    goto :goto_1

    .line 71
    :cond_2
    iget-object v0, p0, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment$onObserveData$1;->this$0:Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;

    .line 72
    .line 73
    invoke-static {v0}, Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;->F2(Lorg/xbet/spin_and_win/presentation/game/SpinAndWinGameFragment;)Lbz0/c;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iget-object v0, v0, Lbz0/c;->k:Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;

    .line 78
    .line 79
    invoke-virtual {p1}, Lorg/xbet/spin_and_win/presentation/game/s$b;->a()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/presentation/views/SpinAndWinWheelView;->h(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 84
    .line 85
    .line 86
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 87
    .line 88
    return-object p1

    .line 89
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 90
    .line 91
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 92
    .line 93
    .line 94
    throw p1

    .line 95
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 96
    .line 97
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 98
    .line 99
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 100
    .line 101
    .line 102
    throw p1
.end method
