.class public final LOA0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a#\u0010\u0005\u001a\u0004\u0018\u00010\u0004*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LYA0/a;",
        "",
        "LdB0/a;",
        "sportModelList",
        "LVA0/b;",
        "a",
        "(LYA0/a;Ljava/util/List;)LVA0/b;",
        "core_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LYA0/a;Ljava/util/List;)LVA0/b;
    .locals 18
    .param p0    # LYA0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LYA0/a;",
            "Ljava/util/List<",
            "LdB0/a;",
            ">;)",
            "LVA0/b;"
        }
    .end annotation

    .line 1
    invoke-virtual/range {p0 .. p0}, LYA0/a;->H()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x0

    .line 10
    if-lez v0, :cond_d

    .line 11
    .line 12
    invoke-virtual/range {p0 .. p0}, LYA0/a;->v()J

    .line 13
    .line 14
    .line 15
    move-result-wide v2

    .line 16
    const-wide/16 v4, 0x1

    .line 17
    .line 18
    cmp-long v0, v2, v4

    .line 19
    .line 20
    if-eqz v0, :cond_d

    .line 21
    .line 22
    invoke-static/range {p0 .. p0}, LMA0/b;->c(LYA0/a;)Lkotlin/Pair;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    move-object v9, v2

    .line 31
    check-cast v9, Ljava/lang/String;

    .line 32
    .line 33
    invoke-virtual {v0}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    move-object v12, v0

    .line 38
    check-cast v12, Ljava/lang/String;

    .line 39
    .line 40
    invoke-interface/range {p1 .. p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-eqz v2, :cond_1

    .line 49
    .line 50
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    move-object v3, v2

    .line 55
    check-cast v3, LdB0/a;

    .line 56
    .line 57
    invoke-virtual {v3}, LdB0/a;->a()J

    .line 58
    .line 59
    .line 60
    move-result-wide v3

    .line 61
    invoke-virtual/range {p0 .. p0}, LYA0/a;->v()J

    .line 62
    .line 63
    .line 64
    move-result-wide v5

    .line 65
    cmp-long v7, v3, v5

    .line 66
    .line 67
    if-nez v7, :cond_0

    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_1
    move-object v2, v1

    .line 71
    :goto_0
    check-cast v2, LdB0/a;

    .line 72
    .line 73
    if-eqz v2, :cond_2

    .line 74
    .line 75
    invoke-virtual {v2}, LdB0/a;->c()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    :cond_2
    const-string v0, ""

    .line 80
    .line 81
    if-nez v1, :cond_3

    .line 82
    .line 83
    move-object v4, v0

    .line 84
    goto :goto_1

    .line 85
    :cond_3
    move-object v4, v1

    .line 86
    :goto_1
    new-instance v3, LVA0/b;

    .line 87
    .line 88
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    sget-object v2, Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;->HOSTS_VS_GUESTS:Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 93
    .line 94
    const-wide/16 v5, 0x0

    .line 95
    .line 96
    if-ne v1, v2, :cond_5

    .line 97
    .line 98
    :cond_4
    move-wide v7, v5

    .line 99
    goto :goto_2

    .line 100
    :cond_5
    invoke-virtual/range {p0 .. p0}, LYA0/a;->F()Ljava/util/List;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    check-cast v1, Ljava/lang/Long;

    .line 109
    .line 110
    if-eqz v1, :cond_4

    .line 111
    .line 112
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 113
    .line 114
    .line 115
    move-result-wide v7

    .line 116
    :goto_2
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    if-ne v1, v2, :cond_6

    .line 121
    .line 122
    goto :goto_3

    .line 123
    :cond_6
    invoke-virtual/range {p0 .. p0}, LYA0/a;->I()Ljava/util/List;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object v1

    .line 131
    check-cast v1, Ljava/lang/Long;

    .line 132
    .line 133
    if-eqz v1, :cond_7

    .line 134
    .line 135
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 136
    .line 137
    .line 138
    move-result-wide v5

    .line 139
    :cond_7
    :goto_3
    invoke-virtual/range {p0 .. p0}, LYA0/a;->D()Ljava/util/List;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 144
    .line 145
    .line 146
    move-result-object v1

    .line 147
    check-cast v1, Ljava/lang/String;

    .line 148
    .line 149
    if-nez v1, :cond_8

    .line 150
    .line 151
    move-object v10, v0

    .line 152
    goto :goto_4

    .line 153
    :cond_8
    move-object v10, v1

    .line 154
    :goto_4
    invoke-virtual/range {p0 .. p0}, LYA0/a;->D()Ljava/util/List;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    const/4 v11, 0x1

    .line 159
    invoke-static {v1, v11}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 160
    .line 161
    .line 162
    move-result-object v1

    .line 163
    check-cast v1, Ljava/lang/String;

    .line 164
    .line 165
    if-nez v1, :cond_9

    .line 166
    .line 167
    move-object v1, v0

    .line 168
    :cond_9
    invoke-virtual/range {p0 .. p0}, LYA0/a;->G()Ljava/util/List;

    .line 169
    .line 170
    .line 171
    move-result-object v13

    .line 172
    invoke-static {v13}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object v13

    .line 176
    check-cast v13, Ljava/lang/String;

    .line 177
    .line 178
    if-nez v13, :cond_a

    .line 179
    .line 180
    move-object v13, v0

    .line 181
    :cond_a
    invoke-virtual/range {p0 .. p0}, LYA0/a;->G()Ljava/util/List;

    .line 182
    .line 183
    .line 184
    move-result-object v14

    .line 185
    invoke-static {v14, v11}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 186
    .line 187
    .line 188
    move-result-object v14

    .line 189
    check-cast v14, Ljava/lang/String;

    .line 190
    .line 191
    if-nez v14, :cond_b

    .line 192
    .line 193
    move-object v14, v0

    .line 194
    :cond_b
    invoke-virtual/range {p0 .. p0}, LYA0/a;->N()Lorg/xbet/sportgame/core/domain/models/gamedetails/GameDetailsType;

    .line 195
    .line 196
    .line 197
    move-result-object v0

    .line 198
    if-ne v0, v2, :cond_c

    .line 199
    .line 200
    const/4 v15, 0x1

    .line 201
    :goto_5
    move-wide/from16 v16, v7

    .line 202
    .line 203
    move-wide v7, v5

    .line 204
    move-wide/from16 v5, v16

    .line 205
    .line 206
    move-object v11, v1

    .line 207
    goto :goto_6

    .line 208
    :cond_c
    const/4 v11, 0x0

    .line 209
    const/4 v15, 0x0

    .line 210
    goto :goto_5

    .line 211
    :goto_6
    invoke-direct/range {v3 .. v15}, LVA0/b;-><init>(Ljava/lang/String;JJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V

    .line 212
    .line 213
    .line 214
    return-object v3

    .line 215
    :cond_d
    return-object v1
.end method
