.class public final LuY0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LuY0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static a(LuY0/a;I)V
    .locals 0
    .param p0    # LuY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, LIY0/b$a;->a(LIY0/b;I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static b(LuY0/a;FFFF)I
    .locals 0
    .param p0    # LuY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LIY0/b$a;->b(LIY0/b;FFFF)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method
