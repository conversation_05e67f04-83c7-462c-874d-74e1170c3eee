.class final Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;
.source "SourceFile"


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.core.domain.usecases.GetGamesForNonAuthScenarioImpl"
    f = "GetGamesForNonAuthScenarioImpl.kt"
    l = {
        0x34
    }
    m = "invoke-hUnOzRk"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->a(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->result:Ljava/lang/Object;

    iget p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->label:I

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl$invoke$1;->this$0:Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v6, p0

    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/core/domain/usecases/GetGamesForNonAuthScenarioImpl;->a(ZZIZZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    move-result-object v0

    if-ne p1, v0, :cond_0

    return-object p1

    :cond_0
    invoke-static {p1}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    move-result-object p1

    return-object p1
.end method
