.class public final Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0007\u0018\u0000 \u000c2\u00020\u0001:\u0001\rB\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0013\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Li8/l;",
        "getThemeStreamUseCase",
        "<init>",
        "(Li8/l;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "p3",
        "()Lkotlinx/coroutines/flow/e;",
        "v1",
        "Li8/l;",
        "x1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final x1:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final v1:Li8/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel;->x1:Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$a;

    return-void
.end method

.method public constructor <init>(Li8/l;)V
    .locals 0
    .param p1    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel;->v1:Li8/l;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final p3()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel;->v1:Li8/l;

    .line 2
    .line 3
    invoke-interface {v0}, Li8/l;->invoke()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$getUrlState$$inlined$map$1;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lorg/xbet/alerts_pipe_impl/presentation/kz_first_deposit_bottom_dialog/KzFirstDepositViewModel$getUrlState$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 10
    .line 11
    .line 12
    return-object v1
.end method
