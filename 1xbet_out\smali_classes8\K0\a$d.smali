.class public final LK0/a$d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LK0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation


# instance fields
.field public final a:LK0/a$e;


# direct methods
.method public constructor <init>(LK0/a$e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LK0/a$d;->a:LK0/a$e;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()LK0/a$e;
    .locals 1

    .line 1
    iget-object v0, p0, LK0/a$d;->a:LK0/a$e;

    .line 2
    .line 3
    return-object v0
.end method
