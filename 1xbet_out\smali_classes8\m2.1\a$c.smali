.class public final Lm2/a$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lm2/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation


# instance fields
.field public final a:I

.field public final b:Z

.field public final c:[B

.field public final d:[B


# direct methods
.method public constructor <init>(IZ[B[B)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lm2/a$c;->a:I

    .line 5
    .line 6
    iput-boolean p2, p0, Lm2/a$c;->b:Z

    .line 7
    .line 8
    iput-object p3, p0, Lm2/a$c;->c:[B

    .line 9
    .line 10
    iput-object p4, p0, Lm2/a$c;->d:[B

    .line 11
    .line 12
    return-void
.end method
