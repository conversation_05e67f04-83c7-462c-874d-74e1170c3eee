.class public final LQQ0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J \u0010\u000b\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0086\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LQQ0/a;",
        "",
        "LOQ0/a;",
        "tennisWinLossRepository",
        "<init>",
        "(LOQ0/a;)V",
        "Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;",
        "matchType",
        "",
        "season",
        "LPQ0/d;",
        "a",
        "(Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;I)LPQ0/d;",
        "LOQ0/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LOQ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LOQ0/a;)V
    .locals 0
    .param p1    # LOQ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LQQ0/a;->a:LOQ0/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;I)LPQ0/d;
    .locals 4
    .param p1    # Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LQQ0/a;->a:LOQ0/a;

    .line 2
    .line 3
    invoke-interface {v0}, LOQ0/a;->j()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_1

    .line 16
    .line 17
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    move-object v2, v1

    .line 22
    check-cast v2, LPQ0/d;

    .line 23
    .line 24
    invoke-virtual {v2}, LPQ0/d;->b()Lorg/xbet/statistic/tennis/impl/wins_and_losses/domain/models/MatchType;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    if-ne v3, p1, :cond_0

    .line 29
    .line 30
    invoke-virtual {v2}, LPQ0/d;->c()I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    if-ne v2, p2, :cond_0

    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_1
    const/4 v1, 0x0

    .line 38
    :goto_0
    check-cast v1, LPQ0/d;

    .line 39
    .line 40
    if-nez v1, :cond_2

    .line 41
    .line 42
    sget-object p1, LPQ0/d;->g:LPQ0/d$a;

    .line 43
    .line 44
    invoke-virtual {p1}, LPQ0/d$a;->a()LPQ0/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    return-object p1

    .line 49
    :cond_2
    return-object v1
.end method
