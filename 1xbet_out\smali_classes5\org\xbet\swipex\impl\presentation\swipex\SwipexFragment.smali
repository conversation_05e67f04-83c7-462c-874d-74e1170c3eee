.class public final Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ac\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 h2\u00020\u0001:\u0001iB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0019\u0010\u0007\u001a\u00020\u0006*\u0008\u0012\u0004\u0012\u00020\u00050\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u001f\u0010\u000b\u001a\u00020\u0006*\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u0004H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0008J\u0019\u0010\r\u001a\u00020\u0006*\u0008\u0012\u0004\u0012\u00020\u000c0\u0004H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u0008J\u000f\u0010\u000e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0003J\u000f\u0010\u000f\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0003J\u0017\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u0017\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u001f\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u000f\u0010\u001e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0003J\u0017\u0010\"\u001a\u00020!2\u0006\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008$\u0010\u0003J\u000f\u0010%\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008%\u0010\u0003J\u0019\u0010(\u001a\u00020\u00062\u0008\u0010\'\u001a\u0004\u0018\u00010&H\u0016\u00a2\u0006\u0004\u0008(\u0010)J\u0019\u0010*\u001a\u00020\u00062\u0008\u0010\'\u001a\u0004\u0018\u00010&H\u0014\u00a2\u0006\u0004\u0008*\u0010)J\u000f\u0010+\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008+\u0010\u0003R\u001a\u00101\u001a\u00020,8\u0016X\u0096D\u00a2\u0006\u000c\n\u0004\u0008-\u0010.\u001a\u0004\u0008/\u00100R\"\u00109\u001a\u0002028\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00083\u00104\u001a\u0004\u00085\u00106\"\u0004\u00087\u00108R\"\u0010A\u001a\u00020:8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>\"\u0004\u0008?\u0010@R\u001b\u0010E\u001a\u00020,8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008B\u0010C\u001a\u0004\u0008D\u00100R\u001b\u0010J\u001a\u00020F8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008G\u0010C\u001a\u0004\u0008H\u0010IR\u001b\u0010O\u001a\u00020K8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008L\u0010C\u001a\u0004\u0008M\u0010NR\u001b\u0010U\u001a\u00020P8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008Q\u0010R\u001a\u0004\u0008S\u0010TR\u001b\u0010Z\u001a\u00020V8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008W\u0010C\u001a\u0004\u0008X\u0010YR\u001b\u0010_\u001a\u00020[8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\\\u0010C\u001a\u0004\u0008]\u0010^R\u0014\u0010c\u001a\u00020`8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010g\u001a\u00020d8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008e\u0010f\u00a8\u0006j"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;",
        "",
        "n3",
        "(Lkotlinx/coroutines/flow/e;)V",
        "",
        "LVX0/i;",
        "m3",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;",
        "o3",
        "w3",
        "j3",
        "LVS0/a;",
        "betSettings",
        "z3",
        "(LVS0/a;)V",
        "T2",
        "",
        "maxWidth",
        "U2",
        "(I)V",
        "Landroid/view/View;",
        "view1",
        "view2",
        "V2",
        "(Landroid/view/View;Landroid/view/View;)I",
        "l3",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;",
        "action",
        "",
        "f3",
        "(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;)Ljava/lang/String;",
        "i3",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onCreate",
        "(Landroid/os/Bundle;)V",
        "t2",
        "v2",
        "",
        "i0",
        "Z",
        "r2",
        "()Z",
        "showNavBar",
        "LTZ0/a;",
        "j0",
        "LTZ0/a;",
        "Y2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "k0",
        "LzX0/k;",
        "d3",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "l0",
        "Lkotlin/j;",
        "c3",
        "smallDevice",
        "LBS0/c;",
        "m0",
        "b3",
        "()LBS0/c;",
        "component",
        "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
        "n0",
        "h3",
        "()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
        "viewModel",
        "LwS0/q;",
        "o0",
        "LRc/c;",
        "g3",
        "()LwS0/q;",
        "viewBinding",
        "LQS0/b;",
        "b1",
        "Z2",
        "()LQS0/b;",
        "cardAdapter",
        "LQS0/a;",
        "k1",
        "e3",
        "()LQS0/a;",
        "sportsAdapter",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;",
        "v1",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;",
        "cardStackListener",
        "Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;",
        "a3",
        "()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;",
        "cardStackManager",
        "x1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final x1:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic y1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i0:Z

.field public j0:LTZ0/a;

.field public k0:LzX0/k;

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getViewBinding()Lorg/xbet/swipex/impl/databinding/SwipexFragmentBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 7
    .line 8
    const-string v4, "viewBinding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->y1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->x1:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$a;

    .line 31
    .line 32
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, LrS0/c;->swipex_fragment:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    iput-boolean v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->i0:Z

    .line 8
    .line 9
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/i;

    .line 10
    .line 11
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/i;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 12
    .line 13
    .line 14
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 15
    .line 16
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->l0:Lkotlin/j;

    .line 21
    .line 22
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/j;

    .line 23
    .line 24
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/j;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->m0:Lkotlin/j;

    .line 32
    .line 33
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/k;

    .line 34
    .line 35
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/k;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 36
    .line 37
    .line 38
    new-instance v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$1;

    .line 39
    .line 40
    invoke-direct {v2, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 41
    .line 42
    .line 43
    new-instance v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$2;

    .line 44
    .line 45
    invoke-direct {v3, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 46
    .line 47
    .line 48
    invoke-static {v1, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    const-class v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 53
    .line 54
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    new-instance v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$3;

    .line 59
    .line 60
    invoke-direct {v4, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 61
    .line 62
    .line 63
    new-instance v5, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$4;

    .line 64
    .line 65
    const/4 v6, 0x0

    .line 66
    invoke-direct {v5, v6, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 67
    .line 68
    .line 69
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->n0:Lkotlin/j;

    .line 74
    .line 75
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$viewBinding$2;->INSTANCE:Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$viewBinding$2;

    .line 76
    .line 77
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->o0:LRc/c;

    .line 82
    .line 83
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/l;

    .line 84
    .line 85
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/l;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 86
    .line 87
    .line 88
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->b1:Lkotlin/j;

    .line 93
    .line 94
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/b;

    .line 95
    .line 96
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/b;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 97
    .line 98
    .line 99
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->k1:Lkotlin/j;

    .line 104
    .line 105
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;

    .line 106
    .line 107
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$b;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 108
    .line 109
    .line 110
    iput-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->v1:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;

    .line 111
    .line 112
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LBS0/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->X2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LBS0/c;

    move-result-object p0

    return-object p0
.end method

.method public static final A3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Landroidx/lifecycle/e0$c;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->b3()LBS0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-interface {v1}, LBS0/c;->a()LBS0/f;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/4 v4, 0x4

    .line 12
    const/4 v5, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    move-object v2, p0

    .line 15
    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/viewmodel/core/f;-><init>(Lorg/xbet/ui_common/viewmodel/core/e;Landroidx/savedstate/f;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method

.method public static synthetic B2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->A3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->t3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->k3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->y3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->p3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic G2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic H2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->r3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic I2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->s3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic J2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->q3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic K2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->T2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Z2()LQS0/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->a3()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic N2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->e3()LQS0/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic O2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->f3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic P2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LwS0/q;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Q2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic R2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;LVS0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->v3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;LVS0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic S2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->w3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final W2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;
    .locals 2

    .line 1
    new-instance v0, LQS0/b;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-virtual {v1, p0}, Lorg/xbet/ui_common/utils/g;->C(Landroid/content/Context;)Z

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-direct {v0, p0}, LQS0/b;-><init>(Z)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public static final X2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LBS0/c;
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, LBS0/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, LBS0/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, LBS0/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->c3()Z

    .line 57
    .line 58
    .line 59
    move-result p0

    .line 60
    const-class v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;

    .line 61
    .line 62
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    invoke-virtual {v2, v0, p0, v1}, LBS0/d;->a(LwX0/c;ZLjava/lang/String;)LBS0/c;

    .line 67
    .line 68
    .line 69
    move-result-object p0

    .line 70
    return-object p0

    .line 71
    :cond_3
    new-instance p0, Ljava/lang/IllegalStateException;

    .line 72
    .line 73
    new-instance v0, Ljava/lang/StringBuilder;

    .line 74
    .line 75
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 76
    .line 77
    .line 78
    const-string v2, "Cannot create dependency "

    .line 79
    .line 80
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 95
    .line 96
    .line 97
    throw p0
.end method

.method private final Z2()LQS0/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LQS0/b;

    .line 8
    .line 9
    return-object v0
.end method

.method private final c3()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Boolean;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    return v0
.end method

.method private final j3()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$initDialogClickListeners$1;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$initDialogClickListeners$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    const-string v1, "REQUEST_TOP_UP"

    .line 11
    .line 12
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 13
    .line 14
    .line 15
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/c;

    .line 16
    .line 17
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/c;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 18
    .line 19
    .line 20
    const-string v1, "CHANGE_FILTERS_KEY"

    .line 21
    .line 22
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$initDialogClickListeners$3;

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    invoke-direct {v0, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$initDialogClickListeners$3;-><init>(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    invoke-static {p0, v1, v0}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public static final k3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->F4()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->w4()V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final p3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->x4()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->F4()V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final q3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->a0()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final r3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;->e()V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final s3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;->g()V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final t3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->D4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final u3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->D4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;LVS0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->z3(LVS0/a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final x3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Z
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    invoke-virtual {v0, p0}, Lorg/xbet/ui_common/utils/g;->M(Landroid/content/Context;)I

    .line 8
    .line 9
    .line 10
    move-result p0

    .line 11
    invoke-static {p0}, Lorg/xbet/ui_common/utils/ExtensionsKt;->v(I)I

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    const/16 v0, 0x2c6

    .line 16
    .line 17
    if-ge p0, v0, :cond_0

    .line 18
    .line 19
    const/4 p0, 0x1

    .line 20
    return p0

    .line 21
    :cond_0
    const/4 p0, 0x0

    .line 22
    return p0
.end method

.method public static synthetic y2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->x3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)Z

    move-result p0

    return p0
.end method

.method public static final y3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/a;
    .locals 3

    .line 1
    new-instance v0, LQS0/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$sportsAdapter$2$1;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v1, v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$sportsAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$sportsAdapter$2$2;

    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 15
    .line 16
    .line 17
    move-result-object p0

    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$sportsAdapter$2$2;-><init>(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    invoke-direct {v0, v1, v2}, LQS0/a;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public static synthetic z2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->W2(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)LQS0/b;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final T2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LwS0/q;->l:Landroidx/appcompat/widget/AppCompatImageButton;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    iget-object v1, v1, LwS0/q;->q:Landroidx/appcompat/widget/AppCompatImageButton;

    .line 12
    .line 13
    invoke-virtual {p0, v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->V2(Landroid/view/View;Landroid/view/View;)I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    int-to-float v0, v0

    .line 18
    const v1, 0x3f19999a

    .line 19
    .line 20
    .line 21
    mul-float v0, v0, v1

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget-object v1, v1, LwS0/q;->e:Landroidx/appcompat/widget/AppCompatTextView;

    .line 28
    .line 29
    float-to-int v0, v0

    .line 30
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setMaxWidth(I)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v1, v1, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 38
    .line 39
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setMaxWidth(I)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->U2(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final U2(I)V
    .locals 7

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget v1, Lpb/f;->text_14:I

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    sget v2, Lpb/f;->text_12:I

    .line 28
    .line 29
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimension(I)F

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    sget v3, Lpb/f;->text_10:I

    .line 46
    .line 47
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimension(I)F

    .line 48
    .line 49
    .line 50
    move-result v2

    .line 51
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    const/4 v3, 0x3

    .line 56
    new-array v3, v3, [Ljava/lang/Float;

    .line 57
    .line 58
    const/4 v4, 0x0

    .line 59
    aput-object v0, v3, v4

    .line 60
    .line 61
    const/4 v0, 0x1

    .line 62
    aput-object v1, v3, v0

    .line 63
    .line 64
    const/4 v0, 0x2

    .line 65
    aput-object v2, v3, v0

    .line 66
    .line 67
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    iget-object v1, v1, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 76
    .line 77
    invoke-virtual {v1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    :cond_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 86
    .line 87
    .line 88
    move-result v3

    .line 89
    if-eqz v3, :cond_1

    .line 90
    .line 91
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v3

    .line 95
    check-cast v3, Ljava/lang/Number;

    .line 96
    .line 97
    invoke-virtual {v3}, Ljava/lang/Number;->floatValue()F

    .line 98
    .line 99
    .line 100
    move-result v3

    .line 101
    invoke-virtual {v1, v3}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 102
    .line 103
    .line 104
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 105
    .line 106
    .line 107
    move-result-object v5

    .line 108
    iget-object v5, v5, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 109
    .line 110
    invoke-virtual {v5}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 111
    .line 112
    .line 113
    move-result-object v5

    .line 114
    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v5

    .line 118
    invoke-virtual {v1, v5}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 119
    .line 120
    .line 121
    move-result v5

    .line 122
    int-to-float v6, p1

    .line 123
    cmpg-float v5, v5, v6

    .line 124
    .line 125
    if-gtz v5, :cond_0

    .line 126
    .line 127
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    iget-object p1, p1, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 132
    .line 133
    invoke-virtual {p1, v4, v3}, Landroidx/appcompat/widget/AppCompatTextView;->setTextSize(IF)V

    .line 134
    .line 135
    .line 136
    return-void

    .line 137
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    iget-object p1, p1, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 142
    .line 143
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->I0(Ljava/util/List;)Ljava/lang/Object;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    check-cast v0, Ljava/lang/Number;

    .line 148
    .line 149
    invoke-virtual {v0}, Ljava/lang/Number;->floatValue()F

    .line 150
    .line 151
    .line 152
    move-result v0

    .line 153
    invoke-virtual {p1, v4, v0}, Landroidx/appcompat/widget/AppCompatTextView;->setTextSize(IF)V

    .line 154
    .line 155
    .line 156
    return-void
.end method

.method public final V2(Landroid/view/View;Landroid/view/View;)I
    .locals 2

    .line 1
    const/4 v0, 0x2

    .line 2
    new-array v1, v0, [I

    .line 3
    .line 4
    new-array v0, v0, [I

    .line 5
    .line 6
    invoke-virtual {p1, v1}, Landroid/view/View;->getLocationOnScreen([I)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p2, v0}, Landroid/view/View;->getLocationOnScreen([I)V

    .line 10
    .line 11
    .line 12
    const/4 p2, 0x0

    .line 13
    aget v0, v0, p2

    .line 14
    .line 15
    aget p2, v1, p2

    .line 16
    .line 17
    sub-int/2addr v0, p2

    .line 18
    invoke-virtual {p1}, Landroid/view/View;->getWidth()I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    sub-int/2addr v0, p1

    .line 23
    return v0
.end method

.method public final Y2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->j0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final a3()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 12
    .line 13
    return-object v0
.end method

.method public final b3()LBS0/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LBS0/c;

    .line 8
    .line 9
    return-object v0
.end method

.method public final d3()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->k0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final e3()LQS0/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LQS0/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final f3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;)Ljava/lang/String;
    .locals 6

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    .line 5
    .line 6
    sget v1, Lpb/k;->bet_processed_successfully:I

    .line 7
    .line 8
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 13
    .line 14
    .line 15
    const-string v1, "\n"

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 18
    .line 19
    .line 20
    sget v2, Lpb/k;->killer_clubs_coefficient:I

    .line 21
    .line 22
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;->b()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v3

    .line 26
    const/4 v4, 0x1

    .line 27
    new-array v4, v4, [Ljava/lang/Object;

    .line 28
    .line 29
    const/4 v5, 0x0

    .line 30
    aput-object v3, v4, v5

    .line 31
    .line 32
    invoke-virtual {p0, v2, v4}, Landroidx/fragment/app/Fragment;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 37
    .line 38
    .line 39
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    sget v1, Lpb/k;->history_bet_rate:I

    .line 43
    .line 44
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 49
    .line 50
    .line 51
    const-string v1, " "

    .line 52
    .line 53
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 54
    .line 55
    .line 56
    invoke-virtual {p1}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b$i;->a()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 61
    .line 62
    .line 63
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    return-object p1
.end method

.method public final g3()LwS0/q;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LwS0/q;

    .line 13
    .line 14
    return-object v0
.end method

.method public final h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final i3()V
    .locals 9

    .line 1
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->c3()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b;->f:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b$a;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b$a;->b()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    sget-object v0, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b;->f:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b$a;

    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b$a;->a()Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    :goto_0
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;

    .line 21
    .line 22
    iget-object v2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->v1:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;

    .line 23
    .line 24
    invoke-direct {v1, v2, v0}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/CardStackLayoutManager;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/cardstack/a;Lorg/xbet/swipex/impl/presentation/swipex/cardstack/b;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 32
    .line 33
    invoke-virtual {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 41
    .line 42
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Z2()LQS0/b;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 54
    .line 55
    const/4 v1, 0x0

    .line 56
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    iget-object v0, v0, LwS0/q;->i:Lorg/xbet/swipex/impl/presentation/swipex/cardstack/SwipexCardsRecyclerView;

    .line 64
    .line 65
    const/4 v1, 0x0

    .line 66
    invoke-virtual {v0, v1}, Landroid/view/View;->setClickable(Z)V

    .line 67
    .line 68
    .line 69
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->c3()Z

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    if-eqz v0, :cond_1

    .line 74
    .line 75
    const/high16 v0, 0x41e00000    # 28.0f

    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_1
    const/high16 v0, 0x42200000    # 40.0f

    .line 79
    .line 80
    :goto_1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    iget-object v2, v1, LwS0/q;->m:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 85
    .line 86
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 87
    .line 88
    .line 89
    move-result-object v6

    .line 90
    const/4 v7, 0x7

    .line 91
    const/4 v8, 0x0

    .line 92
    const/4 v3, 0x0

    .line 93
    const/4 v4, 0x0

    .line 94
    const/4 v5, 0x0

    .line 95
    invoke-static/range {v2 .. v8}, Lorg/xbet/ui_common/utils/ExtensionsKt;->p0(Landroid/view/View;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;ILjava/lang/Object;)V

    .line 96
    .line 97
    .line 98
    return-void
.end method

.method public final l3()V
    .locals 15

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LwS0/q;->t:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->e3()LQS0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LwS0/q;->t:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    sget v2, Lpb/f;->space_2:I

    .line 25
    .line 26
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 27
    .line 28
    .line 29
    move-result v4

    .line 30
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    sget v2, Lpb/f;->space_8:I

    .line 35
    .line 36
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 37
    .line 38
    .line 39
    move-result v6

    .line 40
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    sget v2, LlZ0/g;->space_8:I

    .line 45
    .line 46
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 47
    .line 48
    .line 49
    move-result v5

    .line 50
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    sget v2, LlZ0/g;->space_8:I

    .line 55
    .line 56
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 57
    .line 58
    .line 59
    move-result v7

    .line 60
    new-instance v3, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 61
    .line 62
    const/16 v13, 0x1d0

    .line 63
    .line 64
    const/4 v14, 0x0

    .line 65
    const/4 v8, 0x0

    .line 66
    const/4 v9, 0x0

    .line 67
    const/4 v10, 0x0

    .line 68
    const/4 v11, 0x0

    .line 69
    const/4 v12, 0x0

    .line 70
    invoke-direct/range {v3 .. v14}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v0, v3}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 74
    .line 75
    .line 76
    return-void
.end method

.method public final m3(Lkotlinx/coroutines/flow/e;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Ljava/util/List<",
            "+",
            "LVX0/i;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    new-instance v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeFilterUiState$1;

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    invoke-direct {v4, p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeFilterUiState$1;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 8
    .line 9
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static {v2}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 14
    .line 15
    .line 16
    move-result-object v6

    .line 17
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeFilterUiState$$inlined$observeWithLifecycle$default$1;

    .line 18
    .line 19
    const/4 v5, 0x0

    .line 20
    move-object v1, p1

    .line 21
    invoke-direct/range {v0 .. v5}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeFilterUiState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/4 v9, 0x3

    .line 25
    const/4 v10, 0x0

    .line 26
    move-object v5, v6

    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v8, v0

    .line 30
    invoke-static/range {v5 .. v10}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final n3(Lkotlinx/coroutines/flow/e;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$b;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    invoke-direct {v4, p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$1;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 8
    .line 9
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static {v2}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 14
    .line 15
    .line 16
    move-result-object v6

    .line 17
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$$inlined$observeWithLifecycle$default$1;

    .line 18
    .line 19
    const/4 v5, 0x0

    .line 20
    move-object v1, p1

    .line 21
    invoke-direct/range {v0 .. v5}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiAction$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/4 v9, 0x3

    .line 25
    const/4 v10, 0x0

    .line 26
    move-object v5, v6

    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v8, v0

    .line 30
    invoke-static/range {v5 .. v10}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final o3(Lkotlinx/coroutines/flow/e;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$c;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v4, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    invoke-direct {v4, p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$1;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;Lkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    sget-object v3, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 8
    .line 9
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-static {v2}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 14
    .line 15
    .line 16
    move-result-object v6

    .line 17
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$$inlined$observeWithLifecycle$default$1;

    .line 18
    .line 19
    const/4 v5, 0x0

    .line 20
    move-object v1, p1

    .line 21
    invoke-direct/range {v0 .. v5}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$observeUiState$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 22
    .line 23
    .line 24
    const/4 v9, 0x3

    .line 25
    const/4 v10, 0x0

    .line 26
    move-object v5, v6

    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v8, v0

    .line 30
    invoke-static/range {v5 .. v10}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public onCreate(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->onCreate(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->j3()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->i0:Z

    .line 2
    .line 3
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 5

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->i3()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->l3()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    iget-object p1, p1, LwS0/q;->u:Lorg/xbet/uikit/components/toolbar/statical/DSNavigationBarStatic;

    .line 15
    .line 16
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/a;

    .line 17
    .line 18
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/a;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 19
    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    const/4 v2, 0x1

    .line 23
    const/4 v3, 0x0

    .line 24
    invoke-static {p1, v1, v0, v2, v3}, LK01/d$a;->a(LK01/d;ZLkotlin/jvm/functions/Function0;ILjava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object p1, p1, LwS0/q;->q:Landroidx/appcompat/widget/AppCompatImageButton;

    .line 32
    .line 33
    sget-object v0, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_1000:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 34
    .line 35
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/d;

    .line 36
    .line 37
    invoke-direct {v1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/d;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 38
    .line 39
    .line 40
    invoke-static {p1, v0, v1}, LN11/f;->c(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    iget-object p1, p1, LwS0/q;->l:Landroidx/appcompat/widget/AppCompatImageButton;

    .line 48
    .line 49
    sget-object v1, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 50
    .line 51
    new-instance v4, Lorg/xbet/swipex/impl/presentation/swipex/e;

    .line 52
    .line 53
    invoke-direct {v4, p0}, Lorg/xbet/swipex/impl/presentation/swipex/e;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 54
    .line 55
    .line 56
    invoke-static {p1, v1, v4}, LN11/f;->c(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iget-object p1, p1, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 64
    .line 65
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/f;

    .line 66
    .line 67
    invoke-direct {v1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/f;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 68
    .line 69
    .line 70
    invoke-static {p1, v0, v1}, LN11/f;->c(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 71
    .line 72
    .line 73
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iget-object p1, p1, LwS0/q;->o:Landroidx/appcompat/widget/AppCompatImageView;

    .line 78
    .line 79
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/g;

    .line 80
    .line 81
    invoke-direct {v1, p0}, Lorg/xbet/swipex/impl/presentation/swipex/g;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 82
    .line 83
    .line 84
    invoke-static {p1, v0, v1}, LN11/f;->c(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 85
    .line 86
    .line 87
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iget-object p1, p1, LwS0/q;->p:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 92
    .line 93
    new-instance v0, Lorg/xbet/swipex/impl/presentation/swipex/h;

    .line 94
    .line 95
    invoke-direct {v0, p0}, Lorg/xbet/swipex/impl/presentation/swipex/h;-><init>(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 96
    .line 97
    .line 98
    invoke-static {p1, v3, v0, v2, v3}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 99
    .line 100
    .line 101
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->b3()LBS0/c;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-interface {v0, p0}, LBS0/c;->b(Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->k4()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$onObserveData$1;

    .line 13
    .line 14
    invoke-direct {v5, p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$onObserveData$1;-><init>(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    new-instance v1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v6, 0x0

    .line 30
    invoke-direct/range {v1 .. v6}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v10, 0x3

    .line 34
    const/4 v11, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v8, 0x0

    .line 37
    move-object v6, v0

    .line 38
    move-object v9, v1

    .line 39
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->E0()Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-virtual {p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->o3(Lkotlinx/coroutines/flow/e;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->o4()Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->n3(Lkotlinx/coroutines/flow/e;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->h3()Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->m4()Lkotlinx/coroutines/flow/e;

    .line 69
    .line 70
    .line 71
    move-result-object v0

    .line 72
    invoke-virtual {p0, v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->m3(Lkotlinx/coroutines/flow/e;)V

    .line 73
    .line 74
    .line 75
    return-void
.end method

.method public final w3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->Y2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->error:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->not_enough_money_for_bet:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v4

    .line 21
    sget v5, Lpb/k;->replenish:I

    .line 22
    .line 23
    invoke-virtual {v0, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v5

    .line 27
    sget v6, Lpb/k;->cancel:I

    .line 28
    .line 29
    invoke-virtual {v0, v6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v6

    .line 33
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 34
    .line 35
    const/16 v15, 0xbd0

    .line 36
    .line 37
    const/16 v16, 0x0

    .line 38
    .line 39
    const/4 v7, 0x0

    .line 40
    const-string v8, "REQUEST_TOP_UP"

    .line 41
    .line 42
    const/4 v9, 0x0

    .line 43
    const/4 v10, 0x0

    .line 44
    const/4 v11, 0x0

    .line 45
    const/4 v12, 0x0

    .line 46
    const/4 v14, 0x0

    .line 47
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final z3(LVS0/a;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LwS0/q;->e:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {p1}, LVS0/a;->a()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LwS0/q;->k:Landroidx/appcompat/widget/AppCompatTextView;

    .line 19
    .line 20
    invoke-virtual {p1}, LVS0/a;->c()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    sget v1, LrS0/a;->ic_glyph:I

    .line 32
    .line 33
    invoke-static {v0, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iget-object v1, v1, LwS0/q;->o:Landroidx/appcompat/widget/AppCompatImageView;

    .line 42
    .line 43
    invoke-virtual {v1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexFragment;->g3()LwS0/q;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    iget-object v0, v0, LwS0/q;->h:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    .line 52
    invoke-virtual {p1}, LVS0/a;->b()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 57
    .line 58
    .line 59
    return-void
.end method
