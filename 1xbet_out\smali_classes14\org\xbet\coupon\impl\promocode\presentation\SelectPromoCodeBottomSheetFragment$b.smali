.class public final Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/core/view/K;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->B2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;


# direct methods
.method public constructor <init>(ZLorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;)V
    .locals 0

    iput-boolean p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->a:Z

    iput-object p2, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->b:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onApplyWindowInsets(Landroid/view/View;Landroidx/core/view/F0;)Landroidx/core/view/F0;
    .locals 7

    .line 1
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->b:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->requireView()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    instance-of v0, p1, Landroid/view/View;

    .line 12
    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    check-cast p1, Landroid/view/View;

    .line 16
    .line 17
    :goto_0
    move-object v0, p1

    .line 18
    goto :goto_1

    .line 19
    :cond_0
    const/4 p1, 0x0

    .line 20
    goto :goto_0

    .line 21
    :goto_1
    if-eqz v0, :cond_1

    .line 22
    .line 23
    invoke-static {}, Landroidx/core/view/F0$o;->h()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget v2, p1, LI0/d;->b:I

    .line 32
    .line 33
    const/16 v5, 0xd

    .line 34
    .line 35
    const/4 v6, 0x0

    .line 36
    const/4 v1, 0x0

    .line 37
    const/4 v3, 0x0

    .line 38
    const/4 v4, 0x0

    .line 39
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    :cond_1
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->s(I)Z

    .line 47
    .line 48
    .line 49
    move-result p1

    .line 50
    if-eqz p1, :cond_2

    .line 51
    .line 52
    invoke-static {}, Landroidx/core/view/F0$o;->d()I

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iget p1, p1, LI0/d;->d:I

    .line 61
    .line 62
    :goto_2
    move v4, p1

    .line 63
    goto :goto_3

    .line 64
    :cond_2
    invoke-static {}, Landroidx/core/view/F0$o;->g()I

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iget p1, p1, LI0/d;->d:I

    .line 73
    .line 74
    goto :goto_2

    .line 75
    :goto_3
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->b:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 76
    .line 77
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 78
    .line 79
    .line 80
    move-result-object p1

    .line 81
    iget-object p1, p1, LC7/c;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 82
    .line 83
    invoke-virtual {p1}, Landroid/view/View;->getPaddingLeft()I

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    invoke-virtual {p1}, Landroid/view/View;->getPaddingTop()I

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    invoke-virtual {p1}, Landroid/view/View;->getPaddingRight()I

    .line 92
    .line 93
    .line 94
    move-result v2

    .line 95
    invoke-virtual {p1, v0, v1, v2, v4}, Landroid/view/View;->setPadding(IIII)V

    .line 96
    .line 97
    .line 98
    iget-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->b:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;

    .line 99
    .line 100
    invoke-virtual {p1}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment;->V2()LC7/c;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    iget-object v0, p1, LC7/c;->h:Landroid/widget/TextView;

    .line 105
    .line 106
    const/4 v5, 0x7

    .line 107
    const/4 v6, 0x0

    .line 108
    const/4 v1, 0x0

    .line 109
    const/4 v2, 0x0

    .line 110
    const/4 v3, 0x0

    .line 111
    invoke-static/range {v0 .. v6}, Lorg/xbet/ui_common/utils/ExtensionsKt;->o0(Landroid/view/View;IIIIILjava/lang/Object;)V

    .line 112
    .line 113
    .line 114
    iget-boolean p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeBottomSheetFragment$b;->a:Z

    .line 115
    .line 116
    if-eqz p1, :cond_3

    .line 117
    .line 118
    sget-object p1, Landroidx/core/view/F0;->b:Landroidx/core/view/F0;

    .line 119
    .line 120
    return-object p1

    .line 121
    :cond_3
    return-object p2
.end method
