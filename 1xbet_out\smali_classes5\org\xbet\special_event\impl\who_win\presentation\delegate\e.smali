.class public abstract Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;
.super Lorg/xbet/ui_common/viewmodel/core/k;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008!\u0018\u00002\u00020\u00012\u00020\u0002B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;",
        "Lorg/xbet/ui_common/viewmodel/core/k;",
        "Lorg/xbet/special_event/impl/who_win/presentation/delegate/d;",
        "<init>",
        "()V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget v0, Lorg/xbet/ui_common/viewmodel/core/k;->c:I

    sput v0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/e;->d:I

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/k;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
