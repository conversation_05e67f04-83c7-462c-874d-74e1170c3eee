.class final Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.promocode.presentation.SelectPromoCodeViewModel$loadPromoCodes$2"
    f = "SelectPromoCodeViewModel.kt"
    l = {
        0x3f,
        0x4c,
        0x4e,
        0x63
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->D3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;

    iget-object v1, p0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;-><init>(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 22

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    iget v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x4

    .line 10
    const/4 v4, 0x3

    .line 11
    const/4 v5, 0x2

    .line 12
    const/4 v6, 0x1

    .line 13
    const/4 v7, 0x0

    .line 14
    if-eqz v0, :cond_4

    .line 15
    .line 16
    if-eq v0, v6, :cond_3

    .line 17
    .line 18
    if-eq v0, v5, :cond_2

    .line 19
    .line 20
    if-eq v0, v4, :cond_1

    .line 21
    .line 22
    if-ne v0, v3, :cond_0

    .line 23
    .line 24
    iget-boolean v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->Z$0:Z

    .line 25
    .line 26
    iget-object v4, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$6:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v4, Ljava/util/Collection;

    .line 29
    .line 30
    iget-object v5, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$5:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v5, Lkotlinx/coroutines/flow/V;

    .line 33
    .line 34
    iget-object v8, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$4:Ljava/lang/Object;

    .line 35
    .line 36
    check-cast v8, Luw/c;

    .line 37
    .line 38
    iget-object v9, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$3:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v9, LHX0/e;

    .line 41
    .line 42
    iget-object v10, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$2:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v10, Ljava/util/Iterator;

    .line 45
    .line 46
    iget-object v11, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$1:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v11, Ljava/util/Collection;

    .line 49
    .line 50
    iget-object v12, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v12, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 53
    .line 54
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    move-object v3, v2

    .line 58
    move-object v2, v1

    .line 59
    move-object v1, v3

    .line 60
    move-object/from16 v3, p1

    .line 61
    .line 62
    const/4 v14, 0x4

    .line 63
    goto/16 :goto_e

    .line 64
    .line 65
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 66
    .line 67
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 68
    .line 69
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 70
    .line 71
    .line 72
    throw v0

    .line 73
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 74
    .line 75
    .line 76
    move-object v0, v2

    .line 77
    move-object v2, v1

    .line 78
    move-object v1, v0

    .line 79
    move-object/from16 v0, p1

    .line 80
    .line 81
    goto/16 :goto_a

    .line 82
    .line 83
    :cond_2
    iget-wide v8, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->J$0:J

    .line 84
    .line 85
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$1:Ljava/lang/Object;

    .line 86
    .line 87
    check-cast v0, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 88
    .line 89
    iget-object v5, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 90
    .line 91
    check-cast v5, Ljava/util/List;

    .line 92
    .line 93
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 94
    .line 95
    .line 96
    move-wide v10, v8

    .line 97
    move-object v9, v0

    .line 98
    move-object/from16 v0, p1

    .line 99
    .line 100
    goto/16 :goto_8

    .line 101
    .line 102
    :cond_3
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 103
    .line 104
    check-cast v0, Lkotlinx/coroutines/N;

    .line 105
    .line 106
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 107
    .line 108
    .line 109
    move-object/from16 v0, p1

    .line 110
    .line 111
    goto :goto_2

    .line 112
    :cond_4
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 113
    .line 114
    .line 115
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 116
    .line 117
    check-cast v0, Lkotlinx/coroutines/N;

    .line 118
    .line 119
    iget-object v8, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 120
    .line 121
    invoke-static {v8}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->q3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z

    .line 122
    .line 123
    .line 124
    move-result v8

    .line 125
    if-eqz v8, :cond_5

    .line 126
    .line 127
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 128
    .line 129
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->s3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)LMx/a;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    invoke-virtual {v0}, LMx/a;->a()Lorg/xbet/betting/core/zip/model/bet/BetInfo;

    .line 134
    .line 135
    .line 136
    move-result-object v0

    .line 137
    invoke-static {v0}, LMn/a;->a(Lorg/xbet/betting/core/zip/model/bet/BetInfo;)LNn/a;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    :goto_0
    move-object v8, v0

    .line 146
    goto :goto_3

    .line 147
    :cond_5
    iget-object v8, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 148
    .line 149
    invoke-static {v8}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->r3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/G;

    .line 150
    .line 151
    .line 152
    move-result-object v8

    .line 153
    iput-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 154
    .line 155
    iput v6, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->label:I

    .line 156
    .line 157
    invoke-virtual {v8, v1}, Lorg/xbet/coupon/impl/coupon/domain/usecases/G;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 158
    .line 159
    .line 160
    move-result-object v0

    .line 161
    if-ne v0, v2, :cond_6

    .line 162
    .line 163
    :goto_1
    move-object/from16 v20, v2

    .line 164
    .line 165
    move-object v2, v1

    .line 166
    move-object/from16 v1, v20

    .line 167
    .line 168
    goto/16 :goto_d

    .line 169
    .line 170
    :cond_6
    :goto_2
    check-cast v0, Ljava/util/List;

    .line 171
    .line 172
    goto :goto_0

    .line 173
    :goto_3
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 174
    .line 175
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->q3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z

    .line 176
    .line 177
    .line 178
    move-result v0

    .line 179
    if-eqz v0, :cond_7

    .line 180
    .line 181
    sget-object v0, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->SINGLE:Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 182
    .line 183
    :goto_4
    move-object v9, v0

    .line 184
    goto :goto_5

    .line 185
    :cond_7
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 186
    .line 187
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->t3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/d;

    .line 188
    .line 189
    .line 190
    move-result-object v0

    .line 191
    invoke-interface {v0}, Ltw/d;->invoke()Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    goto :goto_4

    .line 196
    :goto_5
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 197
    .line 198
    :try_start_0
    sget-object v10, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 199
    .line 200
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->A3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lcom/xbet/onexuser/domain/user/c;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/user/c;->g()J

    .line 205
    .line 206
    .line 207
    move-result-wide v10

    .line 208
    invoke-static {v10, v11}, LHc/a;->f(J)Ljava/lang/Long;

    .line 209
    .line 210
    .line 211
    move-result-object v0

    .line 212
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 216
    goto :goto_6

    .line 217
    :catchall_0
    move-exception v0

    .line 218
    sget-object v10, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 219
    .line 220
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 221
    .line 222
    .line 223
    move-result-object v0

    .line 224
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    move-result-object v0

    .line 228
    :goto_6
    const-wide/16 v10, -0x1

    .line 229
    .line 230
    invoke-static {v10, v11}, LHc/a;->f(J)Ljava/lang/Long;

    .line 231
    .line 232
    .line 233
    move-result-object v10

    .line 234
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 235
    .line 236
    .line 237
    move-result v11

    .line 238
    if-eqz v11, :cond_8

    .line 239
    .line 240
    move-object v0, v10

    .line 241
    :cond_8
    check-cast v0, Ljava/lang/Number;

    .line 242
    .line 243
    invoke-virtual {v0}, Ljava/lang/Number;->longValue()J

    .line 244
    .line 245
    .line 246
    move-result-wide v10

    .line 247
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 248
    .line 249
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->q3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z

    .line 250
    .line 251
    .line 252
    move-result v0

    .line 253
    if-eqz v0, :cond_9

    .line 254
    .line 255
    invoke-virtual {v9}, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->toInteger()I

    .line 256
    .line 257
    .line 258
    move-result v0

    .line 259
    :goto_7
    move-wide/from16 v20, v10

    .line 260
    .line 261
    move-object v11, v9

    .line 262
    move-wide/from16 v9, v20

    .line 263
    .line 264
    move v12, v0

    .line 265
    goto :goto_9

    .line 266
    :cond_9
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 267
    .line 268
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->w3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;

    .line 269
    .line 270
    .line 271
    move-result-object v0

    .line 272
    iput-object v8, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 273
    .line 274
    iput-object v9, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$1:Ljava/lang/Object;

    .line 275
    .line 276
    iput-wide v10, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->J$0:J

    .line 277
    .line 278
    iput v5, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->label:I

    .line 279
    .line 280
    invoke-virtual {v0, v9, v1}, Lorg/xbet/coupon/impl/coupon/domain/usecases/GetVidUseCase;->d(Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 281
    .line 282
    .line 283
    move-result-object v0

    .line 284
    if-ne v0, v2, :cond_a

    .line 285
    .line 286
    goto :goto_1

    .line 287
    :cond_a
    move-object v5, v8

    .line 288
    :goto_8
    check-cast v0, Ljava/lang/Number;

    .line 289
    .line 290
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 291
    .line 292
    .line 293
    move-result v0

    .line 294
    move-object v8, v5

    .line 295
    goto :goto_7

    .line 296
    :goto_9
    iget-object v0, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 297
    .line 298
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->z3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/o;

    .line 299
    .line 300
    .line 301
    move-result-object v0

    .line 302
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 303
    .line 304
    .line 305
    move-result-object v5

    .line 306
    iget-object v13, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 307
    .line 308
    invoke-static {v13}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->u3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Ltw/e;

    .line 309
    .line 310
    .line 311
    move-result-object v13

    .line 312
    invoke-interface {v13, v8}, Ltw/e;->a(Ljava/util/List;)J

    .line 313
    .line 314
    .line 315
    move-result-wide v13

    .line 316
    iput-object v7, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 317
    .line 318
    iput-object v7, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$1:Ljava/lang/Object;

    .line 319
    .line 320
    iput v4, v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->label:I

    .line 321
    .line 322
    move-wide v3, v9

    .line 323
    move-wide v9, v13

    .line 324
    const/4 v14, 0x4

    .line 325
    const-string v13, "0.0"

    .line 326
    .line 327
    const/4 v15, 0x4

    .line 328
    const/4 v14, 0x0

    .line 329
    const/16 v16, 0x4

    .line 330
    .line 331
    const/4 v15, 0x1

    .line 332
    const/16 v17, 0x4

    .line 333
    .line 334
    const/16 v16, 0x0

    .line 335
    .line 336
    move-object/from16 v19, v7

    .line 337
    .line 338
    move-object v7, v8

    .line 339
    const/16 v18, 0x1

    .line 340
    .line 341
    move-object v8, v5

    .line 342
    move-wide v5, v3

    .line 343
    move-object/from16 v17, v1

    .line 344
    .line 345
    move-object v1, v2

    .line 346
    move-object v2, v0

    .line 347
    invoke-interface/range {v2 .. v17}, Ltw/o;->a(JJLjava/util/List;Ljava/util/List;JLorg/xbet/betting/core/zip/domain/model/CouponTypeModel;ILjava/lang/String;ZZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 348
    .line 349
    .line 350
    move-result-object v0

    .line 351
    move-object/from16 v2, v17

    .line 352
    .line 353
    if-ne v0, v1, :cond_b

    .line 354
    .line 355
    goto/16 :goto_d

    .line 356
    .line 357
    :cond_b
    :goto_a
    check-cast v0, Ljava/util/List;

    .line 358
    .line 359
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 360
    .line 361
    .line 362
    move-result-object v0

    .line 363
    check-cast v0, Luw/e;

    .line 364
    .line 365
    if-eqz v0, :cond_c

    .line 366
    .line 367
    invoke-virtual {v0}, Luw/e;->K()Ljava/util/List;

    .line 368
    .line 369
    .line 370
    move-result-object v7

    .line 371
    goto :goto_b

    .line 372
    :cond_c
    const/4 v7, 0x0

    .line 373
    :goto_b
    if-nez v7, :cond_d

    .line 374
    .line 375
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 376
    .line 377
    .line 378
    move-result-object v7

    .line 379
    :cond_d
    invoke-interface {v7}, Ljava/util/List;->isEmpty()Z

    .line 380
    .line 381
    .line 382
    move-result v0

    .line 383
    if-eqz v0, :cond_e

    .line 384
    .line 385
    iget-object v0, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 386
    .line 387
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lkotlinx/coroutines/flow/V;

    .line 388
    .line 389
    .line 390
    move-result-object v0

    .line 391
    sget-object v1, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;->a:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$a;

    .line 392
    .line 393
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 394
    .line 395
    .line 396
    goto :goto_f

    .line 397
    :cond_e
    iget-object v0, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 398
    .line 399
    invoke-static {v0}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->x3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lkotlinx/coroutines/flow/V;

    .line 400
    .line 401
    .line 402
    move-result-object v0

    .line 403
    iget-object v3, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->this$0:Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;

    .line 404
    .line 405
    new-instance v4, Ljava/util/ArrayList;

    .line 406
    .line 407
    const/16 v5, 0xa

    .line 408
    .line 409
    invoke-static {v7, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 410
    .line 411
    .line 412
    move-result v5

    .line 413
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 414
    .line 415
    .line 416
    invoke-interface {v7}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 417
    .line 418
    .line 419
    move-result-object v5

    .line 420
    move-object v12, v3

    .line 421
    move-object v10, v5

    .line 422
    move-object v5, v0

    .line 423
    :goto_c
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 424
    .line 425
    .line 426
    move-result v0

    .line 427
    if-eqz v0, :cond_10

    .line 428
    .line 429
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 430
    .line 431
    .line 432
    move-result-object v0

    .line 433
    move-object v8, v0

    .line 434
    check-cast v8, Luw/c;

    .line 435
    .line 436
    invoke-static {v12}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->y3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)LHX0/e;

    .line 437
    .line 438
    .line 439
    move-result-object v9

    .line 440
    invoke-static {v12}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->B3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Z

    .line 441
    .line 442
    .line 443
    move-result v0

    .line 444
    invoke-static {v12}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;->v3(Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel;)Lfk/m;

    .line 445
    .line 446
    .line 447
    move-result-object v3

    .line 448
    iput-object v12, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$0:Ljava/lang/Object;

    .line 449
    .line 450
    iput-object v4, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$1:Ljava/lang/Object;

    .line 451
    .line 452
    iput-object v10, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$2:Ljava/lang/Object;

    .line 453
    .line 454
    iput-object v9, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$3:Ljava/lang/Object;

    .line 455
    .line 456
    iput-object v8, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$4:Ljava/lang/Object;

    .line 457
    .line 458
    iput-object v5, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$5:Ljava/lang/Object;

    .line 459
    .line 460
    iput-object v4, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->L$6:Ljava/lang/Object;

    .line 461
    .line 462
    iput-boolean v0, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->Z$0:Z

    .line 463
    .line 464
    const/4 v14, 0x4

    .line 465
    iput v14, v2, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$loadPromoCodes$2;->label:I

    .line 466
    .line 467
    const/4 v6, 0x1

    .line 468
    const/4 v7, 0x0

    .line 469
    invoke-static {v3, v7, v2, v6, v7}, Lfk/m$a;->a(Lfk/m;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 470
    .line 471
    .line 472
    move-result-object v3

    .line 473
    if-ne v3, v1, :cond_f

    .line 474
    .line 475
    :goto_d
    return-object v1

    .line 476
    :cond_f
    move-object v11, v4

    .line 477
    :goto_e
    check-cast v3, Lorg/xbet/balance/model/BalanceModel;

    .line 478
    .line 479
    invoke-virtual {v3}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 480
    .line 481
    .line 482
    move-result-object v3

    .line 483
    invoke-static {v8, v9, v0, v3}, LQx/a;->d(Luw/c;LHX0/e;ZLjava/lang/String;)LRx/a;

    .line 484
    .line 485
    .line 486
    move-result-object v0

    .line 487
    invoke-interface {v4, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 488
    .line 489
    .line 490
    move-object v4, v11

    .line 491
    goto :goto_c

    .line 492
    :cond_10
    check-cast v4, Ljava/util/List;

    .line 493
    .line 494
    new-instance v0, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;

    .line 495
    .line 496
    invoke-direct {v0, v4}, Lorg/xbet/coupon/impl/promocode/presentation/SelectPromoCodeViewModel$a$c;-><init>(Ljava/util/List;)V

    .line 497
    .line 498
    .line 499
    invoke-interface {v5, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 500
    .line 501
    .line 502
    :goto_f
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 503
    .line 504
    return-object v0
.end method
