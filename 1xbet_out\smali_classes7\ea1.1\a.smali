.class public final Lea1/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0011\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0004\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u000e\u001a\u0004\u0008\u000f\u0010\u0010R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u0011\u001a\u0004\u0008\r\u0010\u0012R\u0017\u0010\u0006\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0013\u0010\u0011\u001a\u0004\u0008\u0014\u0010\u0012R\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u0017R\u0017\u0010\n\u001a\u00020\t8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0018\u001a\u0004\u0008\u0013\u0010\u0019\u00a8\u0006\u001a"
    }
    d2 = {
        "Lea1/a;",
        "",
        "",
        "bonusId",
        "",
        "accountId",
        "partitionId",
        "",
        "showFavorites",
        "Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
        "openedFromType",
        "<init>",
        "(IJJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V",
        "a",
        "I",
        "b",
        "()I",
        "J",
        "()J",
        "c",
        "d",
        "Z",
        "e",
        "()Z",
        "Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
        "()Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:J

.field public final c:J

.field public final d:Z

.field public final e:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(IJJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V
    .locals 0
    .param p7    # Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lea1/a;->a:I

    .line 3
    iput-wide p2, p0, Lea1/a;->b:J

    .line 4
    iput-wide p4, p0, Lea1/a;->c:J

    .line 5
    iput-boolean p6, p0, Lea1/a;->d:Z

    .line 6
    iput-object p7, p0, Lea1/a;->e:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    return-void
.end method

.method public synthetic constructor <init>(IJJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 8

    and-int/lit8 v0, p8, 0x4

    if-eqz v0, :cond_0

    .line 7
    sget-object p4, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    invoke-virtual {p4}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    move-result-wide p4

    :cond_0
    move-wide v4, p4

    and-int/lit8 p4, p8, 0x8

    if-eqz p4, :cond_1

    const/4 p4, 0x0

    const/4 v6, 0x0

    :goto_0
    move-object v0, p0

    move v1, p1

    move-wide v2, p2

    move-object v7, p7

    goto :goto_1

    :cond_1
    move v6, p6

    goto :goto_0

    .line 8
    :goto_1
    invoke-direct/range {v0 .. v7}, Lea1/a;-><init>(IJJZLorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;)V

    return-void
.end method


# virtual methods
.method public final a()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lea1/a;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lea1/a;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public final c()Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lea1/a;->e:Lorg/xplatform/aggregator/api/presentation/model/AggregatorPublisherGamesOpenedFromType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lea1/a;->c:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final e()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lea1/a;->d:Z

    .line 2
    .line 3
    return v0
.end method
