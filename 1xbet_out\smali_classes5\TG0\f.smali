.class public final LTG0/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0001\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a\u001f\u0010\n\u001a\u00020\t2\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0001\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "LUG0/a;",
        "lastGameUiModel",
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "c",
        "(LUG0/a;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "Landroid/content/Context;",
        "context",
        "Landroid/view/View;",
        "f",
        "(Landroid/content/Context;LUG0/a;)Landroid/view/View;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LUG0/a;Landroid/content/Context;)Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LTG0/f;->d(LUG0/a;Landroid/content/Context;)Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUG0/a;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LTG0/f;->e(LUG0/a;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LUG0/a;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 7
    .param p0    # LUG0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const v0, 0x2dd05c0d

    .line 2
    .line 3
    .line 4
    invoke-interface {p2, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object v4

    .line 8
    and-int/lit8 p2, p4, 0x1

    .line 9
    .line 10
    const/4 v1, 0x4

    .line 11
    if-eqz p2, :cond_0

    .line 12
    .line 13
    or-int/lit8 p2, p3, 0x6

    .line 14
    .line 15
    goto :goto_2

    .line 16
    :cond_0
    and-int/lit8 p2, p3, 0x6

    .line 17
    .line 18
    if-nez p2, :cond_3

    .line 19
    .line 20
    and-int/lit8 p2, p3, 0x8

    .line 21
    .line 22
    if-nez p2, :cond_1

    .line 23
    .line 24
    invoke-interface {v4, p0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    goto :goto_0

    .line 29
    :cond_1
    invoke-interface {v4, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    :goto_0
    if-eqz p2, :cond_2

    .line 34
    .line 35
    const/4 p2, 0x4

    .line 36
    goto :goto_1

    .line 37
    :cond_2
    const/4 p2, 0x2

    .line 38
    :goto_1
    or-int/2addr p2, p3

    .line 39
    goto :goto_2

    .line 40
    :cond_3
    move p2, p3

    .line 41
    :goto_2
    and-int/lit8 v2, p4, 0x2

    .line 42
    .line 43
    if-eqz v2, :cond_4

    .line 44
    .line 45
    or-int/lit8 p2, p2, 0x30

    .line 46
    .line 47
    goto :goto_4

    .line 48
    :cond_4
    and-int/lit8 v3, p3, 0x30

    .line 49
    .line 50
    if-nez v3, :cond_6

    .line 51
    .line 52
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v3

    .line 56
    if-eqz v3, :cond_5

    .line 57
    .line 58
    const/16 v3, 0x20

    .line 59
    .line 60
    goto :goto_3

    .line 61
    :cond_5
    const/16 v3, 0x10

    .line 62
    .line 63
    :goto_3
    or-int/2addr p2, v3

    .line 64
    :cond_6
    :goto_4
    and-int/lit8 v3, p2, 0x13

    .line 65
    .line 66
    const/16 v5, 0x12

    .line 67
    .line 68
    if-ne v3, v5, :cond_8

    .line 69
    .line 70
    invoke-interface {v4}, Landroidx/compose/runtime/j;->c()Z

    .line 71
    .line 72
    .line 73
    move-result v3

    .line 74
    if-nez v3, :cond_7

    .line 75
    .line 76
    goto :goto_5

    .line 77
    :cond_7
    invoke-interface {v4}, Landroidx/compose/runtime/j;->n()V

    .line 78
    .line 79
    .line 80
    goto :goto_8

    .line 81
    :cond_8
    :goto_5
    if-eqz v2, :cond_9

    .line 82
    .line 83
    sget-object p1, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 84
    .line 85
    :cond_9
    move-object v2, p1

    .line 86
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 87
    .line 88
    .line 89
    move-result p1

    .line 90
    if-eqz p1, :cond_a

    .line 91
    .line 92
    const/4 p1, -0x1

    .line 93
    const-string v3, "org.xbet.statistic.main.common.presentation.last_games.components.StatisticsEventCardComponent (StatisticsEventCardComponent.kt:20)"

    .line 94
    .line 95
    invoke-static {v0, p2, p1, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 96
    .line 97
    .line 98
    :cond_a
    const p1, 0x4c5de2

    .line 99
    .line 100
    .line 101
    invoke-interface {v4, p1}, Landroidx/compose/runtime/j;->t(I)V

    .line 102
    .line 103
    .line 104
    and-int/lit8 p1, p2, 0xe

    .line 105
    .line 106
    if-eq p1, v1, :cond_c

    .line 107
    .line 108
    and-int/lit8 p1, p2, 0x8

    .line 109
    .line 110
    if-eqz p1, :cond_b

    .line 111
    .line 112
    invoke-interface {v4, p0}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 113
    .line 114
    .line 115
    move-result p1

    .line 116
    if-eqz p1, :cond_b

    .line 117
    .line 118
    goto :goto_6

    .line 119
    :cond_b
    const/4 p1, 0x0

    .line 120
    goto :goto_7

    .line 121
    :cond_c
    :goto_6
    const/4 p1, 0x1

    .line 122
    :goto_7
    invoke-interface {v4}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    if-nez p1, :cond_d

    .line 127
    .line 128
    sget-object p1, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 129
    .line 130
    invoke-virtual {p1}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 131
    .line 132
    .line 133
    move-result-object p1

    .line 134
    if-ne v0, p1, :cond_e

    .line 135
    .line 136
    :cond_d
    new-instance v0, LTG0/d;

    .line 137
    .line 138
    invoke-direct {v0, p0}, LTG0/d;-><init>(LUG0/a;)V

    .line 139
    .line 140
    .line 141
    invoke-interface {v4, v0}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 142
    .line 143
    .line 144
    :cond_e
    move-object v1, v0

    .line 145
    check-cast v1, Lkotlin/jvm/functions/Function1;

    .line 146
    .line 147
    invoke-interface {v4}, Landroidx/compose/runtime/j;->q()V

    .line 148
    .line 149
    .line 150
    and-int/lit8 v5, p2, 0x70

    .line 151
    .line 152
    const/4 v6, 0x4

    .line 153
    const/4 v3, 0x0

    .line 154
    invoke-static/range {v1 .. v6}, Landroidx/compose/ui/viewinterop/AndroidView_androidKt;->a(Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 155
    .line 156
    .line 157
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 158
    .line 159
    .line 160
    move-result p1

    .line 161
    if-eqz p1, :cond_f

    .line 162
    .line 163
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 164
    .line 165
    .line 166
    :cond_f
    move-object p1, v2

    .line 167
    :goto_8
    invoke-interface {v4}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 168
    .line 169
    .line 170
    move-result-object p2

    .line 171
    if-eqz p2, :cond_10

    .line 172
    .line 173
    new-instance v0, LTG0/e;

    .line 174
    .line 175
    invoke-direct {v0, p0, p1, p3, p4}, LTG0/e;-><init>(LUG0/a;Landroidx/compose/ui/l;II)V

    .line 176
    .line 177
    .line 178
    invoke-interface {p2, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 179
    .line 180
    .line 181
    :cond_10
    return-void
.end method

.method public static final d(LUG0/a;Landroid/content/Context;)Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p1

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    sget p1, LlZ0/d;->uikitBackgroundGroupSecondary:I

    .line 12
    .line 13
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->setBackgroundAttrColor(I)V

    .line 14
    .line 15
    .line 16
    invoke-static {v1, p0}, LTG0/f;->f(Landroid/content/Context;LUG0/a;)Landroid/view/View;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    invoke-virtual {v0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, LUG0/a;->h()LUG0/c;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    sget-object p1, LUG0/c$a;->a:LUG0/c$a;

    .line 28
    .line 29
    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_0

    .line 34
    .line 35
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->YELLOW:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 36
    .line 37
    const/4 v4, 0x4

    .line 38
    const/4 v5, 0x0

    .line 39
    const/4 v3, 0x0

    .line 40
    move-object v2, v1

    .line 41
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication$default(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;ZILjava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_0
    sget-object p1, LUG0/c$b;->a:LUG0/c$b;

    .line 46
    .line 47
    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-eqz p1, :cond_1

    .line 52
    .line 53
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->GREEN:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 54
    .line 55
    sget-object v2, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->RED:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 56
    .line 57
    const/4 v4, 0x4

    .line 58
    const/4 v5, 0x0

    .line 59
    const/4 v3, 0x0

    .line 60
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication$default(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;ZILjava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_1
    sget-object p1, LUG0/c$c;->a:LUG0/c$c;

    .line 65
    .line 66
    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 67
    .line 68
    .line 69
    move-result p1

    .line 70
    if-eqz p1, :cond_2

    .line 71
    .line 72
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->RED:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 73
    .line 74
    sget-object v2, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;->GREEN:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;

    .line 75
    .line 76
    const/4 v4, 0x4

    .line 77
    const/4 v5, 0x0

    .line 78
    const/4 v3, 0x0

    .line 79
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication$default(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;ZILjava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    goto :goto_0

    .line 83
    :cond_2
    sget-object p1, LUG0/c$d;->a:LUG0/c$d;

    .line 84
    .line 85
    invoke-static {p0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    move-result p0

    .line 89
    if-eqz p0, :cond_3

    .line 90
    .line 91
    :goto_0
    const/4 p0, 0x0

    .line 92
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutDirection(I)V

    .line 93
    .line 94
    .line 95
    return-object v0

    .line 96
    :cond_3
    new-instance p0, Lkotlin/NoWhenBranchMatchedException;

    .line 97
    .line 98
    invoke-direct {p0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 99
    .line 100
    .line 101
    throw p0
.end method

.method public static final e(LUG0/a;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    .line 2
    .line 3
    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    invoke-static {p0, p1, p4, p2, p3}, LTG0/f;->c(LUG0/a;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final f(Landroid/content/Context;LUG0/a;)Landroid/view/View;
    .locals 10

    .line 1
    invoke-virtual {p1}, LUG0/a;->i()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, -0x2

    .line 6
    const/4 v2, -0x1

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    new-instance v3, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;

    .line 10
    .line 11
    const/4 v7, 0x6

    .line 12
    const/4 v8, 0x0

    .line 13
    const/4 v5, 0x0

    .line 14
    const/4 v6, 0x0

    .line 15
    move-object v4, p0

    .line 16
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 17
    .line 18
    .line 19
    new-instance p0, Landroid/view/ViewGroup$LayoutParams;

    .line 20
    .line 21
    invoke-direct {p0, v2, v1}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {v3, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p1}, LUG0/a;->c()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p0

    .line 31
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setScore(Ljava/lang/CharSequence;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p1}, LUG0/a;->f()LNN0/l;

    .line 35
    .line 36
    .line 37
    move-result-object p0

    .line 38
    invoke-virtual {p0}, LNN0/l;->f()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setFirstTeamsName(Ljava/lang/CharSequence;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1}, LUG0/a;->g()LNN0/l;

    .line 46
    .line 47
    .line 48
    move-result-object p0

    .line 49
    invoke-virtual {p0}, LNN0/l;->f()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object p0

    .line 53
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setSecondTeamsName(Ljava/lang/CharSequence;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p1}, LUG0/a;->f()LNN0/l;

    .line 57
    .line 58
    .line 59
    move-result-object p0

    .line 60
    invoke-virtual {p0}, LNN0/l;->c()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object p0

    .line 64
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setTopFirstTeamLogo(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p1}, LUG0/a;->g()LNN0/l;

    .line 68
    .line 69
    .line 70
    move-result-object p0

    .line 71
    invoke-virtual {p0}, LNN0/l;->c()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object p0

    .line 75
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setTopSecondTeamLogo(Ljava/lang/String;)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {p1}, LUG0/a;->f()LNN0/l;

    .line 79
    .line 80
    .line 81
    move-result-object p0

    .line 82
    invoke-virtual {p0}, LNN0/l;->d()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object p0

    .line 86
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setBotFirstTeamLogo(Ljava/lang/String;)V

    .line 87
    .line 88
    .line 89
    invoke-virtual {p1}, LUG0/a;->g()LNN0/l;

    .line 90
    .line 91
    .line 92
    move-result-object p0

    .line 93
    invoke-virtual {p0}, LNN0/l;->d()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p0

    .line 97
    invoke-virtual {v3, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddlePairTeams;->setBotSecondTeamLogo(Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    return-object v3

    .line 101
    :cond_0
    move-object v4, p0

    .line 102
    new-instance p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;

    .line 103
    .line 104
    const/4 v8, 0x6

    .line 105
    const/4 v9, 0x0

    .line 106
    const/4 v6, 0x0

    .line 107
    const/4 v7, 0x0

    .line 108
    move-object v5, v4

    .line 109
    move-object v4, p0

    .line 110
    invoke-direct/range {v4 .. v9}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 111
    .line 112
    .line 113
    new-instance p0, Landroid/view/ViewGroup$LayoutParams;

    .line 114
    .line 115
    invoke-direct {p0, v2, v1}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {v4, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 119
    .line 120
    .line 121
    invoke-virtual {p1}, LUG0/a;->c()Ljava/lang/String;

    .line 122
    .line 123
    .line 124
    move-result-object p0

    .line 125
    invoke-virtual {v4, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;->setScore(Ljava/lang/CharSequence;)V

    .line 126
    .line 127
    .line 128
    invoke-virtual {p1}, LUG0/a;->f()LNN0/l;

    .line 129
    .line 130
    .line 131
    move-result-object p0

    .line 132
    invoke-virtual {p0}, LNN0/l;->b()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object p0

    .line 136
    invoke-virtual {v4, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;->setFirstTeamLogo(Ljava/lang/String;)V

    .line 137
    .line 138
    .line 139
    invoke-virtual {p1}, LUG0/a;->g()LNN0/l;

    .line 140
    .line 141
    .line 142
    move-result-object p0

    .line 143
    invoke-virtual {p0}, LNN0/l;->b()Ljava/lang/String;

    .line 144
    .line 145
    .line 146
    move-result-object p0

    .line 147
    invoke-virtual {v4, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;->setSecondTeamLogo(Ljava/lang/String;)V

    .line 148
    .line 149
    .line 150
    invoke-virtual {p1}, LUG0/a;->f()LNN0/l;

    .line 151
    .line 152
    .line 153
    move-result-object p0

    .line 154
    invoke-virtual {p0}, LNN0/l;->f()Ljava/lang/String;

    .line 155
    .line 156
    .line 157
    move-result-object p0

    .line 158
    invoke-virtual {v4, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;->setFirstTeamName(Ljava/lang/CharSequence;)V

    .line 159
    .line 160
    .line 161
    invoke-virtual {p1}, LUG0/a;->g()LNN0/l;

    .line 162
    .line 163
    .line 164
    move-result-object p0

    .line 165
    invoke-virtual {p0}, LNN0/l;->f()Ljava/lang/String;

    .line 166
    .line 167
    .line 168
    move-result-object p0

    .line 169
    invoke-virtual {v4, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleTwoTeams;->setSecondTeamName(Ljava/lang/CharSequence;)V

    .line 170
    .line 171
    .line 172
    return-object v4
.end method
