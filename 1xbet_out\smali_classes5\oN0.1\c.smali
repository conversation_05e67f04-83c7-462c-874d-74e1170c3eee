.class public final LoN0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u0004*\u00020\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "LpN0/e;",
        "",
        "LND0/k;",
        "teamModels",
        "LrN0/c;",
        "a",
        "(LpN0/e;Ljava/util/List;)LrN0/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LpN0/e;Ljava/util/List;)LrN0/c;
    .locals 13
    .param p0    # LpN0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LpN0/e;",
            "Ljava/util/List<",
            "LND0/k;",
            ">;)",
            "LrN0/c;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-interface {p1, v0}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    const/4 v2, 0x0

    .line 14
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-interface {v0}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    move-object v3, v1

    .line 21
    check-cast v3, LND0/k;

    .line 22
    .line 23
    invoke-virtual {p0}, LpN0/e;->h()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-virtual {v3}, LND0/k;->c()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    invoke-static {v4, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    if-eqz v3, :cond_0

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_1
    move-object v1, v2

    .line 39
    :goto_0
    move-object v4, v1

    .line 40
    check-cast v4, LND0/k;

    .line 41
    .line 42
    if-eqz v4, :cond_b

    .line 43
    .line 44
    invoke-virtual {p0}, LpN0/e;->a()Ljava/util/List;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    if-eqz v0, :cond_2

    .line 49
    .line 50
    new-instance v2, Ljava/util/ArrayList;

    .line 51
    .line 52
    const/16 v1, 0xa

    .line 53
    .line 54
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-direct {v2, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 59
    .line 60
    .line 61
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    if-eqz v1, :cond_2

    .line 70
    .line 71
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    check-cast v1, LpN0/a;

    .line 76
    .line 77
    invoke-static {v1, p1}, LoN0/d;->a(LpN0/a;Ljava/util/List;)LrN0/d;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    invoke-interface {v2, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    goto :goto_1

    .line 85
    :cond_2
    if-nez v2, :cond_3

    .line 86
    .line 87
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 88
    .line 89
    .line 90
    move-result-object v2

    .line 91
    :cond_3
    move-object v5, v2

    .line 92
    invoke-virtual {p0}, LpN0/e;->e()Ljava/lang/Integer;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    const/4 v0, -0x1

    .line 97
    if-eqz p1, :cond_4

    .line 98
    .line 99
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 100
    .line 101
    .line 102
    move-result p1

    .line 103
    move v6, p1

    .line 104
    goto :goto_2

    .line 105
    :cond_4
    const/4 v6, -0x1

    .line 106
    :goto_2
    invoke-virtual {p0}, LpN0/e;->i()Ljava/lang/Integer;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    if-eqz p1, :cond_5

    .line 111
    .line 112
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 113
    .line 114
    .line 115
    move-result p1

    .line 116
    move v7, p1

    .line 117
    goto :goto_3

    .line 118
    :cond_5
    const/4 v7, -0x1

    .line 119
    :goto_3
    invoke-virtual {p0}, LpN0/e;->c()Ljava/lang/Integer;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    if-eqz p1, :cond_6

    .line 124
    .line 125
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 126
    .line 127
    .line 128
    move-result p1

    .line 129
    move v8, p1

    .line 130
    goto :goto_4

    .line 131
    :cond_6
    const/4 v8, -0x1

    .line 132
    :goto_4
    invoke-virtual {p0}, LpN0/e;->d()Ljava/lang/Integer;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    if-eqz p1, :cond_7

    .line 137
    .line 138
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 139
    .line 140
    .line 141
    move-result p1

    .line 142
    move v9, p1

    .line 143
    goto :goto_5

    .line 144
    :cond_7
    const/4 v9, -0x1

    .line 145
    :goto_5
    invoke-virtual {p0}, LpN0/e;->g()Ljava/lang/String;

    .line 146
    .line 147
    .line 148
    move-result-object p1

    .line 149
    if-nez p1, :cond_8

    .line 150
    .line 151
    const-string p1, ""

    .line 152
    .line 153
    :cond_8
    move-object v10, p1

    .line 154
    invoke-virtual {p0}, LpN0/e;->f()Ljava/lang/Integer;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    if-eqz p1, :cond_9

    .line 159
    .line 160
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 161
    .line 162
    .line 163
    move-result p1

    .line 164
    move v11, p1

    .line 165
    goto :goto_6

    .line 166
    :cond_9
    const/4 v11, -0x1

    .line 167
    :goto_6
    sget-object p1, Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;->Companion:Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType$a;

    .line 168
    .line 169
    invoke-virtual {p0}, LpN0/e;->b()Ljava/lang/Integer;

    .line 170
    .line 171
    .line 172
    move-result-object p0

    .line 173
    if-eqz p0, :cond_a

    .line 174
    .line 175
    invoke-virtual {p0}, Ljava/lang/Integer;->intValue()I

    .line 176
    .line 177
    .line 178
    move-result v0

    .line 179
    :cond_a
    invoke-virtual {p1, v0}, Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType$a;->a(I)Lorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;

    .line 180
    .line 181
    .line 182
    move-result-object v12

    .line 183
    new-instance v3, LrN0/c;

    .line 184
    .line 185
    invoke-direct/range {v3 .. v12}, LrN0/c;-><init>(LND0/k;Ljava/util/List;IIIILjava/lang/String;ILorg/xbet/statistic/statistic_core/domain/models/StageTableRowColorType;)V

    .line 186
    .line 187
    .line 188
    return-object v3

    .line 189
    :cond_b
    new-instance p0, Lcom/xbet/onexcore/BadDataResponseException;

    .line 190
    .line 191
    const/4 p1, 0x1

    .line 192
    invoke-direct {p0, v2, p1, v2}, Lcom/xbet/onexcore/BadDataResponseException;-><init>(Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 193
    .line 194
    .line 195
    throw p0
.end method
