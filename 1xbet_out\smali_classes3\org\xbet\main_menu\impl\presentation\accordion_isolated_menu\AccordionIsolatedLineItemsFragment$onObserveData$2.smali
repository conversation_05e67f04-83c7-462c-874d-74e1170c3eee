.class final Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.main_menu.impl.presentation.accordion_isolated_menu.AccordionIsolatedLineItemsFragment$onObserveData$2"
    f = "AccordionIsolatedLineItemsFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;-><init>(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->invoke(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c$b;->a:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c$b;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/4 v1, 0x0

    .line 22
    const/16 v2, 0x8

    .line 23
    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 27
    .line 28
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->H2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lv80/b;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 33
    .line 34
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 35
    .line 36
    .line 37
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->H2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lv80/b;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, Lv80/b;->c:Landroid/widget/ProgressBar;

    .line 44
    .line 45
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 46
    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_0
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c$a;->a:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsViewModel$c$a;

    .line 50
    .line 51
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 52
    .line 53
    .line 54
    move-result p1

    .line 55
    if-eqz p1, :cond_1

    .line 56
    .line 57
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 58
    .line 59
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->H2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lv80/b;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iget-object p1, p1, Lv80/b;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 64
    .line 65
    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    .line 66
    .line 67
    .line 68
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment$onObserveData$2;->this$0:Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;

    .line 69
    .line 70
    invoke-static {p1}, Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;->H2(Lorg/xbet/main_menu/impl/presentation/accordion_isolated_menu/AccordionIsolatedLineItemsFragment;)Lv80/b;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iget-object p1, p1, Lv80/b;->c:Landroid/widget/ProgressBar;

    .line 75
    .line 76
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 77
    .line 78
    .line 79
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 80
    .line 81
    return-object p1

    .line 82
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 83
    .line 84
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 85
    .line 86
    .line 87
    throw p1

    .line 88
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 89
    .line 90
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 91
    .line 92
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    throw p1
.end method
