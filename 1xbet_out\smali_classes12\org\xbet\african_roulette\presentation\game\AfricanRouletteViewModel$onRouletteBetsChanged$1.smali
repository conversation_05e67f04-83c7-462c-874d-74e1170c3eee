.class final Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;
.super Lkotlin/coroutines/jvm/internal/ContinuationImpl;
.source "SourceFile"


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.african_roulette.presentation.game.AfricanRouletteViewModel"
    f = "AfricanRouletteViewModel.kt"
    l = {
        0x165
    }
    m = "onRouletteBetsChanged"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->i4(Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field D$0:D

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field Z$0:Z

.field Z$1:Z

.field label:I

.field synthetic result:Ljava/lang/Object;

.field final synthetic this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    invoke-direct {p0, p2}, Lkotlin/coroutines/jvm/internal/ContinuationImpl;-><init>(Lkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .param p1    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    iput-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->result:Ljava/lang/Object;

    iget p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    const/high16 v0, -0x80000000

    or-int/2addr p1, v0

    iput p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->label:I

    iget-object p1, p0, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel$onRouletteBetsChanged$1;->this$0:Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;

    const/4 v0, 0x0

    invoke-static {p1, v0, p0}, Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;->L3(Lorg/xbet/african_roulette/presentation/game/AfricanRouletteViewModel;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method
