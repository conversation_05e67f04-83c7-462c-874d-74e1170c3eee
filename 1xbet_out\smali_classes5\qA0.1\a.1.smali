.class public interface abstract LqA0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LqA0/a$a;
    }
.end annotation

.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008p\u0018\u0000 \u00022\u00020\u0001:\u0001\u0002\u0082\u0001\n\u0003\u0004\u0005\u0006\u0007\u0008\t\n\u000b\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LqA0/a;",
        "",
        "a",
        "LqA0/m;",
        "LqA0/o;",
        "LqA0/q;",
        "LqA0/s;",
        "LqA0/u;",
        "LqA0/x;",
        "LqA0/z;",
        "LqA0/B;",
        "LqA0/C;",
        "LqA0/E;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:LqA0/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LqA0/a$a;->a:LqA0/a$a;

    .line 2
    .line 3
    sput-object v0, LqA0/a;->a:LqA0/a$a;

    .line 4
    .line 5
    return-void
.end method
