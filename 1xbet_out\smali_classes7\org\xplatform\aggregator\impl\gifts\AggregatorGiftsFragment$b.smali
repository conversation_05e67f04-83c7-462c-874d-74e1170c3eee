.class public final Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;
.super Landroidx/recyclerview/widget/RecyclerView$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->Q3()Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0019\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\t*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u0008\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0007J\u001f\u0010\t\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\t\u0010\u0007J\'\u0010\u000c\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u00022\u0006\u0010\u000b\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "org/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "",
        "positionStart",
        "itemCount",
        "",
        "onItemRangeChanged",
        "(II)V",
        "onItemRangeInserted",
        "onItemRangeRemoved",
        "fromPosition",
        "toPosition",
        "onItemRangeMoved",
        "(III)V",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onItemRangeChanged(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->E3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeInserted(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->E3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onItemRangeMoved(III)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->E3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 7
    .line 8
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->D3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onItemRangeRemoved(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->E3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment$b;->a:Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;

    .line 7
    .line 8
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;->D3(Lorg/xplatform/aggregator/impl/gifts/AggregatorGiftsFragment;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
