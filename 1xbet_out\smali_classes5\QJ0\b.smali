.class public final synthetic LQJ0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LB4/a;

    invoke-static {p1}, Lorg/xbet/statistic/player/impl/player/players_statistic_cricket/presentation/results/adapter/HeaderAdapterViewHolderKt;->b(LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
