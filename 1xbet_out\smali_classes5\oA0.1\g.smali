.class public final synthetic LoA0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(LB4/a;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LoA0/g;->a:LB4/a;

    iput-object p2, p0, LoA0/g;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LoA0/g;->a:LB4/a;

    iget-object v1, p0, LoA0/g;->b:Ljava/lang/String;

    check-cast p1, LvX0/f;

    invoke-static {v0, v1, p1}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/stadiuminfo/viewholders/ItemInfoViewHolderKt;->b(LB4/a;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
