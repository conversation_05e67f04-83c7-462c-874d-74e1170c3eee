.class public interface abstract Lj4/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lj4/h;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Lcom/github/mikephil/charting/data/Entry;",
        ">",
        "Ljava/lang/Object;",
        "Lj4/h<",
        "TT;>;"
    }
.end annotation


# virtual methods
.method public abstract B0()Z
.end method

.method public abstract C()I
.end method

.method public abstract X()I
.end method

.method public abstract a0()F
.end method

.method public abstract f()Landroid/graphics/drawable/Drawable;
.end method
