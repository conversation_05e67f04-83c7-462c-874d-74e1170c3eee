.class public final Lorg/xplatform/aggregator/impl/gifts/usecases/h;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J(\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\nH\u0086B\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/gifts/usecases/h;",
        "",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
        "promoInteractor",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;)V",
        "",
        "currentAccountId",
        "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
        "state",
        "",
        "bonusId",
        "Lxa1/b;",
        "a",
        "(JLorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/h;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(JLorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .param p3    # Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lxa1/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/gifts/usecases/h;->a:Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;

    .line 2
    .line 3
    move-wide v1, p1

    .line 4
    move-object v4, p3

    .line 5
    move v3, p4

    .line 6
    move-object v5, p5

    .line 7
    invoke-virtual/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/gifts/usecases/AggregatorPromoInteractor;->j(JILorg/xplatform/aggregator/impl/promo/domain/models/StatusBonus;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
