.class public Lk4/a;
.super Lk4/b;
.source "SourceFile"


# static fields
.field public static i:Lp4/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lp4/f<",
            "Lk4/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lk4/a;

    .line 2
    .line 3
    const/4 v4, 0x0

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v1, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    invoke-direct/range {v0 .. v5}, Lk4/a;-><init>(Lp4/j;FFLp4/g;Landroid/view/View;)V

    .line 9
    .line 10
    .line 11
    const/4 v1, 0x2

    .line 12
    invoke-static {v1, v0}, Lp4/f;->a(ILp4/f$a;)Lp4/f;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sput-object v0, Lk4/a;->i:Lp4/f;

    .line 17
    .line 18
    const/high16 v1, 0x3f000000    # 0.5f

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Lp4/f;->g(F)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public constructor <init>(Lp4/j;FFLp4/g;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p5}, Lk4/b;-><init>(Lp4/j;FFLp4/g;Landroid/view/View;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static b(Lp4/j;FFLp4/g;Landroid/view/View;)Lk4/a;
    .locals 1

    .line 1
    sget-object v0, Lk4/a;->i:Lp4/f;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp4/f;->b()Lp4/f$a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lk4/a;

    .line 8
    .line 9
    iput-object p0, v0, Lk4/b;->d:Lp4/j;

    .line 10
    .line 11
    iput p1, v0, Lk4/b;->e:F

    .line 12
    .line 13
    iput p2, v0, Lk4/b;->f:F

    .line 14
    .line 15
    iput-object p3, v0, Lk4/b;->g:Lp4/g;

    .line 16
    .line 17
    iput-object p4, v0, Lk4/b;->h:Landroid/view/View;

    .line 18
    .line 19
    return-object v0
.end method

.method public static c(Lk4/a;)V
    .locals 1

    .line 1
    sget-object v0, Lk4/a;->i:Lp4/f;

    .line 2
    .line 3
    invoke-virtual {v0, p0}, Lp4/f;->c(Lp4/f$a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lp4/f$a;
    .locals 6

    .line 1
    new-instance v0, Lk4/a;

    .line 2
    .line 3
    iget-object v1, p0, Lk4/b;->d:Lp4/j;

    .line 4
    .line 5
    iget v2, p0, Lk4/b;->e:F

    .line 6
    .line 7
    iget v3, p0, Lk4/b;->f:F

    .line 8
    .line 9
    iget-object v4, p0, Lk4/b;->g:Lp4/g;

    .line 10
    .line 11
    iget-object v5, p0, Lk4/b;->h:Landroid/view/View;

    .line 12
    .line 13
    invoke-direct/range {v0 .. v5}, Lk4/a;-><init>(Lp4/j;FFLp4/g;Landroid/view/View;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public run()V
    .locals 3

    .line 1
    iget-object v0, p0, Lk4/b;->c:[F

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    iget v2, p0, Lk4/b;->e:F

    .line 5
    .line 6
    aput v2, v0, v1

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    iget v2, p0, Lk4/b;->f:F

    .line 10
    .line 11
    aput v2, v0, v1

    .line 12
    .line 13
    iget-object v1, p0, Lk4/b;->g:Lp4/g;

    .line 14
    .line 15
    invoke-virtual {v1, v0}, Lp4/g;->k([F)V

    .line 16
    .line 17
    .line 18
    iget-object v0, p0, Lk4/b;->d:Lp4/j;

    .line 19
    .line 20
    iget-object v1, p0, Lk4/b;->c:[F

    .line 21
    .line 22
    iget-object v2, p0, Lk4/b;->h:Landroid/view/View;

    .line 23
    .line 24
    invoke-virtual {v0, v1, v2}, Lp4/j;->e([FLandroid/view/View;)V

    .line 25
    .line 26
    .line 27
    invoke-static {p0}, Lk4/a;->c(Lk4/a;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method
