.class public final Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "gameBonus",
        "Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;",
        "a",
        "(Lorg/xbet/games_section/api/models/GameBonus;)Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/games_section/api/models/GameBonus;)Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;
    .locals 1
    .param p1    # Lorg/xbet/games_section/api/models/GameBonus;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/spin_and_win/presentation/holder/SpinAndWinFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p1}, Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;->L3(Lorg/xbet/games_section/api/models/GameBonus;)V

    .line 7
    .line 8
    .line 9
    return-object v0
.end method
