.class public final synthetic LIG0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(LB4/a;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LIG0/i;->a:LB4/a;

    iput-object p2, p0, LIG0/i;->b:L<PERSON><PERSON>/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LIG0/i;->a:LB4/a;

    iget-object v1, p0, LIG0/i;->b:L<PERSON><PERSON>/jvm/functions/Function1;

    check-cast p1, <PERSON><PERSON><PERSON>/util/List;

    invoke-static {v0, v1, p1}, Lorg/xbet/statistic/lineup/presentation/adapters/delegate/PlayerAdapterDelegateKt;->b(LB4/a;Lkotlin/jvm/functions/Function1;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
