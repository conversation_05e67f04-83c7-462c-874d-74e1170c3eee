.class final Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.uikit_sport.compose.sport_event_card.common.bottom_block.ui.markets.header.MarketHeaderKt$AccordionButton$1$1"
    f = "MarketHeader.kt"
    l = {
        0xac
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $isTurnOver$delegate:Landroidx/compose/runtime/k0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/runtime/k0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $rotationAngle:Landroidx/compose/animation/core/Animatable;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/compose/animation/core/Animatable<",
            "Ljava/lang/Float;",
            "Landroidx/compose/animation/core/k;",
            ">;"
        }
    .end annotation
.end field

.field label:I


# direct methods
.method public constructor <init>(Landroidx/compose/animation/core/Animatable;Landroidx/compose/runtime/k0;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/animation/core/Animatable<",
            "Ljava/lang/Float;",
            "Landroidx/compose/animation/core/k;",
            ">;",
            "Landroidx/compose/runtime/k0<",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$rotationAngle:Landroidx/compose/animation/core/Animatable;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$isTurnOver$delegate:Landroidx/compose/runtime/k0;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;

    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$rotationAngle:Landroidx/compose/animation/core/Animatable;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$isTurnOver$delegate:Landroidx/compose/runtime/k0;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;-><init>(Landroidx/compose/animation/core/Animatable;Landroidx/compose/runtime/k0;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$rotationAngle:Landroidx/compose/animation/core/Animatable;

    .line 28
    .line 29
    iget-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->$isTurnOver$delegate:Landroidx/compose/runtime/k0;

    .line 30
    .line 31
    invoke-static {p1}, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/a;->b(Landroidx/compose/runtime/k0;)Z

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    if-eqz p1, :cond_2

    .line 36
    .line 37
    const/high16 p1, 0x43340000    # 180.0f

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_2
    const/4 p1, 0x0

    .line 41
    :goto_0
    invoke-static {p1}, LHc/a;->d(F)Ljava/lang/Float;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    const/4 v3, 0x0

    .line 46
    const/4 v4, 0x6

    .line 47
    const/16 v5, 0x1f4

    .line 48
    .line 49
    const/4 v6, 0x0

    .line 50
    invoke-static {v5, v3, v6, v4, v6}, Landroidx/compose/animation/core/h;->n(IILandroidx/compose/animation/core/B;ILjava/lang/Object;)Landroidx/compose/animation/core/f0;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    iput v2, p0, Lorg/xbet/uikit_sport/compose/sport_event_card/common/bottom_block/ui/markets/header/MarketHeaderKt$AccordionButton$1$1;->label:I

    .line 55
    .line 56
    const/4 v4, 0x0

    .line 57
    const/4 v5, 0x0

    .line 58
    const/16 v7, 0xc

    .line 59
    .line 60
    const/4 v8, 0x0

    .line 61
    move-object v6, p0

    .line 62
    move-object v2, p1

    .line 63
    invoke-static/range {v1 .. v8}, Landroidx/compose/animation/core/Animatable;->f(Landroidx/compose/animation/core/Animatable;Ljava/lang/Object;Landroidx/compose/animation/core/g;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    if-ne p1, v0, :cond_3

    .line 68
    .line 69
    return-object v0

    .line 70
    :cond_3
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object p1
.end method
