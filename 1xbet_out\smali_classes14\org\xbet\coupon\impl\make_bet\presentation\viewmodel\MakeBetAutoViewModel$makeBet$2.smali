.class final Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.coupon.impl.make_bet.presentation.viewmodel.MakeBetAutoViewModel$makeBet$2"
    f = "MakeBetAutoViewModel.kt"
    l = {
        0x1c5,
        0x1d7,
        0x1d6,
        0x1d5,
        0x1e5
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->C5(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $isApprovedBet:Z

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iput-boolean p2, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->$isApprovedBet:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;

    iget-object v0, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    iget-boolean v1, p0, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->$isApprovedBet:Z

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;-><init>(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;ZLkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v11, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v12

    .line 7
    iget v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 8
    .line 9
    const/4 v13, 0x0

    .line 10
    const/4 v14, 0x5

    .line 11
    const/4 v15, 0x4

    .line 12
    const/4 v1, 0x3

    .line 13
    const/4 v2, 0x2

    .line 14
    const/4 v3, 0x1

    .line 15
    if-eqz v0, :cond_5

    .line 16
    .line 17
    if-eq v0, v3, :cond_4

    .line 18
    .line 19
    if-eq v0, v2, :cond_3

    .line 20
    .line 21
    if-eq v0, v1, :cond_2

    .line 22
    .line 23
    if-eq v0, v15, :cond_1

    .line 24
    .line 25
    if-ne v0, v14, :cond_0

    .line 26
    .line 27
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto/16 :goto_5

    .line 31
    .line 32
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 33
    .line 34
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 35
    .line 36
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    throw v0

    .line 40
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    move-object/from16 v0, p1

    .line 44
    .line 45
    goto/16 :goto_3

    .line 46
    .line 47
    :cond_2
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v0, Lorg/xbet/betting/core/make_bet/domain/usecases/MakeAutoBetUseCase;

    .line 50
    .line 51
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    move-object v14, v0

    .line 55
    move-object/from16 v0, p1

    .line 56
    .line 57
    goto/16 :goto_2

    .line 58
    .line 59
    :cond_3
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v0, Lorg/xbet/coupon/impl/make_bet/domain/scenario/CreateBetDataModelScenario;

    .line 62
    .line 63
    iget-object v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 64
    .line 65
    check-cast v2, Lorg/xbet/betting/core/make_bet/domain/usecases/MakeAutoBetUseCase;

    .line 66
    .line 67
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    move-object v3, v2

    .line 71
    move-object/from16 v2, p1

    .line 72
    .line 73
    goto/16 :goto_1

    .line 74
    .line 75
    :cond_4
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 76
    .line 77
    .line 78
    move-object/from16 v0, p1

    .line 79
    .line 80
    goto :goto_0

    .line 81
    :cond_5
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 85
    .line 86
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->P3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/ui_common/utils/internet/a;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    iput v3, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 95
    .line 96
    invoke-static {v0, v11}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object v0

    .line 100
    if-ne v0, v12, :cond_6

    .line 101
    .line 102
    goto/16 :goto_4

    .line 103
    .line 104
    :cond_6
    :goto_0
    check-cast v0, Ljava/lang/Boolean;

    .line 105
    .line 106
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 107
    .line 108
    .line 109
    move-result v0

    .line 110
    if-nez v0, :cond_7

    .line 111
    .line 112
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 113
    .line 114
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->p4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    new-instance v1, LBx/a$c;

    .line 119
    .line 120
    new-instance v2, Ly01/g;

    .line 121
    .line 122
    sget-object v3, Ly01/i$a;->a:Ly01/i$a;

    .line 123
    .line 124
    iget-object v4, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 125
    .line 126
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->l4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)LHX0/e;

    .line 127
    .line 128
    .line 129
    move-result-object v4

    .line 130
    sget v5, Lpb/k;->no_connection_title_with_hyphen:I

    .line 131
    .line 132
    const/4 v6, 0x0

    .line 133
    new-array v7, v6, [Ljava/lang/Object;

    .line 134
    .line 135
    invoke-interface {v4, v5, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 136
    .line 137
    .line 138
    move-result-object v4

    .line 139
    iget-object v5, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 140
    .line 141
    invoke-static {v5}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->l4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)LHX0/e;

    .line 142
    .line 143
    .line 144
    move-result-object v5

    .line 145
    sget v7, Lpb/k;->no_connection_description:I

    .line 146
    .line 147
    new-array v6, v6, [Ljava/lang/Object;

    .line 148
    .line 149
    invoke-interface {v5, v7, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 150
    .line 151
    .line 152
    move-result-object v5

    .line 153
    const/16 v9, 0x38

    .line 154
    .line 155
    const/4 v10, 0x0

    .line 156
    const/4 v6, 0x0

    .line 157
    const/4 v7, 0x0

    .line 158
    const/4 v8, 0x0

    .line 159
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 160
    .line 161
    .line 162
    invoke-direct {v1, v2}, LBx/a$c;-><init>(Ly01/g;)V

    .line 163
    .line 164
    .line 165
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 166
    .line 167
    .line 168
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 169
    .line 170
    return-object v0

    .line 171
    :cond_7
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 172
    .line 173
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->f4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 174
    .line 175
    .line 176
    move-result-object v0

    .line 177
    sget-object v3, LEx/a$b;->a:LEx/a$b;

    .line 178
    .line 179
    invoke-interface {v0, v3}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 180
    .line 181
    .line 182
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 183
    .line 184
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->V3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/w0;

    .line 185
    .line 186
    .line 187
    move-result-object v0

    .line 188
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/w0;->invoke()I

    .line 189
    .line 190
    .line 191
    move-result v0

    .line 192
    iget-object v3, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 193
    .line 194
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->Y3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Ltw/d;

    .line 195
    .line 196
    .line 197
    move-result-object v3

    .line 198
    invoke-interface {v3}, Ltw/d;->invoke()Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;

    .line 199
    .line 200
    .line 201
    move-result-object v3

    .line 202
    invoke-virtual {v3}, Lorg/xbet/betting/core/zip/domain/model/CouponTypeModel;->toInteger()I

    .line 203
    .line 204
    .line 205
    move-result v3

    .line 206
    if-eq v0, v3, :cond_8

    .line 207
    .line 208
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 209
    .line 210
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->k4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/S2;

    .line 211
    .line 212
    .line 213
    move-result-object v0

    .line 214
    invoke-virtual {v0}, Lorg/xbet/coupon/impl/coupon/domain/usecases/S2;->invoke()V

    .line 215
    .line 216
    .line 217
    :cond_8
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 218
    .line 219
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->i4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/betting/core/make_bet/domain/usecases/MakeAutoBetUseCase;

    .line 220
    .line 221
    .line 222
    move-result-object v0

    .line 223
    iget-object v3, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 224
    .line 225
    invoke-static {v3}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->Q3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/make_bet/domain/scenario/CreateBetDataModelScenario;

    .line 226
    .line 227
    .line 228
    move-result-object v3

    .line 229
    iget-object v4, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 230
    .line 231
    iput-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 232
    .line 233
    iput-object v3, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 234
    .line 235
    iput v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 236
    .line 237
    invoke-static {v4, v11}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->o4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 238
    .line 239
    .line 240
    move-result-object v2

    .line 241
    if-ne v2, v12, :cond_9

    .line 242
    .line 243
    goto/16 :goto_4

    .line 244
    .line 245
    :cond_9
    move-object/from16 v16, v3

    .line 246
    .line 247
    move-object v3, v0

    .line 248
    move-object/from16 v0, v16

    .line 249
    .line 250
    :goto_1
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 251
    .line 252
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 253
    .line 254
    .line 255
    move-result-wide v4

    .line 256
    iget-object v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 257
    .line 258
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->J3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 259
    .line 260
    .line 261
    move-result-object v2

    .line 262
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 263
    .line 264
    .line 265
    move-result-object v2

    .line 266
    check-cast v2, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;

    .line 267
    .line 268
    invoke-virtual {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/model/StepInputUiModel;->e()D

    .line 269
    .line 270
    .line 271
    move-result-wide v6

    .line 272
    move-wide v8, v4

    .line 273
    iget-boolean v5, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->$isApprovedBet:Z

    .line 274
    .line 275
    move-wide v9, v8

    .line 276
    new-instance v8, Lfo/b;

    .line 277
    .line 278
    iget-object v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 279
    .line 280
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->O3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 281
    .line 282
    .line 283
    move-result-object v2

    .line 284
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 285
    .line 286
    .line 287
    move-result-object v2

    .line 288
    check-cast v2, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;

    .line 289
    .line 290
    invoke-virtual {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/model/CoefStepInputUiModel;->c()Ljava/math/BigDecimal;

    .line 291
    .line 292
    .line 293
    move-result-object v2

    .line 294
    invoke-virtual {v2}, Ljava/math/BigDecimal;->doubleValue()D

    .line 295
    .line 296
    .line 297
    move-result-wide v14

    .line 298
    iget-object v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 299
    .line 300
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->s4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lh9/a;

    .line 301
    .line 302
    .line 303
    move-result-object v2

    .line 304
    invoke-virtual {v2}, Lh9/a;->d()Z

    .line 305
    .line 306
    .line 307
    move-result v2

    .line 308
    iget-object v4, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 309
    .line 310
    invoke-static {v4}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->s4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lh9/a;

    .line 311
    .line 312
    .line 313
    move-result-object v4

    .line 314
    invoke-virtual {v4}, Lh9/a;->e()Z

    .line 315
    .line 316
    .line 317
    move-result v4

    .line 318
    invoke-direct {v8, v14, v15, v2, v4}, Lfo/b;-><init>(DZZ)V

    .line 319
    .line 320
    .line 321
    iget-object v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 322
    .line 323
    invoke-static {v2}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->U3(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lorg/xbet/coupon/impl/coupon/domain/usecases/u0;

    .line 324
    .line 325
    .line 326
    move-result-object v2

    .line 327
    invoke-virtual {v2}, Lorg/xbet/coupon/impl/coupon/domain/usecases/u0;->a()Ljava/lang/String;

    .line 328
    .line 329
    .line 330
    move-result-object v2

    .line 331
    iput-object v3, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 332
    .line 333
    iput-object v13, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$1:Ljava/lang/Object;

    .line 334
    .line 335
    iput v1, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 336
    .line 337
    move-object v1, v3

    .line 338
    move-wide v3, v6

    .line 339
    const/4 v6, 0x0

    .line 340
    const/4 v7, 0x0

    .line 341
    move-object v14, v2

    .line 342
    move-wide/from16 v16, v9

    .line 343
    .line 344
    move-object v9, v1

    .line 345
    move-wide/from16 v1, v16

    .line 346
    .line 347
    const/4 v10, 0x0

    .line 348
    move-object/from16 v16, v14

    .line 349
    .line 350
    move-object v14, v9

    .line 351
    move-object/from16 v9, v16

    .line 352
    .line 353
    invoke-virtual/range {v0 .. v11}, Lorg/xbet/coupon/impl/make_bet/domain/scenario/CreateBetDataModelScenario;->c(JDZZLjava/lang/String;Lfo/b;Ljava/lang/String;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 354
    .line 355
    .line 356
    move-result-object v0

    .line 357
    if-ne v0, v12, :cond_a

    .line 358
    .line 359
    goto :goto_4

    .line 360
    :cond_a
    :goto_2
    check-cast v0, Lfo/h;

    .line 361
    .line 362
    iput-object v13, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->L$0:Ljava/lang/Object;

    .line 363
    .line 364
    const/4 v1, 0x4

    .line 365
    iput v1, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 366
    .line 367
    invoke-virtual {v14, v0, v11}, Lorg/xbet/betting/core/make_bet/domain/usecases/MakeAutoBetUseCase;->a(Lfo/h;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 368
    .line 369
    .line 370
    move-result-object v0

    .line 371
    if-ne v0, v12, :cond_b

    .line 372
    .line 373
    goto :goto_4

    .line 374
    :cond_b
    :goto_3
    check-cast v0, Lfo/d;

    .line 375
    .line 376
    iget-object v1, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 377
    .line 378
    const/4 v2, 0x5

    .line 379
    iput v2, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->label:I

    .line 380
    .line 381
    invoke-static {v1, v0, v11}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->t4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;Lfo/d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 382
    .line 383
    .line 384
    move-result-object v0

    .line 385
    if-ne v0, v12, :cond_c

    .line 386
    .line 387
    :goto_4
    return-object v12

    .line 388
    :cond_c
    :goto_5
    iget-object v0, v11, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel$makeBet$2;->this$0:Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;

    .line 389
    .line 390
    invoke-static {v0}, Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;->f4(Lorg/xbet/coupon/impl/make_bet/presentation/viewmodel/MakeBetAutoViewModel;)Lkotlinx/coroutines/flow/V;

    .line 391
    .line 392
    .line 393
    move-result-object v0

    .line 394
    sget-object v1, LEx/a$a;->a:LEx/a$a;

    .line 395
    .line 396
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 397
    .line 398
    .line 399
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 400
    .line 401
    return-object v0
.end method
