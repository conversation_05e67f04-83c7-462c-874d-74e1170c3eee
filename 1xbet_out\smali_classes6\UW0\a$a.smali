.class public final LUW0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LUW0/e$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LUW0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LUW0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LUW0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LTW0/a;)LUW0/e;
    .locals 2

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    new-instance v0, LUW0/a$b;

    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    invoke-direct {v0, p1, v1}, LUW0/a$b;-><init>(LTW0/a;LUW0/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
