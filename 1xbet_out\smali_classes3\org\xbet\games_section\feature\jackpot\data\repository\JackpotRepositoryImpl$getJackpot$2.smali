.class final Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.games_section.feature.jackpot.data.repository.JackpotRepositoryImpl$getJackpot$2"
    f = "JackpotRepositoryImpl.kt"
    l = {
        0x15
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Pair<",
        "+",
        "LP40/c;",
        "+",
        "Ljava/lang/Long;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0002\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "Lkotlin/Pair;",
        "LP40/c;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)Lkotlin/Pair;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;

    iget-object v0, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    invoke-direct {p1, v0, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;-><init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Pair<",
            "LP40/c;",
            "Ljava/lang/Long;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 5

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-object p1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;->d(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;)Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    new-instance v1, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;

    .line 34
    .line 35
    iget-object v3, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->this$0:Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;

    .line 36
    .line 37
    const/4 v4, 0x0

    .line 38
    invoke-direct {v1, v3, v4}, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2$1;-><init>(Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 39
    .line 40
    .line 41
    iput v2, p0, Lorg/xbet/games_section/feature/jackpot/data/repository/JackpotRepositoryImpl$getJackpot$2;->label:I

    .line 42
    .line 43
    invoke-virtual {p1, v1, p0}, Lcom/xbet/onexuser/domain/managers/TokenRefresher;->j(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    if-ne p1, v0, :cond_2

    .line 48
    .line 49
    return-object v0

    .line 50
    :cond_2
    return-object p1
.end method
