.class public final synthetic Lc21/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;

.field public final synthetic c:LY11/a;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;LY11/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lc21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

    iput-object p2, p0, Lc21/c;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;

    iput-object p3, p0, Lc21/c;->c:LY11/a;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lc21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;

    iget-object v1, p0, Lc21/c;->b:Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;

    iget-object v2, p0, Lc21/c;->c:LY11/a;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;->a(Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilled;Lorg/xbet/uikit_aggregator/aggregatorFilter/view/tabsFilled/AggregatorTabFilledList;LY11/a;Landroid/view/View;)V

    return-void
.end method
