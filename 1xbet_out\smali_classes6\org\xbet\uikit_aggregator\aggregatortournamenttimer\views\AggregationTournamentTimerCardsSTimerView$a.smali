.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0011\n\u0002\u0010\u0007\n\u0002\u0008\u000f\u0008\u0002\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\"\u0010\r\u001a\u00020\u00068\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0007\u0010\u0008\u001a\u0004\u0008\t\u0010\n\"\u0004\u0008\u000b\u0010\u000cR\u0017\u0010\u0013\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012R\u0017\u0010\u0016\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0010\u001a\u0004\u0008\u0015\u0010\u0012R\u0017\u0010\u0019\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0010\u001a\u0004\u0008\u0018\u0010\u0012R\u0017\u0010\u001b\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u0010\u001a\u0004\u0008\u0014\u0010\u0012R\"\u0010\u001f\u001a\u00020\u000e8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001c\u0010\u0010\u001a\u0004\u0008\u0007\u0010\u0012\"\u0004\u0008\u001d\u0010\u001eR\u0017\u0010$\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008\u0017\u0010#R\u0017\u0010%\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\"\u001a\u0004\u0008\u001c\u0010#R\u0017\u0010\'\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008&\u0010\"\u001a\u0004\u0008\u000f\u0010#R\u0017\u0010(\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u0010\u001a\u0004\u0008!\u0010\u0012R\u0017\u0010*\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008)\u0010\u0010\u001a\u0004\u0008\u001a\u0010\u0012R\u0017\u0010+\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0011\u0010\"\u001a\u0004\u0008&\u0010#R\u0017\u0010-\u001a\u00020 8\u0006\u00a2\u0006\u000c\n\u0004\u0008,\u0010\"\u001a\u0004\u0008)\u0010#R\u0017\u0010.\u001a\u00020\u000e8\u0006\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\u0010\u001a\u0004\u0008,\u0010\u0012\u00a8\u0006/"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;",
        "",
        "Landroid/content/Context;",
        "context",
        "<init>",
        "(Landroid/content/Context;)V",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;",
        "n",
        "()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;",
        "p",
        "(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;)V",
        "timerSizeType",
        "",
        "b",
        "I",
        "l",
        "()I",
        "timeCellBackgroundWidth",
        "c",
        "j",
        "timeCellBackgroundHeight",
        "d",
        "h",
        "shortTimerWidth",
        "e",
        "longTimerWidth",
        "f",
        "o",
        "(I)V",
        "currentTimerWidth",
        "",
        "g",
        "F",
        "()F",
        "maxDayTitleTextSize",
        "minDayTitleTextSize",
        "i",
        "dayTitleTextSizeShrinkStep",
        "minDayTitleTextWidth",
        "k",
        "maxDayTitleTextWidth",
        "timeBackgroundCornerRadius",
        "m",
        "timeCellBackgroundSpace",
        "timeCellSeparatorSpace",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public f:I

.field public final g:F

.field public final h:F

.field public final i:F

.field public final j:I

.field public final k:I

.field public final l:F

.field public final m:F

.field public final n:I


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;->SHORT:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;

    .line 5
    .line 6
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;

    .line 7
    .line 8
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    sget v1, LlZ0/g;->size_28:I

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->b:I

    .line 19
    .line 20
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sget v1, LlZ0/g;->size_36:I

    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->c:I

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sget v1, LlZ0/g;->size_232:I

    .line 37
    .line 38
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->d:I

    .line 43
    .line 44
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sget v2, LlZ0/g;->size_262:I

    .line 49
    .line 50
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    iput v1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->e:I

    .line 55
    .line 56
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->f:I

    .line 57
    .line 58
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    sget v1, LlZ0/g;->text_16:I

    .line 63
    .line 64
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    int-to-float v0, v0

    .line 69
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->g:F

    .line 70
    .line 71
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    sget v1, LlZ0/g;->text_8:I

    .line 76
    .line 77
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    int-to-float v0, v0

    .line 82
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->h:F

    .line 83
    .line 84
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    sget v1, LlZ0/g;->text_1:I

    .line 89
    .line 90
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 91
    .line 92
    .line 93
    move-result v0

    .line 94
    int-to-float v0, v0

    .line 95
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->i:F

    .line 96
    .line 97
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    sget v1, LlZ0/g;->size_12:I

    .line 102
    .line 103
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 104
    .line 105
    .line 106
    move-result v0

    .line 107
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->j:I

    .line 108
    .line 109
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 110
    .line 111
    .line 112
    move-result-object v0

    .line 113
    sget v1, LlZ0/g;->size_18:I

    .line 114
    .line 115
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 116
    .line 117
    .line 118
    move-result v0

    .line 119
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->k:I

    .line 120
    .line 121
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    sget v1, LlZ0/g;->radius_6:I

    .line 126
    .line 127
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 128
    .line 129
    .line 130
    move-result v0

    .line 131
    int-to-float v0, v0

    .line 132
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->l:F

    .line 133
    .line 134
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    sget v1, LlZ0/g;->space_2:I

    .line 139
    .line 140
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 141
    .line 142
    .line 143
    move-result v0

    .line 144
    int-to-float v0, v0

    .line 145
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->m:F

    .line 146
    .line 147
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    sget v0, LlZ0/g;->space_4:I

    .line 152
    .line 153
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->n:I

    .line 158
    .line 159
    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->f:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->i:F

    .line 2
    .line 3
    return v0
.end method

.method public final c()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public final d()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->g:F

    .line 2
    .line 3
    return v0
.end method

.method public final e()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->k:I

    .line 2
    .line 3
    return v0
.end method

.method public final f()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->h:F

    .line 2
    .line 3
    return v0
.end method

.method public final g()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public final h()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final i()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->l:F

    .line 2
    .line 3
    return v0
.end method

.method public final j()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->m:F

    .line 2
    .line 3
    return v0
.end method

.method public final l()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final m()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->n:I

    .line 2
    .line 3
    return v0
.end method

.method public final n()Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->f:I

    .line 2
    .line 3
    return-void
.end method

.method public final p(Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$a;->a:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/views/AggregationTournamentTimerCardsSTimerView$TimeSize;

    .line 2
    .line 3
    return-void
.end method
