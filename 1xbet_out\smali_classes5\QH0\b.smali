.class public final LQH0/b;
.super Ljava/lang/Object;


# static fields
.field public static fragment_bottom_sheet_dialog_additional_info:I = 0x7f0d0358

.field public static fragment_bottom_sheet_info:I = 0x7f0d035b

.field public static fragment_bottom_sheet_selectors:I = 0x7f0d035d

.field public static fragment_full_description:I = 0x7f0d03a4

.field public static fragment_injuries:I = 0x7f0d03c7

.field public static fragment_player_career:I = 0x7f0d0417

.field public static fragment_player_last_game:I = 0x7f0d0419

.field public static fragment_player_medals:I = 0x7f0d041a

.field public static fragment_player_transfers:I = 0x7f0d041c

.field public static fragment_players_statistic:I = 0x7f0d041f

.field public static fragment_players_statistic_cricket:I = 0x7f0d0420

.field public static fragment_players_statistic_cricket_results:I = 0x7f0d0421

.field public static fragment_referee_card_last_game:I = 0x7f0d0457

.field public static fragment_referee_list:I = 0x7f0d0458

.field public static fragment_referee_team:I = 0x7f0d0459

.field public static fragment_referee_tour:I = 0x7f0d045a

.field public static fragment_refreree_card_menu:I = 0x7f0d0460

.field public static fragment_statistic_kabaddi_top_players:I = 0x7f0d04a1

.field public static fragment_statistic_top_players:I = 0x7f0d04a7

.field public static item_injury:I = 0x7f0d0612

.field public static item_player_career_chip:I = 0x7f0d064c

.field public static item_player_last_game:I = 0x7f0d064d

.field public static item_player_transfer:I = 0x7f0d064e

.field public static item_top_players_pair_statistic:I = 0x7f0d0706

.field public static item_top_players_statistic_block:I = 0x7f0d0707

.field public static kabaddi_shimmer:I = 0x7f0d0736

.field public static layout_kabaddi_top_players:I = 0x7f0d0747

.field public static shimmer_item_line_up:I = 0x7f0d08ca

.field public static shimmer_item_player_transfers:I = 0x7f0d08ce

.field public static shimmer_item_players_statistic:I = 0x7f0d08cf

.field public static shimmer_squad_team_item:I = 0x7f0d08e6

.field public static top_players_shimmer:I = 0x7f0d0a37

.field public static vh_additional_info:I = 0x7f0d0a80

.field public static vh_career_data:I = 0x7f0d0a86

.field public static vh_info_item:I = 0x7f0d0aa2

.field public static vh_players_statistic_cricket_results_info:I = 0x7f0d0ab5

.field public static vh_players_statistic_cricket_results_title:I = 0x7f0d0ab6

.field public static vh_referees_list_item:I = 0x7f0d0ac4

.field public static vh_selector_item:I = 0x7f0d0ac8

.field public static view_holder_player_medals_item:I = 0x7f0d0b3d

.field public static view_holder_referee_card_last_game:I = 0x7f0d0b40

.field public static winter_full_description_item:I = 0x7f0d0bcd


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
