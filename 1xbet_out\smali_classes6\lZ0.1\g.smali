.class public final LlZ0/g;
.super Ljava/lang/Object;


# static fields
.field public static card_size_30:I = 0x7f070080

.field public static card_size_40:I = 0x7f070081

.field public static drop_down_card_width:I = 0x7f070132

.field public static extra_extra_large_horizontal_margin_dynamic:I = 0x7f07015f

.field public static extra_large_horizontal_margin_dynamic:I = 0x7f070160

.field public static extra_small_horizontal_margin_dynamic:I = 0x7f070161

.field public static large_horizontal_margin_dynamic:I = 0x7f0701ca

.field public static line_height_10:I = 0x7f0701cb

.field public static line_height_12:I = 0x7f0701cc

.field public static line_height_14:I = 0x7f0701cd

.field public static line_height_16:I = 0x7f0701ce

.field public static line_height_18:I = 0x7f0701cf

.field public static line_height_20:I = 0x7f0701d0

.field public static line_height_22:I = 0x7f0701d1

.field public static line_height_24:I = 0x7f0701d2

.field public static line_height_28:I = 0x7f0701d3

.field public static line_height_40:I = 0x7f0701d4

.field public static line_height_48:I = 0x7f0701d5

.field public static medium_horizontal_margin_dynamic:I = 0x7f070393

.field public static min_selected_navigation_tab_width:I = 0x7f070398

.field public static one_x_games_top_games_collection_skeleton_height:I = 0x7f070483

.field public static radius_10:I = 0x7f07049a

.field public static radius_12:I = 0x7f07049b

.field public static radius_14:I = 0x7f07049c

.field public static radius_16:I = 0x7f07049d

.field public static radius_2:I = 0x7f07049e

.field public static radius_20:I = 0x7f07049f

.field public static radius_24:I = 0x7f0704a0

.field public static radius_32:I = 0x7f0704a1

.field public static radius_36:I = 0x7f0704a2

.field public static radius_4:I = 0x7f0704a3

.field public static radius_6:I = 0x7f0704a4

.field public static radius_8:I = 0x7f0704a5

.field public static radius_full:I = 0x7f0704a6

.field public static size_1:I = 0x7f070509

.field public static size_10:I = 0x7f07050a

.field public static size_100:I = 0x7f07050b

.field public static size_104:I = 0x7f07050d

.field public static size_106:I = 0x7f07050e

.field public static size_108:I = 0x7f07050f

.field public static size_11:I = 0x7f070510

.field public static size_110:I = 0x7f070511

.field public static size_112:I = 0x7f070512

.field public static size_116:I = 0x7f070514

.field public static size_118:I = 0x7f070515

.field public static size_12:I = 0x7f070516

.field public static size_120:I = 0x7f070517

.field public static size_124:I = 0x7f070519

.field public static size_128:I = 0x7f07051a

.field public static size_132:I = 0x7f07051c

.field public static size_134:I = 0x7f07051e

.field public static size_136:I = 0x7f07051f

.field public static size_138:I = 0x7f070520

.field public static size_14:I = 0x7f070521

.field public static size_144:I = 0x7f070524

.field public static size_148:I = 0x7f070526

.field public static size_150:I = 0x7f070527

.field public static size_152:I = 0x7f070528

.field public static size_16:I = 0x7f07052c

.field public static size_160:I = 0x7f07052d

.field public static size_164:I = 0x7f07052f

.field public static size_166:I = 0x7f070530

.field public static size_168:I = 0x7f070531

.field public static size_172:I = 0x7f070532

.field public static size_176:I = 0x7f070534

.field public static size_18:I = 0x7f070536

.field public static size_182:I = 0x7f070538

.field public static size_19:I = 0x7f07053b

.field public static size_192:I = 0x7f07053d

.field public static size_196:I = 0x7f07053f

.field public static size_198:I = 0x7f070540

.field public static size_2:I = 0x7f070541

.field public static size_20:I = 0x7f070542

.field public static size_200:I = 0x7f070543

.field public static size_210:I = 0x7f070547

.field public static size_22:I = 0x7f07054a

.field public static size_224:I = 0x7f07054c

.field public static size_232:I = 0x7f07054d

.field public static size_24:I = 0x7f070550

.field public static size_240:I = 0x7f070551

.field public static size_242:I = 0x7f070552

.field public static size_250:I = 0x7f070556

.field public static size_256:I = 0x7f070559

.field public static size_258:I = 0x7f07055a

.field public static size_26:I = 0x7f07055b

.field public static size_260:I = 0x7f07055c

.field public static size_262:I = 0x7f07055d

.field public static size_270:I = 0x7f07055f

.field public static size_272:I = 0x7f070560

.field public static size_274:I = 0x7f070561

.field public static size_28:I = 0x7f070562

.field public static size_280:I = 0x7f070563

.field public static size_285:I = 0x7f070564

.field public static size_288:I = 0x7f070566

.field public static size_3:I = 0x7f070568

.field public static size_30:I = 0x7f070569

.field public static size_304:I = 0x7f07056c

.field public static size_308:I = 0x7f07056d

.field public static size_32:I = 0x7f070570

.field public static size_320:I = 0x7f070571

.field public static size_328:I = 0x7f070573

.field public static size_336:I = 0x7f070575

.field public static size_36:I = 0x7f070578

.field public static size_360:I = 0x7f070579

.field public static size_38:I = 0x7f07057b

.field public static size_4:I = 0x7f07057d

.field public static size_40:I = 0x7f07057e

.field public static size_400:I = 0x7f07057f

.field public static size_404:I = 0x7f070581

.field public static size_418:I = 0x7f070584

.field public static size_42:I = 0x7f070585

.field public static size_432:I = 0x7f070587

.field public static size_44:I = 0x7f070589

.field public static size_448:I = 0x7f07058a

.field public static size_48:I = 0x7f07058c

.field public static size_49:I = 0x7f07058d

.field public static size_5:I = 0x7f07058e

.field public static size_50:I = 0x7f07058f

.field public static size_512:I = 0x7f070592

.field public static size_52:I = 0x7f070593

.field public static size_54:I = 0x7f070594

.field public static size_56:I = 0x7f070596

.field public static size_58:I = 0x7f070598

.field public static size_6:I = 0x7f070599

.field public static size_60:I = 0x7f07059a

.field public static size_62:I = 0x7f07059b

.field public static size_64:I = 0x7f07059c

.field public static size_640:I = 0x7f07059d

.field public static size_66:I = 0x7f07059e

.field public static size_68:I = 0x7f07059f

.field public static size_70:I = 0x7f0705a0

.field public static size_72:I = 0x7f0705a1

.field public static size_74:I = 0x7f0705a2

.field public static size_8:I = 0x7f0705a5

.field public static size_80:I = 0x7f0705a6

.field public static size_82:I = 0x7f0705a7

.field public static size_84:I = 0x7f0705a8

.field public static size_86:I = 0x7f0705a9

.field public static size_88:I = 0x7f0705aa

.field public static size_90:I = 0x7f0705ab

.field public static size_92:I = 0x7f0705ac

.field public static size_94:I = 0x7f0705ad

.field public static size_96:I = 0x7f0705ae

.field public static size_98:I = 0x7f0705af

.field public static small_horizontal_margin_dynamic:I = 0x7f0705b5

.field public static space_1:I = 0x7f0705e2

.field public static space_10:I = 0x7f0705e3

.field public static space_100:I = 0x7f0705e4

.field public static space_12:I = 0x7f0705eb

.field public static space_128:I = 0x7f0705ed

.field public static space_138:I = 0x7f0705f1

.field public static space_14:I = 0x7f0705f2

.field public static space_144:I = 0x7f0705f3

.field public static space_148:I = 0x7f0705f5

.field public static space_152:I = 0x7f0705f6

.field public static space_158:I = 0x7f0705f7

.field public static space_16:I = 0x7f0705f8

.field public static space_160:I = 0x7f0705f9

.field public static space_164:I = 0x7f0705fa

.field public static space_168:I = 0x7f0705fb

.field public static space_18:I = 0x7f0705fe

.field public static space_184:I = 0x7f070600

.field public static space_192:I = 0x7f070602

.field public static space_198:I = 0x7f070604

.field public static space_2:I = 0x7f070605

.field public static space_20:I = 0x7f070606

.field public static space_22:I = 0x7f070609

.field public static space_24:I = 0x7f07060c

.field public static space_256:I = 0x7f07060d

.field public static space_26:I = 0x7f07060e

.field public static space_28:I = 0x7f070610

.field public static space_30:I = 0x7f070611

.field public static space_32:I = 0x7f070612

.field public static space_36:I = 0x7f070614

.field public static space_38:I = 0x7f070616

.field public static space_4:I = 0x7f070617

.field public static space_40:I = 0x7f070618

.field public static space_44:I = 0x7f07061a

.field public static space_46:I = 0x7f07061c

.field public static space_48:I = 0x7f07061d

.field public static space_50:I = 0x7f07061e

.field public static space_52:I = 0x7f07061f

.field public static space_56:I = 0x7f070621

.field public static space_6:I = 0x7f070622

.field public static space_60:I = 0x7f070623

.field public static space_64:I = 0x7f070625

.field public static space_68:I = 0x7f070627

.field public static space_72:I = 0x7f070629

.field public static space_74:I = 0x7f07062a

.field public static space_76:I = 0x7f07062b

.field public static space_78:I = 0x7f07062c

.field public static space_8:I = 0x7f07062d

.field public static space_80:I = 0x7f07062e

.field public static space_92:I = 0x7f070632

.field public static space_96:I = 0x7f070634

.field public static text_1:I = 0x7f070648

.field public static text_10:I = 0x7f070649

.field public static text_12:I = 0x7f07064b

.field public static text_14:I = 0x7f07064c

.field public static text_16:I = 0x7f07064d

.field public static text_18:I = 0x7f07064e

.field public static text_2:I = 0x7f07064f

.field public static text_20:I = 0x7f070650

.field public static text_24:I = 0x7f070653

.field public static text_32:I = 0x7f070656

.field public static text_40:I = 0x7f070659

.field public static text_8:I = 0x7f07065c

.field public static text_84:I = 0x7f07065d


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
