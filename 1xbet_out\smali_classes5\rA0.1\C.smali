.class public final synthetic LrA0/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LB4/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LrA0/C;->a:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LrA0/C;->a:LB4/a;

    invoke-static {v0}, Lorg/xbet/sportgame/classic/impl/presentation/adapters/gameinfo/withtimer/viewholders/ShortStatisticViewHolderKt;->a(LB4/a;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
