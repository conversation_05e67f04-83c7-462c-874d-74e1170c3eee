.class public final Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LEy0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJB\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u00152\u0006\u0010\r\u001a\u00020\u000c2\u0008\u0010\u000e\u001a\u0004\u0018\u00010\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0014\u001a\u00020\u0013H\u0096@\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J$\u0010\u001c\u001a\u00020\u001b2\u0012\u0010\u001a\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00160\u00150\u0019H\u0096@\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u000f\u0010\u001e\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ!\u0010!\u001a\u0014\u0012\u0010\u0012\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00160\u00150\u00190 H\u0016\u00a2\u0006\u0004\u0008!\u0010\"R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010#R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010$R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010%R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010&\u00a8\u0006\'"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;",
        "LEy0/a;",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lvy0/b;",
        "stageTableRemoteDataSource",
        "Luy0/a;",
        "stageTableLocalDataSource",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(Lm8/a;Lvy0/b;Luy0/a;Lc8/h;)V",
        "",
        "eventId",
        "userRegistrationCountryId",
        "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
        "enCoefView",
        "",
        "userId",
        "",
        "cutCoef",
        "",
        "LDy0/a;",
        "b",
        "(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Long;ZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "LKo0/a;",
        "actualStageTableResult",
        "",
        "c",
        "(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a",
        "()Z",
        "Lkotlinx/coroutines/flow/e;",
        "d",
        "()Lkotlinx/coroutines/flow/e;",
        "Lm8/a;",
        "Lvy0/b;",
        "Luy0/a;",
        "Lc8/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lvy0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Luy0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lm8/a;Lvy0/b;Luy0/a;Lc8/h;)V
    .locals 0
    .param p1    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lvy0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Luy0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->a:Lm8/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->b:Lvy0/b;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->c:Luy0/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->d:Lc8/h;

    .line 11
    .line 12
    return-void
.end method

.method public static final synthetic e(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Lc8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->d:Lc8/h;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Luy0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->c:Luy0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;)Lvy0/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->b:Lvy0/b;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->c:Luy0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Luy0/a;->c()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public b(ILjava/lang/Integer;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Long;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p3    # Lorg/xbet/coef_type/api/domain/models/EnCoefView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/Integer;",
            "Lorg/xbet/coef_type/api/domain/models/EnCoefView;",
            "Ljava/lang/Long;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "+",
            "LDy0/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->a:Lm8/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    move-object v2, p0

    .line 11
    move v5, p1

    .line 12
    move-object v4, p2

    .line 13
    move-object v3, p3

    .line 14
    move-object v6, p4

    .line 15
    move v7, p5

    .line 16
    invoke-direct/range {v1 .. v8}, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl$getStageTable$2;-><init>(Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;Lorg/xbet/coef_type/api/domain/models/EnCoefView;Ljava/lang/Integer;ILjava/lang/Long;ZLkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0, v1, p6}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1
.end method

.method public c(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 1
    .param p1    # LKo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->c:Luy0/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Luy0/a;->d(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    if-ne p1, p2, :cond_0

    .line 12
    .line 13
    return-object p1

    .line 14
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p1
.end method

.method public d()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LKo0/a<",
            "Ljava/util/List<",
            "LDy0/a;",
            ">;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/data/repository/StageTableRepositoryImpl;->c:Luy0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Luy0/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
