.class public final synthetic LKX0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lvc/h;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LKX0/d;->a:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LKX0/d;->a:Lkotlin/jvm/functions/Function1;

    invoke-static {v0, p1}, LKX0/m;->i(Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)LRe/b;

    move-result-object p1

    return-object p1
.end method
