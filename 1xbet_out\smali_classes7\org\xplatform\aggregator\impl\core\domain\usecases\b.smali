.class public final Lorg/xplatform/aggregator/impl/core/domain/usecases/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/api/domain/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u0096\u0002\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/core/domain/usecases/b;",
        "Lorg/xplatform/aggregator/api/domain/a;",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
        "aggregatorLocalDataSource",
        "Lz91/a;",
        "aggregatorCategoriesLocalDataSource",
        "<init>",
        "(Lorg/xplatform/aggregator/impl/core/data/datasources/a;Lz91/a;)V",
        "",
        "invoke",
        "()V",
        "a",
        "Lorg/xplatform/aggregator/impl/core/data/datasources/a;",
        "b",
        "Lz91/a;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/impl/core/data/datasources/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lz91/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/core/data/datasources/a;Lz91/a;)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/core/data/datasources/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lz91/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->b:Lz91/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public invoke()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->a:Lorg/xplatform/aggregator/impl/core/data/datasources/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/core/data/datasources/a;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->b:Lz91/a;

    .line 7
    .line 8
    invoke-virtual {v0}, Lz91/a;->g()V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->b:Lz91/a;

    .line 12
    .line 13
    invoke-virtual {v0}, Lz91/a;->e()V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/core/domain/usecases/b;->b:Lz91/a;

    .line 17
    .line 18
    invoke-virtual {v0}, Lz91/a;->h()V

    .line 19
    .line 20
    .line 21
    return-void
.end method
