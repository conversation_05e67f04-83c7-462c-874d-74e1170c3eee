.class public final Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$a;,
        Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$TemporaryResultExpiredException;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0010\u0003\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 \u0014*\u0008\u0008\u0000\u0010\u0002*\u00020\u00012\u00020\u0001:\u0002*\u001dB\u000f\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0019\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u00080\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0015\u0010\u000b\u001a\n\u0012\u0004\u0012\u00028\u0000\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001e\u0010\u000f\u001a\u00020\u000e2\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00028\u00000\u0008H\u0086@\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\r\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\r\u0010\u0014\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0015J\u0013\u0010\u0018\u001a\u00020\u0011*\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R.\u0010\u001f\u001a\u001c\u0012\u0018\u0012\u0016\u0012\u0004\u0012\u00020\u001c\u0012\n\u0012\u0008\u0012\u0004\u0012\u00028\u00000\u0008\u0018\u00010\u001b0\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u001b\u0010%\u001a\u00020 8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$R\u0018\u0010)\u001a\u0004\u0018\u00010&8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(\u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;",
        "",
        "T",
        "Lm8/a;",
        "coroutineDispatchers",
        "<init>",
        "(Lm8/a;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LKo0/a;",
        "g",
        "()Lkotlinx/coroutines/flow/e;",
        "f",
        "()LKo0/a;",
        "actualResult",
        "",
        "m",
        "(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "i",
        "()Z",
        "d",
        "()V",
        "k",
        "",
        "h",
        "(Ljava/lang/Throwable;)Z",
        "Lkotlinx/coroutines/flow/V;",
        "Lkotlin/Pair;",
        "",
        "a",
        "Lkotlinx/coroutines/flow/V;",
        "result",
        "Lkotlinx/coroutines/N;",
        "b",
        "Lkotlin/j;",
        "e",
        "()Lkotlinx/coroutines/N;",
        "scope",
        "Lkotlinx/coroutines/x0;",
        "c",
        "Lkotlinx/coroutines/x0;",
        "temporaryResultTimerJob",
        "TemporaryResultExpiredException",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final e:I


# instance fields
.field public final a:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lkotlin/Pair<",
            "Ljava/lang/Long;",
            "LKo0/a<",
            "TT;>;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->d:Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->e:I

    return-void
.end method

.method public constructor <init>(Lm8/a;)V
    .locals 1
    .param p1    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, 0x0

    .line 5
    invoke-static {v0}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/special_event/impl/utils/temporarystore/b;

    .line 12
    .line 13
    invoke-direct {v0, p1}, Lorg/xbet/special_event/impl/utils/temporarystore/b;-><init>(Lm8/a;)V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    invoke-static {p1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    iput-object p1, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->b:Lkotlin/j;

    .line 23
    .line 24
    return-void
.end method

.method public static synthetic a(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->l(Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lm8/a;)Lkotlinx/coroutines/N;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->j(Lm8/a;)Lkotlinx/coroutines/N;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic c(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final j(Lm8/a;)Lkotlinx/coroutines/N;
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {v0, v1, v0}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {p0}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {v0, p0}, Lkotlin/coroutines/CoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    invoke-static {p0}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0
.end method

.method public static final l(Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final d()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->c:Lkotlinx/coroutines/x0;

    .line 8
    .line 9
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final e()Lkotlinx/coroutines/N;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlinx/coroutines/N;

    .line 8
    .line 9
    return-object v0
.end method

.method public final f()LKo0/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LKo0/a<",
            "TT;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlin/Pair;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {v0}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, LKo0/a;

    .line 16
    .line 17
    return-object v0

    .line 18
    :cond_0
    const/4 v0, 0x0

    .line 19
    return-object v0
.end method

.method public final g()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LKo0/a<",
            "TT;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->K(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$getTemporaryResultStream$$inlined$map$1;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$getTemporaryResultStream$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 10
    .line 11
    .line 12
    return-object v1
.end method

.method public final h(Ljava/lang/Throwable;)Z
    .locals 2

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    sget-object v0, Ld8/d;->a:Ld8/d;

    .line 6
    .line 7
    invoke-virtual {v0}, Ld8/d;->a()Lkotlin/ranges/IntRange;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lkotlin/ranges/c;->f()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-virtual {v0}, Lkotlin/ranges/c;->i()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    check-cast p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 20
    .line 21
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getStatusCode()I

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    if-gt v1, p1, :cond_0

    .line 26
    .line 27
    if-gt p1, v0, :cond_0

    .line 28
    .line 29
    const/4 p1, 0x1

    .line 30
    return p1

    .line 31
    :cond_0
    const/4 p1, 0x0

    .line 32
    return p1
.end method

.method public final i()Z
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lkotlin/Pair;

    .line 8
    .line 9
    const/4 v1, 0x1

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    invoke-virtual {v0}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    instance-of v0, v0, LKo0/a$b;

    .line 17
    .line 18
    xor-int/2addr v0, v1

    .line 19
    return v0

    .line 20
    :cond_0
    return v1
.end method

.method public final k()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->c:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    const/4 v1, 0x1

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->e()Lkotlinx/coroutines/N;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    new-instance v3, Lorg/xbet/special_event/impl/utils/temporarystore/a;

    .line 18
    .line 19
    invoke-direct {v3}, Lorg/xbet/special_event/impl/utils/temporarystore/a;-><init>()V

    .line 20
    .line 21
    .line 22
    new-instance v7, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;

    .line 23
    .line 24
    const/4 v0, 0x0

    .line 25
    invoke-direct {v7, p0, v0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$startTemporaryResultStreamCountDownTimer$2;-><init>(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    const/16 v8, 0xe

    .line 29
    .line 30
    const/4 v9, 0x0

    .line 31
    const/4 v4, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    const/4 v6, 0x0

    .line 34
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iput-object v0, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->c:Lkotlinx/coroutines/x0;

    .line 39
    .line 40
    return-void
.end method

.method public final m(LKo0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # LKo0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LKo0/a<",
            "TT;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;-><init>(Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x3

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_4

    .line 37
    .line 38
    if-eq v2, v5, :cond_3

    .line 39
    .line 40
    if-eq v2, v4, :cond_2

    .line 41
    .line 42
    if-ne v2, v3, :cond_1

    .line 43
    .line 44
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    goto/16 :goto_5

    .line 48
    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    goto :goto_3

    .line 61
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_4
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    instance-of p2, p1, LKo0/a$a;

    .line 69
    .line 70
    if-eqz p2, :cond_a

    .line 71
    .line 72
    move-object p2, p1

    .line 73
    check-cast p2, LKo0/a$a;

    .line 74
    .line 75
    invoke-virtual {p2}, LKo0/a$a;->a()Ljava/lang/Throwable;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    invoke-virtual {p0, p2}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->h(Ljava/lang/Throwable;)Z

    .line 80
    .line 81
    .line 82
    move-result p2

    .line 83
    if-eqz p2, :cond_6

    .line 84
    .line 85
    iget-object p2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 86
    .line 87
    new-instance v2, Lkotlin/Pair;

    .line 88
    .line 89
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 90
    .line 91
    .line 92
    move-result-wide v3

    .line 93
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 94
    .line 95
    .line 96
    move-result-object v3

    .line 97
    invoke-direct {v2, v3, p1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 98
    .line 99
    .line 100
    iput v5, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 101
    .line 102
    invoke-interface {p2, v2, v0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object p1

    .line 106
    if-ne p1, v1, :cond_5

    .line 107
    .line 108
    goto :goto_4

    .line 109
    :cond_5
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 110
    .line 111
    return-object p1

    .line 112
    :cond_6
    iget-object p2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 113
    .line 114
    invoke-interface {p2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object p2

    .line 118
    check-cast p2, Lkotlin/Pair;

    .line 119
    .line 120
    if-eqz p2, :cond_7

    .line 121
    .line 122
    invoke-virtual {p2}, Lkotlin/Pair;->getSecond()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object p2

    .line 126
    check-cast p2, LKo0/a;

    .line 127
    .line 128
    goto :goto_2

    .line 129
    :cond_7
    const/4 p2, 0x0

    .line 130
    :goto_2
    instance-of p2, p2, LKo0/a$b;

    .line 131
    .line 132
    if-eqz p2, :cond_8

    .line 133
    .line 134
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 135
    .line 136
    return-object p1

    .line 137
    :cond_8
    iget-object p2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 138
    .line 139
    new-instance v2, Lkotlin/Pair;

    .line 140
    .line 141
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 142
    .line 143
    .line 144
    move-result-wide v5

    .line 145
    invoke-static {v5, v6}, LHc/a;->f(J)Ljava/lang/Long;

    .line 146
    .line 147
    .line 148
    move-result-object v3

    .line 149
    invoke-direct {v2, v3, p1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 150
    .line 151
    .line 152
    iput v4, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 153
    .line 154
    invoke-interface {p2, v2, v0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    if-ne p1, v1, :cond_9

    .line 159
    .line 160
    goto :goto_4

    .line 161
    :cond_9
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 162
    .line 163
    return-object p1

    .line 164
    :cond_a
    instance-of p2, p1, LKo0/a$b;

    .line 165
    .line 166
    if-eqz p2, :cond_c

    .line 167
    .line 168
    iget-object p2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->c:Lkotlinx/coroutines/x0;

    .line 169
    .line 170
    invoke-static {p2}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 171
    .line 172
    .line 173
    iget-object p2, p0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->a:Lkotlinx/coroutines/flow/V;

    .line 174
    .line 175
    new-instance v2, Lkotlin/Pair;

    .line 176
    .line 177
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 178
    .line 179
    .line 180
    move-result-wide v4

    .line 181
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 182
    .line 183
    .line 184
    move-result-object v4

    .line 185
    invoke-direct {v2, v4, p1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 186
    .line 187
    .line 188
    iput v3, v0, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore$updateTemporaryResult$1;->label:I

    .line 189
    .line 190
    invoke-interface {p2, v2, v0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    if-ne p1, v1, :cond_b

    .line 195
    .line 196
    :goto_4
    return-object v1

    .line 197
    :cond_b
    :goto_5
    invoke-virtual {p0}, Lorg/xbet/special_event/impl/utils/temporarystore/TemporaryResultStore;->k()V

    .line 198
    .line 199
    .line 200
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 201
    .line 202
    return-object p1

    .line 203
    :cond_c
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 204
    .line 205
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 206
    .line 207
    .line 208
    throw p1
.end method
