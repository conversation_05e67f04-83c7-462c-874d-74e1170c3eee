.class public final LmJ0/a;
.super LA4/f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/f<",
        "Ljava/util/List<",
        "+",
        "LkJ0/a;",
        ">;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0000\u0018\u00002\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00030\u00020\u0001B\u0011\u0012\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "LmJ0/a;",
        "LA4/f;",
        "",
        "LkJ0/a;",
        "Landroid/graphics/drawable/Drawable;",
        "teamPlaceHolder",
        "<init>",
        "(Landroid/graphics/drawable/Drawable;)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 1
    invoke-direct {p0}, LA4/f;-><init>()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, LA4/a;->d:LA4/d;

    .line 5
    .line 6
    invoke-static {p1}, Lorg/xbet/statistic/player/impl/player/player_transfers/presentation/adapter/PlayerTransferAdapterDelegateKt;->d(Landroid/graphics/drawable/Drawable;)LA4/c;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {v0, p1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 11
    .line 12
    .line 13
    return-void
.end method
