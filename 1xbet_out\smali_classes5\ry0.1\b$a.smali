.class public final Lry0/b$a;
.super LwX0/B;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lry0/b;->a(I)Lq4/q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\'\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u0017\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0006\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tR\u0014\u0010\r\u001a\u00020\n8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\u000e"
    }
    d2 = {
        "ry0/b$a",
        "LwX0/B;",
        "",
        "needAuth",
        "()Z",
        "Landroidx/fragment/app/u;",
        "factory",
        "Landroidx/fragment/app/Fragment;",
        "createFragment",
        "(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;",
        "",
        "getScreenKey",
        "()Ljava/lang/String;",
        "screenKey",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:I


# direct methods
.method public constructor <init>(I)V
    .locals 0

    .line 1
    iput p1, p0, Lry0/b$a;->a:I

    .line 2
    .line 3
    invoke-direct {p0}, LwX0/B;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public createFragment(Landroidx/fragment/app/u;)Landroidx/fragment/app/Fragment;
    .locals 1

    .line 1
    sget-object p1, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->o0:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;

    .line 2
    .line 3
    iget v0, p0, Lry0/b$a;->a:I

    .line 4
    .line 5
    invoke-virtual {p1, v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;->b(I)Landroidx/fragment/app/Fragment;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public getScreenKey()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment;->o0:Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/special_event/impl/venues/presentation/VenuesFragment$a;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public needAuth()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method
