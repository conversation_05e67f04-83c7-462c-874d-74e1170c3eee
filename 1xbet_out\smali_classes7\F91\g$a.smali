.class public final LF91/g$a;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LF91/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0000\n\u0002\u0008\u0003\u0008\u0082\u0003\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\tJ!\u0010\u000c\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "LF91/g$a;",
        "Landroidx/recyclerview/widget/i$f;",
        "Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;",
        "<init>",
        "()V",
        "oldItem",
        "newItem",
        "",
        "e",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Z",
        "d",
        "",
        "f",
        "(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Ljava/lang/Object;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LF91/g$a;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/g$a;->d(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/g$a;->e(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 2
    .line 3
    check-cast p2, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LF91/g$a;->f(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method

.method public d(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Z
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Z
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->getId()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->getId()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    return p1
.end method

.method public f(Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;)Ljava/lang/Object;
    .locals 2
    .param p1    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->c()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->c()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->d()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->d()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_1

    .line 28
    .line 29
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->getName()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->getName()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    if-nez v0, :cond_0

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    invoke-super {p0, p1, p2}, Landroidx/recyclerview/widget/i$f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    return-object p1

    .line 49
    :cond_1
    :goto_0
    invoke-virtual {p2}, Lorg/xplatform/aggregator/impl/category/presentation/models/ProviderUIModel;->getId()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    return-object p1
.end method
