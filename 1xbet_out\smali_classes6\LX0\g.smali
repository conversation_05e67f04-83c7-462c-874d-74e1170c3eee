.class public final synthetic LLX0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/lifecycle/Lifecycle;

.field public final synthetic b:LLX0/i;


# direct methods
.method public synthetic constructor <init>(Landroidx/lifecycle/Lifecycle;LLX0/i;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LLX0/g;->a:Landroidx/lifecycle/Lifecycle;

    iput-object p2, p0, LLX0/g;->b:LLX0/i;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LLX0/g;->a:Landroidx/lifecycle/Lifecycle;

    iget-object v1, p0, LLX0/g;->b:LLX0/i;

    check-cast p1, Landroidx/lifecycle/w;

    check-cast p2, Landroidx/lifecycle/v;

    invoke-static {v0, v1, p1, p2}, LLX0/i;->c(Landroidx/lifecycle/Lifecycle;LLX0/i;Landroidx/lifecycle/w;Landroidx/lifecycle/v;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
