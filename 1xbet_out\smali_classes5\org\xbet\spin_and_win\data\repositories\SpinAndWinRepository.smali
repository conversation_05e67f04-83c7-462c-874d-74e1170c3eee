.class public final Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000f\u0018\u00002\u00020\u0001B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ8\u0010\u0016\u001a\u00020\u00152\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u00102\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u0012H\u0086@\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0019\u0010\u001a\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00190\u00120\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001c\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0015\u0010 \u001a\u00020\u001d2\u0006\u0010\u001c\u001a\u00020\u0019\u00a2\u0006\u0004\u0008 \u0010\u001fJ\r\u0010!\u001a\u00020\u000c\u00a2\u0006\u0004\u0008!\u0010\"J\r\u0010#\u001a\u00020\u001d\u00a2\u0006\u0004\u0008#\u0010$J\r\u0010&\u001a\u00020%\u00a2\u0006\u0004\u0008&\u0010\'J\u0015\u0010(\u001a\u00020\u001d2\u0006\u0010\u001c\u001a\u00020%\u00a2\u0006\u0004\u0008(\u0010)J\u0015\u0010+\u001a\u00020\u001d2\u0006\u0010*\u001a\u00020\u0015\u00a2\u0006\u0004\u0008+\u0010,J\r\u0010-\u001a\u00020\u0015\u00a2\u0006\u0004\u0008-\u0010.J\r\u0010/\u001a\u00020\u001d\u00a2\u0006\u0004\u0008/\u0010$R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u00100R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00101R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u00102R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u00103\u00a8\u00064"
    }
    d2 = {
        "Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;",
        "",
        "Lc8/h;",
        "requestParamsDataSource",
        "LYy0/e;",
        "mapper",
        "Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;",
        "remoteDataSource",
        "Lorg/xbet/spin_and_win/data/a;",
        "localDataSource",
        "<init>",
        "(Lc8/h;LYy0/e;Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;Lorg/xbet/spin_and_win/data/a;)V",
        "",
        "betSum",
        "",
        "activeId",
        "Lorg/xbet/games_section/api/models/GameBonus;",
        "gameBonus",
        "",
        "LZy0/a;",
        "betUser",
        "Ldz0/b;",
        "g",
        "(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lkotlinx/coroutines/flow/e;",
        "Ldz0/a;",
        "c",
        "()Lkotlinx/coroutines/flow/e;",
        "bet",
        "",
        "a",
        "(Ldz0/a;)V",
        "i",
        "e",
        "()D",
        "h",
        "()V",
        "Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "f",
        "()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;",
        "k",
        "(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V",
        "gameResult",
        "j",
        "(Ldz0/b;)V",
        "d",
        "()Ldz0/b;",
        "b",
        "Lc8/h;",
        "LYy0/e;",
        "Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;",
        "Lorg/xbet/spin_and_win/data/a;",
        "spin_and_win_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LYy0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/spin_and_win/data/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lc8/h;LYy0/e;Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;Lorg/xbet/spin_and_win/data/a;)V
    .locals 0
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LYy0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/spin_and_win/data/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->a:Lc8/h;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->b:LYy0/e;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->c:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(Ldz0/a;)V
    .locals 1
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/data/a;->a(Ldz0/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final b()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->b()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final c()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Ldz0/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->c()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final d()Ldz0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->e()Ldz0/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final e()D
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->f()D

    .line 4
    .line 5
    .line 6
    move-result-wide v0

    .line 7
    return-wide v0
.end method

.method public final f()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->g()Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final g(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 12
    .param p6    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(DJ",
            "Lorg/xbet/games_section/api/models/GameBonus;",
            "Ljava/util/List<",
            "LZy0/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ldz0/b;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p7

    .line 2
    .line 3
    instance-of v1, v0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v1, v0

    .line 8
    check-cast v1, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;

    .line 9
    .line 10
    iget v2, v1, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->label:I

    .line 11
    .line 12
    const/high16 v3, -0x80000000

    .line 13
    .line 14
    and-int v4, v2, v3

    .line 15
    .line 16
    if-eqz v4, :cond_0

    .line 17
    .line 18
    sub-int/2addr v2, v3

    .line 19
    iput v2, v1, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->label:I

    .line 20
    .line 21
    :goto_0
    move-object v11, v1

    .line 22
    goto :goto_1

    .line 23
    :cond_0
    new-instance v1, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;

    .line 24
    .line 25
    invoke-direct {v1, p0, v0}, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;-><init>(Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;Lkotlin/coroutines/e;)V

    .line 26
    .line 27
    .line 28
    goto :goto_0

    .line 29
    :goto_1
    iget-object v0, v11, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    iget v2, v11, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->label:I

    .line 36
    .line 37
    const/4 v3, 0x1

    .line 38
    if-eqz v2, :cond_2

    .line 39
    .line 40
    if-ne v2, v3, :cond_1

    .line 41
    .line 42
    iget-object v1, v11, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->L$0:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v1, LYy0/e;

    .line 45
    .line 46
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 53
    .line 54
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    throw v0

    .line 58
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->b:LYy0/e;

    .line 62
    .line 63
    iget-object v2, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->c:Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;

    .line 64
    .line 65
    iget-object v4, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->a:Lc8/h;

    .line 66
    .line 67
    invoke-interface {v4}, Lc8/h;->d()I

    .line 68
    .line 69
    .line 70
    move-result v9

    .line 71
    iget-object v4, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->a:Lc8/h;

    .line 72
    .line 73
    invoke-interface {v4}, Lc8/h;->c()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v10

    .line 77
    iput-object v0, v11, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->L$0:Ljava/lang/Object;

    .line 78
    .line 79
    iput v3, v11, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository$play$1;->label:I

    .line 80
    .line 81
    move-wide v3, p1

    .line 82
    move-wide v5, p3

    .line 83
    move-object/from16 v7, p5

    .line 84
    .line 85
    move-object/from16 v8, p6

    .line 86
    .line 87
    invoke-virtual/range {v2 .. v11}, Lorg/xbet/spin_and_win/data/SpinAndWinRemoteDataSource;->c(DJLorg/xbet/games_section/api/models/GameBonus;Ljava/util/List;ILjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v2

    .line 91
    if-ne v2, v1, :cond_3

    .line 92
    .line 93
    return-object v1

    .line 94
    :cond_3
    move-object v1, v0

    .line 95
    move-object v0, v2

    .line 96
    :goto_2
    check-cast v0, Lg9/d;

    .line 97
    .line 98
    invoke-virtual {v0}, Lg9/d;->a()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    check-cast v0, Laz0/a;

    .line 103
    .line 104
    invoke-virtual {v1, v0}, LYy0/e;->a(Laz0/a;)Ldz0/b;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    return-object v0
.end method

.method public final h()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/spin_and_win/data/a;->h()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final i(Ldz0/a;)V
    .locals 1
    .param p1    # Ldz0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/data/a;->i(Ldz0/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final j(Ldz0/b;)V
    .locals 1
    .param p1    # Ldz0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/data/a;->j(Ldz0/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final k(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V
    .locals 1
    .param p1    # Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/spin_and_win/data/repositories/SpinAndWinRepository;->d:Lorg/xbet/spin_and_win/data/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/spin_and_win/data/a;->k(Lorg/xbet/spin_and_win/domain/model/SpinAndWinBetType;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
