.class public final Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LE31/j;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\r\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u001d\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u00aa\u00012\u00020\u00012\u00020\u0002:\u0001lB\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0017\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\u000f\u0010\u0016\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\rJ\u000f\u0010\u0017\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\rJ\u001f\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0018\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ7\u0010\"\u001a\u00020\u000b2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\u00072\u0006\u0010!\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010$\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008$\u0010\u0011J\r\u0010&\u001a\u00020%\u00a2\u0006\u0004\u0008&\u0010\'J\r\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008)\u0010*J\r\u0010,\u001a\u00020+\u00a2\u0006\u0004\u0008,\u0010-J\r\u0010.\u001a\u00020+\u00a2\u0006\u0004\u0008.\u0010-J\r\u0010/\u001a\u00020+\u00a2\u0006\u0004\u0008/\u0010-J\r\u00100\u001a\u00020+\u00a2\u0006\u0004\u00080\u0010-J\r\u00102\u001a\u000201\u00a2\u0006\u0004\u00082\u00103J\r\u00104\u001a\u00020+\u00a2\u0006\u0004\u00084\u0010-J\u0017\u00107\u001a\u00020\u000b2\u0008\u00106\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u00087\u00108J\u001d\u0010;\u001a\u00020\u000b2\u0006\u00109\u001a\u0002012\u0006\u0010:\u001a\u00020\u0007\u00a2\u0006\u0004\u0008;\u0010<J!\u0010=\u001a\u00020\u000b2\u0006\u00109\u001a\u0002012\n\u0008\u0002\u0010:\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u0008=\u0010>JU\u0010D\u001a\u00020\u000b2\u0006\u0010?\u001a\u0002012\u0006\u0010@\u001a\u0002012\n\u0008\u0002\u0010:\u001a\u0004\u0018\u0001052\u0014\u0008\u0002\u0010B\u001a\u000e\u0012\u0004\u0012\u000205\u0012\u0004\u0012\u00020\u000b0A2\u0014\u0008\u0002\u0010C\u001a\u000e\u0012\u0004\u0012\u000205\u0012\u0004\u0012\u00020\u000b0A\u00a2\u0006\u0004\u0008D\u0010EJ\u0017\u0010H\u001a\u00020\u000b2\u0008\u0010G\u001a\u0004\u0018\u00010F\u00a2\u0006\u0004\u0008H\u0010IJ\u0017\u0010H\u001a\u00020\u000b2\u0008\u0008\u0001\u0010J\u001a\u00020\u0007\u00a2\u0006\u0004\u0008H\u0010KJ\u0017\u0010M\u001a\u00020\u000b2\u0008\u0008\u0001\u0010L\u001a\u00020\u0007\u00a2\u0006\u0004\u0008M\u0010KJ\u0017\u0010M\u001a\u00020\u000b2\u0008\u0010N\u001a\u0004\u0018\u00010F\u00a2\u0006\u0004\u0008M\u0010IJ\u0015\u0010P\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008P\u0010QJ\u0015\u0010R\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008R\u0010QJ\u0015\u0010S\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008S\u0010QJ\u0015\u0010T\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008T\u0010QJ\u0015\u0010U\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008U\u0010QJ\u0015\u0010V\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008V\u0010QJ\u0015\u0010W\u001a\u00020\u000b2\u0006\u0010O\u001a\u00020\u001c\u00a2\u0006\u0004\u0008W\u0010QJ\u0015\u0010Y\u001a\u00020\u000b2\u0006\u0010X\u001a\u00020\u001c\u00a2\u0006\u0004\u0008Y\u0010QJ\u0015\u0010Z\u001a\u00020\u000b2\u0006\u0010X\u001a\u00020\u001c\u00a2\u0006\u0004\u0008Z\u0010QJ\u0017\u0010\\\u001a\u00020\u000b2\u0008\u0008\u0001\u0010[\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\\\u0010KJ\u0017\u0010]\u001a\u00020\u000b2\u0008\u0008\u0001\u0010[\u001a\u00020\u0007\u00a2\u0006\u0004\u0008]\u0010KJ\u0017\u0010^\u001a\u00020\u000b2\u0008\u0008\u0001\u0010[\u001a\u00020\u0007\u00a2\u0006\u0004\u0008^\u0010KJ\u0017\u0010_\u001a\u00020\u000b2\u0008\u0008\u0001\u0010[\u001a\u00020\u0007\u00a2\u0006\u0004\u0008_\u0010KJ\u0015\u0010b\u001a\u00020\u000b2\u0006\u0010a\u001a\u00020`\u00a2\u0006\u0004\u0008b\u0010cJ\u0015\u0010d\u001a\u00020\u000b2\u0006\u0010a\u001a\u00020`\u00a2\u0006\u0004\u0008d\u0010cJ\u0015\u0010e\u001a\u00020\u000b2\u0006\u0010a\u001a\u00020`\u00a2\u0006\u0004\u0008e\u0010cJ\u0015\u0010f\u001a\u00020\u000b2\u0006\u0010a\u001a\u00020`\u00a2\u0006\u0004\u0008f\u0010cJ\u0017\u00107\u001a\u00020\u000b2\u0008\u0008\u0001\u00106\u001a\u00020\u0007\u00a2\u0006\u0004\u00087\u0010KJ\u0017\u0010h\u001a\u00020\u000b2\u0008\u0008\u0001\u0010g\u001a\u00020\u0007\u00a2\u0006\u0004\u0008h\u0010KJ\u0017\u0010h\u001a\u00020\u000b2\u0008\u0010g\u001a\u0004\u0018\u00010i\u00a2\u0006\u0004\u0008h\u0010jJ\r\u0010k\u001a\u00020\u000b\u00a2\u0006\u0004\u0008k\u0010\rR\u001b\u0010o\u001a\u00020%8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008l\u0010m\u001a\u0004\u0008n\u0010\'R\u001b\u0010r\u001a\u00020(8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008p\u0010m\u001a\u0004\u0008q\u0010*R\u001b\u0010u\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008s\u0010m\u001a\u0004\u0008t\u0010-R\u001b\u0010x\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008v\u0010m\u001a\u0004\u0008w\u0010-R\u001b\u0010{\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008y\u0010m\u001a\u0004\u0008z\u0010-R\u001b\u0010~\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008|\u0010m\u001a\u0004\u0008}\u0010-R\u001f\u0010\u0083\u0001\u001a\u00020\u007f8BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u0080\u0001\u0010m\u001a\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0018\u0010\u0087\u0001\u001a\u00030\u0084\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0017\u0010\u008a\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u0089\u0001R\u0016\u0010\u008b\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0013\u0010\u0089\u0001R\u0016\u0010\u008c\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0015\u0010\u0089\u0001R\u0016\u0010\u008d\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0010\u0010\u0089\u0001R\u0017\u0010\u008f\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u0089\u0001R\u0016\u0010\u0090\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008;\u0010\u0089\u0001R\u0016\u0010\u0091\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008=\u0010\u0089\u0001R\u0016\u0010\u0092\u0001\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008D\u0010\u0089\u0001R\u0019\u0010\u0095\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0019\u0010\u0097\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0096\u0001\u0010\u0094\u0001R\u0019\u0010\u0099\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u0094\u0001R\u0019\u0010\u009b\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009a\u0001\u0010\u0094\u0001R\u0018\u0010\u009c\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u000c\u0010\u0094\u0001R\u0018\u0010\u009d\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0016\u0010\u0094\u0001R\u0018\u0010\u009e\u0001\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0017\u0010\u0094\u0001R\u0019\u0010\u00a1\u0001\u001a\u00020F8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u001c\u0010\u00a5\u0001\u001a\u0005\u0018\u00010\u00a2\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u001a\u0010\u00a9\u0001\u001a\u00030\u00a6\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001\u00a8\u0006\u00ab\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;",
        "Landroid/widget/FrameLayout;",
        "LE31/j;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAtr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "u",
        "()V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "l",
        "(Landroid/graphics/Canvas;)V",
        "visibleButtonCount",
        "j",
        "(I)I",
        "k",
        "v",
        "w",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "onDraw",
        "Landroid/widget/ImageView;",
        "getIcon",
        "()Landroid/widget/ImageView;",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "getSportImage",
        "()Lorg/xbet/uikit/components/views/LoadableImageView;",
        "Lcom/google/android/material/button/MaterialButton;",
        "getStream",
        "()Lcom/google/android/material/button/MaterialButton;",
        "getNotification",
        "getNotificationImage",
        "getFavorite",
        "",
        "getTitleText",
        "()Ljava/lang/String;",
        "getZone",
        "Landroid/graphics/drawable/Drawable;",
        "icon",
        "setIcon",
        "(Landroid/graphics/drawable/Drawable;)V",
        "url",
        "placeholder",
        "n",
        "(Ljava/lang/String;I)V",
        "o",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "mainUrl",
        "defaultUrl",
        "Lkotlin/Function1;",
        "onLoadedMain",
        "onLoadedDefault",
        "p",
        "(Ljava/lang/String;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "titleRes",
        "(I)V",
        "soonRes",
        "setSoonText",
        "soon",
        "visible",
        "setIconVisible",
        "(Z)V",
        "setTopIconVisible",
        "setStreamButtonVisible",
        "setSoonTextViewVisible",
        "setZoneButtonVisible",
        "setNotificationButtonVisible",
        "setFavoriteButtonVisible",
        "selected",
        "setNotificationButtonSelected",
        "setFavoriteButtonSelected",
        "iconRes",
        "setStreamButtonIconRes",
        "setZoneButtonIconRes",
        "setNotificationButtonIconRes",
        "setFavoriteButtonIconRes",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setStreamButtonClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "setZoneButtonClickListener",
        "setNotificationButtonClickListener",
        "setFavoriteButtonClickListener",
        "color",
        "setIconTint",
        "Landroid/content/res/ColorStateList;",
        "(Landroid/content/res/ColorStateList;)V",
        "setDefaultIconTint",
        "a",
        "Lkotlin/j;",
        "getTopIcon",
        "topIcon",
        "b",
        "getSportImageView",
        "sportImageView",
        "c",
        "getStreamButton",
        "streamButton",
        "d",
        "getZoneButton",
        "zoneButton",
        "e",
        "getNotificationButton",
        "notificationButton",
        "f",
        "getFavoriteButton",
        "favoriteButton",
        "Landroid/widget/TextView;",
        "g",
        "getSoonTextView",
        "()Landroid/widget/TextView;",
        "soonTextView",
        "Landroid/text/TextPaint;",
        "h",
        "Landroid/text/TextPaint;",
        "textPaint",
        "i",
        "I",
        "contentMarginEnd",
        "contentMarginTop",
        "contentStartMargin",
        "marginBetweenButtons",
        "m",
        "textMargin",
        "sportIconSize",
        "topIconSize",
        "buttonSize",
        "q",
        "Z",
        "sportImageViewVisibility",
        "r",
        "streamButtonVisibility",
        "s",
        "soonTextViewVisibility",
        "t",
        "zoneButtonVisibility",
        "topIconVisibility",
        "notificationButtonVisibility",
        "favoriteButtonVisibility",
        "x",
        "Ljava/lang/CharSequence;",
        "titleText",
        "Landroid/text/StaticLayout;",
        "y",
        "Landroid/text/StaticLayout;",
        "titleTextStaticLayout",
        "",
        "z",
        "F",
        "startDrawTitleCoordinate",
        "A",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final A:Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final B:I


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public q:Z

.field public r:Z

.field public s:Z

.field public t:Z

.field public u:Z

.field public v:Z

.field public w:Z

.field public x:Ljava/lang/CharSequence;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y:Landroid/text/StaticLayout;

.field public z:F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->A:Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->B:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    sget-object p3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance v0, LE31/a;

    invoke-direct {v0, p1, p0}, LE31/a;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->a:Lkotlin/j;

    .line 7
    new-instance v0, LE31/b;

    invoke-direct {v0, p1, p0}, LE31/b;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->b:Lkotlin/j;

    .line 8
    new-instance v0, LE31/c;

    invoke-direct {v0, p1, p0}, LE31/c;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->c:Lkotlin/j;

    .line 9
    new-instance v0, LE31/d;

    invoke-direct {v0, p1, p0}, LE31/d;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->d:Lkotlin/j;

    .line 10
    new-instance v0, LE31/e;

    invoke-direct {v0, p1, p0}, LE31/e;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->e:Lkotlin/j;

    .line 11
    new-instance v0, LE31/f;

    invoke-direct {v0, p1, p0}, LE31/f;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->f:Lkotlin/j;

    .line 12
    new-instance v0, LE31/g;

    invoke-direct {v0, p1}, LE31/g;-><init>(Landroid/content/Context;)V

    invoke-static {p3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->g:Lkotlin/j;

    .line 13
    new-instance p3, Landroid/text/TextPaint;

    invoke-direct {p3}, Landroid/text/TextPaint;-><init>()V

    .line 14
    sget v0, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {p3, p1, v0}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    const/4 v0, 0x1

    .line 15
    invoke-virtual {p3, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 16
    iput-object p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->h:Landroid/text/TextPaint;

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_2:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->i:I

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_2:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->j:I

    .line 19
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_8:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_4:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_8:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->o:I

    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_36:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 25
    const-string p3, ""

    iput-object p3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    const/4 p3, 0x0

    .line 26
    invoke-virtual {p0, p3}, Landroid/view/View;->setWillNotDraw(Z)V

    .line 27
    sget-object v0, Lm31/g;->EventCardHeader:[I

    .line 28
    invoke-virtual {p1, p2, v0, p3, p3}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 29
    sget p3, Lm31/g;->EventCardHeader_title:I

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 30
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p3

    if-nez p3, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setTitle(Ljava/lang/CharSequence;)V

    .line 31
    :cond_1
    :goto_0
    sget p1, Lm31/g;->EventCardHeader_icon:I

    invoke-virtual {p2, p1}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setIcon(Landroid/graphics/drawable/Drawable;)V

    .line 32
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardHeaderStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final A(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Landroid/widget/ImageView;
    .locals 1

    .line 1
    new-instance v0, Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 7
    .line 8
    .line 9
    move-result p0

    .line 10
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 11
    .line 12
    .line 13
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 14
    .line 15
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 16
    .line 17
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 21
    .line 22
    .line 23
    sget p0, LlZ0/h;->ic_glyph_popular_red:I

    .line 24
    .line 25
    invoke-virtual {v0, p0}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 26
    .line 27
    .line 28
    return-object v0
.end method

.method public static final B(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 3

    .line 1
    new-instance v0, Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    sget v2, Lm31/b;->eventCardHeaderButtonStyle:I

    .line 5
    .line 6
    invoke-direct {v0, p0, v1, v2}, Lcom/google/android/material/button/MaterialButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 7
    .line 8
    .line 9
    sget p0, LlZ0/j;->stream_button_uitest:I

    .line 10
    .line 11
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 12
    .line 13
    .line 14
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 15
    .line 16
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 17
    .line 18
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 22
    .line 23
    .line 24
    sget p0, LlZ0/h;->ic_glyph_video_zone:I

    .line 25
    .line 26
    invoke-virtual {v0, p0}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 27
    .line 28
    .line 29
    return-object v0
.end method

.method public static synthetic a(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->B(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lorg/xbet/uikit/components/views/LoadableImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->y(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lorg/xbet/uikit/components/views/LoadableImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->A(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Landroid/widget/ImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->z(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final getFavoriteButton()Lcom/google/android/material/button/MaterialButton;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->f:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/button/MaterialButton;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getNotificationButton()Lcom/google/android/material/button/MaterialButton;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/button/MaterialButton;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSoonTextView()Landroid/widget/TextView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->g:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/widget/TextView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getStreamButton()Lcom/google/android/material/button/MaterialButton;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/button/MaterialButton;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTopIcon()Landroid/widget/ImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/widget/ImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getZoneButton()Lcom/google/android/material/button/MaterialButton;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/button/MaterialButton;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic h(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->s(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Landroid/content/Context;)Landroid/widget/TextView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x(Landroid/content/Context;)Landroid/widget/TextView;

    move-result-object p0

    return-object p0
.end method

.method public static final m(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 3

    .line 1
    new-instance v0, Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    sget v2, Lm31/b;->eventCardHeaderButtonStyle:I

    .line 5
    .line 6
    invoke-direct {v0, p0, v1, v2}, Lcom/google/android/material/button/MaterialButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 7
    .line 8
    .line 9
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 14
    .line 15
    .line 16
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 17
    .line 18
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 19
    .line 20
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    .line 25
    .line 26
    sget p0, LlZ0/h;->uikit_selector_favorites:I

    .line 27
    .line 28
    invoke-virtual {v0, p0}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 29
    .line 30
    .line 31
    return-object v0
.end method

.method public static synthetic q(Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;Ljava/lang/String;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 6

    .line 1
    and-int/lit8 p7, p6, 0x4

    .line 2
    .line 3
    if-eqz p7, :cond_0

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 6
    .line 7
    .line 8
    move-result-object p3

    .line 9
    invoke-virtual {p3}, Lorg/xbet/uikit/components/views/LoadableImageView;->getPlaceholder()Landroid/graphics/drawable/Drawable;

    .line 10
    .line 11
    .line 12
    move-result-object p3

    .line 13
    :cond_0
    move-object v3, p3

    .line 14
    and-int/lit8 p3, p6, 0x8

    .line 15
    .line 16
    if-eqz p3, :cond_1

    .line 17
    .line 18
    new-instance p4, LE31/h;

    .line 19
    .line 20
    invoke-direct {p4}, LE31/h;-><init>()V

    .line 21
    .line 22
    .line 23
    :cond_1
    move-object v4, p4

    .line 24
    and-int/lit8 p3, p6, 0x10

    .line 25
    .line 26
    if-eqz p3, :cond_2

    .line 27
    .line 28
    new-instance p5, LE31/i;

    .line 29
    .line 30
    invoke-direct {p5}, LE31/i;-><init>()V

    .line 31
    .line 32
    .line 33
    :cond_2
    move-object v0, p0

    .line 34
    move-object v1, p1

    .line 35
    move-object v2, p2

    .line 36
    move-object v5, p5

    .line 37
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p(Ljava/lang/String;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public static final r(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final s(Landroid/graphics/drawable/Drawable;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final t(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 3

    .line 1
    new-instance v0, Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    sget v2, Lm31/b;->eventCardHeaderButtonStyle:I

    .line 5
    .line 6
    invoke-direct {v0, p0, v1, v2}, Lcom/google/android/material/button/MaterialButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 7
    .line 8
    .line 9
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 14
    .line 15
    .line 16
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 17
    .line 18
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 19
    .line 20
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    .line 25
    .line 26
    sget p0, LlZ0/h;->uikit_selector_notifications:I

    .line 27
    .line 28
    invoke-virtual {v0, p0}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 29
    .line 30
    .line 31
    return-object v0
.end method

.method public static final x(Landroid/content/Context;)Landroid/widget/TextView;
    .locals 3

    .line 1
    new-instance v0, Landroid/widget/TextView;

    .line 2
    .line 3
    new-instance v1, Lk/d;

    .line 4
    .line 5
    sget v2, LlZ0/n;->TextStyle_Caption_Bold_S:I

    .line 6
    .line 7
    invoke-direct {v1, p0, v2}, Lk/d;-><init>(Landroid/content/Context;I)V

    .line 8
    .line 9
    .line 10
    invoke-direct {v0, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 11
    .line 12
    .line 13
    sget v1, LlZ0/f;->static_white:I

    .line 14
    .line 15
    invoke-static {p0, v1}, LF0/b;->getColor(Landroid/content/Context;I)I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 20
    .line 21
    .line 22
    const/16 v1, 0x11

    .line 23
    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setGravity(I)V

    .line 25
    .line 26
    .line 27
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 28
    .line 29
    const/4 v2, -0x2

    .line 30
    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 34
    .line 35
    .line 36
    invoke-static {}, Landroid/view/View;->generateViewId()I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    invoke-virtual {v0, v1}, Landroid/view/View;->setId(I)V

    .line 41
    .line 42
    .line 43
    const/high16 v1, 0x41000000    # 8.0f

    .line 44
    .line 45
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextSize(F)V

    .line 46
    .line 47
    .line 48
    sget v1, Lm31/c;->event_card_header_soon_bg:I

    .line 49
    .line 50
    invoke-static {p0, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    invoke-virtual {v0, p0}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 55
    .line 56
    .line 57
    return-object v0
.end method

.method public static final y(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lorg/xbet/uikit/components/views/LoadableImageView;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    const/4 v4, 0x6

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x0

    .line 7
    move-object v1, p0

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/views/LoadableImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 9
    .line 10
    .line 11
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 16
    .line 17
    .line 18
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 19
    .line 20
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 21
    .line 22
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 26
    .line 27
    .line 28
    sget p0, LlZ0/d;->uikitSecondary:I

    .line 29
    .line 30
    const/4 p1, 0x0

    .line 31
    const/4 v2, 0x2

    .line 32
    invoke-static {v1, p0, p1, v2, p1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 33
    .line 34
    .line 35
    move-result p0

    .line 36
    invoke-static {p0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    invoke-static {v0, p0}, LX0/j;->c(Landroid/widget/ImageView;Landroid/content/res/ColorStateList;)V

    .line 41
    .line 42
    .line 43
    return-object v0
.end method

.method public static final z(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;)Lcom/google/android/material/button/MaterialButton;
    .locals 3

    .line 1
    new-instance v0, Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    sget v2, Lm31/b;->eventCardHeaderButtonStyle:I

    .line 5
    .line 6
    invoke-direct {v0, p0, v1, v2}, Lcom/google/android/material/button/MaterialButton;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 7
    .line 8
    .line 9
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 14
    .line 15
    .line 16
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 17
    .line 18
    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 19
    .line 20
    invoke-direct {p0, p1, p1}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    .line 25
    .line 26
    sget p0, LlZ0/h;->ic_glyph_broadcast:I

    .line 27
    .line 28
    invoke-virtual {v0, p0}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 29
    .line 30
    .line 31
    return-object v0
.end method


# virtual methods
.method public final getFavorite()Lcom/google/android/material/button/MaterialButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getIcon()Landroid/widget/ImageView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getNotification()Lcom/google/android/material/button/MaterialButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getNotificationImage()Lcom/google/android/material/button/MaterialButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getSportImage()Lorg/xbet/uikit/components/views/LoadableImageView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getStream()Lcom/google/android/material/button/MaterialButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getTitleText()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final getZone()Lcom/google/android/material/button/MaterialButton;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final j(I)I
    .locals 3

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 6
    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 10
    .line 11
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 12
    .line 13
    add-int/2addr v0, v1

    .line 14
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 15
    .line 16
    add-int/2addr v0, v1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    if-eqz v0, :cond_1

    .line 19
    .line 20
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 21
    .line 22
    if-eqz v0, :cond_1

    .line 23
    .line 24
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 25
    .line 26
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->o:I

    .line 27
    .line 28
    add-int/2addr v1, v0

    .line 29
    div-int/lit8 v0, v0, 0x2

    .line 30
    .line 31
    add-int/2addr v1, v0

    .line 32
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 33
    .line 34
    add-int/2addr v1, v0

    .line 35
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 36
    .line 37
    add-int/2addr v0, v1

    .line 38
    goto :goto_0

    .line 39
    :cond_1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 40
    .line 41
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->i:I

    .line 46
    .line 47
    sub-int/2addr v1, v2

    .line 48
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 49
    .line 50
    mul-int v2, v2, p1

    .line 51
    .line 52
    sub-int/2addr v1, v2

    .line 53
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 54
    .line 55
    add-int/lit8 p1, p1, -0x1

    .line 56
    .line 57
    mul-int v2, v2, p1

    .line 58
    .line 59
    sub-int/2addr v1, v2

    .line 60
    iget p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 61
    .line 62
    sub-int/2addr v1, p1

    .line 63
    int-to-float p1, v0

    .line 64
    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->z:F

    .line 65
    .line 66
    sub-int/2addr v1, v0

    .line 67
    return v1
.end method

.method public final k(I)I
    .locals 3

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 6
    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 14
    .line 15
    sub-int/2addr v0, v1

    .line 16
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 17
    .line 18
    sub-int/2addr v0, v1

    .line 19
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 20
    .line 21
    :goto_0
    sub-int/2addr v0, v1

    .line 22
    goto :goto_1

    .line 23
    :cond_0
    if-eqz v0, :cond_1

    .line 24
    .line 25
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 26
    .line 27
    if-eqz v0, :cond_1

    .line 28
    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 34
    .line 35
    sub-int/2addr v0, v1

    .line 36
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->o:I

    .line 37
    .line 38
    sub-int/2addr v0, v2

    .line 39
    div-int/lit8 v1, v1, 0x2

    .line 40
    .line 41
    sub-int/2addr v0, v1

    .line 42
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->n:I

    .line 43
    .line 44
    sub-int/2addr v0, v1

    .line 45
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 46
    .line 47
    goto :goto_0

    .line 48
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :goto_1
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->i:I

    .line 56
    .line 57
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 58
    .line 59
    mul-int v2, v2, p1

    .line 60
    .line 61
    add-int/2addr v1, v2

    .line 62
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 63
    .line 64
    add-int/lit8 p1, p1, -0x1

    .line 65
    .line 66
    mul-int v2, v2, p1

    .line 67
    .line 68
    add-int/2addr v1, v2

    .line 69
    iget p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->m:I

    .line 70
    .line 71
    add-int/2addr v1, p1

    .line 72
    int-to-float p1, v1

    .line 73
    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->z:F

    .line 74
    .line 75
    sub-int/2addr v0, v1

    .line 76
    return v0
.end method

.method public final l(Landroid/graphics/Canvas;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->y:Landroid/text/StaticLayout;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 6
    .line 7
    .line 8
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->z:F

    .line 9
    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    invoke-virtual {v0}, Landroid/text/Layout;->getHeight()I

    .line 15
    .line 16
    .line 17
    move-result v3

    .line 18
    sub-int/2addr v2, v3

    .line 19
    int-to-float v2, v2

    .line 20
    const/high16 v3, 0x3f000000    # 0.5f

    .line 21
    .line 22
    mul-float v2, v2, v3

    .line 23
    .line 24
    invoke-virtual {p1, v1, v2}, Landroid/graphics/Canvas;->translate(FF)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {v0, p1}, Landroid/text/Layout;->draw(Landroid/graphics/Canvas;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V

    .line 31
    .line 32
    .line 33
    :cond_0
    return-void
.end method

.method public final n(Ljava/lang/String;I)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v5, 0xc

    .line 6
    .line 7
    const/4 v6, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    const/4 v4, 0x0

    .line 10
    move-object v1, p1

    .line 11
    move v2, p2

    .line 12
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->W(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final o(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v5, 0xc

    .line 6
    .line 7
    const/4 v6, 0x0

    .line 8
    const/4 v3, 0x0

    .line 9
    const/4 v4, 0x0

    .line 10
    move-object v1, p1

    .line 11
    move-object v2, p2

    .line 12
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->X(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 0
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l(Landroid/graphics/Canvas;)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    const/4 p2, 0x1

    .line 9
    if-ne p1, p2, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w()V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public onMeasure(II)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final p(Ljava/lang/String;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Landroid/graphics/drawable/Drawable;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/16 v7, 0x20

    .line 6
    .line 7
    const/4 v8, 0x0

    .line 8
    const/4 v6, 0x0

    .line 9
    move-object v1, p1

    .line 10
    move-object v2, p2

    .line 11
    move-object v3, p3

    .line 12
    move-object v4, p4

    .line 13
    move-object v5, p5

    .line 14
    invoke-static/range {v0 .. v8}, Lorg/xbet/uikit/components/views/LoadableImageView;->Y(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final setDefaultIconTint()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/d;->uikitSecondary:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x2

    .line 9
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setIconTint(I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setFavoriteButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFavoriteButtonIconRes(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFavoriteButtonSelected(Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setSelected(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFavoriteButtonVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final setIcon(I)V
    .locals 1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setIcon(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setIcon(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 2
    iget-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    if-nez p1, :cond_0

    const/4 p1, 0x1

    .line 3
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    :cond_0
    return-void
.end method

.method public final setIconTint(I)V
    .locals 0

    .line 1
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setIconTint(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public final setIconTint(Landroid/content/res/ColorStateList;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    move-result-object v0

    invoke-static {v0, p1}, LX0/j;->c(Landroid/widget/ImageView;Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public final setIconVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final setNotificationButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setNotificationButtonIconRes(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setNotificationButtonSelected(Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setSelected(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setNotificationButtonVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final setSoonText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setSoonText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSoonText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSoonTextViewVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->s:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->s:Z

    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    const v0, 0x3ecccccd

    .line 42
    .line 43
    .line 44
    invoke-virtual {p1, v0}, Landroid/view/View;->setAlpha(F)V

    .line 45
    .line 46
    .line 47
    :cond_2
    return-void
.end method

.method public final setStreamButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setStreamButtonIconRes(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setStreamButtonVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    if-nez p1, :cond_1

    .line 2
    const-string p1, ""

    :cond_1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    .line 3
    invoke-virtual {p0, p1}, Landroid/view/View;->setContentDescription(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopIconVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final setZoneButtonClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setZoneButtonIconRes(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Lcom/google/android/material/button/MaterialButton;->setIconResource(I)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setZoneButtonVisible(Z)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 2
    .line 3
    if-eq p1, v0, :cond_2

    .line 4
    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz p1, :cond_1

    .line 27
    .line 28
    const/4 v1, 0x0

    .line 29
    goto :goto_0

    .line 30
    :cond_1
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 36
    .line 37
    :cond_2
    return-void
.end method

.method public final u()V
    .locals 15

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    .line 4
    .line 5
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    if-nez v2, :cond_0

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r:Z

    .line 13
    .line 14
    invoke-static {v2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    iget-boolean v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 19
    .line 20
    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    iget-boolean v4, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 25
    .line 26
    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    iget-boolean v5, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 31
    .line 32
    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    const/4 v6, 0x4

    .line 37
    new-array v6, v6, [Ljava/lang/Boolean;

    .line 38
    .line 39
    aput-object v2, v6, v0

    .line 40
    .line 41
    aput-object v3, v6, v1

    .line 42
    .line 43
    const/4 v2, 0x2

    .line 44
    aput-object v4, v6, v2

    .line 45
    .line 46
    const/4 v2, 0x3

    .line 47
    aput-object v5, v6, v2

    .line 48
    .line 49
    invoke-static {v6}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-static {v2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    move-result v3

    .line 57
    if-eqz v3, :cond_1

    .line 58
    .line 59
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-eqz v3, :cond_1

    .line 64
    .line 65
    goto :goto_1

    .line 66
    :cond_1
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    :cond_2
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 71
    .line 72
    .line 73
    move-result v3

    .line 74
    if-eqz v3, :cond_3

    .line 75
    .line 76
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v3

    .line 80
    check-cast v3, Ljava/lang/Boolean;

    .line 81
    .line 82
    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    .line 83
    .line 84
    .line 85
    move-result v3

    .line 86
    if-eqz v3, :cond_2

    .line 87
    .line 88
    add-int/2addr v0, v1

    .line 89
    if-gez v0, :cond_2

    .line 90
    .line 91
    invoke-static {}, Lkotlin/collections/v;->w()V

    .line 92
    .line 93
    .line 94
    goto :goto_0

    .line 95
    :cond_3
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 96
    .line 97
    .line 98
    move-result v2

    .line 99
    if-ne v2, v1, :cond_4

    .line 100
    .line 101
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k(I)I

    .line 102
    .line 103
    .line 104
    move-result v0

    .line 105
    :goto_2
    move v3, v0

    .line 106
    goto :goto_3

    .line 107
    :cond_4
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->j(I)I

    .line 108
    .line 109
    .line 110
    move-result v0

    .line 111
    goto :goto_2

    .line 112
    :goto_3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->x:Ljava/lang/CharSequence;

    .line 113
    .line 114
    invoke-static {v0}, Lorg/xbet/uikit/utils/g;->a(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 115
    .line 116
    .line 117
    move-result-object v1

    .line 118
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->h:Landroid/text/TextPaint;

    .line 119
    .line 120
    sget-object v10, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 121
    .line 122
    sget-object v12, Landroid/text/Layout$Alignment;->ALIGN_NORMAL:Landroid/text/Layout$Alignment;

    .line 123
    .line 124
    const/16 v13, 0x4f0

    .line 125
    .line 126
    const/4 v14, 0x0

    .line 127
    const/4 v4, 0x2

    .line 128
    const/4 v5, 0x0

    .line 129
    const/4 v6, 0x0

    .line 130
    const/4 v7, 0x0

    .line 131
    const/4 v8, 0x0

    .line 132
    const/4 v9, 0x0

    .line 133
    const/4 v11, 0x0

    .line 134
    invoke-static/range {v1 .. v14}, Lorg/xbet/uikit/utils/H;->d(Ljava/lang/CharSequence;Landroid/text/TextPaint;IIIIFFZLandroid/text/TextUtils$TruncateAt;ILandroid/text/Layout$Alignment;ILjava/lang/Object;)Landroid/text/StaticLayout;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->y:Landroid/text/StaticLayout;

    .line 139
    .line 140
    return-void
.end method

.method public final v()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->i:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->p:I

    .line 9
    .line 10
    sub-int/2addr v0, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->j:I

    .line 12
    .line 13
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 14
    .line 15
    if-eqz v2, :cond_0

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 22
    .line 23
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-virtual {v4}, Landroid/view/View;->getWidth()I

    .line 28
    .line 29
    .line 30
    move-result v4

    .line 31
    add-int/2addr v4, v3

    .line 32
    iget v5, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 33
    .line 34
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    invoke-virtual {v6}, Landroid/view/View;->getHeight()I

    .line 39
    .line 40
    .line 41
    move-result v6

    .line 42
    add-int/2addr v5, v6

    .line 43
    invoke-virtual {v2, v3, v3, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 44
    .line 45
    .line 46
    :cond_0
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 47
    .line 48
    if-eqz v2, :cond_2

    .line 49
    .line 50
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 51
    .line 52
    if-eqz v2, :cond_1

    .line 53
    .line 54
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 55
    .line 56
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    invoke-virtual {v3}, Landroid/view/View;->getWidth()I

    .line 61
    .line 62
    .line 63
    move-result v3

    .line 64
    add-int/2addr v2, v3

    .line 65
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 66
    .line 67
    div-int/lit8 v3, v3, 0x2

    .line 68
    .line 69
    add-int/2addr v2, v3

    .line 70
    goto :goto_0

    .line 71
    :cond_1
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 72
    .line 73
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    iget v4, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 78
    .line 79
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 80
    .line 81
    .line 82
    move-result-object v5

    .line 83
    invoke-virtual {v5}, Landroid/view/View;->getWidth()I

    .line 84
    .line 85
    .line 86
    move-result v5

    .line 87
    add-int/2addr v5, v2

    .line 88
    iget v6, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 89
    .line 90
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 91
    .line 92
    .line 93
    move-result-object v7

    .line 94
    invoke-virtual {v7}, Landroid/view/View;->getHeight()I

    .line 95
    .line 96
    .line 97
    move-result v7

    .line 98
    add-int/2addr v6, v7

    .line 99
    invoke-virtual {v3, v2, v4, v5, v6}, Landroid/view/View;->layout(IIII)V

    .line 100
    .line 101
    .line 102
    :cond_2
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 103
    .line 104
    if-eqz v2, :cond_3

    .line 105
    .line 106
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 107
    .line 108
    .line 109
    move-result-object v2

    .line 110
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 111
    .line 112
    .line 113
    move-result-object v3

    .line 114
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 115
    .line 116
    .line 117
    move-result v3

    .line 118
    add-int/2addr v3, v0

    .line 119
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 120
    .line 121
    .line 122
    move-result-object v4

    .line 123
    invoke-virtual {v4}, Landroid/view/View;->getHeight()I

    .line 124
    .line 125
    .line 126
    move-result v4

    .line 127
    add-int/2addr v4, v1

    .line 128
    invoke-virtual {v2, v0, v1, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 129
    .line 130
    .line 131
    :cond_3
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 132
    .line 133
    if-eqz v2, :cond_5

    .line 134
    .line 135
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 136
    .line 137
    if-eqz v2, :cond_4

    .line 138
    .line 139
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 140
    .line 141
    .line 142
    move-result-object v2

    .line 143
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 144
    .line 145
    .line 146
    move-result v2

    .line 147
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 148
    .line 149
    int-to-float v3, v3

    .line 150
    sub-float/2addr v2, v3

    .line 151
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 152
    .line 153
    .line 154
    move-result-object v3

    .line 155
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 156
    .line 157
    .line 158
    move-result v3

    .line 159
    int-to-float v3, v3

    .line 160
    sub-float/2addr v2, v3

    .line 161
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 162
    .line 163
    .line 164
    move-result-object v2

    .line 165
    goto :goto_1

    .line 166
    :cond_4
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 167
    .line 168
    .line 169
    move-result-object v2

    .line 170
    :goto_1
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 171
    .line 172
    .line 173
    move-result v2

    .line 174
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 175
    .line 176
    .line 177
    move-result-object v3

    .line 178
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 179
    .line 180
    .line 181
    move-result-object v4

    .line 182
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 183
    .line 184
    .line 185
    move-result v4

    .line 186
    add-int/2addr v4, v2

    .line 187
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 188
    .line 189
    .line 190
    move-result-object v5

    .line 191
    invoke-virtual {v5}, Landroid/view/View;->getHeight()I

    .line 192
    .line 193
    .line 194
    move-result v5

    .line 195
    add-int/2addr v5, v1

    .line 196
    invoke-virtual {v3, v2, v1, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 197
    .line 198
    .line 199
    :cond_5
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 200
    .line 201
    if-eqz v2, :cond_8

    .line 202
    .line 203
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 204
    .line 205
    if-eqz v2, :cond_6

    .line 206
    .line 207
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 208
    .line 209
    .line 210
    move-result-object v2

    .line 211
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 212
    .line 213
    .line 214
    move-result v2

    .line 215
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 216
    .line 217
    int-to-float v3, v3

    .line 218
    sub-float/2addr v2, v3

    .line 219
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 220
    .line 221
    .line 222
    move-result-object v3

    .line 223
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 224
    .line 225
    .line 226
    move-result v3

    .line 227
    int-to-float v3, v3

    .line 228
    sub-float/2addr v2, v3

    .line 229
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 230
    .line 231
    .line 232
    move-result-object v2

    .line 233
    goto :goto_2

    .line 234
    :cond_6
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 235
    .line 236
    if-eqz v2, :cond_7

    .line 237
    .line 238
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 239
    .line 240
    .line 241
    move-result-object v2

    .line 242
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 243
    .line 244
    .line 245
    move-result v2

    .line 246
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 247
    .line 248
    int-to-float v3, v3

    .line 249
    sub-float/2addr v2, v3

    .line 250
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 251
    .line 252
    .line 253
    move-result-object v3

    .line 254
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 255
    .line 256
    .line 257
    move-result v3

    .line 258
    int-to-float v3, v3

    .line 259
    sub-float/2addr v2, v3

    .line 260
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 261
    .line 262
    .line 263
    move-result-object v2

    .line 264
    goto :goto_2

    .line 265
    :cond_7
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 266
    .line 267
    .line 268
    move-result-object v2

    .line 269
    :goto_2
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 270
    .line 271
    .line 272
    move-result v2

    .line 273
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 274
    .line 275
    .line 276
    move-result-object v3

    .line 277
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 278
    .line 279
    .line 280
    move-result-object v4

    .line 281
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 282
    .line 283
    .line 284
    move-result v4

    .line 285
    add-int/2addr v4, v2

    .line 286
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 287
    .line 288
    .line 289
    move-result-object v5

    .line 290
    invoke-virtual {v5}, Landroid/view/View;->getHeight()I

    .line 291
    .line 292
    .line 293
    move-result v5

    .line 294
    add-int/2addr v5, v1

    .line 295
    invoke-virtual {v3, v2, v1, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 296
    .line 297
    .line 298
    :cond_8
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r:Z

    .line 299
    .line 300
    if-eqz v2, :cond_c

    .line 301
    .line 302
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 303
    .line 304
    if-eqz v2, :cond_9

    .line 305
    .line 306
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 307
    .line 308
    .line 309
    move-result-object v2

    .line 310
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 311
    .line 312
    .line 313
    move-result v2

    .line 314
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 315
    .line 316
    int-to-float v3, v3

    .line 317
    sub-float/2addr v2, v3

    .line 318
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 319
    .line 320
    .line 321
    move-result-object v3

    .line 322
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 323
    .line 324
    .line 325
    move-result v3

    .line 326
    int-to-float v3, v3

    .line 327
    sub-float/2addr v2, v3

    .line 328
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 329
    .line 330
    .line 331
    move-result-object v2

    .line 332
    goto :goto_3

    .line 333
    :cond_9
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 334
    .line 335
    if-eqz v2, :cond_a

    .line 336
    .line 337
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 338
    .line 339
    .line 340
    move-result-object v2

    .line 341
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 342
    .line 343
    .line 344
    move-result v2

    .line 345
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 346
    .line 347
    int-to-float v3, v3

    .line 348
    sub-float/2addr v2, v3

    .line 349
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 350
    .line 351
    .line 352
    move-result-object v3

    .line 353
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 354
    .line 355
    .line 356
    move-result v3

    .line 357
    int-to-float v3, v3

    .line 358
    sub-float/2addr v2, v3

    .line 359
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 360
    .line 361
    .line 362
    move-result-object v2

    .line 363
    goto :goto_3

    .line 364
    :cond_a
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 365
    .line 366
    if-eqz v2, :cond_b

    .line 367
    .line 368
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 369
    .line 370
    .line 371
    move-result-object v2

    .line 372
    invoke-virtual {v2}, Landroid/view/View;->getX()F

    .line 373
    .line 374
    .line 375
    move-result v2

    .line 376
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 377
    .line 378
    int-to-float v3, v3

    .line 379
    sub-float/2addr v2, v3

    .line 380
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 381
    .line 382
    .line 383
    move-result-object v3

    .line 384
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 385
    .line 386
    .line 387
    move-result v3

    .line 388
    int-to-float v3, v3

    .line 389
    sub-float/2addr v2, v3

    .line 390
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 391
    .line 392
    .line 393
    move-result-object v2

    .line 394
    goto :goto_3

    .line 395
    :cond_b
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 396
    .line 397
    .line 398
    move-result-object v2

    .line 399
    :goto_3
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 400
    .line 401
    .line 402
    move-result v2

    .line 403
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 404
    .line 405
    .line 406
    move-result-object v3

    .line 407
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 408
    .line 409
    .line 410
    move-result-object v4

    .line 411
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 412
    .line 413
    .line 414
    move-result v4

    .line 415
    add-int/2addr v4, v2

    .line 416
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 417
    .line 418
    .line 419
    move-result-object v5

    .line 420
    invoke-virtual {v5}, Landroid/view/View;->getHeight()I

    .line 421
    .line 422
    .line 423
    move-result v5

    .line 424
    add-int/2addr v5, v1

    .line 425
    invoke-virtual {v3, v2, v1, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 426
    .line 427
    .line 428
    :cond_c
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->s:Z

    .line 429
    .line 430
    if-eqz v2, :cond_10

    .line 431
    .line 432
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 433
    .line 434
    if-eqz v2, :cond_d

    .line 435
    .line 436
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 437
    .line 438
    .line 439
    move-result-object v0

    .line 440
    invoke-virtual {v0}, Landroid/view/View;->getX()F

    .line 441
    .line 442
    .line 443
    move-result v0

    .line 444
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 445
    .line 446
    int-to-float v2, v2

    .line 447
    sub-float/2addr v0, v2

    .line 448
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 449
    .line 450
    .line 451
    move-result-object v2

    .line 452
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 453
    .line 454
    .line 455
    move-result v2

    .line 456
    int-to-float v2, v2

    .line 457
    sub-float/2addr v0, v2

    .line 458
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 459
    .line 460
    .line 461
    move-result-object v0

    .line 462
    goto :goto_4

    .line 463
    :cond_d
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 464
    .line 465
    if-eqz v2, :cond_e

    .line 466
    .line 467
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 468
    .line 469
    .line 470
    move-result-object v0

    .line 471
    invoke-virtual {v0}, Landroid/view/View;->getX()F

    .line 472
    .line 473
    .line 474
    move-result v0

    .line 475
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 476
    .line 477
    int-to-float v2, v2

    .line 478
    sub-float/2addr v0, v2

    .line 479
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 480
    .line 481
    .line 482
    move-result-object v2

    .line 483
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 484
    .line 485
    .line 486
    move-result v2

    .line 487
    int-to-float v2, v2

    .line 488
    sub-float/2addr v0, v2

    .line 489
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 490
    .line 491
    .line 492
    move-result-object v0

    .line 493
    goto :goto_4

    .line 494
    :cond_e
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 495
    .line 496
    if-eqz v2, :cond_f

    .line 497
    .line 498
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 499
    .line 500
    .line 501
    move-result-object v0

    .line 502
    invoke-virtual {v0}, Landroid/view/View;->getX()F

    .line 503
    .line 504
    .line 505
    move-result v0

    .line 506
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 507
    .line 508
    int-to-float v2, v2

    .line 509
    sub-float/2addr v0, v2

    .line 510
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 511
    .line 512
    .line 513
    move-result-object v2

    .line 514
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 515
    .line 516
    .line 517
    move-result v2

    .line 518
    int-to-float v2, v2

    .line 519
    sub-float/2addr v0, v2

    .line 520
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 521
    .line 522
    .line 523
    move-result-object v0

    .line 524
    goto :goto_4

    .line 525
    :cond_f
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 526
    .line 527
    .line 528
    move-result-object v0

    .line 529
    :goto_4
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 530
    .line 531
    .line 532
    move-result v0

    .line 533
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 534
    .line 535
    .line 536
    move-result-object v2

    .line 537
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 538
    .line 539
    .line 540
    move-result v2

    .line 541
    div-int/lit8 v2, v2, 0x2

    .line 542
    .line 543
    add-int/2addr v0, v2

    .line 544
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 545
    .line 546
    .line 547
    move-result-object v2

    .line 548
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 549
    .line 550
    .line 551
    move-result-object v3

    .line 552
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 553
    .line 554
    .line 555
    move-result v3

    .line 556
    add-int/2addr v3, v0

    .line 557
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 558
    .line 559
    .line 560
    move-result-object v4

    .line 561
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 562
    .line 563
    .line 564
    move-result v4

    .line 565
    add-int/2addr v4, v1

    .line 566
    invoke-virtual {v2, v0, v1, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 567
    .line 568
    .line 569
    :cond_10
    return-void
.end method

.method public final w()V
    .locals 7

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->i:I

    .line 2
    .line 3
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 12
    .line 13
    sub-int/2addr v1, v2

    .line 14
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    sub-int/2addr v1, v2

    .line 23
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 28
    .line 29
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 30
    .line 31
    .line 32
    move-result-object v4

    .line 33
    invoke-virtual {v4}, Landroid/view/View;->getWidth()I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    add-int/2addr v4, v1

    .line 38
    iget v5, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 39
    .line 40
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 41
    .line 42
    .line 43
    move-result-object v6

    .line 44
    invoke-virtual {v6}, Landroid/view/View;->getHeight()I

    .line 45
    .line 46
    .line 47
    move-result v6

    .line 48
    add-int/2addr v5, v6

    .line 49
    invoke-virtual {v2, v1, v3, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 50
    .line 51
    .line 52
    :cond_0
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->q:Z

    .line 53
    .line 54
    if-eqz v1, :cond_2

    .line 55
    .line 56
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->u:Z

    .line 57
    .line 58
    if-eqz v1, :cond_1

    .line 59
    .line 60
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 61
    .line 62
    .line 63
    move-result v1

    .line 64
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 65
    .line 66
    sub-int/2addr v1, v2

    .line 67
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getTopIcon()Landroid/widget/ImageView;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 72
    .line 73
    .line 74
    move-result v2

    .line 75
    sub-int/2addr v1, v2

    .line 76
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 77
    .line 78
    div-int/lit8 v2, v2, 0x2

    .line 79
    .line 80
    sub-int/2addr v1, v2

    .line 81
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 86
    .line 87
    .line 88
    move-result v2

    .line 89
    :goto_0
    sub-int/2addr v1, v2

    .line 90
    goto :goto_1

    .line 91
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 92
    .line 93
    .line 94
    move-result v1

    .line 95
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 96
    .line 97
    sub-int/2addr v1, v2

    .line 98
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 99
    .line 100
    .line 101
    move-result-object v2

    .line 102
    invoke-virtual {v2}, Landroid/view/View;->getWidth()I

    .line 103
    .line 104
    .line 105
    move-result v2

    .line 106
    goto :goto_0

    .line 107
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 112
    .line 113
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 114
    .line 115
    .line 116
    move-result-object v4

    .line 117
    invoke-virtual {v4}, Landroid/view/View;->getWidth()I

    .line 118
    .line 119
    .line 120
    move-result v4

    .line 121
    add-int/2addr v4, v1

    .line 122
    iget v5, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->k:I

    .line 123
    .line 124
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSportImageView()Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 125
    .line 126
    .line 127
    move-result-object v6

    .line 128
    invoke-virtual {v6}, Landroid/view/View;->getHeight()I

    .line 129
    .line 130
    .line 131
    move-result v6

    .line 132
    add-int/2addr v5, v6

    .line 133
    invoke-virtual {v2, v1, v3, v4, v5}, Landroid/view/View;->layout(IIII)V

    .line 134
    .line 135
    .line 136
    :cond_2
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 137
    .line 138
    if-eqz v1, :cond_3

    .line 139
    .line 140
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 145
    .line 146
    .line 147
    move-result-object v2

    .line 148
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 149
    .line 150
    .line 151
    move-result v2

    .line 152
    add-int/2addr v2, v0

    .line 153
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 154
    .line 155
    .line 156
    move-result-object v3

    .line 157
    invoke-virtual {v3}, Landroid/view/View;->getHeight()I

    .line 158
    .line 159
    .line 160
    move-result v3

    .line 161
    add-int/2addr v3, v0

    .line 162
    invoke-virtual {v1, v0, v0, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 163
    .line 164
    .line 165
    :cond_3
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 166
    .line 167
    if-eqz v1, :cond_5

    .line 168
    .line 169
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 170
    .line 171
    if-eqz v1, :cond_4

    .line 172
    .line 173
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 174
    .line 175
    .line 176
    move-result-object v1

    .line 177
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 178
    .line 179
    .line 180
    move-result v1

    .line 181
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 182
    .line 183
    int-to-float v2, v2

    .line 184
    add-float/2addr v1, v2

    .line 185
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 186
    .line 187
    .line 188
    move-result-object v2

    .line 189
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 190
    .line 191
    .line 192
    move-result v2

    .line 193
    int-to-float v2, v2

    .line 194
    add-float/2addr v1, v2

    .line 195
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 196
    .line 197
    .line 198
    move-result-object v1

    .line 199
    goto :goto_2

    .line 200
    :cond_4
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 201
    .line 202
    .line 203
    move-result-object v1

    .line 204
    :goto_2
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 205
    .line 206
    .line 207
    move-result v1

    .line 208
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 209
    .line 210
    .line 211
    move-result-object v2

    .line 212
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 213
    .line 214
    .line 215
    move-result-object v3

    .line 216
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 217
    .line 218
    .line 219
    move-result v3

    .line 220
    add-int/2addr v3, v1

    .line 221
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 222
    .line 223
    .line 224
    move-result-object v4

    .line 225
    invoke-virtual {v4}, Landroid/view/View;->getHeight()I

    .line 226
    .line 227
    .line 228
    move-result v4

    .line 229
    add-int/2addr v4, v0

    .line 230
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 231
    .line 232
    .line 233
    :cond_5
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 234
    .line 235
    if-eqz v1, :cond_8

    .line 236
    .line 237
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 238
    .line 239
    if-eqz v1, :cond_6

    .line 240
    .line 241
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 246
    .line 247
    .line 248
    move-result v1

    .line 249
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 250
    .line 251
    int-to-float v2, v2

    .line 252
    add-float/2addr v1, v2

    .line 253
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 254
    .line 255
    .line 256
    move-result-object v2

    .line 257
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 258
    .line 259
    .line 260
    move-result v2

    .line 261
    int-to-float v2, v2

    .line 262
    add-float/2addr v1, v2

    .line 263
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 264
    .line 265
    .line 266
    move-result-object v1

    .line 267
    goto :goto_3

    .line 268
    :cond_6
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 269
    .line 270
    if-eqz v1, :cond_7

    .line 271
    .line 272
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 273
    .line 274
    .line 275
    move-result-object v1

    .line 276
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 277
    .line 278
    .line 279
    move-result v1

    .line 280
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 281
    .line 282
    int-to-float v2, v2

    .line 283
    add-float/2addr v1, v2

    .line 284
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 285
    .line 286
    .line 287
    move-result-object v2

    .line 288
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 289
    .line 290
    .line 291
    move-result v2

    .line 292
    int-to-float v2, v2

    .line 293
    add-float/2addr v1, v2

    .line 294
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 295
    .line 296
    .line 297
    move-result-object v1

    .line 298
    goto :goto_3

    .line 299
    :cond_7
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 300
    .line 301
    .line 302
    move-result-object v1

    .line 303
    :goto_3
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 304
    .line 305
    .line 306
    move-result v1

    .line 307
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 308
    .line 309
    .line 310
    move-result-object v2

    .line 311
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 312
    .line 313
    .line 314
    move-result-object v3

    .line 315
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 316
    .line 317
    .line 318
    move-result v3

    .line 319
    add-int/2addr v3, v1

    .line 320
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 321
    .line 322
    .line 323
    move-result-object v4

    .line 324
    invoke-virtual {v4}, Landroid/view/View;->getHeight()I

    .line 325
    .line 326
    .line 327
    move-result v4

    .line 328
    add-int/2addr v4, v0

    .line 329
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 330
    .line 331
    .line 332
    :cond_8
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->r:Z

    .line 333
    .line 334
    if-eqz v1, :cond_c

    .line 335
    .line 336
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 337
    .line 338
    if-eqz v1, :cond_9

    .line 339
    .line 340
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 341
    .line 342
    .line 343
    move-result-object v1

    .line 344
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 345
    .line 346
    .line 347
    move-result v1

    .line 348
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 349
    .line 350
    int-to-float v2, v2

    .line 351
    sub-float/2addr v1, v2

    .line 352
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 353
    .line 354
    .line 355
    move-result-object v2

    .line 356
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 357
    .line 358
    .line 359
    move-result v2

    .line 360
    int-to-float v2, v2

    .line 361
    sub-float/2addr v1, v2

    .line 362
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 363
    .line 364
    .line 365
    move-result-object v1

    .line 366
    goto :goto_4

    .line 367
    :cond_9
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 368
    .line 369
    if-eqz v1, :cond_a

    .line 370
    .line 371
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 372
    .line 373
    .line 374
    move-result-object v1

    .line 375
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 376
    .line 377
    .line 378
    move-result v1

    .line 379
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 380
    .line 381
    int-to-float v2, v2

    .line 382
    add-float/2addr v1, v2

    .line 383
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 384
    .line 385
    .line 386
    move-result-object v2

    .line 387
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 388
    .line 389
    .line 390
    move-result v2

    .line 391
    int-to-float v2, v2

    .line 392
    add-float/2addr v1, v2

    .line 393
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 394
    .line 395
    .line 396
    move-result-object v1

    .line 397
    goto :goto_4

    .line 398
    :cond_a
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 399
    .line 400
    if-eqz v1, :cond_b

    .line 401
    .line 402
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 403
    .line 404
    .line 405
    move-result-object v1

    .line 406
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 407
    .line 408
    .line 409
    move-result v1

    .line 410
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 411
    .line 412
    int-to-float v2, v2

    .line 413
    add-float/2addr v1, v2

    .line 414
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 415
    .line 416
    .line 417
    move-result-object v2

    .line 418
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 419
    .line 420
    .line 421
    move-result v2

    .line 422
    int-to-float v2, v2

    .line 423
    add-float/2addr v1, v2

    .line 424
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 425
    .line 426
    .line 427
    move-result-object v1

    .line 428
    goto :goto_4

    .line 429
    :cond_b
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 430
    .line 431
    .line 432
    move-result-object v1

    .line 433
    :goto_4
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 434
    .line 435
    .line 436
    move-result v1

    .line 437
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 438
    .line 439
    .line 440
    move-result-object v2

    .line 441
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 442
    .line 443
    .line 444
    move-result-object v3

    .line 445
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 446
    .line 447
    .line 448
    move-result v3

    .line 449
    add-int/2addr v3, v1

    .line 450
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 451
    .line 452
    .line 453
    move-result-object v4

    .line 454
    invoke-virtual {v4}, Landroid/view/View;->getHeight()I

    .line 455
    .line 456
    .line 457
    move-result v4

    .line 458
    add-int/2addr v4, v0

    .line 459
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 460
    .line 461
    .line 462
    :cond_c
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->s:Z

    .line 463
    .line 464
    if-eqz v1, :cond_10

    .line 465
    .line 466
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->t:Z

    .line 467
    .line 468
    if-eqz v1, :cond_d

    .line 469
    .line 470
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getZoneButton()Lcom/google/android/material/button/MaterialButton;

    .line 471
    .line 472
    .line 473
    move-result-object v1

    .line 474
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 475
    .line 476
    .line 477
    move-result v1

    .line 478
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 479
    .line 480
    int-to-float v2, v2

    .line 481
    sub-float/2addr v1, v2

    .line 482
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 483
    .line 484
    .line 485
    move-result-object v2

    .line 486
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 487
    .line 488
    .line 489
    move-result v2

    .line 490
    int-to-float v2, v2

    .line 491
    sub-float/2addr v1, v2

    .line 492
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 493
    .line 494
    .line 495
    move-result-object v1

    .line 496
    goto :goto_5

    .line 497
    :cond_d
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->v:Z

    .line 498
    .line 499
    if-eqz v1, :cond_e

    .line 500
    .line 501
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getNotificationButton()Lcom/google/android/material/button/MaterialButton;

    .line 502
    .line 503
    .line 504
    move-result-object v1

    .line 505
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 506
    .line 507
    .line 508
    move-result v1

    .line 509
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 510
    .line 511
    int-to-float v2, v2

    .line 512
    add-float/2addr v1, v2

    .line 513
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 514
    .line 515
    .line 516
    move-result-object v2

    .line 517
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 518
    .line 519
    .line 520
    move-result v2

    .line 521
    int-to-float v2, v2

    .line 522
    add-float/2addr v1, v2

    .line 523
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 524
    .line 525
    .line 526
    move-result-object v1

    .line 527
    goto :goto_5

    .line 528
    :cond_e
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->w:Z

    .line 529
    .line 530
    if-eqz v1, :cond_f

    .line 531
    .line 532
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getFavoriteButton()Lcom/google/android/material/button/MaterialButton;

    .line 533
    .line 534
    .line 535
    move-result-object v1

    .line 536
    invoke-virtual {v1}, Landroid/view/View;->getX()F

    .line 537
    .line 538
    .line 539
    move-result v1

    .line 540
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->l:I

    .line 541
    .line 542
    int-to-float v2, v2

    .line 543
    add-float/2addr v1, v2

    .line 544
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getStreamButton()Lcom/google/android/material/button/MaterialButton;

    .line 545
    .line 546
    .line 547
    move-result-object v2

    .line 548
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 549
    .line 550
    .line 551
    move-result v2

    .line 552
    int-to-float v2, v2

    .line 553
    add-float/2addr v1, v2

    .line 554
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 555
    .line 556
    .line 557
    move-result-object v1

    .line 558
    goto :goto_5

    .line 559
    :cond_f
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 560
    .line 561
    .line 562
    move-result-object v1

    .line 563
    :goto_5
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 564
    .line 565
    .line 566
    move-result v1

    .line 567
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 568
    .line 569
    .line 570
    move-result-object v2

    .line 571
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 572
    .line 573
    .line 574
    move-result v2

    .line 575
    div-int/lit8 v2, v2, 0x2

    .line 576
    .line 577
    add-int/2addr v1, v2

    .line 578
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 579
    .line 580
    .line 581
    move-result-object v2

    .line 582
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 583
    .line 584
    .line 585
    move-result-object v3

    .line 586
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 587
    .line 588
    .line 589
    move-result v3

    .line 590
    add-int/2addr v3, v1

    .line 591
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/top/EventCardHeader;->getSoonTextView()Landroid/widget/TextView;

    .line 592
    .line 593
    .line 594
    move-result-object v4

    .line 595
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 596
    .line 597
    .line 598
    move-result v4

    .line 599
    add-int/2addr v4, v0

    .line 600
    invoke-virtual {v2, v1, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 601
    .line 602
    .line 603
    :cond_10
    return-void
.end method
