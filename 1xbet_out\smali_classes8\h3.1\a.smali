.class public final synthetic Lh3/a;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static a(Lh3/b;Lcoil3/o;)V
    .locals 0

    .line 1
    return-void
.end method

.method public static b(Lh3/b;Lcoil3/o;)V
    .locals 0

    .line 1
    return-void
.end method

.method public static c(Lh3/b;Lcoil3/o;)V
    .locals 0
    .param p1    # Lcoil3/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method
