.class final Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.tile_matching.presentation.game.TileMatchingGameFragment$observeGameState$1"
    f = "TileMatchingGameFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->g3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;

    iget-object v1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;-><init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_8

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$a;->a()Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->I2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Lcom/xbet/onexuser/domain/entity/onexgame/configs/OneXGamesType;)V

    .line 28
    .line 29
    .line 30
    goto/16 :goto_0

    .line 31
    .line 32
    :cond_0
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;

    .line 33
    .line 34
    if-eqz v0, :cond_1

    .line 35
    .line 36
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 37
    .line 38
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 43
    .line 44
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;

    .line 45
    .line 46
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;->a()Ljava/util/List;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;->c()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$d;->b()Ljava/util/List;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-virtual {v0, v1, v2, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->A(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 59
    .line 60
    .line 61
    goto/16 :goto_0

    .line 62
    .line 63
    :cond_1
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;

    .line 64
    .line 65
    if-eqz v0, :cond_2

    .line 66
    .line 67
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 68
    .line 69
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 74
    .line 75
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;

    .line 76
    .line 77
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;->a()Ljava/util/List;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$f;->b()Ljava/util/List;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    invoke-virtual {v0, v1, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->p(Ljava/util/List;Ljava/util/List;)V

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_2
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 90
    .line 91
    if-eqz v0, :cond_3

    .line 92
    .line 93
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 94
    .line 95
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 100
    .line 101
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;

    .line 102
    .line 103
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;->a()Ljava/util/List;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    invoke-virtual {v0, v1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->setCells$tile_matching_release(Ljava/util/List;)V

    .line 108
    .line 109
    .line 110
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;->b()Ljava/util/List;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 115
    .line 116
    .line 117
    move-result v0

    .line 118
    if-nez v0, :cond_6

    .line 119
    .line 120
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 121
    .line 122
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 127
    .line 128
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$g;->b()Ljava/util/List;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->setWinCells$tile_matching_release(Ljava/util/List;)V

    .line 133
    .line 134
    .line 135
    goto :goto_0

    .line 136
    :cond_3
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$e;

    .line 137
    .line 138
    if-eqz v0, :cond_4

    .line 139
    .line 140
    iget-object p1, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 141
    .line 142
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    invoke-static {p1, v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->E2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;Ljava/util/List;)V

    .line 147
    .line 148
    .line 149
    goto :goto_0

    .line 150
    :cond_4
    instance-of v0, p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;

    .line 151
    .line 152
    if-eqz v0, :cond_5

    .line 153
    .line 154
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment$observeGameState$1;->this$0:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;

    .line 155
    .line 156
    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;->J2(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameFragment;)LxT0/a;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    iget-object v0, v0, LxT0/a;->k:Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;

    .line 161
    .line 162
    check-cast p1, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;

    .line 163
    .line 164
    invoke-virtual {p1}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$c;->a()Z

    .line 165
    .line 166
    .line 167
    move-result p1

    .line 168
    invoke-virtual {v0, p1}, Lorg/xbet/tile_matching/presentation/views/TileMatchingGameFieldView;->t(Z)V

    .line 169
    .line 170
    .line 171
    goto :goto_0

    .line 172
    :cond_5
    sget-object v0, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel$d$b;

    .line 173
    .line 174
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 175
    .line 176
    .line 177
    move-result p1

    .line 178
    if-eqz p1, :cond_7

    .line 179
    .line 180
    :cond_6
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 181
    .line 182
    return-object p1

    .line 183
    :cond_7
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 184
    .line 185
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 186
    .line 187
    .line 188
    throw p1

    .line 189
    :cond_8
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 190
    .line 191
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 192
    .line 193
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 194
    .line 195
    .line 196
    throw p1
.end method
