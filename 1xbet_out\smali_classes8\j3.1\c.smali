.class public final Lj3/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lj3/e;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001J\u000f\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0003\u0010\u0004R\u0014\u0010\u0007\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0003\u0010\u0006R\u0014\u0010\u000b\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\nR\u0017\u0010\u0010\u001a\u00020\u000c8\u0006\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u000e\u001a\u0004\u0008\t\u0010\u000fR\u0017\u0010\u0015\u001a\u00020\u00118\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u0013\u001a\u0004\u0008\r\u0010\u0014\u00a8\u0006\u0016"
    }
    d2 = {
        "Lj3/c;",
        "Lj3/e;",
        "",
        "a",
        "()V",
        "Lj3/f;",
        "Lj3/f;",
        "target",
        "Lcoil3/request/i;",
        "b",
        "Lcoil3/request/i;",
        "result",
        "",
        "c",
        "I",
        "()I",
        "durationMillis",
        "",
        "d",
        "Z",
        "()Z",
        "preferExactIntrinsicSize",
        "coil-core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lj3/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcoil3/request/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I

.field public final d:Z


# virtual methods
.method public a()V
    .locals 7

    .line 1
    new-instance v0, Lj3/b;

    .line 2
    .line 3
    iget-object v1, p0, Lj3/c;->a:Lj3/f;

    .line 4
    .line 5
    invoke-interface {v1}, Lj3/f;->b()Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v2, p0, Lj3/c;->b:Lcoil3/request/i;

    .line 10
    .line 11
    invoke-interface {v2}, Lcoil3/request/i;->getImage()Lcoil3/o;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    if-eqz v2, :cond_0

    .line 16
    .line 17
    iget-object v3, p0, Lj3/c;->a:Lj3/f;

    .line 18
    .line 19
    invoke-interface {v3}, Lj3/f;->getView()Landroid/view/View;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-virtual {v3}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-static {v2, v3}, Lcoil3/v;->a(Lcoil3/o;Landroid/content/res/Resources;)Landroid/graphics/drawable/Drawable;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    goto :goto_0

    .line 32
    :cond_0
    const/4 v2, 0x0

    .line 33
    :goto_0
    iget-object v3, p0, Lj3/c;->b:Lcoil3/request/i;

    .line 34
    .line 35
    invoke-interface {v3}, Lcoil3/request/i;->a()Lcoil3/request/e;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    invoke-virtual {v3}, Lcoil3/request/e;->w()Lcoil3/size/Scale;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    iget v4, p0, Lj3/c;->c:I

    .line 44
    .line 45
    iget-object v5, p0, Lj3/c;->b:Lcoil3/request/i;

    .line 46
    .line 47
    instance-of v6, v5, Lcoil3/request/r;

    .line 48
    .line 49
    if-eqz v6, :cond_2

    .line 50
    .line 51
    check-cast v5, Lcoil3/request/r;

    .line 52
    .line 53
    invoke-virtual {v5}, Lcoil3/request/r;->c()Z

    .line 54
    .line 55
    .line 56
    move-result v5

    .line 57
    if-nez v5, :cond_1

    .line 58
    .line 59
    goto :goto_1

    .line 60
    :cond_1
    const/4 v5, 0x0

    .line 61
    goto :goto_2

    .line 62
    :cond_2
    :goto_1
    const/4 v5, 0x1

    .line 63
    :goto_2
    iget-boolean v6, p0, Lj3/c;->d:Z

    .line 64
    .line 65
    invoke-direct/range {v0 .. v6}, Lj3/b;-><init>(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Lcoil3/size/Scale;IZZ)V

    .line 66
    .line 67
    .line 68
    iget-object v1, p0, Lj3/c;->b:Lcoil3/request/i;

    .line 69
    .line 70
    instance-of v2, v1, Lcoil3/request/r;

    .line 71
    .line 72
    if-eqz v2, :cond_3

    .line 73
    .line 74
    iget-object v1, p0, Lj3/c;->a:Lj3/f;

    .line 75
    .line 76
    invoke-static {v0}, Lcoil3/v;->c(Landroid/graphics/drawable/Drawable;)Lcoil3/o;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-interface {v1, v0}, Lh3/b;->c(Lcoil3/o;)V

    .line 81
    .line 82
    .line 83
    return-void

    .line 84
    :cond_3
    instance-of v1, v1, Lcoil3/request/d;

    .line 85
    .line 86
    if-eqz v1, :cond_4

    .line 87
    .line 88
    iget-object v1, p0, Lj3/c;->a:Lj3/f;

    .line 89
    .line 90
    invoke-static {v0}, Lcoil3/v;->c(Landroid/graphics/drawable/Drawable;)Lcoil3/o;

    .line 91
    .line 92
    .line 93
    move-result-object v0

    .line 94
    invoke-interface {v1, v0}, Lh3/b;->d(Lcoil3/o;)V

    .line 95
    .line 96
    .line 97
    return-void

    .line 98
    :cond_4
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 99
    .line 100
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 101
    .line 102
    .line 103
    throw v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lj3/c;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final c()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lj3/c;->d:Z

    .line 2
    .line 3
    return v0
.end method
