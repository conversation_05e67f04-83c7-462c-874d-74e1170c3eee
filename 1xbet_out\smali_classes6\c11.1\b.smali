.class public final Lc11/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001aO\u0010\n\u001a\u00020\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u00002\u0016\u0008\u0002\u0010\u0007\u001a\u0010\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u00052\n\u0008\u0002\u0010\t\u001a\u0004\u0018\u00010\u0008H\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "",
        "checked",
        "Landroidx/compose/ui/l;",
        "modifier",
        "enabled",
        "Lkotlin/Function1;",
        "",
        "onCheckChange",
        "Landroidx/compose/foundation/interaction/i;",
        "interactionSource",
        "b",
        "(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;Landroidx/compose/runtime/j;II)V",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, Lc11/b;->c(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;Landroidx/compose/runtime/j;II)V
    .locals 54
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Landroidx/compose/ui/l;",
            "Z",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/foundation/interaction/i;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .line 1
    move/from16 v6, p6

    .line 2
    .line 3
    const v0, -0x19e1da72

    .line 4
    .line 5
    .line 6
    move-object/from16 v1, p5

    .line 7
    .line 8
    invoke-interface {v1, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 9
    .line 10
    .line 11
    move-result-object v14

    .line 12
    and-int/lit8 v1, p7, 0x1

    .line 13
    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    or-int/lit8 v1, v6, 0x6

    .line 17
    .line 18
    move v2, v1

    .line 19
    move/from16 v1, p0

    .line 20
    .line 21
    goto :goto_1

    .line 22
    :cond_0
    and-int/lit8 v1, v6, 0x6

    .line 23
    .line 24
    if-nez v1, :cond_2

    .line 25
    .line 26
    move/from16 v1, p0

    .line 27
    .line 28
    invoke-interface {v14, v1}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-eqz v2, :cond_1

    .line 33
    .line 34
    const/4 v2, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const/4 v2, 0x2

    .line 37
    :goto_0
    or-int/2addr v2, v6

    .line 38
    goto :goto_1

    .line 39
    :cond_2
    move/from16 v1, p0

    .line 40
    .line 41
    move v2, v6

    .line 42
    :goto_1
    and-int/lit8 v3, p7, 0x2

    .line 43
    .line 44
    if-eqz v3, :cond_4

    .line 45
    .line 46
    or-int/lit8 v2, v2, 0x30

    .line 47
    .line 48
    :cond_3
    move-object/from16 v4, p1

    .line 49
    .line 50
    goto :goto_3

    .line 51
    :cond_4
    and-int/lit8 v4, v6, 0x30

    .line 52
    .line 53
    if-nez v4, :cond_3

    .line 54
    .line 55
    move-object/from16 v4, p1

    .line 56
    .line 57
    invoke-interface {v14, v4}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v5

    .line 61
    if-eqz v5, :cond_5

    .line 62
    .line 63
    const/16 v5, 0x20

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_5
    const/16 v5, 0x10

    .line 67
    .line 68
    :goto_2
    or-int/2addr v2, v5

    .line 69
    :goto_3
    and-int/lit8 v5, p7, 0x4

    .line 70
    .line 71
    if-eqz v5, :cond_7

    .line 72
    .line 73
    or-int/lit16 v2, v2, 0x180

    .line 74
    .line 75
    :cond_6
    move/from16 v7, p2

    .line 76
    .line 77
    goto :goto_5

    .line 78
    :cond_7
    and-int/lit16 v7, v6, 0x180

    .line 79
    .line 80
    if-nez v7, :cond_6

    .line 81
    .line 82
    move/from16 v7, p2

    .line 83
    .line 84
    invoke-interface {v14, v7}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 85
    .line 86
    .line 87
    move-result v8

    .line 88
    if-eqz v8, :cond_8

    .line 89
    .line 90
    const/16 v8, 0x100

    .line 91
    .line 92
    goto :goto_4

    .line 93
    :cond_8
    const/16 v8, 0x80

    .line 94
    .line 95
    :goto_4
    or-int/2addr v2, v8

    .line 96
    :goto_5
    and-int/lit8 v8, p7, 0x8

    .line 97
    .line 98
    if-eqz v8, :cond_a

    .line 99
    .line 100
    or-int/lit16 v2, v2, 0xc00

    .line 101
    .line 102
    :cond_9
    move-object/from16 v9, p3

    .line 103
    .line 104
    goto :goto_7

    .line 105
    :cond_a
    and-int/lit16 v9, v6, 0xc00

    .line 106
    .line 107
    if-nez v9, :cond_9

    .line 108
    .line 109
    move-object/from16 v9, p3

    .line 110
    .line 111
    invoke-interface {v14, v9}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 112
    .line 113
    .line 114
    move-result v10

    .line 115
    if-eqz v10, :cond_b

    .line 116
    .line 117
    const/16 v10, 0x800

    .line 118
    .line 119
    goto :goto_6

    .line 120
    :cond_b
    const/16 v10, 0x400

    .line 121
    .line 122
    :goto_6
    or-int/2addr v2, v10

    .line 123
    :goto_7
    and-int/lit8 v10, p7, 0x10

    .line 124
    .line 125
    if-eqz v10, :cond_d

    .line 126
    .line 127
    or-int/lit16 v2, v2, 0x6000

    .line 128
    .line 129
    :cond_c
    move-object/from16 v11, p4

    .line 130
    .line 131
    goto :goto_9

    .line 132
    :cond_d
    and-int/lit16 v11, v6, 0x6000

    .line 133
    .line 134
    if-nez v11, :cond_c

    .line 135
    .line 136
    move-object/from16 v11, p4

    .line 137
    .line 138
    invoke-interface {v14, v11}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 139
    .line 140
    .line 141
    move-result v12

    .line 142
    if-eqz v12, :cond_e

    .line 143
    .line 144
    const/16 v12, 0x4000

    .line 145
    .line 146
    goto :goto_8

    .line 147
    :cond_e
    const/16 v12, 0x2000

    .line 148
    .line 149
    :goto_8
    or-int/2addr v2, v12

    .line 150
    :goto_9
    and-int/lit16 v12, v2, 0x2493

    .line 151
    .line 152
    const/16 v13, 0x2492

    .line 153
    .line 154
    if-ne v12, v13, :cond_10

    .line 155
    .line 156
    invoke-interface {v14}, Landroidx/compose/runtime/j;->c()Z

    .line 157
    .line 158
    .line 159
    move-result v12

    .line 160
    if-nez v12, :cond_f

    .line 161
    .line 162
    goto :goto_a

    .line 163
    :cond_f
    invoke-interface {v14}, Landroidx/compose/runtime/j;->n()V

    .line 164
    .line 165
    .line 166
    move-object v2, v4

    .line 167
    move v3, v7

    .line 168
    move-object v4, v9

    .line 169
    move-object v5, v11

    .line 170
    goto/16 :goto_f

    .line 171
    .line 172
    :cond_10
    :goto_a
    if-eqz v3, :cond_11

    .line 173
    .line 174
    sget-object v3, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 175
    .line 176
    goto :goto_b

    .line 177
    :cond_11
    move-object v3, v4

    .line 178
    :goto_b
    if-eqz v5, :cond_12

    .line 179
    .line 180
    const/4 v4, 0x1

    .line 181
    goto :goto_c

    .line 182
    :cond_12
    move v4, v7

    .line 183
    :goto_c
    const/4 v5, 0x0

    .line 184
    if-eqz v8, :cond_13

    .line 185
    .line 186
    move-object v8, v5

    .line 187
    goto :goto_d

    .line 188
    :cond_13
    move-object v8, v9

    .line 189
    :goto_d
    if-eqz v10, :cond_14

    .line 190
    .line 191
    move-object v13, v5

    .line 192
    goto :goto_e

    .line 193
    :cond_14
    move-object v13, v11

    .line 194
    :goto_e
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 195
    .line 196
    .line 197
    move-result v5

    .line 198
    if-eqz v5, :cond_15

    .line 199
    .line 200
    const/4 v5, -0x1

    .line 201
    const-string v7, "org.xbet.uikit.compose.components.ds_switch.DsSwitch (DsSwitch.kt:21)"

    .line 202
    .line 203
    invoke-static {v0, v2, v5, v7}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 204
    .line 205
    .line 206
    :cond_15
    new-instance v12, Landroidx/compose/material3/h1;

    .line 207
    .line 208
    sget-object v0, LB11/e;->a:LB11/e;

    .line 209
    .line 210
    const/4 v5, 0x6

    .line 211
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 212
    .line 213
    .line 214
    move-result-object v7

    .line 215
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 216
    .line 217
    .line 218
    move-result-wide v16

    .line 219
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 220
    .line 221
    .line 222
    move-result-object v7

    .line 223
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getPrimary-0d7_KjU()J

    .line 224
    .line 225
    .line 226
    move-result-wide v18

    .line 227
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 228
    .line 229
    .line 230
    move-result-object v7

    .line 231
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getPrimary-0d7_KjU()J

    .line 232
    .line 233
    .line 234
    move-result-wide v20

    .line 235
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 236
    .line 237
    .line 238
    move-result-object v7

    .line 239
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 240
    .line 241
    .line 242
    move-result-wide v22

    .line 243
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 244
    .line 245
    .line 246
    move-result-object v7

    .line 247
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 248
    .line 249
    .line 250
    move-result-wide v24

    .line 251
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 252
    .line 253
    .line 254
    move-result-object v7

    .line 255
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 256
    .line 257
    .line 258
    move-result-wide v26

    .line 259
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 260
    .line 261
    .line 262
    move-result-object v7

    .line 263
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 264
    .line 265
    .line 266
    move-result-wide v28

    .line 267
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 268
    .line 269
    .line 270
    move-result-object v7

    .line 271
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 272
    .line 273
    .line 274
    move-result-wide v30

    .line 275
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 276
    .line 277
    .line 278
    move-result-object v7

    .line 279
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 280
    .line 281
    .line 282
    move-result-wide v32

    .line 283
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 284
    .line 285
    .line 286
    move-result-object v7

    .line 287
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getPrimary-0d7_KjU()J

    .line 288
    .line 289
    .line 290
    move-result-wide v34

    .line 291
    const/16 v40, 0xe

    .line 292
    .line 293
    const/16 v41, 0x0

    .line 294
    .line 295
    const/high16 v36, 0x3f000000    # 0.5f

    .line 296
    .line 297
    const/16 v37, 0x0

    .line 298
    .line 299
    const/16 v38, 0x0

    .line 300
    .line 301
    const/16 v39, 0x0

    .line 302
    .line 303
    invoke-static/range {v34 .. v41}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 304
    .line 305
    .line 306
    move-result-wide v34

    .line 307
    sget-object v7, Landroidx/compose/ui/graphics/v0;->b:Landroidx/compose/ui/graphics/v0$a;

    .line 308
    .line 309
    invoke-virtual {v7}, Landroidx/compose/ui/graphics/v0$a;->d()J

    .line 310
    .line 311
    .line 312
    move-result-wide v36

    .line 313
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 314
    .line 315
    .line 316
    move-result-object v7

    .line 317
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 318
    .line 319
    .line 320
    move-result-wide v38

    .line 321
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 322
    .line 323
    .line 324
    move-result-object v7

    .line 325
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 326
    .line 327
    .line 328
    move-result-wide v40

    .line 329
    const/16 v46, 0xe

    .line 330
    .line 331
    const/16 v47, 0x0

    .line 332
    .line 333
    const/high16 v42, 0x3f000000    # 0.5f

    .line 334
    .line 335
    const/16 v43, 0x0

    .line 336
    .line 337
    const/16 v44, 0x0

    .line 338
    .line 339
    const/16 v45, 0x0

    .line 340
    .line 341
    invoke-static/range {v40 .. v47}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 342
    .line 343
    .line 344
    move-result-wide v40

    .line 345
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 346
    .line 347
    .line 348
    move-result-object v7

    .line 349
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getBackgroundContent-0d7_KjU()J

    .line 350
    .line 351
    .line 352
    move-result-wide v42

    .line 353
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 354
    .line 355
    .line 356
    move-result-object v7

    .line 357
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 358
    .line 359
    .line 360
    move-result-wide v44

    .line 361
    const/16 v50, 0xe

    .line 362
    .line 363
    const/16 v51, 0x0

    .line 364
    .line 365
    const/high16 v46, 0x3f000000    # 0.5f

    .line 366
    .line 367
    const/16 v47, 0x0

    .line 368
    .line 369
    const/16 v48, 0x0

    .line 370
    .line 371
    const/16 v49, 0x0

    .line 372
    .line 373
    invoke-static/range {v44 .. v51}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 374
    .line 375
    .line 376
    move-result-wide v44

    .line 377
    invoke-virtual {v0, v14, v5}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 378
    .line 379
    .line 380
    move-result-object v0

    .line 381
    invoke-virtual {v0}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary60-0d7_KjU()J

    .line 382
    .line 383
    .line 384
    move-result-wide v46

    .line 385
    const/16 v52, 0xe

    .line 386
    .line 387
    const/16 v53, 0x0

    .line 388
    .line 389
    const/high16 v48, 0x3f000000    # 0.5f

    .line 390
    .line 391
    const/16 v50, 0x0

    .line 392
    .line 393
    const/16 v51, 0x0

    .line 394
    .line 395
    invoke-static/range {v46 .. v53}, Landroidx/compose/ui/graphics/v0;->k(JFFFFILjava/lang/Object;)J

    .line 396
    .line 397
    .line 398
    move-result-wide v46

    .line 399
    const/16 v48, 0x0

    .line 400
    .line 401
    move-object v15, v12

    .line 402
    invoke-direct/range {v15 .. v48}, Landroidx/compose/material3/h1;-><init>(JJJJJJJJJJJJJJJJLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 403
    .line 404
    .line 405
    and-int/lit8 v0, v2, 0xe

    .line 406
    .line 407
    shr-int/lit8 v7, v2, 0x6

    .line 408
    .line 409
    and-int/lit8 v7, v7, 0x70

    .line 410
    .line 411
    or-int/2addr v0, v7

    .line 412
    shl-int/lit8 v7, v2, 0x3

    .line 413
    .line 414
    and-int/lit16 v7, v7, 0x380

    .line 415
    .line 416
    or-int/2addr v0, v7

    .line 417
    shl-int/2addr v2, v5

    .line 418
    const v5, 0xe000

    .line 419
    .line 420
    .line 421
    and-int/2addr v5, v2

    .line 422
    or-int/2addr v0, v5

    .line 423
    const/high16 v5, 0x380000

    .line 424
    .line 425
    and-int/2addr v2, v5

    .line 426
    or-int v15, v0, v2

    .line 427
    .line 428
    const/16 v16, 0x8

    .line 429
    .line 430
    const/4 v10, 0x0

    .line 431
    move v7, v1

    .line 432
    move-object v9, v3

    .line 433
    move v11, v4

    .line 434
    invoke-static/range {v7 .. v16}, Landroidx/compose/material3/SwitchKt;->a(ZLkotlin/jvm/functions/Function1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;ZLandroidx/compose/material3/h1;Landroidx/compose/foundation/interaction/i;Landroidx/compose/runtime/j;II)V

    .line 435
    .line 436
    .line 437
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 438
    .line 439
    .line 440
    move-result v0

    .line 441
    if-eqz v0, :cond_16

    .line 442
    .line 443
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 444
    .line 445
    .line 446
    :cond_16
    move-object v4, v8

    .line 447
    move-object v2, v9

    .line 448
    move v3, v11

    .line 449
    move-object v5, v13

    .line 450
    :goto_f
    invoke-interface {v14}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 451
    .line 452
    .line 453
    move-result-object v8

    .line 454
    if-eqz v8, :cond_17

    .line 455
    .line 456
    new-instance v0, Lc11/a;

    .line 457
    .line 458
    move/from16 v1, p0

    .line 459
    .line 460
    move/from16 v7, p7

    .line 461
    .line 462
    invoke-direct/range {v0 .. v7}, Lc11/a;-><init>(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;II)V

    .line 463
    .line 464
    .line 465
    invoke-interface {v8, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 466
    .line 467
    .line 468
    :cond_17
    return-void
.end method

.method public static final c(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 8

    .line 1
    or-int/lit8 p5, p5, 0x1

    .line 2
    .line 3
    invoke-static {p5}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v6

    .line 7
    move v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move v2, p2

    .line 10
    move-object v3, p3

    .line 11
    move-object v4, p4

    .line 12
    move v7, p6

    .line 13
    move-object v5, p7

    .line 14
    invoke-static/range {v0 .. v7}, Lc11/b;->b(ZLandroidx/compose/ui/l;ZLkotlin/jvm/functions/Function1;Landroidx/compose/foundation/interaction/i;Landroidx/compose/runtime/j;II)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method
