.class final Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.special_event.impl.who_win.presentation.delegate.WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2"
    f = "WhoWinCardViewModelDelegateImpl.kt"
    l = {
        0x6a,
        0x6f
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->f2(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $simpleBetZip:Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

.field final synthetic $singleBetGame:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

.field label:I

.field final synthetic this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;",
            "Lorg/xbet/betting/core/coupon/models/SingleBetGame;",
            "Lorg/xbet/betting/core/coupon/models/SimpleBetZip;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$singleBetGame:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iput-object p3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$simpleBetZip:Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;

    iget-object v0, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$singleBetGame:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    iget-object v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$simpleBetZip:Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;-><init>(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto :goto_2

    .line 19
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1

    .line 27
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    .line 35
    .line 36
    invoke-static {p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->o(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Ltw/j;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$singleBetGame:Lorg/xbet/betting/core/coupon/models/SingleBetGame;

    .line 41
    .line 42
    iget-object v4, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->$simpleBetZip:Lorg/xbet/betting/core/coupon/models/SimpleBetZip;

    .line 43
    .line 44
    sget-object v5, Lorg/xbet/betting/core/coupon/models/CouponEntryFeature;->DEFAULT:Lorg/xbet/betting/core/coupon/models/CouponEntryFeature;

    .line 45
    .line 46
    iput v3, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->label:I

    .line 47
    .line 48
    invoke-interface {p1, v1, v4, v5, p0}, Ltw/j;->a(Lorg/xbet/betting/core/coupon/models/SingleBetGame;Lorg/xbet/betting/core/coupon/models/SimpleBetZip;Lorg/xbet/betting/core/coupon/models/CouponEntryFeature;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    if-ne p1, v0, :cond_3

    .line 53
    .line 54
    goto :goto_1

    .line 55
    :cond_3
    :goto_0
    check-cast p1, LNn/d$c;

    .line 56
    .line 57
    iget-object v1, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->this$0:Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;

    .line 58
    .line 59
    invoke-static {v1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;->l(Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl;)Lkotlinx/coroutines/flow/V;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v3, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$b;

    .line 64
    .line 65
    invoke-direct {v3, p1}, Lorg/xbet/special_event/impl/who_win/presentation/delegate/a$b;-><init>(LNn/d;)V

    .line 66
    .line 67
    .line 68
    iput v2, p0, Lorg/xbet/special_event/impl/who_win/presentation/delegate/WhoWinCardViewModelDelegateImpl$onReplaceCouponClicked$2;->label:I

    .line 69
    .line 70
    invoke-interface {v1, v3, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    if-ne p1, v0, :cond_4

    .line 75
    .line 76
    :goto_1
    return-object v0

    .line 77
    :cond_4
    :goto_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 78
    .line 79
    return-object p1
.end method
