.class public final Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;,
        Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00be\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008<\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001:\u0004\u00d7\u0001\u00d8\u0001B\u0083\u0002\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u00a2\u0006\u0004\u0008@\u0010AJ\u000f\u0010C\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008C\u0010DJ\u000f\u0010E\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008E\u0010DJ\u000f\u0010F\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008F\u0010DJ\u000f\u0010H\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008H\u0010IJ\u000f\u0010J\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008J\u0010DJ\u000f\u0010K\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008K\u0010DJ\u000f\u0010L\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008L\u0010DJ\u000f\u0010M\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008M\u0010DJ\u0014\u0010O\u001a\u00020N*\u00020\u0000H\u0082@\u00a2\u0006\u0004\u0008O\u0010PJ\u0010\u0010Q\u001a\u00020NH\u0082@\u00a2\u0006\u0004\u0008Q\u0010RJ\u0017\u0010U\u001a\u00020N2\u0006\u0010T\u001a\u00020SH\u0002\u00a2\u0006\u0004\u0008U\u0010VJ\u0017\u0010Y\u001a\u00020B2\u0006\u0010X\u001a\u00020WH\u0002\u00a2\u0006\u0004\u0008Y\u0010ZJ\r\u0010[\u001a\u00020B\u00a2\u0006\u0004\u0008[\u0010DJ\r\u0010\\\u001a\u00020B\u00a2\u0006\u0004\u0008\\\u0010DJ\r\u0010]\u001a\u00020B\u00a2\u0006\u0004\u0008]\u0010DJ\r\u0010^\u001a\u00020B\u00a2\u0006\u0004\u0008^\u0010DJ\r\u0010_\u001a\u00020B\u00a2\u0006\u0004\u0008_\u0010DJ\r\u0010`\u001a\u00020B\u00a2\u0006\u0004\u0008`\u0010DJ\r\u0010a\u001a\u00020B\u00a2\u0006\u0004\u0008a\u0010DJ\u0017\u0010b\u001a\u00020B2\u0008\u0010T\u001a\u0004\u0018\u00010S\u00a2\u0006\u0004\u0008b\u0010cJ\u0017\u0010d\u001a\u00020B2\u0008\u0010T\u001a\u0004\u0018\u00010S\u00a2\u0006\u0004\u0008d\u0010cJ\u0015\u0010f\u001a\u00020B2\u0006\u0010e\u001a\u00020G\u00a2\u0006\u0004\u0008f\u0010gJ\r\u0010h\u001a\u00020B\u00a2\u0006\u0004\u0008h\u0010DJ\r\u0010i\u001a\u00020B\u00a2\u0006\u0004\u0008i\u0010DJ\u0013\u0010l\u001a\u0008\u0012\u0004\u0012\u00020k0j\u00a2\u0006\u0004\u0008l\u0010mJ\u0013\u0010o\u001a\u0008\u0012\u0004\u0012\u00020n0j\u00a2\u0006\u0004\u0008o\u0010mJ\u0013\u0010q\u001a\u0008\u0012\u0004\u0012\u00020p0j\u00a2\u0006\u0004\u0008q\u0010mJ\u0013\u0010r\u001a\u0008\u0012\u0004\u0012\u00020N0j\u00a2\u0006\u0004\u0008r\u0010mJ\u0013\u0010t\u001a\u0008\u0012\u0004\u0012\u00020s0j\u00a2\u0006\u0004\u0008t\u0010mJ\r\u0010u\u001a\u00020B\u00a2\u0006\u0004\u0008u\u0010DJ\r\u0010v\u001a\u00020B\u00a2\u0006\u0004\u0008v\u0010DR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0091\u0001\u0010\u0092\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0099\u0001\u0010\u009a\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009d\u0001\u0010\u009e\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a1\u0001\u0010\u00a2\u0001R\u0016\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u00a6\u0001R\u0015\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008^\u0010\u00a7\u0001R\u0016\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a8\u0001\u0010\u00a9\u0001R\u0016\u00109\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0016\u0010;\u001a\u00020:8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ac\u0001\u0010\u00ad\u0001R\u0016\u0010=\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001R\u0018\u0010\u00b3\u0001\u001a\u00030\u00b0\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00b2\u0001R\u0018\u0010\u00b7\u0001\u001a\u00030\u00b4\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b5\u0001\u0010\u00b6\u0001R\u001e\u0010\u00bb\u0001\u001a\t\u0012\u0004\u0012\u00020p0\u00b8\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001R\u001e\u0010\u00bf\u0001\u001a\t\u0012\u0004\u0012\u00020N0\u00bc\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bd\u0001\u0010\u00be\u0001R\u001e\u0010\u00c1\u0001\u001a\t\u0012\u0004\u0012\u00020n0\u00bc\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00be\u0001R\u0019\u0010\u00c4\u0001\u001a\u00020G8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0001\u0010\u00c3\u0001R\u001c\u0010\u00c8\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0001\u0010\u00c7\u0001R\u001c\u0010\u00ca\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00c9\u0001\u0010\u00c7\u0001R\u001c\u0010\u00cc\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cb\u0001\u0010\u00c7\u0001R\u001c\u0010\u00ce\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cd\u0001\u0010\u00c7\u0001R\u001c\u0010\u00d0\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00cf\u0001\u0010\u00c7\u0001R\u001c\u0010\u00d2\u0001\u001a\u0005\u0018\u00010\u00c5\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d1\u0001\u0010\u00c7\u0001R\u001a\u0010\u00d6\u0001\u001a\u00030\u00d3\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00d4\u0001\u0010\u00d5\u0001\u00a8\u0006\u00d9\u0001"
    }
    d2 = {
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Landroidx/lifecycle/Q;",
        "savedStateHandle",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lorg/xbet/main_menu/impl/domain/usecases/a;",
        "checkMessagesSupportedUseCase",
        "LwX0/c;",
        "router",
        "LGa/a;",
        "settingsScreenFactory",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lfk/b;",
        "addScreenBalanceUseCase",
        "Lfk/u;",
        "saveLastBalanceIdUseCase",
        "LZa0/a;",
        "messagesScreenFactory",
        "LZc0/b;",
        "personalScreenFactory",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Loi/a;",
        "authScreenFactory",
        "Lej0/d;",
        "getRegistrationTypesUseCase",
        "LIn0/c;",
        "getSessionTimeStreamUseCase",
        "LYU/a;",
        "calendarEventFeature",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "isBettingDisabledUseCase",
        "Lp9/e;",
        "getUserIdUseCase",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "getProfileUseCase",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lq80/a;",
        "changeActiveBalanceScenario",
        "Lh9/a;",
        "userSettingsInteractor",
        "Lp9/g;",
        "observeLoginStateUseCase",
        "LXa0/f;",
        "getUnreadMessagesCountUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lgk0/a;",
        "getAccountSelectionStyleConfigTypeScenario",
        "LU80/a;",
        "mainMenuContainerAnalyticsDelegate",
        "Lmn0/i;",
        "getSecurityLevelProtectionStageUseCase",
        "LfX/b;",
        "testRepository",
        "LA7/a;",
        "getCommonConfigUseCase",
        "<init>",
        "(Landroidx/lifecycle/Q;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/a;LwX0/c;LGa/a;Lm8/a;Lfk/b;Lfk/u;LZa0/a;LZc0/b;LxX0/a;Loi/a;Lej0/d;LIn0/c;LYU/a;Lorg/xbet/remoteconfig/domain/usecases/k;Lp9/e;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lfk/l;Lq80/a;Lh9/a;Lp9/g;LXa0/f;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;Lgk0/a;LU80/a;Lmn0/i;LfX/b;LA7/a;)V",
        "",
        "u4",
        "()V",
        "O4",
        "N4",
        "",
        "i4",
        "()Z",
        "r4",
        "x4",
        "w4",
        "t4",
        "",
        "o4",
        "(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "n4",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "j4",
        "(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;",
        "",
        "throwable",
        "p4",
        "(Ljava/lang/Throwable;)V",
        "L4",
        "M4",
        "C4",
        "H4",
        "D4",
        "K4",
        "B4",
        "z4",
        "(Lorg/xbet/balance/model/BalanceModel;)V",
        "h4",
        "balanceHasChanged",
        "A4",
        "(Z)V",
        "F4",
        "G4",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "g4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
        "E0",
        "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;",
        "m4",
        "l4",
        "Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;",
        "k4",
        "E4",
        "J4",
        "v1",
        "Landroidx/lifecycle/Q;",
        "x1",
        "Lorg/xbet/main_menu/impl/domain/usecases/a;",
        "y1",
        "LwX0/c;",
        "F1",
        "LGa/a;",
        "H1",
        "Lm8/a;",
        "I1",
        "Lfk/b;",
        "P1",
        "Lfk/u;",
        "S1",
        "LZa0/a;",
        "V1",
        "LZc0/b;",
        "b2",
        "LxX0/a;",
        "v2",
        "Loi/a;",
        "x2",
        "Lej0/d;",
        "y2",
        "LIn0/c;",
        "F2",
        "LYU/a;",
        "H2",
        "Lorg/xbet/remoteconfig/domain/usecases/k;",
        "I2",
        "Lp9/e;",
        "P2",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "S2",
        "Lfk/l;",
        "V2",
        "Lq80/a;",
        "X2",
        "Lh9/a;",
        "F3",
        "Lp9/g;",
        "H3",
        "LXa0/f;",
        "I3",
        "Lorg/xbet/ui_common/utils/M;",
        "S3",
        "LHX0/e;",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "X4",
        "Lgk0/a;",
        "v5",
        "LU80/a;",
        "w5",
        "Lmn0/i;",
        "x5",
        "LfX/b;",
        "Lv7/b;",
        "y5",
        "Lv7/b;",
        "commonConfig",
        "Lek0/o;",
        "z5",
        "Lek0/o;",
        "remoteConfigModel",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "A5",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "uiAction",
        "Lkotlinx/coroutines/flow/V;",
        "B5",
        "Lkotlinx/coroutines/flow/V;",
        "sessionTimerState",
        "C5",
        "uiState",
        "D5",
        "Z",
        "authorized",
        "Lkotlinx/coroutines/x0;",
        "E5",
        "Lkotlinx/coroutines/x0;",
        "observeLoginStateJob",
        "F5",
        "loadBalanceAndUserJob",
        "G5",
        "connectionJob",
        "H5",
        "unreadMessagesJob",
        "I5",
        "sessionTimerJob",
        "J5",
        "getProfileJob",
        "Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;",
        "K5",
        "Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;",
        "lastSelectedTab",
        "b",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public D5:Z

.field public E5:Lkotlinx/coroutines/x0;

.field public final F1:LGa/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LYU/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lp9/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F5:Lkotlinx/coroutines/x0;

.field public G5:Lkotlinx/coroutines/x0;

.field public final H1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/remoteconfig/domain/usecases/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:LXa0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H5:Lkotlinx/coroutines/x0;

.field public final I1:Lfk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lp9/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I5:Lkotlinx/coroutines/x0;

.field public J5:Lkotlinx/coroutines/x0;

.field public K5:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lfk/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LZa0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LZc0/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lq80/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lh9/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lgk0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Landroidx/lifecycle/Q;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Loi/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:LU80/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lmn0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/main_menu/impl/domain/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lej0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LIn0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lv7/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z5:Lek0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/lifecycle/Q;Lorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/main_menu/impl/domain/usecases/a;LwX0/c;LGa/a;Lm8/a;Lfk/b;Lfk/u;LZa0/a;LZc0/b;LxX0/a;Loi/a;Lej0/d;LIn0/c;LYU/a;Lorg/xbet/remoteconfig/domain/usecases/k;Lp9/e;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lfk/l;Lq80/a;Lh9/a;Lp9/g;LXa0/f;Lorg/xbet/ui_common/utils/M;LHX0/e;Lorg/xbet/ui_common/utils/internet/a;Lgk0/a;LU80/a;Lmn0/i;LfX/b;LA7/a;)V
    .locals 13
    .param p1    # Landroidx/lifecycle/Q;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/main_menu/impl/domain/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LGa/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lfk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lfk/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LZa0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LZc0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Loi/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lej0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LIn0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # LYU/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/remoteconfig/domain/usecases/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lp9/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lq80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lh9/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lp9/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # LXa0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Lgk0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # LU80/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # Lmn0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p31    # LA7/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v1:Landroidx/lifecycle/Q;

    .line 5
    .line 6
    move-object/from16 p1, p3

    .line 7
    .line 8
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x1:Lorg/xbet/main_menu/impl/domain/usecases/a;

    .line 9
    .line 10
    move-object/from16 p1, p4

    .line 11
    .line 12
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 13
    .line 14
    move-object/from16 p1, p5

    .line 15
    .line 16
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F1:LGa/a;

    .line 17
    .line 18
    move-object/from16 p1, p6

    .line 19
    .line 20
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 21
    .line 22
    move-object/from16 p1, p7

    .line 23
    .line 24
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I1:Lfk/b;

    .line 25
    .line 26
    move-object/from16 p1, p8

    .line 27
    .line 28
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P1:Lfk/u;

    .line 29
    .line 30
    move-object/from16 p1, p9

    .line 31
    .line 32
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S1:LZa0/a;

    .line 33
    .line 34
    move-object/from16 p1, p10

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->V1:LZc0/b;

    .line 37
    .line 38
    move-object/from16 p1, p11

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->b2:LxX0/a;

    .line 41
    .line 42
    move-object/from16 p1, p12

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v2:Loi/a;

    .line 45
    .line 46
    move-object/from16 p1, p13

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x2:Lej0/d;

    .line 49
    .line 50
    move-object/from16 p1, p14

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y2:LIn0/c;

    .line 53
    .line 54
    move-object/from16 p1, p15

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F2:LYU/a;

    .line 57
    .line 58
    move-object/from16 p1, p16

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H2:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 61
    .line 62
    move-object/from16 p1, p17

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I2:Lp9/e;

    .line 65
    .line 66
    move-object/from16 p1, p18

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P2:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 69
    .line 70
    move-object/from16 p1, p19

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S2:Lfk/l;

    .line 73
    .line 74
    move-object/from16 p1, p20

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->V2:Lq80/a;

    .line 77
    .line 78
    move-object/from16 p1, p21

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X2:Lh9/a;

    .line 81
    .line 82
    move-object/from16 p1, p22

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F3:Lp9/g;

    .line 85
    .line 86
    move-object/from16 p1, p23

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H3:LXa0/f;

    .line 89
    .line 90
    move-object/from16 p1, p24

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 93
    .line 94
    move-object/from16 p1, p25

    .line 95
    .line 96
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 97
    .line 98
    move-object/from16 p1, p26

    .line 99
    .line 100
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H4:Lorg/xbet/ui_common/utils/internet/a;

    .line 101
    .line 102
    move-object/from16 p1, p27

    .line 103
    .line 104
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X4:Lgk0/a;

    .line 105
    .line 106
    move-object/from16 p1, p28

    .line 107
    .line 108
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 109
    .line 110
    move-object/from16 p1, p29

    .line 111
    .line 112
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->w5:Lmn0/i;

    .line 113
    .line 114
    move-object/from16 p1, p30

    .line 115
    .line 116
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x5:LfX/b;

    .line 117
    .line 118
    invoke-virtual/range {p31 .. p31}, LA7/a;->a()Lv7/b;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y5:Lv7/b;

    .line 123
    .line 124
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z5:Lek0/o;

    .line 129
    .line 130
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 131
    .line 132
    const/4 v0, 0x0

    .line 133
    const/4 v1, 0x3

    .line 134
    const/4 v2, 0x0

    .line 135
    invoke-direct {p1, v2, v0, v1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 136
    .line 137
    .line 138
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 139
    .line 140
    const-string p1, ""

    .line 141
    .line 142
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 147
    .line 148
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 149
    .line 150
    const-string v0, ""

    .line 151
    .line 152
    const-string v1, ""

    .line 153
    .line 154
    const-string v2, ""

    .line 155
    .line 156
    const-string v3, ""

    .line 157
    .line 158
    const/4 v4, 0x0

    .line 159
    const/4 v5, 0x0

    .line 160
    const/4 v6, 0x0

    .line 161
    const/4 v7, 0x0

    .line 162
    const/4 v8, 0x0

    .line 163
    const/4 v9, 0x0

    .line 164
    const/4 v10, 0x0

    .line 165
    const/4 v11, 0x0

    .line 166
    const-string v12, ""

    .line 167
    .line 168
    move-object p2, p1

    .line 169
    move-object/from16 p14, v0

    .line 170
    .line 171
    move-object/from16 p15, v1

    .line 172
    .line 173
    move-object/from16 p3, v2

    .line 174
    .line 175
    move-object/from16 p4, v3

    .line 176
    .line 177
    move-object/from16 p10, v9

    .line 178
    .line 179
    move-object/from16 p11, v10

    .line 180
    .line 181
    move-object/from16 p13, v12

    .line 182
    .line 183
    const/16 p5, 0x0

    .line 184
    .line 185
    const/16 p6, 0x0

    .line 186
    .line 187
    const/16 p7, 0x0

    .line 188
    .line 189
    const/16 p8, 0x0

    .line 190
    .line 191
    const/16 p9, 0x0

    .line 192
    .line 193
    const/16 p12, 0x0

    .line 194
    .line 195
    invoke-direct/range {p2 .. p15}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;-><init>(Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 196
    .line 197
    .line 198
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 203
    .line 204
    sget-object p1, Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;->TOP:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 205
    .line 206
    iput-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K5:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 207
    .line 208
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lfk/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S2:Lfk/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P2:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lej0/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x2:Lej0/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lmn0/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->w5:Lmn0/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K5:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LU80/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->E5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lek0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z5:Lek0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final I4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-direct {v0, v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$b;-><init>(Z)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lfk/u;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P1:Lfk/u;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LfX/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x5:LfX/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final P4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/z;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/main_menu/impl/presentation/container/z;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final Q4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic S3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->n4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic T3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->o4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic U3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lh9/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X2:Lh9/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic V3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->p4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic W3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lorg/xbet/remoteconfig/domain/usecases/k;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H2:Lorg/xbet/remoteconfig/domain/usecases/k;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic X3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->r4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Y3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->s4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Z3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->t4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic a4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->u4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic b4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic c4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->w4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic d4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic e4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic f4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->D5:Z

    .line 2
    .line 3
    return-void
.end method

.method private final j4(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;
    .locals 5

    .line 1
    const/4 v0, 0x2

    .line 2
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 3
    .line 4
    sget v2, Lpb/k;->account_change_warning:I

    .line 5
    .line 6
    const/4 v3, 0x0

    .line 7
    new-array v4, v3, [Ljava/lang/Object;

    .line 8
    .line 9
    invoke-interface {v1, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getTypeAccount()Lcom/xbet/onexcore/data/configs/TypeAccount;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/configs/TypeAccount;->isPrimaryOrMulti()Z

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    if-eqz p1, :cond_0

    .line 22
    .line 23
    return-object v1

    .line 24
    :cond_0
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 25
    .line 26
    sget v2, Lpb/k;->account_change_warning2:I

    .line 27
    .line 28
    new-array v4, v3, [Ljava/lang/Object;

    .line 29
    .line 30
    invoke-interface {p1, v2, v4}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    new-array v2, v0, [Ljava/lang/Object;

    .line 35
    .line 36
    aput-object v1, v2, v3

    .line 37
    .line 38
    const/4 v1, 0x1

    .line 39
    aput-object p1, v2, v1

    .line 40
    .line 41
    invoke-static {v2, v0}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    const-string v0, "%s\n\n%s"

    .line 46
    .line 47
    invoke-static {v0, p1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    return-object p1
.end method

.method public static synthetic p3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->q4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I3:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/x;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/x;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic q3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->Q4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final q4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$e;

    .line 4
    .line 5
    invoke-direct {p1, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$e;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic s4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->p4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->i4()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic u3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lfk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I1:Lfk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method private final u4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F3:Lp9/g;

    .line 2
    .line 3
    invoke-virtual {v0}, Lp9/g;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$2;

    .line 32
    .line 33
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadData$2;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->E5:Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    return-void
.end method

.method public static final synthetic v3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Loi/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v2:Loi/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->p4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)LYU/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F2:LYU/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->j4(Lorg/xbet/balance/model/BalanceModel;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lkotlinx/coroutines/x0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->G5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->p4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)Lgk0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X4:Lgk0/a;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final A4(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X2:Lh9/a;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lh9/a;->g(Z)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final B4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onCancelSelectedBalanceResult$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onCancelSelectedBalanceResult$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onCancelSelectedBalanceResult$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onCancelSelectedBalanceResult$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final C4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K5:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, LU80/a;->d(Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v2:Loi/a;

    .line 11
    .line 12
    new-instance v2, Lorg/xbet/auth/api/presentation/a;

    .line 13
    .line 14
    invoke-direct {v2}, Lorg/xbet/auth/api/presentation/a;-><init>()V

    .line 15
    .line 16
    .line 17
    sget-object v3, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    invoke-virtual {v2}, Lorg/xbet/auth/api/presentation/a;->a()Lorg/xbet/auth/api/presentation/AuthScreenParams$Login;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-interface {v1, v2}, Loi/a;->a(Lorg/xbet/auth/api/presentation/AuthScreenParams;)Lq4/q;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public final D4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LU80/a;->f()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 7
    .line 8
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S1:LZa0/a;

    .line 9
    .line 10
    invoke-interface {v1}, LZa0/a;->a()LwX0/B;

    .line 11
    .line 12
    .line 13
    move-result-object v1

    .line 14
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final E0()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->k0(Lkotlinx/coroutines/flow/Z;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/Z;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;

    .line 14
    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUiState$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->h0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    return-object v0
.end method

.method public final E4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->V1:LZc0/b;

    .line 4
    .line 5
    invoke-interface {v1}, LZc0/b;->b()Lq4/q;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final F4()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->K5:Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, LU80/a;->g(Lorg/xbet/main_menu/impl/domain/models/MainMenuCategoryModel;)V

    .line 6
    .line 7
    .line 8
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->b2:LxX0/a;

    .line 9
    .line 10
    iget-object v3, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 11
    .line 12
    const/4 v7, 0x4

    .line 13
    const/4 v8, 0x0

    .line 14
    const/4 v4, 0x1

    .line 15
    const-wide/16 v5, 0x0

    .line 16
    .line 17
    invoke-static/range {v2 .. v8}, LxX0/a$a;->a(LxX0/a;LwX0/c;ZJILjava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final G4()V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->D5:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 7
    .line 8
    invoke-virtual {v0}, LU80/a;->h()V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 12
    .line 13
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->V1:LZc0/b;

    .line 14
    .line 15
    const/4 v2, 0x0

    .line 16
    invoke-interface {v1, v2}, LZc0/b;->c(Z)Lq4/q;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final H4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->a()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/y;

    .line 17
    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/y;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 19
    .line 20
    .line 21
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    invoke-direct {v5, p0, v4}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onRegistrationClicked$3;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/16 v6, 0x8

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final J4()V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->N4()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->O4()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final K4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LU80/a;->k()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F1:LGa/a;

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    invoke-interface {v0, v1}, LGa/a;->a(Z)Lq4/q;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y1:LwX0/c;

    .line 14
    .line 15
    invoke-virtual {v1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final L4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onShowBalancesClicked$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onShowBalancesClicked$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onShowBalancesClicked$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onShowBalancesClicked$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final M4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onUpdateBalanceClicked$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onUpdateBalanceClicked$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onUpdateBalanceClicked$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onUpdateBalanceClicked$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final N4()V
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J5:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    invoke-interface {v1}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    const/4 v2, 0x1

    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    iget-boolean v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->D5:Z

    .line 16
    .line 17
    if-nez v1, :cond_2

    .line 18
    .line 19
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->C5:Lkotlinx/coroutines/flow/V;

    .line 20
    .line 21
    :cond_1
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    move-object v3, v2

    .line 26
    check-cast v3, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 27
    .line 28
    const/16 v17, 0x1eff

    .line 29
    .line 30
    const/16 v18, 0x0

    .line 31
    .line 32
    const/4 v4, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    const/4 v6, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    const/4 v10, 0x0

    .line 39
    const/4 v11, 0x0

    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    const/4 v14, 0x0

    .line 43
    const/4 v15, 0x0

    .line 44
    const/16 v16, 0x0

    .line 45
    .line 46
    invoke-static/range {v3 .. v18}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;->b(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/Integer;Lorg/xbet/uikit/models/StateStatus;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$b;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    if-eqz v2, :cond_1

    .line 55
    .line 56
    return-void

    .line 57
    :cond_2
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    iget-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 62
    .line 63
    invoke-interface {v1}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 64
    .line 65
    .line 66
    move-result-object v6

    .line 67
    new-instance v4, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$2;

    .line 68
    .line 69
    invoke-direct {v4, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$2;-><init>(Ljava/lang/Object;)V

    .line 70
    .line 71
    .line 72
    new-instance v8, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-direct {v8, v0, v1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$setupSettingsIconStatus$3;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 76
    .line 77
    .line 78
    const/16 v9, 0xa

    .line 79
    .line 80
    const/4 v10, 0x0

    .line 81
    const/4 v5, 0x0

    .line 82
    const/4 v7, 0x0

    .line 83
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    iput-object v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->J5:Lkotlinx/coroutines/x0;

    .line 88
    .line 89
    return-void
.end method

.method public final O4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y5:Lv7/b;

    .line 2
    .line 3
    invoke-virtual {v0}, Lv7/b;->x()Lcom/xbet/config/domain/model/common/IdentificationFlowEnum;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lcom/xbet/config/domain/model/common/IdentificationFlowEnum;->TJ:Lcom/xbet/config/domain/model/common/IdentificationFlowEnum;

    .line 8
    .line 9
    if-ne v0, v1, :cond_0

    .line 10
    .line 11
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 16
    .line 17
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/container/w;

    .line 22
    .line 23
    invoke-direct {v3, p0}, Lorg/xbet/main_menu/impl/presentation/container/w;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;)V

    .line 24
    .line 25
    .line 26
    new-instance v7, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$showIdentificationAlertIfNeed$2;

    .line 27
    .line 28
    const/4 v0, 0x0

    .line 29
    invoke-direct {v7, p0, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$showIdentificationAlertIfNeed$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 30
    .line 31
    .line 32
    const/16 v8, 0xa

    .line 33
    .line 34
    const/4 v9, 0x0

    .line 35
    const/4 v4, 0x0

    .line 36
    const/4 v6, 0x0

    .line 37
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    :cond_0
    return-void
.end method

.method public final g4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->X4:Lgk0/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lgk0/a;->invoke()Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, LV80/a;->a(Lorg/xbet/remoteconfig/domain/models/AccountSelectionStyleConfigType;)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0
.end method

.method public final h4(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    return-void

    .line 4
    :cond_0
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->V2:Lq80/a;

    .line 5
    .line 6
    invoke-interface {v0, p1}, Lq80/a;->a(Lorg/xbet/balance/model/BalanceModel;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->t4()V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->v5:LU80/a;

    .line 13
    .line 14
    invoke-virtual {p1}, LU80/a;->b()V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 18
    .line 19
    sget-object v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$a;->a:Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a$a;

    .line 20
    .line 21
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final i4()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->x1:Lorg/xbet/main_menu/impl/domain/usecases/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/main_menu/impl/domain/usecases/a;->a()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z5:Lek0/o;

    .line 10
    .line 11
    invoke-virtual {v0}, Lek0/o;->G0()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    const/4 v0, 0x1

    .line 18
    return v0

    .line 19
    :cond_0
    const/4 v0, 0x0

    .line 20
    return v0
.end method

.method public final k4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->z5:Lek0/o;

    .line 2
    .line 3
    invoke-virtual {v0}, Lek0/o;->Q1()Lorg/xbet/remoteconfig/domain/models/MainMenuStyleConfigType;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->X(Ljava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final l4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->B5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->A5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n4(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :catchall_0
    move-exception p1

    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    :try_start_1
    sget-object p1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 56
    .line 57
    iget-object p1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->P2:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 58
    .line 59
    iput v3, v0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserName$1;->label:I

    .line 60
    .line 61
    const/4 v2, 0x0

    .line 62
    invoke-virtual {p1, v2, v0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    if-ne p1, v1, :cond_3

    .line 67
    .line 68
    return-object v1

    .line 69
    :cond_3
    :goto_1
    check-cast p1, Le9/a;

    .line 70
    .line 71
    invoke-virtual {p1}, Le9/a;->A()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    invoke-virtual {p1}, Le9/a;->W()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    new-instance v1, Ljava/lang/StringBuilder;

    .line 80
    .line 81
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 82
    .line 83
    .line 84
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 85
    .line 86
    .line 87
    const-string v0, " "

    .line 88
    .line 89
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 90
    .line 91
    .line 92
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 93
    .line 94
    .line 95
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    invoke-static {p1}, Lkotlin/text/StringsKt;->H1(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 111
    goto :goto_3

    .line 112
    :goto_2
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 113
    .line 114
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object p1

    .line 118
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    :goto_3
    invoke-static {p1}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result v0

    .line 126
    if-eqz v0, :cond_4

    .line 127
    .line 128
    const-string p1, ""

    .line 129
    .line 130
    :cond_4
    return-object p1
.end method

.method public final o4(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    instance-of v2, p2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;

    .line 4
    .line 5
    if-eqz v2, :cond_0

    .line 6
    .line 7
    move-object v2, p2

    .line 8
    check-cast v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;

    .line 9
    .line 10
    iget v3, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->label:I

    .line 11
    .line 12
    const/high16 v4, -0x80000000

    .line 13
    .line 14
    and-int v5, v3, v4

    .line 15
    .line 16
    if-eqz v5, :cond_0

    .line 17
    .line 18
    sub-int/2addr v3, v4

    .line 19
    iput v3, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->label:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;

    .line 23
    .line 24
    invoke-direct {v2, p0, p2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    :goto_0
    iget-object p2, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    iget v4, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->label:I

    .line 34
    .line 35
    if-eqz v4, :cond_2

    .line 36
    .line 37
    if-ne v4, v1, :cond_1

    .line 38
    .line 39
    iget-wide v3, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->J$0:J

    .line 40
    .line 41
    iget-object p1, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->L$0:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;

    .line 44
    .line 45
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto :goto_1

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    iget-object p2, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I2:Lp9/e;

    .line 61
    .line 62
    invoke-virtual {p2}, Lp9/e;->a()J

    .line 63
    .line 64
    .line 65
    move-result-wide v4

    .line 66
    iput-object p1, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->L$0:Ljava/lang/Object;

    .line 67
    .line 68
    iput-wide v4, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->J$0:J

    .line 69
    .line 70
    iput v1, v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$getUserNameOrId$1;->label:I

    .line 71
    .line 72
    invoke-virtual {p1, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->n4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object p2

    .line 76
    if-ne p2, v3, :cond_3

    .line 77
    .line 78
    return-object v3

    .line 79
    :cond_3
    move-wide v3, v4

    .line 80
    :goto_1
    check-cast p2, Ljava/lang/String;

    .line 81
    .line 82
    invoke-static {p2}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 83
    .line 84
    .line 85
    move-result v2

    .line 86
    if-nez v2, :cond_4

    .line 87
    .line 88
    return-object p2

    .line 89
    :cond_4
    const-wide/16 v5, 0x0

    .line 90
    .line 91
    cmp-long p2, v3, v5

    .line 92
    .line 93
    if-lez p2, :cond_5

    .line 94
    .line 95
    iget-object p1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 96
    .line 97
    sget p2, Lpb/k;->menu_account_id:I

    .line 98
    .line 99
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 100
    .line 101
    .line 102
    move-result-object v2

    .line 103
    new-array v1, v1, [Ljava/lang/Object;

    .line 104
    .line 105
    aput-object v2, v1, v0

    .line 106
    .line 107
    invoke-interface {p1, p2, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    return-object p1

    .line 112
    :cond_5
    iget-object p1, p1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->S3:LHX0/e;

    .line 113
    .line 114
    sget p2, Lpb/k;->user:I

    .line 115
    .line 116
    new-array v0, v0, [Ljava/lang/Object;

    .line 117
    .line 118
    invoke-interface {p1, p2, v0}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    return-object p1
.end method

.method public final r4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->G5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H4:Lorg/xbet/ui_common/utils/internet/a;

    .line 7
    .line 8
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalanceUserDataWhenConnected$1;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalanceUserDataWhenConnected$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 27
    .line 28
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalanceUserDataWhenConnected$2;

    .line 37
    .line 38
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalanceUserDataWhenConnected$2;-><init>(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->G5:Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    return-void
.end method

.method public final t4()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v4

    .line 16
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$1;

    .line 17
    .line 18
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$1;-><init>(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    new-instance v6, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;

    .line 22
    .line 23
    const/4 v0, 0x0

    .line 24
    invoke-direct {v6, p0, v0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$loadBalancesWithUserData$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    const/16 v7, 0xa

    .line 28
    .line 29
    const/4 v8, 0x0

    .line 30
    const/4 v3, 0x0

    .line 31
    const/4 v5, 0x0

    .line 32
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->F5:Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    return-void
.end method

.method public final w4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->y2:LIn0/c;

    .line 7
    .line 8
    invoke-virtual {v0}, LIn0/c;->a()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeSessionTimer$1;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeSessionTimer$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v3, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 27
    .line 28
    invoke-interface {v3}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    invoke-static {v1, v3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    new-instance v3, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeSessionTimer$2;

    .line 37
    .line 38
    invoke-direct {v3, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeSessionTimer$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 39
    .line 40
    .line 41
    invoke-static {v0, v1, v3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->I5:Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    return-void
.end method

.method public final x4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H3:LXa0/f;

    .line 7
    .line 8
    invoke-interface {v0}, LXa0/f;->invoke()Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$1;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    iget-object v2, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 27
    .line 28
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    new-instance v2, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$2;

    .line 37
    .line 38
    invoke-direct {v2, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$observeUnreadMessages$2;-><init>(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H5:Lkotlinx/coroutines/x0;

    .line 46
    .line 47
    return-void
.end method

.method public final z4(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 8

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    return-void

    .line 4
    :cond_0
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    iget-object v1, p0, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;->H1:Lm8/a;

    .line 9
    .line 10
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v1, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onBalanceSelectedResult$1;

    .line 15
    .line 16
    invoke-direct {v1, p0}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onBalanceSelectedResult$1;-><init>(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    new-instance v5, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onBalanceSelectedResult$2;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel$onBalanceSelectedResult$2;-><init>(Lorg/xbet/main_menu/impl/presentation/container/MainMenuContainerViewModel;Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    const/16 v6, 0xa

    .line 26
    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v4, 0x0

    .line 29
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 30
    .line 31
    .line 32
    return-void
.end method
