.class public Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Serializable;


# instance fields
.field private final bdsState:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Lorg/spongycastle/pqc/crypto/xmss/BDS;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/TreeMap;

    invoke-direct {v0}, Ljava/util/TreeMap;-><init>()V

    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;Lorg/spongycastle/pqc/crypto/xmss/l;J[B[B)V
    .locals 7

    .line 6
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 7
    new-instance v1, Ljava/util/TreeMap;

    invoke-direct {v1}, Ljava/util/TreeMap;-><init>()V

    iput-object v1, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 8
    iget-object v1, p1, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    .line 9
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Integer;

    .line 10
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    iget-object v3, p1, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    invoke-interface {v3, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v2, v1, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    move-object v0, p0

    move-object v1, p2

    move-wide v2, p3

    move-object v4, p5

    move-object v5, p6

    .line 11
    invoke-virtual/range {v0 .. v5}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->a(Lorg/spongycastle/pqc/crypto/xmss/l;J[B[B)V

    return-void
.end method

.method public constructor <init>(Lorg/spongycastle/pqc/crypto/xmss/l;J[B[B)V
    .locals 8

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    new-instance v0, Ljava/util/TreeMap;

    invoke-direct {v0}, Ljava/util/TreeMap;-><init>()V

    iput-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    const-wide/16 v0, 0x0

    move-wide v4, v0

    :goto_0
    cmp-long v0, v4, p2

    if-gez v0, :cond_0

    move-object v2, p0

    move-object v3, p1

    move-object v6, p4

    move-object v7, p5

    .line 5
    invoke-virtual/range {v2 .. v7}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->a(Lorg/spongycastle/pqc/crypto/xmss/l;J[B[B)V

    const-wide/16 p4, 0x1

    add-long/2addr v4, p4

    move-object p4, v6

    move-object p5, v7

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public final a(Lorg/spongycastle/pqc/crypto/xmss/l;J[B[B)V
    .locals 10

    .line 1
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/l;->g()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/spongycastle/pqc/crypto/xmss/q;->d()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-static {p2, p3, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->j(JI)J

    .line 10
    .line 11
    .line 12
    move-result-wide v2

    .line 13
    invoke-static {p2, p3, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->i(JI)I

    .line 14
    .line 15
    .line 16
    move-result v4

    .line 17
    new-instance v5, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 18
    .line 19
    invoke-direct {v5}, Lorg/spongycastle/pqc/crypto/xmss/f$b;-><init>()V

    .line 20
    .line 21
    .line 22
    invoke-virtual {v5, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    .line 23
    .line 24
    .line 25
    move-result-object v5

    .line 26
    check-cast v5, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 27
    .line 28
    invoke-virtual {v5, v4}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->p(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 29
    .line 30
    .line 31
    move-result-object v5

    .line 32
    invoke-virtual {v5}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->l()Lorg/spongycastle/pqc/crypto/xmss/k;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    check-cast v5, Lorg/spongycastle/pqc/crypto/xmss/f;

    .line 37
    .line 38
    const/4 v6, 0x1

    .line 39
    shl-int v7, v6, v1

    .line 40
    .line 41
    sub-int/2addr v7, v6

    .line 42
    if-ge v4, v7, :cond_2

    .line 43
    .line 44
    const/4 v8, 0x0

    .line 45
    invoke-virtual {p0, v8}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->get(I)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 46
    .line 47
    .line 48
    move-result-object v9

    .line 49
    if-eqz v9, :cond_0

    .line 50
    .line 51
    if-nez v4, :cond_1

    .line 52
    .line 53
    :cond_0
    new-instance v4, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 54
    .line 55
    invoke-direct {v4, v0, p4, p5, v5}, Lorg/spongycastle/pqc/crypto/xmss/BDS;-><init>(Lorg/spongycastle/pqc/crypto/xmss/q;[B[BLorg/spongycastle/pqc/crypto/xmss/f;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p0, v8, v4}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->put(ILorg/spongycastle/pqc/crypto/xmss/BDS;)V

    .line 59
    .line 60
    .line 61
    :cond_1
    invoke-virtual {p0, v8, p4, p5, v5}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->update(I[B[BLorg/spongycastle/pqc/crypto/xmss/f;)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 62
    .line 63
    .line 64
    :cond_2
    :goto_0
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/l;->d()I

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-ge v6, v0, :cond_5

    .line 69
    .line 70
    invoke-static {v2, v3, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->i(JI)I

    .line 71
    .line 72
    .line 73
    move-result v0

    .line 74
    invoke-static {v2, v3, v1}, Lorg/spongycastle/pqc/crypto/xmss/t;->j(JI)J

    .line 75
    .line 76
    .line 77
    move-result-wide v2

    .line 78
    new-instance v4, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 79
    .line 80
    invoke-direct {v4}, Lorg/spongycastle/pqc/crypto/xmss/f$b;-><init>()V

    .line 81
    .line 82
    .line 83
    invoke-virtual {v4, v6}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->g(I)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    .line 84
    .line 85
    .line 86
    move-result-object v4

    .line 87
    check-cast v4, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 88
    .line 89
    invoke-virtual {v4, v2, v3}, Lorg/spongycastle/pqc/crypto/xmss/k$a;->h(J)Lorg/spongycastle/pqc/crypto/xmss/k$a;

    .line 90
    .line 91
    .line 92
    move-result-object v4

    .line 93
    check-cast v4, Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 94
    .line 95
    invoke-virtual {v4, v0}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->p(I)Lorg/spongycastle/pqc/crypto/xmss/f$b;

    .line 96
    .line 97
    .line 98
    move-result-object v4

    .line 99
    invoke-virtual {v4}, Lorg/spongycastle/pqc/crypto/xmss/f$b;->l()Lorg/spongycastle/pqc/crypto/xmss/k;

    .line 100
    .line 101
    .line 102
    move-result-object v4

    .line 103
    check-cast v4, Lorg/spongycastle/pqc/crypto/xmss/f;

    .line 104
    .line 105
    if-ge v0, v7, :cond_4

    .line 106
    .line 107
    invoke-static {p2, p3, v1, v6}, Lorg/spongycastle/pqc/crypto/xmss/t;->m(JII)Z

    .line 108
    .line 109
    .line 110
    move-result v0

    .line 111
    if-eqz v0, :cond_4

    .line 112
    .line 113
    invoke-virtual {p0, v6}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->get(I)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 114
    .line 115
    .line 116
    move-result-object v0

    .line 117
    if-nez v0, :cond_3

    .line 118
    .line 119
    new-instance v0, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 120
    .line 121
    invoke-virtual {p1}, Lorg/spongycastle/pqc/crypto/xmss/l;->g()Lorg/spongycastle/pqc/crypto/xmss/q;

    .line 122
    .line 123
    .line 124
    move-result-object v5

    .line 125
    invoke-direct {v0, v5, p4, p5, v4}, Lorg/spongycastle/pqc/crypto/xmss/BDS;-><init>(Lorg/spongycastle/pqc/crypto/xmss/q;[B[BLorg/spongycastle/pqc/crypto/xmss/f;)V

    .line 126
    .line 127
    .line 128
    invoke-virtual {p0, v6, v0}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->put(ILorg/spongycastle/pqc/crypto/xmss/BDS;)V

    .line 129
    .line 130
    .line 131
    :cond_3
    invoke-virtual {p0, v6, p4, p5, v4}, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->update(I[B[BLorg/spongycastle/pqc/crypto/xmss/f;)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 132
    .line 133
    .line 134
    :cond_4
    add-int/lit8 v6, v6, 0x1

    .line 135
    .line 136
    goto :goto_0

    .line 137
    :cond_5
    return-void
.end method

.method public get(I)Lorg/spongycastle/pqc/crypto/xmss/BDS;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/spongycastle/util/d;->b(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 12
    .line 13
    return-object p1
.end method

.method public isEmpty()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Map;->isEmpty()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public put(ILorg/spongycastle/pqc/crypto/xmss/BDS;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/spongycastle/util/d;->b(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public setXMSS(Lorg/spongycastle/pqc/crypto/xmss/q;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Map;->keySet()Ljava/util/Set;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    check-cast v1, Ljava/lang/Integer;

    .line 22
    .line 23
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 24
    .line 25
    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    check-cast v1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 30
    .line 31
    invoke-virtual {v1, p1}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->setXMSS(Lorg/spongycastle/pqc/crypto/xmss/q;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {v1}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->validate()V

    .line 35
    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    return-void
.end method

.method public update(I[B[BLorg/spongycastle/pqc/crypto/xmss/f;)Lorg/spongycastle/pqc/crypto/xmss/BDS;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/spongycastle/util/d;->b(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, Lorg/spongycastle/pqc/crypto/xmss/BDSStateMap;->bdsState:Ljava/util/Map;

    .line 8
    .line 9
    invoke-static {p1}, Lorg/spongycastle/util/d;->b(I)Ljava/lang/Integer;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-interface {v2, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 18
    .line 19
    invoke-virtual {p1, p2, p3, p4}, Lorg/spongycastle/pqc/crypto/xmss/BDS;->getNextState([B[BLorg/spongycastle/pqc/crypto/xmss/f;)Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    check-cast p1, Lorg/spongycastle/pqc/crypto/xmss/BDS;

    .line 28
    .line 29
    return-object p1
.end method
