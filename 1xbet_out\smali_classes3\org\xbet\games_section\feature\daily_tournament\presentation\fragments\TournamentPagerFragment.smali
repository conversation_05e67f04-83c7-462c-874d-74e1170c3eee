.class public final Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;
.super LXW0/a;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\u000b\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u0000 S2\u00020\u00012\u00020\u0002:\u0001TB\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0003\u00a2\u0006\u0004\u0008\u0006\u0010\u0004J\u0017\u0010\t\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0004J\u000f\u0010\u000c\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\u0004J\u000f\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0010\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0004J\u0019\u0010\u0013\u001a\u00020\u00052\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u0014\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0015\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0004J\u000f\u0010\u0016\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0004J\u000f\u0010\u0017\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0004J\u000f\u0010\u0018\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0004R\"\u0010 \u001a\u00020\u00198\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001d\"\u0004\u0008\u001e\u0010\u001fR\u001b\u0010&\u001a\u00020!8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\u001a\u0004\u0008$\u0010%R\u001b\u0010)\u001a\u00020\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\'\u0010#\u001a\u0004\u0008(\u0010\u000fR+\u00102\u001a\u00020*2\u0006\u0010+\u001a\u00020*8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/\"\u0004\u00080\u00101R+\u0010:\u001a\u0002032\u0006\u0010+\u001a\u0002038B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00084\u00105\u001a\u0004\u00086\u00107\"\u0004\u00088\u00109R+\u0010>\u001a\u0002032\u0006\u0010+\u001a\u0002038B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008;\u00105\u001a\u0004\u0008<\u00107\"\u0004\u0008=\u00109R+\u0010B\u001a\u0002032\u0006\u0010+\u001a\u0002038B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008?\u00105\u001a\u0004\u0008@\u00107\"\u0004\u0008A\u00109R\u001b\u0010H\u001a\u00020C8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008D\u0010E\u001a\u0004\u0008F\u0010GR\u001b\u0010M\u001a\u00020I8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008J\u0010#\u001a\u0004\u0008K\u0010LR\u001b\u0010R\u001a\u00020N8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008O\u0010#\u001a\u0004\u0008P\u0010Q\u00a8\u0006U"
    }
    d2 = {
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;",
        "LXW0/a;",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/a;",
        "<init>",
        "()V",
        "",
        "f3",
        "Lp40/b;",
        "item",
        "h3",
        "(Lp40/b;)V",
        "b3",
        "a3",
        "Ln40/e;",
        "a1",
        "()Ln40/e;",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "x2",
        "onResume",
        "onDestroyView",
        "Ln40/e$a;",
        "i0",
        "Ln40/e$a;",
        "Z2",
        "()Ln40/e$a;",
        "setViewModelFactory",
        "(Ln40/e$a;)V",
        "viewModelFactory",
        "Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;",
        "j0",
        "Lkotlin/j;",
        "Y2",
        "()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;",
        "viewModel",
        "k0",
        "R2",
        "component",
        "",
        "<set-?>",
        "l0",
        "LeX0/a;",
        "S2",
        "()Z",
        "i3",
        "(Z)V",
        "fromGames",
        "",
        "m0",
        "LeX0/k;",
        "W2",
        "()Ljava/lang/String;",
        "l3",
        "(Ljava/lang/String;)V",
        "tournamentTitle",
        "n0",
        "U2",
        "j3",
        "tournamentBgName",
        "o0",
        "V2",
        "k3",
        "tournamentPrizeName",
        "Lm40/g;",
        "b1",
        "LRc/c;",
        "X2",
        "()Lm40/g;",
        "viewBinding",
        "Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;",
        "k1",
        "Q2",
        "()Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;",
        "appBarOffsetChangedListener",
        "Landroidx/viewpager2/widget/ViewPager2$i;",
        "v1",
        "T2",
        "()Landroidx/viewpager2/widget/ViewPager2$i;",
        "onPageChangeCallback",
        "x1",
        "a",
        "daily_tournament_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final x1:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic y1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:Ln40/e$a;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 9

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;

    .line 4
    .line 5
    const-string v2, "fromGames"

    .line 6
    .line 7
    const-string v3, "getFromGames()Z"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "tournamentTitle"

    .line 20
    .line 21
    const-string v5, "getTournamentTitle()Ljava/lang/String;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "tournamentBgName"

    .line 33
    .line 34
    const-string v6, "getTournamentBgName()Ljava/lang/String;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "tournamentPrizeName"

    .line 46
    .line 47
    const-string v7, "getTournamentPrizeName()Ljava/lang/String;"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    new-instance v6, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 57
    .line 58
    const-string v7, "viewBinding"

    .line 59
    .line 60
    const-string v8, "getViewBinding()Lorg/xbet/games_section/feature/daily_tournament/databinding/FragmentPagerDailyTournamentBinding;"

    .line 61
    .line 62
    invoke-direct {v6, v1, v7, v8, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 63
    .line 64
    .line 65
    invoke-static {v6}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v6, 0x5

    .line 70
    new-array v6, v6, [Lkotlin/reflect/m;

    .line 71
    .line 72
    aput-object v0, v6, v4

    .line 73
    .line 74
    const/4 v0, 0x1

    .line 75
    aput-object v2, v6, v0

    .line 76
    .line 77
    const/4 v0, 0x2

    .line 78
    aput-object v3, v6, v0

    .line 79
    .line 80
    const/4 v0, 0x3

    .line 81
    aput-object v5, v6, v0

    .line 82
    .line 83
    const/4 v0, 0x4

    .line 84
    aput-object v1, v6, v0

    .line 85
    .line 86
    sput-object v6, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 87
    .line 88
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;

    .line 89
    .line 90
    const/4 v1, 0x0

    .line 91
    invoke-direct {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    sput-object v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->x1:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$a;

    .line 95
    .line 96
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lh40/b;->fragment_pager_daily_tournament:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/h;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/h;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/i;

    .line 51
    .line 52
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/i;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->k0:Lkotlin/j;

    .line 60
    .line 61
    new-instance v0, LeX0/a;

    .line 62
    .line 63
    const-string v1, "FROM_GAMES"

    .line 64
    .line 65
    const/4 v2, 0x0

    .line 66
    const/4 v3, 0x2

    .line 67
    invoke-direct {v0, v1, v2, v3, v5}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 68
    .line 69
    .line 70
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->l0:LeX0/a;

    .line 71
    .line 72
    new-instance v0, LeX0/k;

    .line 73
    .line 74
    const-string v1, "TOURNAMENT_TITLE"

    .line 75
    .line 76
    invoke-direct {v0, v1, v5, v3, v5}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 77
    .line 78
    .line 79
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->m0:LeX0/k;

    .line 80
    .line 81
    new-instance v0, LeX0/k;

    .line 82
    .line 83
    const-string v1, "TOURNAMENT_BG_NAME"

    .line 84
    .line 85
    invoke-direct {v0, v1, v5, v3, v5}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 86
    .line 87
    .line 88
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->n0:LeX0/k;

    .line 89
    .line 90
    new-instance v0, LeX0/k;

    .line 91
    .line 92
    const-string v1, "TOURNAMENT_PRIZE_NAME"

    .line 93
    .line 94
    invoke-direct {v0, v1, v5, v3, v5}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 95
    .line 96
    .line 97
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->o0:LeX0/k;

    .line 98
    .line 99
    sget-object v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$viewBinding$2;->INSTANCE:Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$viewBinding$2;

    .line 100
    .line 101
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->b1:LRc/c;

    .line 106
    .line 107
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/j;

    .line 108
    .line 109
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/j;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 110
    .line 111
    .line 112
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 113
    .line 114
    .line 115
    move-result-object v0

    .line 116
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->k1:Lkotlin/j;

    .line 117
    .line 118
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/k;

    .line 119
    .line 120
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/k;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 121
    .line 122
    .line 123
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 124
    .line 125
    .line 126
    move-result-object v0

    .line 127
    iput-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->v1:Lkotlin/j;

    .line 128
    .line 129
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->e3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->c3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic C2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lcom/google/android/material/appbar/AppBarLayout;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->O2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lcom/google/android/material/appbar/AppBarLayout;I)V

    return-void
.end method

.method public static synthetic D2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Ln40/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->P2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Ln40/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic E2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;I)Ljava/lang/CharSequence;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->g3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;I)Ljava/lang/CharSequence;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->N2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic G2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lm40/g;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lp40/b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->h3(Lp40/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Z)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->i3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->j3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic L2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->k3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic M2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->l3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final N2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/l;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/l;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final O2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lcom/google/android/material/appbar/AppBarLayout;I)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/g;->e:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    invoke-virtual {p1}, Lcom/google/android/material/appbar/AppBarLayout;->getTotalScrollRange()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    iget-object v2, v2, Lm40/g;->l:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 18
    .line 19
    invoke-virtual {v2}, Landroid/view/View;->getHeight()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    sub-int/2addr v1, v2

    .line 24
    div-int/lit8 v1, v1, 0x8

    .line 25
    .line 26
    int-to-float v1, v1

    .line 27
    int-to-float v2, p2

    .line 28
    div-float/2addr v1, v2

    .line 29
    const/4 v2, -0x1

    .line 30
    int-to-float v2, v2

    .line 31
    mul-float v1, v1, v2

    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    const/high16 v1, 0x3f800000    # 1.0f

    .line 35
    .line 36
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setAlpha(F)V

    .line 37
    .line 38
    .line 39
    invoke-static {p2}, Ljava/lang/Math;->abs(I)I

    .line 40
    .line 41
    .line 42
    move-result p2

    .line 43
    invoke-virtual {p1}, Lcom/google/android/material/appbar/AppBarLayout;->getTotalScrollRange()I

    .line 44
    .line 45
    .line 46
    move-result p1

    .line 47
    add-int/lit8 p1, p1, -0x14

    .line 48
    .line 49
    if-lt p2, p1, :cond_1

    .line 50
    .line 51
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 52
    .line 53
    .line 54
    move-result-object p0

    .line 55
    iget-object p0, p0, Lm40/g;->e:Landroid/widget/FrameLayout;

    .line 56
    .line 57
    const/4 p1, 0x0

    .line 58
    invoke-virtual {p0, p1}, Landroid/view/View;->setAlpha(F)V

    .line 59
    .line 60
    .line 61
    :cond_1
    return-void
.end method

.method public static final P2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Ln40/e;
    .locals 4

    .line 1
    invoke-static {}, Ln40/a;->a()Ln40/e$e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    instance-of v2, v1, LQW0/f;

    .line 14
    .line 15
    const-string v3, "Can not find dependencies provider for "

    .line 16
    .line 17
    if-eqz v2, :cond_2

    .line 18
    .line 19
    check-cast v1, LQW0/f;

    .line 20
    .line 21
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    instance-of v2, v2, LSv/c;

    .line 26
    .line 27
    if-eqz v2, :cond_1

    .line 28
    .line 29
    invoke-interface {v1}, LQW0/f;->a()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    if-eqz p0, :cond_0

    .line 34
    .line 35
    check-cast p0, LSv/c;

    .line 36
    .line 37
    invoke-interface {v0, p0}, Ln40/e$e;->a(LSv/c;)Ln40/e;

    .line 38
    .line 39
    .line 40
    move-result-object p0

    .line 41
    return-object p0

    .line 42
    :cond_0
    new-instance p0, Ljava/lang/NullPointerException;

    .line 43
    .line 44
    const-string v0, "null cannot be cast to non-null type org.xbet.core.di.dependencies.DailyTournamentDependencies"

    .line 45
    .line 46
    invoke-direct {p0, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p0

    .line 50
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 51
    .line 52
    new-instance v1, Ljava/lang/StringBuilder;

    .line 53
    .line 54
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 55
    .line 56
    .line 57
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 58
    .line 59
    .line 60
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 61
    .line 62
    .line 63
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p0

    .line 67
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    throw v0

    .line 71
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 72
    .line 73
    new-instance v1, Ljava/lang/StringBuilder;

    .line 74
    .line 75
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 76
    .line 77
    .line 78
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 79
    .line 80
    .line 81
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object p0

    .line 88
    invoke-direct {v0, p0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw v0
.end method

.method private final R2()Ln40/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ln40/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final b3()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/g;->l:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 6
    .line 7
    sget v1, Lh40/c;->menu_one_x_games_fg:I

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Lcom/google/android/material/appbar/MaterialToolbar;->inflateMenu(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, Lm40/g;->l:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 17
    .line 18
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    sget v2, Lpb/g;->ic_back_games:I

    .line 23
    .line 24
    invoke-static {v1, v2}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, Lcom/google/android/material/appbar/MaterialToolbar;->setNavigationIcon(Landroid/graphics/drawable/Drawable;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    iget-object v0, v0, Lm40/g;->l:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 36
    .line 37
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/e;

    .line 38
    .line 39
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/e;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    iget-object v0, v0, Lm40/g;->l:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 50
    .line 51
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/f;

    .line 52
    .line 53
    invoke-direct {v1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/f;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setOnMenuItemClickListener(Landroidx/appcompat/widget/Toolbar$g;)V

    .line 57
    .line 58
    .line 59
    return-void
.end method

.method public static final c3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->y3()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static final d3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/MenuItem;)Z
    .locals 1

    .line 1
    invoke-interface {p1}, Landroid/view/MenuItem;->getItemId()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    sget v0, Lh40/a;->one_x_rules:I

    .line 6
    .line 7
    if-ne p1, v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->S2()Z

    .line 14
    .line 15
    .line 16
    move-result p0

    .line 17
    invoke-virtual {p1, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->z3(Z)V

    .line 18
    .line 19
    .line 20
    const/4 p0, 0x1

    .line 21
    return p0

    .line 22
    :cond_0
    const/4 p0, 0x0

    .line 23
    return p0
.end method

.method public static final e3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$b;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final g3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;I)Ljava/lang/CharSequence;
    .locals 1

    .line 1
    if-eqz p1, :cond_1

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-eq p1, v0, :cond_0

    .line 5
    .line 6
    sget p1, Lpb/k;->stocks_prizes:I

    .line 7
    .line 8
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object p0

    .line 12
    return-object p0

    .line 13
    :cond_0
    sget p1, Lpb/k;->results:I

    .line 14
    .line 15
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0

    .line 20
    :cond_1
    sget p1, Lpb/k;->tournament_title:I

    .line 21
    .line 22
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p0

    .line 26
    return-object p0
.end method

.method public static final m3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Z2()Ln40/e$a;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static synthetic y2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/MenuItem;)Z
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->d3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Landroid/view/MenuItem;)Z

    move-result p0

    return p0
.end method

.method public static synthetic z2(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->m3(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final Q2()Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;

    .line 8
    .line 9
    return-object v0
.end method

.method public final S2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->l0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method public final T2()Landroidx/viewpager2/widget/ViewPager2$i;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/viewpager2/widget/ViewPager2$i;

    .line 8
    .line 9
    return-object v0
.end method

.method public final U2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final V2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->o0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final W2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->m0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final X2()Lm40/g;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->b1:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lm40/g;

    .line 13
    .line 14
    return-object v0
.end method

.method public final Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final Z2()Ln40/e$a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->i0:Ln40/e$a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public a1()Ln40/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->R2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final a3()V
    .locals 8

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    sget v3, Lpb/c;->darkBackground:I

    .line 14
    .line 15
    const/16 v6, 0x8

    .line 16
    .line 17
    const/4 v7, 0x0

    .line 18
    const/4 v5, 0x0

    .line 19
    move v4, v3

    .line 20
    invoke-static/range {v1 .. v7}, Lorg/xbet/ui_common/utils/I0;->e(Landroid/view/Window;Landroid/content/Context;IIZILjava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final f3()V
    .locals 17
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ResourceType"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v1, v1, Lm40/g;->f:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->W2()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/CollapsingToolbarLayout;->setTitle(Ljava/lang/CharSequence;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    iget-object v1, v1, Lm40/g;->f:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 21
    .line 22
    sget-object v2, Lub/b;->a:Lub/b;

    .line 23
    .line 24
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    sget v4, Lpb/c;->darkBackground:I

    .line 29
    .line 30
    const/4 v6, 0x4

    .line 31
    const/4 v7, 0x0

    .line 32
    const/4 v5, 0x0

    .line 33
    invoke-static/range {v2 .. v7}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/CollapsingToolbarLayout;->setContentScrimColor(I)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    iget-object v1, v1, Lm40/g;->f:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 45
    .line 46
    sget v2, Lpb/l;->TextAppearance_AppTheme_New_AppBar:I

    .line 47
    .line 48
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/CollapsingToolbarLayout;->setExpandedTitleTextAppearance(I)V

    .line 49
    .line 50
    .line 51
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    iget-object v1, v1, Lm40/g;->f:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 56
    .line 57
    sget v2, Lpb/l;->TextAppearance_AppTheme_New_AppBar:I

    .line 58
    .line 59
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/CollapsingToolbarLayout;->setCollapsedTitleTextAppearance(I)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    iget-object v1, v1, Lm40/g;->f:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 67
    .line 68
    const/4 v2, -0x1

    .line 69
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/CollapsingToolbarLayout;->setStatusBarScrimColor(I)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    iget-object v1, v1, Lm40/g;->c:Lcom/google/android/material/appbar/AppBarLayout;

    .line 77
    .line 78
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Q2()Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;

    .line 79
    .line 80
    .line 81
    move-result-object v2

    .line 82
    invoke-virtual {v1, v2}, Lcom/google/android/material/appbar/AppBarLayout;->addOnOffsetChangedListener(Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;)V

    .line 83
    .line 84
    .line 85
    sget-object v3, LCX0/l;->a:LCX0/l;

    .line 86
    .line 87
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    iget-object v4, v1, Lm40/g;->g:Lorg/xbet/games_section/feature/daily_tournament/presentation/ForegroundImageView;

    .line 92
    .line 93
    sget-object v1, Lq40/a;->a:Lq40/a;

    .line 94
    .line 95
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->U2()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v2

    .line 99
    invoke-virtual {v1, v2}, Lq40/a;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v5

    .line 103
    sget-object v2, LYW0/d$d;->a:LYW0/d$d;

    .line 104
    .line 105
    const/4 v15, 0x1

    .line 106
    new-array v9, v15, [LYW0/d;

    .line 107
    .line 108
    const/16 v16, 0x0

    .line 109
    .line 110
    aput-object v2, v9, v16

    .line 111
    .line 112
    sget-object v10, LYW0/c$e;->a:LYW0/c$e;

    .line 113
    .line 114
    sget v6, Lpb/g;->plug_news:I

    .line 115
    .line 116
    const/16 v13, 0xcc

    .line 117
    .line 118
    const/4 v14, 0x0

    .line 119
    const/4 v7, 0x0

    .line 120
    const/4 v8, 0x0

    .line 121
    const/4 v11, 0x0

    .line 122
    const/4 v12, 0x0

    .line 123
    invoke-static/range {v3 .. v14}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 124
    .line 125
    .line 126
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 127
    .line 128
    .line 129
    move-result-object v4

    .line 130
    iget-object v4, v4, Lm40/g;->h:Landroid/widget/ImageView;

    .line 131
    .line 132
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->V2()Ljava/lang/String;

    .line 133
    .line 134
    .line 135
    move-result-object v5

    .line 136
    invoke-interface {v5}, Ljava/lang/CharSequence;->length()I

    .line 137
    .line 138
    .line 139
    move-result v5

    .line 140
    if-lez v5, :cond_0

    .line 141
    .line 142
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->V2()Ljava/lang/String;

    .line 143
    .line 144
    .line 145
    move-result-object v5

    .line 146
    invoke-virtual {v1, v5}, Lq40/a;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 147
    .line 148
    .line 149
    move-result-object v5

    .line 150
    new-array v9, v15, [LYW0/d;

    .line 151
    .line 152
    aput-object v2, v9, v16

    .line 153
    .line 154
    sget v6, Lpb/g;->plug_news:I

    .line 155
    .line 156
    const/16 v13, 0xcc

    .line 157
    .line 158
    const/4 v14, 0x0

    .line 159
    const/4 v7, 0x0

    .line 160
    const/4 v8, 0x0

    .line 161
    const/4 v11, 0x0

    .line 162
    const/4 v12, 0x0

    .line 163
    invoke-static/range {v3 .. v14}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 164
    .line 165
    .line 166
    :cond_0
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->V2()Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 171
    .line 172
    .line 173
    move-result v1

    .line 174
    if-nez v1, :cond_1

    .line 175
    .line 176
    goto :goto_0

    .line 177
    :cond_1
    const/4 v15, 0x0

    .line 178
    :goto_0
    if-eqz v15, :cond_2

    .line 179
    .line 180
    const/16 v16, 0x4

    .line 181
    .line 182
    const/4 v1, 0x4

    .line 183
    goto :goto_1

    .line 184
    :cond_2
    const/4 v1, 0x0

    .line 185
    :goto_1
    invoke-virtual {v4, v1}, Landroid/view/View;->setVisibility(I)V

    .line 186
    .line 187
    .line 188
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 189
    .line 190
    .line 191
    move-result-object v1

    .line 192
    iget-object v1, v1, Lm40/g;->q:Landroidx/viewpager2/widget/ViewPager2;

    .line 193
    .line 194
    new-instance v2, Lr40/d;

    .line 195
    .line 196
    invoke-direct {v2, v0}, Lr40/d;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 197
    .line 198
    .line 199
    invoke-virtual {v1, v2}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 200
    .line 201
    .line 202
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    iget-object v1, v1, Lm40/g;->q:Landroidx/viewpager2/widget/ViewPager2;

    .line 207
    .line 208
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->T2()Landroidx/viewpager2/widget/ViewPager2$i;

    .line 209
    .line 210
    .line 211
    move-result-object v2

    .line 212
    invoke-virtual {v1, v2}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 213
    .line 214
    .line 215
    new-instance v1, Lu01/j;

    .line 216
    .line 217
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 218
    .line 219
    .line 220
    move-result-object v2

    .line 221
    iget-object v2, v2, Lm40/g;->i:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 222
    .line 223
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 224
    .line 225
    .line 226
    move-result-object v3

    .line 227
    iget-object v3, v3, Lm40/g;->q:Landroidx/viewpager2/widget/ViewPager2;

    .line 228
    .line 229
    new-instance v4, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/g;

    .line 230
    .line 231
    invoke-direct {v4, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/g;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 232
    .line 233
    .line 234
    invoke-direct {v1, v2, v3, v4}, Lu01/j;-><init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroidx/viewpager2/widget/ViewPager2;Lkotlin/jvm/functions/Function1;)V

    .line 235
    .line 236
    .line 237
    invoke-virtual {v1}, Lu01/j;->d()V

    .line 238
    .line 239
    .line 240
    return-void
.end method

.method public final h3(Lp40/b;)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/g;->n:Landroid/widget/TextView;

    .line 6
    .line 7
    invoke-virtual {p1}, Lp40/b;->a()J

    .line 8
    .line 9
    .line 10
    move-result-wide v1

    .line 11
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, Lm40/g;->p:Landroid/widget/TextView;

    .line 23
    .line 24
    invoke-virtual {p1}, Lp40/b;->b()F

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    invoke-static {p1}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final i3(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->l0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final j3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final k3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->o0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final l3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->m0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->y1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, Lm40/g;->c:Lcom/google/android/material/appbar/AppBarLayout;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Q2()Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, v1}, Lcom/google/android/material/appbar/AppBarLayout;->removeOnOffsetChangedListener(Lcom/google/android/material/appbar/AppBarLayout$OnOffsetChangedListener;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->X2()Lm40/g;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, Lm40/g;->q:Landroidx/viewpager2/widget/ViewPager2;

    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->T2()Landroidx/viewpager2/widget/ViewPager2$i;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->o(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const/4 v1, 0x0

    .line 32
    invoke-virtual {v0, v1}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->C3(Z)V

    .line 33
    .line 34
    .line 35
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public onResume()V
    .locals 0

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->a3()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->b3()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->f3()V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->R2()Ln40/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, Ln40/e;->d(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;->Y2()Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/viewmodels/DailyTournamentPagerViewModel;->u3()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$onObserveData$1;-><init>(Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/games_section/feature/daily_tournament/presentation/fragments/TournamentPagerFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
