.class public final LNI0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LQW0/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0014\u0018\u00002\u00020\u0001BQ\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\'\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001aH\u0000\u00a2\u0006\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/\u00a8\u00060"
    }
    d2 = {
        "LNI0/d;",
        "LQW0/a;",
        "Lf8/g;",
        "serviceGenerator",
        "LQW0/c;",
        "coroutinesLib",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LSX0/a;",
        "lottieConfigurator",
        "LkC0/a;",
        "gameScreenGeneralFactory",
        "LQD0/d;",
        "putStatisticHeaderDataUseCase",
        "LDH0/a;",
        "statisticScreenFactory",
        "Lc8/h;",
        "requestParamsDataSource",
        "<init>",
        "(Lf8/g;LQW0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;LkC0/a;LQD0/d;LDH0/a;Lc8/h;)V",
        "",
        "playerId",
        "",
        "sportId",
        "LwX0/c;",
        "router",
        "LNI0/c;",
        "a",
        "(Ljava/lang/String;JLwX0/c;)LNI0/c;",
        "Lf8/g;",
        "b",
        "LQW0/c;",
        "c",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "d",
        "Lorg/xbet/ui_common/utils/M;",
        "e",
        "LSX0/a;",
        "f",
        "LkC0/a;",
        "g",
        "LQD0/d;",
        "h",
        "LDH0/a;",
        "i",
        "Lc8/h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lf8/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LQW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LkC0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LQD0/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:LDH0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lf8/g;LQW0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LSX0/a;LkC0/a;LQD0/d;LDH0/a;Lc8/h;)V
    .locals 0
    .param p1    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LkC0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LQD0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LDH0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LNI0/d;->a:Lf8/g;

    .line 5
    .line 6
    iput-object p2, p0, LNI0/d;->b:LQW0/c;

    .line 7
    .line 8
    iput-object p3, p0, LNI0/d;->c:Lorg/xbet/ui_common/utils/internet/a;

    .line 9
    .line 10
    iput-object p4, p0, LNI0/d;->d:Lorg/xbet/ui_common/utils/M;

    .line 11
    .line 12
    iput-object p5, p0, LNI0/d;->e:LSX0/a;

    .line 13
    .line 14
    iput-object p6, p0, LNI0/d;->f:LkC0/a;

    .line 15
    .line 16
    iput-object p7, p0, LNI0/d;->g:LQD0/d;

    .line 17
    .line 18
    iput-object p8, p0, LNI0/d;->h:LDH0/a;

    .line 19
    .line 20
    iput-object p9, p0, LNI0/d;->i:Lc8/h;

    .line 21
    .line 22
    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;JLwX0/c;)LNI0/c;
    .locals 14
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LNI0/a;->a()LNI0/c$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, LNI0/d;->b:LQW0/c;

    .line 6
    .line 7
    iget-object v2, p0, LNI0/d;->a:Lf8/g;

    .line 8
    .line 9
    iget-object v4, p0, LNI0/d;->c:Lorg/xbet/ui_common/utils/internet/a;

    .line 10
    .line 11
    iget-object v6, p0, LNI0/d;->d:Lorg/xbet/ui_common/utils/M;

    .line 12
    .line 13
    iget-object v7, p0, LNI0/d;->e:LSX0/a;

    .line 14
    .line 15
    iget-object v8, p0, LNI0/d;->f:LkC0/a;

    .line 16
    .line 17
    iget-object v9, p0, LNI0/d;->g:LQD0/d;

    .line 18
    .line 19
    iget-object v10, p0, LNI0/d;->h:LDH0/a;

    .line 20
    .line 21
    iget-object v13, p0, LNI0/d;->i:Lc8/h;

    .line 22
    .line 23
    move-object v3, p1

    .line 24
    move-wide/from16 v11, p2

    .line 25
    .line 26
    move-object/from16 v5, p4

    .line 27
    .line 28
    invoke-interface/range {v0 .. v13}, LNI0/c$a;->a(LQW0/c;Lf8/g;Ljava/lang/String;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Lorg/xbet/ui_common/utils/M;LSX0/a;LkC0/a;LQD0/d;LDH0/a;JLc8/h;)LNI0/c;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method
