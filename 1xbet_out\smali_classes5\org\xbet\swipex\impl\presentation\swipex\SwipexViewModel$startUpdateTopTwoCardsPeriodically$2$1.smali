.class final Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.swipex.impl.presentation.swipex.SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1"
    f = "SwipexViewModel.kt"
    l = {
        0x2d4,
        0x1df,
        0x1e2,
        0x1e5
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Long;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Result<",
        "+",
        "Lkotlin/Unit;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\u0010\u0000\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004H\n"
    }
    d2 = {
        "<anonymous>",
        "Lkotlin/Result;",
        "",
        "it",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $$this$launchJob:Lkotlinx/coroutines/N;

.field I$0:I

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;


# direct methods
.method public constructor <init>(Lkotlinx/coroutines/N;Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->$$this$launchJob:Lkotlinx/coroutines/N;

    iput-object p2, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;

    iget-object v0, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->$$this$launchJob:Lkotlinx/coroutines/N;

    iget-object v1, p0, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;-><init>(Lkotlinx/coroutines/N;Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public final invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Result<",
            "Lkotlin/Unit;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-virtual {p0, p1, p3}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 2
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, v0, v1, p2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->invoke(JLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v3, p0

    .line 2
    .line 3
    const/4 v6, 0x1

    .line 4
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    move-result-object v7

    .line 8
    iget v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->label:I

    .line 9
    .line 10
    const/4 v9, 0x4

    .line 11
    const/4 v10, 0x3

    .line 12
    const/4 v11, 0x2

    .line 13
    const/4 v12, 0x0

    .line 14
    if-eqz v0, :cond_5

    .line 15
    .line 16
    if-eq v0, v6, :cond_3

    .line 17
    .line 18
    if-eq v0, v11, :cond_2

    .line 19
    .line 20
    if-eq v0, v10, :cond_1

    .line 21
    .line 22
    if-ne v0, v9, :cond_0

    .line 23
    .line 24
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 25
    .line 26
    .line 27
    goto/16 :goto_8

    .line 28
    .line 29
    :catchall_0
    move-exception v0

    .line 30
    goto/16 :goto_a

    .line 31
    .line 32
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 33
    .line 34
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 35
    .line 36
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    throw v0

    .line 40
    :cond_1
    iget v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->I$0:I

    .line 41
    .line 42
    iget-object v1, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$5:Ljava/lang/Object;

    .line 43
    .line 44
    check-cast v1, Ljava/util/Collection;

    .line 45
    .line 46
    iget-object v2, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$4:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v2, Ljava/util/Iterator;

    .line 49
    .line 50
    iget-object v4, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$3:Ljava/lang/Object;

    .line 51
    .line 52
    check-cast v4, Ljava/util/List;

    .line 53
    .line 54
    iget-object v5, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 55
    .line 56
    check-cast v5, Lkotlinx/coroutines/sync/a;

    .line 57
    .line 58
    iget-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 59
    .line 60
    check-cast v13, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 61
    .line 62
    iget-object v14, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 63
    .line 64
    check-cast v14, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 65
    .line 66
    :try_start_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 67
    .line 68
    .line 69
    move-object v10, v13

    .line 70
    move-object v13, v1

    .line 71
    move-object/from16 v1, p1

    .line 72
    .line 73
    goto/16 :goto_3

    .line 74
    .line 75
    :catchall_1
    move-exception v0

    .line 76
    goto/16 :goto_9

    .line 77
    .line 78
    :cond_2
    iget v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->I$0:I

    .line 79
    .line 80
    iget-object v1, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$5:Ljava/lang/Object;

    .line 81
    .line 82
    check-cast v1, Ljava/util/Collection;

    .line 83
    .line 84
    iget-object v2, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$4:Ljava/lang/Object;

    .line 85
    .line 86
    check-cast v2, Ljava/util/Iterator;

    .line 87
    .line 88
    iget-object v4, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$3:Ljava/lang/Object;

    .line 89
    .line 90
    check-cast v4, Ljava/util/List;

    .line 91
    .line 92
    iget-object v5, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 93
    .line 94
    check-cast v5, Lkotlinx/coroutines/sync/a;

    .line 95
    .line 96
    iget-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 97
    .line 98
    check-cast v13, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 99
    .line 100
    iget-object v14, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 101
    .line 102
    check-cast v14, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 103
    .line 104
    :try_start_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 105
    .line 106
    .line 107
    move-object v10, v13

    .line 108
    move-object v13, v1

    .line 109
    move-object/from16 v1, p1

    .line 110
    .line 111
    goto/16 :goto_5

    .line 112
    .line 113
    :cond_3
    iget-object v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 114
    .line 115
    check-cast v0, Lkotlinx/coroutines/sync/a;

    .line 116
    .line 117
    iget-object v1, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 118
    .line 119
    check-cast v1, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 120
    .line 121
    iget-object v2, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 122
    .line 123
    check-cast v2, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 124
    .line 125
    :try_start_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 126
    .line 127
    .line 128
    :cond_4
    move-object v5, v0

    .line 129
    goto :goto_0

    .line 130
    :cond_5
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 131
    .line 132
    .line 133
    iget-object v2, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->this$0:Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;

    .line 134
    .line 135
    :try_start_4
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 136
    .line 137
    invoke-static {v2}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->O3(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;)Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->b(Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;)Lkotlinx/coroutines/sync/a;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    iput-object v2, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 146
    .line 147
    iput-object v1, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 148
    .line 149
    iput-object v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 150
    .line 151
    iput v6, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->label:I

    .line 152
    .line 153
    invoke-interface {v0, v12, v3}, Lkotlinx/coroutines/sync/a;->g(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 154
    .line 155
    .line 156
    move-result-object v4
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 157
    if-ne v4, v7, :cond_4

    .line 158
    .line 159
    goto/16 :goto_7

    .line 160
    .line 161
    :goto_0
    :try_start_5
    new-instance v0, Ljava/util/ArrayList;

    .line 162
    .line 163
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 164
    .line 165
    .line 166
    invoke-static {v1}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->a(Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;)Ljava/util/LinkedList;

    .line 167
    .line 168
    .line 169
    move-result-object v4

    .line 170
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 171
    .line 172
    .line 173
    move-result-object v4
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 174
    move-object v13, v0

    .line 175
    move-object v0, v2

    .line 176
    move-object v14, v4

    .line 177
    move-object v15, v5

    .line 178
    const/4 v2, 0x0

    .line 179
    :goto_1
    :try_start_6
    invoke-interface {v14}, Ljava/util/Iterator;->hasNext()Z

    .line 180
    .line 181
    .line 182
    move-result v4

    .line 183
    if-eqz v4, :cond_b

    .line 184
    .line 185
    invoke-interface {v14}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 186
    .line 187
    .line 188
    move-result-object v4

    .line 189
    add-int/lit8 v5, v2, 0x1

    .line 190
    .line 191
    if-gez v2, :cond_6

    .line 192
    .line 193
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 194
    .line 195
    .line 196
    goto :goto_2

    .line 197
    :catchall_2
    move-exception v0

    .line 198
    move-object v5, v15

    .line 199
    goto/16 :goto_9

    .line 200
    .line 201
    :cond_6
    :goto_2
    check-cast v4, LDS0/c;

    .line 202
    .line 203
    if-eqz v2, :cond_9

    .line 204
    .line 205
    if-eq v2, v6, :cond_7

    .line 206
    .line 207
    move v2, v5

    .line 208
    move-object v5, v13

    .line 209
    goto/16 :goto_6

    .line 210
    .line 211
    :cond_7
    iput-object v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 212
    .line 213
    iput-object v1, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 214
    .line 215
    iput-object v15, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 216
    .line 217
    iput-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$3:Ljava/lang/Object;

    .line 218
    .line 219
    iput-object v14, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$4:Ljava/lang/Object;

    .line 220
    .line 221
    iput-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$5:Ljava/lang/Object;

    .line 222
    .line 223
    iput v5, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->I$0:I

    .line 224
    .line 225
    iput v10, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->label:I

    .line 226
    .line 227
    const/4 v2, 0x0

    .line 228
    move-object/from16 v16, v1

    .line 229
    .line 230
    move-object v1, v4

    .line 231
    const/4 v4, 0x2

    .line 232
    move/from16 v17, v5

    .line 233
    .line 234
    const/4 v5, 0x0

    .line 235
    move-object/from16 v10, v16

    .line 236
    .line 237
    move/from16 v8, v17

    .line 238
    .line 239
    invoke-static/range {v0 .. v5}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->S4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;LDS0/c;ZLkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 240
    .line 241
    .line 242
    move-result-object v1
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    .line 243
    if-ne v1, v7, :cond_8

    .line 244
    .line 245
    goto/16 :goto_7

    .line 246
    .line 247
    :cond_8
    move-object v4, v13

    .line 248
    move-object v2, v14

    .line 249
    move-object v5, v15

    .line 250
    move-object v14, v0

    .line 251
    move v0, v8

    .line 252
    :goto_3
    :try_start_7
    check-cast v1, LDS0/c;
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_1

    .line 253
    .line 254
    :goto_4
    move-object v15, v2

    .line 255
    move v2, v0

    .line 256
    move-object v0, v14

    .line 257
    move-object v14, v15

    .line 258
    move-object v15, v5

    .line 259
    move-object v5, v4

    .line 260
    move-object v4, v1

    .line 261
    move-object v1, v10

    .line 262
    goto :goto_6

    .line 263
    :cond_9
    move-object v10, v1

    .line 264
    move-object v1, v4

    .line 265
    move v8, v5

    .line 266
    :try_start_8
    iput-object v0, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 267
    .line 268
    iput-object v10, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 269
    .line 270
    iput-object v15, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 271
    .line 272
    iput-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$3:Ljava/lang/Object;

    .line 273
    .line 274
    iput-object v14, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$4:Ljava/lang/Object;

    .line 275
    .line 276
    iput-object v13, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$5:Ljava/lang/Object;

    .line 277
    .line 278
    iput v8, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->I$0:I

    .line 279
    .line 280
    iput v11, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->label:I

    .line 281
    .line 282
    invoke-static {v0, v1, v6, v3}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->d4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;LDS0/c;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 283
    .line 284
    .line 285
    move-result-object v1
    :try_end_8
    .catchall {:try_start_8 .. :try_end_8} :catchall_2

    .line 286
    if-ne v1, v7, :cond_a

    .line 287
    .line 288
    goto :goto_7

    .line 289
    :cond_a
    move-object v4, v13

    .line 290
    move-object v2, v14

    .line 291
    move-object v5, v15

    .line 292
    move-object v14, v0

    .line 293
    move v0, v8

    .line 294
    :goto_5
    :try_start_9
    check-cast v1, LDS0/c;
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_1

    .line 295
    .line 296
    goto :goto_4

    .line 297
    :goto_6
    :try_start_a
    invoke-interface {v13, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 298
    .line 299
    .line 300
    move-object v13, v5

    .line 301
    const/4 v10, 0x3

    .line 302
    goto :goto_1

    .line 303
    :cond_b
    move-object v10, v1

    .line 304
    invoke-static {v10}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->a(Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;)Ljava/util/LinkedList;

    .line 305
    .line 306
    .line 307
    move-result-object v1

    .line 308
    invoke-virtual {v1}, Ljava/util/LinkedList;->clear()V

    .line 309
    .line 310
    .line 311
    invoke-static {v10}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->a(Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;)Ljava/util/LinkedList;

    .line 312
    .line 313
    .line 314
    move-result-object v1

    .line 315
    invoke-virtual {v1, v13}, Ljava/util/LinkedList;->addAll(Ljava/util/Collection;)Z

    .line 316
    .line 317
    .line 318
    invoke-static {v10}, Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;->a(Lorg/xbet/swipex/impl/presentation/swipex/utils/SwipexBlockedEventCardQueue;)Ljava/util/LinkedList;

    .line 319
    .line 320
    .line 321
    move-result-object v1

    .line 322
    invoke-static {v1}, Lkotlin/collections/CollectionsKt;->z1(Ljava/lang/Iterable;)Ljava/util/List;
    :try_end_a
    .catchall {:try_start_a .. :try_end_a} :catchall_2

    .line 323
    .line 324
    .line 325
    :try_start_b
    invoke-interface {v15, v12}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 326
    .line 327
    .line 328
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$0:Ljava/lang/Object;

    .line 329
    .line 330
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$1:Ljava/lang/Object;

    .line 331
    .line 332
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$2:Ljava/lang/Object;

    .line 333
    .line 334
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$3:Ljava/lang/Object;

    .line 335
    .line 336
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$4:Ljava/lang/Object;

    .line 337
    .line 338
    iput-object v12, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->L$5:Ljava/lang/Object;

    .line 339
    .line 340
    iput v9, v3, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel$startUpdateTopTwoCardsPeriodically$2$1;->label:I

    .line 341
    .line 342
    const/4 v1, 0x0

    .line 343
    invoke-static {v0, v1, v3, v6, v12}, Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;->Q4(Lorg/xbet/swipex/impl/presentation/swipex/SwipexViewModel;ZLkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 344
    .line 345
    .line 346
    move-result-object v0

    .line 347
    if-ne v0, v7, :cond_c

    .line 348
    .line 349
    :goto_7
    return-object v7

    .line 350
    :cond_c
    :goto_8
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 351
    .line 352
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 353
    .line 354
    .line 355
    move-result-object v0

    .line 356
    goto :goto_b

    .line 357
    :goto_9
    invoke-interface {v5, v12}, Lkotlinx/coroutines/sync/a;->h(Ljava/lang/Object;)V

    .line 358
    .line 359
    .line 360
    throw v0
    :try_end_b
    .catchall {:try_start_b .. :try_end_b} :catchall_0

    .line 361
    :goto_a
    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 362
    .line 363
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 364
    .line 365
    .line 366
    move-result-object v0

    .line 367
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 368
    .line 369
    .line 370
    move-result-object v0

    .line 371
    :goto_b
    invoke-static {v0}, Lkotlin/Result;->box-impl(Ljava/lang/Object;)Lkotlin/Result;

    .line 372
    .line 373
    .line 374
    move-result-object v0

    .line 375
    return-object v0
.end method
