.class public final synthetic Lorg/xplatform/aggregator/impl/gifts/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/gifts/f;->e()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
