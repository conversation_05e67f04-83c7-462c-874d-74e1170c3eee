.class public final LMS0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;",
        "LLS0/a;",
        "a",
        "(Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;)LLS0/a;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;)LLS0/a;
    .locals 9
    .param p0    # Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LLS0/a;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getId()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getSportId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v3

    .line 15
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getSubSportId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v5

    .line 19
    invoke-virtual {p0}, Lorg/xbet/swipex/impl/domain/model/SwipeXFilterChampModel;->getSelected()Z

    .line 20
    .line 21
    .line 22
    move-result p0

    .line 23
    invoke-static {p0}, LLS0/a$a$a;->b(Z)Z

    .line 24
    .line 25
    .line 26
    move-result v7

    .line 27
    const/4 v8, 0x0

    .line 28
    invoke-direct/range {v0 .. v8}, LLS0/a;-><init>(ILjava/lang/String;JJZLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 29
    .line 30
    .line 31
    return-object v0
.end method
