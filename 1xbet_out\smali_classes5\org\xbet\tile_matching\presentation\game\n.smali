.class public final synthetic Lorg/xbet/tile_matching/presentation/game/n;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/tile_matching/presentation/game/n;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/tile_matching/presentation/game/n;->a:Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;

    invoke-static {v0}, Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;->r3(Lorg/xbet/tile_matching/presentation/game/TileMatchingGameViewModel;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
